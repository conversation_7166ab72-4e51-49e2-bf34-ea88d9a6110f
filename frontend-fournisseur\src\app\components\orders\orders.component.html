<div class="orders-container">
  <!-- Header -->
  <div class="orders-header">
    <h1>📦 Gestion des Commandes</h1>
    <div class="header-actions">
      <button class="btn btn-secondary" (click)="loadOrders()">
        🔄 Actualiser
      </button>
    </div>
  </div>

  <!-- Filtres -->
  <div class="filters-section">
    <div class="filters-row">
      <div class="filter-group">
        <label for="search">🔍 Rechercher:</label>
        <input
          id="search"
          type="text"
          class="form-control"
          placeholder="Référence, client..."
          [value]="searchTerm()"
          (input)="onSearchChange($event)"
        />
      </div>

      <div class="filter-group">
        <label for="status">📊 Statut:</label>
        <select
          id="status"
          class="form-control"
          [value]="selectedStatus()"
          (change)="onStatusChange($event)"
        >
          <option value="">Tous les statuts</option>
          <option value="Nouvelle">Nouvelle</option>
          <option value="Acceptee">Acceptée</option>
          <option value="EnPreparation">En préparation</option>
          <option value="Prete">Prête</option>
          <option value="Expediee">Expédiée</option>
          <option value="Livree">Livrée</option>
          <option value="Refusee">Refusée</option>
        </select>
      </div>

      <div class="filter-group">
        <button class="btn btn-outline-secondary" (click)="clearFilters()">
          🗑️ Effacer filtres
        </button>
      </div>
    </div>
  </div>

  <!-- Loading -->
  @if (isLoading()) {
    <div class="loading-container">
      <div class="spinner"></div>
      <p>Chargement des commandes...</p>
    </div>
  }

  <!-- Error -->
  @if (error()) {
    <div class="alert alert-danger">
      <strong>❌ Erreur:</strong> {{ error() }}
    </div>
  }

  <!-- Orders List -->
  @if (!isLoading() && !error()) {
    <div class="orders-list">
      @if (paginatedOrders().length === 0) {
        <div class="no-orders">
          <p>📭 Aucune commande trouvée</p>
          @if (searchTerm() || selectedStatus()) {
            <button class="btn btn-primary" (click)="clearFilters()">
              Voir toutes les commandes
            </button>
          }
        </div>
      } @else {
        @for (order of paginatedOrders(); track order.id) {
          <div class="order-card">
            <!-- Order Header -->
            <div class="order-header" (click)="toggleOrderDetails(order.id)">
              <div class="order-info">
                <div class="order-reference">
                  <strong>📋 {{ order.reference }}</strong>
                </div>
                <div class="order-meta">
                  <span class="order-date">📅 {{ formatDate(order.dateCreation) }}</span>
                  <span class="order-total">💰 {{ formatPrice(order.montantTotal) }}</span>
                </div>
              </div>
              
              <div class="order-status">
                <span class="status-badge" [class]="getStatusClass(order.statut)">
                  {{ getStatusText(order.statut) }}
                </span>
                <span class="expand-icon" [class.expanded]="isOrderExpanded(order.id)">
                  ▼
                </span>
              </div>
            </div>

            <!-- Order Details (Expandable) -->
            @if (isOrderExpanded(order.id)) {
              <div class="order-details">
                <!-- Order Info -->
                <div class="client-info">
                  <h4>📋 Informations Commande</h4>
                  <div class="info-grid">
                    <div class="info-item">
                      <strong>Référence:</strong> {{ order.reference }}
                    </div>
                    <div class="info-item">
                      <strong>Fournisseur:</strong> {{ order.nomFournisseur }}
                    </div>
                    <div class="info-item">
                      <strong>Matricule:</strong> {{ order.matriculeFiscale || 'Non renseigné' }}
                    </div>
                    <div class="info-item">
                      <strong>Frais livraison:</strong> {{ formatPrice(order.fraisLivraison) }}
                    </div>
                    @if (order.numeroBonLivraison) {
                      <div class="info-item">
                        <strong>Bon de livraison:</strong> {{ order.numeroBonLivraison }}
                      </div>
                    }
                  </div>
                </div>



                <!-- Products -->
                <div class="products-section">
                  <h4>🛍️ Produits Commandés</h4>
                  @if (order.lignesCommande && order.lignesCommande.length > 0) {
                    <div class="products-list">
                      @for (ligne of order.lignesCommande; track ligne.id) {
                        <div class="product-item">
                          <div class="product-info">
                            <div class="product-name">
                              <strong>{{ ligne.nomProduit }}</strong>
                            </div>
                            <div class="product-details">
                              <span class="product-ref">Réf: {{ ligne.referenceProduit || 'N/A' }}</span>
                              @if (ligne.imagePrincipale) {
                                <span class="product-image">🖼️ Image disponible</span>
                              }
                            </div>
                          </div>
                          <div class="product-quantity">
                            <span class="quantity">Qté: {{ ligne.quantite }}</span>
                          </div>
                          <div class="product-price">
                            <div class="unit-price">{{ formatPrice(ligne.prixUnitaire) }}/u</div>
                            <div class="total-price"><strong>{{ formatPrice(ligne.totalLigne) }}</strong></div>
                          </div>
                        </div>
                      }
                    </div>
                  } @else {
                    <p class="no-products">Aucun produit trouvé pour cette commande</p>
                  }
                </div>

                <!-- Order Actions -->
                <div class="order-actions">
                  <h4>⚡ Actions</h4>
                  <div class="actions-buttons">
                    @if (canConfirmOrder(order.statut)) {
                      <button
                        class="btn btn-success"
                        (click)="updateOrderStatus(order.id, 'Acceptee')"
                      >
                        ✅ Accepter
                      </button>
                    }

                    @if (canPrepareOrder(order.statut)) {
                      <button
                        class="btn btn-warning"
                        (click)="updateOrderStatus(order.id, 'EnPreparation')"
                      >
                        📦 Préparer
                      </button>
                    }

                    @if (canShipOrder(order.statut)) {
                      <button
                        class="btn btn-info"
                        (click)="updateOrderStatus(order.id, 'Expediee')"
                      >
                        🚚 Expédier
                      </button>
                    }

                    @if (canDeliverOrder(order.statut)) {
                      <button
                        class="btn btn-primary"
                        (click)="updateOrderStatus(order.id, 'Livree')"
                      >
                        ✅ Marquer comme livrée
                      </button>
                    }

                    @if (canCancelOrder(order.statut)) {
                      <button
                        class="btn btn-danger"
                        (click)="updateOrderStatus(order.id, 'Refusee')"
                      >
                        ❌ Refuser
                      </button>
                    }
                  </div>
                </div>
              </div>
            }
          </div>
        }
      }
    </div>

    <!-- Pagination -->
    @if (totalPages() > 1) {
      <div class="pagination-container">
        <div class="pagination-info">
          Affichage {{ (currentPage() - 1) * pageSize() + 1 }} à 
          {{ Math.min(currentPage() * pageSize(), filteredOrders().length) }} 
          sur {{ filteredOrders().length }} commandes
        </div>
        
        <div class="pagination">
          <button 
            class="btn btn-outline-primary"
            [disabled]="currentPage() === 1"
            (click)="onPageChange(currentPage() - 1)"
          >
            ← Précédent
          </button>
          
          @for (page of [].constructor(totalPages()); track $index) {
            <button 
              class="btn"
              [class.btn-primary]="$index + 1 === currentPage()"
              [class.btn-outline-primary]="$index + 1 !== currentPage()"
              (click)="onPageChange($index + 1)"
            >
              {{ $index + 1 }}
            </button>
          }
          
          <button 
            class="btn btn-outline-primary"
            [disabled]="currentPage() === totalPages()"
            (click)="onPageChange(currentPage() + 1)"
          >
            Suivant →
          </button>
        </div>
      </div>
    }
  }
</div>

{"version": 3, "sources": ["src/app/components/auth/register/register.component.css"], "sourcesContent": [".register-container {\n  min-height: 100vh;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n  padding: 20px;\n}\n\n.register-card {\n  background: white;\n  border-radius: 12px;\n  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);\n  padding: 40px;\n  width: 100%;\n  max-width: 800px;\n  max-height: 90vh;\n  overflow-y: auto;\n}\n\n.register-header {\n  text-align: center;\n  margin-bottom: 30px;\n}\n\n.register-header h1 {\n  color: #333;\n  font-size: 2rem;\n  font-weight: 600;\n  margin-bottom: 8px;\n}\n\n.register-header p {\n  color: #666;\n  font-size: 1rem;\n  margin: 0;\n}\n\n.register-form {\n  display: flex;\n  flex-direction: column;\n  gap: 30px;\n}\n\n.form-section {\n  border: 1px solid #e9ecef;\n  border-radius: 8px;\n  padding: 20px;\n  background-color: #f8f9fa;\n}\n\n.form-section h3 {\n  color: #495057;\n  font-size: 1.2rem;\n  font-weight: 600;\n  margin-bottom: 20px;\n  padding-bottom: 10px;\n  border-bottom: 2px solid #dee2e6;\n}\n\n.form-row {\n  display: grid;\n  grid-template-columns: 1fr 1fr;\n  gap: 20px;\n  margin-bottom: 20px;\n}\n\n.form-group {\n  display: flex;\n  flex-direction: column;\n}\n\n.form-group:last-child {\n  margin-bottom: 0;\n}\n\n.form-group label {\n  font-weight: 500;\n  color: #495057;\n  margin-bottom: 8px;\n  font-size: 0.9rem;\n}\n\n.form-control {\n  padding: 12px 16px;\n  border: 2px solid #e9ecef;\n  border-radius: 8px;\n  font-size: 1rem;\n  transition: all 0.3s ease;\n  background-color: white;\n}\n\n.form-control:focus {\n  outline: none;\n  border-color: #667eea;\n  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);\n}\n\n.form-control.is-invalid {\n  border-color: #dc3545;\n}\n\n.form-control.is-invalid:focus {\n  border-color: #dc3545;\n  box-shadow: 0 0 0 3px rgba(220, 53, 69, 0.1);\n}\n\n.invalid-feedback {\n  color: #dc3545;\n  font-size: 0.875rem;\n  margin-top: 5px;\n}\n\n.alert {\n  padding: 12px 16px;\n  border-radius: 8px;\n  margin-bottom: 20px;\n  font-size: 0.9rem;\n}\n\n.alert-danger {\n  background-color: #f8d7da;\n  border: 1px solid #f5c6cb;\n  color: #721c24;\n}\n\n.alert-success {\n  background-color: #d4edda;\n  border: 1px solid #c3e6cb;\n  color: #155724;\n}\n\n.form-actions {\n  display: flex;\n  gap: 15px;\n  justify-content: space-between;\n  margin-top: 20px;\n}\n\n.btn {\n  padding: 12px 24px;\n  border: none;\n  border-radius: 8px;\n  font-size: 1rem;\n  font-weight: 500;\n  cursor: pointer;\n  transition: all 0.3s ease;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  gap: 8px;\n  min-width: 140px;\n}\n\n.btn:disabled {\n  opacity: 0.6;\n  cursor: not-allowed;\n}\n\n.btn-primary {\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n  color: white;\n}\n\n.btn-primary:hover:not(:disabled) {\n  transform: translateY(-2px);\n  box-shadow: 0 8px 20px rgba(102, 126, 234, 0.3);\n}\n\n.btn-secondary {\n  background-color: #6c757d;\n  color: white;\n}\n\n.btn-secondary:hover:not(:disabled) {\n  background-color: #5a6268;\n  transform: translateY(-2px);\n}\n\n.spinner {\n  width: 16px;\n  height: 16px;\n  border: 2px solid transparent;\n  border-top: 2px solid currentColor;\n  border-radius: 50%;\n  animation: spin 1s linear infinite;\n}\n\n@keyframes spin {\n  0% { transform: rotate(0deg); }\n  100% { transform: rotate(360deg); }\n}\n\n/* Responsive */\n@media (max-width: 768px) {\n  .register-container {\n    padding: 10px;\n  }\n\n  .register-card {\n    padding: 20px;\n    max-height: 95vh;\n  }\n\n  .form-row {\n    grid-template-columns: 1fr;\n    gap: 15px;\n  }\n\n  .form-actions {\n    flex-direction: column;\n  }\n\n  .register-header h1 {\n    font-size: 1.5rem;\n  }\n}\n\n@media (max-width: 480px) {\n  .register-card {\n    padding: 15px;\n  }\n\n  .form-section {\n    padding: 15px;\n  }\n}\n"], "mappings": ";AAAA,CAAC;AACC,cAAY;AACZ,WAAS;AACT,eAAa;AACb,mBAAiB;AACjB;AAAA,IAAY;AAAA,MAAgB,MAAhB;AAAA,MAAwB,QAAQ,EAAhC;AAAA,MAAoC,QAAQ;AACxD,WAAS;AACX;AAEA,CAAC;AACC,cAAY;AACZ,iBAAe;AACf,cAAY,EAAE,KAAK,KAAK,KAAK,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE;AACtC,WAAS;AACT,SAAO;AACP,aAAW;AACX,cAAY;AACZ,cAAY;AACd;AAEA,CAAC;AACC,cAAY;AACZ,iBAAe;AACjB;AAEA,CALC,gBAKgB;AACf,SAAO;AACP,aAAW;AACX,eAAa;AACb,iBAAe;AACjB;AAEA,CAZC,gBAYgB;AACf,SAAO;AACP,aAAW;AACX,UAAQ;AACV;AAEA,CAAC;AACC,WAAS;AACT,kBAAgB;AAChB,OAAK;AACP;AAEA,CAAC;AACC,UAAQ,IAAI,MAAM;AAClB,iBAAe;AACf,WAAS;AACT,oBAAkB;AACpB;AAEA,CAPC,aAOa;AACZ,SAAO;AACP,aAAW;AACX,eAAa;AACb,iBAAe;AACf,kBAAgB;AAChB,iBAAe,IAAI,MAAM;AAC3B;AAEA,CAAC;AACC,WAAS;AACT,yBAAuB,IAAI;AAC3B,OAAK;AACL,iBAAe;AACjB;AAEA,CAAC;AACC,WAAS;AACT,kBAAgB;AAClB;AAEA,CALC,UAKU;AACT,iBAAe;AACjB;AAEA,CATC,WASW;AACV,eAAa;AACb,SAAO;AACP,iBAAe;AACf,aAAW;AACb;AAEA,CAAC;AACC,WAAS,KAAK;AACd,UAAQ,IAAI,MAAM;AAClB,iBAAe;AACf,aAAW;AACX,cAAY,IAAI,KAAK;AACrB,oBAAkB;AACpB;AAEA,CATC,YASY;AACX,WAAS;AACT,gBAAc;AACd,cAAY,EAAE,EAAE,EAAE,IAAI,KAAK,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE;AAC5C;AAEA,CAfC,YAeY,CAAC;AACZ,gBAAc;AAChB;AAEA,CAnBC,YAmBY,CAJC,UAIU;AACtB,gBAAc;AACd,cAAY,EAAE,EAAE,EAAE,IAAI,KAAK,GAAG,EAAE,EAAE,EAAE,EAAE,EAAE;AAC1C;AAEA,CAAC;AACC,SAAO;AACP,aAAW;AACX,cAAY;AACd;AAEA,CAAC;AACC,WAAS,KAAK;AACd,iBAAe;AACf,iBAAe;AACf,aAAW;AACb;AAEA,CAAC;AACC,oBAAkB;AAClB,UAAQ,IAAI,MAAM;AAClB,SAAO;AACT;AAEA,CAAC;AACC,oBAAkB;AAClB,UAAQ,IAAI,MAAM;AAClB,SAAO;AACT;AAEA,CAAC;AACC,WAAS;AACT,OAAK;AACL,mBAAiB;AACjB,cAAY;AACd;AAEA,CAAC;AACC,WAAS,KAAK;AACd,UAAQ;AACR,iBAAe;AACf,aAAW;AACX,eAAa;AACb,UAAQ;AACR,cAAY,IAAI,KAAK;AACrB,WAAS;AACT,eAAa;AACb,mBAAiB;AACjB,OAAK;AACL,aAAW;AACb;AAEA,CAfC,GAeG;AACF,WAAS;AACT,UAAQ;AACV;AAEA,CAAC;AACC;AAAA,IAAY;AAAA,MAAgB,MAAhB;AAAA,MAAwB,QAAQ,EAAhC;AAAA,MAAoC,QAAQ;AACxD,SAAO;AACT;AAEA,CALC,WAKW,MAAM,KAAK;AACrB,aAAW,WAAW;AACtB,cAAY,EAAE,IAAI,KAAK,KAAK,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE;AAC7C;AAEA,CAAC;AACC,oBAAkB;AAClB,SAAO;AACT;AAEA,CALC,aAKa,MAAM,KAAK;AACvB,oBAAkB;AAClB,aAAW,WAAW;AACxB;AAEA,CAAC;AACC,SAAO;AACP,UAAQ;AACR,UAAQ,IAAI,MAAM;AAClB,cAAY,IAAI,MAAM;AACtB,iBAAe;AACf,aAAW,KAAK,GAAG,OAAO;AAC5B;AAEA,WAHa;AAIX;AAAK,eAAW,OAAO;AAAO;AAC9B;AAAO,eAAW,OAAO;AAAS;AACpC;AAGA,OAAO,CAAC,SAAS,EAAE;AACjB,GAnMD;AAoMG,aAAS;AACX;AAEA,GA9LD;AA+LG,aAAS;AACT,gBAAY;AACd;AAEA,GAhJD;AAiJG,2BAAuB;AACvB,SAAK;AACP;AAEA,GA7ED;AA8EG,oBAAgB;AAClB;AAEA,GAjMD,gBAiMkB;AACf,eAAW;AACb;AACF;AAEA,OAAO,CAAC,SAAS,EAAE;AACjB,GAlND;AAmNG,aAAS;AACX;AAEA,GAnLD;AAoLG,aAAS;AACX;AACF;", "names": []}
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using System.Security.Claims;
using WebApiPfe.DTOs.Admin;
using WebApiPfe.Services.Interfaces;

namespace WebApiPfe.Controllers
{
    [ApiController]
    [Route("api/promotion-gestion")]
    [Authorize]
    public class PromotionGestionController : ControllerBase
    {
        private readonly IPromotionGestionService _promotionService;
        private readonly ILogger<PromotionGestionController> _logger;

        public PromotionGestionController(
            IPromotionGestionService promotionService,
            ILogger<PromotionGestionController> logger)
        {
            _promotionService = promotionService;
            _logger = logger;
        }

        /// <summary>
        /// Obtenir toutes les promotions avec filtres (Admin)
        /// </summary>
        [HttpGet]
        [Authorize(Roles = "Admin")]
        public async Task<ActionResult<List<PromotionGestionDto>>> GetPromotions([FromQuery] PromotionFilterDto filter)
        {
            try
            {
                var promotions = await _promotionService.GetPromotionsAsync(filter);
                return Ok(promotions);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Erreur lors de la récupération des promotions");
                return StatusCode(500, new { message = "Erreur interne du serveur" });
            }
        }

        /// <summary>
        /// Obtenir une promotion spécifique
        /// </summary>
        [HttpGet("{id}")]
        [Authorize(Roles = "Admin,Fournisseur")]
        public async Task<ActionResult<PromotionGestionDto>> GetPromotion(int id)
        {
            try
            {
                var userId = GetCurrentUserId();
                var userRole = GetCurrentUserRole();

                var promotion = await _promotionService.GetPromotionAsync(id);
                if (promotion == null)
                    return NotFound(new { message = "Promotion non trouvée" });

                // Vérifier les permissions pour les fournisseurs
                if (userRole == "Fournisseur" && promotion.FournisseurId != userId)
                    return Forbid();

                return Ok(promotion);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Erreur lors de la récupération de la promotion {Id}", id);
                return StatusCode(500, new { message = "Erreur interne du serveur" });
            }
        }

        /// <summary>
        /// Créer une nouvelle promotion
        /// </summary>
        [HttpPost]
        [Authorize(Roles = "Admin,Fournisseur")]
        public async Task<ActionResult<PromotionGestionDto>> CreatePromotion([FromBody] PromotionCreateDto dto)
        {
            try
            {
                var userId = GetCurrentUserId();
                var userRole = GetCurrentUserRole();

                // Pour les fournisseurs, forcer l'ID fournisseur
                int? fournisseurId = userRole == "Fournisseur" ? userId : dto.FournisseurId;

                var promotion = await _promotionService.CreatePromotionAsync(dto, fournisseurId);
                return CreatedAtAction(nameof(GetPromotion), new { id = promotion.Id }, promotion);
            }
            catch (InvalidOperationException ex)
            {
                return BadRequest(new { message = ex.Message });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Erreur lors de la création de la promotion");
                return StatusCode(500, new { message = "Erreur interne du serveur" });
            }
        }

        /// <summary>
        /// Mettre à jour une promotion
        /// </summary>
        [HttpPut("{id}")]
        [Authorize(Roles = "Admin,Fournisseur")]
        public async Task<ActionResult<PromotionGestionDto>> UpdatePromotion(int id, [FromBody] PromotionUpdateDto dto)
        {
            try
            {
                var userId = GetCurrentUserId();
                var userRole = GetCurrentUserRole();

                // Vérifier les permissions pour les fournisseurs
                if (userRole == "Fournisseur")
                {
                    var existingPromotion = await _promotionService.GetPromotionAsync(id);
                    if (existingPromotion?.FournisseurId != userId)
                        return Forbid();
                }

                var promotion = await _promotionService.UpdatePromotionAsync(id, dto);
                return Ok(promotion);
            }
            catch (KeyNotFoundException ex)
            {
                return NotFound(new { message = ex.Message });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Erreur lors de la mise à jour de la promotion {Id}", id);
                return StatusCode(500, new { message = "Erreur interne du serveur" });
            }
        }

        /// <summary>
        /// Supprimer une promotion
        /// </summary>
        [HttpDelete("{id}")]
        [Authorize(Roles = "Admin,Fournisseur")]
        public async Task<ActionResult> DeletePromotion(int id)
        {
            try
            {
                var userId = GetCurrentUserId();
                var userRole = GetCurrentUserRole();

                // Vérifier les permissions pour les fournisseurs
                if (userRole == "Fournisseur")
                {
                    var existingPromotion = await _promotionService.GetPromotionAsync(id);
                    if (existingPromotion?.FournisseurId != userId)
                        return Forbid();
                }

                await _promotionService.DeletePromotionAsync(id);
                return NoContent();
            }
            catch (KeyNotFoundException ex)
            {
                return NotFound(new { message = ex.Message });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Erreur lors de la suppression de la promotion {Id}", id);
                return StatusCode(500, new { message = "Erreur interne du serveur" });
            }
        }

        /// <summary>
        /// Activer/Désactiver une promotion
        /// </summary>
        [HttpPatch("{id}/toggle")]
        [Authorize(Roles = "Admin,Fournisseur")]
        public async Task<ActionResult<PromotionGestionDto>> TogglePromotion(int id)
        {
            try
            {
                var userId = GetCurrentUserId();
                var userRole = GetCurrentUserRole();

                // Vérifier les permissions pour les fournisseurs
                if (userRole == "Fournisseur")
                {
                    var existingPromotion = await _promotionService.GetPromotionAsync(id);
                    if (existingPromotion?.FournisseurId != userId)
                        return Forbid();
                }

                var promotion = await _promotionService.TogglePromotionAsync(id);
                return Ok(promotion);
            }
            catch (KeyNotFoundException ex)
            {
                return NotFound(new { message = ex.Message });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Erreur lors du changement d'état de la promotion {Id}", id);
                return StatusCode(500, new { message = "Erreur interne du serveur" });
            }
        }

        /// <summary>
        /// Valider un code de promotion
        /// </summary>
        [HttpPost("validate")]
        [AllowAnonymous]
        public async Task<ActionResult<PromotionValidationResultDto>> ValidatePromotion([FromBody] ValidatePromotionDto dto)
        {
            try
            {
                var result = await _promotionService.ValidatePromotionAsync(dto);
                return Ok(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Erreur lors de la validation de la promotion {Code}", dto.Code);
                return StatusCode(500, new { message = "Erreur interne du serveur" });
            }
        }

        /// <summary>
        /// Obtenir les promotions applicables
        /// </summary>
        [HttpGet("applicable")]
        [AllowAnonymous]
        public async Task<ActionResult<List<PromotionGestionDto>>> GetApplicablePromotions(
            [FromQuery] int? produitId = null,
            [FromQuery] decimal? montantCommande = null)
        {
            try
            {
                var promotions = await _promotionService.GetApplicablePromotionsAsync(produitId, montantCommande);
                return Ok(promotions);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Erreur lors de la récupération des promotions applicables");
                return StatusCode(500, new { message = "Erreur interne du serveur" });
            }
        }

        /// <summary>
        /// Obtenir les statistiques des promotions (Admin)
        /// </summary>
        [HttpGet("statistiques")]
        [Authorize(Roles = "Admin")]
        public async Task<ActionResult<PromotionStatsDto>> GetPromotionStats()
        {
            try
            {
                var stats = await _promotionService.GetPromotionStatsAsync();
                return Ok(stats);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Erreur lors de la récupération des statistiques des promotions");
                return StatusCode(500, new { message = "Erreur interne du serveur" });
            }
        }

        /// <summary>
        /// Obtenir les promotions d'un fournisseur
        /// </summary>
        [HttpGet("fournisseur")]
        [Authorize(Roles = "Fournisseur")]
        public async Task<ActionResult<List<PromotionGestionDto>>> GetPromotionsFournisseur([FromQuery] PromotionFilterDto filter)
        {
            try
            {
                var fournisseurId = GetCurrentUserId();
                var promotions = await _promotionService.GetPromotionsFournisseurAsync(fournisseurId, filter);
                return Ok(promotions);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Erreur lors de la récupération des promotions du fournisseur");
                return StatusCode(500, new { message = "Erreur interne du serveur" });
            }
        }

        /// <summary>
        /// Obtenir les statistiques des promotions pour un fournisseur
        /// </summary>
        [HttpGet("statistiques/fournisseur")]
        [Authorize(Roles = "Fournisseur")]
        public async Task<ActionResult<PromotionStatsDto>> GetPromotionStatsFournisseur()
        {
            try
            {
                var fournisseurId = GetCurrentUserId();
                var stats = await _promotionService.GetPromotionStatsFournisseurAsync(fournisseurId);
                return Ok(stats);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Erreur lors de la récupération des statistiques des promotions du fournisseur");
                return StatusCode(500, new { message = "Erreur interne du serveur" });
            }
        }

        /// <summary>
        /// Obtenir les promotions qui expirent bientôt (Admin)
        /// </summary>
        [HttpGet("expirants")]
        [Authorize(Roles = "Admin")]
        public async Task<ActionResult<List<PromotionGestionDto>>> GetPromotionsExpirants([FromQuery] int jours = 7)
        {
            try
            {
                var promotions = await _promotionService.GetPromotionsExpirantsAsync(jours);
                return Ok(promotions);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Erreur lors de la récupération des promotions expirants");
                return StatusCode(500, new { message = "Erreur interne du serveur" });
            }
        }

        /// <summary>
        /// Obtenir les promotions populaires (Admin)
        /// </summary>
        [HttpGet("populaires")]
        [Authorize(Roles = "Admin")]
        public async Task<ActionResult<List<PromotionGestionDto>>> GetPromotionsPopulaires([FromQuery] int limit = 10)
        {
            try
            {
                var promotions = await _promotionService.GetPromotionsPopulairesAsync(limit);
                return Ok(promotions);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Erreur lors de la récupération des promotions populaires");
                return StatusCode(500, new { message = "Erreur interne du serveur" });
            }
        }

        /// <summary>
        /// Vérifier l'unicité d'un code de promotion
        /// </summary>
        [HttpGet("check-code/{code}")]
        [Authorize(Roles = "Admin,Fournisseur")]
        public async Task<ActionResult<bool>> CheckCodeExists(string code, [FromQuery] int? excludeId = null)
        {
            try
            {
                var exists = await _promotionService.CodeExistsAsync(code, excludeId);
                return Ok(new { exists });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Erreur lors de la vérification du code {Code}", code);
                return StatusCode(500, new { message = "Erreur interne du serveur" });
            }
        }

        // Méthodes d'aide privées
        private int GetCurrentUserId()
        {
            var userIdClaim = User.FindFirst(ClaimTypes.NameIdentifier)?.Value;
            return int.TryParse(userIdClaim, out var userId) ? userId : 0;
        }

        private string GetCurrentUserRole()
        {
            return User.FindFirst(ClaimTypes.Role)?.Value ?? "";
        }
    }
}

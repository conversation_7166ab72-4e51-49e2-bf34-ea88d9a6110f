import {
  DemandeService,
  StatutDemande
} from "./chunk-WQ24PYVH.js";
import {
  DefaultValueAccessor,
  FormsModule,
  NgControlStatus,
  NgModel,
  NgSelectOption,
  SelectControlValueAccessor,
  ɵNgSelectMultipleOption
} from "./chunk-HQBVYEOO.js";
import "./chunk-GFHHPDQ6.js";
import "./chunk-7JDDWGD3.js";
import {
  CommonModule,
  Component,
  NgForOf,
  NgIf,
  setClassMetadata,
  ɵsetClassDebugInfo,
  ɵɵadvance,
  ɵɵclassProp,
  ɵɵdefineComponent,
  ɵɵdirectiveInject,
  ɵɵelement,
  ɵɵelementEnd,
  ɵɵelementStart,
  ɵɵgetCurrentView,
  ɵɵlistener,
  ɵɵnextContext,
  ɵɵproperty,
  ɵɵresetView,
  ɵɵrestoreView,
  ɵɵsanitizeUrl,
  ɵɵstyleProp,
  ɵɵtemplate,
  ɵɵtext,
  ɵɵtextInterpolate,
  ɵɵtextInterpolate1,
  ɵɵtwoWayBindingSet,
  ɵɵtwoWayListener,
  ɵɵtwoWayProperty
} from "./chunk-UBZQS7JS.js";

// src/app/components/admin/demandes-management/demandes-management.component.ts
function DemandesManagementComponent_span_9_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "span", 7);
    \u0275\u0275text(1);
    \u0275\u0275elementEnd();
  }
  if (rf & 2) {
    const ctx_r0 = \u0275\u0275nextContext();
    \u0275\u0275advance();
    \u0275\u0275textInterpolate(ctx_r0.demandesCategories.length);
  }
}
function DemandesManagementComponent_span_12_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "span", 7);
    \u0275\u0275text(1);
    \u0275\u0275elementEnd();
  }
  if (rf & 2) {
    const ctx_r0 = \u0275\u0275nextContext();
    \u0275\u0275advance();
    \u0275\u0275textInterpolate(ctx_r0.demandesSousCategories.length);
  }
}
function DemandesManagementComponent_div_13_div_11_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "div", 18)(1, "p");
    \u0275\u0275text(2, "Aucune demande de cat\xE9gorie trouv\xE9e");
    \u0275\u0275elementEnd()();
  }
}
function DemandesManagementComponent_div_13_div_13_div_19_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "div", 26);
    \u0275\u0275element(1, "img", 27);
    \u0275\u0275elementEnd();
  }
  if (rf & 2) {
    const demande_r3 = \u0275\u0275nextContext().$implicit;
    \u0275\u0275advance();
    \u0275\u0275property("src", demande_r3.imageUrl, \u0275\u0275sanitizeUrl);
  }
}
function DemandesManagementComponent_div_13_div_13_div_20_Template(rf, ctx) {
  if (rf & 1) {
    const _r4 = \u0275\u0275getCurrentView();
    \u0275\u0275elementStart(0, "div", 28)(1, "button", 29);
    \u0275\u0275listener("click", function DemandesManagementComponent_div_13_div_13_div_20_Template_button_click_1_listener() {
      \u0275\u0275restoreView(_r4);
      const demande_r3 = \u0275\u0275nextContext().$implicit;
      const ctx_r0 = \u0275\u0275nextContext(2);
      return \u0275\u0275resetView(ctx_r0.traiterDemandeCategorie(demande_r3.id, 1));
    });
    \u0275\u0275text(2, " \u2705 Approuver ");
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(3, "button", 30);
    \u0275\u0275listener("click", function DemandesManagementComponent_div_13_div_13_div_20_Template_button_click_3_listener() {
      \u0275\u0275restoreView(_r4);
      const demande_r3 = \u0275\u0275nextContext().$implicit;
      const ctx_r0 = \u0275\u0275nextContext(2);
      return \u0275\u0275resetView(ctx_r0.openRejectModal(demande_r3, "categorie"));
    });
    \u0275\u0275text(4, " \u274C Rejeter ");
    \u0275\u0275elementEnd()();
  }
}
function DemandesManagementComponent_div_13_div_13_div_21_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "div", 31)(1, "p")(2, "strong");
    \u0275\u0275text(3, "Commentaire admin:");
    \u0275\u0275elementEnd();
    \u0275\u0275text(4);
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(5, "p")(6, "strong");
    \u0275\u0275text(7, "Trait\xE9 le:");
    \u0275\u0275elementEnd();
    \u0275\u0275text(8);
    \u0275\u0275elementEnd()();
  }
  if (rf & 2) {
    const demande_r3 = \u0275\u0275nextContext().$implicit;
    const ctx_r0 = \u0275\u0275nextContext(2);
    \u0275\u0275advance(4);
    \u0275\u0275textInterpolate1(" ", demande_r3.commentaireAdmin, "");
    \u0275\u0275advance(4);
    \u0275\u0275textInterpolate1(" ", ctx_r0.formatDate(demande_r3.dateTraitement), "");
  }
}
function DemandesManagementComponent_div_13_div_13_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "div", 19)(1, "div", 20)(2, "h3");
    \u0275\u0275text(3);
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(4, "span", 21);
    \u0275\u0275text(5);
    \u0275\u0275elementEnd()();
    \u0275\u0275elementStart(6, "div", 22)(7, "p")(8, "strong");
    \u0275\u0275text(9, "Description:");
    \u0275\u0275elementEnd();
    \u0275\u0275text(10);
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(11, "p")(12, "strong");
    \u0275\u0275text(13, "Demand\xE9 par:");
    \u0275\u0275elementEnd();
    \u0275\u0275text(14);
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(15, "p")(16, "strong");
    \u0275\u0275text(17, "Date de demande:");
    \u0275\u0275elementEnd();
    \u0275\u0275text(18);
    \u0275\u0275elementEnd();
    \u0275\u0275template(19, DemandesManagementComponent_div_13_div_13_div_19_Template, 2, 1, "div", 23);
    \u0275\u0275elementEnd();
    \u0275\u0275template(20, DemandesManagementComponent_div_13_div_13_div_20_Template, 5, 0, "div", 24)(21, DemandesManagementComponent_div_13_div_13_div_21_Template, 9, 2, "div", 25);
    \u0275\u0275elementEnd();
  }
  if (rf & 2) {
    const demande_r3 = ctx.$implicit;
    const ctx_r0 = \u0275\u0275nextContext(2);
    \u0275\u0275advance(3);
    \u0275\u0275textInterpolate(demande_r3.nom);
    \u0275\u0275advance();
    \u0275\u0275styleProp("background-color", ctx_r0.getStatutColor(demande_r3.statut));
    \u0275\u0275advance();
    \u0275\u0275textInterpolate1(" ", ctx_r0.getStatutLabel(demande_r3.statut), " ");
    \u0275\u0275advance(5);
    \u0275\u0275textInterpolate1(" ", demande_r3.description, "");
    \u0275\u0275advance(4);
    \u0275\u0275textInterpolate1(" ", demande_r3.fournisseurNom, "");
    \u0275\u0275advance(4);
    \u0275\u0275textInterpolate1(" ", ctx_r0.formatDate(demande_r3.dateDemande), "");
    \u0275\u0275advance();
    \u0275\u0275property("ngIf", demande_r3.imageUrl);
    \u0275\u0275advance();
    \u0275\u0275property("ngIf", demande_r3.statut === 0);
    \u0275\u0275advance();
    \u0275\u0275property("ngIf", demande_r3.statut !== 0 && demande_r3.commentaireAdmin);
  }
}
function DemandesManagementComponent_div_13_Template(rf, ctx) {
  if (rf & 1) {
    const _r2 = \u0275\u0275getCurrentView();
    \u0275\u0275elementStart(0, "div", 8)(1, "div", 9)(2, "select", 10);
    \u0275\u0275twoWayListener("ngModelChange", function DemandesManagementComponent_div_13_Template_select_ngModelChange_2_listener($event) {
      \u0275\u0275restoreView(_r2);
      const ctx_r0 = \u0275\u0275nextContext();
      \u0275\u0275twoWayBindingSet(ctx_r0.filtreStatutCategories, $event) || (ctx_r0.filtreStatutCategories = $event);
      return \u0275\u0275resetView($event);
    });
    \u0275\u0275listener("change", function DemandesManagementComponent_div_13_Template_select_change_2_listener() {
      \u0275\u0275restoreView(_r2);
      const ctx_r0 = \u0275\u0275nextContext();
      return \u0275\u0275resetView(ctx_r0.loadDemandesCategories());
    });
    \u0275\u0275elementStart(3, "option", 11);
    \u0275\u0275text(4, "Tous les statuts");
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(5, "option", 12);
    \u0275\u0275text(6, "En attente");
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(7, "option", 13);
    \u0275\u0275text(8, "Approuv\xE9es");
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(9, "option", 14);
    \u0275\u0275text(10, "Rejet\xE9es");
    \u0275\u0275elementEnd()()();
    \u0275\u0275template(11, DemandesManagementComponent_div_13_div_11_Template, 3, 0, "div", 15);
    \u0275\u0275elementStart(12, "div", 16);
    \u0275\u0275template(13, DemandesManagementComponent_div_13_div_13_Template, 22, 10, "div", 17);
    \u0275\u0275elementEnd()();
  }
  if (rf & 2) {
    const ctx_r0 = \u0275\u0275nextContext();
    \u0275\u0275advance(2);
    \u0275\u0275twoWayProperty("ngModel", ctx_r0.filtreStatutCategories);
    \u0275\u0275advance(9);
    \u0275\u0275property("ngIf", ctx_r0.demandesCategories.length === 0);
    \u0275\u0275advance(2);
    \u0275\u0275property("ngForOf", ctx_r0.demandesCategories);
  }
}
function DemandesManagementComponent_div_14_div_11_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "div", 18)(1, "p");
    \u0275\u0275text(2, "Aucune demande de sous-cat\xE9gorie trouv\xE9e");
    \u0275\u0275elementEnd()();
  }
}
function DemandesManagementComponent_div_14_div_13_div_23_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "div", 26);
    \u0275\u0275element(1, "img", 32);
    \u0275\u0275elementEnd();
  }
  if (rf & 2) {
    const demande_r6 = \u0275\u0275nextContext().$implicit;
    \u0275\u0275advance();
    \u0275\u0275property("src", demande_r6.imageUrl, \u0275\u0275sanitizeUrl);
  }
}
function DemandesManagementComponent_div_14_div_13_div_24_Template(rf, ctx) {
  if (rf & 1) {
    const _r7 = \u0275\u0275getCurrentView();
    \u0275\u0275elementStart(0, "div", 28)(1, "button", 29);
    \u0275\u0275listener("click", function DemandesManagementComponent_div_14_div_13_div_24_Template_button_click_1_listener() {
      \u0275\u0275restoreView(_r7);
      const demande_r6 = \u0275\u0275nextContext().$implicit;
      const ctx_r0 = \u0275\u0275nextContext(2);
      return \u0275\u0275resetView(ctx_r0.traiterDemandeSousCategorie(demande_r6.id, 1));
    });
    \u0275\u0275text(2, " \u2705 Approuver ");
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(3, "button", 30);
    \u0275\u0275listener("click", function DemandesManagementComponent_div_14_div_13_div_24_Template_button_click_3_listener() {
      \u0275\u0275restoreView(_r7);
      const demande_r6 = \u0275\u0275nextContext().$implicit;
      const ctx_r0 = \u0275\u0275nextContext(2);
      return \u0275\u0275resetView(ctx_r0.openRejectModal(demande_r6, "sous-categorie"));
    });
    \u0275\u0275text(4, " \u274C Rejeter ");
    \u0275\u0275elementEnd()();
  }
}
function DemandesManagementComponent_div_14_div_13_div_25_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "div", 31)(1, "p")(2, "strong");
    \u0275\u0275text(3, "Commentaire admin:");
    \u0275\u0275elementEnd();
    \u0275\u0275text(4);
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(5, "p")(6, "strong");
    \u0275\u0275text(7, "Trait\xE9 le:");
    \u0275\u0275elementEnd();
    \u0275\u0275text(8);
    \u0275\u0275elementEnd()();
  }
  if (rf & 2) {
    const demande_r6 = \u0275\u0275nextContext().$implicit;
    const ctx_r0 = \u0275\u0275nextContext(2);
    \u0275\u0275advance(4);
    \u0275\u0275textInterpolate1(" ", demande_r6.commentaireAdmin, "");
    \u0275\u0275advance(4);
    \u0275\u0275textInterpolate1(" ", ctx_r0.formatDate(demande_r6.dateTraitement), "");
  }
}
function DemandesManagementComponent_div_14_div_13_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "div", 19)(1, "div", 20)(2, "h3");
    \u0275\u0275text(3);
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(4, "span", 21);
    \u0275\u0275text(5);
    \u0275\u0275elementEnd()();
    \u0275\u0275elementStart(6, "div", 22)(7, "p")(8, "strong");
    \u0275\u0275text(9, "Description:");
    \u0275\u0275elementEnd();
    \u0275\u0275text(10);
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(11, "p")(12, "strong");
    \u0275\u0275text(13, "Cat\xE9gorie parent:");
    \u0275\u0275elementEnd();
    \u0275\u0275text(14);
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(15, "p")(16, "strong");
    \u0275\u0275text(17, "Demand\xE9 par:");
    \u0275\u0275elementEnd();
    \u0275\u0275text(18);
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(19, "p")(20, "strong");
    \u0275\u0275text(21, "Date de demande:");
    \u0275\u0275elementEnd();
    \u0275\u0275text(22);
    \u0275\u0275elementEnd();
    \u0275\u0275template(23, DemandesManagementComponent_div_14_div_13_div_23_Template, 2, 1, "div", 23);
    \u0275\u0275elementEnd();
    \u0275\u0275template(24, DemandesManagementComponent_div_14_div_13_div_24_Template, 5, 0, "div", 24)(25, DemandesManagementComponent_div_14_div_13_div_25_Template, 9, 2, "div", 25);
    \u0275\u0275elementEnd();
  }
  if (rf & 2) {
    const demande_r6 = ctx.$implicit;
    const ctx_r0 = \u0275\u0275nextContext(2);
    \u0275\u0275advance(3);
    \u0275\u0275textInterpolate(demande_r6.nom);
    \u0275\u0275advance();
    \u0275\u0275styleProp("background-color", ctx_r0.getStatutColor(demande_r6.statut));
    \u0275\u0275advance();
    \u0275\u0275textInterpolate1(" ", ctx_r0.getStatutLabel(demande_r6.statut), " ");
    \u0275\u0275advance(5);
    \u0275\u0275textInterpolate1(" ", demande_r6.description, "");
    \u0275\u0275advance(4);
    \u0275\u0275textInterpolate1(" ", demande_r6.categorieNom, "");
    \u0275\u0275advance(4);
    \u0275\u0275textInterpolate1(" ", demande_r6.fournisseurNom, "");
    \u0275\u0275advance(4);
    \u0275\u0275textInterpolate1(" ", ctx_r0.formatDate(demande_r6.dateDemande), "");
    \u0275\u0275advance();
    \u0275\u0275property("ngIf", demande_r6.imageUrl);
    \u0275\u0275advance();
    \u0275\u0275property("ngIf", demande_r6.statut === 0);
    \u0275\u0275advance();
    \u0275\u0275property("ngIf", demande_r6.statut !== 0 && demande_r6.commentaireAdmin);
  }
}
function DemandesManagementComponent_div_14_Template(rf, ctx) {
  if (rf & 1) {
    const _r5 = \u0275\u0275getCurrentView();
    \u0275\u0275elementStart(0, "div", 8)(1, "div", 9)(2, "select", 10);
    \u0275\u0275twoWayListener("ngModelChange", function DemandesManagementComponent_div_14_Template_select_ngModelChange_2_listener($event) {
      \u0275\u0275restoreView(_r5);
      const ctx_r0 = \u0275\u0275nextContext();
      \u0275\u0275twoWayBindingSet(ctx_r0.filtreStatutSousCategories, $event) || (ctx_r0.filtreStatutSousCategories = $event);
      return \u0275\u0275resetView($event);
    });
    \u0275\u0275listener("change", function DemandesManagementComponent_div_14_Template_select_change_2_listener() {
      \u0275\u0275restoreView(_r5);
      const ctx_r0 = \u0275\u0275nextContext();
      return \u0275\u0275resetView(ctx_r0.loadDemandesSousCategories());
    });
    \u0275\u0275elementStart(3, "option", 11);
    \u0275\u0275text(4, "Tous les statuts");
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(5, "option", 12);
    \u0275\u0275text(6, "En attente");
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(7, "option", 13);
    \u0275\u0275text(8, "Approuv\xE9es");
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(9, "option", 14);
    \u0275\u0275text(10, "Rejet\xE9es");
    \u0275\u0275elementEnd()()();
    \u0275\u0275template(11, DemandesManagementComponent_div_14_div_11_Template, 3, 0, "div", 15);
    \u0275\u0275elementStart(12, "div", 16);
    \u0275\u0275template(13, DemandesManagementComponent_div_14_div_13_Template, 26, 11, "div", 17);
    \u0275\u0275elementEnd()();
  }
  if (rf & 2) {
    const ctx_r0 = \u0275\u0275nextContext();
    \u0275\u0275advance(2);
    \u0275\u0275twoWayProperty("ngModel", ctx_r0.filtreStatutSousCategories);
    \u0275\u0275advance(9);
    \u0275\u0275property("ngIf", ctx_r0.demandesSousCategories.length === 0);
    \u0275\u0275advance(2);
    \u0275\u0275property("ngForOf", ctx_r0.demandesSousCategories);
  }
}
function DemandesManagementComponent_div_15_Template(rf, ctx) {
  if (rf & 1) {
    const _r8 = \u0275\u0275getCurrentView();
    \u0275\u0275elementStart(0, "div", 33);
    \u0275\u0275listener("click", function DemandesManagementComponent_div_15_Template_div_click_0_listener() {
      \u0275\u0275restoreView(_r8);
      const ctx_r0 = \u0275\u0275nextContext();
      return \u0275\u0275resetView(ctx_r0.closeRejectModal());
    });
    \u0275\u0275elementStart(1, "div", 34);
    \u0275\u0275listener("click", function DemandesManagementComponent_div_15_Template_div_click_1_listener($event) {
      \u0275\u0275restoreView(_r8);
      return \u0275\u0275resetView($event.stopPropagation());
    });
    \u0275\u0275elementStart(2, "div", 35)(3, "h3");
    \u0275\u0275text(4, "Rejeter la demande");
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(5, "button", 36);
    \u0275\u0275listener("click", function DemandesManagementComponent_div_15_Template_button_click_5_listener() {
      \u0275\u0275restoreView(_r8);
      const ctx_r0 = \u0275\u0275nextContext();
      return \u0275\u0275resetView(ctx_r0.closeRejectModal());
    });
    \u0275\u0275text(6, "\xD7");
    \u0275\u0275elementEnd()();
    \u0275\u0275elementStart(7, "div", 37)(8, "p");
    \u0275\u0275text(9, "\xCAtes-vous s\xFBr de vouloir rejeter cette demande ?");
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(10, "textarea", 38);
    \u0275\u0275twoWayListener("ngModelChange", function DemandesManagementComponent_div_15_Template_textarea_ngModelChange_10_listener($event) {
      \u0275\u0275restoreView(_r8);
      const ctx_r0 = \u0275\u0275nextContext();
      \u0275\u0275twoWayBindingSet(ctx_r0.commentaireRejet, $event) || (ctx_r0.commentaireRejet = $event);
      return \u0275\u0275resetView($event);
    });
    \u0275\u0275text(11, "            ");
    \u0275\u0275elementEnd()();
    \u0275\u0275elementStart(12, "div", 39)(13, "button", 40);
    \u0275\u0275listener("click", function DemandesManagementComponent_div_15_Template_button_click_13_listener() {
      \u0275\u0275restoreView(_r8);
      const ctx_r0 = \u0275\u0275nextContext();
      return \u0275\u0275resetView(ctx_r0.closeRejectModal());
    });
    \u0275\u0275text(14, "Annuler");
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(15, "button", 30);
    \u0275\u0275listener("click", function DemandesManagementComponent_div_15_Template_button_click_15_listener() {
      \u0275\u0275restoreView(_r8);
      const ctx_r0 = \u0275\u0275nextContext();
      return \u0275\u0275resetView(ctx_r0.confirmReject());
    });
    \u0275\u0275text(16, "Rejeter");
    \u0275\u0275elementEnd()()()();
  }
  if (rf & 2) {
    const ctx_r0 = \u0275\u0275nextContext();
    \u0275\u0275advance(10);
    \u0275\u0275twoWayProperty("ngModel", ctx_r0.commentaireRejet);
  }
}
var DemandesManagementComponent = class _DemandesManagementComponent {
  demandeService;
  activeTab = "categories";
  demandesCategories = [];
  demandesSousCategories = [];
  filtreStatutCategories = "";
  filtreStatutSousCategories = "";
  showRejectModal = false;
  currentDemande = null;
  currentType = "categorie";
  commentaireRejet = "";
  constructor(demandeService) {
    this.demandeService = demandeService;
  }
  ngOnInit() {
    this.loadDemandesCategories();
  }
  loadDemandesCategories() {
    console.log("\u{1F504} Chargement des demandes de cat\xE9gories (Admin)...");
    if (this.filtreStatutCategories) {
      const statut = parseInt(this.filtreStatutCategories);
      console.log("\u{1F4CA} Filtrage par statut:", statut);
      this.demandeService.getDemandesCategoriesByStatut(statut).subscribe({
        next: (demandes) => {
          console.log("\u2705 Demandes re\xE7ues (par statut):", demandes);
          this.demandesCategories = demandes;
        },
        error: (error) => {
          console.error("\u274C Erreur lors du chargement des demandes par statut:", error);
        }
      });
    } else {
      console.log("\u{1F4CB} Chargement de toutes les demandes...");
      this.demandeService.getAllDemandesCategories().subscribe({
        next: (demandes) => {
          console.log("\u2705 Toutes les demandes re\xE7ues:", demandes);
          this.demandesCategories = demandes;
        },
        error: (error) => {
          console.error("\u274C Erreur lors du chargement de toutes les demandes:", error);
        }
      });
    }
  }
  loadDemandesSousCategories() {
    if (this.filtreStatutSousCategories) {
      const statut = parseInt(this.filtreStatutSousCategories);
      this.demandeService.getDemandesSousCategoriesByStatut(statut).subscribe((demandes) => this.demandesSousCategories = demandes);
    } else {
      this.demandeService.getAllDemandesSousCategories().subscribe((demandes) => this.demandesSousCategories = demandes);
    }
  }
  traiterDemandeCategorie(id, statut) {
    const traitement = { statut };
    this.demandeService.traiterDemandeCategorie(id, traitement).subscribe(() => {
      this.loadDemandesCategories();
    });
  }
  traiterDemandeSousCategorie(id, statut) {
    const traitement = { statut };
    this.demandeService.traiterDemandeSousCategorie(id, traitement).subscribe(() => {
      this.loadDemandesSousCategories();
    });
  }
  openRejectModal(demande, type) {
    this.currentDemande = demande;
    this.currentType = type;
    this.commentaireRejet = "";
    this.showRejectModal = true;
  }
  closeRejectModal() {
    this.showRejectModal = false;
    this.currentDemande = null;
    this.commentaireRejet = "";
  }
  confirmReject() {
    if (this.currentDemande) {
      const traitement = {
        statut: StatutDemande.Rejetee,
        commentaireAdmin: this.commentaireRejet || void 0
      };
      if (this.currentType === "categorie") {
        this.demandeService.traiterDemandeCategorie(this.currentDemande.id, traitement).subscribe(() => {
          this.loadDemandesCategories();
          this.closeRejectModal();
        });
      } else {
        this.demandeService.traiterDemandeSousCategorie(this.currentDemande.id, traitement).subscribe(() => {
          this.loadDemandesSousCategories();
          this.closeRejectModal();
        });
      }
    }
  }
  getStatutLabel(statut) {
    return this.demandeService.getStatutLabel(statut);
  }
  getStatutColor(statut) {
    return this.demandeService.getStatutColor(statut);
  }
  formatDate(date) {
    return new Date(date).toLocaleDateString("fr-FR", {
      year: "numeric",
      month: "long",
      day: "numeric",
      hour: "2-digit",
      minute: "2-digit"
    });
  }
  static \u0275fac = function DemandesManagementComponent_Factory(__ngFactoryType__) {
    return new (__ngFactoryType__ || _DemandesManagementComponent)(\u0275\u0275directiveInject(DemandeService));
  };
  static \u0275cmp = /* @__PURE__ */ \u0275\u0275defineComponent({ type: _DemandesManagementComponent, selectors: [["app-demandes-management"]], decls: 16, vars: 9, consts: [[1, "demandes-management"], [1, "page-header"], [1, "tabs"], [1, "tab-btn", 3, "click"], ["class", "badge", 4, "ngIf"], ["class", "tab-content", 4, "ngIf"], ["class", "modal-overlay", 3, "click", 4, "ngIf"], [1, "badge"], [1, "tab-content"], [1, "filters"], [3, "ngModelChange", "change", "ngModel"], ["value", ""], ["value", "0"], ["value", "1"], ["value", "2"], ["class", "empty-state", 4, "ngIf"], [1, "demandes-grid"], ["class", "demande-card", 4, "ngFor", "ngForOf"], [1, "empty-state"], [1, "demande-card"], [1, "demande-header"], [1, "statut-badge"], [1, "demande-content"], ["class", "image-preview", 4, "ngIf"], ["class", "demande-actions", 4, "ngIf"], ["class", "admin-comment", 4, "ngIf"], [1, "image-preview"], ["alt", "Image de la cat\xE9gorie", 3, "src"], [1, "demande-actions"], [1, "btn", "btn-success", 3, "click"], [1, "btn", "btn-danger", 3, "click"], [1, "admin-comment"], ["alt", "Image de la sous-cat\xE9gorie", 3, "src"], [1, "modal-overlay", 3, "click"], [1, "modal", 3, "click"], [1, "modal-header"], [1, "close-btn", 3, "click"], [1, "modal-content"], ["placeholder", "Commentaire (optionnel)", "rows", "3", 3, "ngModelChange", "ngModel"], [1, "modal-actions"], [1, "btn", "btn-secondary", 3, "click"]], template: function DemandesManagementComponent_Template(rf, ctx) {
    if (rf & 1) {
      \u0275\u0275elementStart(0, "div", 0)(1, "div", 1)(2, "h1");
      \u0275\u0275text(3, "\u{1F514} Gestion des Demandes");
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(4, "p");
      \u0275\u0275text(5, "G\xE9rez les demandes de cr\xE9ation de cat\xE9gories et sous-cat\xE9gories");
      \u0275\u0275elementEnd()();
      \u0275\u0275elementStart(6, "div", 2)(7, "button", 3);
      \u0275\u0275listener("click", function DemandesManagementComponent_Template_button_click_7_listener() {
        ctx.activeTab = "categories";
        return ctx.loadDemandesCategories();
      });
      \u0275\u0275text(8, " Demandes de Cat\xE9gories ");
      \u0275\u0275template(9, DemandesManagementComponent_span_9_Template, 2, 1, "span", 4);
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(10, "button", 3);
      \u0275\u0275listener("click", function DemandesManagementComponent_Template_button_click_10_listener() {
        ctx.activeTab = "sous-categories";
        return ctx.loadDemandesSousCategories();
      });
      \u0275\u0275text(11, " Demandes de Sous-cat\xE9gories ");
      \u0275\u0275template(12, DemandesManagementComponent_span_12_Template, 2, 1, "span", 4);
      \u0275\u0275elementEnd()();
      \u0275\u0275template(13, DemandesManagementComponent_div_13_Template, 14, 3, "div", 5)(14, DemandesManagementComponent_div_14_Template, 14, 3, "div", 5)(15, DemandesManagementComponent_div_15_Template, 17, 1, "div", 6);
      \u0275\u0275elementEnd();
    }
    if (rf & 2) {
      \u0275\u0275advance(7);
      \u0275\u0275classProp("active", ctx.activeTab === "categories");
      \u0275\u0275advance(2);
      \u0275\u0275property("ngIf", ctx.demandesCategories.length > 0);
      \u0275\u0275advance();
      \u0275\u0275classProp("active", ctx.activeTab === "sous-categories");
      \u0275\u0275advance(2);
      \u0275\u0275property("ngIf", ctx.demandesSousCategories.length > 0);
      \u0275\u0275advance();
      \u0275\u0275property("ngIf", ctx.activeTab === "categories");
      \u0275\u0275advance();
      \u0275\u0275property("ngIf", ctx.activeTab === "sous-categories");
      \u0275\u0275advance();
      \u0275\u0275property("ngIf", ctx.showRejectModal);
    }
  }, dependencies: [CommonModule, NgForOf, NgIf, FormsModule, NgSelectOption, \u0275NgSelectMultipleOption, DefaultValueAccessor, SelectControlValueAccessor, NgControlStatus, NgModel], styles: ["\n\n.demandes-management[_ngcontent-%COMP%] {\n  padding: 2rem;\n}\n.page-header[_ngcontent-%COMP%] {\n  margin-bottom: 2rem;\n}\n.page-header[_ngcontent-%COMP%]   h1[_ngcontent-%COMP%] {\n  margin: 0 0 0.5rem 0;\n  color: #1e293b;\n}\n.page-header[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\n  margin: 0;\n  color: #64748b;\n}\n.tabs[_ngcontent-%COMP%] {\n  display: flex;\n  border-bottom: 2px solid #e2e8f0;\n  margin-bottom: 2rem;\n}\n.tab-btn[_ngcontent-%COMP%] {\n  padding: 1rem 2rem;\n  background: none;\n  border: none;\n  cursor: pointer;\n  font-size: 1rem;\n  color: #64748b;\n  border-bottom: 2px solid transparent;\n  transition: all 0.3s ease;\n  position: relative;\n}\n.tab-btn.active[_ngcontent-%COMP%] {\n  color: #3b82f6;\n  border-bottom-color: #3b82f6;\n}\n.badge[_ngcontent-%COMP%] {\n  background: #ef4444;\n  color: white;\n  border-radius: 50%;\n  padding: 0.25rem 0.5rem;\n  font-size: 0.75rem;\n  margin-left: 0.5rem;\n}\n.filters[_ngcontent-%COMP%] {\n  margin-bottom: 1.5rem;\n}\n.filters[_ngcontent-%COMP%]   select[_ngcontent-%COMP%] {\n  padding: 0.5rem 1rem;\n  border: 1px solid #d1d5db;\n  border-radius: 8px;\n  background: white;\n}\n.empty-state[_ngcontent-%COMP%] {\n  text-align: center;\n  padding: 3rem;\n  color: #64748b;\n}\n.demandes-grid[_ngcontent-%COMP%] {\n  display: grid;\n  grid-template-columns: repeat(auto-fill, minmax(400px, 1fr));\n  gap: 1.5rem;\n}\n.demande-card[_ngcontent-%COMP%] {\n  background: white;\n  border-radius: 12px;\n  padding: 1.5rem;\n  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);\n  border: 1px solid #e2e8f0;\n}\n.demande-header[_ngcontent-%COMP%] {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 1rem;\n}\n.demande-header[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%] {\n  margin: 0;\n  color: #1e293b;\n}\n.statut-badge[_ngcontent-%COMP%] {\n  padding: 0.25rem 0.75rem;\n  border-radius: 20px;\n  color: white;\n  font-size: 0.875rem;\n  font-weight: 500;\n}\n.demande-content[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\n  margin: 0.5rem 0;\n  color: #475569;\n}\n.image-preview[_ngcontent-%COMP%] {\n  margin: 1rem 0;\n}\n.image-preview[_ngcontent-%COMP%]   img[_ngcontent-%COMP%] {\n  max-width: 100px;\n  max-height: 100px;\n  border-radius: 8px;\n  object-fit: cover;\n}\n.demande-actions[_ngcontent-%COMP%] {\n  display: flex;\n  gap: 0.5rem;\n  margin-top: 1rem;\n  padding-top: 1rem;\n  border-top: 1px solid #e2e8f0;\n}\n.admin-comment[_ngcontent-%COMP%] {\n  margin-top: 1rem;\n  padding-top: 1rem;\n  border-top: 1px solid #e2e8f0;\n  background: #f8fafc;\n  padding: 1rem;\n  border-radius: 8px;\n}\n.btn[_ngcontent-%COMP%] {\n  padding: 0.5rem 1rem;\n  border: none;\n  border-radius: 6px;\n  cursor: pointer;\n  font-size: 0.875rem;\n  transition: all 0.3s ease;\n}\n.btn-success[_ngcontent-%COMP%] {\n  background: #10b981;\n  color: white;\n}\n.btn-success[_ngcontent-%COMP%]:hover {\n  background: #059669;\n}\n.btn-danger[_ngcontent-%COMP%] {\n  background: #ef4444;\n  color: white;\n}\n.btn-danger[_ngcontent-%COMP%]:hover {\n  background: #dc2626;\n}\n.btn-secondary[_ngcontent-%COMP%] {\n  background: #6b7280;\n  color: white;\n}\n.btn-secondary[_ngcontent-%COMP%]:hover {\n  background: #4b5563;\n}\n.modal-overlay[_ngcontent-%COMP%] {\n  position: fixed;\n  top: 0;\n  left: 0;\n  right: 0;\n  bottom: 0;\n  background: rgba(0, 0, 0, 0.5);\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  z-index: 1000;\n}\n.modal[_ngcontent-%COMP%] {\n  background: white;\n  border-radius: 12px;\n  width: 90%;\n  max-width: 500px;\n  max-height: 90vh;\n  overflow-y: auto;\n}\n.modal-header[_ngcontent-%COMP%] {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  padding: 1.5rem;\n  border-bottom: 1px solid #e2e8f0;\n}\n.modal-header[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%] {\n  margin: 0;\n}\n.close-btn[_ngcontent-%COMP%] {\n  background: none;\n  border: none;\n  font-size: 1.5rem;\n  cursor: pointer;\n  color: #6b7280;\n}\n.modal-content[_ngcontent-%COMP%] {\n  padding: 1.5rem;\n}\n.modal-content[_ngcontent-%COMP%]   textarea[_ngcontent-%COMP%] {\n  width: 100%;\n  padding: 0.75rem;\n  border: 1px solid #d1d5db;\n  border-radius: 8px;\n  margin-top: 1rem;\n  resize: vertical;\n}\n.modal-actions[_ngcontent-%COMP%] {\n  display: flex;\n  justify-content: flex-end;\n  gap: 0.5rem;\n  padding: 1.5rem;\n  border-top: 1px solid #e2e8f0;\n}\n/*# sourceMappingURL=demandes-management.component.css.map */"] });
};
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && setClassMetadata(DemandesManagementComponent, [{
    type: Component,
    args: [{ selector: "app-demandes-management", standalone: true, imports: [CommonModule, FormsModule], template: `
    <div class="demandes-management">
      <div class="page-header">
        <h1>\u{1F514} Gestion des Demandes</h1>
        <p>G\xE9rez les demandes de cr\xE9ation de cat\xE9gories et sous-cat\xE9gories</p>
      </div>

      <div class="tabs">
        <button 
          class="tab-btn" 
          [class.active]="activeTab === 'categories'"
          (click)="activeTab = 'categories'; loadDemandesCategories()">
          Demandes de Cat\xE9gories
          <span *ngIf="demandesCategories.length > 0" class="badge">{{ demandesCategories.length }}</span>
        </button>
        <button 
          class="tab-btn" 
          [class.active]="activeTab === 'sous-categories'"
          (click)="activeTab = 'sous-categories'; loadDemandesSousCategories()">
          Demandes de Sous-cat\xE9gories
          <span *ngIf="demandesSousCategories.length > 0" class="badge">{{ demandesSousCategories.length }}</span>
        </button>
      </div>

      <!-- Demandes de cat\xE9gories -->
      <div *ngIf="activeTab === 'categories'" class="tab-content">
        <div class="filters">
          <select [(ngModel)]="filtreStatutCategories" (change)="loadDemandesCategories()">
            <option value="">Tous les statuts</option>
            <option value="0">En attente</option>
            <option value="1">Approuv\xE9es</option>
            <option value="2">Rejet\xE9es</option>
          </select>
        </div>

        <div *ngIf="demandesCategories.length === 0" class="empty-state">
          <p>Aucune demande de cat\xE9gorie trouv\xE9e</p>
        </div>

        <div class="demandes-grid">
          <div *ngFor="let demande of demandesCategories" class="demande-card">
            <div class="demande-header">
              <h3>{{ demande.nom }}</h3>
              <span class="statut-badge" [style.background-color]="getStatutColor(demande.statut)">
                {{ getStatutLabel(demande.statut) }}
              </span>
            </div>
            
            <div class="demande-content">
              <p><strong>Description:</strong> {{ demande.description }}</p>
              <p><strong>Demand\xE9 par:</strong> {{ demande.fournisseurNom }}</p>
              <p><strong>Date de demande:</strong> {{ formatDate(demande.dateDemande) }}</p>
              
              <div *ngIf="demande.imageUrl" class="image-preview">
                <img [src]="demande.imageUrl" alt="Image de la cat\xE9gorie" />
              </div>
            </div>

            <div *ngIf="demande.statut === 0" class="demande-actions">
              <button class="btn btn-success" (click)="traiterDemandeCategorie(demande.id, 1)">
                \u2705 Approuver
              </button>
              <button class="btn btn-danger" (click)="openRejectModal(demande, 'categorie')">
                \u274C Rejeter
              </button>
            </div>

            <div *ngIf="demande.statut !== 0 && demande.commentaireAdmin" class="admin-comment">
              <p><strong>Commentaire admin:</strong> {{ demande.commentaireAdmin }}</p>
              <p><strong>Trait\xE9 le:</strong> {{ formatDate(demande.dateTraitement!) }}</p>
            </div>
          </div>
        </div>
      </div>

      <!-- Demandes de sous-cat\xE9gories -->
      <div *ngIf="activeTab === 'sous-categories'" class="tab-content">
        <div class="filters">
          <select [(ngModel)]="filtreStatutSousCategories" (change)="loadDemandesSousCategories()">
            <option value="">Tous les statuts</option>
            <option value="0">En attente</option>
            <option value="1">Approuv\xE9es</option>
            <option value="2">Rejet\xE9es</option>
          </select>
        </div>

        <div *ngIf="demandesSousCategories.length === 0" class="empty-state">
          <p>Aucune demande de sous-cat\xE9gorie trouv\xE9e</p>
        </div>

        <div class="demandes-grid">
          <div *ngFor="let demande of demandesSousCategories" class="demande-card">
            <div class="demande-header">
              <h3>{{ demande.nom }}</h3>
              <span class="statut-badge" [style.background-color]="getStatutColor(demande.statut)">
                {{ getStatutLabel(demande.statut) }}
              </span>
            </div>
            
            <div class="demande-content">
              <p><strong>Description:</strong> {{ demande.description }}</p>
              <p><strong>Cat\xE9gorie parent:</strong> {{ demande.categorieNom }}</p>
              <p><strong>Demand\xE9 par:</strong> {{ demande.fournisseurNom }}</p>
              <p><strong>Date de demande:</strong> {{ formatDate(demande.dateDemande) }}</p>
              
              <div *ngIf="demande.imageUrl" class="image-preview">
                <img [src]="demande.imageUrl" alt="Image de la sous-cat\xE9gorie" />
              </div>
            </div>

            <div *ngIf="demande.statut === 0" class="demande-actions">
              <button class="btn btn-success" (click)="traiterDemandeSousCategorie(demande.id, 1)">
                \u2705 Approuver
              </button>
              <button class="btn btn-danger" (click)="openRejectModal(demande, 'sous-categorie')">
                \u274C Rejeter
              </button>
            </div>

            <div *ngIf="demande.statut !== 0 && demande.commentaireAdmin" class="admin-comment">
              <p><strong>Commentaire admin:</strong> {{ demande.commentaireAdmin }}</p>
              <p><strong>Trait\xE9 le:</strong> {{ formatDate(demande.dateTraitement!) }}</p>
            </div>
          </div>
        </div>
      </div>

      <!-- Modal de rejet -->
      <div *ngIf="showRejectModal" class="modal-overlay" (click)="closeRejectModal()">
        <div class="modal" (click)="$event.stopPropagation()">
          <div class="modal-header">
            <h3>Rejeter la demande</h3>
            <button class="close-btn" (click)="closeRejectModal()">\xD7</button>
          </div>
          <div class="modal-content">
            <p>\xCAtes-vous s\xFBr de vouloir rejeter cette demande ?</p>
            <textarea 
              [(ngModel)]="commentaireRejet" 
              placeholder="Commentaire (optionnel)"
              rows="3">
            </textarea>
          </div>
          <div class="modal-actions">
            <button class="btn btn-secondary" (click)="closeRejectModal()">Annuler</button>
            <button class="btn btn-danger" (click)="confirmReject()">Rejeter</button>
          </div>
        </div>
      </div>
    </div>
  `, styles: ["/* angular:styles/component:css;67ff0ed633472062b1408654866082fd251674b668e604c3aa692abb6f2b868f;C:/Users/<USER>/Desktop/STE/PFE/PFE/frontend-fournisseur/src/app/components/admin/demandes-management/demandes-management.component.ts */\n.demandes-management {\n  padding: 2rem;\n}\n.page-header {\n  margin-bottom: 2rem;\n}\n.page-header h1 {\n  margin: 0 0 0.5rem 0;\n  color: #1e293b;\n}\n.page-header p {\n  margin: 0;\n  color: #64748b;\n}\n.tabs {\n  display: flex;\n  border-bottom: 2px solid #e2e8f0;\n  margin-bottom: 2rem;\n}\n.tab-btn {\n  padding: 1rem 2rem;\n  background: none;\n  border: none;\n  cursor: pointer;\n  font-size: 1rem;\n  color: #64748b;\n  border-bottom: 2px solid transparent;\n  transition: all 0.3s ease;\n  position: relative;\n}\n.tab-btn.active {\n  color: #3b82f6;\n  border-bottom-color: #3b82f6;\n}\n.badge {\n  background: #ef4444;\n  color: white;\n  border-radius: 50%;\n  padding: 0.25rem 0.5rem;\n  font-size: 0.75rem;\n  margin-left: 0.5rem;\n}\n.filters {\n  margin-bottom: 1.5rem;\n}\n.filters select {\n  padding: 0.5rem 1rem;\n  border: 1px solid #d1d5db;\n  border-radius: 8px;\n  background: white;\n}\n.empty-state {\n  text-align: center;\n  padding: 3rem;\n  color: #64748b;\n}\n.demandes-grid {\n  display: grid;\n  grid-template-columns: repeat(auto-fill, minmax(400px, 1fr));\n  gap: 1.5rem;\n}\n.demande-card {\n  background: white;\n  border-radius: 12px;\n  padding: 1.5rem;\n  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);\n  border: 1px solid #e2e8f0;\n}\n.demande-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 1rem;\n}\n.demande-header h3 {\n  margin: 0;\n  color: #1e293b;\n}\n.statut-badge {\n  padding: 0.25rem 0.75rem;\n  border-radius: 20px;\n  color: white;\n  font-size: 0.875rem;\n  font-weight: 500;\n}\n.demande-content p {\n  margin: 0.5rem 0;\n  color: #475569;\n}\n.image-preview {\n  margin: 1rem 0;\n}\n.image-preview img {\n  max-width: 100px;\n  max-height: 100px;\n  border-radius: 8px;\n  object-fit: cover;\n}\n.demande-actions {\n  display: flex;\n  gap: 0.5rem;\n  margin-top: 1rem;\n  padding-top: 1rem;\n  border-top: 1px solid #e2e8f0;\n}\n.admin-comment {\n  margin-top: 1rem;\n  padding-top: 1rem;\n  border-top: 1px solid #e2e8f0;\n  background: #f8fafc;\n  padding: 1rem;\n  border-radius: 8px;\n}\n.btn {\n  padding: 0.5rem 1rem;\n  border: none;\n  border-radius: 6px;\n  cursor: pointer;\n  font-size: 0.875rem;\n  transition: all 0.3s ease;\n}\n.btn-success {\n  background: #10b981;\n  color: white;\n}\n.btn-success:hover {\n  background: #059669;\n}\n.btn-danger {\n  background: #ef4444;\n  color: white;\n}\n.btn-danger:hover {\n  background: #dc2626;\n}\n.btn-secondary {\n  background: #6b7280;\n  color: white;\n}\n.btn-secondary:hover {\n  background: #4b5563;\n}\n.modal-overlay {\n  position: fixed;\n  top: 0;\n  left: 0;\n  right: 0;\n  bottom: 0;\n  background: rgba(0, 0, 0, 0.5);\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  z-index: 1000;\n}\n.modal {\n  background: white;\n  border-radius: 12px;\n  width: 90%;\n  max-width: 500px;\n  max-height: 90vh;\n  overflow-y: auto;\n}\n.modal-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  padding: 1.5rem;\n  border-bottom: 1px solid #e2e8f0;\n}\n.modal-header h3 {\n  margin: 0;\n}\n.close-btn {\n  background: none;\n  border: none;\n  font-size: 1.5rem;\n  cursor: pointer;\n  color: #6b7280;\n}\n.modal-content {\n  padding: 1.5rem;\n}\n.modal-content textarea {\n  width: 100%;\n  padding: 0.75rem;\n  border: 1px solid #d1d5db;\n  border-radius: 8px;\n  margin-top: 1rem;\n  resize: vertical;\n}\n.modal-actions {\n  display: flex;\n  justify-content: flex-end;\n  gap: 0.5rem;\n  padding: 1.5rem;\n  border-top: 1px solid #e2e8f0;\n}\n/*# sourceMappingURL=demandes-management.component.css.map */\n"] }]
  }], () => [{ type: DemandeService }], null);
})();
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && \u0275setClassDebugInfo(DemandesManagementComponent, { className: "DemandesManagementComponent", filePath: "src/app/components/admin/demandes-management/demandes-management.component.ts", lineNumber: 395 });
})();
export {
  DemandesManagementComponent
};
//# sourceMappingURL=chunk-M7E4P5MN.js.map

import {
  AdminAuthService
} from "./chunk-2RV3R4JN.js";
import {
  Router,
  RouterLink,
  RouterModule
} from "./chunk-6BVUYNW4.js";
import {
  AdminService
} from "./chunk-EFJVWLOV.js";
import {
  environment
} from "./chunk-GFHHPDQ6.js";
import {
  HttpClient
} from "./chunk-7JDDWGD3.js";
import {
  CommonModule,
  Component,
  DecimalPipe,
  Injectable,
  NgForOf,
  NgIf,
  Observable,
  catchError,
  computed,
  of,
  setClassMetadata,
  signal,
  tap,
  ɵsetClassDebugInfo,
  ɵɵadvance,
  ɵɵattribute,
  ɵɵclassMap,
  ɵɵdefineComponent,
  ɵɵdefineInjectable,
  ɵɵdirectiveInject,
  ɵɵelement,
  ɵɵelementEnd,
  ɵɵelementStart,
  ɵɵgetCurrentView,
  ɵɵinject,
  ɵɵlistener,
  ɵɵnamespaceHTML,
  ɵɵnamespaceSVG,
  ɵɵnextContext,
  ɵɵpipe,
  ɵɵpipeBind1,
  ɵɵproperty,
  ɵɵresetView,
  ɵɵrestoreView,
  ɵɵsanitizeHtml,
  ɵɵtemplate,
  ɵɵtext,
  ɵɵtextInterpolate,
  ɵɵtextInterpolate1
} from "./chunk-UBZQS7JS.js";

// src/app/services/admin-test.service.ts
var AdminTestService = class _AdminTestService {
  http;
  API_URL = `${environment.apiUrl || "https://localhost:7264/api"}/Admin`;
  constructor(http) {
    this.http = http;
  }
  /**
   * Test de connectivité avec le backend admin
   */
  testConnectivity() {
    console.log("\u{1F517} Test de connectivit\xE9 admin...");
    return this.http.get(`${this.API_URL}/statistiques`).pipe(tap((response) => {
      console.log("\u2705 Connectivit\xE9 admin OK:", response);
    }), catchError((error) => {
      console.error("\u274C Erreur de connectivit\xE9 admin:", error);
      return of({
        error: true,
        status: error.status,
        message: error.message,
        url: error.url
      });
    }));
  }
  /**
   * Test des endpoints utilisateurs
   */
  testUsersEndpoint() {
    console.log("\u{1F465} Test endpoint utilisateurs...");
    return this.http.get(`${this.API_URL}/utilisateurs`).pipe(tap((response) => {
      console.log("\u2705 Endpoint utilisateurs OK:", response);
    }), catchError((error) => {
      console.error("\u274C Erreur endpoint utilisateurs:", error);
      return of({
        error: true,
        status: error.status,
        message: error.message,
        url: error.url
      });
    }));
  }
  /**
   * Test de création d'une catégorie (pour vérifier les permissions)
   */
  testCreateCategory() {
    console.log("\u{1F4C1} Test cr\xE9ation cat\xE9gorie...");
    const testCategory = {
      nom: "Test Category",
      description: "Cat\xE9gorie de test pour v\xE9rifier les permissions"
    };
    return this.http.post(`${this.API_URL}/categories`, testCategory).pipe(tap((response) => {
      console.log("\u2705 Cr\xE9ation cat\xE9gorie OK:", response);
    }), catchError((error) => {
      console.error("\u274C Erreur cr\xE9ation cat\xE9gorie:", error);
      return of({
        error: true,
        status: error.status,
        message: error.message,
        url: error.url
      });
    }));
  }
  /**
   * Test complet de tous les endpoints
   */
  runFullTest() {
    console.log("\u{1F9EA} D\xE9marrage du test complet des endpoints admin...");
    const tests = [
      { name: "Statistiques", test: () => this.testConnectivity() },
      { name: "Utilisateurs", test: () => this.testUsersEndpoint() },
      { name: "Cat\xE9gories", test: () => this.testCreateCategory() }
    ];
    const results = [];
    return new Observable((observer) => {
      let completed = 0;
      tests.forEach((testCase, index) => {
        testCase.test().subscribe({
          next: (result) => {
            results[index] = {
              name: testCase.name,
              success: !result.error,
              result
            };
            completed++;
            if (completed === tests.length) {
              observer.next({
                summary: {
                  total: tests.length,
                  passed: results.filter((r) => r.success).length,
                  failed: results.filter((r) => !r.success).length
                },
                details: results
              });
              observer.complete();
            }
          },
          error: (error) => {
            results[index] = {
              name: testCase.name,
              success: false,
              error
            };
            completed++;
            if (completed === tests.length) {
              observer.next({
                summary: {
                  total: tests.length,
                  passed: results.filter((r) => r.success).length,
                  failed: results.filter((r) => !r.success).length
                },
                details: results
              });
              observer.complete();
            }
          }
        });
      });
    });
  }
  static \u0275fac = function AdminTestService_Factory(__ngFactoryType__) {
    return new (__ngFactoryType__ || _AdminTestService)(\u0275\u0275inject(HttpClient));
  };
  static \u0275prov = /* @__PURE__ */ \u0275\u0275defineInjectable({ token: _AdminTestService, factory: _AdminTestService.\u0275fac, providedIn: "root" });
};
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && setClassMetadata(AdminTestService, [{
    type: Injectable,
    args: [{
      providedIn: "root"
    }]
  }], () => [{ type: HttpClient }], null);
})();

// src/app/components/admin/admin-dashboard/admin-dashboard.component.ts
function AdminDashboardComponent_div_36_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "div", 30);
    \u0275\u0275element(1, "div", 31);
    \u0275\u0275elementStart(2, "p");
    \u0275\u0275text(3, "Chargement des statistiques...");
    \u0275\u0275elementEnd()();
  }
}
function AdminDashboardComponent_div_37_Template(rf, ctx) {
  if (rf & 1) {
    const _r1 = \u0275\u0275getCurrentView();
    \u0275\u0275elementStart(0, "div", 32)(1, "div", 33);
    \u0275\u0275text(2, "\u26A0\uFE0F");
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(3, "h3");
    \u0275\u0275text(4, "Erreur de chargement");
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(5, "p");
    \u0275\u0275text(6);
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(7, "button", 34);
    \u0275\u0275listener("click", function AdminDashboardComponent_div_37_Template_button_click_7_listener() {
      \u0275\u0275restoreView(_r1);
      const ctx_r1 = \u0275\u0275nextContext();
      return \u0275\u0275resetView(ctx_r1.loadStatistiques());
    });
    \u0275\u0275text(8, "R\xE9essayer");
    \u0275\u0275elementEnd()();
  }
  if (rf & 2) {
    const ctx_r1 = \u0275\u0275nextContext();
    \u0275\u0275advance(6);
    \u0275\u0275textInterpolate(ctx_r1.error());
  }
}
function AdminDashboardComponent_div_38_div_2_div_4_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "div", 57)(1, "span");
    \u0275\u0275text(2);
    \u0275\u0275elementEnd()();
  }
  if (rf & 2) {
    const card_r3 = \u0275\u0275nextContext().$implicit;
    const ctx_r1 = \u0275\u0275nextContext(2);
    \u0275\u0275advance();
    \u0275\u0275classMap(ctx_r1.getTrendClass(card_r3.trend));
    \u0275\u0275advance();
    \u0275\u0275textInterpolate1(" ", ctx_r1.formatTrend(card_r3.trend), " ");
  }
}
function AdminDashboardComponent_div_38_div_2_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "div", 48)(1, "div", 49)(2, "div", 50);
    \u0275\u0275element(3, "span", 51);
    \u0275\u0275elementEnd();
    \u0275\u0275template(4, AdminDashboardComponent_div_38_div_2_div_4_Template, 3, 3, "div", 52);
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(5, "div", 53)(6, "div", 54);
    \u0275\u0275text(7);
    \u0275\u0275pipe(8, "number");
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(9, "div", 55);
    \u0275\u0275text(10);
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(11, "div", 56);
    \u0275\u0275text(12);
    \u0275\u0275elementEnd()()();
  }
  if (rf & 2) {
    const card_r3 = ctx.$implicit;
    const ctx_r1 = \u0275\u0275nextContext(2);
    \u0275\u0275attribute("data-color", card_r3.color);
    \u0275\u0275advance(3);
    \u0275\u0275property("innerHTML", ctx_r1.getCardIcon(card_r3.icon), \u0275\u0275sanitizeHtml);
    \u0275\u0275advance();
    \u0275\u0275property("ngIf", card_r3.trend);
    \u0275\u0275advance(3);
    \u0275\u0275textInterpolate(\u0275\u0275pipeBind1(8, 6, card_r3.value));
    \u0275\u0275advance(3);
    \u0275\u0275textInterpolate(card_r3.title);
    \u0275\u0275advance(2);
    \u0275\u0275textInterpolate(card_r3.description);
  }
}
function AdminDashboardComponent_div_38_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "div", 35)(1, "div", 36);
    \u0275\u0275template(2, AdminDashboardComponent_div_38_div_2_Template, 13, 8, "div", 37);
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(3, "div", 38)(4, "h2", 39);
    \u0275\u0275text(5, "Actions rapides");
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(6, "div", 40)(7, "button", 41)(8, "div", 42);
    \u0275\u0275text(9, "\u{1F465}");
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(10, "div", 43)(11, "h3");
    \u0275\u0275text(12, "Gestion des utilisateurs");
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(13, "p");
    \u0275\u0275text(14, "G\xE9rer les comptes et permissions");
    \u0275\u0275elementEnd()();
    \u0275\u0275elementStart(15, "div", 44);
    \u0275\u0275text(16, "\u2192");
    \u0275\u0275elementEnd()();
    \u0275\u0275elementStart(17, "button", 45)(18, "div", 42);
    \u0275\u0275text(19, "\u{1F4E6}");
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(20, "div", 43)(21, "h3");
    \u0275\u0275text(22, "Gestion des produits");
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(23, "p");
    \u0275\u0275text(24, "Valider et mod\xE9rer les produits");
    \u0275\u0275elementEnd()();
    \u0275\u0275elementStart(25, "div", 44);
    \u0275\u0275text(26, "\u2192");
    \u0275\u0275elementEnd()();
    \u0275\u0275elementStart(27, "button", 46)(28, "div", 42);
    \u0275\u0275text(29, "\u{1F4CB}");
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(30, "div", 43)(31, "h3");
    \u0275\u0275text(32, "Demandes en attente");
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(33, "p");
    \u0275\u0275text(34, "Traiter les demandes de cat\xE9gories");
    \u0275\u0275elementEnd()();
    \u0275\u0275elementStart(35, "div", 44);
    \u0275\u0275text(36, "\u2192");
    \u0275\u0275elementEnd()();
    \u0275\u0275elementStart(37, "button", 47)(38, "div", 42);
    \u0275\u0275text(39, "\u2B50");
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(40, "div", 43)(41, "h3");
    \u0275\u0275text(42, "Mod\xE9ration des avis");
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(43, "p");
    \u0275\u0275text(44, "G\xE9rer les avis clients");
    \u0275\u0275elementEnd()();
    \u0275\u0275elementStart(45, "div", 44);
    \u0275\u0275text(46, "\u2192");
    \u0275\u0275elementEnd()()()()();
  }
  if (rf & 2) {
    const ctx_r1 = \u0275\u0275nextContext();
    \u0275\u0275advance(2);
    \u0275\u0275property("ngForOf", ctx_r1.dashboardCards());
  }
}
var AdminDashboardComponent = class _AdminDashboardComponent {
  adminService;
  adminTestService;
  adminAuthService;
  router;
  // Angular 19: Signals
  activeSection = signal("dashboard");
  statistiques = signal(null);
  isLoading = signal(false);
  error = signal("");
  // Computed signals
  dashboardCards = computed(() => {
    const stats = this.statistiques();
    if (!stats)
      return [];
    return [
      {
        title: "Utilisateurs",
        value: stats.nombreUtilisateurs,
        icon: "icon-users",
        color: "blue",
        description: `${stats.nombreFournisseurs} fournisseurs, ${stats.nombreClients} clients`,
        trend: this.calculateTrend(stats.nombreUtilisateurs, 0)
      },
      {
        title: "Produits",
        value: stats.nombreProduits || 0,
        icon: "icon-package",
        color: "green",
        description: "Produits disponibles",
        trend: this.calculateTrend(stats.nombreProduits || 0, 0)
      },
      {
        title: "Commandes",
        value: stats.nombreCommandes,
        icon: "icon-shopping-cart",
        color: "orange",
        description: `${stats.nombreCommandesAnnulees || 0} annul\xE9es`,
        trend: this.calculateTrend(stats.nombreCommandes, 0)
      },
      {
        title: "Ventes",
        value: stats.nombreVentes || 0,
        icon: "icon-trending-up",
        color: "purple",
        description: "Total des ventes",
        trend: this.calculateTrend(stats.nombreVentes || 0, 0)
      }
    ];
  });
  menuItems = [
    {
      id: "dashboard",
      label: "Tableau de bord",
      icon: "icon-home",
      description: "Vue d'ensemble et statistiques"
    },
    {
      id: "users",
      label: "Utilisateurs",
      icon: "icon-users",
      description: "Gestion des comptes utilisateurs"
    },
    {
      id: "categories",
      label: "Cat\xE9gories",
      icon: "icon-folder",
      description: "Gestion des cat\xE9gories et sous-cat\xE9gories"
    },
    {
      id: "orders",
      label: "Commandes",
      icon: "icon-shopping-cart",
      description: "Gestion des commandes"
    },
    {
      id: "products",
      label: "Produits",
      icon: "icon-package",
      description: "Validation et mise en avant des produits"
    }
  ];
  constructor(adminService, adminTestService, adminAuthService, router) {
    this.adminService = adminService;
    this.adminTestService = adminTestService;
    this.adminAuthService = adminAuthService;
    this.router = router;
  }
  ngOnInit() {
    this.loadStatistiques();
  }
  loadStatistiques() {
    this.isLoading.set(true);
    this.error.set("");
    this.adminService.getStatistiques().subscribe({
      next: (stats) => {
        console.log("\u{1F4CA} Statistiques re\xE7ues:", stats);
        this.statistiques.set(stats);
        this.isLoading.set(false);
      },
      error: (error) => {
        console.error("\u274C Erreur lors du chargement des statistiques:", error);
        this.error.set("Erreur lors du chargement des statistiques");
        this.isLoading.set(false);
      }
    });
  }
  setActiveSection(section) {
    this.activeSection.set(section);
  }
  formatPrice(price) {
    return new Intl.NumberFormat("fr-TN", {
      style: "currency",
      currency: "TND",
      minimumFractionDigits: 0,
      maximumFractionDigits: 0
    }).format(price);
  }
  formatNumber(num) {
    return new Intl.NumberFormat("fr-FR").format(num);
  }
  refresh() {
    this.loadStatistiques();
  }
  getCardClass(color) {
    return `stat-card stat-card-${color}`;
  }
  getMenuItemClass(itemId) {
    return this.activeSection() === itemId ? "menu-item active" : "menu-item";
  }
  testBackendConnection() {
    console.log("\u{1F9EA} D\xE9marrage du test de connectivit\xE9 backend...");
    this.adminTestService.runFullTest().subscribe({
      next: (results) => {
        console.log("\u{1F4CA} R\xE9sultats des tests:", results);
        const { summary, details } = results;
        const message = `Tests termin\xE9s: ${summary.passed}/${summary.total} r\xE9ussis

` + details.map((test) => `${test.success ? "\u2705" : "\u274C"} ${test.name}: ${test.success ? "OK" : "ERREUR"}`).join("\n");
        alert(message);
      },
      error: (error) => {
        console.error("\u274C Erreur lors des tests:", error);
        alert("Erreur lors des tests de connectivit\xE9");
      }
    });
  }
  getCardIcon(iconClass) {
    const iconMap = {
      "icon-users": "\u{1F465}",
      "icon-package": "\u{1F4E6}",
      "icon-shopping-cart": "\u{1F6D2}",
      "icon-trending-up": "\u{1F4C8}"
    };
    return iconMap[iconClass] || "\u{1F4CA}";
  }
  getCurrentTime() {
    return (/* @__PURE__ */ new Date()).toLocaleTimeString("fr-FR", {
      hour: "2-digit",
      minute: "2-digit"
    });
  }
  logout() {
    this.adminAuthService.logout().subscribe({
      next: () => {
        this.router.navigate(["/adminOptiLet"]);
      },
      error: (error) => {
        console.error("Erreur lors de la d\xE9connexion:", error);
        this.router.navigate(["/adminOptiLet"]);
      }
    });
  }
  calculateTrend(current, previous) {
    if (previous === 0)
      return current > 0 ? 100 : 0;
    return Math.round((current - previous) / previous * 100);
  }
  getTrendClass(trend) {
    const baseClass = "trend-indicator";
    if (trend > 0)
      return `${baseClass} positive`;
    if (trend < 0)
      return `${baseClass} negative`;
    return `${baseClass} neutral`;
  }
  formatTrend(trend) {
    if (trend === 0)
      return "0%";
    const sign = trend > 0 ? "+" : "";
    return `${sign}${trend}%`;
  }
  static \u0275fac = function AdminDashboardComponent_Factory(__ngFactoryType__) {
    return new (__ngFactoryType__ || _AdminDashboardComponent)(\u0275\u0275directiveInject(AdminService), \u0275\u0275directiveInject(AdminTestService), \u0275\u0275directiveInject(AdminAuthService), \u0275\u0275directiveInject(Router));
  };
  static \u0275cmp = /* @__PURE__ */ \u0275\u0275defineComponent({ type: _AdminDashboardComponent, selectors: [["app-admin-dashboard"]], decls: 39, vars: 5, consts: [[1, "admin-dashboard"], [1, "dashboard-header"], [1, "header-content"], [1, "welcome-section"], [1, "welcome-icon"], [1, "icon-container"], ["width", "28", "height", "28", "viewBox", "0 0 24 24", "fill", "none", "stroke", "currentColor", "stroke-width", "2"], ["d", "M3 9l9-7 9 7v11a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2z"], ["points", "9,22 9,12 15,12 15,22"], [1, "welcome-text"], [1, "dashboard-title"], [1, "dashboard-subtitle"], [1, "breadcrumb"], [1, "breadcrumb-item"], [1, "breadcrumb-separator"], [1, "breadcrumb-item", "active"], [1, "header-actions"], [1, "time-info"], [1, "current-time"], [1, "refresh-btn", 3, "click", "disabled"], ["width", "18", "height", "18", "viewBox", "0 0 24 24", "fill", "none", "stroke", "currentColor", "stroke-width", "2"], ["points", "23 4 23 10 17 10"], ["d", "M20.49 9A9 9 0 0 0 5.64 5.64L1 10m22 4l-4.64 4.36A9 9 0 0 1 3.51 15"], [1, "logout-btn", 3, "click"], ["d", "M9 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h4"], ["points", "16,17 21,12 16,7"], ["x1", "21", "y1", "12", "x2", "9", "y2", "12"], ["class", "loading-state", 4, "ngIf"], ["class", "error-state", 4, "ngIf"], ["class", "dashboard-content", 4, "ngIf"], [1, "loading-state"], [1, "loading-spinner"], [1, "error-state"], [1, "error-icon"], [1, "retry-btn", 3, "click"], [1, "dashboard-content"], [1, "stats-grid"], ["class", "stat-card", 4, "ngFor", "ngForOf"], [1, "quick-actions"], [1, "section-title"], [1, "actions-grid"], ["routerLink", "/admin/dashboard/users", 1, "action-card"], [1, "action-icon"], [1, "action-content"], [1, "action-arrow"], ["routerLink", "/admin/dashboard/products", 1, "action-card"], ["routerLink", "/admin/dashboard/demandes", 1, "action-card"], ["routerLink", "/admin/dashboard/avis-moderation", 1, "action-card"], [1, "stat-card"], [1, "card-header"], [1, "card-icon"], [3, "innerHTML"], ["class", "card-trend", 4, "ngIf"], [1, "card-body"], [1, "card-value"], [1, "card-title"], [1, "card-description"], [1, "card-trend"]], template: function AdminDashboardComponent_Template(rf, ctx) {
    if (rf & 1) {
      \u0275\u0275elementStart(0, "div", 0)(1, "div", 1)(2, "div", 2)(3, "div", 3)(4, "div", 4)(5, "div", 5);
      \u0275\u0275namespaceSVG();
      \u0275\u0275elementStart(6, "svg", 6);
      \u0275\u0275element(7, "path", 7)(8, "polyline", 8);
      \u0275\u0275elementEnd()()();
      \u0275\u0275namespaceHTML();
      \u0275\u0275elementStart(9, "div", 9)(10, "h1", 10);
      \u0275\u0275text(11, "Tableau de bord administrateur");
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(12, "p", 11);
      \u0275\u0275text(13, "Bienvenue dans l'espace d'administration OptiLet");
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(14, "div", 12)(15, "span", 13);
      \u0275\u0275text(16, "Administration");
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(17, "span", 14);
      \u0275\u0275text(18, "\u203A");
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(19, "span", 15);
      \u0275\u0275text(20, "Dashboard");
      \u0275\u0275elementEnd()()()();
      \u0275\u0275elementStart(21, "div", 16)(22, "div", 17)(23, "span", 18);
      \u0275\u0275text(24);
      \u0275\u0275elementEnd()();
      \u0275\u0275elementStart(25, "button", 19);
      \u0275\u0275listener("click", function AdminDashboardComponent_Template_button_click_25_listener() {
        return ctx.loadStatistiques();
      });
      \u0275\u0275namespaceSVG();
      \u0275\u0275elementStart(26, "svg", 20);
      \u0275\u0275element(27, "polyline", 21)(28, "path", 22);
      \u0275\u0275elementEnd();
      \u0275\u0275text(29, " Actualiser ");
      \u0275\u0275elementEnd();
      \u0275\u0275namespaceHTML();
      \u0275\u0275elementStart(30, "button", 23);
      \u0275\u0275listener("click", function AdminDashboardComponent_Template_button_click_30_listener() {
        return ctx.logout();
      });
      \u0275\u0275namespaceSVG();
      \u0275\u0275elementStart(31, "svg", 20);
      \u0275\u0275element(32, "path", 24)(33, "polyline", 25)(34, "line", 26);
      \u0275\u0275elementEnd();
      \u0275\u0275text(35, " D\xE9connexion ");
      \u0275\u0275elementEnd()()()();
      \u0275\u0275template(36, AdminDashboardComponent_div_36_Template, 4, 0, "div", 27)(37, AdminDashboardComponent_div_37_Template, 9, 1, "div", 28)(38, AdminDashboardComponent_div_38_Template, 47, 1, "div", 29);
      \u0275\u0275elementEnd();
    }
    if (rf & 2) {
      \u0275\u0275advance(24);
      \u0275\u0275textInterpolate(ctx.getCurrentTime());
      \u0275\u0275advance();
      \u0275\u0275property("disabled", ctx.isLoading());
      \u0275\u0275advance(11);
      \u0275\u0275property("ngIf", ctx.isLoading());
      \u0275\u0275advance();
      \u0275\u0275property("ngIf", ctx.error() && !ctx.isLoading());
      \u0275\u0275advance();
      \u0275\u0275property("ngIf", !ctx.isLoading() && !ctx.error());
    }
  }, dependencies: [CommonModule, NgForOf, NgIf, DecimalPipe, RouterModule, RouterLink], styles: ['\n\n.admin-dashboard[_ngcontent-%COMP%] {\n  padding: 1.5rem;\n  background:\n    linear-gradient(\n      135deg,\n      #f8fafc 0%,\n      #e2e8f0 100%);\n  min-height: 100vh;\n}\n.dashboard-header[_ngcontent-%COMP%] {\n  margin-bottom: 2rem;\n  padding: 2rem;\n  background:\n    linear-gradient(\n      135deg,\n      #ffffff 0%,\n      #f8fafc 100%);\n  border-radius: 20px;\n  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);\n  border: 1px solid rgba(255, 255, 255, 0.2);\n  -webkit-backdrop-filter: blur(10px);\n  backdrop-filter: blur(10px);\n}\n.header-content[_ngcontent-%COMP%] {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n}\n.welcome-section[_ngcontent-%COMP%] {\n  display: flex;\n  align-items: center;\n  gap: 1.5rem;\n}\n.welcome-icon[_ngcontent-%COMP%] {\n  position: relative;\n}\n.icon-container[_ngcontent-%COMP%] {\n  width: 70px;\n  height: 70px;\n  background:\n    linear-gradient(\n      135deg,\n      #667eea 0%,\n      #764ba2 100%);\n  border-radius: 20px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  color: white;\n  box-shadow: 0 8px 32px rgba(102, 126, 234, 0.3);\n  position: relative;\n}\n.icon-container[_ngcontent-%COMP%]::before {\n  content: "";\n  position: absolute;\n  inset: -2px;\n  background:\n    linear-gradient(\n      135deg,\n      #667eea,\n      #764ba2);\n  border-radius: 22px;\n  z-index: -1;\n  opacity: 0.3;\n  filter: blur(8px);\n}\n.welcome-text[_ngcontent-%COMP%] {\n  flex: 1;\n}\n.dashboard-title[_ngcontent-%COMP%] {\n  font-size: 2.2rem;\n  font-weight: 800;\n  color: #1e293b;\n  margin: 0 0 0.5rem 0;\n  background:\n    linear-gradient(\n      135deg,\n      #1e293b 0%,\n      #475569 100%);\n  -webkit-background-clip: text;\n  -webkit-text-fill-color: transparent;\n  background-clip: text;\n}\n.dashboard-subtitle[_ngcontent-%COMP%] {\n  color: #64748b;\n  font-size: 1.1rem;\n  margin: 0 0 0.75rem 0;\n  font-weight: 500;\n}\n.breadcrumb[_ngcontent-%COMP%] {\n  display: flex;\n  align-items: center;\n  gap: 0.5rem;\n  font-size: 0.875rem;\n}\n.breadcrumb-item[_ngcontent-%COMP%] {\n  color: #94a3b8;\n  font-weight: 500;\n}\n.breadcrumb-item.active[_ngcontent-%COMP%] {\n  color: #667eea;\n  font-weight: 600;\n}\n.breadcrumb-separator[_ngcontent-%COMP%] {\n  color: #cbd5e1;\n  font-weight: 300;\n}\n.header-actions[_ngcontent-%COMP%] {\n  display: flex;\n  align-items: center;\n  gap: 1rem;\n  flex-direction: row;\n  text-align: right;\n}\n.time-info[_ngcontent-%COMP%] {\n  font-size: 0.875rem;\n  color: #64748b;\n  font-weight: 500;\n}\n.current-time[_ngcontent-%COMP%] {\n  background:\n    linear-gradient(\n      135deg,\n      #f1f5f9 0%,\n      #e2e8f0 100%);\n  padding: 0.5rem 1rem;\n  border-radius: 12px;\n  border: 1px solid #e2e8f0;\n}\n.refresh-btn[_ngcontent-%COMP%] {\n  background:\n    linear-gradient(\n      135deg,\n      #667eea 0%,\n      #764ba2 100%);\n  color: white;\n  border: none;\n  padding: 1rem 1.5rem;\n  border-radius: 16px;\n  cursor: pointer;\n  transition: all 0.3s ease;\n  display: flex;\n  align-items: center;\n  gap: 0.75rem;\n  font-weight: 600;\n  font-size: 0.95rem;\n  box-shadow: 0 8px 32px rgba(102, 126, 234, 0.3);\n  border: 1px solid rgba(255, 255, 255, 0.2);\n  -webkit-backdrop-filter: blur(10px);\n  backdrop-filter: blur(10px);\n}\n.refresh-btn[_ngcontent-%COMP%]:hover:not(:disabled) {\n  transform: translateY(-3px);\n  box-shadow: 0 12px 40px rgba(102, 126, 234, 0.4);\n  background:\n    linear-gradient(\n      135deg,\n      #5a67d8 0%,\n      #6b46c1 100%);\n}\n.refresh-btn[_ngcontent-%COMP%]:disabled {\n  opacity: 0.6;\n  cursor: not-allowed;\n  transform: none;\n}\n.refresh-btn[_ngcontent-%COMP%]   svg[_ngcontent-%COMP%] {\n  transition: transform 0.3s ease;\n}\n.refresh-btn[_ngcontent-%COMP%]:hover:not(:disabled)   svg[_ngcontent-%COMP%] {\n  transform: rotate(180deg);\n}\n.logout-btn[_ngcontent-%COMP%] {\n  background:\n    linear-gradient(\n      135deg,\n      #ef4444 0%,\n      #dc2626 100%);\n  color: white;\n  border: none;\n  padding: 1rem 1.5rem;\n  border-radius: 16px;\n  cursor: pointer;\n  transition: all 0.3s ease;\n  display: flex;\n  align-items: center;\n  gap: 0.75rem;\n  font-weight: 600;\n  font-size: 0.95rem;\n  box-shadow: 0 8px 32px rgba(239, 68, 68, 0.3);\n  border: 1px solid rgba(255, 255, 255, 0.2);\n  -webkit-backdrop-filter: blur(10px);\n  backdrop-filter: blur(10px);\n}\n.logout-btn[_ngcontent-%COMP%]:hover {\n  transform: translateY(-3px);\n  box-shadow: 0 12px 40px rgba(239, 68, 68, 0.4);\n  background:\n    linear-gradient(\n      135deg,\n      #dc2626 0%,\n      #b91c1c 100%);\n}\n.logout-btn[_ngcontent-%COMP%]   svg[_ngcontent-%COMP%] {\n  transition: transform 0.3s ease;\n}\n.logout-btn[_ngcontent-%COMP%]:hover   svg[_ngcontent-%COMP%] {\n  transform: translateX(2px);\n}\n.loading-state[_ngcontent-%COMP%] {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  justify-content: center;\n  padding: 4rem;\n  text-align: center;\n}\n.loading-spinner[_ngcontent-%COMP%] {\n  width: 40px;\n  height: 40px;\n  border: 4px solid #e2e8f0;\n  border-top: 4px solid #3b82f6;\n  border-radius: 50%;\n  animation: _ngcontent-%COMP%_spin 1s linear infinite;\n  margin-bottom: 1rem;\n}\n@keyframes _ngcontent-%COMP%_spin {\n  0% {\n    transform: rotate(0deg);\n  }\n  100% {\n    transform: rotate(360deg);\n  }\n}\n.error-state[_ngcontent-%COMP%] {\n  background: #fef2f2;\n  border: 1px solid #fecaca;\n  border-radius: 12px;\n  padding: 2rem;\n  text-align: center;\n  margin: 2rem 0;\n}\n.error-icon[_ngcontent-%COMP%] {\n  font-size: 3rem;\n  margin-bottom: 1rem;\n}\n.error-state[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%] {\n  color: #dc2626;\n  margin: 0 0 0.5rem 0;\n}\n.error-state[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\n  color: #7f1d1d;\n  margin: 0 0 1.5rem 0;\n}\n.retry-btn[_ngcontent-%COMP%] {\n  background: #dc2626;\n  color: white;\n  border: none;\n  padding: 0.75rem 1.5rem;\n  border-radius: 8px;\n  cursor: pointer;\n  font-weight: 500;\n}\n.stats-grid[_ngcontent-%COMP%] {\n  display: grid;\n  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));\n  gap: 1.5rem;\n  margin-bottom: 3rem;\n}\n.stat-card[_ngcontent-%COMP%] {\n  background:\n    linear-gradient(\n      135deg,\n      #ffffff 0%,\n      #f8fafc 100%);\n  border-radius: 20px;\n  padding: 2rem;\n  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);\n  border: 1px solid rgba(255, 255, 255, 0.2);\n  transition: all 0.4s ease;\n  position: relative;\n  overflow: hidden;\n  -webkit-backdrop-filter: blur(10px);\n  backdrop-filter: blur(10px);\n}\n.stat-card[_ngcontent-%COMP%]::before {\n  content: "";\n  position: absolute;\n  top: 0;\n  left: 0;\n  right: 0;\n  height: 5px;\n  background:\n    linear-gradient(\n      135deg,\n      #667eea 0%,\n      #764ba2 100%);\n}\n.stat-card[data-color=green][_ngcontent-%COMP%]::before {\n  background:\n    linear-gradient(\n      135deg,\n      #10b981 0%,\n      #059669 100%);\n}\n.stat-card[data-color=orange][_ngcontent-%COMP%]::before {\n  background:\n    linear-gradient(\n      135deg,\n      #f59e0b 0%,\n      #d97706 100%);\n}\n.stat-card[data-color=purple][_ngcontent-%COMP%]::before {\n  background:\n    linear-gradient(\n      135deg,\n      #8b5cf6 0%,\n      #7c3aed 100%);\n}\n.stat-card[data-color=red][_ngcontent-%COMP%]::before {\n  background:\n    linear-gradient(\n      135deg,\n      #ef4444 0%,\n      #dc2626 100%);\n}\n.stat-card[_ngcontent-%COMP%]:hover {\n  transform: translateY(-8px) scale(1.02);\n  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);\n}\n.stat-card[_ngcontent-%COMP%]::after {\n  content: "";\n  position: absolute;\n  top: -50%;\n  right: -50%;\n  width: 100%;\n  height: 100%;\n  background:\n    linear-gradient(\n      45deg,\n      transparent,\n      rgba(255, 255, 255, 0.1),\n      transparent);\n  transform: rotate(45deg);\n  transition: all 0.6s ease;\n  opacity: 0;\n}\n.stat-card[_ngcontent-%COMP%]:hover::after {\n  animation: _ngcontent-%COMP%_shimmer 0.6s ease-in-out;\n}\n@keyframes _ngcontent-%COMP%_shimmer {\n  0% {\n    transform: translateX(-100%) translateY(-100%) rotate(45deg);\n    opacity: 0;\n  }\n  50% {\n    opacity: 1;\n  }\n  100% {\n    transform: translateX(100%) translateY(100%) rotate(45deg);\n    opacity: 0;\n  }\n}\n.card-header[_ngcontent-%COMP%] {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 1rem;\n}\n.card-icon[_ngcontent-%COMP%] {\n  width: 50px;\n  height: 50px;\n  border-radius: 12px;\n  background:\n    linear-gradient(\n      135deg,\n      #f1f5f9,\n      #e2e8f0);\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  font-size: 1.5rem;\n}\n.card-trend[_ngcontent-%COMP%] {\n  display: flex;\n  align-items: center;\n}\n.trend-indicator[_ngcontent-%COMP%] {\n  font-size: 0.875rem;\n  font-weight: 600;\n  padding: 0.25rem 0.5rem;\n  border-radius: 6px;\n}\n.trend-indicator.positive[_ngcontent-%COMP%] {\n  background:\n    linear-gradient(\n      135deg,\n      #dcfce7,\n      #bbf7d0);\n  color: #166534;\n  border: 1px solid #86efac;\n}\n.trend-indicator.negative[_ngcontent-%COMP%] {\n  background:\n    linear-gradient(\n      135deg,\n      #fecaca,\n      #fca5a5);\n  color: #991b1b;\n  border: 1px solid #f87171;\n}\n.trend-indicator.neutral[_ngcontent-%COMP%] {\n  background:\n    linear-gradient(\n      135deg,\n      #f1f5f9,\n      #e2e8f0);\n  color: #64748b;\n  border: 1px solid #cbd5e1;\n}\n.card-body[_ngcontent-%COMP%] {\n  text-align: left;\n}\n.card-value[_ngcontent-%COMP%] {\n  font-size: 2.5rem;\n  font-weight: 700;\n  color: #1e293b;\n  margin-bottom: 0.5rem;\n  line-height: 1;\n}\n.card-title[_ngcontent-%COMP%] {\n  font-size: 1rem;\n  font-weight: 600;\n  color: #374151;\n  margin-bottom: 0.25rem;\n}\n.card-description[_ngcontent-%COMP%] {\n  font-size: 0.875rem;\n  color: #6b7280;\n}\n.quick-actions[_ngcontent-%COMP%] {\n  margin-bottom: 3rem;\n}\n.section-title[_ngcontent-%COMP%] {\n  font-size: 1.5rem;\n  font-weight: 600;\n  color: #1e293b;\n  margin-bottom: 1.5rem;\n}\n.actions-grid[_ngcontent-%COMP%] {\n  display: grid;\n  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));\n  gap: 1.5rem;\n}\n.action-card[_ngcontent-%COMP%] {\n  background: white;\n  border: 1px solid #e2e8f0;\n  border-radius: 16px;\n  padding: 1.5rem;\n  cursor: pointer;\n  transition: all 0.3s ease;\n  display: flex;\n  align-items: center;\n  gap: 1rem;\n  text-decoration: none;\n  color: inherit;\n}\n.action-card[_ngcontent-%COMP%]:hover {\n  transform: translateY(-2px);\n  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);\n  border-color: #3b82f6;\n}\n.action-icon[_ngcontent-%COMP%] {\n  width: 60px;\n  height: 60px;\n  border-radius: 12px;\n  background:\n    linear-gradient(\n      135deg,\n      #f1f5f9,\n      #e2e8f0);\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  font-size: 1.5rem;\n  flex-shrink: 0;\n}\n.action-content[_ngcontent-%COMP%] {\n  flex: 1;\n}\n.action-content[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%] {\n  font-size: 1rem;\n  font-weight: 600;\n  color: #1e293b;\n  margin: 0 0 0.25rem 0;\n}\n.action-content[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\n  font-size: 0.875rem;\n  color: #6b7280;\n  margin: 0;\n}\n.action-arrow[_ngcontent-%COMP%] {\n  font-size: 1.25rem;\n  color: #9ca3af;\n  transition: all 0.3s ease;\n}\n.action-card[_ngcontent-%COMP%]:hover   .action-arrow[_ngcontent-%COMP%] {\n  color: #3b82f6;\n  transform: translateX(4px);\n}\n@media (max-width: 768px) {\n  .admin-dashboard[_ngcontent-%COMP%] {\n    padding: 1rem;\n  }\n  .dashboard-header[_ngcontent-%COMP%] {\n    flex-direction: column;\n    gap: 1rem;\n    text-align: center;\n  }\n  .stats-grid[_ngcontent-%COMP%] {\n    grid-template-columns: 1fr;\n  }\n  .actions-grid[_ngcontent-%COMP%] {\n    grid-template-columns: 1fr;\n  }\n  .action-card[_ngcontent-%COMP%] {\n    flex-direction: column;\n    text-align: center;\n  }\n}\n/*# sourceMappingURL=admin-dashboard.component.css.map */'] });
};
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && setClassMetadata(AdminDashboardComponent, [{
    type: Component,
    args: [{ selector: "app-admin-dashboard", standalone: true, imports: [
      CommonModule,
      RouterModule
    ], template: `<div class="admin-dashboard">
  <!-- Header am\xE9lior\xE9 -->
  <div class="dashboard-header">
    <div class="header-content">
      <div class="welcome-section">
        <div class="welcome-icon">
          <div class="icon-container">
            <svg width="28" height="28" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
              <path d="M3 9l9-7 9 7v11a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2z"></path>
              <polyline points="9,22 9,12 15,12 15,22"></polyline>
            </svg>
          </div>
        </div>
        <div class="welcome-text">
          <h1 class="dashboard-title">Tableau de bord administrateur</h1>
          <p class="dashboard-subtitle">Bienvenue dans l'espace d'administration OptiLet</p>
          <div class="breadcrumb">
            <span class="breadcrumb-item">Administration</span>
            <span class="breadcrumb-separator">\u203A</span>
            <span class="breadcrumb-item active">Dashboard</span>
          </div>
        </div>
      </div>
      <div class="header-actions">
        <div class="time-info">
          <span class="current-time">{{ getCurrentTime() }}</span>
        </div>
        <button class="refresh-btn" (click)="loadStatistiques()" [disabled]="isLoading()">
          <svg width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
            <polyline points="23 4 23 10 17 10"></polyline>
            <path d="M20.49 9A9 9 0 0 0 5.64 5.64L1 10m22 4l-4.64 4.36A9 9 0 0 1 3.51 15"></path>
          </svg>
          Actualiser
        </button>
        <button class="logout-btn" (click)="logout()">
          <svg width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
            <path d="M9 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h4"></path>
            <polyline points="16,17 21,12 16,7"></polyline>
            <line x1="21" y1="12" x2="9" y2="12"></line>
          </svg>
          D\xE9connexion
        </button>
      </div>
    </div>
  </div>

  <!-- Loading -->
  <div *ngIf="isLoading()" class="loading-state">
    <div class="loading-spinner"></div>
    <p>Chargement des statistiques...</p>
  </div>

  <!-- Error -->
  <div *ngIf="error() && !isLoading()" class="error-state">
    <div class="error-icon">\u26A0\uFE0F</div>
    <h3>Erreur de chargement</h3>
    <p>{{ error() }}</p>
    <button class="retry-btn" (click)="loadStatistiques()">R\xE9essayer</button>
  </div>

  <!-- Dashboard Content -->
  <div *ngIf="!isLoading() && !error()" class="dashboard-content">

    <!-- Statistics Cards -->
    <div class="stats-grid">
      <div *ngFor="let card of dashboardCards()" class="stat-card" [attr.data-color]="card.color">
        <div class="card-header">
          <div class="card-icon">
            <span [innerHTML]="getCardIcon(card.icon)"></span>
          </div>
          <div class="card-trend" *ngIf="card.trend">
            <span [class]="getTrendClass(card.trend)">
              {{ formatTrend(card.trend) }}
            </span>
          </div>
        </div>
        <div class="card-body">
          <div class="card-value">{{ card.value | number }}</div>
          <div class="card-title">{{ card.title }}</div>
          <div class="card-description">{{ card.description }}</div>
        </div>
      </div>
    </div>

    <!-- Quick Actions -->
    <div class="quick-actions">
      <h2 class="section-title">Actions rapides</h2>
      <div class="actions-grid">
        <button class="action-card" routerLink="/admin/dashboard/users">
          <div class="action-icon">\u{1F465}</div>
          <div class="action-content">
            <h3>Gestion des utilisateurs</h3>
            <p>G\xE9rer les comptes et permissions</p>
          </div>
          <div class="action-arrow">\u2192</div>
        </button>

        <button class="action-card" routerLink="/admin/dashboard/products">
          <div class="action-icon">\u{1F4E6}</div>
          <div class="action-content">
            <h3>Gestion des produits</h3>
            <p>Valider et mod\xE9rer les produits</p>
          </div>
          <div class="action-arrow">\u2192</div>
        </button>

        <button class="action-card" routerLink="/admin/dashboard/demandes">
          <div class="action-icon">\u{1F4CB}</div>
          <div class="action-content">
            <h3>Demandes en attente</h3>
            <p>Traiter les demandes de cat\xE9gories</p>
          </div>
          <div class="action-arrow">\u2192</div>
        </button>

        <button class="action-card" routerLink="/admin/dashboard/avis-moderation">
          <div class="action-icon">\u2B50</div>
          <div class="action-content">
            <h3>Mod\xE9ration des avis</h3>
            <p>G\xE9rer les avis clients</p>
          </div>
          <div class="action-arrow">\u2192</div>
        </button>
      </div>
    </div>

  </div>
</div>

`, styles: ['/* src/app/components/admin/admin-dashboard/admin-dashboard.component.css */\n.admin-dashboard {\n  padding: 1.5rem;\n  background:\n    linear-gradient(\n      135deg,\n      #f8fafc 0%,\n      #e2e8f0 100%);\n  min-height: 100vh;\n}\n.dashboard-header {\n  margin-bottom: 2rem;\n  padding: 2rem;\n  background:\n    linear-gradient(\n      135deg,\n      #ffffff 0%,\n      #f8fafc 100%);\n  border-radius: 20px;\n  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);\n  border: 1px solid rgba(255, 255, 255, 0.2);\n  -webkit-backdrop-filter: blur(10px);\n  backdrop-filter: blur(10px);\n}\n.header-content {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n}\n.welcome-section {\n  display: flex;\n  align-items: center;\n  gap: 1.5rem;\n}\n.welcome-icon {\n  position: relative;\n}\n.icon-container {\n  width: 70px;\n  height: 70px;\n  background:\n    linear-gradient(\n      135deg,\n      #667eea 0%,\n      #764ba2 100%);\n  border-radius: 20px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  color: white;\n  box-shadow: 0 8px 32px rgba(102, 126, 234, 0.3);\n  position: relative;\n}\n.icon-container::before {\n  content: "";\n  position: absolute;\n  inset: -2px;\n  background:\n    linear-gradient(\n      135deg,\n      #667eea,\n      #764ba2);\n  border-radius: 22px;\n  z-index: -1;\n  opacity: 0.3;\n  filter: blur(8px);\n}\n.welcome-text {\n  flex: 1;\n}\n.dashboard-title {\n  font-size: 2.2rem;\n  font-weight: 800;\n  color: #1e293b;\n  margin: 0 0 0.5rem 0;\n  background:\n    linear-gradient(\n      135deg,\n      #1e293b 0%,\n      #475569 100%);\n  -webkit-background-clip: text;\n  -webkit-text-fill-color: transparent;\n  background-clip: text;\n}\n.dashboard-subtitle {\n  color: #64748b;\n  font-size: 1.1rem;\n  margin: 0 0 0.75rem 0;\n  font-weight: 500;\n}\n.breadcrumb {\n  display: flex;\n  align-items: center;\n  gap: 0.5rem;\n  font-size: 0.875rem;\n}\n.breadcrumb-item {\n  color: #94a3b8;\n  font-weight: 500;\n}\n.breadcrumb-item.active {\n  color: #667eea;\n  font-weight: 600;\n}\n.breadcrumb-separator {\n  color: #cbd5e1;\n  font-weight: 300;\n}\n.header-actions {\n  display: flex;\n  align-items: center;\n  gap: 1rem;\n  flex-direction: row;\n  text-align: right;\n}\n.time-info {\n  font-size: 0.875rem;\n  color: #64748b;\n  font-weight: 500;\n}\n.current-time {\n  background:\n    linear-gradient(\n      135deg,\n      #f1f5f9 0%,\n      #e2e8f0 100%);\n  padding: 0.5rem 1rem;\n  border-radius: 12px;\n  border: 1px solid #e2e8f0;\n}\n.refresh-btn {\n  background:\n    linear-gradient(\n      135deg,\n      #667eea 0%,\n      #764ba2 100%);\n  color: white;\n  border: none;\n  padding: 1rem 1.5rem;\n  border-radius: 16px;\n  cursor: pointer;\n  transition: all 0.3s ease;\n  display: flex;\n  align-items: center;\n  gap: 0.75rem;\n  font-weight: 600;\n  font-size: 0.95rem;\n  box-shadow: 0 8px 32px rgba(102, 126, 234, 0.3);\n  border: 1px solid rgba(255, 255, 255, 0.2);\n  -webkit-backdrop-filter: blur(10px);\n  backdrop-filter: blur(10px);\n}\n.refresh-btn:hover:not(:disabled) {\n  transform: translateY(-3px);\n  box-shadow: 0 12px 40px rgba(102, 126, 234, 0.4);\n  background:\n    linear-gradient(\n      135deg,\n      #5a67d8 0%,\n      #6b46c1 100%);\n}\n.refresh-btn:disabled {\n  opacity: 0.6;\n  cursor: not-allowed;\n  transform: none;\n}\n.refresh-btn svg {\n  transition: transform 0.3s ease;\n}\n.refresh-btn:hover:not(:disabled) svg {\n  transform: rotate(180deg);\n}\n.logout-btn {\n  background:\n    linear-gradient(\n      135deg,\n      #ef4444 0%,\n      #dc2626 100%);\n  color: white;\n  border: none;\n  padding: 1rem 1.5rem;\n  border-radius: 16px;\n  cursor: pointer;\n  transition: all 0.3s ease;\n  display: flex;\n  align-items: center;\n  gap: 0.75rem;\n  font-weight: 600;\n  font-size: 0.95rem;\n  box-shadow: 0 8px 32px rgba(239, 68, 68, 0.3);\n  border: 1px solid rgba(255, 255, 255, 0.2);\n  -webkit-backdrop-filter: blur(10px);\n  backdrop-filter: blur(10px);\n}\n.logout-btn:hover {\n  transform: translateY(-3px);\n  box-shadow: 0 12px 40px rgba(239, 68, 68, 0.4);\n  background:\n    linear-gradient(\n      135deg,\n      #dc2626 0%,\n      #b91c1c 100%);\n}\n.logout-btn svg {\n  transition: transform 0.3s ease;\n}\n.logout-btn:hover svg {\n  transform: translateX(2px);\n}\n.loading-state {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  justify-content: center;\n  padding: 4rem;\n  text-align: center;\n}\n.loading-spinner {\n  width: 40px;\n  height: 40px;\n  border: 4px solid #e2e8f0;\n  border-top: 4px solid #3b82f6;\n  border-radius: 50%;\n  animation: spin 1s linear infinite;\n  margin-bottom: 1rem;\n}\n@keyframes spin {\n  0% {\n    transform: rotate(0deg);\n  }\n  100% {\n    transform: rotate(360deg);\n  }\n}\n.error-state {\n  background: #fef2f2;\n  border: 1px solid #fecaca;\n  border-radius: 12px;\n  padding: 2rem;\n  text-align: center;\n  margin: 2rem 0;\n}\n.error-icon {\n  font-size: 3rem;\n  margin-bottom: 1rem;\n}\n.error-state h3 {\n  color: #dc2626;\n  margin: 0 0 0.5rem 0;\n}\n.error-state p {\n  color: #7f1d1d;\n  margin: 0 0 1.5rem 0;\n}\n.retry-btn {\n  background: #dc2626;\n  color: white;\n  border: none;\n  padding: 0.75rem 1.5rem;\n  border-radius: 8px;\n  cursor: pointer;\n  font-weight: 500;\n}\n.stats-grid {\n  display: grid;\n  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));\n  gap: 1.5rem;\n  margin-bottom: 3rem;\n}\n.stat-card {\n  background:\n    linear-gradient(\n      135deg,\n      #ffffff 0%,\n      #f8fafc 100%);\n  border-radius: 20px;\n  padding: 2rem;\n  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);\n  border: 1px solid rgba(255, 255, 255, 0.2);\n  transition: all 0.4s ease;\n  position: relative;\n  overflow: hidden;\n  -webkit-backdrop-filter: blur(10px);\n  backdrop-filter: blur(10px);\n}\n.stat-card::before {\n  content: "";\n  position: absolute;\n  top: 0;\n  left: 0;\n  right: 0;\n  height: 5px;\n  background:\n    linear-gradient(\n      135deg,\n      #667eea 0%,\n      #764ba2 100%);\n}\n.stat-card[data-color=green]::before {\n  background:\n    linear-gradient(\n      135deg,\n      #10b981 0%,\n      #059669 100%);\n}\n.stat-card[data-color=orange]::before {\n  background:\n    linear-gradient(\n      135deg,\n      #f59e0b 0%,\n      #d97706 100%);\n}\n.stat-card[data-color=purple]::before {\n  background:\n    linear-gradient(\n      135deg,\n      #8b5cf6 0%,\n      #7c3aed 100%);\n}\n.stat-card[data-color=red]::before {\n  background:\n    linear-gradient(\n      135deg,\n      #ef4444 0%,\n      #dc2626 100%);\n}\n.stat-card:hover {\n  transform: translateY(-8px) scale(1.02);\n  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);\n}\n.stat-card::after {\n  content: "";\n  position: absolute;\n  top: -50%;\n  right: -50%;\n  width: 100%;\n  height: 100%;\n  background:\n    linear-gradient(\n      45deg,\n      transparent,\n      rgba(255, 255, 255, 0.1),\n      transparent);\n  transform: rotate(45deg);\n  transition: all 0.6s ease;\n  opacity: 0;\n}\n.stat-card:hover::after {\n  animation: shimmer 0.6s ease-in-out;\n}\n@keyframes shimmer {\n  0% {\n    transform: translateX(-100%) translateY(-100%) rotate(45deg);\n    opacity: 0;\n  }\n  50% {\n    opacity: 1;\n  }\n  100% {\n    transform: translateX(100%) translateY(100%) rotate(45deg);\n    opacity: 0;\n  }\n}\n.card-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 1rem;\n}\n.card-icon {\n  width: 50px;\n  height: 50px;\n  border-radius: 12px;\n  background:\n    linear-gradient(\n      135deg,\n      #f1f5f9,\n      #e2e8f0);\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  font-size: 1.5rem;\n}\n.card-trend {\n  display: flex;\n  align-items: center;\n}\n.trend-indicator {\n  font-size: 0.875rem;\n  font-weight: 600;\n  padding: 0.25rem 0.5rem;\n  border-radius: 6px;\n}\n.trend-indicator.positive {\n  background:\n    linear-gradient(\n      135deg,\n      #dcfce7,\n      #bbf7d0);\n  color: #166534;\n  border: 1px solid #86efac;\n}\n.trend-indicator.negative {\n  background:\n    linear-gradient(\n      135deg,\n      #fecaca,\n      #fca5a5);\n  color: #991b1b;\n  border: 1px solid #f87171;\n}\n.trend-indicator.neutral {\n  background:\n    linear-gradient(\n      135deg,\n      #f1f5f9,\n      #e2e8f0);\n  color: #64748b;\n  border: 1px solid #cbd5e1;\n}\n.card-body {\n  text-align: left;\n}\n.card-value {\n  font-size: 2.5rem;\n  font-weight: 700;\n  color: #1e293b;\n  margin-bottom: 0.5rem;\n  line-height: 1;\n}\n.card-title {\n  font-size: 1rem;\n  font-weight: 600;\n  color: #374151;\n  margin-bottom: 0.25rem;\n}\n.card-description {\n  font-size: 0.875rem;\n  color: #6b7280;\n}\n.quick-actions {\n  margin-bottom: 3rem;\n}\n.section-title {\n  font-size: 1.5rem;\n  font-weight: 600;\n  color: #1e293b;\n  margin-bottom: 1.5rem;\n}\n.actions-grid {\n  display: grid;\n  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));\n  gap: 1.5rem;\n}\n.action-card {\n  background: white;\n  border: 1px solid #e2e8f0;\n  border-radius: 16px;\n  padding: 1.5rem;\n  cursor: pointer;\n  transition: all 0.3s ease;\n  display: flex;\n  align-items: center;\n  gap: 1rem;\n  text-decoration: none;\n  color: inherit;\n}\n.action-card:hover {\n  transform: translateY(-2px);\n  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);\n  border-color: #3b82f6;\n}\n.action-icon {\n  width: 60px;\n  height: 60px;\n  border-radius: 12px;\n  background:\n    linear-gradient(\n      135deg,\n      #f1f5f9,\n      #e2e8f0);\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  font-size: 1.5rem;\n  flex-shrink: 0;\n}\n.action-content {\n  flex: 1;\n}\n.action-content h3 {\n  font-size: 1rem;\n  font-weight: 600;\n  color: #1e293b;\n  margin: 0 0 0.25rem 0;\n}\n.action-content p {\n  font-size: 0.875rem;\n  color: #6b7280;\n  margin: 0;\n}\n.action-arrow {\n  font-size: 1.25rem;\n  color: #9ca3af;\n  transition: all 0.3s ease;\n}\n.action-card:hover .action-arrow {\n  color: #3b82f6;\n  transform: translateX(4px);\n}\n@media (max-width: 768px) {\n  .admin-dashboard {\n    padding: 1rem;\n  }\n  .dashboard-header {\n    flex-direction: column;\n    gap: 1rem;\n    text-align: center;\n  }\n  .stats-grid {\n    grid-template-columns: 1fr;\n  }\n  .actions-grid {\n    grid-template-columns: 1fr;\n  }\n  .action-card {\n    flex-direction: column;\n    text-align: center;\n  }\n}\n/*# sourceMappingURL=admin-dashboard.component.css.map */\n'] }]
  }], () => [{ type: AdminService }, { type: AdminTestService }, { type: AdminAuthService }, { type: Router }], null);
})();
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && \u0275setClassDebugInfo(AdminDashboardComponent, { className: "AdminDashboardComponent", filePath: "src/app/components/admin/admin-dashboard/admin-dashboard.component.ts", lineNumber: 27 });
})();
export {
  AdminDashboardComponent
};
//# sourceMappingURL=chunk-WAO4F4AD.js.map

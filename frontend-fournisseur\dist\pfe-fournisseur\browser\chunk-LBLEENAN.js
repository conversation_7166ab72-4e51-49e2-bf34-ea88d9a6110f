import {
  environment
} from "./chunk-GFHHPDQ6.js";
import {
  HttpClient
} from "./chunk-7JDDWGD3.js";
import {
  Injectable,
  setClassMetadata,
  tap,
  ɵɵdefineInjectable,
  ɵɵinject
} from "./chunk-UBZQS7JS.js";

// src/app/services/categorie.service.ts
var CategorieService = class _CategorieService {
  http;
  API_URL = `${environment.apiUrl || "https://localhost:7264/api"}/Categories`;
  constructor(http) {
    this.http = http;
  }
  /**
   * GET /api/Categories - Obtenir toutes les catégories
   */
  getAll() {
    console.log("\u{1F4E6} R\xE9cup\xE9ration des cat\xE9gories");
    return this.http.get(`${this.API_URL}/enriched`).pipe(tap((response) => console.log("\u2705 Cat\xE9gories r\xE9cup\xE9r\xE9es:", response)));
  }
  /**
   * GET /api/Categories/admin - Obtenir toutes les catégories pour les admins (y compris non validées)
   */
  getAllForAdmin() {
    console.log("\u{1F4E6} R\xE9cup\xE9ration des cat\xE9gories pour admin");
    return this.http.get(`${this.API_URL}/admin`).pipe(tap((response) => console.log("\u2705 Cat\xE9gories admin r\xE9cup\xE9r\xE9es:", response)));
  }
  /**
   * GET /api/Categories/{id} - Obtenir une catégorie par ID
   */
  getById(id) {
    console.log("\u{1F50D} R\xE9cup\xE9ration de la cat\xE9gorie ID:", id);
    return this.http.get(`${this.API_URL}/${id}`).pipe(tap((response) => console.log("\u2705 Cat\xE9gorie r\xE9cup\xE9r\xE9e:", response)));
  }
  /**
   * POST /api/Categories - Créer une nouvelle catégorie
   */
  create(categorie) {
    console.log("\u2795 Cr\xE9ation d'une nouvelle cat\xE9gorie:", categorie);
    return this.http.post(this.API_URL, categorie).pipe(tap((response) => console.log("\u2705 Cat\xE9gorie cr\xE9\xE9e:", response)));
  }
  /**
   * PUT /api/Categories/{id} - Mettre à jour une catégorie
   */
  update(id, categorie) {
    console.log("\u270F\uFE0F Mise \xE0 jour de la cat\xE9gorie ID:", id, categorie);
    return this.http.put(`${this.API_URL}/${id}`, categorie).pipe(tap((response) => console.log("\u2705 Cat\xE9gorie mise \xE0 jour:", response)));
  }
  /**
   * DELETE /api/Categories/{id} - Supprimer une catégorie
   */
  delete(id) {
    console.log("\u{1F5D1}\uFE0F Suppression de la cat\xE9gorie ID:", id);
    return this.http.delete(`${this.API_URL}/${id}`).pipe(tap(() => console.log("\u2705 Cat\xE9gorie supprim\xE9e:", id)));
  }
  /**
   * GET /api/Categories/{id}/sous-categories - Obtenir les sous-catégories d'une catégorie
   */
  getSousCategories(id) {
    console.log("\u{1F4C2} R\xE9cup\xE9ration des sous-cat\xE9gories pour la cat\xE9gorie:", id);
    return this.http.get(`${this.API_URL}/${id}/sous-categories`).pipe(tap((response) => console.log("\u2705 Sous-cat\xE9gories r\xE9cup\xE9r\xE9es:", response)));
  }
  /**
   * GET /api/Categories/{id}/produits-count - Obtenir le nombre de produits d'une catégorie
   */
  getProduitsCount(id) {
    console.log("\u{1F522} R\xE9cup\xE9ration du nombre de produits pour la cat\xE9gorie:", id);
    return this.http.get(`${this.API_URL}/${id}/produits-count`).pipe(tap((response) => console.log("\u2705 Nombre de produits r\xE9cup\xE9r\xE9:", response)));
  }
  /**
   * GET /api/Categories/dropdown - Obtenir les catégories pour dropdown
   */
  getDropdown() {
    console.log("\u{1F4CB} R\xE9cup\xE9ration des cat\xE9gories pour dropdown");
    return this.http.get(`${this.API_URL}/dropdown`).pipe(tap((response) => console.log("\u2705 Dropdown cat\xE9gories r\xE9cup\xE9r\xE9:", response)));
  }
  /**
   * PATCH /api/Categories/{id}/toggle-visibility - Basculer la visibilité d'une catégorie
   */
  toggleVisibility(id) {
    console.log("\u{1F441}\uFE0F Basculer la visibilit\xE9 de la cat\xE9gorie:", id);
    return this.http.patch(`${this.API_URL}/${id}/toggle-visibility`, {}).pipe(tap((response) => console.log("\u2705 Visibilit\xE9 bascul\xE9e:", response)));
  }
  static \u0275fac = function CategorieService_Factory(__ngFactoryType__) {
    return new (__ngFactoryType__ || _CategorieService)(\u0275\u0275inject(HttpClient));
  };
  static \u0275prov = /* @__PURE__ */ \u0275\u0275defineInjectable({ token: _CategorieService, factory: _CategorieService.\u0275fac, providedIn: "root" });
};
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && setClassMetadata(CategorieService, [{
    type: Injectable,
    args: [{
      providedIn: "root"
    }]
  }], () => [{ type: HttpClient }], null);
})();

export {
  CategorieService
};
//# sourceMappingURL=chunk-LBLEENAN.js.map

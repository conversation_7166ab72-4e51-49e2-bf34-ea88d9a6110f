{"version": 3, "sources": ["src/app/components/auth/login/login.component.css"], "sourcesContent": ["/* 🏢 Interface de Connexion Fournisseur - Design Professionnel */\n\n.login-container {\n  min-height: 100vh;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);\n  padding: 2rem;\n  position: relative;\n}\n\n.login-container::before {\n  content: '';\n  position: absolute;\n  top: 0;\n  left: 0;\n  right: 0;\n  bottom: 0;\n  background-image:\n    radial-gradient(circle at 25% 25%, rgba(59, 130, 246, 0.05) 0%, transparent 50%),\n    radial-gradient(circle at 75% 75%, rgba(16, 185, 129, 0.05) 0%, transparent 50%);\n  pointer-events: none;\n}\n\n.login-card {\n  background: rgba(255, 255, 255, 0.98);\n  backdrop-filter: blur(10px);\n  border: 1px solid rgba(255, 255, 255, 0.2);\n  border-radius: 16px;\n  box-shadow:\n    0 20px 25px -5px rgba(0, 0, 0, 0.1),\n    0 10px 10px -5px rgba(0, 0, 0, 0.04);\n  padding: 3rem;\n  width: 100%;\n  max-width: 480px;\n  position: relative;\n  transition: all 0.3s ease;\n}\n\n.login-card:hover {\n  box-shadow:\n    0 25px 50px -12px rgba(0, 0, 0, 0.15),\n    0 20px 25px -5px rgba(0, 0, 0, 0.1);\n}\n\n.login-card::before {\n  content: '';\n  position: absolute;\n  top: 0;\n  left: 0;\n  right: 0;\n  height: 4px;\n  background: linear-gradient(90deg, #3b82f6 0%, #10b981 100%);\n  border-radius: 16px 16px 0 0;\n}\n\n/* Supprimé - remplacé par sparkleEnhanced */\n\n\n\n/* Supprimé - remplacé par sparkleEnhanced */\n\n.login-header {\n  text-align: center;\n  margin-bottom: 2.5rem;\n  position: relative;\n}\n\n.login-header::before {\n  content: '🏢';\n  display: block;\n  font-size: 2.5rem;\n  margin-bottom: 1rem;\n  opacity: 0.8;\n}\n\n\n\n.login-header h1 {\n  color: #1e293b;\n  margin-bottom: 0.5rem;\n  font-size: 2rem;\n  font-weight: 700;\n  letter-spacing: -0.025em;\n}\n\n.login-header p {\n  color: #64748b;\n  margin: 0;\n  font-size: 1rem;\n  font-weight: 500;\n}\n\n.login-form {\n  margin-bottom: 2rem;\n}\n\n.form-group {\n  margin-bottom: 2rem;\n  position: relative;\n}\n\n.form-group label {\n  display: block;\n  margin-bottom: 0.5rem;\n  color: #374151;\n  font-weight: 600;\n  font-size: 0.875rem;\n  letter-spacing: 0.025em;\n}\n\n.form-control {\n  width: 100%;\n  padding: 1rem 1.25rem;\n  border: 1px solid #d1d5db;\n  border-radius: 8px;\n  font-size: 1rem;\n  font-weight: 400;\n  background: #ffffff;\n  transition: all 0.2s ease;\n  box-sizing: border-box;\n}\n\n.form-control::placeholder {\n  color: #9ca3af;\n  font-weight: 400;\n}\n\n.form-control:focus {\n  outline: none;\n  border-color: #3b82f6;\n  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);\n}\n\n.form-control.is-invalid {\n  border-color: #ef4444;\n  box-shadow: 0 0 0 3px rgba(239, 68, 68, 0.1);\n}\n\n.invalid-feedback {\n  color: #ef4444;\n  font-size: 0.875rem;\n  font-weight: 500;\n  margin-top: 0.5rem;\n  display: flex;\n  align-items: center;\n  gap: 0.5rem;\n}\n\n.invalid-feedback::before {\n  content: '⚠️';\n  font-size: 0.875rem;\n}\n\n.alert {\n  padding: 1.25rem 1.5rem;\n  border-radius: 16px;\n  margin-bottom: 1.5rem;\n  font-size: 0.95rem;\n  font-weight: 500;\n  position: relative;\n  overflow: hidden;\n  backdrop-filter: blur(10px);\n}\n\n.alert::before {\n  content: '';\n  position: absolute;\n  top: 0;\n  left: 0;\n  width: 4px;\n  height: 100%;\n  background: currentColor;\n}\n\n.alert-danger {\n  background: rgba(254, 242, 242, 0.9);\n  color: #dc2626;\n  border: 1px solid rgba(239, 68, 68, 0.2);\n}\n\n.alert-danger::after {\n  content: '❌';\n  position: absolute;\n  top: 1rem;\n  right: 1rem;\n  font-size: 1.25rem;\n}\n\n.btn {\n  padding: 1rem 2rem;\n  border: none;\n  border-radius: 8px;\n  font-size: 1rem;\n  font-weight: 600;\n  cursor: pointer;\n  transition: all 0.2s ease;\n  text-decoration: none;\n  display: inline-block;\n  text-align: center;\n  position: relative;\n}\n\n.btn-primary {\n  background: #3b82f6;\n  color: white;\n  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);\n}\n\n.btn-primary:hover:not(:disabled) {\n  background: #2563eb;\n  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);\n}\n\n.btn-primary:active:not(:disabled) {\n  background: #1d4ed8;\n}\n\n.btn-primary:disabled {\n  opacity: 0.6;\n  cursor: not-allowed;\n  background: #94a3b8;\n}\n\n\n\n.btn-login {\n  width: 100%;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  gap: 0.75rem;\n}\n\n.spinner {\n  width: 20px;\n  height: 20px;\n  border: 3px solid rgba(255, 255, 255, 0.3);\n  border-top: 3px solid white;\n  border-radius: 50%;\n\n  display: inline-block;\n}\n\n\n\n.login-footer {\n  text-align: center;\n  margin-top: 2.5rem;\n  padding-top: 2rem;\n  border-top: 1px solid rgba(226, 232, 240, 0.6);\n}\n\n.login-footer p {\n  color: #64748b;\n  font-size: 0.95rem;\n  margin: 0 0 1rem 0;\n}\n\n.register-link {\n  color: #3b82f6;\n  text-decoration: none;\n  font-size: 0.95rem;\n  font-weight: 600;\n  position: relative;\n  transition: all 0.3s ease;\n}\n\n.register-link::after {\n  content: '';\n  position: absolute;\n  bottom: -2px;\n  left: 0;\n  width: 0;\n  height: 2px;\n  background: linear-gradient(90deg, #3b82f6, #6366f1);\n  transition: width 0.3s ease;\n}\n\n.register-link:hover {\n  color: #6366f1;\n  transform: translateY(-1px);\n}\n\n.register-link:hover::after {\n  width: 100%;\n}\n\n/* Lien admin */\n.admin-link {\n  margin-top: 15px;\n  text-align: center;\n}\n\n.admin-link-btn {\n  display: inline-flex;\n  align-items: center;\n  gap: 10px;\n  padding: 12px 24px;\n  background: linear-gradient(135deg, #3b82f6 0%, #6366f1 50%, #8b5cf6 100%);\n  color: white;\n  text-decoration: none;\n  border-radius: 14px;\n  font-weight: 700;\n  font-size: 0.95rem;\n  transition: all 0.3s ease;\n  box-shadow:\n    0 4px 12px rgba(59, 130, 246, 0.4),\n    0 2px 6px rgba(99, 102, 241, 0.3);\n  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);\n}\n\n.admin-link-btn:hover {\n  background: linear-gradient(135deg, #6366f1 0%, #8b5cf6 50%, #a855f7 100%);\n  transform: translateY(-3px) scale(1.05);\n  box-shadow:\n    0 8px 20px rgba(59, 130, 246, 0.5),\n    0 4px 12px rgba(99, 102, 241, 0.4);\n}\n\n/* Design épuré et professionnel */\n\n/* 📱 Responsive Design Créatif */\n@media (max-width: 640px) {\n  .login-container {\n    padding: 1rem;\n  }\n\n  .login-card {\n    padding: 2rem 1.5rem;\n    margin: 1rem;\n  }\n\n  .login-header h1 {\n    font-size: 1.75rem;\n  }\n\n  .login-header::before {\n    font-size: 2.5rem;\n  }\n\n  .form-control {\n    padding: 1rem 1.25rem;\n  }\n\n  .btn-login {\n    padding: 1rem 1.5rem;\n    font-size: 1rem;\n  }\n}\n\n@media (max-width: 480px) {\n  .login-card {\n    padding: 1.5rem 1rem;\n  }\n\n  .login-header h1 {\n    font-size: 1.5rem;\n  }\n\n  .login-header::before {\n    font-size: 2rem;\n  }\n}\n\n/* 🌙 Mode sombre (optionnel) */\n@media (prefers-color-scheme: dark) {\n  .login-card {\n    background: linear-gradient(135deg,\n      rgba(15, 23, 42, 0.95) 0%,\n      rgba(30, 41, 59, 0.92) 25%,\n      rgba(51, 65, 85, 0.90) 50%,\n      rgba(30, 41, 59, 0.92) 75%,\n      rgba(15, 23, 42, 0.95) 100%);\n    border: 1px solid rgba(255, 255, 255, 0.1);\n  }\n\n  .login-header h1 {\n    background: linear-gradient(135deg, #60a5fa 0%, #818cf8 25%, #a78bfa 50%, #c084fc 75%, #f472b6 100%);\n    -webkit-background-clip: text;\n    -webkit-text-fill-color: transparent;\n    background-clip: text;\n  }\n\n  .login-header p {\n    color: #94a3b8;\n  }\n\n  .form-group label {\n    color: #e2e8f0;\n  }\n\n  .form-control {\n    background: rgba(30, 41, 59, 0.8);\n    border-color: rgba(71, 85, 105, 0.8);\n    color: #e2e8f0;\n  }\n\n  .form-control::placeholder {\n    color: #64748b;\n  }\n}\n\n\n\n\n"], "mappings": ";AAEA,CAAC;AACC,cAAY;AACZ,WAAS;AACT,eAAa;AACb,mBAAiB;AACjB;AAAA,IAAY;AAAA,MAAgB,MAAhB;AAAA,MAAwB,QAAQ,EAAhC;AAAA,MAAoC,QAAQ;AACxD,WAAS;AACT,YAAU;AACZ;AAEA,CAVC,eAUe;AACd,WAAS;AACT,YAAU;AACV,OAAK;AACL,QAAM;AACN,SAAO;AACP,UAAQ;AACR;AAAA,IACE;AAAA,MAAgB,OAAO,GAAG,IAAI,GAAG;AAAA,MAAE,KAAK,EAAE,EAAE,GAAG,EAAE,GAAG,EAAE,MAAM,EAAE;AAAA,MAAE,YAAY,IAAI;AAAA,IAChF;AAAA,MAAgB,OAAO,GAAG,IAAI,GAAG;AAAA,MAAE,KAAK,EAAE,EAAE,GAAG,EAAE,GAAG,EAAE,MAAM,EAAE;AAAA,MAAE,YAAY;AAC9E,kBAAgB;AAClB;AAEA,CAAC;AACC,cAAY,KAAK,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE;AAChC,2BAAiB,KAAK;AAAtB,mBAAiB,KAAK;AACtB,UAAQ,IAAI,MAAM,KAAK,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE;AACtC,iBAAe;AACf,cACE,EAAE,KAAK,KAAK,KAAK,KAAK,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,IAAI,EACnC,EAAE,KAAK,KAAK,KAAK,KAAK,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE;AACjC,WAAS;AACT,SAAO;AACP,aAAW;AACX,YAAU;AACV,cAAY,IAAI,KAAK;AACvB;AAEA,CAfC,UAeU;AACT,cACE,EAAE,KAAK,KAAK,MAAM,KAAK,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,KAAK,EACrC,EAAE,KAAK,KAAK,KAAK,KAAK,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE;AACnC;AAEA,CArBC,UAqBU;AACT,WAAS;AACT,YAAU;AACV,OAAK;AACL,QAAM;AACN,SAAO;AACP,UAAQ;AACR;AAAA,IAAY;AAAA,MAAgB,KAAhB;AAAA,MAAuB,QAAQ,EAA/B;AAAA,MAAmC,QAAQ;AACvD,iBAAe,KAAK,KAAK,EAAE;AAC7B;AAQA,CAAC;AACC,cAAY;AACZ,iBAAe;AACf,YAAU;AACZ;AAEA,CANC,YAMY;AACX,WAAS;AACT,WAAS;AACT,aAAW;AACX,iBAAe;AACf,WAAS;AACX;AAIA,CAhBC,aAgBa;AACZ,SAAO;AACP,iBAAe;AACf,aAAW;AACX,eAAa;AACb,kBAAgB;AAClB;AAEA,CAxBC,aAwBa;AACZ,SAAO;AACP,UAAQ;AACR,aAAW;AACX,eAAa;AACf;AAEA,CAAC;AACC,iBAAe;AACjB;AAEA,CAAC;AACC,iBAAe;AACf,YAAU;AACZ;AAEA,CALC,WAKW;AACV,WAAS;AACT,iBAAe;AACf,SAAO;AACP,eAAa;AACb,aAAW;AACX,kBAAgB;AAClB;AAEA,CAAC;AACC,SAAO;AACP,WAAS,KAAK;AACd,UAAQ,IAAI,MAAM;AAClB,iBAAe;AACf,aAAW;AACX,eAAa;AACb,cAAY;AACZ,cAAY,IAAI,KAAK;AACrB,cAAY;AACd;AAEA,CAZC,YAYY;AACX,SAAO;AACP,eAAa;AACf;AAEA,CAjBC,YAiBY;AACX,WAAS;AACT,gBAAc;AACd,cAAY,EAAE,EAAE,EAAE,IAAI,KAAK,EAAE,EAAE,GAAG,EAAE,GAAG,EAAE;AAC3C;AAEA,CAvBC,YAuBY,CAAC;AACZ,gBAAc;AACd,cAAY,EAAE,EAAE,EAAE,IAAI,KAAK,GAAG,EAAE,EAAE,EAAE,EAAE,EAAE;AAC1C;AAEA,CAAC;AACC,SAAO;AACP,aAAW;AACX,eAAa;AACb,cAAY;AACZ,WAAS;AACT,eAAa;AACb,OAAK;AACP;AAEA,CAVC,gBAUgB;AACf,WAAS;AACT,aAAW;AACb;AAEA,CAAC;AACC,WAAS,QAAQ;AACjB,iBAAe;AACf,iBAAe;AACf,aAAW;AACX,eAAa;AACb,YAAU;AACV,YAAU;AACV,2BAAiB,KAAK;AAAtB,mBAAiB,KAAK;AACxB;AAEA,CAXC,KAWK;AACJ,WAAS;AACT,YAAU;AACV,OAAK;AACL,QAAM;AACN,SAAO;AACP,UAAQ;AACR,cAAY;AACd;AAEA,CAAC;AACC,cAAY,KAAK,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE;AAChC,SAAO;AACP,UAAQ,IAAI,MAAM,KAAK,GAAG,EAAE,EAAE,EAAE,EAAE,EAAE;AACtC;AAEA,CANC,YAMY;AACX,WAAS;AACT,YAAU;AACV,OAAK;AACL,SAAO;AACP,aAAW;AACb;AAEA,CAAC;AACC,WAAS,KAAK;AACd,UAAQ;AACR,iBAAe;AACf,aAAW;AACX,eAAa;AACb,UAAQ;AACR,cAAY,IAAI,KAAK;AACrB,mBAAiB;AACjB,WAAS;AACT,cAAY;AACZ,YAAU;AACZ;AAEA,CAAC;AACC,cAAY;AACZ,SAAO;AACP,cAAY,EAAE,IAAI,IAAI,KAAK,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE;AACtC;AAEA,CANC,WAMW,MAAM,KAAK;AACrB,cAAY;AACZ,cAAY,EAAE,IAAI,IAAI,KAAK,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE;AACtC;AAEA,CAXC,WAWW,OAAO,KAAK;AACtB,cAAY;AACd;AAEA,CAfC,WAeW;AACV,WAAS;AACT,UAAQ;AACR,cAAY;AACd;AAIA,CAAC;AACC,SAAO;AACP,WAAS;AACT,eAAa;AACb,mBAAiB;AACjB,OAAK;AACP;AAEA,CAAC;AACC,SAAO;AACP,UAAQ;AACR,UAAQ,IAAI,MAAM,KAAK,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE;AACtC,cAAY,IAAI,MAAM;AACtB,iBAAe;AAEf,WAAS;AACX;AAIA,CAAC;AACC,cAAY;AACZ,cAAY;AACZ,eAAa;AACb,cAAY,IAAI,MAAM,KAAK,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE;AAC5C;AAEA,CAPC,aAOa;AACZ,SAAO;AACP,aAAW;AACX,UAAQ,EAAE,EAAE,KAAK;AACnB;AAEA,CAAC;AACC,SAAO;AACP,mBAAiB;AACjB,aAAW;AACX,eAAa;AACb,YAAU;AACV,cAAY,IAAI,KAAK;AACvB;AAEA,CATC,aASa;AACZ,WAAS;AACT,YAAU;AACV,UAAQ;AACR,QAAM;AACN,SAAO;AACP,UAAQ;AACR;AAAA,IAAY;AAAA,MAAgB,KAAhB;AAAA,MAAuB,OAAvB;AAAA,MAAgC;AAC5C,cAAY,MAAM,KAAK;AACzB;AAEA,CApBC,aAoBa;AACZ,SAAO;AACP,aAAW,WAAW;AACxB;AAEA,CAzBC,aAyBa,MAAM;AAClB,SAAO;AACT;AAGA,CAAC;AACC,cAAY;AACZ,cAAY;AACd;AAEA,CAAC;AACC,WAAS;AACT,eAAa;AACb,OAAK;AACL,WAAS,KAAK;AACd;AAAA,IAAY;AAAA,MAAgB,MAAhB;AAAA,MAAwB,QAAQ,EAAhC;AAAA,MAAoC,QAAQ,GAA5C;AAAA,MAAiD,QAAQ;AACrE,SAAO;AACP,mBAAiB;AACjB,iBAAe;AACf,eAAa;AACb,aAAW;AACX,cAAY,IAAI,KAAK;AACrB,cACE,EAAE,IAAI,KAAK,KAAK,EAAE,EAAE,GAAG,EAAE,GAAG,EAAE,IAAI,EAClC,EAAE,IAAI,IAAI,KAAK,EAAE,EAAE,GAAG,EAAE,GAAG,EAAE;AAC/B,eAAa,EAAE,IAAI,IAAI,KAAK,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE;AACvC;AAEA,CAlBC,cAkBc;AACb;AAAA,IAAY;AAAA,MAAgB,MAAhB;AAAA,MAAwB,QAAQ,EAAhC;AAAA,MAAoC,QAAQ,GAA5C;AAAA,MAAiD,QAAQ;AACrE,aAAW,WAAW,MAAM,MAAM;AAClC,cACE,EAAE,IAAI,KAAK,KAAK,EAAE,EAAE,GAAG,EAAE,GAAG,EAAE,IAAI,EAClC,EAAE,IAAI,KAAK,KAAK,EAAE,EAAE,GAAG,EAAE,GAAG,EAAE;AAClC;AAKA,OAAO,CAAC,SAAS,EAAE;AACjB,GAnUD;AAoUG,aAAS;AACX;AAEA,GAhTD;AAiTG,aAAS,KAAK;AACd,YAAQ;AACV;AAEA,GA/QD,aA+Qe;AACZ,eAAW;AACb;AAEA,GAnRD,YAmRc;AACX,eAAW;AACb;AAEA,GAtOD;AAuOG,aAAS,KAAK;AAChB;AAEA,GAvHD;AAwHG,aAAS,KAAK;AACd,eAAW;AACb;AACF;AAEA,OAAO,CAAC,SAAS,EAAE;AACjB,GAxUD;AAyUG,aAAS,OAAO;AAClB;AAEA,GAtSD,aAsSe;AACZ,eAAW;AACb;AAEA,GA1SD,YA0Sc;AACX,eAAW;AACb;AACF;AAGA,OAAO,CAAC,oBAAoB,EAAE;AAC5B,GAvVD;AAwVG;AAAA,MAAY;AAAA,QAAgB,MAAhB;AAAA,QACV,KAAK,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,MAAM,EADb;AAAA,QAEV,KAAK,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,MAAM,GAFb;AAAA,QAGV,KAAK,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,MAAM,GAHb;AAAA,QAIV,KAAK,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,MAAM,GAJb;AAAA,QAKV,KAAK,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,MAAM;AACzB,YAAQ,IAAI,MAAM,KAAK,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE;AACxC;AAEA,GA3TD,aA2Te;AACZ;AAAA,MAAY;AAAA,QAAgB,MAAhB;AAAA,QAAwB,QAAQ,EAAhC;AAAA,QAAoC,QAAQ,GAA5C;AAAA,QAAiD,QAAQ,GAAzD;AAAA,QAA8D,QAAQ,GAAtE;AAAA,QAA2E,QAAQ;AAC/F,6BAAyB;AACzB,6BAAyB;AACzB,qBAAiB;AACnB;AAEA,GAlUD,aAkUe;AACZ,WAAO;AACT;AAEA,GAnSD,WAmSa;AACV,WAAO;AACT;AAEA,GAzRD;AA0RG,gBAAY,KAAK,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE;AAC7B,kBAAc,KAAK,EAAE,EAAE,EAAE,EAAE,GAAG,EAAE;AAChC,WAAO;AACT;AAEA,GA/RD,YA+Rc;AACX,WAAO;AACT;AACF;", "names": []}
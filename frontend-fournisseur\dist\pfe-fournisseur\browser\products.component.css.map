{"version": 3, "sources": ["src/app/components/products/products.component.scss"], "sourcesContent": [".products-container {\n  padding: 2rem;\n  max-width: 1200px;\n  margin: 0 auto;\n  background: #f8f9fa;\n  min-height: 100vh;\n}\n\n.products-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 2rem;\n  background: white;\n  padding: 1.5rem;\n  border-radius: 12px;\n  box-shadow: 0 2px 8px rgba(0,0,0,0.1);\n\n  h1 {\n    margin: 0;\n    color: #333;\n    font-size: 2rem;\n    font-weight: 600;\n  }\n}\n\n.stats-grid {\n  display: grid;\n  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));\n  gap: 1rem;\n  margin-bottom: 2rem;\n}\n\n.stat-card {\n  background: white;\n  padding: 1.5rem;\n  border-radius: 12px;\n  box-shadow: 0 2px 8px rgba(0,0,0,0.1);\n  text-align: center;\n  transition: transform 0.2s ease;\n\n  &:hover {\n    transform: translateY(-2px);\n  }\n}\n\n.stat-number {\n  font-size: 2.5rem;\n  font-weight: bold;\n  color: #007bff;\n  margin-bottom: 0.5rem;\n}\n\n.stat-label {\n  color: #666;\n  font-size: 0.9rem;\n  text-transform: uppercase;\n  letter-spacing: 0.5px;\n}\n\n.search-box {\n  margin-bottom: 2rem;\n}\n\n.search-input {\n  width: 100%;\n  max-width: 400px;\n  padding: 0.75rem 1rem;\n  border: 2px solid #e9ecef;\n  border-radius: 8px;\n  font-size: 1rem;\n  transition: border-color 0.2s ease;\n\n  &:focus {\n    outline: none;\n    border-color: #007bff;\n    box-shadow: 0 0 0 3px rgba(0, 123, 255, 0.1);\n  }\n}\n\n.products-grid {\n  display: grid;\n  grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));\n  gap: 1.5rem;\n  margin-bottom: 2rem;\n}\n\n.product-card {\n  background: white;\n  border-radius: 12px;\n  padding: 1.5rem;\n  box-shadow: 0 2px 8px rgba(0,0,0,0.1);\n  transition: all 0.3s ease;\n  border: 1px solid #e9ecef;\n  cursor: pointer;\n\n  &:hover {\n    transform: translateY(-4px);\n    box-shadow: 0 8px 25px rgba(0,0,0,0.15);\n  }\n}\n\n.product-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: flex-start;\n  margin-bottom: 1rem;\n\n  h3 {\n    margin: 0;\n    color: #333;\n    font-size: 1.25rem;\n    font-weight: 600;\n  }\n}\n\n.product-status {\n  padding: 0.25rem 0.75rem;\n  border-radius: 20px;\n  font-size: 0.75rem;\n  font-weight: 600;\n  text-transform: uppercase;\n  letter-spacing: 0.5px;\n\n  &.status-in-stock {\n    background: #d4edda;\n    color: #155724;\n    border: 1px solid #c3e6cb;\n  }\n\n  &.status-low-stock {\n    background: #fff3cd;\n    color: #856404;\n    border: 1px solid #ffeaa7;\n  }\n\n  &.status-out-of-stock {\n    background: #f8d7da;\n    color: #721c24;\n    border: 1px solid #f5c6cb;\n  }\n}\n\n.product-description {\n  color: #666;\n  margin-bottom: 1rem;\n  line-height: 1.5;\n  display: -webkit-box;\n  -webkit-line-clamp: 2;\n  -webkit-box-orient: vertical;\n  overflow: hidden;\n}\n\n.product-details {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 1rem;\n  padding: 0.75rem;\n  background: #f8f9fa;\n  border-radius: 8px;\n}\n\n// Styles pour les prix (inspirés de la page admin)\n.price-container {\n  display: flex;\n  flex-direction: column;\n  gap: 0.25rem;\n  margin-bottom: 0.75rem;\n\n  .price-initial, .price-final {\n    display: flex;\n    align-items: center;\n    gap: 0.5rem;\n\n    .price-label {\n      font-size: 0.75rem;\n      color: #6c757d;\n      font-weight: 500;\n    }\n\n    .price-value {\n      font-weight: bold;\n\n      &.initial {\n        color: #6c757d;\n\n        &.crossed {\n          text-decoration: line-through;\n        }\n      }\n\n      &.final {\n        color: #007bff;\n        font-size: 1.1rem;\n      }\n    }\n  }\n\n  .discount-badge {\n    background: linear-gradient(135deg, #dc3545, #c82333);\n    color: white;\n    padding: 0.25rem 0.5rem;\n    border-radius: 12px;\n    font-size: 0.75rem;\n    font-weight: bold;\n    align-self: flex-start;\n    box-shadow: 0 2px 4px rgba(220, 53, 69, 0.3);\n  }\n}\n\n// Styles pour le stock\n.stock-container {\n  display: flex;\n  flex-direction: column;\n  gap: 0.25rem;\n\n  .stock-value {\n    font-size: 0.875rem;\n    color: #28a745;\n    font-weight: 500;\n\n    &.stock-critique {\n      color: #dc3545;\n      font-weight: bold;\n    }\n  }\n\n  .stock-alert {\n    font-size: 0.75rem;\n    color: #dc3545;\n    font-weight: bold;\n    background: rgba(220, 53, 69, 0.1);\n    padding: 0.125rem 0.25rem;\n    border-radius: 4px;\n  }\n}\n\n// Styles pour les métadonnées du produit\n.product-meta {\n  display: flex;\n  flex-direction: column;\n  gap: 0.25rem;\n  margin-bottom: 0.5rem;\n\n  .product-category {\n    font-size: 0.875rem;\n    color: #007bff;\n    font-weight: 500;\n  }\n\n  .product-brand {\n    font-size: 0.75rem;\n    color: #6c757d;\n    font-style: italic;\n  }\n\n  .product-reference {\n    font-size: 0.75rem;\n    color: #6c757d;\n\n    .ref-original {\n      font-weight: 500;\n    }\n\n    .ref-fournisseur {\n      color: #28a745;\n      margin-left: 0.25rem;\n    }\n  }\n}\n\n// Badge \"En avant\" (si on l'ajoute plus tard)\n.featured-badge {\n  position: absolute;\n  top: 0.5rem;\n  right: 0.5rem;\n  background: linear-gradient(135deg, #ffc107, #e0a800);\n  color: #212529;\n  padding: 0.25rem 0.5rem;\n  border-radius: 12px;\n  font-size: 0.75rem;\n  font-weight: bold;\n  box-shadow: 0 2px 4px rgba(255, 193, 7, 0.3);\n}\n\n.promotion-badge {\n    background: #dc3545;\n    color: white;\n    padding: 0.125rem 0.5rem;\n    border-radius: 12px;\n    font-size: 0.7rem;\n    font-weight: bold;\n    text-transform: uppercase;\n  }\n\n.product-stock {\n  color: #666;\n  font-weight: 500;\n}\n\n.product-actions {\n  display: flex;\n  gap: 0.5rem;\n  justify-content: flex-end;\n}\n\n.btn {\n  padding: 0.5rem 1rem;\n  border: none;\n  border-radius: 6px;\n  cursor: pointer;\n  font-size: 0.875rem;\n  font-weight: 500;\n  transition: all 0.2s ease;\n  text-decoration: none;\n  display: inline-flex;\n  align-items: center;\n  gap: 0.25rem;\n\n  &:hover {\n    transform: translateY(-1px);\n  }\n\n  &:active {\n    transform: translateY(0);\n  }\n\n  &:disabled {\n    opacity: 0.6;\n    cursor: not-allowed;\n    transform: none;\n  }\n}\n\n.btn-primary {\n  background: linear-gradient(135deg, #007bff, #0056b3);\n  color: white;\n  box-shadow: 0 2px 4px rgba(0, 123, 255, 0.3);\n\n  &:hover {\n    background: linear-gradient(135deg, #0056b3, #004085);\n    box-shadow: 0 4px 8px rgba(0, 123, 255, 0.4);\n  }\n}\n\n.btn-secondary {\n  background: #6c757d;\n  color: white;\n  box-shadow: 0 2px 4px rgba(108, 117, 125, 0.3);\n\n  &:hover {\n    background: #545b62;\n    box-shadow: 0 4px 8px rgba(108, 117, 125, 0.4);\n  }\n}\n\n.btn-danger {\n  background: linear-gradient(135deg, #dc3545, #c82333);\n  color: white;\n  box-shadow: 0 2px 4px rgba(220, 53, 69, 0.3);\n\n  &:hover {\n    background: linear-gradient(135deg, #c82333, #a71e2a);\n    box-shadow: 0 4px 8px rgba(220, 53, 69, 0.4);\n  }\n}\n\n.btn-outline-primary {\n  background: transparent;\n  color: #007bff;\n  border: 2px solid #007bff;\n\n  &:hover {\n    background: #007bff;\n    color: white;\n  }\n}\n\n.btn-outline-secondary {\n  background: transparent;\n  color: #6c757d;\n  border: 2px solid #6c757d;\n\n  &:hover {\n    background: #6c757d;\n    color: white;\n  }\n}\n\n.btn-sm {\n  padding: 0.25rem 0.5rem;\n  font-size: 0.75rem;\n}\n\n.btn-info {\n  background: linear-gradient(135deg, #17a2b8, #138496);\n  color: white;\n  box-shadow: 0 2px 4px rgba(23, 162, 184, 0.3);\n\n  &:hover {\n    background: linear-gradient(135deg, #138496, #117a8b);\n    box-shadow: 0 4px 8px rgba(23, 162, 184, 0.4);\n  }\n}\n\n.loading {\n  text-align: center;\n  padding: 4rem 2rem;\n  color: #666;\n\n  .spinner {\n    width: 40px;\n    height: 40px;\n    border: 4px solid #f3f3f3;\n    border-top: 4px solid #007bff;\n    border-radius: 50%;\n    animation: spin 1s linear infinite;\n    margin: 0 auto 1rem;\n  }\n\n  p {\n    font-size: 1.1rem;\n    margin: 0;\n  }\n}\n\n@keyframes spin {\n  0% { transform: rotate(0deg); }\n  100% { transform: rotate(360deg); }\n}\n\n.no-products {\n  text-align: center;\n  padding: 4rem 2rem;\n  background: white;\n  border-radius: 12px;\n  box-shadow: 0 2px 8px rgba(0,0,0,0.1);\n\n  h3 {\n    color: #333;\n    margin-bottom: 1rem;\n    font-size: 1.5rem;\n  }\n\n  p {\n    color: #666;\n    margin-bottom: 2rem;\n    font-size: 1.1rem;\n  }\n}\n\n// Modal styles\n.modal-overlay {\n  position: fixed;\n  top: 0;\n  left: 0;\n  right: 0;\n  bottom: 0;\n  background: rgba(0, 0, 0, 0.5);\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  z-index: 1000;\n  padding: 1rem;\n}\n\n.modal-content {\n  background: white;\n  border-radius: 12px;\n  width: 100%;\n  max-width: 800px;\n  max-height: 90vh;\n  overflow-y: auto;\n  box-shadow: 0 10px 30px rgba(0,0,0,0.3);\n}\n\n.modal-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  padding: 1.5rem;\n  border-bottom: 1px solid #e9ecef;\n\n  h3 {\n    margin: 0;\n    color: #333;\n    font-size: 1.5rem;\n  }\n}\n\n.modal-actions {\n  display: flex;\n  gap: 0.5rem;\n}\n\n.product-form {\n  padding: 1.5rem;\n}\n\n.form-section {\n  margin-bottom: 2rem;\n  padding: 1.5rem;\n  background: #f8f9fa;\n  border-radius: 8px;\n  border: 1px solid #e9ecef;\n\n  h4 {\n    margin: 0 0 1rem 0;\n    color: #333;\n    font-size: 1.2rem;\n    font-weight: 600;\n    padding-bottom: 0.5rem;\n    border-bottom: 2px solid #007bff;\n  }\n}\n\n.form-row {\n  display: grid;\n  grid-template-columns: 1fr 1fr;\n  gap: 1rem;\n  margin-bottom: 1rem;\n\n  @media (max-width: 768px) {\n    grid-template-columns: 1fr;\n  }\n}\n\n.form-group {\n  margin-bottom: 1rem;\n\n  label {\n    display: block;\n    margin-bottom: 0.5rem;\n    color: #333;\n    font-weight: 500;\n    font-size: 0.9rem;\n  }\n}\n\n.form-control {\n  width: 100%;\n  padding: 0.75rem;\n  border: 2px solid #e9ecef;\n  border-radius: 6px;\n  font-size: 1rem;\n  transition: border-color 0.2s ease;\n\n  &:focus {\n    outline: none;\n    border-color: #007bff;\n    box-shadow: 0 0 0 3px rgba(0, 123, 255, 0.1);\n  }\n\n  &.ng-invalid.ng-touched {\n    border-color: #dc3545;\n  }\n}\n\n.input-with-button {\n  display: flex;\n  gap: 0.5rem;\n\n  .form-control {\n    flex: 1;\n  }\n\n  .btn {\n    flex-shrink: 0;\n  }\n}\n\n.form-text {\n  font-size: 0.875rem;\n  color: #6c757d;\n  margin-top: 0.25rem;\n}\n\n.text-muted {\n  color: #6c757d;\n}\n\n.price-info {\n  background: #e7f3ff;\n  border: 1px solid #b3d9ff;\n  border-radius: 6px;\n  padding: 1rem;\n  margin-top: 0.5rem;\n\n  strong {\n    color: #0056b3;\n    font-size: 1.1rem;\n  }\n\n  small {\n    display: block;\n    margin-top: 0.25rem;\n  }\n}\n\n.form-actions {\n  display: flex;\n  justify-content: flex-end;\n  gap: 1rem;\n  padding: 1.5rem;\n  border-top: 1px solid #e9ecef;\n  background: #f8f9fa;\n  margin: 0 -1.5rem -1.5rem -1.5rem;\n  border-radius: 0 0 12px 12px;\n}\n\n// Styles pour la gestion des images\n.existing-images {\n  margin-bottom: 1.5rem;\n  padding: 1rem;\n  background: #e8f5e8;\n  border-radius: 8px;\n  border: 1px solid #c3e6cb;\n\n  h6 {\n    margin: 0 0 0.75rem 0;\n    color: #155724;\n    font-weight: 600;\n  }\n}\n\n// Styles pour l'upload d'images\n.image-upload-actions {\n  display: flex;\n  align-items: center;\n  gap: 10px;\n  margin-bottom: 10px;\n\n  .btn {\n    display: flex;\n    align-items: center;\n    gap: 5px;\n\n    &:disabled {\n      opacity: 0.6;\n      cursor: not-allowed;\n    }\n  }\n\n  .form-text {\n    margin: 0;\n    font-size: 0.875rem;\n  }\n}\n\n.image-preview-container {\n  display: flex;\n  flex-wrap: wrap;\n  gap: 1rem;\n  margin-top: 1rem;\n}\n\n.image-preview {\n  position: relative;\n  width: 120px;\n  height: 120px;\n  border-radius: 8px;\n  overflow: hidden;\n  box-shadow: 0 2px 8px rgba(0,0,0,0.1);\n\n  &.existing {\n    border: 2px solid #28a745;\n    opacity: 0.9;\n  }\n\n  &.new {\n    border: 2px solid #007bff;\n  }\n\n  img {\n    width: 100%;\n    height: 100%;\n    object-fit: cover;\n  }\n}\n\n.image-info {\n  position: absolute;\n  bottom: 0;\n  left: 0;\n  right: 0;\n  background: linear-gradient(transparent, rgba(0, 0, 0, 0.8));\n  color: white;\n  padding: 0.5rem 0.25rem 0.25rem;\n  font-size: 0.75rem;\n\n  .image-name {\n    display: block;\n    white-space: nowrap;\n    overflow: hidden;\n    text-overflow: ellipsis;\n    font-weight: 500;\n  }\n\n  .image-main {\n    color: #ffc107;\n    font-weight: bold;\n    font-size: 0.7rem;\n  }\n}\n\n.btn-remove {\n  position: absolute;\n  top: 0.25rem;\n  right: 0.25rem;\n  background: rgba(220, 53, 69, 0.9);\n  color: white;\n  border: none;\n  border-radius: 50%;\n  width: 24px;\n  height: 24px;\n  cursor: pointer;\n  font-size: 16px;\n  line-height: 1;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  transition: background-color 0.2s ease;\n\n  &:hover {\n    background: rgba(220, 53, 69, 1);\n  }\n}\n\n// Responsive design\n@media (max-width: 768px) {\n  .products-container {\n    padding: 1rem;\n  }\n\n  .products-header {\n    flex-direction: column;\n    gap: 1rem;\n    text-align: center;\n\n    h1 {\n      font-size: 1.5rem;\n    }\n  }\n\n  .stats-grid {\n    grid-template-columns: 1fr 1fr;\n  }\n\n  .products-grid {\n    grid-template-columns: 1fr;\n  }\n\n  .product-actions {\n    flex-direction: column;\n  }\n\n  .modal-content {\n    margin: 0.5rem;\n    max-height: 95vh;\n  }\n\n  .form-actions {\n    flex-direction: column;\n  }\n}\n\n// Styles pour le modal des détails\n.details-modal {\n  max-width: 900px;\n}\n\n.product-details-content {\n  padding: 1.5rem;\n}\n\n.details-grid {\n  display: grid;\n  grid-template-columns: 1fr 1fr;\n  gap: 2rem;\n  margin-bottom: 2rem;\n\n  @media (max-width: 768px) {\n    grid-template-columns: 1fr;\n  }\n}\n\n.detail-section {\n  background: #f8f9fa;\n  padding: 1.5rem;\n  border-radius: 8px;\n  border: 1px solid #e9ecef;\n\n  h4 {\n    margin: 0 0 1rem 0;\n    color: #333;\n    font-size: 1.1rem;\n    font-weight: 600;\n    padding-bottom: 0.5rem;\n    border-bottom: 2px solid #007bff;\n  }\n}\n\n.detail-item {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  padding: 0.5rem 0;\n  border-bottom: 1px solid #e9ecef;\n\n  &:last-child {\n    border-bottom: none;\n  }\n\n  label {\n    font-weight: 600;\n    color: #555;\n    margin-right: 1rem;\n  }\n\n  span {\n    color: #333;\n    text-align: right;\n    flex: 1;\n  }\n}\n\n.promotion-price {\n  color: #dc3545;\n  font-weight: bold;\n}\n\n.images-gallery {\n  display: grid;\n  grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));\n  gap: 1rem;\n}\n\n.image-item {\n  position: relative;\n  border-radius: 8px;\n  overflow: hidden;\n  box-shadow: 0 2px 8px rgba(0,0,0,0.1);\n\n  img {\n    width: 100%;\n    height: 120px;\n    object-fit: cover;\n  }\n\n  .main-badge {\n    position: absolute;\n    top: 0.25rem;\n    right: 0.25rem;\n    background: #ffc107;\n    color: #333;\n    padding: 0.125rem 0.5rem;\n    border-radius: 12px;\n    font-size: 0.7rem;\n    font-weight: bold;\n  }\n}\n\n.details-actions {\n  display: flex;\n  justify-content: flex-end;\n  gap: 1rem;\n  padding: 1.5rem;\n  border-top: 1px solid #e9ecef;\n  background: #f8f9fa;\n  margin: 0 -1.5rem -1.5rem -1.5rem;\n  border-radius: 0 0 12px 12px;\n}\n\n/* Nouveaux styles inspirés du stock dépôt */\n.page-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 32px;\n  padding: 24px;\n  background: white;\n  border-radius: 12px;\n  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);\n\n  .header-content h1 {\n    margin: 0 0 8px 0;\n    font-size: 28px;\n    font-weight: 700;\n    color: #1f2937;\n  }\n\n  .header-content p {\n    margin: 0;\n    color: #6b7280;\n    font-size: 16px;\n  }\n}\n\n.stats-section {\n  margin-bottom: 32px;\n\n  .stats-grid {\n    display: grid;\n    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));\n    gap: 20px;\n  }\n\n  .stat-card {\n    display: flex;\n    align-items: center;\n    gap: 16px;\n    padding: 20px;\n    background: white;\n    border-radius: 12px;\n    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);\n    transition: transform 0.2s ease;\n\n    &:hover {\n      transform: translateY(-2px);\n    }\n\n    &.alert {\n      background: #fef2f2;\n      border-left: 4px solid #ef4444;\n    }\n\n    .stat-icon {\n      font-size: 32px;\n      width: 48px;\n      height: 48px;\n      display: flex;\n      align-items: center;\n      justify-content: center;\n      background: #f3f4f6;\n      border-radius: 50%;\n    }\n\n    .stat-value {\n      font-size: 24px;\n      font-weight: 700;\n      color: #1f2937;\n      margin-bottom: 4px;\n    }\n\n    .stat-label {\n      font-size: 14px;\n      color: #6b7280;\n      font-weight: 500;\n    }\n  }\n}\n\n.toolbar {\n  margin-bottom: 24px;\n  padding: 20px;\n  background: white;\n  border-radius: 12px;\n  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);\n\n  .search-section {\n    display: flex;\n    align-items: center;\n    gap: 24px;\n    flex-wrap: wrap;\n  }\n\n  .search-box {\n    position: relative;\n    flex: 1;\n    min-width: 300px;\n\n    input {\n      width: 100%;\n      padding: 12px 16px 12px 40px;\n      border: 1px solid #d1d5db;\n      border-radius: 8px;\n      font-size: 14px;\n      background: #f9fafb;\n\n      &:focus {\n        outline: none;\n        border-color: #3b82f6;\n        background: white;\n      }\n    }\n\n    .search-icon {\n      position: absolute;\n      left: 12px;\n      top: 50%;\n      transform: translateY(-50%);\n      color: #6b7280;\n      font-size: 16px;\n    }\n  }\n\n  .filters {\n    display: flex;\n    gap: 16px;\n    flex-wrap: wrap;\n\n    .filter-checkbox {\n      display: flex;\n      align-items: center;\n      gap: 8px;\n      cursor: pointer;\n      font-size: 14px;\n      color: #374151;\n\n      input[type=\"checkbox\"] {\n        width: 16px;\n        height: 16px;\n        accent-color: #3b82f6;\n      }\n    }\n  }\n}\n\n/* Classes de niveau de stock */\n.stock-empty {\n  color: #dc2626;\n  font-weight: 700;\n}\n\n.stock-low {\n  color: #f59e0b;\n  font-weight: 600;\n}\n\n.stock-medium {\n  color: #3b82f6;\n  font-weight: 500;\n}\n\n.stock-ok {\n  color: #059669;\n  font-weight: 500;\n}\n"], "mappings": ";;;AAAA,CAAA;AACE,WAAA;AACA,aAAA;AACA,UAAA,EAAA;AACA,cAAA;AACA,cAAA;;AAGF,CAAA;AACE,WAAA;AACA,mBAAA;AACA,eAAA;AACA,iBAAA;AACA,cAAA;AACA,WAAA;AACA,iBAAA;AACA,cAAA,EAAA,IAAA,IAAA,KAAA,CAAA,EAAA,CAAA,EAAA,CAAA,EAAA;;AAEA,CAVF,gBAUE;AACE,UAAA;AACA,SAAA;AACA,aAAA;AACA,eAAA;;AAIJ,CAAA;AACE,WAAA;AACA,yBAAA,OAAA,QAAA,EAAA,OAAA,KAAA,EAAA;AACA,OAAA;AACA,iBAAA;;AAGF,CAAA;AACE,cAAA;AACA,WAAA;AACA,iBAAA;AACA,cAAA,EAAA,IAAA,IAAA,KAAA,CAAA,EAAA,CAAA,EAAA,CAAA,EAAA;AACA,cAAA;AACA,cAAA,UAAA,KAAA;;AAEA,CARF,SAQE;AACE,aAAA,WAAA;;AAIJ,CAAA;AACE,aAAA;AACA,eAAA;AACA,SAAA;AACA,iBAAA;;AAGF,CAAA;AACE,SAAA;AACA,aAAA;AACA,kBAAA;AACA,kBAAA;;AAGF,CAAA;AACE,iBAAA;;AAGF,CAAA;AACE,SAAA;AACA,aAAA;AACA,WAAA,QAAA;AACA,UAAA,IAAA,MAAA;AACA,iBAAA;AACA,aAAA;AACA,cAAA,aAAA,KAAA;;AAEA,CATF,YASE;AACE,WAAA;AACA,gBAAA;AACA,cAAA,EAAA,EAAA,EAAA,IAAA,KAAA,CAAA,EAAA,GAAA,EAAA,GAAA,EAAA;;AAIJ,CAAA;AACE,WAAA;AACA,yBAAA,OAAA,SAAA,EAAA,OAAA,KAAA,EAAA;AACA,OAAA;AACA,iBAAA;;AAGF,CAAA;AACE,cAAA;AACA,iBAAA;AACA,WAAA;AACA,cAAA,EAAA,IAAA,IAAA,KAAA,CAAA,EAAA,CAAA,EAAA,CAAA,EAAA;AACA,cAAA,IAAA,KAAA;AACA,UAAA,IAAA,MAAA;AACA,UAAA;;AAEA,CATF,YASE;AACE,aAAA,WAAA;AACA,cAAA,EAAA,IAAA,KAAA,KAAA,CAAA,EAAA,CAAA,EAAA,CAAA,EAAA;;AAIJ,CAAA;AACE,WAAA;AACA,mBAAA;AACA,eAAA;AACA,iBAAA;;AAEA,CANF,eAME;AACE,UAAA;AACA,SAAA;AACA,aAAA;AACA,eAAA;;AAIJ,CAAA;AACE,WAAA,QAAA;AACA,iBAAA;AACA,aAAA;AACA,eAAA;AACA,kBAAA;AACA,kBAAA;;AAEA,CARF,cAQE,CAAA;AACE,cAAA;AACA,SAAA;AACA,UAAA,IAAA,MAAA;;AAGF,CAdF,cAcE,CAAA;AACE,cAAA;AACA,SAAA;AACA,UAAA,IAAA,MAAA;;AAGF,CApBF,cAoBE,CAAA;AACE,cAAA;AACA,SAAA;AACA,UAAA,IAAA,MAAA;;AAIJ,CAAA;AACE,SAAA;AACA,iBAAA;AACA,eAAA;AACA,WAAA;AACA,sBAAA;AACA,sBAAA;AACA,YAAA;;AAGF,CAAA;AACE,WAAA;AACA,mBAAA;AACA,eAAA;AACA,iBAAA;AACA,WAAA;AACA,cAAA;AACA,iBAAA;;AAIF,CAAA;AACE,WAAA;AACA,kBAAA;AACA,OAAA;AACA,iBAAA;;AAEA,CANF,gBAME,CAAA;AAAA,CANF,gBAME,CAAA;AACE,WAAA;AACA,eAAA;AACA,OAAA;;AAEA,CAXJ,gBAWI,CALF,cAKE,CAAA;AAAA,CAXJ,gBAWI,CALF,YAKE,CAAA;AACE,aAAA;AACA,SAAA;AACA,eAAA;;AAGF,CAjBJ,gBAiBI,CAXF,cAWE,CAAA;AAAA,CAjBJ,gBAiBI,CAXF,YAWE,CAAA;AACE,eAAA;;AAEA,CApBN,gBAoBM,CAdJ,cAcI,CAHF,WAGE,CAAA;AAAA,CApBN,gBAoBM,CAdJ,YAcI,CAHF,WAGE,CAAA;AACE,SAAA;;AAEA,CAvBR,gBAuBQ,CAjBN,cAiBM,CANJ,WAMI,CAHF,OAGE,CAAA;AAAA,CAvBR,gBAuBQ,CAjBN,YAiBM,CANJ,WAMI,CAHF,OAGE,CAAA;AACE,mBAAA;;AAIJ,CA5BN,gBA4BM,CAtBJ,cAsBI,CAXF,WAWE,CAAA;AAAA,CA5BN,gBA4BM,CAtBJ,YAsBI,CAXF,WAWE,CAAA;AACE,SAAA;AACA,aAAA;;AAKN,CAnCF,gBAmCE,CAAA;AACE;IAAA;MAAA,MAAA;MAAA,OAAA;MAAA;AACA,SAAA;AACA,WAAA,QAAA;AACA,iBAAA;AACA,aAAA;AACA,eAAA;AACA,cAAA;AACA,cAAA,EAAA,IAAA,IAAA,KAAA,GAAA,EAAA,EAAA,EAAA,EAAA,EAAA;;AAKJ,CAAA;AACE,WAAA;AACA,kBAAA;AACA,OAAA;;AAEA,CALF,gBAKE,CAAA;AACE,aAAA;AACA,SAAA;AACA,eAAA;;AAEA,CAVJ,gBAUI,CALF,WAKE,CAAA;AACE,SAAA;AACA,eAAA;;AAIJ,CAhBF,gBAgBE,CAAA;AACE,aAAA;AACA,SAAA;AACA,eAAA;AACA,cAAA,KAAA,GAAA,EAAA,EAAA,EAAA,EAAA,EAAA;AACA,WAAA,SAAA;AACA,iBAAA;;AAKJ,CAAA;AACE,WAAA;AACA,kBAAA;AACA,OAAA;AACA,iBAAA;;AAEA,CANF,aAME,CAAA;AACE,aAAA;AACA,SAAA;AACA,eAAA;;AAGF,CAZF,aAYE,CAAA;AACE,aAAA;AACA,SAAA;AACA,cAAA;;AAGF,CAlBF,aAkBE,CAAA;AACE,aAAA;AACA,SAAA;;AAEA,CAtBJ,aAsBI,CAJF,kBAIE,CAAA;AACE,eAAA;;AAGF,CA1BJ,aA0BI,CARF,kBAQE,CAAA;AACE,SAAA;AACA,eAAA;;AAMN,CAAA;AACE,YAAA;AACA,OAAA;AACA,SAAA;AACA;IAAA;MAAA,MAAA;MAAA,OAAA;MAAA;AACA,SAAA;AACA,WAAA,QAAA;AACA,iBAAA;AACA,aAAA;AACA,eAAA;AACA,cAAA,EAAA,IAAA,IAAA,KAAA,GAAA,EAAA,GAAA,EAAA,CAAA,EAAA;;AAGF,CAAA;AACI,cAAA;AACA,SAAA;AACA,WAAA,SAAA;AACA,iBAAA;AACA,aAAA;AACA,eAAA;AACA,kBAAA;;AAGJ,CAAA;AACE,SAAA;AACA,eAAA;;AAGF,CAAA;AACE,WAAA;AACA,OAAA;AACA,mBAAA;;AAGF,CAAA;AACE,WAAA,OAAA;AACA,UAAA;AACA,iBAAA;AACA,UAAA;AACA,aAAA;AACA,eAAA;AACA,cAAA,IAAA,KAAA;AACA,mBAAA;AACA,WAAA;AACA,eAAA;AACA,OAAA;;AAEA,CAbF,GAaE;AACE,aAAA,WAAA;;AAGF,CAjBF,GAiBE;AACE,aAAA,WAAA;;AAGF,CArBF,GAqBE;AACE,WAAA;AACA,UAAA;AACA,aAAA;;AAIJ,CAAA;AACE;IAAA;MAAA,MAAA;MAAA,OAAA;MAAA;AACA,SAAA;AACA,cAAA,EAAA,IAAA,IAAA,KAAA,CAAA,EAAA,GAAA,EAAA,GAAA,EAAA;;AAEA,CALF,WAKE;AACE;IAAA;MAAA,MAAA;MAAA,OAAA;MAAA;AACA,cAAA,EAAA,IAAA,IAAA,KAAA,CAAA,EAAA,GAAA,EAAA,GAAA,EAAA;;AAIJ,CAAA;AACE,cAAA;AACA,SAAA;AACA,cAAA,EAAA,IAAA,IAAA,KAAA,GAAA,EAAA,GAAA,EAAA,GAAA,EAAA;;AAEA,CALF,aAKE;AACE,cAAA;AACA,cAAA,EAAA,IAAA,IAAA,KAAA,GAAA,EAAA,GAAA,EAAA,GAAA,EAAA;;AAIJ,CAAA;AACE;IAAA;MAAA,MAAA;MAAA,OAAA;MAAA;AACA,SAAA;AACA,cAAA,EAAA,IAAA,IAAA,KAAA,GAAA,EAAA,EAAA,EAAA,EAAA,EAAA;;AAEA,CALF,UAKE;AACE;IAAA;MAAA,MAAA;MAAA,OAAA;MAAA;AACA,cAAA,EAAA,IAAA,IAAA,KAAA,GAAA,EAAA,EAAA,EAAA,EAAA,EAAA;;AAIJ,CAAA;AACE,cAAA;AACA,SAAA;AACA,UAAA,IAAA,MAAA;;AAEA,CALF,mBAKE;AACE,cAAA;AACA,SAAA;;AAIJ,CAAA;AACE,cAAA;AACA,SAAA;AACA,UAAA,IAAA,MAAA;;AAEA,CALF,qBAKE;AACE,cAAA;AACA,SAAA;;AAIJ,CAAA;AACE,WAAA,QAAA;AACA,aAAA;;AAGF,CAAA;AACE;IAAA;MAAA,MAAA;MAAA,OAAA;MAAA;AACA,SAAA;AACA,cAAA,EAAA,IAAA,IAAA,KAAA,EAAA,EAAA,GAAA,EAAA,GAAA,EAAA;;AAEA,CALF,QAKE;AACE;IAAA;MAAA,MAAA;MAAA,OAAA;MAAA;AACA,cAAA,EAAA,IAAA,IAAA,KAAA,EAAA,EAAA,GAAA,EAAA,GAAA,EAAA;;AAIJ,CAAA;AACE,cAAA;AACA,WAAA,KAAA;AACA,SAAA;;AAEA,CALF,QAKE,CAAA;AACE,SAAA;AACA,UAAA;AACA,UAAA,IAAA,MAAA;AACA,cAAA,IAAA,MAAA;AACA,iBAAA;AACA,aAAA,KAAA,GAAA,OAAA;AACA,UAAA,EAAA,KAAA;;AAGF,CAfF,QAeE;AACE,aAAA;AACA,UAAA;;AAIJ,WAVI;AAWF;AAAK,eAAA,OAAA;;AACL;AAAO,eAAA,OAAA;;;AAGT,CAAA;AACE,cAAA;AACA,WAAA,KAAA;AACA,cAAA;AACA,iBAAA;AACA,cAAA,EAAA,IAAA,IAAA,KAAA,CAAA,EAAA,CAAA,EAAA,CAAA,EAAA;;AAEA,CAPF,YAOE;AACE,SAAA;AACA,iBAAA;AACA,aAAA;;AAGF,CAbF,YAaE;AACE,SAAA;AACA,iBAAA;AACA,aAAA;;AAKJ,CAAA;AACE,YAAA;AACA,OAAA;AACA,QAAA;AACA,SAAA;AACA,UAAA;AACA,cAAA,KAAA,CAAA,EAAA,CAAA,EAAA,CAAA,EAAA;AACA,WAAA;AACA,eAAA;AACA,mBAAA;AACA,WAAA;AACA,WAAA;;AAGF,CAAA;AACE,cAAA;AACA,iBAAA;AACA,SAAA;AACA,aAAA;AACA,cAAA;AACA,cAAA;AACA,cAAA,EAAA,KAAA,KAAA,KAAA,CAAA,EAAA,CAAA,EAAA,CAAA,EAAA;;AAGF,CAAA;AACE,WAAA;AACA,mBAAA;AACA,eAAA;AACA,WAAA;AACA,iBAAA,IAAA,MAAA;;AAEA,CAPF,aAOE;AACE,UAAA;AACA,SAAA;AACA,aAAA;;AAIJ,CAAA;AACE,WAAA;AACA,OAAA;;AAGF,CAAA;AACE,WAAA;;AAGF,CAAA;AACE,iBAAA;AACA,WAAA;AACA,cAAA;AACA,iBAAA;AACA,UAAA,IAAA,MAAA;;AAEA,CAPF,aAOE;AACE,UAAA,EAAA,EAAA,KAAA;AACA,SAAA;AACA,aAAA;AACA,eAAA;AACA,kBAAA;AACA,iBAAA,IAAA,MAAA;;AAIJ,CAAA;AACE,WAAA;AACA,yBAAA,IAAA;AACA,OAAA;AACA,iBAAA;;AAEA,OAAA,CAAA,SAAA,EAAA;AANF,GAAA;AAOI,2BAAA;;;AAIJ,CAAA;AACE,iBAAA;;AAEA,CAHF,WAGE;AACE,WAAA;AACA,iBAAA;AACA,SAAA;AACA,eAAA;AACA,aAAA;;AAIJ,CAAA;AACE,SAAA;AACA,WAAA;AACA,UAAA,IAAA,MAAA;AACA,iBAAA;AACA,aAAA;AACA,cAAA,aAAA,KAAA;;AAEA,CARF,YAQE;AACE,WAAA;AACA,gBAAA;AACA,cAAA,EAAA,EAAA,EAAA,IAAA,KAAA,CAAA,EAAA,GAAA,EAAA,GAAA,EAAA;;AAGF,CAdF,YAcE,CAAA,UAAA,CAAA;AACE,gBAAA;;AAIJ,CAAA;AACE,WAAA;AACA,OAAA;;AAEA,CAJF,kBAIE,CAvBF;AAwBI,QAAA;;AAGF,CARF,kBAQE,CApQF;AAqQI,eAAA;;AAIJ,CAAA;AACE,aAAA;AACA,SAAA;AACA,cAAA;;AAGF,CAAA;AACE,SAAA;;AAGF,CAAA;AACE,cAAA;AACA,UAAA,IAAA,MAAA;AACA,iBAAA;AACA,WAAA;AACA,cAAA;;AAEA,CAPF,WAOE;AACE,SAAA;AACA,aAAA;;AAGF,CAZF,WAYE;AACE,WAAA;AACA,cAAA;;AAIJ,CAAA;AACE,WAAA;AACA,mBAAA;AACA,OAAA;AACA,WAAA;AACA,cAAA,IAAA,MAAA;AACA,cAAA;AACA,UAAA,EAAA,QAAA,QAAA;AACA,iBAAA,EAAA,EAAA,KAAA;;AAIF,CAAA;AACE,iBAAA;AACA,WAAA;AACA,cAAA;AACA,iBAAA;AACA,UAAA,IAAA,MAAA;;AAEA,CAPF,gBAOE;AACE,UAAA,EAAA,EAAA,QAAA;AACA,SAAA;AACA,eAAA;;AAKJ,CAAA;AACE,WAAA;AACA,eAAA;AACA,OAAA;AACA,iBAAA;;AAEA,CANF,qBAME,CAtUF;AAuUI,WAAA;AACA,eAAA;AACA,OAAA;;AAEA,CAXJ,qBAWI,CA3UJ,GA2UI;AACE,WAAA;AACA,UAAA;;AAIJ,CAjBF,qBAiBE,CAxEF;AAyEI,UAAA;AACA,aAAA;;AAIJ,CAAA;AACE,WAAA;AACA,aAAA;AACA,OAAA;AACA,cAAA;;AAGF,CAAA;AACE,YAAA;AACA,SAAA;AACA,UAAA;AACA,iBAAA;AACA,YAAA;AACA,cAAA,EAAA,IAAA,IAAA,KAAA,CAAA,EAAA,CAAA,EAAA,CAAA,EAAA;;AAEA,CARF,aAQE,CAAA;AACE,UAAA,IAAA,MAAA;AACA,WAAA;;AAGF,CAbF,aAaE,CAAA;AACE,UAAA,IAAA,MAAA;;AAGF,CAjBF,cAiBE;AACE,SAAA;AACA,UAAA;AACA,cAAA;;AAIJ,CAAA;AACE,YAAA;AACA,UAAA;AACA,QAAA;AACA,SAAA;AACA,cAAA,gBAAA,WAAA,EAAA,KAAA,CAAA,EAAA,CAAA,EAAA,CAAA,EAAA;AACA,SAAA;AACA,WAAA,OAAA,QAAA;AACA,aAAA;;AAEA,CAVF,WAUE,CAAA;AACE,WAAA;AACA,eAAA;AACA,YAAA;AACA,iBAAA;AACA,eAAA;;AAGF,CAlBF,WAkBE,CAAA;AACE,SAAA;AACA,eAAA;AACA,aAAA;;AAIJ,CAAA;AACE,YAAA;AACA,OAAA;AACA,SAAA;AACA,cAAA,KAAA,GAAA,EAAA,EAAA,EAAA,EAAA,EAAA;AACA,SAAA;AACA,UAAA;AACA,iBAAA;AACA,SAAA;AACA,UAAA;AACA,UAAA;AACA,aAAA;AACA,eAAA;AACA,WAAA;AACA,eAAA;AACA,mBAAA;AACA,cAAA,iBAAA,KAAA;;AAEA,CAlBF,UAkBE;AACE,cAAA,IAAA,GAAA,EAAA,EAAA,EAAA;;AAKJ,OAAA,CAAA,SAAA,EAAA;AACE,GA3tBF;AA4tBI,aAAA;;AAGF,GAvtBF;AAwtBI,oBAAA;AACA,SAAA;AACA,gBAAA;;AAEA,GA5tBJ,gBA4tBI;AACE,eAAA;;AAIJ,GA/sBF;AAgtBI,2BAAA,IAAA;;AAGF,GA7pBF;AA8pBI,2BAAA;;AAGF,GApcF;AAqcI,oBAAA;;AAGF,GAlSF;AAmSI,YAAA;AACA,gBAAA;;AAGF,GAlKF;AAmKI,oBAAA;;;AAKJ,CAAA;AACE,aAAA;;AAGF,CAAA;AACE,WAAA;;AAGF,CAAA;AACE,WAAA;AACA,yBAAA,IAAA;AACA,OAAA;AACA,iBAAA;;AAEA,OAAA,CAAA,SAAA,EAAA;AANF,GAAA;AAOI,2BAAA;;;AAIJ,CAAA;AACE,cAAA;AACA,WAAA;AACA,iBAAA;AACA,UAAA,IAAA,MAAA;;AAEA,CANF,eAME;AACE,UAAA,EAAA,EAAA,KAAA;AACA,SAAA;AACA,aAAA;AACA,eAAA;AACA,kBAAA;AACA,iBAAA,IAAA,MAAA;;AAIJ,CAAA;AACE,WAAA;AACA,mBAAA;AACA,eAAA;AACA,WAAA,OAAA;AACA,iBAAA,IAAA,MAAA;;AAEA,CAPF,WAOE;AACE,iBAAA;;AAGF,CAXF,YAWE;AACE,eAAA;AACA,SAAA;AACA,gBAAA;;AAGF,CAjBF,YAiBE;AACE,SAAA;AACA,cAAA;AACA,QAAA;;AAIJ,CAAA;AACE,SAAA;AACA,eAAA;;AAGF,CAAA;AACE,WAAA;AACA,yBAAA,OAAA,SAAA,EAAA,OAAA,KAAA,EAAA;AACA,OAAA;;AAGF,CAAA;AACE,YAAA;AACA,iBAAA;AACA,YAAA;AACA,cAAA,EAAA,IAAA,IAAA,KAAA,CAAA,EAAA,CAAA,EAAA,CAAA,EAAA;;AAEA,CANF,WAME;AACE,SAAA;AACA,UAAA;AACA,cAAA;;AAGF,CAZF,WAYE,CAAA;AACE,YAAA;AACA,OAAA;AACA,SAAA;AACA,cAAA;AACA,SAAA;AACA,WAAA,SAAA;AACA,iBAAA;AACA,aAAA;AACA,eAAA;;AAIJ,CAAA;AACE,WAAA;AACA,mBAAA;AACA,OAAA;AACA,WAAA;AACA,cAAA,IAAA,MAAA;AACA,cAAA;AACA,UAAA,EAAA,QAAA,QAAA;AACA,iBAAA,EAAA,EAAA,KAAA;;AAIF,CAAA;AACE,WAAA;AACA,mBAAA;AACA,eAAA;AACA,iBAAA;AACA,WAAA;AACA,cAAA;AACA,iBAAA;AACA,cAAA,EAAA,IAAA,IAAA,KAAA,CAAA,EAAA,CAAA,EAAA,CAAA,EAAA;;AAEA,CAVF,YAUE,CAAA,eAAA;AACE,UAAA,EAAA,EAAA,IAAA;AACA,aAAA;AACA,eAAA;AACA,SAAA;;AAGF,CAjBF,YAiBE,CAPA,eAOA;AACE,UAAA;AACA,SAAA;AACA,aAAA;;AAIJ,CAAA;AACE,iBAAA;;AAEA,CAHF,cAGE,CA52BF;AA62BI,WAAA;AACA,yBAAA,OAAA,QAAA,EAAA,OAAA,KAAA,EAAA;AACA,OAAA;;AAGF,CATF,cASE,CA32BF;AA42BI,WAAA;AACA,eAAA;AACA,OAAA;AACA,WAAA;AACA,cAAA;AACA,iBAAA;AACA,cAAA,EAAA,IAAA,IAAA,KAAA,CAAA,EAAA,CAAA,EAAA,CAAA,EAAA;AACA,cAAA,UAAA,KAAA;;AAEA,CAnBJ,cAmBI,CAr3BJ,SAq3BI;AACE,aAAA,WAAA;;AAGF,CAvBJ,cAuBI,CAz3BJ,SAy3BI,CAAA;AACE,cAAA;AACA,eAAA,IAAA,MAAA;;AAGF,CA5BJ,cA4BI,CA93BJ,UA83BI,CAAA;AACE,aAAA;AACA,SAAA;AACA,UAAA;AACA,WAAA;AACA,eAAA;AACA,mBAAA;AACA,cAAA;AACA,iBAAA;;AAGF,CAvCJ,cAuCI,CAz4BJ,UAy4BI,CAAA;AACE,aAAA;AACA,eAAA;AACA,SAAA;AACA,iBAAA;;AAGF,CA9CJ,cA8CI,CAh5BJ,UAg5BI,CA53BJ;AA63BM,aAAA;AACA,SAAA;AACA,eAAA;;AAKN,CAAA;AACE,iBAAA;AACA,WAAA;AACA,cAAA;AACA,iBAAA;AACA,cAAA,EAAA,IAAA,IAAA,KAAA,CAAA,EAAA,CAAA,EAAA,CAAA,EAAA;;AAEA,CAPF,QAOE,CAAA;AACE,WAAA;AACA,eAAA;AACA,OAAA;AACA,aAAA;;AAGF,CAdF,QAcE,CA34BF;AA44BI,YAAA;AACA,QAAA;AACA,aAAA;;AAEA,CAnBJ,QAmBI,CAh5BJ,WAg5BI;AACE,SAAA;AACA,WAAA,KAAA,KAAA,KAAA;AACA,UAAA,IAAA,MAAA;AACA,iBAAA;AACA,aAAA;AACA,cAAA;;AAEA,CA3BN,QA2BM,CAx5BN,WAw5BM,KAAA;AACE,WAAA;AACA,gBAAA;AACA,cAAA;;AAIJ,CAlCJ,QAkCI,CA/5BJ,WA+5BI,CAAA;AACE,YAAA;AACA,QAAA;AACA,OAAA;AACA,aAAA,WAAA;AACA,SAAA;AACA,aAAA;;AAIJ,CA5CF,QA4CE,CAAA;AACE,WAAA;AACA,OAAA;AACA,aAAA;;AAEA,CAjDJ,QAiDI,CALF,QAKE,CAAA;AACE,WAAA;AACA,eAAA;AACA,OAAA;AACA,UAAA;AACA,aAAA;AACA,SAAA;;AAEA,CAzDN,QAyDM,CAbJ,QAaI,CARF,gBAQE,KAAA,CAAA;AACE,SAAA;AACA,UAAA;AACA,gBAAA;;AAOR,CAAA;AACE,SAAA;AACA,eAAA;;AAGF,CAAA;AACE,SAAA;AACA,eAAA;;AAGF,CAAA;AACE,SAAA;AACA,eAAA;;AAGF,CAAA;AACE,SAAA;AACA,eAAA;;", "names": []}
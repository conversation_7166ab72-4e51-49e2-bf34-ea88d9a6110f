# Script de test pour les APIs de promotion
# Auteur: Assistant IA
# Date: 20 juillet 2025

Write-Host "🧪 Test des APIs de Promotion" -ForegroundColor Green
Write-Host "================================" -ForegroundColor Green

$baseUrl = "http://localhost:5014"
$headers = @{
    "Content-Type" = "application/json"
    "Accept" = "application/json"
}

Write-Host ""
Write-Host "📋 1. Test de l'endpoint de base (sans auth)" -ForegroundColor Yellow

try {
    $response = Invoke-WebRequest -Uri "$baseUrl/api/promotion-gestion" -Method GET -Headers $headers -ErrorAction Stop
    Write-Host "✅ Endpoint accessible (Status: $($response.StatusCode))" -ForegroundColor Green
} catch {
    if ($_.Exception.Response.StatusCode -eq 401) {
        Write-Host "✅ Endpoint protégé correctement (401 Unauthorized)" -ForegroundColor Green
    } else {
        Write-Host "❌ Erreur inattendue: $($_.Exception.Message)" -ForegroundColor Red
    }
}

Write-Host ""
Write-Host "📋 2. Test de l'endpoint Swagger" -ForegroundColor Yellow

try {
    $response = Invoke-WebRequest -Uri "$baseUrl/swagger" -Method GET -ErrorAction Stop
    Write-Host "✅ Swagger accessible (Status: $($response.StatusCode))" -ForegroundColor Green
} catch {
    Write-Host "❌ Swagger non accessible: $($_.Exception.Message)" -ForegroundColor Red
}

Write-Host ""
Write-Host "📋 3. Test de l'endpoint de validation (public)" -ForegroundColor Yellow

try {
    $testData = @{
        code = "TEST123"
        montantCommande = 100.00
        produitId = 1
        clientId = 1
    } | ConvertTo-Json

    $response = Invoke-WebRequest -Uri "$baseUrl/api/promotion-gestion/validate" -Method POST -Body $testData -Headers $headers -ErrorAction Stop
    Write-Host "✅ Endpoint de validation accessible (Status: $($response.StatusCode))" -ForegroundColor Green
    Write-Host "📄 Réponse: $($response.Content)" -ForegroundColor Cyan
} catch {
    if ($_.Exception.Response.StatusCode -eq 400) {
        Write-Host "✅ Validation fonctionne (400 Bad Request pour code inexistant)" -ForegroundColor Green
    } else {
        Write-Host "⚠️  Réponse: $($_.Exception.Message)" -ForegroundColor Yellow
    }
}

Write-Host ""
Write-Host "📋 4. Test de l'endpoint promotions applicables (public)" -ForegroundColor Yellow

try {
    $response = Invoke-WebRequest -Uri "$baseUrl/api/promotion-gestion/applicable?produitId=1&montantCommande=100" -Method GET -Headers $headers -ErrorAction Stop
    Write-Host "✅ Endpoint promotions applicables accessible (Status: $($response.StatusCode))" -ForegroundColor Green
    Write-Host "📄 Réponse: $($response.Content)" -ForegroundColor Cyan
} catch {
    Write-Host "⚠️  Erreur: $($_.Exception.Message)" -ForegroundColor Yellow
}

Write-Host ""
Write-Host "📋 5. Vérification des routes configurées" -ForegroundColor Yellow

$routes = @(
    "/api/promotion-gestion",
    "/api/promotion-gestion/validate", 
    "/api/promotion-gestion/applicable",
    "/api/promotion-gestion/statistiques",
    "/api/promotion-gestion/fournisseur",
    "/api/promotion-gestion/expirants",
    "/api/promotion-gestion/populaires"
)

foreach ($route in $routes) {
    try {
        $response = Invoke-WebRequest -Uri "$baseUrl$route" -Method GET -Headers $headers -ErrorAction Stop
        Write-Host "✅ $route - Accessible" -ForegroundColor Green
    } catch {
        if ($_.Exception.Response.StatusCode -eq 401) {
            Write-Host "🔒 $route - Protégé (401)" -ForegroundColor Blue
        } elseif ($_.Exception.Response.StatusCode -eq 403) {
            Write-Host "🔒 $route - Accès refusé (403)" -ForegroundColor Blue
        } else {
            Write-Host "❌ $route - Erreur: $($_.Exception.Response.StatusCode)" -ForegroundColor Red
        }
    }
}

Write-Host ""
Write-Host "📋 6. Test de connectivité des frontends" -ForegroundColor Yellow

# Test Frontend Client
try {
    $response = Invoke-WebRequest -Uri "http://localhost:4200" -Method GET -ErrorAction Stop
    Write-Host "✅ Frontend Client accessible (Port 4200)" -ForegroundColor Green
} catch {
    Write-Host "❌ Frontend Client non accessible" -ForegroundColor Red
}

# Test Frontend Fournisseur  
try {
    $response = Invoke-WebRequest -Uri "http://localhost:58548" -Method GET -ErrorAction Stop
    Write-Host "✅ Frontend Fournisseur accessible (Port 58548)" -ForegroundColor Green
} catch {
    Write-Host "❌ Frontend Fournisseur non accessible" -ForegroundColor Red
}

Write-Host ""
Write-Host "🎉 Tests terminés!" -ForegroundColor Green
Write-Host ""
Write-Host "📝 Résumé:" -ForegroundColor Cyan
Write-Host "- Backend API: http://localhost:5014" -ForegroundColor White
Write-Host "- Swagger UI: http://localhost:5014/swagger" -ForegroundColor White  
Write-Host "- Frontend Client: http://localhost:4200" -ForegroundColor White
Write-Host "- Frontend Fournisseur: http://localhost:58548" -ForegroundColor White
Write-Host ""
Write-Host "🔧 Pour tester avec authentification:" -ForegroundColor Yellow
Write-Host "1. Ouvrir Swagger UI" -ForegroundColor White
Write-Host "2. Utiliser les credentials admin:" -ForegroundColor White
Write-Host "   Email: <EMAIL>" -ForegroundColor White
Write-Host "   Password: Admin123!" -ForegroundColor White

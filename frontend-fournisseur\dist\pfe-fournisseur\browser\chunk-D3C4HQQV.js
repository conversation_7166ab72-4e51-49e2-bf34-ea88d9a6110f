import {
  CommonModule,
  Component,
  setClassMetadata,
  ɵsetClassDebugInfo,
  ɵɵdefineComponent,
  ɵɵelementEnd,
  ɵɵelementStart,
  ɵɵtext
} from "./chunk-UBZQS7JS.js";

// src/app/components/admin/profile/admin-profile/admin-profile.component.ts
var AdminProfileComponent = class _AdminProfileComponent {
  static \u0275fac = function AdminProfileComponent_Factory(__ngFactoryType__) {
    return new (__ngFactoryType__ || _AdminProfileComponent)();
  };
  static \u0275cmp = /* @__PURE__ */ \u0275\u0275defineComponent({ type: _AdminProfileComponent, selectors: [["app-admin-profile"]], decls: 5, vars: 0, template: function AdminProfileComponent_Template(rf, ctx) {
    if (rf & 1) {
      \u0275\u0275elementStart(0, "div")(1, "h1");
      \u0275\u0275text(2, "\u{1F464} Mon profil admin");
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(3, "p");
      \u0275\u0275text(4, "En d\xE9veloppement...");
      \u0275\u0275elementEnd()();
    }
  }, dependencies: [CommonModule], encapsulation: 2 });
};
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && setClassMetadata(AdminProfileComponent, [{
    type: Component,
    args: [{
      selector: "app-admin-profile",
      standalone: true,
      imports: [CommonModule],
      template: `<div><h1>\u{1F464} Mon profil admin</h1><p>En d\xE9veloppement...</p></div>`
    }]
  }], null, null);
})();
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && \u0275setClassDebugInfo(AdminProfileComponent, { className: "AdminProfileComponent", filePath: "src/app/components/admin/profile/admin-profile/admin-profile.component.ts", lineNumber: 10 });
})();
export {
  AdminProfileComponent
};
//# sourceMappingURL=chunk-D3C4HQQV.js.map

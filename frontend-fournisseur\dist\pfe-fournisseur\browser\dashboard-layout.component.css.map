{"version": 3, "sources": ["src/app/components/layout/dashboard-layout/dashboard-layout.component.css"], "sourcesContent": [".dashboard-layout {\n  display: flex;\n  height: 100vh;\n  overflow: hidden;\n}\n\n.mobile-overlay {\n  position: fixed;\n  top: 0;\n  left: 0;\n  right: 0;\n  bottom: 0;\n  background: rgba(0, 0, 0, 0.5);\n  z-index: 999;\n  opacity: 0;\n  visibility: hidden;\n  transition: all 0.3s ease;\n}\n\n.mobile-overlay.show {\n  opacity: 1;\n  visibility: visible;\n}\n\n.sidebar-container {\n  position: relative;\n  z-index: 1000;\n}\n\n.main-container {\n  flex: 1;\n  display: flex;\n  flex-direction: column;\n  margin-left: 280px;\n  transition: margin-left 0.3s ease;\n  min-width: 0;\n}\n\n.main-container.sidebar-collapsed {\n  margin-left: 70px;\n}\n\n.main-content {\n  flex: 1;\n  overflow-y: auto;\n  background: #f8fafc;\n}\n\n.content-wrapper {\n  padding: 24px;\n  min-height: calc(100vh - 64px);\n}\n\n/* Mobile Styles (< 768px) */\n@media (max-width: 767px) {\n  .dashboard-layout {\n    position: relative;\n  }\n\n  .sidebar-container {\n    position: fixed;\n    top: 0;\n    left: 0;\n    height: 100vh;\n    transform: translateX(-100%);\n    transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1);\n    z-index: 1000;\n  }\n\n  .sidebar-container.mobile-show {\n    transform: translateX(0);\n  }\n\n  .main-container {\n    margin-left: 0;\n    width: 100%;\n  }\n\n  .main-container.sidebar-collapsed {\n    margin-left: 0;\n  }\n\n  .content-wrapper {\n    padding: 16px;\n    min-height: calc(100vh - 56px); /* Header plus petit sur mobile */\n  }\n}\n\n/* Tablet Styles (768px - 1023px) */\n@media (min-width: 768px) and (max-width: 1023px) {\n  .main-container {\n    margin-left: 240px;\n  }\n\n  .main-container.sidebar-collapsed {\n    margin-left: 60px;\n  }\n\n  .content-wrapper {\n    padding: 20px;\n    min-height: calc(100vh - 60px);\n  }\n}\n\n/* Desktop Styles (> 1024px) */\n@media (min-width: 1024px) {\n  .main-container {\n    margin-left: 280px;\n  }\n\n  .main-container.sidebar-collapsed {\n    margin-left: 70px;\n  }\n\n  .content-wrapper {\n    padding: 24px;\n    min-height: calc(100vh - 64px);\n  }\n}\n\n/* Custom Scrollbar */\n.main-content::-webkit-scrollbar {\n  width: 6px;\n}\n\n.main-content::-webkit-scrollbar-track {\n  background: #f1f5f9;\n}\n\n.main-content::-webkit-scrollbar-thumb {\n  background: #cbd5e1;\n  border-radius: 3px;\n}\n\n.main-content::-webkit-scrollbar-thumb:hover {\n  background: #94a3b8;\n}\n"], "mappings": ";AAAA,CAAC;AACC,WAAS;AACT,UAAQ;AACR,YAAU;AACZ;AAEA,CAAC;AACC,YAAU;AACV,OAAK;AACL,QAAM;AACN,SAAO;AACP,UAAQ;AACR,cAAY,KAAK,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE;AAC1B,WAAS;AACT,WAAS;AACT,cAAY;AACZ,cAAY,IAAI,KAAK;AACvB;AAEA,CAbC,cAac,CAAC;AACd,WAAS;AACT,cAAY;AACd;AAEA,CAAC;AACC,YAAU;AACV,WAAS;AACX;AAEA,CAAC;AACC,QAAM;AACN,WAAS;AACT,kBAAgB;AAChB,eAAa;AACb,cAAY,YAAY,KAAK;AAC7B,aAAW;AACb;AAEA,CATC,cASc,CAAC;AACd,eAAa;AACf;AAEA,CAAC;AACC,QAAM;AACN,cAAY;AACZ,cAAY;AACd;AAEA,CAAC;AACC,WAAS;AACT,cAAY,KAAK,MAAM,EAAE;AAC3B;AAGA,OAAO,CAAC,SAAS,EAAE;AACjB,GAvDD;AAwDG,cAAU;AACZ;AAEA,GAnCD;AAoCG,cAAU;AACV,SAAK;AACL,UAAM;AACN,YAAQ;AACR,eAAW,WAAW;AACtB,gBAAY,UAAU,KAAK,aAAa,GAAG,EAAE,CAAC,EAAE,GAAG,EAAE;AACrD,aAAS;AACX;AAEA,GA7CD,iBA6CmB,CAAC;AACjB,eAAW,WAAW;AACxB;AAEA,GA5CD;AA6CG,iBAAa;AACb,WAAO;AACT;AAEA,GAjDD,cAiDgB,CAxCD;AAyCZ,iBAAa;AACf;AAEA,GAlCD;AAmCG,aAAS;AACT,gBAAY,KAAK,MAAM,EAAE;AAC3B;AACF;AAGA,OAAO,CAAC,SAAS,EAAE,OAAO,IAAI,CAAC,SAAS,EAAE;AACxC,GA7DD;AA8DG,iBAAa;AACf;AAEA,GAjED,cAiEgB,CAxDD;AAyDZ,iBAAa;AACf;AAEA,GAlDD;AAmDG,aAAS;AACT,gBAAY,KAAK,MAAM,EAAE;AAC3B;AACF;AAGA,OAAO,CAAC,SAAS,EAAE;AACjB,GA7ED;AA8EG,iBAAa;AACf;AAEA,GAjFD,cAiFgB,CAxED;AAyEZ,iBAAa;AACf;AAEA,GAlED;AAmEG,aAAS;AACT,gBAAY,KAAK,MAAM,EAAE;AAC3B;AACF;AAGA,CA/EC,YA+EY;AACX,SAAO;AACT;AAEA,CAnFC,YAmFY;AACX,cAAY;AACd;AAEA,CAvFC,YAuFY;AACX,cAAY;AACZ,iBAAe;AACjB;AAEA,CA5FC,YA4FY,yBAAyB;AACpC,cAAY;AACd;", "names": []}
// Styles élégants pour la gestion des avis fournisseur
@use 'sass:color';
@import '../../../../styles.scss';

// Variables de couleurs et styles élégants
$gradient-primary: linear-gradient(135deg, #{$primary-color} 0%, #{color.adjust($primary-color, $lightness: 15%)} 50%, #{color.adjust($primary-color, $lightness: -5%)} 100%);
$gradient-elegant: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
$gradient-success: linear-gradient(135deg, #11998e 0%, #38ef7d 100%);
$gradient-warning: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
$gradient-danger: linear-gradient(135deg, #fc466b 0%, #3f5efb 100%);
$gradient-background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);

$shadow-elegant: 0 10px 40px rgba(0, 0, 0, 0.1);
$shadow-light: 0 4px 20px rgba(0, 0, 0, 0.08);
$shadow-medium: 0 8px 30px rgba(0, 0, 0, 0.12);
$shadow-heavy: 0 15px 50px rgba(0, 0, 0, 0.15);
$shadow-floating: 0 20px 60px rgba(0, 0, 0, 0.1);

$border-radius: 16px;
$border-radius-large: 24px;
$transition: all 0.4s cubic-bezier(0.25, 0.8, 0.25, 1);
$transition-fast: all 0.2s ease-out;

// Container principal élégant
.container-fluid {
  background: $gradient-background;
  min-height: 100vh;
  padding: 2rem;
  position: relative;
  
  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 300px;
    background: linear-gradient(135deg, rgba(102, 126, 234, 0.1) 0%, rgba(118, 75, 162, 0.1) 100%);
    z-index: 0;
  }
  
  > * {
    position: relative;
    z-index: 1;
  }

  @media (max-width: 768px) {
    padding: 1rem;
  }
}

// En-tête élégant et sophistiqué
.page-header {
  background: $gradient-elegant;
  border-radius: $border-radius-large;
  padding: 3rem 2.5rem;
  margin-bottom: 3rem;
  box-shadow: $shadow-floating;
  color: white;
  position: relative;
  overflow: hidden;
  
  .header-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    position: relative;
    z-index: 2;

    @media (max-width: 768px) {
      flex-direction: column;
      gap: 2rem;
      text-align: center;
    }
  }

  .header-title {
    display: flex;
    align-items: center;
    gap: 1.5rem;

    .title-icon {
      width: 80px;
      height: 80px;
      background: rgba(255, 255, 255, 0.2);
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 2.5rem;
      backdrop-filter: blur(10px);
      border: 2px solid rgba(255, 255, 255, 0.3);
    }

    .title-text {
      .page-title {
        font-size: 2.5rem;
        font-weight: 700;
        margin: 0;
        text-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
        
        @media (max-width: 768px) {
          font-size: 2rem;
        }
      }

      .page-subtitle {
        font-size: 1.2rem;
        margin: 0.5rem 0 0 0;
        opacity: 0.9;
        font-weight: 300;
      }
    }
  }

  .header-actions {
    .btn {
      background: rgba(255, 255, 255, 0.2);
      border: 2px solid rgba(255, 255, 255, 0.3);
      color: white;
      padding: 0.75rem 1.5rem;
      border-radius: 50px;
      font-weight: 600;
      backdrop-filter: blur(10px);
      transition: $transition-fast;

      &:hover {
        background: rgba(255, 255, 255, 0.3);
        border-color: rgba(255, 255, 255, 0.5);
        transform: translateY(-2px);
        box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2);
      }
    }
  }
}

// Statistiques élégantes en ligne
.stats-container {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 2rem;
  margin-bottom: 3rem;

  @media (max-width: 768px) {
    grid-template-columns: 1fr;
    gap: 1rem;
  }
}

.stats-card {
  background: white;
  border-radius: $border-radius;
  padding: 2rem;
  box-shadow: $shadow-light;
  border: 1px solid rgba(255, 255, 255, 0.8);
  backdrop-filter: blur(10px);
  transition: $transition;
  position: relative;
  overflow: hidden;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: $gradient-primary;
  }

  &:hover {
    transform: translateY(-8px);
    box-shadow: $shadow-medium;
  }

  &.stats-primary::before { background: $gradient-primary; }
  &.stats-success::before { background: $gradient-success; }
  &.stats-warning::before { background: $gradient-warning; }
  &.stats-danger::before { background: $gradient-danger; }
  &.stats-info::before { background: $gradient-elegant; }

  .stats-icon {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.8rem;
    margin-bottom: 1.5rem;
    background: rgba(102, 126, 234, 0.1);
    color: #667eea;
  }

  .stats-content {
    .stats-number {
      font-size: 2.5rem;
      font-weight: 800;
      color: #2d3748;
      margin-bottom: 0.5rem;
      line-height: 1;
    }

    .stats-label {
      font-size: 0.95rem;
      color: #718096;
      font-weight: 600;
      text-transform: uppercase;
      letter-spacing: 0.5px;
    }
  }
}

// Filtres élégants
.filters-section {
  margin-bottom: 2rem;
}

.filters-card {
  background: white;
  border-radius: $border-radius;
  box-shadow: $shadow-light;
  overflow: hidden;
  border: 1px solid rgba(255, 255, 255, 0.8);

  .filters-header {
    background: $gradient-primary;
    color: white;
    padding: 1.5rem 2rem;
    
    h5 {
      margin: 0;
      font-weight: 600;
      font-size: 1.1rem;
    }
  }

  .filters-content {
    padding: 2rem;
    
    .form-label {
      font-weight: 600;
      color: #4a5568;
      margin-bottom: 0.5rem;
      font-size: 0.9rem;
    }

    .form-select, .form-control {
      border: 2px solid #e2e8f0;
      border-radius: 12px;
      padding: 0.75rem 1rem;
      font-size: 0.95rem;
      transition: $transition-fast;

      &:focus {
        border-color: $primary-color;
        box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
      }
    }

    .input-group-text {
      background: #f7fafc;
      border: 2px solid #e2e8f0;
      border-right: none;
      color: #718096;
    }

    .btn-outline-secondary {
      border: 2px solid #e2e8f0;
      color: #718096;
      border-radius: 12px;
      font-weight: 600;
      transition: $transition-fast;

      &:hover {
        background: #f7fafc;
        border-color: #cbd5e0;
        color: #4a5568;
      }
    }
  }
}

// Messages d'état
.alert {
  border-radius: $border-radius;
  border: none;
  box-shadow: $shadow-light;
  
  &.alert-danger {
    background: linear-gradient(135deg, rgba(252, 70, 107, 0.1) 0%, rgba(63, 94, 251, 0.1) 100%);
    color: #e53e3e;
    border-left: 4px solid #e53e3e;
  }
}

// Loading
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 4rem 2rem;
  
  .loading-spinner {
    text-align: center;
    
    .spinner-border {
      width: 3rem;
      height: 3rem;
      border-width: 0.3em;
    }
    
    .loading-text {
      margin-top: 1rem;
      color: #718096;
      font-weight: 600;
    }
  }
}

// État vide
.empty-state {
  text-align: center;
  padding: 4rem 2rem;
  color: #718096;

  .empty-icon {
    font-size: 4rem;
    margin-bottom: 1.5rem;
    opacity: 0.5;
  }

  h3 {
    font-size: 1.5rem;
    font-weight: 600;
    margin-bottom: 1rem;
    color: #4a5568;
  }

  p {
    font-size: 1.1rem;
    margin: 0;
  }
}

// Liste des avis
.avis-container {
  .avis-list {
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
  }
}

.avis-card {
  background: white;
  border-radius: $border-radius;
  box-shadow: $shadow-light;
  border: 1px solid rgba(255, 255, 255, 0.8);
  overflow: hidden;
  transition: $transition;

  &:hover {
    transform: translateY(-4px);
    box-shadow: $shadow-medium;
  }

  .avis-header {
    background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
    padding: 1.5rem 2rem;
    border-bottom: 1px solid #e2e8f0;
    display: flex;
    justify-content: space-between;
    align-items: center;

    .avis-product {
      .product-name {
        font-size: 1.1rem;
        font-weight: 600;
        color: #2d3748;
        margin: 0 0 0.25rem 0;
      }

      .product-ref {
        color: #718096;
        font-size: 0.9rem;
      }
    }

    .avis-date {
      color: #718096;
      font-size: 0.9rem;
    }
  }

  .avis-content {
    padding: 2rem;

    .avis-client {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 1.5rem;

      .client-info {
        display: flex;
        align-items: center;
        color: #4a5568;
        font-weight: 600;

        i {
          color: #718096;
        }
      }

      .avis-rating {
        display: flex;
        align-items: center;
        gap: 0.5rem;

        .stars {
          display: flex;
          gap: 0.2rem;

          .bi-star-fill {
            color: #ffc107;
          }

          .bi-star {
            color: #e2e8f0;
          }
        }

        .rating-value {
          font-weight: 600;
          color: #4a5568;
        }
      }
    }

    .avis-comment {
      margin-bottom: 1.5rem;

      .comment-content {
        display: flex;
        align-items: flex-start;
        gap: 0.75rem;
        padding: 1rem;
        background: #f7fafc;
        border-radius: 12px;
        border-left: 4px solid #4299e1;

        i {
          color: #4299e1;
          margin-top: 0.2rem;
        }

        span {
          color: #2d3748;
          line-height: 1.6;
        }
      }

      .comment-deleted {
        display: flex;
        align-items: center;
        gap: 0.75rem;
        padding: 1rem;
        background: #fed7d7;
        border-radius: 12px;
        border-left: 4px solid #e53e3e;

        i {
          color: #e53e3e;
        }

        span {
          color: #c53030;
          font-style: italic;
        }
      }

      .comment-none {
        display: flex;
        align-items: center;
        gap: 0.75rem;
        padding: 1rem;
        background: #f0fff4;
        border-radius: 12px;
        border-left: 4px solid #38a169;

        i {
          color: #38a169;
        }

        span {
          color: #2f855a;
          font-style: italic;
        }
      }
    }

    .avis-footer {
      display: flex;
      justify-content: space-between;
      align-items: center;

      .avis-status {
        .badge {
          padding: 0.5rem 1rem;
          border-radius: 50px;
          font-weight: 600;
          font-size: 0.85rem;

          &.badge-success {
            background: $gradient-success;
            color: white;
          }

          &.badge-warning {
            background: $gradient-warning;
            color: white;
          }

          &.badge-danger {
            background: $gradient-danger;
            color: white;
          }

          &.badge-secondary {
            background: #e2e8f0;
            color: #4a5568;
          }
        }
      }

      .avis-actions {
        display: flex;
        gap: 0.5rem;

        .btn {
          border-radius: 50px;
          padding: 0.5rem 1rem;
          font-size: 0.9rem;
          font-weight: 600;
          transition: $transition-fast;

          &:hover {
            transform: translateY(-2px);
          }
        }
      }
    }
  }
}

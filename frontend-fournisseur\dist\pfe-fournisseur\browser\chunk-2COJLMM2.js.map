{"version": 3, "sources": ["src/app/components/dashboard/dashboard.component.ts", "src/app/components/dashboard/dashboard.component.html"], "sourcesContent": ["import { Component, OnInit } from '@angular/core';\r\nimport { CommonModule } from '@angular/common';\r\nimport { RouterModule } from '@angular/router';\r\nimport { AuthService } from '../../services/auth.service';\r\nimport { DashboardService, DashboardStats, RecentOrder, RecentActivity, Fournisseur } from '../../services/dashboard.service';\r\nimport { User } from '../../models/user.model';\r\n\r\n@Component({\r\n  selector: 'app-dashboard',\r\n  standalone: true,\r\n  imports: [CommonModule, RouterModule],\r\n  templateUrl: './dashboard.component.html',\r\n  styleUrls: ['./dashboard.component.css']\r\n})\r\nexport class DashboardComponent implements OnInit {\r\n  currentUser: User | null = null;\r\n  fournisseurInfo: Fournisseur | null = null;\r\n\r\n  // États de chargement\r\n  isLoading = true;\r\n  isStatsLoading = true;\r\n  isOrdersLoading = true;\r\n  isActivityLoading = true;\r\n\r\n  // Statistiques du dashboard (données réelles)\r\n  stats: DashboardStats = {\r\n    totalProducts: 0,\r\n    activeOrders: 0,\r\n    pendingDeliveries: 0,\r\n    monthlyRevenue: 0\r\n  };\r\n\r\n  // Commandes récentes (données réelles)\r\n  recentOrders: RecentOrder[] = [];\r\n\r\n  // Activité récente (données réelles)\r\n  recentActivity: RecentActivity[] = [];\r\n\r\n  // Données pour le graphique des ventes (calculées dynamiquement)\r\n  public salesData: { month: string, sales: number }[] = [];\r\n\r\n  constructor(\r\n    private authService: AuthService,\r\n    private dashboardService: DashboardService\r\n  ) {}\r\n\r\n  ngOnInit(): void {\r\n    this.authService.currentUser$.subscribe(user => {\r\n      this.currentUser = user;\r\n    });\r\n\r\n    // Charger les données du dashboard\r\n    this.loadDashboardData();\r\n  }\r\n\r\n  /**\r\n   * Charger toutes les données du dashboard\r\n   */\r\n  private loadDashboardData(): void {\r\n    this.isLoading = true;\r\n    console.log('🔄 Chargement des données du dashboard...');\r\n\r\n    // Vérifier l'utilisateur connecté\r\n    const currentUser = this.authService.getCurrentUser();\r\n    const supplierId = localStorage.getItem('supplierId');\r\n    console.log('👤 Utilisateur connecté:', currentUser);\r\n    console.log('🏪 ID Fournisseur (localStorage):', supplierId);\r\n\r\n    // Charger les informations du fournisseur\r\n    this.dashboardService.getFournisseurInfo().subscribe({\r\n      next: (fournisseur) => {\r\n        this.fournisseurInfo = fournisseur;\r\n        console.log('✅ Informations fournisseur chargées:', fournisseur);\r\n      },\r\n      error: (error) => {\r\n        console.error('❌ Erreur lors du chargement du fournisseur:', error);\r\n      }\r\n    });\r\n\r\n    // Charger les statistiques\r\n    this.loadStats();\r\n\r\n    // Charger les commandes récentes\r\n    this.loadRecentOrders();\r\n\r\n    // Charger l'activité récente\r\n    this.loadRecentActivity();\r\n\r\n    // Calculer les données de ventes\r\n    this.calculateSalesData();\r\n\r\n    this.isLoading = false;\r\n  }\r\n\r\n  /**\r\n   * Charger les statistiques\r\n   */\r\n  private loadStats(): void {\r\n    this.isStatsLoading = true;\r\n    console.log('📊 Chargement des statistiques...');\r\n\r\n    this.dashboardService.getDashboardStats().subscribe({\r\n      next: (stats) => {\r\n        this.stats = stats;\r\n        this.isStatsLoading = false;\r\n        console.log('✅ Statistiques chargées:', stats);\r\n      },\r\n      error: (error) => {\r\n        console.error('❌ Erreur lors du chargement des statistiques:', error);\r\n        this.isStatsLoading = false;\r\n        // Garder les valeurs par défaut (0)\r\n      }\r\n    });\r\n  }\r\n\r\n  /**\r\n   * Charger les commandes récentes\r\n   */\r\n  private loadRecentOrders(): void {\r\n    this.isOrdersLoading = true;\r\n\r\n    this.dashboardService.getRecentOrders().subscribe({\r\n      next: (orders) => {\r\n        this.recentOrders = orders;\r\n        this.isOrdersLoading = false;\r\n        console.log('Commandes récentes chargées:', orders);\r\n      },\r\n      error: (error) => {\r\n        console.error('Erreur lors du chargement des commandes:', error);\r\n        this.isOrdersLoading = false;\r\n        // Garder un tableau vide\r\n      }\r\n    });\r\n  }\r\n\r\n  /**\r\n   * Charger l'activité récente\r\n   */\r\n  private loadRecentActivity(): void {\r\n    this.isActivityLoading = true;\r\n\r\n    this.dashboardService.getRecentActivity().subscribe({\r\n      next: (activity) => {\r\n        this.recentActivity = activity;\r\n        this.isActivityLoading = false;\r\n        console.log('Activité récente chargée:', activity);\r\n      },\r\n      error: (error) => {\r\n        console.error('Erreur lors du chargement de l\\'activité:', error);\r\n        this.isActivityLoading = false;\r\n        // Garder un tableau vide\r\n      }\r\n    });\r\n  }\r\n\r\n  getStatusClass(status: string): string {\r\n    switch (status) {\r\n      case 'Livré':\r\n        return 'status-delivered';\r\n      case 'En cours':\r\n        return 'status-processing';\r\n      case 'En préparation':\r\n        return 'status-preparing';\r\n      default:\r\n        return 'status-default';\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Calculer les données de ventes pour le graphique\r\n   */\r\n  private calculateSalesData(): void {\r\n    this.dashboardService.getFournisseurOrders().subscribe({\r\n      next: (orders) => {\r\n        // Calculer les ventes des 6 derniers mois\r\n        const monthNames = ['Janvier', 'Février', 'Mars', 'Avril', 'Mai', 'Juin',\r\n                           'Juillet', 'Août', 'Septembre', 'Octobre', 'Novembre', 'Décembre'];\r\n\r\n        const currentDate = new Date();\r\n        const salesByMonth: { month: string, sales: number }[] = [];\r\n\r\n        // Générer les 6 derniers mois\r\n        for (let i = 5; i >= 0; i--) {\r\n          const monthDate = new Date(currentDate.getFullYear(), currentDate.getMonth() - i, 1);\r\n          const monthName = monthNames[monthDate.getMonth()];\r\n\r\n          // Calculer les ventes pour ce mois\r\n          const monthlySales = orders\r\n            .filter(order => {\r\n              const orderDate = new Date(order.dateCommande);\r\n              return orderDate.getMonth() === monthDate.getMonth() &&\r\n                     orderDate.getFullYear() === monthDate.getFullYear();\r\n            })\r\n            .reduce((total, order) => total + order.montantTotal, 0);\r\n\r\n          salesByMonth.push({\r\n            month: monthName,\r\n            sales: Math.round(monthlySales) // Arrondir pour l'affichage\r\n          });\r\n        }\r\n\r\n        this.salesData = salesByMonth;\r\n        console.log('📊 Données de ventes calculées:', this.salesData);\r\n      },\r\n      error: (error) => {\r\n        console.error('❌ Erreur lors du calcul des données de ventes:', error);\r\n        // En cas d'erreur, laisser le tableau vide\r\n        this.salesData = [];\r\n      }\r\n    });\r\n  }\r\n\r\n  /**\r\n   * Obtenir la valeur maximale des ventes pour normaliser le graphique\r\n   */\r\n  getMaxSales(): number {\r\n    if (this.salesData.length === 0) return 1;\r\n    const maxSales = Math.max(...this.salesData.map(data => data.sales));\r\n    return maxSales > 0 ? maxSales : 1;\r\n  }\r\n\r\n  formatCurrency(amount: number): string {\r\n    return new Intl.NumberFormat('fr-TN', {\r\n      style: 'currency',\r\n      currency: 'TND'\r\n    }).format(amount);\r\n  }\r\n}\r\n", "<div class=\"dashboard-container\">\r\n  <!-- Header moderne avec gradient -->\r\n  <div class=\"dashboard-header\">\r\n    <div class=\"header-content\">\r\n      <div class=\"header-text\">\r\n        <h1 class=\"dashboard-title\">\r\n          <span class=\"title-icon\">👋</span>\r\n          <span *ngIf=\"fournisseurInfo\">\r\n            Bonjour {{ fournisseurInfo.prenom }} {{ fournisseurInfo.nom }}\r\n          </span>\r\n          <span *ngIf=\"!fournisseurInfo && currentUser\">\r\n            Bonjour {{ currentUser.prenom }} {{ currentUser.nom }}\r\n          </span>\r\n          <span *ngIf=\"!fournisseurInfo && !currentUser\">\r\n            Bienvenue\r\n          </span>\r\n        </h1>\r\n        <p class=\"dashboard-subtitle\">\r\n          <span *ngIf=\"fournisseurInfo\">\r\n            {{ fournisseurInfo.raisonSociale }} - Voici un aperçu de votre activité aujourd'hui\r\n          </span>\r\n          <span *ngIf=\"!fournisseurInfo\">\r\n            Voici un aperçu de votre activité aujourd'hui\r\n          </span>\r\n        </p>\r\n      </div>\r\n      <div class=\"header-actions\">\r\n        <button class=\"btn btn-primary btn-lg\" routerLink=\"/dashboard/products\">\r\n          <span>➕</span> Nouveau produit\r\n        </button>\r\n      </div>\r\n    </div>\r\n  </div>\r\n\r\n  <!-- Statistiques modernes -->\r\n  <div class=\"stats-section\">\r\n    <div class=\"stats-grid\">\r\n      <div class=\"stat-card stat-primary\">\r\n        <div class=\"stat-header\">\r\n          <div class=\"stat-icon-wrapper\">\r\n            <span class=\"stat-icon\">📦</span>\r\n          </div>\r\n          <div class=\"stat-trend\" [ngClass]=\"stats.totalProducts > 0 ? 'positive' : 'neutral'\">\r\n            <span class=\"trend-icon\" *ngIf=\"stats.totalProducts > 0\">📈</span>\r\n            <span class=\"trend-icon\" *ngIf=\"stats.totalProducts === 0\">➖</span>\r\n          </div>\r\n        </div>\r\n        <div class=\"stat-content\">\r\n          <div class=\"stat-number\" *ngIf=\"!isStatsLoading\">{{ stats.totalProducts }}</div>\r\n          <div class=\"stat-number loading\" *ngIf=\"isStatsLoading\">\r\n            <div class=\"spinner\"></div>\r\n          </div>\r\n          <div class=\"stat-label\">Produits actifs</div>\r\n          <div class=\"stat-description\">Dans votre catalogue</div>\r\n        </div>\r\n      </div>\r\n\r\n      <div class=\"stat-card stat-success\">\r\n        <div class=\"stat-header\">\r\n          <div class=\"stat-icon-wrapper\">\r\n            <span class=\"stat-icon\">📋</span>\r\n          </div>\r\n          <div class=\"stat-trend\" [ngClass]=\"stats.activeOrders > 0 ? 'positive' : 'neutral'\">\r\n            <span class=\"trend-icon\" *ngIf=\"stats.activeOrders > 0\">📈</span>\r\n            <span class=\"trend-icon\" *ngIf=\"stats.activeOrders === 0\">➖</span>\r\n          </div>\r\n        </div>\r\n        <div class=\"stat-content\">\r\n          <div class=\"stat-number\" *ngIf=\"!isStatsLoading\">{{ stats.activeOrders }}</div>\r\n          <div class=\"stat-number loading\" *ngIf=\"isStatsLoading\">\r\n            <div class=\"spinner\"></div>\r\n          </div>\r\n          <div class=\"stat-label\">Commandes ce mois</div>\r\n          <div class=\"stat-description\">En cours de traitement</div>\r\n        </div>\r\n      </div>\r\n\r\n      <div class=\"stat-card stat-warning\">\r\n        <div class=\"stat-header\">\r\n          <div class=\"stat-icon-wrapper\">\r\n            <span class=\"stat-icon\">💰</span>\r\n          </div>\r\n          <div class=\"stat-trend\" [ngClass]=\"stats.monthlyRevenue > 0 ? 'positive' : 'neutral'\">\r\n            <span class=\"trend-icon\" *ngIf=\"stats.monthlyRevenue > 0\">💰</span>\r\n            <span class=\"trend-icon\" *ngIf=\"stats.monthlyRevenue === 0\">➖</span>\r\n          </div>\r\n        </div>\r\n        <div class=\"stat-content\">\r\n          <div class=\"stat-number\" *ngIf=\"!isStatsLoading\">{{ formatCurrency(stats.monthlyRevenue) }}</div>\r\n          <div class=\"stat-number loading\" *ngIf=\"isStatsLoading\">\r\n            <div class=\"spinner\"></div>\r\n          </div>\r\n          <div class=\"stat-label\">Chiffre d'affaires</div>\r\n          <div class=\"stat-description\">Ce mois-ci</div>\r\n        </div>\r\n      </div>\r\n\r\n      <div class=\"stat-card stat-info\">\r\n        <div class=\"stat-header\">\r\n          <div class=\"stat-icon-wrapper\">\r\n            <span class=\"stat-icon\">🚚</span>\r\n          </div>\r\n          <div class=\"stat-trend\" [ngClass]=\"stats.pendingDeliveries > 0 ? 'positive' : 'neutral'\">\r\n            <span class=\"trend-icon\" *ngIf=\"stats.pendingDeliveries > 0\">🚚</span>\r\n            <span class=\"trend-icon\" *ngIf=\"stats.pendingDeliveries === 0\">➖</span>\r\n          </div>\r\n        </div>\r\n        <div class=\"stat-content\">\r\n          <div class=\"stat-number\" *ngIf=\"!isStatsLoading\">{{ stats.pendingDeliveries }}</div>\r\n          <div class=\"stat-number loading\" *ngIf=\"isStatsLoading\">\r\n            <div class=\"spinner\"></div>\r\n          </div>\r\n          <div class=\"stat-label\">Livraisons</div>\r\n          <div class=\"stat-description\">En cours</div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  </div>\r\n\r\n  <!-- Contenu principal avec grille moderne -->\r\n  <div class=\"dashboard-main\">\r\n    <div class=\"dashboard-grid\">\r\n\r\n      <!-- Graphique des ventes -->\r\n      <div class=\"dashboard-card chart-card\">\r\n        <div class=\"card-header\">\r\n          <h3 class=\"card-title\">\r\n            <span class=\"card-icon\">📈</span>\r\n            Évolution des ventes\r\n          </h3>\r\n          <div class=\"card-actions\">\r\n            <select class=\"period-selector\">\r\n              <option>7 derniers jours</option>\r\n              <option>30 derniers jours</option>\r\n              <option>3 derniers mois</option>\r\n            </select>\r\n          </div>\r\n        </div>\r\n        <div class=\"card-body\">\r\n          <div class=\"chart-container\">\r\n            <!-- Aucune donnée de ventes -->\r\n            <div *ngIf=\"salesData.length === 0\" class=\"empty-state\">\r\n              <div class=\"empty-icon\">📊</div>\r\n              <h4>Aucune donnée de ventes</h4>\r\n              <p>Les données apparaîtront après vos premières commandes</p>\r\n            </div>\r\n\r\n            <!-- Graphique avec données -->\r\n            <div *ngIf=\"salesData.length > 0\" class=\"chart-placeholder\">\r\n              <div class=\"chart-bars\">\r\n                <div *ngFor=\"let data of salesData\" class=\"chart-bar\" [style.height.%]=\"data.sales > 0 ? (data.sales / getMaxSales() * 100) : 5\">\r\n                  <div class=\"bar-tooltip\">{{ formatCurrency(data.sales) }}</div>\r\n                </div>\r\n              </div>\r\n              <div class=\"chart-labels\">\r\n                <span *ngFor=\"let data of salesData\">{{ data.month.substring(0, 3) }}</span>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      <!-- Commandes récentes -->\r\n      <div class=\"dashboard-card orders-card\">\r\n        <div class=\"card-header\">\r\n          <h3 class=\"card-title\">\r\n            <span class=\"card-icon\">📋</span>\r\n            Commandes récentes\r\n          </h3>\r\n          <a routerLink=\"/dashboard/orders\" class=\"card-link\">Voir tout</a>\r\n        </div>\r\n        <div class=\"card-body\">\r\n          <!-- État de chargement -->\r\n          <div *ngIf=\"isOrdersLoading\" class=\"loading-state\">\r\n            <div class=\"spinner\"></div>\r\n            <p>Chargement des commandes...</p>\r\n          </div>\r\n\r\n          <!-- Aucune commande -->\r\n          <div *ngIf=\"!isOrdersLoading && recentOrders.length === 0\" class=\"empty-state\">\r\n            <div class=\"empty-icon\">📭</div>\r\n            <h4>Aucune commande récente</h4>\r\n            <p>Les nouvelles commandes apparaîtront ici</p>\r\n          </div>\r\n\r\n          <!-- Liste des commandes -->\r\n          <div *ngIf=\"!isOrdersLoading && recentOrders.length > 0\" class=\"orders-list\">\r\n            <div *ngFor=\"let order of recentOrders\" class=\"order-item\">\r\n              <div class=\"order-info\">\r\n                <div class=\"order-id\">{{ order.reference }}</div>\r\n                <div class=\"order-customer\">{{ order.client }}</div>\r\n                <div class=\"order-date\">{{ order.date | date:'dd/MM/yyyy' }}</div>\r\n              </div>\r\n              <div class=\"order-details\">\r\n                <div class=\"order-amount\">{{ formatCurrency(order.amount) }}</div>\r\n                <span class=\"badge\" [ngClass]=\"'badge-' + getStatusClass(order.status)\">\r\n                  {{ order.status }}\r\n                </span>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      <!-- Activité récente -->\r\n      <div class=\"dashboard-card activity-card\">\r\n        <div class=\"card-header\">\r\n          <h3 class=\"card-title\">\r\n            <span class=\"card-icon\">🔔</span>\r\n            Activité récente\r\n          </h3>\r\n        </div>\r\n        <div class=\"card-body\">\r\n          <!-- État de chargement -->\r\n          <div *ngIf=\"isActivityLoading\" class=\"loading-state\">\r\n            <div class=\"spinner\"></div>\r\n            <p>Chargement de l'activité...</p>\r\n          </div>\r\n\r\n          <!-- Aucune activité -->\r\n          <div *ngIf=\"!isActivityLoading && recentActivity.length === 0\" class=\"empty-state\">\r\n            <div class=\"empty-icon\">📭</div>\r\n            <h4>Aucune activité récente</h4>\r\n            <p>Vos dernières actions apparaîtront ici</p>\r\n          </div>\r\n\r\n          <!-- Liste d'activité -->\r\n          <div *ngIf=\"!isActivityLoading && recentActivity.length > 0\" class=\"activity-list\">\r\n            <div *ngFor=\"let activity of recentActivity\" class=\"activity-item\">\r\n              <div class=\"activity-icon\">{{ activity.icon }}</div>\r\n              <div class=\"activity-content\">\r\n                <div class=\"activity-title\">{{ activity.title }}</div>\r\n                <div class=\"activity-time\">{{ activity.time }}</div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      <!-- Actions rapides -->\r\n      <div class=\"dashboard-card actions-card\">\r\n        <div class=\"card-header\">\r\n          <h3 class=\"card-title\">\r\n            <span class=\"card-icon\">⚡</span>\r\n            Actions rapides\r\n          </h3>\r\n        </div>\r\n        <div class=\"card-body\">\r\n          <div class=\"actions-grid\">\r\n            <button class=\"action-button\" routerLink=\"/dashboard/products\">\r\n              <div class=\"action-icon\">📦</div>\r\n              <div class=\"action-content\">\r\n                <div class=\"action-title\">Produits</div>\r\n                <div class=\"action-subtitle\">Gérer le catalogue</div>\r\n              </div>\r\n            </button>\r\n\r\n            <button class=\"action-button\" routerLink=\"/dashboard/referentiels\">\r\n              <div class=\"action-icon\">🗂️</div>\r\n              <div class=\"action-content\">\r\n                <div class=\"action-title\">Référentiels</div>\r\n                <div class=\"action-subtitle\">Catégories & marques</div>\r\n              </div>\r\n            </button>\r\n\r\n            <button class=\"action-button\" routerLink=\"/dashboard/orders\">\r\n              <div class=\"action-icon\">📋</div>\r\n              <div class=\"action-content\">\r\n                <div class=\"action-title\">Commandes</div>\r\n                <div class=\"action-subtitle\">Suivi & gestion</div>\r\n              </div>\r\n            </button>\r\n\r\n            <button class=\"action-button\" routerLink=\"/dashboard/profile\">\r\n              <div class=\"action-icon\">👤</div>\r\n              <div class=\"action-content\">\r\n                <div class=\"action-title\">Profil</div>\r\n                <div class=\"action-subtitle\">Paramètres compte</div>\r\n              </div>\r\n            </button>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  </div>\r\n</div>"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACOU,IAAA,yBAAA,GAAA,MAAA;AACE,IAAA,iBAAA,CAAA;AACF,IAAA,uBAAA;;;;AADE,IAAA,oBAAA;AAAA,IAAA,6BAAA,aAAA,OAAA,gBAAA,QAAA,KAAA,OAAA,gBAAA,KAAA,GAAA;;;;;AAEF,IAAA,yBAAA,GAAA,MAAA;AACE,IAAA,iBAAA,CAAA;AACF,IAAA,uBAAA;;;;AADE,IAAA,oBAAA;AAAA,IAAA,6BAAA,aAAA,OAAA,YAAA,QAAA,KAAA,OAAA,YAAA,KAAA,GAAA;;;;;AAEF,IAAA,yBAAA,GAAA,MAAA;AACE,IAAA,iBAAA,GAAA,aAAA;AACF,IAAA,uBAAA;;;;;AAGA,IAAA,yBAAA,GAAA,MAAA;AACE,IAAA,iBAAA,CAAA;AACF,IAAA,uBAAA;;;;AADE,IAAA,oBAAA;AAAA,IAAA,6BAAA,KAAA,OAAA,gBAAA,eAAA,yDAAA;;;;;AAEF,IAAA,yBAAA,GAAA,MAAA;AACE,IAAA,iBAAA,GAAA,uDAAA;AACF,IAAA,uBAAA;;;;;AAoBE,IAAA,yBAAA,GAAA,QAAA,EAAA;AAAyD,IAAA,iBAAA,GAAA,WAAA;AAAE,IAAA,uBAAA;;;;;AAC3D,IAAA,yBAAA,GAAA,QAAA,EAAA;AAA2D,IAAA,iBAAA,GAAA,QAAA;AAAC,IAAA,uBAAA;;;;;AAI9D,IAAA,yBAAA,GAAA,OAAA,EAAA;AAAiD,IAAA,iBAAA,CAAA;AAAyB,IAAA,uBAAA;;;;AAAzB,IAAA,oBAAA;AAAA,IAAA,4BAAA,OAAA,MAAA,aAAA;;;;;AACjD,IAAA,yBAAA,GAAA,OAAA,EAAA;AACE,IAAA,oBAAA,GAAA,OAAA,EAAA;AACF,IAAA,uBAAA;;;;;AAYE,IAAA,yBAAA,GAAA,QAAA,EAAA;AAAwD,IAAA,iBAAA,GAAA,WAAA;AAAE,IAAA,uBAAA;;;;;AAC1D,IAAA,yBAAA,GAAA,QAAA,EAAA;AAA0D,IAAA,iBAAA,GAAA,QAAA;AAAC,IAAA,uBAAA;;;;;AAI7D,IAAA,yBAAA,GAAA,OAAA,EAAA;AAAiD,IAAA,iBAAA,CAAA;AAAwB,IAAA,uBAAA;;;;AAAxB,IAAA,oBAAA;AAAA,IAAA,4BAAA,OAAA,MAAA,YAAA;;;;;AACjD,IAAA,yBAAA,GAAA,OAAA,EAAA;AACE,IAAA,oBAAA,GAAA,OAAA,EAAA;AACF,IAAA,uBAAA;;;;;AAYE,IAAA,yBAAA,GAAA,QAAA,EAAA;AAA0D,IAAA,iBAAA,GAAA,WAAA;AAAE,IAAA,uBAAA;;;;;AAC5D,IAAA,yBAAA,GAAA,QAAA,EAAA;AAA4D,IAAA,iBAAA,GAAA,QAAA;AAAC,IAAA,uBAAA;;;;;AAI/D,IAAA,yBAAA,GAAA,OAAA,EAAA;AAAiD,IAAA,iBAAA,CAAA;AAA0C,IAAA,uBAAA;;;;AAA1C,IAAA,oBAAA;AAAA,IAAA,4BAAA,OAAA,eAAA,OAAA,MAAA,cAAA,CAAA;;;;;AACjD,IAAA,yBAAA,GAAA,OAAA,EAAA;AACE,IAAA,oBAAA,GAAA,OAAA,EAAA;AACF,IAAA,uBAAA;;;;;AAYE,IAAA,yBAAA,GAAA,QAAA,EAAA;AAA6D,IAAA,iBAAA,GAAA,WAAA;AAAE,IAAA,uBAAA;;;;;AAC/D,IAAA,yBAAA,GAAA,QAAA,EAAA;AAA+D,IAAA,iBAAA,GAAA,QAAA;AAAC,IAAA,uBAAA;;;;;AAIlE,IAAA,yBAAA,GAAA,OAAA,EAAA;AAAiD,IAAA,iBAAA,CAAA;AAA6B,IAAA,uBAAA;;;;AAA7B,IAAA,oBAAA;AAAA,IAAA,4BAAA,OAAA,MAAA,iBAAA;;;;;AACjD,IAAA,yBAAA,GAAA,OAAA,EAAA;AACE,IAAA,oBAAA,GAAA,OAAA,EAAA;AACF,IAAA,uBAAA;;;;;AA8BE,IAAA,yBAAA,GAAA,OAAA,EAAA,EAAwD,GAAA,OAAA,EAAA;AAC9B,IAAA,iBAAA,GAAA,WAAA;AAAE,IAAA,uBAAA;AAC1B,IAAA,yBAAA,GAAA,IAAA;AAAI,IAAA,iBAAA,GAAA,4BAAA;AAAuB,IAAA,uBAAA;AAC3B,IAAA,yBAAA,GAAA,GAAA;AAAG,IAAA,iBAAA,GAAA,oEAAA;AAAsD,IAAA,uBAAA,EAAI;;;;;AAM3D,IAAA,yBAAA,GAAA,OAAA,EAAA,EAAiI,GAAA,OAAA,EAAA;AACtG,IAAA,iBAAA,CAAA;AAAgC,IAAA,uBAAA,EAAM;;;;;AADX,IAAA,sBAAA,UAAA,QAAA,QAAA,IAAA,QAAA,QAAA,OAAA,YAAA,IAAA,MAAA,GAAA,GAAA;AAC3B,IAAA,oBAAA,CAAA;AAAA,IAAA,4BAAA,OAAA,eAAA,QAAA,KAAA,CAAA;;;;;AAI3B,IAAA,yBAAA,GAAA,MAAA;AAAqC,IAAA,iBAAA,CAAA;AAAgC,IAAA,uBAAA;;;;AAAhC,IAAA,oBAAA;AAAA,IAAA,4BAAA,QAAA,MAAA,UAAA,GAAA,CAAA,CAAA;;;;;AAPzC,IAAA,yBAAA,GAAA,OAAA,EAAA,EAA4D,GAAA,OAAA,EAAA;AAExD,IAAA,qBAAA,GAAA,0CAAA,GAAA,GAAA,OAAA,EAAA;AAGF,IAAA,uBAAA;AACA,IAAA,yBAAA,GAAA,OAAA,EAAA;AACE,IAAA,qBAAA,GAAA,2CAAA,GAAA,GAAA,QAAA,EAAA;AACF,IAAA,uBAAA,EAAM;;;;AANkB,IAAA,oBAAA,CAAA;AAAA,IAAA,qBAAA,WAAA,OAAA,SAAA;AAKC,IAAA,oBAAA,CAAA;AAAA,IAAA,qBAAA,WAAA,OAAA,SAAA;;;;;AAkB7B,IAAA,yBAAA,GAAA,OAAA,EAAA;AACE,IAAA,oBAAA,GAAA,OAAA,EAAA;AACA,IAAA,yBAAA,GAAA,GAAA;AAAG,IAAA,iBAAA,GAAA,6BAAA;AAA2B,IAAA,uBAAA,EAAI;;;;;AAIpC,IAAA,yBAAA,GAAA,OAAA,EAAA,EAA+E,GAAA,OAAA,EAAA;AACrD,IAAA,iBAAA,GAAA,WAAA;AAAE,IAAA,uBAAA;AAC1B,IAAA,yBAAA,GAAA,IAAA;AAAI,IAAA,iBAAA,GAAA,4BAAA;AAAuB,IAAA,uBAAA;AAC3B,IAAA,yBAAA,GAAA,GAAA;AAAG,IAAA,iBAAA,GAAA,6CAAA;AAAwC,IAAA,uBAAA,EAAI;;;;;AAK/C,IAAA,yBAAA,GAAA,OAAA,EAAA,EAA2D,GAAA,OAAA,EAAA,EACjC,GAAA,OAAA,EAAA;AACA,IAAA,iBAAA,CAAA;AAAqB,IAAA,uBAAA;AAC3C,IAAA,yBAAA,GAAA,OAAA,EAAA;AAA4B,IAAA,iBAAA,CAAA;AAAkB,IAAA,uBAAA;AAC9C,IAAA,yBAAA,GAAA,OAAA,EAAA;AAAwB,IAAA,iBAAA,CAAA;;AAAoC,IAAA,uBAAA,EAAM;AAEpE,IAAA,yBAAA,GAAA,OAAA,EAAA,EAA2B,IAAA,OAAA,EAAA;AACC,IAAA,iBAAA,EAAA;AAAkC,IAAA,uBAAA;AAC5D,IAAA,yBAAA,IAAA,QAAA,EAAA;AACE,IAAA,iBAAA,EAAA;AACF,IAAA,uBAAA,EAAO,EACH;;;;;AATkB,IAAA,oBAAA,CAAA;AAAA,IAAA,4BAAA,SAAA,SAAA;AACM,IAAA,oBAAA,CAAA;AAAA,IAAA,4BAAA,SAAA,MAAA;AACJ,IAAA,oBAAA,CAAA;AAAA,IAAA,4BAAA,sBAAA,GAAA,GAAA,SAAA,MAAA,YAAA,CAAA;AAGE,IAAA,oBAAA,CAAA;AAAA,IAAA,4BAAA,OAAA,eAAA,SAAA,MAAA,CAAA;AACN,IAAA,oBAAA;AAAA,IAAA,qBAAA,WAAA,WAAA,OAAA,eAAA,SAAA,MAAA,CAAA;AAClB,IAAA,oBAAA;AAAA,IAAA,6BAAA,KAAA,SAAA,QAAA,GAAA;;;;;AAVR,IAAA,yBAAA,GAAA,OAAA,EAAA;AACE,IAAA,qBAAA,GAAA,2CAAA,IAAA,GAAA,OAAA,EAAA;AAaF,IAAA,uBAAA;;;;AAbyB,IAAA,oBAAA;AAAA,IAAA,qBAAA,WAAA,OAAA,YAAA;;;;;AA2BzB,IAAA,yBAAA,GAAA,OAAA,EAAA;AACE,IAAA,oBAAA,GAAA,OAAA,EAAA;AACA,IAAA,yBAAA,GAAA,GAAA;AAAG,IAAA,iBAAA,GAAA,gCAAA;AAA2B,IAAA,uBAAA,EAAI;;;;;AAIpC,IAAA,yBAAA,GAAA,OAAA,EAAA,EAAmF,GAAA,OAAA,EAAA;AACzD,IAAA,iBAAA,GAAA,WAAA;AAAE,IAAA,uBAAA;AAC1B,IAAA,yBAAA,GAAA,IAAA;AAAI,IAAA,iBAAA,GAAA,+BAAA;AAAuB,IAAA,uBAAA;AAC3B,IAAA,yBAAA,GAAA,GAAA;AAAG,IAAA,iBAAA,GAAA,8CAAA;AAAsC,IAAA,uBAAA,EAAI;;;;;AAK7C,IAAA,yBAAA,GAAA,OAAA,EAAA,EAAmE,GAAA,OAAA,EAAA;AACtC,IAAA,iBAAA,CAAA;AAAmB,IAAA,uBAAA;AAC9C,IAAA,yBAAA,GAAA,OAAA,EAAA,EAA8B,GAAA,OAAA,EAAA;AACA,IAAA,iBAAA,CAAA;AAAoB,IAAA,uBAAA;AAChD,IAAA,yBAAA,GAAA,OAAA,EAAA;AAA2B,IAAA,iBAAA,CAAA;AAAmB,IAAA,uBAAA,EAAM,EAChD;;;;AAJqB,IAAA,oBAAA,CAAA;AAAA,IAAA,4BAAA,YAAA,IAAA;AAEG,IAAA,oBAAA,CAAA;AAAA,IAAA,4BAAA,YAAA,KAAA;AACD,IAAA,oBAAA,CAAA;AAAA,IAAA,4BAAA,YAAA,IAAA;;;;;AALjC,IAAA,yBAAA,GAAA,OAAA,EAAA;AACE,IAAA,qBAAA,GAAA,2CAAA,GAAA,GAAA,OAAA,EAAA;AAOF,IAAA,uBAAA;;;;AAP4B,IAAA,oBAAA;AAAA,IAAA,qBAAA,WAAA,OAAA,cAAA;;;ADtNhC,IAAO,qBAAP,MAAO,oBAAkB;EA4BnB;EACA;EA5BV,cAA2B;EAC3B,kBAAsC;;EAGtC,YAAY;EACZ,iBAAiB;EACjB,kBAAkB;EAClB,oBAAoB;;EAGpB,QAAwB;IACtB,eAAe;IACf,cAAc;IACd,mBAAmB;IACnB,gBAAgB;;;EAIlB,eAA8B,CAAA;;EAG9B,iBAAmC,CAAA;;EAG5B,YAAgD,CAAA;EAEvD,YACU,aACA,kBAAkC;AADlC,SAAA,cAAA;AACA,SAAA,mBAAA;EACP;EAEH,WAAQ;AACN,SAAK,YAAY,aAAa,UAAU,UAAO;AAC7C,WAAK,cAAc;IACrB,CAAC;AAGD,SAAK,kBAAiB;EACxB;;;;EAKQ,oBAAiB;AACvB,SAAK,YAAY;AACjB,YAAQ,IAAI,qDAA2C;AAGvD,UAAM,cAAc,KAAK,YAAY,eAAc;AACnD,UAAM,aAAa,aAAa,QAAQ,YAAY;AACpD,YAAQ,IAAI,sCAA4B,WAAW;AACnD,YAAQ,IAAI,4CAAqC,UAAU;AAG3D,SAAK,iBAAiB,mBAAkB,EAAG,UAAU;MACnD,MAAM,CAAC,gBAAe;AACpB,aAAK,kBAAkB;AACvB,gBAAQ,IAAI,gDAAwC,WAAW;MACjE;MACA,OAAO,CAAC,UAAS;AACf,gBAAQ,MAAM,oDAA+C,KAAK;MACpE;KACD;AAGD,SAAK,UAAS;AAGd,SAAK,iBAAgB;AAGrB,SAAK,mBAAkB;AAGvB,SAAK,mBAAkB;AAEvB,SAAK,YAAY;EACnB;;;;EAKQ,YAAS;AACf,SAAK,iBAAiB;AACtB,YAAQ,IAAI,0CAAmC;AAE/C,SAAK,iBAAiB,kBAAiB,EAAG,UAAU;MAClD,MAAM,CAAC,UAAS;AACd,aAAK,QAAQ;AACb,aAAK,iBAAiB;AACtB,gBAAQ,IAAI,oCAA4B,KAAK;MAC/C;MACA,OAAO,CAAC,UAAS;AACf,gBAAQ,MAAM,sDAAiD,KAAK;AACpE,aAAK,iBAAiB;MAExB;KACD;EACH;;;;EAKQ,mBAAgB;AACtB,SAAK,kBAAkB;AAEvB,SAAK,iBAAiB,gBAAe,EAAG,UAAU;MAChD,MAAM,CAAC,WAAU;AACf,aAAK,eAAe;AACpB,aAAK,kBAAkB;AACvB,gBAAQ,IAAI,sCAAgC,MAAM;MACpD;MACA,OAAO,CAAC,UAAS;AACf,gBAAQ,MAAM,4CAA4C,KAAK;AAC/D,aAAK,kBAAkB;MAEzB;KACD;EACH;;;;EAKQ,qBAAkB;AACxB,SAAK,oBAAoB;AAEzB,SAAK,iBAAiB,kBAAiB,EAAG,UAAU;MAClD,MAAM,CAAC,aAAY;AACjB,aAAK,iBAAiB;AACtB,aAAK,oBAAoB;AACzB,gBAAQ,IAAI,sCAA6B,QAAQ;MACnD;MACA,OAAO,CAAC,UAAS;AACf,gBAAQ,MAAM,+CAA6C,KAAK;AAChE,aAAK,oBAAoB;MAE3B;KACD;EACH;EAEA,eAAe,QAAc;AAC3B,YAAQ,QAAQ;MACd,KAAK;AACH,eAAO;MACT,KAAK;AACH,eAAO;MACT,KAAK;AACH,eAAO;MACT;AACE,eAAO;IACX;EACF;;;;EAKQ,qBAAkB;AACxB,SAAK,iBAAiB,qBAAoB,EAAG,UAAU;MACrD,MAAM,CAAC,WAAU;AAEf,cAAM,aAAa;UAAC;UAAW;UAAW;UAAQ;UAAS;UAAO;UAC/C;UAAW;UAAQ;UAAa;UAAW;UAAY;QAAU;AAEpF,cAAM,cAAc,oBAAI,KAAI;AAC5B,cAAM,eAAmD,CAAA;AAGzD,iBAAS,IAAI,GAAG,KAAK,GAAG,KAAK;AAC3B,gBAAM,YAAY,IAAI,KAAK,YAAY,YAAW,GAAI,YAAY,SAAQ,IAAK,GAAG,CAAC;AACnF,gBAAM,YAAY,WAAW,UAAU,SAAQ,CAAE;AAGjD,gBAAM,eAAe,OAClB,OAAO,WAAQ;AACd,kBAAM,YAAY,IAAI,KAAK,MAAM,YAAY;AAC7C,mBAAO,UAAU,SAAQ,MAAO,UAAU,SAAQ,KAC3C,UAAU,YAAW,MAAO,UAAU,YAAW;UAC1D,CAAC,EACA,OAAO,CAAC,OAAO,UAAU,QAAQ,MAAM,cAAc,CAAC;AAEzD,uBAAa,KAAK;YAChB,OAAO;YACP,OAAO,KAAK,MAAM,YAAY;;WAC/B;QACH;AAEA,aAAK,YAAY;AACjB,gBAAQ,IAAI,gDAAmC,KAAK,SAAS;MAC/D;MACA,OAAO,CAAC,UAAS;AACf,gBAAQ,MAAM,0DAAkD,KAAK;AAErE,aAAK,YAAY,CAAA;MACnB;KACD;EACH;;;;EAKA,cAAW;AACT,QAAI,KAAK,UAAU,WAAW;AAAG,aAAO;AACxC,UAAM,WAAW,KAAK,IAAI,GAAG,KAAK,UAAU,IAAI,UAAQ,KAAK,KAAK,CAAC;AACnE,WAAO,WAAW,IAAI,WAAW;EACnC;EAEA,eAAe,QAAc;AAC3B,WAAO,IAAI,KAAK,aAAa,SAAS;MACpC,OAAO;MACP,UAAU;KACX,EAAE,OAAO,MAAM;EAClB;;qCApNW,qBAAkB,4BAAA,WAAA,GAAA,4BAAA,gBAAA,CAAA;EAAA;yEAAlB,qBAAkB,WAAA,CAAA,CAAA,eAAA,CAAA,GAAA,OAAA,KAAA,MAAA,IAAA,QAAA,CAAA,CAAA,GAAA,qBAAA,GAAA,CAAA,GAAA,kBAAA,GAAA,CAAA,GAAA,gBAAA,GAAA,CAAA,GAAA,aAAA,GAAA,CAAA,GAAA,iBAAA,GAAA,CAAA,GAAA,YAAA,GAAA,CAAA,GAAA,MAAA,GAAA,CAAA,GAAA,oBAAA,GAAA,CAAA,GAAA,gBAAA,GAAA,CAAA,cAAA,uBAAA,GAAA,OAAA,eAAA,QAAA,GAAA,CAAA,GAAA,eAAA,GAAA,CAAA,GAAA,YAAA,GAAA,CAAA,GAAA,aAAA,cAAA,GAAA,CAAA,GAAA,aAAA,GAAA,CAAA,GAAA,mBAAA,GAAA,CAAA,GAAA,WAAA,GAAA,CAAA,GAAA,cAAA,GAAA,SAAA,GAAA,CAAA,SAAA,cAAA,GAAA,MAAA,GAAA,CAAA,GAAA,cAAA,GAAA,CAAA,SAAA,eAAA,GAAA,MAAA,GAAA,CAAA,SAAA,uBAAA,GAAA,MAAA,GAAA,CAAA,GAAA,YAAA,GAAA,CAAA,GAAA,kBAAA,GAAA,CAAA,GAAA,aAAA,cAAA,GAAA,CAAA,GAAA,aAAA,cAAA,GAAA,CAAA,GAAA,aAAA,WAAA,GAAA,CAAA,GAAA,gBAAA,GAAA,CAAA,GAAA,gBAAA,GAAA,CAAA,GAAA,kBAAA,YAAA,GAAA,CAAA,GAAA,aAAA,GAAA,CAAA,GAAA,YAAA,GAAA,CAAA,GAAA,WAAA,GAAA,CAAA,GAAA,cAAA,GAAA,CAAA,GAAA,iBAAA,GAAA,CAAA,GAAA,WAAA,GAAA,CAAA,GAAA,iBAAA,GAAA,CAAA,SAAA,eAAA,GAAA,MAAA,GAAA,CAAA,SAAA,qBAAA,GAAA,MAAA,GAAA,CAAA,GAAA,kBAAA,aAAA,GAAA,CAAA,cAAA,qBAAA,GAAA,WAAA,GAAA,CAAA,SAAA,iBAAA,GAAA,MAAA,GAAA,CAAA,SAAA,eAAA,GAAA,MAAA,GAAA,CAAA,GAAA,kBAAA,eAAA,GAAA,CAAA,SAAA,iBAAA,GAAA,MAAA,GAAA,CAAA,GAAA,kBAAA,cAAA,GAAA,CAAA,GAAA,cAAA,GAAA,CAAA,cAAA,uBAAA,GAAA,eAAA,GAAA,CAAA,GAAA,aAAA,GAAA,CAAA,GAAA,gBAAA,GAAA,CAAA,GAAA,cAAA,GAAA,CAAA,GAAA,iBAAA,GAAA,CAAA,cAAA,2BAAA,GAAA,eAAA,GAAA,CAAA,cAAA,qBAAA,GAAA,eAAA,GAAA,CAAA,cAAA,sBAAA,GAAA,eAAA,GAAA,CAAA,GAAA,YAAA,GAAA,CAAA,GAAA,aAAA,GAAA,CAAA,GAAA,eAAA,SAAA,GAAA,CAAA,GAAA,SAAA,GAAA,CAAA,GAAA,aAAA,GAAA,CAAA,GAAA,YAAA,GAAA,CAAA,GAAA,mBAAA,GAAA,CAAA,GAAA,YAAA,GAAA,CAAA,SAAA,aAAA,GAAA,UAAA,GAAA,SAAA,SAAA,GAAA,CAAA,GAAA,cAAA,GAAA,CAAA,GAAA,SAAA,SAAA,GAAA,CAAA,GAAA,WAAA,GAAA,CAAA,GAAA,aAAA,GAAA,CAAA,GAAA,eAAA,GAAA,CAAA,GAAA,aAAA,GAAA,CAAA,SAAA,cAAA,GAAA,SAAA,SAAA,GAAA,CAAA,GAAA,YAAA,GAAA,CAAA,GAAA,YAAA,GAAA,CAAA,GAAA,UAAA,GAAA,CAAA,GAAA,gBAAA,GAAA,CAAA,GAAA,YAAA,GAAA,CAAA,GAAA,eAAA,GAAA,CAAA,GAAA,cAAA,GAAA,CAAA,GAAA,SAAA,GAAA,SAAA,GAAA,CAAA,GAAA,eAAA,GAAA,CAAA,SAAA,iBAAA,GAAA,SAAA,SAAA,GAAA,CAAA,GAAA,eAAA,GAAA,CAAA,GAAA,eAAA,GAAA,CAAA,GAAA,kBAAA,GAAA,CAAA,GAAA,gBAAA,GAAA,CAAA,GAAA,eAAA,CAAA,GAAA,UAAA,SAAA,4BAAA,IAAA,KAAA;AAAA,QAAA,KAAA,GAAA;ACd/B,MAAA,yBAAA,GAAA,OAAA,CAAA,EAAiC,GAAA,OAAA,CAAA,EAED,GAAA,OAAA,CAAA,EACA,GAAA,OAAA,CAAA,EACD,GAAA,MAAA,CAAA,EACK,GAAA,QAAA,CAAA;AACD,MAAA,iBAAA,GAAA,WAAA;AAAE,MAAA,uBAAA;AAC3B,MAAA,qBAAA,GAAA,oCAAA,GAAA,GAAA,QAAA,CAAA,EAA8B,GAAA,oCAAA,GAAA,GAAA,QAAA,CAAA,EAGgB,GAAA,oCAAA,GAAA,GAAA,QAAA,CAAA;AAMhD,MAAA,uBAAA;AACA,MAAA,yBAAA,IAAA,KAAA,CAAA;AACE,MAAA,qBAAA,IAAA,qCAAA,GAAA,GAAA,QAAA,CAAA,EAA8B,IAAA,qCAAA,GAAA,GAAA,QAAA,CAAA;AAMhC,MAAA,uBAAA,EAAI;AAEN,MAAA,yBAAA,IAAA,OAAA,CAAA,EAA4B,IAAA,UAAA,CAAA,EAC8C,IAAA,MAAA;AAChE,MAAA,iBAAA,IAAA,QAAA;AAAC,MAAA,uBAAA;AAAQ,MAAA,iBAAA,IAAA,mBAAA;AACjB,MAAA,uBAAA,EAAS,EACL,EACF;AAIR,MAAA,yBAAA,IAAA,OAAA,EAAA,EAA2B,IAAA,OAAA,EAAA,EACD,IAAA,OAAA,EAAA,EACc,IAAA,OAAA,EAAA,EACT,IAAA,OAAA,EAAA,EACQ,IAAA,QAAA,EAAA;AACL,MAAA,iBAAA,IAAA,WAAA;AAAE,MAAA,uBAAA,EAAO;AAEnC,MAAA,yBAAA,IAAA,OAAA,EAAA;AACE,MAAA,qBAAA,IAAA,qCAAA,GAAA,GAAA,QAAA,EAAA,EAAyD,IAAA,qCAAA,GAAA,GAAA,QAAA,EAAA;AAE3D,MAAA,uBAAA,EAAM;AAER,MAAA,yBAAA,IAAA,OAAA,EAAA;AACE,MAAA,qBAAA,IAAA,oCAAA,GAAA,GAAA,OAAA,EAAA,EAAiD,IAAA,oCAAA,GAAA,GAAA,OAAA,EAAA;AAIjD,MAAA,yBAAA,IAAA,OAAA,EAAA;AAAwB,MAAA,iBAAA,IAAA,iBAAA;AAAe,MAAA,uBAAA;AACvC,MAAA,yBAAA,IAAA,OAAA,EAAA;AAA8B,MAAA,iBAAA,IAAA,sBAAA;AAAoB,MAAA,uBAAA,EAAM,EACpD;AAGR,MAAA,yBAAA,IAAA,OAAA,EAAA,EAAoC,IAAA,OAAA,EAAA,EACT,IAAA,OAAA,EAAA,EACQ,IAAA,QAAA,EAAA;AACL,MAAA,iBAAA,IAAA,WAAA;AAAE,MAAA,uBAAA,EAAO;AAEnC,MAAA,yBAAA,IAAA,OAAA,EAAA;AACE,MAAA,qBAAA,IAAA,qCAAA,GAAA,GAAA,QAAA,EAAA,EAAwD,IAAA,qCAAA,GAAA,GAAA,QAAA,EAAA;AAE1D,MAAA,uBAAA,EAAM;AAER,MAAA,yBAAA,IAAA,OAAA,EAAA;AACE,MAAA,qBAAA,IAAA,oCAAA,GAAA,GAAA,OAAA,EAAA,EAAiD,IAAA,oCAAA,GAAA,GAAA,OAAA,EAAA;AAIjD,MAAA,yBAAA,IAAA,OAAA,EAAA;AAAwB,MAAA,iBAAA,IAAA,mBAAA;AAAiB,MAAA,uBAAA;AACzC,MAAA,yBAAA,IAAA,OAAA,EAAA;AAA8B,MAAA,iBAAA,IAAA,wBAAA;AAAsB,MAAA,uBAAA,EAAM,EACtD;AAGR,MAAA,yBAAA,IAAA,OAAA,EAAA,EAAoC,IAAA,OAAA,EAAA,EACT,IAAA,OAAA,EAAA,EACQ,IAAA,QAAA,EAAA;AACL,MAAA,iBAAA,IAAA,WAAA;AAAE,MAAA,uBAAA,EAAO;AAEnC,MAAA,yBAAA,IAAA,OAAA,EAAA;AACE,MAAA,qBAAA,IAAA,qCAAA,GAAA,GAAA,QAAA,EAAA,EAA0D,IAAA,qCAAA,GAAA,GAAA,QAAA,EAAA;AAE5D,MAAA,uBAAA,EAAM;AAER,MAAA,yBAAA,IAAA,OAAA,EAAA;AACE,MAAA,qBAAA,IAAA,oCAAA,GAAA,GAAA,OAAA,EAAA,EAAiD,IAAA,oCAAA,GAAA,GAAA,OAAA,EAAA;AAIjD,MAAA,yBAAA,IAAA,OAAA,EAAA;AAAwB,MAAA,iBAAA,IAAA,oBAAA;AAAkB,MAAA,uBAAA;AAC1C,MAAA,yBAAA,IAAA,OAAA,EAAA;AAA8B,MAAA,iBAAA,IAAA,YAAA;AAAU,MAAA,uBAAA,EAAM,EAC1C;AAGR,MAAA,yBAAA,IAAA,OAAA,EAAA,EAAiC,IAAA,OAAA,EAAA,EACN,IAAA,OAAA,EAAA,EACQ,IAAA,QAAA,EAAA;AACL,MAAA,iBAAA,IAAA,WAAA;AAAE,MAAA,uBAAA,EAAO;AAEnC,MAAA,yBAAA,IAAA,OAAA,EAAA;AACE,MAAA,qBAAA,IAAA,qCAAA,GAAA,GAAA,QAAA,EAAA,EAA6D,IAAA,qCAAA,GAAA,GAAA,QAAA,EAAA;AAE/D,MAAA,uBAAA,EAAM;AAER,MAAA,yBAAA,IAAA,OAAA,EAAA;AACE,MAAA,qBAAA,IAAA,oCAAA,GAAA,GAAA,OAAA,EAAA,EAAiD,IAAA,oCAAA,GAAA,GAAA,OAAA,EAAA;AAIjD,MAAA,yBAAA,IAAA,OAAA,EAAA;AAAwB,MAAA,iBAAA,IAAA,YAAA;AAAU,MAAA,uBAAA;AAClC,MAAA,yBAAA,IAAA,OAAA,EAAA;AAA8B,MAAA,iBAAA,IAAA,UAAA;AAAQ,MAAA,uBAAA,EAAM,EACxC,EACF,EACF;AAIR,MAAA,yBAAA,IAAA,OAAA,EAAA,EAA4B,IAAA,OAAA,EAAA,EACE,IAAA,OAAA,EAAA,EAGa,IAAA,OAAA,EAAA,EACZ,IAAA,MAAA,EAAA,EACA,IAAA,QAAA,EAAA;AACG,MAAA,iBAAA,IAAA,WAAA;AAAE,MAAA,uBAAA;AAC1B,MAAA,iBAAA,IAAA,2BAAA;AACF,MAAA,uBAAA;AACA,MAAA,yBAAA,IAAA,OAAA,EAAA,EAA0B,IAAA,UAAA,EAAA,EACQ,IAAA,QAAA;AACtB,MAAA,iBAAA,IAAA,kBAAA;AAAgB,MAAA,uBAAA;AACxB,MAAA,yBAAA,IAAA,QAAA;AAAQ,MAAA,iBAAA,IAAA,mBAAA;AAAiB,MAAA,uBAAA;AACzB,MAAA,yBAAA,IAAA,QAAA;AAAQ,MAAA,iBAAA,IAAA,iBAAA;AAAe,MAAA,uBAAA,EAAS,EACzB,EACL;AAER,MAAA,yBAAA,IAAA,OAAA,EAAA,EAAuB,IAAA,OAAA,EAAA;AAGnB,MAAA,qBAAA,IAAA,oCAAA,GAAA,GAAA,OAAA,EAAA,EAAwD,IAAA,oCAAA,GAAA,GAAA,OAAA,EAAA;AAiB1D,MAAA,uBAAA,EAAM,EACF;AAIR,MAAA,yBAAA,KAAA,OAAA,EAAA,EAAwC,KAAA,OAAA,EAAA,EACb,KAAA,MAAA,EAAA,EACA,KAAA,QAAA,EAAA;AACG,MAAA,iBAAA,KAAA,WAAA;AAAE,MAAA,uBAAA;AAC1B,MAAA,iBAAA,KAAA,yBAAA;AACF,MAAA,uBAAA;AACA,MAAA,yBAAA,KAAA,KAAA,EAAA;AAAoD,MAAA,iBAAA,KAAA,WAAA;AAAS,MAAA,uBAAA,EAAI;AAEnE,MAAA,yBAAA,KAAA,OAAA,EAAA;AAEE,MAAA,qBAAA,KAAA,qCAAA,GAAA,GAAA,OAAA,EAAA,EAAmD,KAAA,qCAAA,GAAA,GAAA,OAAA,EAAA,EAM4B,KAAA,qCAAA,GAAA,GAAA,OAAA,EAAA;AAsBjF,MAAA,uBAAA,EAAM;AAIR,MAAA,yBAAA,KAAA,OAAA,EAAA,EAA0C,KAAA,OAAA,EAAA,EACf,KAAA,MAAA,EAAA,EACA,KAAA,QAAA,EAAA;AACG,MAAA,iBAAA,KAAA,WAAA;AAAE,MAAA,uBAAA;AAC1B,MAAA,iBAAA,KAAA,0BAAA;AACF,MAAA,uBAAA,EAAK;AAEP,MAAA,yBAAA,KAAA,OAAA,EAAA;AAEE,MAAA,qBAAA,KAAA,qCAAA,GAAA,GAAA,OAAA,EAAA,EAAqD,KAAA,qCAAA,GAAA,GAAA,OAAA,EAAA,EAM8B,KAAA,qCAAA,GAAA,GAAA,OAAA,EAAA;AAgBrF,MAAA,uBAAA,EAAM;AAIR,MAAA,yBAAA,KAAA,OAAA,EAAA,EAAyC,KAAA,OAAA,EAAA,EACd,KAAA,MAAA,EAAA,EACA,KAAA,QAAA,EAAA;AACG,MAAA,iBAAA,KAAA,QAAA;AAAC,MAAA,uBAAA;AACzB,MAAA,iBAAA,KAAA,mBAAA;AACF,MAAA,uBAAA,EAAK;AAEP,MAAA,yBAAA,KAAA,OAAA,EAAA,EAAuB,KAAA,OAAA,EAAA,EACK,KAAA,UAAA,EAAA,EACuC,KAAA,OAAA,EAAA;AACpC,MAAA,iBAAA,KAAA,WAAA;AAAE,MAAA,uBAAA;AAC3B,MAAA,yBAAA,KAAA,OAAA,EAAA,EAA4B,KAAA,OAAA,EAAA;AACA,MAAA,iBAAA,KAAA,UAAA;AAAQ,MAAA,uBAAA;AAClC,MAAA,yBAAA,KAAA,OAAA,EAAA;AAA6B,MAAA,iBAAA,KAAA,uBAAA;AAAkB,MAAA,uBAAA,EAAM,EACjD;AAGR,MAAA,yBAAA,KAAA,UAAA,EAAA,EAAmE,KAAA,OAAA,EAAA;AACxC,MAAA,iBAAA,KAAA,iBAAA;AAAG,MAAA,uBAAA;AAC5B,MAAA,yBAAA,KAAA,OAAA,EAAA,EAA4B,KAAA,OAAA,EAAA;AACA,MAAA,iBAAA,KAAA,oBAAA;AAAY,MAAA,uBAAA;AACtC,MAAA,yBAAA,KAAA,OAAA,EAAA;AAA6B,MAAA,iBAAA,KAAA,yBAAA;AAAoB,MAAA,uBAAA,EAAM,EACnD;AAGR,MAAA,yBAAA,KAAA,UAAA,EAAA,EAA6D,KAAA,OAAA,EAAA;AAClC,MAAA,iBAAA,KAAA,WAAA;AAAE,MAAA,uBAAA;AAC3B,MAAA,yBAAA,KAAA,OAAA,EAAA,EAA4B,KAAA,OAAA,EAAA;AACA,MAAA,iBAAA,KAAA,WAAA;AAAS,MAAA,uBAAA;AACnC,MAAA,yBAAA,KAAA,OAAA,EAAA;AAA6B,MAAA,iBAAA,KAAA,iBAAA;AAAe,MAAA,uBAAA,EAAM,EAC9C;AAGR,MAAA,yBAAA,KAAA,UAAA,EAAA,EAA8D,KAAA,OAAA,EAAA;AACnC,MAAA,iBAAA,KAAA,WAAA;AAAE,MAAA,uBAAA;AAC3B,MAAA,yBAAA,KAAA,OAAA,EAAA,EAA4B,KAAA,OAAA,EAAA;AACA,MAAA,iBAAA,KAAA,QAAA;AAAM,MAAA,uBAAA;AAChC,MAAA,yBAAA,KAAA,OAAA,EAAA;AAA6B,MAAA,iBAAA,KAAA,sBAAA;AAAiB,MAAA,uBAAA,EAAM,EAChD,EACC,EACL,EACF,EACF,EACF,EACF;;;AArRS,MAAA,oBAAA,CAAA;AAAA,MAAA,qBAAA,QAAA,IAAA,eAAA;AAGA,MAAA,oBAAA;AAAA,MAAA,qBAAA,QAAA,CAAA,IAAA,mBAAA,IAAA,WAAA;AAGA,MAAA,oBAAA;AAAA,MAAA,qBAAA,QAAA,CAAA,IAAA,mBAAA,CAAA,IAAA,WAAA;AAKA,MAAA,oBAAA,CAAA;AAAA,MAAA,qBAAA,QAAA,IAAA,eAAA;AAGA,MAAA,oBAAA;AAAA,MAAA,qBAAA,QAAA,CAAA,IAAA,eAAA;AAqBiB,MAAA,oBAAA,EAAA;AAAA,MAAA,qBAAA,WAAA,IAAA,MAAA,gBAAA,IAAA,aAAA,SAAA;AACI,MAAA,oBAAA;AAAA,MAAA,qBAAA,QAAA,IAAA,MAAA,gBAAA,CAAA;AACA,MAAA,oBAAA;AAAA,MAAA,qBAAA,QAAA,IAAA,MAAA,kBAAA,CAAA;AAIF,MAAA,oBAAA,CAAA;AAAA,MAAA,qBAAA,QAAA,CAAA,IAAA,cAAA;AACQ,MAAA,oBAAA;AAAA,MAAA,qBAAA,QAAA,IAAA,cAAA;AAaV,MAAA,oBAAA,EAAA;AAAA,MAAA,qBAAA,WAAA,IAAA,MAAA,eAAA,IAAA,aAAA,SAAA;AACI,MAAA,oBAAA;AAAA,MAAA,qBAAA,QAAA,IAAA,MAAA,eAAA,CAAA;AACA,MAAA,oBAAA;AAAA,MAAA,qBAAA,QAAA,IAAA,MAAA,iBAAA,CAAA;AAIF,MAAA,oBAAA,CAAA;AAAA,MAAA,qBAAA,QAAA,CAAA,IAAA,cAAA;AACQ,MAAA,oBAAA;AAAA,MAAA,qBAAA,QAAA,IAAA,cAAA;AAaV,MAAA,oBAAA,EAAA;AAAA,MAAA,qBAAA,WAAA,IAAA,MAAA,iBAAA,IAAA,aAAA,SAAA;AACI,MAAA,oBAAA;AAAA,MAAA,qBAAA,QAAA,IAAA,MAAA,iBAAA,CAAA;AACA,MAAA,oBAAA;AAAA,MAAA,qBAAA,QAAA,IAAA,MAAA,mBAAA,CAAA;AAIF,MAAA,oBAAA,CAAA;AAAA,MAAA,qBAAA,QAAA,CAAA,IAAA,cAAA;AACQ,MAAA,oBAAA;AAAA,MAAA,qBAAA,QAAA,IAAA,cAAA;AAaV,MAAA,oBAAA,EAAA;AAAA,MAAA,qBAAA,WAAA,IAAA,MAAA,oBAAA,IAAA,aAAA,SAAA;AACI,MAAA,oBAAA;AAAA,MAAA,qBAAA,QAAA,IAAA,MAAA,oBAAA,CAAA;AACA,MAAA,oBAAA;AAAA,MAAA,qBAAA,QAAA,IAAA,MAAA,sBAAA,CAAA;AAIF,MAAA,oBAAA,CAAA;AAAA,MAAA,qBAAA,QAAA,CAAA,IAAA,cAAA;AACQ,MAAA,oBAAA;AAAA,MAAA,qBAAA,QAAA,IAAA,cAAA;AAgC1B,MAAA,oBAAA,EAAA;AAAA,MAAA,qBAAA,QAAA,IAAA,UAAA,WAAA,CAAA;AAOA,MAAA,oBAAA;AAAA,MAAA,qBAAA,QAAA,IAAA,UAAA,SAAA,CAAA;AAyBF,MAAA,oBAAA,EAAA;AAAA,MAAA,qBAAA,QAAA,IAAA,eAAA;AAMA,MAAA,oBAAA;AAAA,MAAA,qBAAA,QAAA,CAAA,IAAA,mBAAA,IAAA,aAAA,WAAA,CAAA;AAOA,MAAA,oBAAA;AAAA,MAAA,qBAAA,QAAA,CAAA,IAAA,mBAAA,IAAA,aAAA,SAAA,CAAA;AA4BA,MAAA,oBAAA,CAAA;AAAA,MAAA,qBAAA,QAAA,IAAA,iBAAA;AAMA,MAAA,oBAAA;AAAA,MAAA,qBAAA,QAAA,CAAA,IAAA,qBAAA,IAAA,eAAA,WAAA,CAAA;AAOA,MAAA,oBAAA;AAAA,MAAA,qBAAA,QAAA,CAAA,IAAA,qBAAA,IAAA,eAAA,SAAA,CAAA;;oBDzNJ,cAAY,SAAA,SAAA,MAAA,UAAE,cAAY,UAAA,GAAA,QAAA,CAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;oDAAA,EAAA,CAAA;;;sEAIzB,oBAAkB,CAAA;UAP9B;uBACW,iBAAe,YACb,MAAI,SACP,CAAC,cAAc,YAAY,GAAC,UAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;SAAA,QAAA,CAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;CAAA,EAAA,CAAA;;;;6EAI1B,oBAAkB,EAAA,WAAA,sBAAA,UAAA,uDAAA,YAAA,GAAA,CAAA;AAAA,GAAA;", "names": []}
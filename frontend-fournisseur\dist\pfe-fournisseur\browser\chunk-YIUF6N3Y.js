import {
  environment
} from "./chunk-GFHHPDQ6.js";
import {
  HttpClient,
  HttpParams
} from "./chunk-7JDDWGD3.js";
import {
  Injectable,
  setClassMetadata,
  tap,
  ɵɵdefineInjectable,
  ɵɵinject
} from "./chunk-UBZQS7JS.js";

// src/app/services/produit.service.ts
var ProduitService = class _ProduitService {
  http;
  API_URL = `${environment.apiUrl || "https://localhost:7264/api"}/Produits`;
  constructor(http) {
    this.http = http;
  }
  /**
   * GET /api/Produits - Obtenir tous les produits
   */
  getAll(filters) {
    let params = new HttpParams();
    if (filters) {
      Object.keys(filters).forEach((key) => {
        if (filters[key] !== null && filters[key] !== void 0) {
          params = params.set(key, filters[key].toString());
        }
      });
    }
    console.log("\u{1F4E6} R\xE9cup\xE9ration des produits avec filtres:", filters);
    return this.http.get(this.API_URL, { params }).pipe(tap((response) => console.log("\u2705 Produits r\xE9cup\xE9r\xE9s:", response)));
  }
  /**
   * GET /api/Produits/{id} - Obtenir un produit par ID
   */
  getById(id) {
    console.log("\u{1F50D} R\xE9cup\xE9ration du produit ID:", id);
    return this.http.get(`${this.API_URL}/${id}`).pipe(tap((response) => console.log("\u2705 Produit r\xE9cup\xE9r\xE9:", response)));
  }
  /**
   * POST /api/Produits - Créer un nouveau produit (multipart/form-data)
   */
  create(produit) {
    console.log("\u2795 Cr\xE9ation d'un nouveau produit:", produit);
    const formData = new FormData();
    formData.append("referenceOriginal", produit.referenceOriginal);
    if (produit.referenceFournisseur) {
      formData.append("referenceFournisseur", produit.referenceFournisseur);
    }
    formData.append("codeABarre", produit.codeABarre);
    formData.append("nom", produit.nom);
    formData.append("description", produit.description);
    formData.append("prixAchatHT", produit.prixAchatHT.toString());
    formData.append("prixVenteHT", produit.prixVenteHT.toString());
    formData.append("tauxTVAId", produit.tauxTVAId.toString());
    formData.append("stock", produit.stock.toString());
    formData.append("sousCategorieId", produit.sousCategorieId.toString());
    formData.append("marqueId", produit.marqueId.toString());
    formData.append("formeId", produit.formeId.toString());
    formData.append("fournisseurId", produit.fournisseurId.toString());
    if (produit.pourcentageRemise !== void 0) {
      formData.append("pourcentageRemise", produit.pourcentageRemise.toString());
    }
    if (produit.imageFiles && produit.imageFiles.length > 0) {
      produit.imageFiles.forEach((file, index) => {
        formData.append("imageFiles", file, file.name);
      });
    }
    return this.http.post(this.API_URL, formData).pipe(tap((response) => console.log("\u2705 Produit cr\xE9\xE9:", response)));
  }
  /**
   * PUT /api/Produits/{id} - Mettre à jour un produit (multipart/form-data)
   */
  update(id, produit) {
    console.log("\u270F\uFE0F Mise \xE0 jour du produit ID:", id, produit);
    const formData = new FormData();
    formData.append("id", produit.id.toString());
    formData.append("nom", produit.nom);
    formData.append("description", produit.description);
    if (produit.prixVenteHT !== void 0) {
      formData.append("prixVenteHT", produit.prixVenteHT.toString());
    }
    if (produit.stock !== void 0) {
      formData.append("stock", produit.stock.toString());
    }
    if (produit.pourcentageRemise !== void 0) {
      formData.append("pourcentageRemise", produit.pourcentageRemise.toString());
    }
    if (produit.imageFiles && produit.imageFiles.length > 0) {
      produit.imageFiles.forEach((file, index) => {
        formData.append("imageFiles", file, file.name);
      });
    }
    return this.http.put(`${this.API_URL}/${id}`, formData).pipe(tap(() => console.log("\u2705 Produit mis \xE0 jour")));
  }
  /**
   * Supprimer un produit
   */
  delete(id) {
    return this.http.delete(`${this.API_URL}/${id}`);
  }
  /**
   * Obtenir les produits par sous-catégorie
   */
  getBySousCategorie(sousCategorieId) {
    return this.http.get(`${this.API_URL}/by-sous-categorie/${sousCategorieId}`);
  }
  /**
   * Obtenir les produits par catégorie
   */
  getByCategorie(categorieId) {
    return this.http.get(`${this.API_URL}/by-categorie/${categorieId}`);
  }
  /**
   * Obtenir les produits par fournisseur
   */
  getByFournisseur(fournisseurId) {
    return this.http.get(`${this.API_URL}/by-fournisseur/${fournisseurId}`);
  }
  /**
   * Obtenir les produits par marque
   */
  getByMarque(marqueId) {
    return this.http.get(`${this.API_URL}/by-marque/${marqueId}`);
  }
  /**
   * Rechercher des produits
   */
  search(query, filters) {
    let params = new HttpParams().set("q", query);
    if (filters) {
      Object.keys(filters).forEach((key) => {
        if (filters[key] !== null && filters[key] !== void 0) {
          params = params.set(key, filters[key].toString());
        }
      });
    }
    return this.http.get(`${this.API_URL}/search`, { params });
  }
  /**
   * GET /api/Produits/dropdown - Obtenir les produits pour dropdown
   */
  getDropdown() {
    return this.http.get(`${this.API_URL}/dropdown`);
  }
  /**
   * PATCH /api/Produits/{id}/stock - Mettre à jour le stock d'un produit
   */
  updateStock(id, stockData) {
    console.log("\u{1F4CA} Mise \xE0 jour du stock du produit ID:", id, stockData);
    return this.http.patch(`${this.API_URL}/${id}/stock`, stockData).pipe(tap(() => console.log("\u2705 Stock mis \xE0 jour")));
  }
  /**
   * PATCH /api/Produits/{id}/prix - Mettre à jour le prix d'un produit
   */
  updatePrix(id, prixData) {
    console.log("\u{1F4B0} Mise \xE0 jour du prix du produit ID:", id, prixData);
    return this.http.patch(`${this.API_URL}/${id}/prix`, prixData).pipe(tap(() => console.log("\u2705 Prix mis \xE0 jour")));
  }
  /**
   * Obtenir un produit par référence originale
   */
  getByReferenceOriginal(reference) {
    return this.http.get(`${this.API_URL}/by-reference-original/${reference}`);
  }
  /**
   * Obtenir un produit par référence fournisseur
   */
  getByReferenceFournisseur(reference) {
    return this.http.get(`${this.API_URL}/by-reference-fournisseur/${reference}`);
  }
  /**
   * Obtenir les produits par forme
   */
  getByForme(formeId) {
    return this.http.get(`${this.API_URL}/by-forme/${formeId}`);
  }
  // Gestion des images selon les endpoints backend
  /**
   * GET /api/Produits/{id}/images - Obtenir les images d'un produit
   */
  getImages(produitId) {
    return this.http.get(`${this.API_URL}/${produitId}/images`);
  }
  /**
   * POST /api/Produits/{id}/images - Ajouter une image à un produit
   */
  addImage(produitId, imageData) {
    return this.http.post(`${this.API_URL}/${produitId}/images`, imageData);
  }
  /**
   * DELETE /api/Produits/{produitId}/images/{imageId} - Supprimer une image
   */
  deleteImage(produitId, imageId) {
    return this.http.delete(`${this.API_URL}/${produitId}/images/${imageId}`);
  }
  /**
   * PUT /api/Produits/{produitId}/images/{imageId} - Mettre à jour une image
   */
  updateImage(produitId, imageId, imageData) {
    return this.http.put(`${this.API_URL}/${produitId}/images/${imageId}`, imageData);
  }
  /**
   * PATCH /api/Produits/{produitId}/images/{imageId}/main - Définir comme image principale
   */
  setMainImage(produitId, imageId) {
    return this.http.patch(`${this.API_URL}/${produitId}/images/${imageId}/main`, {});
  }
  static \u0275fac = function ProduitService_Factory(__ngFactoryType__) {
    return new (__ngFactoryType__ || _ProduitService)(\u0275\u0275inject(HttpClient));
  };
  static \u0275prov = /* @__PURE__ */ \u0275\u0275defineInjectable({ token: _ProduitService, factory: _ProduitService.\u0275fac, providedIn: "root" });
};
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && setClassMetadata(ProduitService, [{
    type: Injectable,
    args: [{
      providedIn: "root"
    }]
  }], () => [{ type: HttpClient }], null);
})();

export {
  ProduitService
};
//# sourceMappingURL=chunk-YIUF6N3Y.js.map

<div class="products-container">
  <!-- Header -->
  <div class="products-header">
    <h1>🛍️ Gestion des Produits</h1>
    <div>
      <button class="btn btn-secondary" (click)="loadData()" style="margin-right: 10px;">
        🔄 Recharger
      </button>
      <button class="btn btn-primary" (click)="openAddForm()">
        ➕ Ajouter un produit
      </button>
    </div>
  </div>

  <!-- Statistiques -->
  <div class="stats-grid">
    <div class="stat-card">
      <div class="stat-number">{{ stats().total }}</div>
      <div class="stat-label">Total Produits</div>
    </div>
    <div class="stat-card">
      <div class="stat-number">{{ stats().active }}</div>
      <div class="stat-label">En Stock</div>
    </div>
    <div class="stat-card">
      <div class="stat-number">{{ stats().outOfStock }}</div>
      <div class="stat-label">Rupture</div>
    </div>
    <div class="stat-card">
      <div class="stat-number">{{ formatPrice(stats().totalValue) }}</div>
      <div class="stat-label">Valeur Totale</div>
    </div>
  </div>

  <!-- Recherche -->
  <div class="search-box">
    <input 
      type="text" 
      placeholder="Rechercher..." 
      [value]="searchQuery()"
      (input)="onSearch($event)"
      class="search-input"
    />
  </div>

  <!-- Message d'erreur -->
  <div *ngIf="error()" class="error-message" style="background: #fee; border: 1px solid #fcc; padding: 15px; margin: 20px 0; border-radius: 5px; color: #c00;">
    <h3>❌ Erreur</h3>
    <p>{{ error() }}</p>
    <button class="btn btn-primary" (click)="loadData()">🔄 Réessayer</button>
  </div>

  <!-- En-tête avec statistiques -->
  <div class="page-header" *ngIf="!isLoading() && !error()">
    <div class="header-content">
      <h1>📦 Gestion des Produits</h1>
      <p>Gérez votre catalogue de produits et suivez vos stocks</p>
    </div>

    <div class="header-actions">
      <button class="btn btn-primary" (click)="openAddForm()">
        ➕ Nouveau Produit
      </button>
    </div>
  </div>

  <!-- Statistiques -->
  <div class="stats-section" *ngIf="products().length > 0 && !isLoading() && !error()">
    <div class="stats-grid">
      <div class="stat-card">
        <div class="stat-icon">📦</div>
        <div class="stat-content">
          <div class="stat-value">{{ products().length }}</div>
          <div class="stat-label">Produits</div>
        </div>
      </div>

      <div class="stat-card">
        <div class="stat-icon">📊</div>
        <div class="stat-content">
          <div class="stat-value">{{ getTotalStock() }}</div>
          <div class="stat-label">Unités en stock</div>
        </div>
      </div>

      <div class="stat-card">
        <div class="stat-icon">💰</div>
        <div class="stat-content">
          <div class="stat-value">{{ getTotalValue() | number:'1.0-0' }} DT</div>
          <div class="stat-label">Valeur du stock</div>
        </div>
      </div>

      <div class="stat-card alert" *ngIf="getLowStockCount() > 0">
        <div class="stat-icon">⚠️</div>
        <div class="stat-content">
          <div class="stat-value">{{ getLowStockCount() }}</div>
          <div class="stat-label">Stock faible</div>
        </div>
      </div>
    </div>
  </div>

  <!-- Barre d'outils -->
  <div class="toolbar" *ngIf="!isLoading() && !error()">
    <div class="search-section">
      <div class="search-box">
        <input
          type="text"
          [(ngModel)]="searchQuery"
          placeholder="Rechercher un produit..."
          class="form-control"
        />
        <span class="search-icon">🔍</span>
      </div>

      <div class="filters">
        <label class="filter-checkbox">
          <input type="checkbox" [(ngModel)]="filters.stockFaible" />
          <span>Stock faible</span>
        </label>
        <label class="filter-checkbox">
          <input type="checkbox" [(ngModel)]="filters.enPromotion" />
          <span>En promotion</span>
        </label>
        <label class="filter-checkbox">
          <input type="checkbox" [(ngModel)]="filters.misEnAvant" />
          <span>Mis en avant</span>
        </label>
      </div>
    </div>
  </div>

  <!-- Liste des produits -->
  <div class="products-grid" *ngIf="!isLoading() && !error(); else loadingTemplate">
    <div *ngFor="let product of currentPageProducts()" class="product-card" (click)="viewProductDetails(product)">
      <!-- Image du produit -->
      <div class="product-image">
        <img
          [src]="getMainProductImage(product)"
          [alt]="product.nom"
          (error)="onImageError($event)"
          loading="lazy"
        />
        <span *ngIf="hasPromotion(product)" class="promotion-badge">PROMO</span>
      </div>

      <div class="product-content">
        <div class="product-header">
          <h3>{{ product.nom }}</h3>
          <span class="product-status" [class]="getStatusClass(product)">
            {{ getStatusText(product) }}
          </span>
        </div>

        <div class="product-meta">
          <div class="product-category">{{ product.sousCategorie?.nom }}</div>
          <div class="product-brand" *ngIf="product.marque?.name">{{ product.marque.name }}</div>
          <div class="product-reference">
            <span class="ref-original">Réf: {{ product.referenceOriginal }}</span>
            <span *ngIf="product.referenceFournisseur" class="ref-fournisseur">
              ({{ product.referenceFournisseur }})
            </span>
          </div>
        </div>

        <p class="product-description" *ngIf="product.description">{{ product.description }}</p>
        <div class="product-details">
          <div class="price-container">
            <!-- Prix initial (barré si promotion) -->
            <div class="price-initial" *ngIf="hasPromotion(product)">
              <span class="price-label">Prix initial:</span>
              <span class="price-value initial crossed">{{ formatPrice(product.prixVenteTTC) }}</span>
            </div>

            <!-- Prix final -->
            <div class="price-final">
              <span class="price-label" *ngIf="hasPromotion(product)">Prix final:</span>
              <span class="price-value final">{{ formatPrice(getFinalPrice(product)) }}</span>
            </div>

            <!-- Badge de remise -->
            <div class="discount-badge" *ngIf="hasPromotion(product) && product.pourcentageRemiseTotale">
              -{{ product.pourcentageRemiseTotale }}%
            </div>
          </div>

          <div class="stock-container">
            <span class="stock-value" [ngClass]="{'stock-critique': product.stock <= 10}">
              {{ product.stock }} unités
            </span>
            <div class="stock-alert" *ngIf="product.stock <= 10">
              ⚠️ Stock critique
            </div>
          </div>
        </div>
        <div class="product-actions" (click)="$event.stopPropagation()">
          <button class="btn btn-sm btn-info" (click)="viewProductDetails(product)">
            👁️ Voir
          </button>
          <button class="btn btn-sm btn-primary" (click)="editProduct(product)">
            ✏️ Modifier
          </button>
          <button class="btn btn-sm btn-danger" (click)="deleteProduct(product)">
            🗑️ Supprimer
          </button>
        </div>
      </div>
    </div>
  </div>

  <!-- Template de chargement -->
  <ng-template #loadingTemplate>
    <div class="loading">
      <div class="spinner"></div>
      <p>Chargement des produits...</p>
    </div>
  </ng-template>

  <!-- Message si aucun produit -->
  <div *ngIf="currentPageProducts().length === 0 && !isLoading()" class="no-products">
    <h3>Aucun produit trouvé</h3>
    <p>Commencez par ajouter votre premier produit</p>
    <button class="btn btn-primary" (click)="openAddForm()">
      ➕ Ajouter votre premier produit
    </button>
  </div>

  <!-- Modal des détails du produit -->
  <div class="modal-overlay" *ngIf="showDetails()" (click)="closeDetails()">
    <div class="modal-content details-modal" (click)="$event.stopPropagation()">
      <div class="modal-header">
        <h3>Détails du produit</h3>
        <button type="button" class="btn btn-sm btn-outline-secondary" (click)="closeDetails()">
          ✕ Fermer
        </button>
      </div>

      <div class="product-details-content" *ngIf="selectedProductDetails()">
        <div class="details-grid">
          <!-- Informations de base -->
          <div class="detail-section">
            <h4>Informations générales</h4>
            <div class="detail-item">
              <label>Nom :</label>
              <span>{{ selectedProductDetails()!.nom }}</span>
            </div>
            <div class="detail-item">
              <label>Description :</label>
              <span>{{ selectedProductDetails()!.description }}</span>
            </div>
            <div class="detail-item">
              <label>Référence originale :</label>
              <span>{{ selectedProductDetails()!.referenceOriginal }}</span>
            </div>
            <div class="detail-item" *ngIf="selectedProductDetails()!.referenceFournisseur">
              <label>Référence fournisseur :</label>
              <span>{{ selectedProductDetails()!.referenceFournisseur }}</span>
            </div>
            <div class="detail-item">
              <label>Code à barres :</label>
              <span>{{ selectedProductDetails()!.codeABarre }}</span>
            </div>
          </div>

          <!-- Prix et stock -->
          <div class="detail-section">
            <h4>Prix et stock</h4>
            <div class="detail-item">
              <label>Prix d'achat HT :</label>
              <span>{{ formatPrice(selectedProductDetails()!.prixAchatHT) }}</span>
            </div>
            <div class="detail-item">
              <label>Prix de vente HT :</label>
              <span>{{ formatPrice(selectedProductDetails()!.prixVenteHT) }}</span>
            </div>
            <div class="detail-item">
              <label>Prix de vente TTC :</label>
              <span>{{ formatPrice(selectedProductDetails()!.prixVenteTTC) }}</span>
            </div>
            <div class="detail-item" *ngIf="selectedProductDetails()!.prixApresRemisesOutlet && selectedProductDetails()!.prixApresRemisesOutlet !== selectedProductDetails()!.prixVenteTTC">
              <label>Prix après remise outlet :</label>
              <span class="outlet-price">{{ formatPrice(selectedProductDetails()!.prixApresRemisesOutlet!) }}</span>
            </div>
            <div class="detail-item" *ngIf="hasPromotion(selectedProductDetails()!)">
              <label>Prix final après toutes promotions :</label>
              <span class="promotion-price">{{ formatPrice(selectedProductDetails()!.prixApresRemises!) }}</span>
            </div>
            <div class="detail-item">
              <label>Stock :</label>
              <span [class]="getStatusClass(selectedProductDetails()!)">{{ selectedProductDetails()!.stock }} unités</span>
            </div>
            <div class="detail-item" *ngIf="selectedProductDetails()?.pourcentageRemiseTotale && selectedProductDetails()!.pourcentageRemiseTotale! > 0">
              <label>Économie totale :</label>
              <span class="savings-badge">-{{ selectedProductDetails()!.pourcentageRemiseTotale }}%</span>
              <small class="text-muted">
                ({{ formatPrice((selectedProductDetails()!.prixVenteTTC || 0) - (selectedProductDetails()!.prixApresRemises || 0)) }} d'économie)
              </small>
            </div>
          </div>

          <!-- Catégories -->
          <div class="detail-section">
            <h4>Classifications</h4>
            <div class="detail-item" *ngIf="selectedProductDetails()!.sousCategorie">
              <label>Sous-catégorie :</label>
              <span>{{ selectedProductDetails()!.sousCategorie!.nom }}</span>
            </div>
            <div class="detail-item" *ngIf="selectedProductDetails()!.marque">
              <label>Marque :</label>
              <span>{{ selectedProductDetails()!.marque!.name }}</span>
            </div>
            <div class="detail-item" *ngIf="selectedProductDetails()!.forme">
              <label>Forme :</label>
              <span>{{ selectedProductDetails()!.forme!.nom }}</span>
            </div>
            <div class="detail-item" *ngIf="selectedProductDetails()!.tauxTVA">
              <label>Taux TVA :</label>
              <span>{{ selectedProductDetails()!.tauxTVA!.libelle }} ({{ selectedProductDetails()!.tauxTVA!.taux }}%)</span>
            </div>
          </div>

          <!-- Images -->
          <div class="detail-section" *ngIf="selectedProductDetails()!.images && selectedProductDetails()!.images!.length > 0">
            <h4>Images</h4>
            <div class="images-gallery">
              <div class="image-item" *ngFor="let image of selectedProductDetails()!.images">
                <img
                  [src]="imageUrlService.getProduitImageUrl(image.imageUrl)"
                  [alt]="image.altText || 'Image produit'"
                />
                <span *ngIf="image.isMain" class="main-badge">Principale</span>
              </div>
            </div>
          </div>
        </div>

        <div class="details-actions">
          <button class="btn btn-primary" (click)="editProduct(selectedProductDetails()!); closeDetails()">
            ✏️ Modifier ce produit
          </button>
          <button class="btn btn-secondary" (click)="closeDetails()">
            Fermer
          </button>
        </div>
      </div>
    </div>
  </div>

  <!-- Modal du formulaire -->
  <div class="modal-overlay" *ngIf="showForm()" (click)="closeForm()">
    <div class="modal-content" (click)="$event.stopPropagation()">
      <div class="modal-header">
        <h3>{{ isEditMode() ? 'Modifier' : 'Ajouter' }} un produit</h3>
        <div class="modal-actions">
          <button type="button" class="btn btn-sm btn-outline-secondary" (click)="refreshDropdowns()" title="Recharger les listes">
            🔄 Actualiser
          </button>
          <button type="button" class="btn btn-sm btn-outline-secondary" (click)="closeForm()">
            ✕ Fermer
          </button>
        </div>
      </div>
      
      <form #productForm="ngForm" name="productForm" (ngSubmit)="onSubmit(productForm)" class="product-form">
        <!-- Informations de base -->
        <div class="form-section">
          <h4>Informations de base</h4>
          
          <div class="form-row">
            <div class="form-group">
              <label>Référence originale *</label>
              <input
                type="text"
                name="referenceOriginal"
                [(ngModel)]="formData.referenceOriginal"
                required
                class="form-control"
                placeholder="REF-001"
              />
            </div>
            <div class="form-group">
              <label>Référence fournisseur</label>
              <input
                type="text"
                name="referenceFournisseur"
                [(ngModel)]="formData.referenceFournisseur"
                class="form-control"
                placeholder="FOUR-001"
              />
            </div>
          </div>

          <div class="form-row">
            <div class="form-group">
              <label>Nom du produit *</label>
              <input
                type="text"
                name="nom"
                [(ngModel)]="formData.nom"
                required
                class="form-control"
                placeholder="Nom du produit"
              />
            </div>
            <div class="form-group">
              <label>Code à barres</label>
              <input
                type="text"
                name="codeABarre"
                [(ngModel)]="formData.codeABarre"
                class="form-control"
                placeholder="Généré automatiquement"
              />
            </div>
          </div>

          <div class="form-group">
            <label>Description *</label>
            <textarea
              name="description"
              [(ngModel)]="formData.description"
              required
              class="form-control"
              rows="3"
              placeholder="Description du produit"
            ></textarea>
          </div>
        </div>

        <!-- Prix et stock -->
        <div class="form-section">
          <h4>Prix et stock</h4>
          
          <div class="form-row">
            <div class="form-group">
              <label>Prix d'achat HT (DT) *</label>
              <input
                type="number"
                name="prixAchat"
                [(ngModel)]="formData.prixAchat"
                required
                min="0"
                step="0.01"
                class="form-control"
                (input)="onPrixChange()"
              />
            </div>
            <div class="form-group">
              <label>Prix de vente HT (DT) *</label>
              <input
                type="number"
                name="prixVente"
                [(ngModel)]="formData.prixVente"
                required
                min="0"
                step="0.01"
                class="form-control"
                (input)="onPrixChange()"
              />
            </div>
          </div>

          <!-- Affichage du prix TTC calculé -->
          <div class="form-group" *ngIf="calculatedPrixTTC > 0">
            <div class="price-info">
              <strong>Prix de vente TTC calculé : {{ formatPrice(calculatedPrixTTC) }}</strong>
              <small class="text-muted">(TVA {{ selectedTauxTVA }}% incluse)</small>
            </div>
          </div>

          <div class="form-row">
            <div class="form-group">
              <label>Stock initial</label>
              <input
                type="number"
                name="stock"
                [(ngModel)]="formData.stock"
                min="0"
                class="form-control"
                placeholder="0"
              />
            </div>
            <div class="form-group">
              <label>Pourcentage de remise outlet (%)</label>
              <input
                type="number"
                name="pourcentageRemise"
                [(ngModel)]="formData.pourcentageRemise"
                min="0"
                max="100"
                step="1"
                class="form-control"
                placeholder="30"
                title="Remise outlet appliquée automatiquement (défaut: 30%)"
              />
              <small class="form-text text-muted">
                💡 Remise outlet appliquée automatiquement sur ce produit (défaut: 30%)
              </small>
            </div>
          </div>
        </div>

        <!-- Catégories et classifications -->
        <div class="form-section">
          <h4>Catégories et classifications</h4>
          
          <div class="form-row">
            <div class="form-group">
              <label>Catégorie *</label>
              <div class="input-with-button">
                <select name="categorieId" [(ngModel)]="formData.categorieId" required class="form-control" (change)="onCategorieChange($event)">
                  <option value="">Sélectionner</option>
                  <option *ngFor="let cat of categoriesDropdown()" [value]="cat.id">
                    {{ cat.nom }}
                  </option>
                </select>
              </div>
            </div>
            <div class="form-group">
              <label>Sous-catégorie * ({{ sousCategoriesDropdown().length }} disponibles)</label>
              <div class="input-with-button">
                <select name="sousCategorieId" [(ngModel)]="formData.sousCategorieId" required class="form-control">
                  <option value="">Sélectionner une sous-catégorie</option>
                  <option *ngFor="let subCat of sousCategoriesDropdown(); trackBy: trackBySousCategorieId" [value]="subCat.id">
                    {{ subCat.nom }}
                  </option>
                </select>
                <!-- Debug info -->
                <small class="debug-info" style="color: #666; font-size: 12px;">
                  Debug: {{ sousCategoriesDropdown().length }} sous-catégories chargées
                </small>
              </div>
            </div>
          </div>

          <div class="form-row">
            <div class="form-group">
              <label>Marque *</label>
              <div class="input-with-button">
                <select name="marqueId" [(ngModel)]="formData.marqueId" required class="form-control">
                  <option value="">Sélectionner</option>
                  <option *ngFor="let marque of marquesDropdown()" [value]="marque.id">
                    {{ marque.name }}
                  </option>
                </select>
              </div>
            </div>
            <div class="form-group">
              <label>Forme *</label>
              <div class="input-with-button">
                <select name="formeId" [(ngModel)]="formData.formeId" required class="form-control">
                  <option value="">Sélectionner</option>
                  <option *ngFor="let forme of formesDropdown()" [value]="forme.id">
                    {{ forme.nom }}
                  </option>
                </select>
              </div>
            </div>
          </div>

          <div class="form-row">
            <div class="form-group">
              <label>Taux TVA *</label>
              <div class="input-with-button">
                <select name="tauxTVAId" [(ngModel)]="formData.tauxTVAId" required class="form-control" (change)="onTauxTVAChange($event)">
                  <option value="">Sélectionner</option>
                  <option *ngFor="let taux of tauxTVADropdown()" [value]="taux.id">
                    {{ taux.libelle }} ({{ taux.taux }}%)
                  </option>
                </select>
              </div>
            </div>
          </div>
        </div>

        <!-- Images du produit -->
        <div class="form-section">
          <h4>Images du produit</h4>

          <!-- Images existantes (en mode édition) -->
          <div *ngIf="isEditMode() && selectedProduct()?.images?.length" class="existing-images">
            <h6>Images actuelles :</h6>
            <div class="image-preview-container">
              <div class="image-preview existing" *ngFor="let image of selectedProduct()!.images; let i = index">
                <img
                  [src]="imageUrlService.getProduitImageUrl(image.imageUrl)"
                  [alt]="image.altText || 'Image produit'"
                />
                <div class="image-info">
                  <span class="image-name">Image {{ i + 1 }}</span>
                  <span class="image-main" *ngIf="image.isMain">(Principale)</span>
                </div>
              </div>
            </div>
            <small class="form-text text-muted">
              Les nouvelles images sélectionnées ci-dessous remplaceront les images existantes.
            </small>
          </div>

          <!-- Sélection d'images une par une -->
          <div class="form-group">
            <label>{{ isEditMode() ? 'Nouvelles images' : 'Images du produit' }}</label>

            <!-- Input file caché -->
            <input
              #fileInput
              type="file"
              accept="image/*"
              style="display: none;"
              (change)="onSingleImageSelected($event)"
            />

            <!-- Bouton pour ajouter la première image ou d'autres images -->
            <div class="image-upload-actions">
              <button
                type="button"
                class="btn btn-outline-primary"
                (click)="fileInput.click()"
                [disabled]="selectedImageFiles.length >= 5"
              >
                <span *ngIf="selectedImageFiles.length === 0">📷 Choisir une image</span>
                <span *ngIf="selectedImageFiles.length > 0">➕ Ajouter autre image</span>
              </button>

              <small class="form-text text-muted" style="margin-left: 10px;">
                {{ selectedImageFiles.length }}/5 images sélectionnées
                <span *ngIf="selectedImageFiles.length >= 5"> (Maximum atteint)</span>
              </small>
            </div>

            <small class="form-text text-muted" style="margin-top: 5px;">
              {{ isEditMode() ? 'Ajoutez de nouvelles images pour remplacer les existantes.' : 'La première image sera l\'image principale.' }}
            </small>

            <!-- Prévisualisation des images sélectionnées -->
            <div class="image-preview-container" *ngIf="selectedImageFiles.length > 0" style="margin-top: 15px;">
              <div class="image-preview new" *ngFor="let file of selectedImageFiles; let i = index">
                <img [src]="getImagePreview(file)" [alt]="file.name" />
                <div class="image-info">
                  <span class="image-name">{{ file.name }}</span>
                  <span class="image-main" *ngIf="i === 0">({{ isEditMode() ? 'Nouvelle image principale' : 'Image principale' }})</span>
                  <button type="button" class="btn-remove" (click)="removeImageFile(i)" title="Supprimer cette image">×</button>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Actions -->
        <div class="form-actions">
          <button type="button" class="btn btn-secondary" (click)="closeForm()">
            Annuler
          </button>
          <button type="submit" class="btn btn-primary" [disabled]="!productForm.valid || isLoading()">
            {{ isEditMode() ? 'Modifier' : 'Ajouter' }}
          </button>
        </div>
      </form>
    </div>
  </div>
</div>

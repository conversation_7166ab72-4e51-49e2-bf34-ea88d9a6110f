{"version": 3, "sources": ["src/app/components/admin/auth/admin-login/admin-login.component.css"], "sourcesContent": ["/* 👨‍💼 Interface de Connexion Admin - Design Professionnel */\n.admin-login-page {\n  min-height: 100vh;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  background: linear-gradient(135deg, #f1f5f9 0%, #e2e8f0 100%);\n  padding: 2rem;\n}\n\n\n\n/* Container principal */\n.login-container {\n  width: 100%;\n  max-width: 450px;\n  background: rgba(255, 255, 255, 0.98);\n  backdrop-filter: blur(10px);\n  border: 1px solid rgba(255, 255, 255, 0.2);\n  border-radius: 16px;\n  box-shadow:\n    0 20px 25px -5px rgba(0, 0, 0, 0.1),\n    0 10px 10px -5px rgba(0, 0, 0, 0.04);\n  padding: 3rem;\n  position: relative;\n}\n\n.login-container::before {\n  content: '';\n  position: absolute;\n  top: 0;\n  left: 0;\n  right: 0;\n  height: 4px;\n  background: linear-gradient(90deg, #1e293b 0%, #475569 100%);\n  border-radius: 16px 16px 0 0;\n}\n\n/* Header */\n.login-header {\n  text-align: center;\n  margin-bottom: 2rem;\n}\n\n.logo-container {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  gap: 1rem;\n  margin-bottom: 2rem;\n}\n\n.logo-icon {\n  width: 60px;\n  height: 60px;\n  background: #1e293b;\n  border-radius: 12px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  color: white;\n  font-size: 1.5rem;\n}\n\n.logo-title {\n  font-size: 1.75rem;\n  font-weight: 700;\n  color: #1e293b;\n  margin: 0;\n}\n\n.login-subtitle {\n  color: #64748b;\n  font-size: 0.875rem;\n  margin: 0;\n  font-weight: 500;\n}\n\n/* Formulaire */\n.card-header {\n  text-align: center;\n  margin-bottom: 2rem;\n}\n\n.card-header h2 {\n  font-size: 1.5rem;\n  font-weight: 600;\n  color: #1e293b;\n  margin: 0 0 0.5rem 0;\n}\n\n.card-header p {\n  color: #64748b;\n  font-size: 0.875rem;\n  margin: 0;\n}\n\n/* Formulaire */\n.login-form,\n.otp-form {\n  display: flex;\n  flex-direction: column;\n  gap: 1.5rem;\n}\n\n.form-group {\n  display: flex;\n  flex-direction: column;\n  gap: 0.5rem;\n}\n\n.form-label {\n  display: flex;\n  align-items: center;\n  gap: 0.5rem;\n  font-size: 0.875rem;\n  font-weight: 500;\n  color: #374151;\n}\n\n.label-icon {\n  font-size: 1rem;\n}\n\n.form-control {\n  padding: 1rem 1.25rem;\n  border: 1px solid #d1d5db;\n  border-radius: 8px;\n  font-size: 1rem;\n  transition: all 0.2s ease;\n  background: white;\n}\n\n.form-control:focus {\n  outline: none;\n  border-color: #1e293b;\n  box-shadow: 0 0 0 3px rgba(30, 41, 59, 0.1);\n}\n\n.form-control.error {\n  border-color: #ef4444;\n  box-shadow: 0 0 0 3px rgba(239, 68, 68, 0.1);\n}\n\n.otp-input {\n  text-align: center;\n  font-size: 1.5rem;\n  font-weight: 600;\n  letter-spacing: 0.5rem;\n  font-family: monospace;\n}\n\n/* Checkbox */\n.checkbox-group {\n  margin: 0.5rem 0;\n}\n\n.checkbox-label {\n  display: flex;\n  align-items: center;\n  gap: 0.75rem;\n  cursor: pointer;\n  font-size: 0.875rem;\n  color: #374151;\n}\n\n.checkbox-input {\n  display: none;\n}\n\n.checkbox-custom {\n  width: 20px;\n  height: 20px;\n  border: 2px solid #d1d5db;\n  border-radius: 4px;\n  position: relative;\n  transition: all 0.3s ease;\n}\n\n.checkbox-input:checked + .checkbox-custom {\n  background: #667eea;\n  border-color: #667eea;\n}\n\n.checkbox-input:checked + .checkbox-custom::after {\n  content: '✓';\n  position: absolute;\n  top: 50%;\n  left: 50%;\n  transform: translate(-50%, -50%);\n  color: white;\n  font-size: 12px;\n  font-weight: bold;\n}\n\n/* Messages */\n.message-container {\n  margin: 1rem 0;\n}\n\n.error-alert,\n.success-alert {\n  display: flex;\n  align-items: center;\n  gap: 0.5rem;\n  padding: 0.75rem 1rem;\n  border-radius: 8px;\n  font-size: 0.875rem;\n  font-weight: 500;\n}\n\n.error-alert {\n  background: #fef2f2;\n  color: #dc2626;\n  border: 1px solid #fecaca;\n}\n\n.success-alert {\n  background: #f0fdf4;\n  color: #16a34a;\n  border: 1px solid #bbf7d0;\n}\n\n.error-message {\n  color: #ef4444;\n  font-size: 0.75rem;\n  margin-top: 0.25rem;\n  font-weight: 500;\n}\n\n/* Boutons */\n.login-button,\n.verify-button {\n  width: 100%;\n  padding: 1rem;\n  background: #1e293b;\n  color: white;\n  border: none;\n  border-radius: 8px;\n  font-size: 1rem;\n  font-weight: 600;\n  cursor: pointer;\n  transition: all 0.2s ease;\n}\n\n.login-button:hover:not(:disabled),\n.verify-button:hover:not(:disabled) {\n  background: #334155;\n  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);\n}\n\n.login-button:disabled,\n.verify-button:disabled {\n  opacity: 0.6;\n  cursor: not-allowed;\n  background: #94a3b8;\n}\n\n.button-content {\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  gap: 0.5rem;\n}\n\n.button-icon {\n  font-size: 1.125rem;\n}\n\n.loading-spinner {\n  width: 20px;\n  height: 20px;\n  border: 2px solid rgba(255, 255, 255, 0.3);\n  border-top: 2px solid white;\n  border-radius: 50%;\n}\n\n/* Boutons OTP */\n.otp-buttons {\n  display: flex;\n  gap: 1rem;\n  margin-top: 1rem;\n}\n\n.back-button {\n  flex: 1;\n  padding: 0.875rem;\n  background: #f8fafc;\n  color: #64748b;\n  border: 2px solid #e2e8f0;\n  border-radius: 12px;\n  font-size: 0.875rem;\n  font-weight: 500;\n  cursor: pointer;\n  transition: all 0.3s ease;\n}\n\n.back-button:hover:not(:disabled) {\n  background: #e2e8f0;\n  color: #475569;\n}\n\n.verify-button {\n  flex: 2;\n}\n\n/* Informations de connexion */\n.login-info {\n  margin-top: 2rem;\n  padding-top: 1.5rem;\n  border-top: 1px solid #e5e7eb;\n  text-align: center;\n}\n\n.info-title {\n  font-size: 0.875rem;\n  color: #64748b;\n  margin: 0 0 1rem 0;\n  font-weight: 500;\n}\n\n.info-content {\n  text-align: center;\n}\n\n.info-content p {\n  font-size: 0.75rem;\n  color: #64748b;\n  margin: 0.5rem 0;\n}\n\n.info-content strong {\n  color: #374151;\n  font-weight: 600;\n}\n\n/* Footer */\n.login-footer {\n  text-align: center;\n  margin-top: 2rem;\n  color: rgba(255, 255, 255, 0.8);\n}\n\n.footer-text {\n  font-size: 0.75rem;\n  margin: 0 0 1rem 0;\n}\n\n.security-badges {\n  display: flex;\n  justify-content: center;\n  gap: 1rem;\n}\n\n.security-badge {\n  font-size: 0.75rem;\n  padding: 0.25rem 0.5rem;\n  background: rgba(255, 255, 255, 0.1);\n  border-radius: 12px;\n  backdrop-filter: blur(10px);\n}\n\n/* Responsive */\n@media (max-width: 768px) {\n  .login-container {\n    padding: 1rem;\n    max-width: 100%;\n  }\n\n  .login-card {\n    padding: 2rem;\n  }\n\n  .logo-title {\n    font-size: 1.5rem;\n  }\n\n  .otp-buttons {\n    flex-direction: column;\n  }\n\n  .security-badges {\n    flex-wrap: wrap;\n    gap: 0.5rem;\n  }\n}\n"], "mappings": ";AACA,CAAC;AACC,cAAY;AACZ,WAAS;AACT,eAAa;AACb,mBAAiB;AACjB;AAAA,IAAY;AAAA,MAAgB,MAAhB;AAAA,MAAwB,QAAQ,EAAhC;AAAA,MAAoC,QAAQ;AACxD,WAAS;AACX;AAKA,CAAC;AACC,SAAO;AACP,aAAW;AACX,cAAY,KAAK,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE;AAChC,2BAAiB,KAAK;AAAtB,mBAAiB,KAAK;AACtB,UAAQ,IAAI,MAAM,KAAK,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE;AACtC,iBAAe;AACf,cACE,EAAE,KAAK,KAAK,KAAK,KAAK,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,IAAI,EACnC,EAAE,KAAK,KAAK,KAAK,KAAK,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE;AACjC,WAAS;AACT,YAAU;AACZ;AAEA,CAdC,eAce;AACd,WAAS;AACT,YAAU;AACV,OAAK;AACL,QAAM;AACN,SAAO;AACP,UAAQ;AACR;AAAA,IAAY;AAAA,MAAgB,KAAhB;AAAA,MAAuB,QAAQ,EAA/B;AAAA,MAAmC,QAAQ;AACvD,iBAAe,KAAK,KAAK,EAAE;AAC7B;AAGA,CAAC;AACC,cAAY;AACZ,iBAAe;AACjB;AAEA,CAAC;AACC,WAAS;AACT,kBAAgB;AAChB,eAAa;AACb,OAAK;AACL,iBAAe;AACjB;AAEA,CAAC;AACC,SAAO;AACP,UAAQ;AACR,cAAY;AACZ,iBAAe;AACf,WAAS;AACT,eAAa;AACb,mBAAiB;AACjB,SAAO;AACP,aAAW;AACb;AAEA,CAAC;AACC,aAAW;AACX,eAAa;AACb,SAAO;AACP,UAAQ;AACV;AAEA,CAAC;AACC,SAAO;AACP,aAAW;AACX,UAAQ;AACR,eAAa;AACf;AAGA,CAAC;AACC,cAAY;AACZ,iBAAe;AACjB;AAEA,CALC,YAKY;AACX,aAAW;AACX,eAAa;AACb,SAAO;AACP,UAAQ,EAAE,EAAE,OAAO;AACrB;AAEA,CAZC,YAYY;AACX,SAAO;AACP,aAAW;AACX,UAAQ;AACV;AAGA,CAAC;AACD,CAAC;AACC,WAAS;AACT,kBAAgB;AAChB,OAAK;AACP;AAEA,CAAC;AACC,WAAS;AACT,kBAAgB;AAChB,OAAK;AACP;AAEA,CAAC;AACC,WAAS;AACT,eAAa;AACb,OAAK;AACL,aAAW;AACX,eAAa;AACb,SAAO;AACT;AAEA,CAAC;AACC,aAAW;AACb;AAEA,CAAC;AACC,WAAS,KAAK;AACd,UAAQ,IAAI,MAAM;AAClB,iBAAe;AACf,aAAW;AACX,cAAY,IAAI,KAAK;AACrB,cAAY;AACd;AAEA,CATC,YASY;AACX,WAAS;AACT,gBAAc;AACd,cAAY,EAAE,EAAE,EAAE,IAAI,KAAK,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE;AACzC;AAEA,CAfC,YAeY,CAAC;AACZ,gBAAc;AACd,cAAY,EAAE,EAAE,EAAE,IAAI,KAAK,GAAG,EAAE,EAAE,EAAE,EAAE,EAAE;AAC1C;AAEA,CAAC;AACC,cAAY;AACZ,aAAW;AACX,eAAa;AACb,kBAAgB;AAChB,eAAa;AACf;AAGA,CAAC;AACC,UAAQ,OAAO;AACjB;AAEA,CAAC;AACC,WAAS;AACT,eAAa;AACb,OAAK;AACL,UAAQ;AACR,aAAW;AACX,SAAO;AACT;AAEA,CAAC;AACC,WAAS;AACX;AAEA,CAAC;AACC,SAAO;AACP,UAAQ;AACR,UAAQ,IAAI,MAAM;AAClB,iBAAe;AACf,YAAU;AACV,cAAY,IAAI,KAAK;AACvB;AAEA,CAbC,cAac,SAAS,EAAE,CATzB;AAUC,cAAY;AACZ,gBAAc;AAChB;AAEA,CAlBC,cAkBc,SAAS,EAAE,CAdzB,eAcyC;AACxC,WAAS;AACT,YAAU;AACV,OAAK;AACL,QAAM;AACN,aAAW,UAAU,IAAI,EAAE;AAC3B,SAAO;AACP,aAAW;AACX,eAAa;AACf;AAGA,CAAC;AACC,UAAQ,KAAK;AACf;AAEA,CAAC;AACD,CAAC;AACC,WAAS;AACT,eAAa;AACb,OAAK;AACL,WAAS,QAAQ;AACjB,iBAAe;AACf,aAAW;AACX,eAAa;AACf;AAEA,CAXC;AAYC,cAAY;AACZ,SAAO;AACP,UAAQ,IAAI,MAAM;AACpB;AAEA,CAhBC;AAiBC,cAAY;AACZ,SAAO;AACP,UAAQ,IAAI,MAAM;AACpB;AAEA,CAAC;AACC,SAAO;AACP,aAAW;AACX,cAAY;AACZ,eAAa;AACf;AAGA,CAAC;AACD,CAAC;AACC,SAAO;AACP,WAAS;AACT,cAAY;AACZ,SAAO;AACP,UAAQ;AACR,iBAAe;AACf,aAAW;AACX,eAAa;AACb,UAAQ;AACR,cAAY,IAAI,KAAK;AACvB;AAEA,CAdC,YAcY,MAAM,KAAK;AACxB,CAdC,aAca,MAAM,KAAK;AACvB,cAAY;AACZ,cAAY,EAAE,IAAI,IAAI,KAAK,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE;AACtC;AAEA,CApBC,YAoBY;AACb,CApBC,aAoBa;AACZ,WAAS;AACT,UAAQ;AACR,cAAY;AACd;AAEA,CAAC;AACC,WAAS;AACT,eAAa;AACb,mBAAiB;AACjB,OAAK;AACP;AAEA,CAAC;AACC,aAAW;AACb;AAEA,CAAC;AACC,SAAO;AACP,UAAQ;AACR,UAAQ,IAAI,MAAM,KAAK,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE;AACtC,cAAY,IAAI,MAAM;AACtB,iBAAe;AACjB;AAGA,CAAC;AACC,WAAS;AACT,OAAK;AACL,cAAY;AACd;AAEA,CAAC;AACC,QAAM;AACN,WAAS;AACT,cAAY;AACZ,SAAO;AACP,UAAQ,IAAI,MAAM;AAClB,iBAAe;AACf,aAAW;AACX,eAAa;AACb,UAAQ;AACR,cAAY,IAAI,KAAK;AACvB;AAEA,CAbC,WAaW,MAAM,KAAK;AACrB,cAAY;AACZ,SAAO;AACT;AAEA,CAtEC;AAuEC,QAAM;AACR;AAGA,CAAC;AACC,cAAY;AACZ,eAAa;AACb,cAAY,IAAI,MAAM;AACtB,cAAY;AACd;AAEA,CAAC;AACC,aAAW;AACX,SAAO;AACP,UAAQ,EAAE,EAAE,KAAK;AACjB,eAAa;AACf;AAEA,CAAC;AACC,cAAY;AACd;AAEA,CAJC,aAIa;AACZ,aAAW;AACX,SAAO;AACP,UAAQ,OAAO;AACjB;AAEA,CAVC,aAUa;AACZ,SAAO;AACP,eAAa;AACf;AAGA,CAAC;AACC,cAAY;AACZ,cAAY;AACZ,SAAO,KAAK,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE;AAC7B;AAEA,CAAC;AACC,aAAW;AACX,UAAQ,EAAE,EAAE,KAAK;AACnB;AAEA,CAAC;AACC,WAAS;AACT,mBAAiB;AACjB,OAAK;AACP;AAEA,CAAC;AACC,aAAW;AACX,WAAS,QAAQ;AACjB,cAAY,KAAK,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE;AAChC,iBAAe;AACf,2BAAiB,KAAK;AAAtB,mBAAiB,KAAK;AACxB;AAGA,OAAO,CAAC,SAAS,EAAE;AACjB,GA/VD;AAgWG,aAAS;AACT,eAAW;AACb;AAEA,GAAC;AACC,aAAS;AACX;AAEA,GArTD;AAsTG,eAAW;AACb;AAEA,GAnGD;AAoGG,oBAAgB;AAClB;AAEA,GAjCD;AAkCG,eAAW;AACX,SAAK;AACP;AACF;", "names": []}
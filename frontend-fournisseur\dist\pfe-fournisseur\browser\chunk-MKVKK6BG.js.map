{"version": 3, "sources": ["src/app/components/auth/register/register.component.ts", "src/app/components/auth/register/register.component.html"], "sourcesContent": ["import { Component } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { FormBuilder, FormGroup, Validators, ReactiveFormsModule } from '@angular/forms';\nimport { Router } from '@angular/router';\nimport { AuthService } from '../../../services/auth.service';\n\nexport interface RegisterFournisseurRequest {\n  // Champs de UtilisateurCreateDto\n  email: string;\n  password: string;\n  nom: string;\n  prenom: string;\n  phoneNumber: string;\n  dateNaissance: string;\n\n  // Champs spécifiques au fournisseur\n  matriculeFiscale: string;\n  raisonSociale: string;\n  description?: string;\n  rib: string;\n  codeBanque: string;\n  commission?: number;\n  delaiPreparationJours?: number;\n  estActif?: boolean;\n  fraisLivraisonBase?: number;\n\n  // Adresse de l'entreprise (selon AdresseCreateDto backend)\n  rue: string;\n  ville: string;\n  codePostal: string;\n  pays: string;\n\n  logoFile: File;\n}\n\n@Component({\n  selector: 'app-register',\n  standalone: true,\n  imports: [CommonModule, ReactiveFormsModule],\n  templateUrl: './register.component.html',\n  styleUrls: ['./register.component.css']\n})\nexport class RegisterComponent {\n  registerForm: FormGroup;\n  isLoading = false;\n  errorMessage = '';\n  successMessage = '';\n\n  constructor(\n    private formBuilder: FormBuilder,\n    private authService: AuthService,\n    private router: Router\n  ) {\n    this.registerForm = this.formBuilder.group({\n      // Informations personnelles\n      email: ['', [Validators.required, Validators.email]],\n      password: ['', [Validators.required, Validators.minLength(8), Validators.maxLength(100)]],\n      confirmPassword: ['', [Validators.required]],\n      nom: ['', [Validators.required, Validators.minLength(2), Validators.maxLength(50)]],\n      prenom: ['', [Validators.required, Validators.minLength(2), Validators.maxLength(50)]],\n      phoneNumber: ['', [Validators.required, Validators.pattern(/^\\d{8,15}$/)]],\n      dateNaissance: ['', [Validators.required]],\n\n      // Informations entreprise\n      matriculeFiscale: ['', [Validators.required, Validators.pattern(/^\\d{8}$/)]],\n      raisonSociale: ['', [Validators.required, Validators.minLength(2), Validators.maxLength(200)]],\n      description: [''],\n      rib: ['', [Validators.required, Validators.pattern(/^\\d{20}$/)]],\n      codeBanque: ['', [Validators.required, Validators.pattern(/^\\d{3}$/)]],\n      commission: [0.75, [Validators.required, Validators.min(0.5), Validators.max(1)]],\n      delaiPreparationJours: [2, [Validators.required, Validators.min(1), Validators.max(30)]],\n      fraisLivraisonBase: [9.99, [Validators.required, Validators.min(0)]],\n\n      // Adresse de l'entreprise (selon AdresseCreateDto backend)\n      rue: ['', [Validators.required, Validators.minLength(5)]],\n      ville: ['', [Validators.required, Validators.minLength(2)]],\n      codePostal: ['', [Validators.required, Validators.pattern(/^\\d{4}$/)]],\n      pays: ['Tunisie', [Validators.required]],\n\n      logoFile: [null] // Rendu optionnel car on ajoute un logo par défaut\n    }, { validators: this.passwordMatchValidator });\n  }\n\n  passwordMatchValidator(form: FormGroup) {\n    const password = form.get('password');\n    const confirmPassword = form.get('confirmPassword');\n\n    if (password && confirmPassword && password.value !== confirmPassword.value) {\n      confirmPassword.setErrors({ passwordMismatch: true });\n      return { passwordMismatch: true };\n    }\n\n    return null;\n  }\n\n  validateFormData(formValue: any): boolean {\n    // Validation du matricule fiscal (8 chiffres)\n    if (!/^\\d{8}$/.test(formValue.matriculeFiscale)) {\n      this.errorMessage = 'Le matricule fiscal doit contenir exactement 8 chiffres.';\n      return false;\n    }\n\n    // Validation du code banque (3 chiffres)\n    if (!/^\\d{3}$/.test(formValue.codeBanque)) {\n      this.errorMessage = 'Le code banque doit contenir exactement 3 chiffres.';\n      return false;\n    }\n\n    // Validation du RIB (20 chiffres et doit commencer par le code banque)\n    if (!/^\\d{20}$/.test(formValue.rib)) {\n      this.errorMessage = 'Le RIB doit contenir exactement 20 chiffres.';\n      return false;\n    }\n\n    if (!formValue.rib.startsWith(formValue.codeBanque)) {\n      this.errorMessage = 'Le RIB doit commencer par le code banque.';\n      return false;\n    }\n\n    // Validation du téléphone\n    if (!/^\\d{8,15}$/.test(formValue.phoneNumber)) {\n      this.errorMessage = 'Le numéro de téléphone doit contenir entre 8 et 15 chiffres.';\n      return false;\n    }\n\n    return true;\n  }\n\n  getFieldDisplayName(fieldName: string): string {\n    const fieldNames: { [key: string]: string } = {\n      'email': 'Email',\n      'password': 'Mot de passe',\n      'nom': 'Nom',\n      'prenom': 'Prénom',\n      'phoneNumber': 'Téléphone',\n      'dateNaissance': 'Date de naissance',\n      'matriculeFiscale': 'Matricule fiscal',\n      'raisonSociale': 'Raison sociale',\n      'rib': 'RIB',\n      'codeBanque': 'Code banque',\n      'description': 'Description',\n      'commission': 'Commission',\n      'delaiPreparationJours': 'Délai de préparation',\n      'fraisLivraisonBase': 'Frais de livraison',\n      'logoFile': 'Logo'\n    };\n\n    return fieldNames[fieldName] || fieldName;\n  }\n\n  onSubmit(): void {\n    if (this.registerForm.valid) {\n      this.isLoading = true;\n      this.errorMessage = '';\n      this.successMessage = '';\n\n      const formValue = this.registerForm.value;\n\n      // Validation côté client avant envoi\n      if (!this.validateFormData(formValue)) {\n        this.isLoading = false;\n        return;\n      }\n\n      // Créer un FormData pour l'envoi avec fichier\n      const formData = new FormData();\n\n      // Ajouter tous les champs au FormData selon l'API (camelCase)\n      formData.append('email', formValue.email);\n      formData.append('password', formValue.password);\n      formData.append('nom', formValue.nom);\n      formData.append('prenom', formValue.prenom);\n      formData.append('phoneNumber', formValue.phoneNumber);\n\n      // Format de date ISO pour le backend\n      if (formValue.dateNaissance) {\n        const date = new Date(formValue.dateNaissance);\n        formData.append('dateNaissance', date.toISOString());\n      }\n\n      formData.append('matriculeFiscale', formValue.matriculeFiscale);\n      formData.append('raisonSociale', formValue.raisonSociale);\n      formData.append('rib', formValue.rib); // Corrigé : le backend utilise camelCase\n      formData.append('codeBanque', formValue.codeBanque);\n\n      if (formValue.description) {\n        formData.append('description', formValue.description);\n      }\n\n      formData.append('commission', formValue.commission?.toString() || '0.75');\n      formData.append('delaiPreparationJours', formValue.delaiPreparationJours?.toString() || '2');\n      formData.append('fraisLivraisonBase', formValue.fraisLivraisonBase?.toString() || '9.99');\n      formData.append('estActif', 'true');\n\n      // Ajouter les champs d'adresse (selon le format attendu par le backend)\n      if (formValue.rue) {\n        formData.append('adresseRue', formValue.rue);\n      }\n      if (formValue.ville) {\n        formData.append('adresseVille', formValue.ville);\n      }\n      if (formValue.codePostal) {\n        formData.append('adresseCodePostal', formValue.codePostal);\n      }\n      if (formValue.pays) {\n        formData.append('adressePays', formValue.pays);\n      }\n      formData.append('adresseEstPrincipale', 'true'); // Première adresse = principale\n\n      // Ajouter le fichier logo\n      if (formValue.logoFile) {\n        formData.append('logoFile', formValue.logoFile);\n      } else {\n        // Créer un fichier logo par défaut si aucun n'est sélectionné\n        const defaultLogoBlob = new Blob([\n          new Uint8Array([\n            0x89, 0x50, 0x4E, 0x47, 0x0D, 0x0A, 0x1A, 0x0A, 0x00, 0x00, 0x00, 0x0D,\n            0x49, 0x48, 0x44, 0x52, 0x00, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, 0x01,\n            0x08, 0x06, 0x00, 0x00, 0x00, 0x1F, 0x15, 0xC4, 0x89, 0x00, 0x00, 0x00,\n            0x0A, 0x49, 0x44, 0x41, 0x54, 0x78, 0x9C, 0x63, 0x00, 0x01, 0x00, 0x00,\n            0x05, 0x00, 0x01, 0x0D, 0x0A, 0x2D, 0xB4, 0x00, 0x00, 0x00, 0x00, 0x49,\n            0x45, 0x4E, 0x44, 0xAE, 0x42, 0x60, 0x82\n          ])\n        ], { type: 'image/png' });\n        \n        const defaultLogoFile = new File([defaultLogoBlob], 'default-logo.png', { type: 'image/png' });\n        formData.append('logoFile', defaultLogoFile);\n        console.log('📝 Logo par défaut ajouté car aucun logo n\\'a été sélectionné');\n      }\n\n      // Debug: Afficher le contenu du FormData\n      console.log('📋 FormData final:');\n      for (let pair of formData.entries()) {\n        const key = pair[0];\n        const value = pair[1];\n        if (value instanceof File) {\n          console.log(`${key}: [File] ${value.name} (${value.size} bytes, ${value.type})`);\n        } else {\n          console.log(`${key}: ${value}`);\n        }\n      }\n\n      this.authService.registerFournisseur(formData).subscribe({\n        next: () => {\n          this.isLoading = false;\n          this.successMessage = 'Inscription réussie ! Vous pouvez maintenant vous connecter.';\n\n          // Rediriger vers la page de login après 2 secondes\n          setTimeout(() => {\n            this.router.navigate(['/login']);\n          }, 2000);\n        },\n        error: (error) => {\n          this.isLoading = false;\n          \n          // Gestion détaillée des erreurs\n          if (error.status === 400) {\n            console.error('❌ Erreur 400 - Détails:', error.error);\n            \n            // Essayer de parser les erreurs de validation\n            if (error.error && typeof error.error === 'object') {\n              if (error.error.errors) {\n                // Format: { message: \"Validation failed\", errors: { field: [\"error1\", \"error2\"] } }\n                const validationErrors = error.error.errors;\n                let errorMessages: string[] = [];\n\n                Object.keys(validationErrors).forEach(key => {\n                  const fieldErrors = validationErrors[key];\n                  if (Array.isArray(fieldErrors)) {\n                    errorMessages.push(`${this.getFieldDisplayName(key)}: ${fieldErrors.join(', ')}`);\n                  } else if (typeof fieldErrors === 'string') {\n                    errorMessages.push(`${this.getFieldDisplayName(key)}: ${fieldErrors}`);\n                  }\n                });\n\n                if (errorMessages.length > 0) {\n                  this.errorMessage = 'Erreurs de validation:\\n' + errorMessages.join('\\n');\n                } else {\n                  this.errorMessage = error.error.message || 'Erreur de validation. Vérifiez vos données.';\n                }\n              } else {\n                // Ancien format ou format différent\n                const validationErrors = error.error;\n                let errorMessages: string[] = [];\n\n                Object.keys(validationErrors).forEach(key => {\n                  const fieldErrors = validationErrors[key];\n                  if (Array.isArray(fieldErrors)) {\n                    errorMessages.push(`${this.getFieldDisplayName(key)}: ${fieldErrors.join(', ')}`);\n                  } else if (typeof fieldErrors === 'string') {\n                    errorMessages.push(`${this.getFieldDisplayName(key)}: ${fieldErrors}`);\n                  }\n                });\n\n                if (errorMessages.length > 0) {\n                  this.errorMessage = 'Erreurs de validation:\\n' + errorMessages.join('\\n');\n                } else {\n                  this.errorMessage = error.error?.message || 'Erreur de validation. Vérifiez vos données.';\n                }\n              }\n            } else {\n              this.errorMessage = error.error?.message || 'Erreur lors de l\\'inscription. Veuillez réessayer.';\n            }\n          } else {\n            this.errorMessage = error.error?.message || 'Erreur lors de l\\'inscription. Veuillez réessayer.';\n          }\n        }\n      });\n    } else {\n      this.markFormGroupTouched();\n    }\n  }\n\n  private markFormGroupTouched(): void {\n    Object.keys(this.registerForm.controls).forEach(key => {\n      const control = this.registerForm.get(key);\n      control?.markAsTouched();\n    });\n  }\n\n  goToLogin(): void {\n    this.router.navigate(['/login']);\n  }\n\n  onFileSelected(event: any): void {\n    const file = event.target.files[0];\n    if (file) {\n      this.registerForm.patchValue({\n        logoFile: file\n      });\n    }\n  }\n\n  testBackendConnection(): void {\n    console.log('🧪 Test de connectivité backend...');\n\n    // Test simple avec les données du formulaire\n    const formValue = this.registerForm.value;\n\n    // Créer un objet de test sans fichier (avec noms corrects selon API camelCase)\n    const testData = {\n      email: formValue.email || '<EMAIL>',\n      password: formValue.password || 'Test123!',\n      nom: formValue.nom || 'TestNom',\n      prenom: formValue.prenom || 'TestPrenom',\n      phoneNumber: formValue.phoneNumber || '1234567890',\n      dateNaissance: formValue.dateNaissance || '2025-06-17T06:50:12.478Z',\n      matriculeFiscale: formValue.matriculeFiscale || '12345678',\n      raisonSociale: formValue.raisonSociale || 'Test Company',\n      rib: formValue.rib || '12312345678901234567', // RIB qui commence par le code banque 123\n      codeBanque: formValue.codeBanque || '123',\n      description: formValue.description || 'Test description',\n      commission: 0.75,\n      delaiPreparationJours: 2,\n      fraisLivraisonBase: 9.99,\n      estActif: true\n    };\n\n    console.log('🧪 Test data:', testData);\n\n    // Test 1: FormData avec logo par défaut\n    console.log('🧪 Test 1: FormData avec logo par défaut...');\n    this.authService.registerFournisseurJSON(testData).subscribe({\n      next: (response) => {\n        console.log('✅ Test FormData réussi:', response);\n        alert('✅ Test FormData réussi ! Le backend fonctionne.\\nLe problème vient probablement du fichier logo ou des données.');\n\n        // Si FormData fonctionne, tester avec les vraies données du formulaire\n        this.testWithRealFormData();\n      },\n      error: (error) => {\n        console.error('❌ Test FormData échoué:', error);\n\n        if (error.status === 415) {\n          alert('❌ Erreur 415 avec FormData.\\nLe backend ne supporte peut-être pas cet endpoint ou il manque des headers.');\n        } else if (error.status === 400) {\n          alert('✅ Le backend répond (erreur 400).\\nLe problème vient probablement des données ou de la validation.');\n        } else {\n          alert(`❌ Test FormData échoué: ${error.status} - ${error.statusText}\\nVoir la console pour plus de détails.`);\n        }\n      }\n    });\n  }\n\n  testWithRealFormData(): void {\n    console.log('🧪 Test 2: Avec les vraies données du formulaire...');\n\n    if (!this.registerForm.valid) {\n      alert('❌ Le formulaire n\\'est pas valide. Veuillez remplir tous les champs requis.');\n      return;\n    }\n\n    const formValue = this.registerForm.value;\n    const formData = new FormData();\n\n    // Ajouter tous les champs au FormData\n    formData.append('Email', formValue.email);\n    formData.append('Password', formValue.password);\n    formData.append('Nom', formValue.nom);\n    formData.append('Prenom', formValue.prenom);\n    formData.append('Telephone', formValue.phoneNumber);\n    if (formValue.dateNaissance) {\n      formData.append('DateNaissance', formValue.dateNaissance);\n    }\n    formData.append('MatriculeFiscale', formValue.matriculeFiscale);\n    formData.append('RaisonSociale', formValue.raisonSociale);\n    formData.append('Rib', formValue.rib);\n    formData.append('CodeBanque', formValue.codeBanque);\n\n    if (formValue.description) {\n      formData.append('Description', formValue.description);\n    }\n\n    formData.append('Commission', formValue.commission?.toString() || '0.75');\n    formData.append('DelaiPreparationJours', formValue.delaiPreparationJours?.toString() || '2');\n    formData.append('FraisLivraisonBase', formValue.fraisLivraisonBase?.toString() || '9.99');\n    formData.append('EstActif', 'true');\n\n    // Ajouter le fichier logo\n    if (formValue.logoFile) {\n      formData.append('LogoFile', formValue.logoFile);\n    } else {\n      // Créer un fichier logo par défaut si aucun n'est sélectionné\n      const defaultLogoBlob = new Blob([\n        new Uint8Array([\n          0x89, 0x50, 0x4E, 0x47, 0x0D, 0x0A, 0x1A, 0x0A, 0x00, 0x00, 0x00, 0x0D,\n          0x49, 0x48, 0x44, 0x52, 0x00, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, 0x01,\n          0x08, 0x06, 0x00, 0x00, 0x00, 0x1F, 0x15, 0xC4, 0x89, 0x00, 0x00, 0x00,\n          0x0A, 0x49, 0x44, 0x41, 0x54, 0x78, 0x9C, 0x63, 0x00, 0x01, 0x00, 0x00,\n          0x05, 0x00, 0x01, 0x0D, 0x0A, 0x2D, 0xB4, 0x00, 0x00, 0x00, 0x00, 0x49,\n          0x45, 0x4E, 0x44, 0xAE, 0x42, 0x60, 0x82\n        ])\n      ], { type: 'image/png' });\n      \n      const defaultLogoFile = new File([defaultLogoBlob], 'default-logo.png', { type: 'image/png' });\n      formData.append('LogoFile', defaultLogoFile);\n    }\n\n    console.log('🧪 FormData avec vraies données:');\n    for (let pair of formData.entries()) {\n      const key = pair[0];\n      const value = pair[1];\n      if (value instanceof File) {\n        console.log(`${key}: [File] ${value.name} (${value.size} bytes, ${value.type})`);\n      } else {\n        console.log(`${key}: ${value}`);\n      }\n    }\n\n    this.authService.registerFournisseur(formData).subscribe({\n      next: (response) => {\n        console.log('✅ Test avec vraies données réussi:', response);\n        alert('✅ Test avec vraies données réussi !\\nL\\'inscription devrait fonctionner maintenant.');\n      },\n      error: (error) => {\n        console.error('❌ Test avec vraies données échoué:', error);\n        alert(`❌ Test avec vraies données échoué: ${error.status} - ${error.statusText}\\nVoir la console pour plus de détails.`);\n      }\n    });\n  }\n\n  testMinimalData(): void {\n    console.log('🧪 Test avec données minimales...');\n    \n    this.authService.testEndpointWithMinimalData().subscribe({\n      next: (response) => {\n        console.log('✅ Test minimal réussi:', response);\n        alert('✅ Test minimal réussi !\\nLe backend fonctionne avec FormData et logo par défaut.');\n      },\n      error: (error) => {\n        console.error('❌ Test minimal échoué:', error);\n        alert(`❌ Test minimal échoué: ${error.status} - ${error.statusText}\\nVoir la console pour plus de détails.`);\n      }\n    });\n  }\n\n  // Getters pour faciliter l'accès aux contrôles dans le template\n  get email() { return this.registerForm.get('email'); }\n  get password() { return this.registerForm.get('password'); }\n  get confirmPassword() { return this.registerForm.get('confirmPassword'); }\n  get nom() { return this.registerForm.get('nom'); }\n  get prenom() { return this.registerForm.get('prenom'); }\n  get phoneNumber() { return this.registerForm.get('phoneNumber'); }\n  get dateNaissance() { return this.registerForm.get('dateNaissance'); }\n  get matriculeFiscale() { return this.registerForm.get('matriculeFiscale'); }\n  get raisonSociale() { return this.registerForm.get('raisonSociale'); }\n  get description() { return this.registerForm.get('description'); }\n  get rib() { return this.registerForm.get('rib'); }\n  get codeBanque() { return this.registerForm.get('codeBanque'); }\n  get commission() { return this.registerForm.get('commission'); }\n  get delaiPreparationJours() { return this.registerForm.get('delaiPreparationJours'); }\n  get fraisLivraisonBase() { return this.registerForm.get('fraisLivraisonBase'); }\n\n  // Getters pour les champs d'adresse (selon AdresseCreateDto backend)\n  get rue() { return this.registerForm.get('rue'); }\n  get ville() { return this.registerForm.get('ville'); }\n  get codePostal() { return this.registerForm.get('codePostal'); }\n  get pays() { return this.registerForm.get('pays'); }\n\n  get logoFile() { return this.registerForm.get('logoFile'); }\n}\n", "<div class=\"register-container\">\n  <div class=\"register-card\">\n    <div class=\"register-header\">\n      <h1>Inscription Fournisseur</h1>\n      <p><PERSON><PERSON>ez votre compte fournisseur</p>\n    </div>\n\n    <form [formGroup]=\"registerForm\" (ngSubmit)=\"onSubmit()\" class=\"register-form\">\n      \n      <!-- Informations personnelles -->\n      <div class=\"form-section\">\n        <h3>Informations personnelles</h3>\n        \n        <div class=\"form-row\">\n          <div class=\"form-group\">\n            <label for=\"prenom\">Prénom *</label>\n            <input\n              type=\"text\"\n              id=\"prenom\"\n              formControlName=\"prenom\"\n              class=\"form-control\"\n              [class.is-invalid]=\"prenom?.invalid && prenom?.touched\"\n              placeholder=\"Votre prénom\"\n            />\n            <div class=\"invalid-feedback\" *ngIf=\"prenom?.invalid && prenom?.touched\">\n              <div *ngIf=\"prenom?.errors?.['required']\">Le prénom est requis</div>\n              <div *ngIf=\"prenom?.errors?.['minlength']\">Le prénom doit contenir au moins 2 caractères</div>\n            </div>\n          </div>\n\n          <div class=\"form-group\">\n            <label for=\"nom\">Nom *</label>\n            <input\n              type=\"text\"\n              id=\"nom\"\n              formControlName=\"nom\"\n              class=\"form-control\"\n              [class.is-invalid]=\"nom?.invalid && nom?.touched\"\n              placeholder=\"Votre nom\"\n            />\n            <div class=\"invalid-feedback\" *ngIf=\"nom?.invalid && nom?.touched\">\n              <div *ngIf=\"nom?.errors?.['required']\">Le nom est requis</div>\n              <div *ngIf=\"nom?.errors?.['minlength']\">Le nom doit contenir au moins 2 caractères</div>\n            </div>\n          </div>\n        </div>\n\n        <div class=\"form-row\">\n          <div class=\"form-group\">\n            <label for=\"email\">Email *</label>\n            <input\n              type=\"email\"\n              id=\"email\"\n              formControlName=\"email\"\n              class=\"form-control\"\n              [class.is-invalid]=\"email?.invalid && email?.touched\"\n              placeholder=\"<EMAIL>\"\n            />\n            <div class=\"invalid-feedback\" *ngIf=\"email?.invalid && email?.touched\">\n              <div *ngIf=\"email?.errors?.['required']\">L'email est requis</div>\n              <div *ngIf=\"email?.errors?.['email']\">Format d'email invalide</div>\n            </div>\n          </div>\n\n          <div class=\"form-group\">\n            <label for=\"phoneNumber\">Téléphone *</label>\n            <input\n              type=\"tel\"\n              id=\"phoneNumber\"\n              formControlName=\"phoneNumber\"\n              class=\"form-control\"\n              [class.is-invalid]=\"phoneNumber?.invalid && phoneNumber?.touched\"\n              placeholder=\"0123456789\"\n            />\n            <div class=\"invalid-feedback\" *ngIf=\"phoneNumber?.invalid && phoneNumber?.touched\">\n              <div *ngIf=\"phoneNumber?.errors?.['required']\">Le téléphone est requis</div>\n              <div *ngIf=\"phoneNumber?.errors?.['pattern']\">Le téléphone doit contenir entre 8 et 15 chiffres</div>\n            </div>\n          </div>\n        </div>\n\n        <div class=\"form-group\">\n          <label for=\"dateNaissance\">Date de naissance *</label>\n          <input\n            type=\"date\"\n            id=\"dateNaissance\"\n            formControlName=\"dateNaissance\"\n            class=\"form-control\"\n            [class.is-invalid]=\"dateNaissance?.invalid && dateNaissance?.touched\"\n          />\n          <div class=\"invalid-feedback\" *ngIf=\"dateNaissance?.invalid && dateNaissance?.touched\">\n            <div *ngIf=\"dateNaissance?.errors?.['required']\">La date de naissance est requise</div>\n          </div>\n        </div>\n\n        <div class=\"form-row\">\n          <div class=\"form-group\">\n            <label for=\"password\">Mot de passe *</label>\n            <input\n              type=\"password\"\n              id=\"password\"\n              formControlName=\"password\"\n              class=\"form-control\"\n              [class.is-invalid]=\"password?.invalid && password?.touched\"\n              placeholder=\"Votre mot de passe\"\n            />\n            <div class=\"invalid-feedback\" *ngIf=\"password?.invalid && password?.touched\">\n              <div *ngIf=\"password?.errors?.['required']\">Le mot de passe est requis</div>\n              <div *ngIf=\"password?.errors?.['minlength']\">Le mot de passe doit contenir au moins 6 caractères</div>\n            </div>\n          </div>\n\n          <div class=\"form-group\">\n            <label for=\"confirmPassword\">Confirmer le mot de passe *</label>\n            <input\n              type=\"password\"\n              id=\"confirmPassword\"\n              formControlName=\"confirmPassword\"\n              class=\"form-control\"\n              [class.is-invalid]=\"confirmPassword?.invalid && confirmPassword?.touched\"\n              placeholder=\"Confirmez votre mot de passe\"\n            />\n            <div class=\"invalid-feedback\" *ngIf=\"confirmPassword?.invalid && confirmPassword?.touched\">\n              <div *ngIf=\"confirmPassword?.errors?.['required']\">La confirmation est requise</div>\n              <div *ngIf=\"confirmPassword?.errors?.['passwordMismatch']\">Les mots de passe ne correspondent pas</div>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      <!-- Informations entreprise -->\n      <div class=\"form-section\">\n        <h3>Informations entreprise</h3>\n        \n        <div class=\"form-row\">\n          <div class=\"form-group\">\n            <label for=\"matriculeFiscale\">Matricule Fiscale *</label>\n            <input\n              type=\"text\"\n              id=\"matriculeFiscale\"\n              formControlName=\"matriculeFiscale\"\n              class=\"form-control\"\n              [class.is-invalid]=\"matriculeFiscale?.invalid && matriculeFiscale?.touched\"\n              placeholder=\"12345678\"\n              maxlength=\"8\"\n            />\n            <div class=\"invalid-feedback\" *ngIf=\"matriculeFiscale?.invalid && matriculeFiscale?.touched\">\n              <div *ngIf=\"matriculeFiscale?.errors?.['required']\">Le matricule fiscale est requis</div>\n              <div *ngIf=\"matriculeFiscale?.errors?.['minlength'] || matriculeFiscale?.errors?.['maxlength']\">Le matricule fiscale doit contenir exactement 8 caractères</div>\n            </div>\n          </div>\n\n          <div class=\"form-group\">\n            <label for=\"raisonSociale\">Raison Sociale *</label>\n            <input\n              type=\"text\"\n              id=\"raisonSociale\"\n              formControlName=\"raisonSociale\"\n              class=\"form-control\"\n              [class.is-invalid]=\"raisonSociale?.invalid && raisonSociale?.touched\"\n              placeholder=\"Nom de votre entreprise\"\n              maxlength=\"200\"\n            />\n            <div class=\"invalid-feedback\" *ngIf=\"raisonSociale?.invalid && raisonSociale?.touched\">\n              <div *ngIf=\"raisonSociale?.errors?.['required']\">La raison sociale est requise</div>\n              <div *ngIf=\"raisonSociale?.errors?.['minlength']\">La raison sociale doit contenir au moins 2 caractères</div>\n              <div *ngIf=\"raisonSociale?.errors?.['maxlength']\">La raison sociale ne peut pas dépasser 200 caractères</div>\n            </div>\n          </div>\n        </div>\n\n        <div class=\"form-group\">\n          <label for=\"description\">Description (optionnel)</label>\n          <textarea\n            id=\"description\"\n            formControlName=\"description\"\n            class=\"form-control\"\n            [class.is-invalid]=\"description?.invalid && description?.touched\"\n            placeholder=\"Description de votre entreprise\"\n            rows=\"3\"\n          ></textarea>\n        </div>\n\n        <div class=\"form-row\">\n          <div class=\"form-group\">\n            <label for=\"rib\">RIB *</label>\n            <input\n              type=\"text\"\n              id=\"rib\"\n              formControlName=\"rib\"\n              class=\"form-control\"\n              [class.is-invalid]=\"rib?.invalid && rib?.touched\"\n              placeholder=\"12345678901234567890\"\n              maxlength=\"20\"\n            />\n            <div class=\"invalid-feedback\" *ngIf=\"rib?.invalid && rib?.touched\">\n              <div *ngIf=\"rib?.errors?.['required']\">Le RIB est requis</div>\n              <div *ngIf=\"rib?.errors?.['minlength'] || rib?.errors?.['maxlength']\">Le RIB doit contenir exactement 20 caractères</div>\n            </div>\n          </div>\n\n          <div class=\"form-group\">\n            <label for=\"codeBanque\">Code Banque *</label>\n            <input\n              type=\"text\"\n              id=\"codeBanque\"\n              formControlName=\"codeBanque\"\n              class=\"form-control\"\n              [class.is-invalid]=\"codeBanque?.invalid && codeBanque?.touched\"\n              placeholder=\"123\"\n              maxlength=\"3\"\n            />\n            <div class=\"invalid-feedback\" *ngIf=\"codeBanque?.invalid && codeBanque?.touched\">\n              <div *ngIf=\"codeBanque?.errors?.['required']\">Le code banque est requis</div>\n              <div *ngIf=\"codeBanque?.errors?.['minlength'] || codeBanque?.errors?.['maxlength']\">Le code banque doit contenir exactement 3 caractères</div>\n            </div>\n          </div>\n        </div>\n\n        <!-- Paramètres commerciaux -->\n        <div class=\"form-row\">\n          <div class=\"form-group\">\n            <label for=\"commission\">Commission (%) *</label>\n            <input\n              type=\"number\"\n              id=\"commission\"\n              formControlName=\"commission\"\n              class=\"form-control\"\n              [class.is-invalid]=\"commission?.invalid && commission?.touched\"\n              placeholder=\"0.75\"\n              min=\"0.5\"\n              max=\"1\"\n              step=\"0.01\"\n            />\n            <div class=\"invalid-feedback\" *ngIf=\"commission?.invalid && commission?.touched\">\n              <div *ngIf=\"commission?.errors?.['required']\">La commission est requise</div>\n              <div *ngIf=\"commission?.errors?.['min']\">La commission doit être au minimum 50% (0.5)</div>\n              <div *ngIf=\"commission?.errors?.['max']\">La commission doit être au maximum 100% (1.0)</div>\n            </div>\n            <small class=\"form-text text-muted\">Commission sur les ventes (entre 50% et 100%)</small>\n          </div>\n\n          <div class=\"form-group\">\n            <label for=\"delaiPreparationJours\">Délai de préparation (jours) *</label>\n            <input\n              type=\"number\"\n              id=\"delaiPreparationJours\"\n              formControlName=\"delaiPreparationJours\"\n              class=\"form-control\"\n              [class.is-invalid]=\"delaiPreparationJours?.invalid && delaiPreparationJours?.touched\"\n              placeholder=\"2\"\n              min=\"1\"\n              max=\"30\"\n            />\n            <div class=\"invalid-feedback\" *ngIf=\"delaiPreparationJours?.invalid && delaiPreparationJours?.touched\">\n              <div *ngIf=\"delaiPreparationJours?.errors?.['required']\">Le délai de préparation est requis</div>\n              <div *ngIf=\"delaiPreparationJours?.errors?.['min']\">Le délai doit être d'au moins 1 jour</div>\n            </div>\n            <small class=\"form-text text-muted\">Nombre de jours pour préparer une commande</small>\n          </div>\n        </div>\n\n        <div class=\"form-group\">\n          <label for=\"fraisLivraisonBase\">Frais de livraison de base (€) *</label>\n          <input\n            type=\"number\"\n            id=\"fraisLivraisonBase\"\n            formControlName=\"fraisLivraisonBase\"\n            class=\"form-control\"\n            [class.is-invalid]=\"fraisLivraisonBase?.invalid && fraisLivraisonBase?.touched\"\n            placeholder=\"9.99\"\n            min=\"0\"\n            step=\"0.01\"\n          />\n          <div class=\"invalid-feedback\" *ngIf=\"fraisLivraisonBase?.invalid && fraisLivraisonBase?.touched\">\n            <div *ngIf=\"fraisLivraisonBase?.errors?.['required']\">Les frais de livraison sont requis</div>\n            <div *ngIf=\"fraisLivraisonBase?.errors?.['min']\">Les frais de livraison doivent être positifs</div>\n          </div>\n          <small class=\"form-text text-muted\">Frais de livraison de base pour vos produits</small>\n        </div>\n\n        <!-- Adresse de l'entreprise -->\n        <h3 class=\"section-title\">📍 Adresse de l'entreprise</h3>\n\n        <div class=\"form-group\">\n          <label for=\"rue\">Rue *</label>\n          <input\n            type=\"text\"\n            id=\"rue\"\n            formControlName=\"rue\"\n            class=\"form-control\"\n            [class.is-invalid]=\"rue?.invalid && rue?.touched\"\n            placeholder=\"123 Rue de la République\"\n          />\n          <div class=\"invalid-feedback\" *ngIf=\"rue?.invalid && rue?.touched\">\n            <div *ngIf=\"rue?.errors?.['required']\">La rue est requise</div>\n            <div *ngIf=\"rue?.errors?.['minlength']\">La rue doit contenir au moins 5 caractères</div>\n          </div>\n        </div>\n\n        <div class=\"form-row\">\n          <div class=\"form-group\">\n            <label for=\"codePostal\">Code postal *</label>\n            <input\n              type=\"text\"\n              id=\"codePostal\"\n              formControlName=\"codePostal\"\n              class=\"form-control\"\n              [class.is-invalid]=\"codePostal?.invalid && codePostal?.touched\"\n              placeholder=\"1000\"\n              maxlength=\"4\"\n            />\n            <div class=\"invalid-feedback\" *ngIf=\"codePostal?.invalid && codePostal?.touched\">\n              <div *ngIf=\"codePostal?.errors?.['required']\">Le code postal est requis</div>\n              <div *ngIf=\"codePostal?.errors?.['pattern']\">Le code postal doit contenir 4 chiffres</div>\n            </div>\n          </div>\n\n          <div class=\"form-group\">\n            <label for=\"ville\">Ville *</label>\n            <input\n              type=\"text\"\n              id=\"ville\"\n              formControlName=\"ville\"\n              class=\"form-control\"\n              [class.is-invalid]=\"ville?.invalid && ville?.touched\"\n              placeholder=\"Paris\"\n            />\n            <div class=\"invalid-feedback\" *ngIf=\"ville?.invalid && ville?.touched\">\n              <div *ngIf=\"ville?.errors?.['required']\">La ville est requise</div>\n              <div *ngIf=\"ville?.errors?.['minlength']\">La ville doit contenir au moins 2 caractères</div>\n            </div>\n          </div>\n        </div>\n\n        <div class=\"form-group\">\n          <label for=\"pays\">Pays *</label>\n          <input\n            type=\"text\"\n            id=\"pays\"\n            formControlName=\"pays\"\n            class=\"form-control\"\n            value=\"Tunisie\"\n            readonly\n            style=\"background-color: #f8f9fa; cursor: not-allowed;\"\n          />\n          <small class=\"form-text text-muted\">Le pays est automatiquement défini sur Tunisie</small>\n        </div>\n\n        <div class=\"form-group\">\n          <label for=\"logoFile\">Logo de l'entreprise (optionnel)</label>\n          <input\n            type=\"file\"\n            id=\"logoFile\"\n            class=\"form-control\"\n            [class.is-invalid]=\"logoFile?.invalid && logoFile?.touched\"\n            accept=\"image/*\"\n            (change)=\"onFileSelected($event)\"\n          />\n          <small class=\"form-text text-muted\">Si aucun logo n'est sélectionné, un logo par défaut sera utilisé.</small>\n        </div>\n      </div>\n\n      <!-- Messages -->\n      <div class=\"alert alert-danger\" *ngIf=\"errorMessage\">\n        <div style=\"white-space: pre-line;\">{{ errorMessage }}</div>\n      </div>\n\n      <div class=\"alert alert-success\" *ngIf=\"successMessage\">\n        {{ successMessage }}\n      </div>\n\n      <!-- Boutons -->\n      <div class=\"form-actions\">\n        <button\n          type=\"button\"\n          class=\"btn btn-secondary\"\n          (click)=\"goToLogin()\"\n          [disabled]=\"isLoading\"\n        >\n          Retour à la connexion\n        </button>\n\n        <button\n          type=\"submit\"\n          class=\"btn btn-primary\"\n          [disabled]=\"isLoading\"\n        >\n          <span *ngIf=\"isLoading\" class=\"spinner\"></span>\n          {{ isLoading ? 'Inscription...' : 'S\\'inscrire' }}\n        </button>\n      </div>\n    </form>\n  </div>\n</div>\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACyBc,IAAA,yBAAA,GAAA,KAAA;AAA0C,IAAA,iBAAA,GAAA,yBAAA;AAAoB,IAAA,uBAAA;;;;;AAC9D,IAAA,yBAAA,GAAA,KAAA;AAA2C,IAAA,iBAAA,GAAA,qDAAA;AAA6C,IAAA,uBAAA;;;;;AAF1F,IAAA,yBAAA,GAAA,OAAA,EAAA;AACE,IAAA,qBAAA,GAAA,yCAAA,GAAA,GAAA,OAAA,EAAA,EAA0C,GAAA,yCAAA,GAAA,GAAA,OAAA,EAAA;AAE5C,IAAA,uBAAA;;;;AAFQ,IAAA,oBAAA;AAAA,IAAA,qBAAA,QAAA,OAAA,UAAA,OAAA,OAAA,OAAA,OAAA,UAAA,OAAA,OAAA,OAAA,OAAA,OAAA,UAAA,CAAA;AACA,IAAA,oBAAA;AAAA,IAAA,qBAAA,QAAA,OAAA,UAAA,OAAA,OAAA,OAAA,OAAA,UAAA,OAAA,OAAA,OAAA,OAAA,OAAA,WAAA,CAAA;;;;;AAeN,IAAA,yBAAA,GAAA,KAAA;AAAuC,IAAA,iBAAA,GAAA,mBAAA;AAAiB,IAAA,uBAAA;;;;;AACxD,IAAA,yBAAA,GAAA,KAAA;AAAwC,IAAA,iBAAA,GAAA,+CAAA;AAA0C,IAAA,uBAAA;;;;;AAFpF,IAAA,yBAAA,GAAA,OAAA,EAAA;AACE,IAAA,qBAAA,GAAA,yCAAA,GAAA,GAAA,OAAA,EAAA,EAAuC,GAAA,yCAAA,GAAA,GAAA,OAAA,EAAA;AAEzC,IAAA,uBAAA;;;;AAFQ,IAAA,oBAAA;AAAA,IAAA,qBAAA,QAAA,OAAA,OAAA,OAAA,OAAA,OAAA,IAAA,UAAA,OAAA,OAAA,OAAA,IAAA,OAAA,UAAA,CAAA;AACA,IAAA,oBAAA;AAAA,IAAA,qBAAA,QAAA,OAAA,OAAA,OAAA,OAAA,OAAA,IAAA,UAAA,OAAA,OAAA,OAAA,IAAA,OAAA,WAAA,CAAA;;;;;AAiBN,IAAA,yBAAA,GAAA,KAAA;AAAyC,IAAA,iBAAA,GAAA,oBAAA;AAAkB,IAAA,uBAAA;;;;;AAC3D,IAAA,yBAAA,GAAA,KAAA;AAAsC,IAAA,iBAAA,GAAA,yBAAA;AAAuB,IAAA,uBAAA;;;;;AAF/D,IAAA,yBAAA,GAAA,OAAA,EAAA;AACE,IAAA,qBAAA,GAAA,yCAAA,GAAA,GAAA,OAAA,EAAA,EAAyC,GAAA,yCAAA,GAAA,GAAA,OAAA,EAAA;AAE3C,IAAA,uBAAA;;;;AAFQ,IAAA,oBAAA;AAAA,IAAA,qBAAA,QAAA,OAAA,SAAA,OAAA,OAAA,OAAA,MAAA,UAAA,OAAA,OAAA,OAAA,MAAA,OAAA,UAAA,CAAA;AACA,IAAA,oBAAA;AAAA,IAAA,qBAAA,QAAA,OAAA,SAAA,OAAA,OAAA,OAAA,MAAA,UAAA,OAAA,OAAA,OAAA,MAAA,OAAA,OAAA,CAAA;;;;;AAeN,IAAA,yBAAA,GAAA,KAAA;AAA+C,IAAA,iBAAA,GAAA,+BAAA;AAAuB,IAAA,uBAAA;;;;;AACtE,IAAA,yBAAA,GAAA,KAAA;AAA8C,IAAA,iBAAA,GAAA,yDAAA;AAAiD,IAAA,uBAAA;;;;;AAFjG,IAAA,yBAAA,GAAA,OAAA,EAAA;AACE,IAAA,qBAAA,GAAA,yCAAA,GAAA,GAAA,OAAA,EAAA,EAA+C,GAAA,yCAAA,GAAA,GAAA,OAAA,EAAA;AAEjD,IAAA,uBAAA;;;;AAFQ,IAAA,oBAAA;AAAA,IAAA,qBAAA,QAAA,OAAA,eAAA,OAAA,OAAA,OAAA,YAAA,UAAA,OAAA,OAAA,OAAA,YAAA,OAAA,UAAA,CAAA;AACA,IAAA,oBAAA;AAAA,IAAA,qBAAA,QAAA,OAAA,eAAA,OAAA,OAAA,OAAA,YAAA,UAAA,OAAA,OAAA,OAAA,YAAA,OAAA,SAAA,CAAA;;;;;AAeR,IAAA,yBAAA,GAAA,KAAA;AAAiD,IAAA,iBAAA,GAAA,kCAAA;AAAgC,IAAA,uBAAA;;;;;AADnF,IAAA,yBAAA,GAAA,OAAA,EAAA;AACE,IAAA,qBAAA,GAAA,yCAAA,GAAA,GAAA,OAAA,EAAA;AACF,IAAA,uBAAA;;;;AADQ,IAAA,oBAAA;AAAA,IAAA,qBAAA,QAAA,OAAA,iBAAA,OAAA,OAAA,OAAA,cAAA,UAAA,OAAA,OAAA,OAAA,cAAA,OAAA,UAAA,CAAA;;;;;AAgBJ,IAAA,yBAAA,GAAA,KAAA;AAA4C,IAAA,iBAAA,GAAA,4BAAA;AAA0B,IAAA,uBAAA;;;;;AACtE,IAAA,yBAAA,GAAA,KAAA;AAA6C,IAAA,iBAAA,GAAA,wDAAA;AAAmD,IAAA,uBAAA;;;;;AAFlG,IAAA,yBAAA,GAAA,OAAA,EAAA;AACE,IAAA,qBAAA,GAAA,yCAAA,GAAA,GAAA,OAAA,EAAA,EAA4C,GAAA,yCAAA,GAAA,GAAA,OAAA,EAAA;AAE9C,IAAA,uBAAA;;;;AAFQ,IAAA,oBAAA;AAAA,IAAA,qBAAA,QAAA,OAAA,YAAA,OAAA,OAAA,OAAA,SAAA,UAAA,OAAA,OAAA,OAAA,SAAA,OAAA,UAAA,CAAA;AACA,IAAA,oBAAA;AAAA,IAAA,qBAAA,QAAA,OAAA,YAAA,OAAA,OAAA,OAAA,SAAA,UAAA,OAAA,OAAA,OAAA,SAAA,OAAA,WAAA,CAAA;;;;;AAeN,IAAA,yBAAA,GAAA,KAAA;AAAmD,IAAA,iBAAA,GAAA,6BAAA;AAA2B,IAAA,uBAAA;;;;;AAC9E,IAAA,yBAAA,GAAA,KAAA;AAA2D,IAAA,iBAAA,GAAA,wCAAA;AAAsC,IAAA,uBAAA;;;;;AAFnG,IAAA,yBAAA,GAAA,OAAA,EAAA;AACE,IAAA,qBAAA,GAAA,yCAAA,GAAA,GAAA,OAAA,EAAA,EAAmD,GAAA,yCAAA,GAAA,GAAA,OAAA,EAAA;AAErD,IAAA,uBAAA;;;;AAFQ,IAAA,oBAAA;AAAA,IAAA,qBAAA,QAAA,OAAA,mBAAA,OAAA,OAAA,OAAA,gBAAA,UAAA,OAAA,OAAA,OAAA,gBAAA,OAAA,UAAA,CAAA;AACA,IAAA,oBAAA;AAAA,IAAA,qBAAA,QAAA,OAAA,mBAAA,OAAA,OAAA,OAAA,gBAAA,UAAA,OAAA,OAAA,OAAA,gBAAA,OAAA,kBAAA,CAAA;;;;;AAuBN,IAAA,yBAAA,GAAA,KAAA;AAAoD,IAAA,iBAAA,GAAA,iCAAA;AAA+B,IAAA,uBAAA;;;;;AACnF,IAAA,yBAAA,GAAA,KAAA;AAAgG,IAAA,iBAAA,GAAA,+DAAA;AAA0D,IAAA,uBAAA;;;;;AAF5J,IAAA,yBAAA,GAAA,OAAA,EAAA;AACE,IAAA,qBAAA,GAAA,yCAAA,GAAA,GAAA,OAAA,EAAA,EAAoD,GAAA,yCAAA,GAAA,GAAA,OAAA,EAAA;AAEtD,IAAA,uBAAA;;;;AAFQ,IAAA,oBAAA;AAAA,IAAA,qBAAA,QAAA,OAAA,oBAAA,OAAA,OAAA,OAAA,iBAAA,UAAA,OAAA,OAAA,OAAA,iBAAA,OAAA,UAAA,CAAA;AACA,IAAA,oBAAA;AAAA,IAAA,qBAAA,SAAA,OAAA,oBAAA,OAAA,OAAA,OAAA,iBAAA,UAAA,OAAA,OAAA,OAAA,iBAAA,OAAA,WAAA,OAAA,OAAA,oBAAA,OAAA,OAAA,OAAA,iBAAA,UAAA,OAAA,OAAA,OAAA,iBAAA,OAAA,WAAA,EAAA;;;;;AAgBN,IAAA,yBAAA,GAAA,KAAA;AAAiD,IAAA,iBAAA,GAAA,+BAAA;AAA6B,IAAA,uBAAA;;;;;AAC9E,IAAA,yBAAA,GAAA,KAAA;AAAkD,IAAA,iBAAA,GAAA,0DAAA;AAAqD,IAAA,uBAAA;;;;;AACvG,IAAA,yBAAA,GAAA,KAAA;AAAkD,IAAA,iBAAA,GAAA,6DAAA;AAAqD,IAAA,uBAAA;;;;;AAHzG,IAAA,yBAAA,GAAA,OAAA,EAAA;AACE,IAAA,qBAAA,GAAA,yCAAA,GAAA,GAAA,OAAA,EAAA,EAAiD,GAAA,yCAAA,GAAA,GAAA,OAAA,EAAA,EACC,GAAA,yCAAA,GAAA,GAAA,OAAA,EAAA;AAEpD,IAAA,uBAAA;;;;AAHQ,IAAA,oBAAA;AAAA,IAAA,qBAAA,QAAA,OAAA,iBAAA,OAAA,OAAA,OAAA,cAAA,UAAA,OAAA,OAAA,OAAA,cAAA,OAAA,UAAA,CAAA;AACA,IAAA,oBAAA;AAAA,IAAA,qBAAA,QAAA,OAAA,iBAAA,OAAA,OAAA,OAAA,cAAA,UAAA,OAAA,OAAA,OAAA,cAAA,OAAA,WAAA,CAAA;AACA,IAAA,oBAAA;AAAA,IAAA,qBAAA,QAAA,OAAA,iBAAA,OAAA,OAAA,OAAA,cAAA,UAAA,OAAA,OAAA,OAAA,cAAA,OAAA,WAAA,CAAA;;;;;AA8BN,IAAA,yBAAA,GAAA,KAAA;AAAuC,IAAA,iBAAA,GAAA,mBAAA;AAAiB,IAAA,uBAAA;;;;;AACxD,IAAA,yBAAA,GAAA,KAAA;AAAsE,IAAA,iBAAA,GAAA,kDAAA;AAA6C,IAAA,uBAAA;;;;;AAFrH,IAAA,yBAAA,GAAA,OAAA,EAAA;AACE,IAAA,qBAAA,GAAA,yCAAA,GAAA,GAAA,OAAA,EAAA,EAAuC,GAAA,yCAAA,GAAA,GAAA,OAAA,EAAA;AAEzC,IAAA,uBAAA;;;;AAFQ,IAAA,oBAAA;AAAA,IAAA,qBAAA,QAAA,OAAA,OAAA,OAAA,OAAA,OAAA,IAAA,UAAA,OAAA,OAAA,OAAA,IAAA,OAAA,UAAA,CAAA;AACA,IAAA,oBAAA;AAAA,IAAA,qBAAA,SAAA,OAAA,OAAA,OAAA,OAAA,OAAA,IAAA,UAAA,OAAA,OAAA,OAAA,IAAA,OAAA,WAAA,OAAA,OAAA,OAAA,OAAA,OAAA,OAAA,IAAA,UAAA,OAAA,OAAA,OAAA,IAAA,OAAA,WAAA,EAAA;;;;;AAgBN,IAAA,yBAAA,GAAA,KAAA;AAA8C,IAAA,iBAAA,GAAA,2BAAA;AAAyB,IAAA,uBAAA;;;;;AACvE,IAAA,yBAAA,GAAA,KAAA;AAAoF,IAAA,iBAAA,GAAA,yDAAA;AAAoD,IAAA,uBAAA;;;;;AAF1I,IAAA,yBAAA,GAAA,OAAA,EAAA;AACE,IAAA,qBAAA,GAAA,yCAAA,GAAA,GAAA,OAAA,EAAA,EAA8C,GAAA,yCAAA,GAAA,GAAA,OAAA,EAAA;AAEhD,IAAA,uBAAA;;;;AAFQ,IAAA,oBAAA;AAAA,IAAA,qBAAA,QAAA,OAAA,cAAA,OAAA,OAAA,OAAA,WAAA,UAAA,OAAA,OAAA,OAAA,WAAA,OAAA,UAAA,CAAA;AACA,IAAA,oBAAA;AAAA,IAAA,qBAAA,SAAA,OAAA,cAAA,OAAA,OAAA,OAAA,WAAA,UAAA,OAAA,OAAA,OAAA,WAAA,OAAA,WAAA,OAAA,OAAA,cAAA,OAAA,OAAA,OAAA,WAAA,UAAA,OAAA,OAAA,OAAA,WAAA,OAAA,WAAA,EAAA;;;;;AAqBN,IAAA,yBAAA,GAAA,KAAA;AAA8C,IAAA,iBAAA,GAAA,2BAAA;AAAyB,IAAA,uBAAA;;;;;AACvE,IAAA,yBAAA,GAAA,KAAA;AAAyC,IAAA,iBAAA,GAAA,iDAAA;AAA4C,IAAA,uBAAA;;;;;AACrF,IAAA,yBAAA,GAAA,KAAA;AAAyC,IAAA,iBAAA,GAAA,kDAAA;AAA6C,IAAA,uBAAA;;;;;AAHxF,IAAA,yBAAA,GAAA,OAAA,EAAA;AACE,IAAA,qBAAA,GAAA,yCAAA,GAAA,GAAA,OAAA,EAAA,EAA8C,GAAA,yCAAA,GAAA,GAAA,OAAA,EAAA,EACL,GAAA,yCAAA,GAAA,GAAA,OAAA,EAAA;AAE3C,IAAA,uBAAA;;;;AAHQ,IAAA,oBAAA;AAAA,IAAA,qBAAA,QAAA,OAAA,cAAA,OAAA,OAAA,OAAA,WAAA,UAAA,OAAA,OAAA,OAAA,WAAA,OAAA,UAAA,CAAA;AACA,IAAA,oBAAA;AAAA,IAAA,qBAAA,QAAA,OAAA,cAAA,OAAA,OAAA,OAAA,WAAA,UAAA,OAAA,OAAA,OAAA,WAAA,OAAA,KAAA,CAAA;AACA,IAAA,oBAAA;AAAA,IAAA,qBAAA,QAAA,OAAA,cAAA,OAAA,OAAA,OAAA,WAAA,UAAA,OAAA,OAAA,OAAA,WAAA,OAAA,KAAA,CAAA;;;;;AAkBN,IAAA,yBAAA,GAAA,KAAA;AAAyD,IAAA,iBAAA,GAAA,0CAAA;AAAkC,IAAA,uBAAA;;;;;AAC3F,IAAA,yBAAA,GAAA,KAAA;AAAoD,IAAA,iBAAA,GAAA,4CAAA;AAAoC,IAAA,uBAAA;;;;;AAF1F,IAAA,yBAAA,GAAA,OAAA,EAAA;AACE,IAAA,qBAAA,GAAA,yCAAA,GAAA,GAAA,OAAA,EAAA,EAAyD,GAAA,yCAAA,GAAA,GAAA,OAAA,EAAA;AAE3D,IAAA,uBAAA;;;;AAFQ,IAAA,oBAAA;AAAA,IAAA,qBAAA,QAAA,OAAA,yBAAA,OAAA,OAAA,OAAA,sBAAA,UAAA,OAAA,OAAA,OAAA,sBAAA,OAAA,UAAA,CAAA;AACA,IAAA,oBAAA;AAAA,IAAA,qBAAA,QAAA,OAAA,yBAAA,OAAA,OAAA,OAAA,sBAAA,UAAA,OAAA,OAAA,OAAA,sBAAA,OAAA,KAAA,CAAA;;;;;AAmBR,IAAA,yBAAA,GAAA,KAAA;AAAsD,IAAA,iBAAA,GAAA,oCAAA;AAAkC,IAAA,uBAAA;;;;;AACxF,IAAA,yBAAA,GAAA,KAAA;AAAiD,IAAA,iBAAA,GAAA,iDAAA;AAA4C,IAAA,uBAAA;;;;;AAF/F,IAAA,yBAAA,GAAA,OAAA,EAAA;AACE,IAAA,qBAAA,GAAA,yCAAA,GAAA,GAAA,OAAA,EAAA,EAAsD,GAAA,yCAAA,GAAA,GAAA,OAAA,EAAA;AAExD,IAAA,uBAAA;;;;AAFQ,IAAA,oBAAA;AAAA,IAAA,qBAAA,QAAA,OAAA,sBAAA,OAAA,OAAA,OAAA,mBAAA,UAAA,OAAA,OAAA,OAAA,mBAAA,OAAA,UAAA,CAAA;AACA,IAAA,oBAAA;AAAA,IAAA,qBAAA,QAAA,OAAA,sBAAA,OAAA,OAAA,OAAA,mBAAA,UAAA,OAAA,OAAA,OAAA,mBAAA,OAAA,KAAA,CAAA;;;;;AAmBN,IAAA,yBAAA,GAAA,KAAA;AAAuC,IAAA,iBAAA,GAAA,oBAAA;AAAkB,IAAA,uBAAA;;;;;AACzD,IAAA,yBAAA,GAAA,KAAA;AAAwC,IAAA,iBAAA,GAAA,+CAAA;AAA0C,IAAA,uBAAA;;;;;AAFpF,IAAA,yBAAA,GAAA,OAAA,EAAA;AACE,IAAA,qBAAA,GAAA,0CAAA,GAAA,GAAA,OAAA,EAAA,EAAuC,GAAA,0CAAA,GAAA,GAAA,OAAA,EAAA;AAEzC,IAAA,uBAAA;;;;AAFQ,IAAA,oBAAA;AAAA,IAAA,qBAAA,QAAA,OAAA,OAAA,OAAA,OAAA,OAAA,IAAA,UAAA,OAAA,OAAA,OAAA,IAAA,OAAA,UAAA,CAAA;AACA,IAAA,oBAAA;AAAA,IAAA,qBAAA,QAAA,OAAA,OAAA,OAAA,OAAA,OAAA,IAAA,UAAA,OAAA,OAAA,OAAA,IAAA,OAAA,WAAA,CAAA;;;;;AAiBJ,IAAA,yBAAA,GAAA,KAAA;AAA8C,IAAA,iBAAA,GAAA,2BAAA;AAAyB,IAAA,uBAAA;;;;;AACvE,IAAA,yBAAA,GAAA,KAAA;AAA6C,IAAA,iBAAA,GAAA,yCAAA;AAAuC,IAAA,uBAAA;;;;;AAFtF,IAAA,yBAAA,GAAA,OAAA,EAAA;AACE,IAAA,qBAAA,GAAA,0CAAA,GAAA,GAAA,OAAA,EAAA,EAA8C,GAAA,0CAAA,GAAA,GAAA,OAAA,EAAA;AAEhD,IAAA,uBAAA;;;;AAFQ,IAAA,oBAAA;AAAA,IAAA,qBAAA,QAAA,OAAA,cAAA,OAAA,OAAA,OAAA,WAAA,UAAA,OAAA,OAAA,OAAA,WAAA,OAAA,UAAA,CAAA;AACA,IAAA,oBAAA;AAAA,IAAA,qBAAA,QAAA,OAAA,cAAA,OAAA,OAAA,OAAA,WAAA,UAAA,OAAA,OAAA,OAAA,WAAA,OAAA,SAAA,CAAA;;;;;AAeN,IAAA,yBAAA,GAAA,KAAA;AAAyC,IAAA,iBAAA,GAAA,sBAAA;AAAoB,IAAA,uBAAA;;;;;AAC7D,IAAA,yBAAA,GAAA,KAAA;AAA0C,IAAA,iBAAA,GAAA,iDAAA;AAA4C,IAAA,uBAAA;;;;;AAFxF,IAAA,yBAAA,GAAA,OAAA,EAAA;AACE,IAAA,qBAAA,GAAA,0CAAA,GAAA,GAAA,OAAA,EAAA,EAAyC,GAAA,0CAAA,GAAA,GAAA,OAAA,EAAA;AAE3C,IAAA,uBAAA;;;;AAFQ,IAAA,oBAAA;AAAA,IAAA,qBAAA,QAAA,OAAA,SAAA,OAAA,OAAA,OAAA,MAAA,UAAA,OAAA,OAAA,OAAA,MAAA,OAAA,UAAA,CAAA;AACA,IAAA,oBAAA;AAAA,IAAA,qBAAA,QAAA,OAAA,SAAA,OAAA,OAAA,OAAA,MAAA,UAAA,OAAA,OAAA,OAAA,MAAA,OAAA,WAAA,CAAA;;;;;AAkCd,IAAA,yBAAA,GAAA,OAAA,EAAA,EAAqD,GAAA,OAAA,EAAA;AACf,IAAA,iBAAA,CAAA;AAAkB,IAAA,uBAAA,EAAM;;;;AAAxB,IAAA,oBAAA,CAAA;AAAA,IAAA,4BAAA,OAAA,YAAA;;;;;AAGtC,IAAA,yBAAA,GAAA,OAAA,EAAA;AACE,IAAA,iBAAA,CAAA;AACF,IAAA,uBAAA;;;;AADE,IAAA,oBAAA;AAAA,IAAA,6BAAA,KAAA,OAAA,gBAAA,GAAA;;;;;AAmBE,IAAA,oBAAA,GAAA,QAAA,EAAA;;;AD1VJ,IAAO,oBAAP,MAAO,mBAAiB;EAOlB;EACA;EACA;EARV;EACA,YAAY;EACZ,eAAe;EACf,iBAAiB;EAEjB,YACU,aACA,aACA,QAAc;AAFd,SAAA,cAAA;AACA,SAAA,cAAA;AACA,SAAA,SAAA;AAER,SAAK,eAAe,KAAK,YAAY,MAAM;;MAEzC,OAAO,CAAC,IAAI,CAAC,WAAW,UAAU,WAAW,KAAK,CAAC;MACnD,UAAU,CAAC,IAAI,CAAC,WAAW,UAAU,WAAW,UAAU,CAAC,GAAG,WAAW,UAAU,GAAG,CAAC,CAAC;MACxF,iBAAiB,CAAC,IAAI,CAAC,WAAW,QAAQ,CAAC;MAC3C,KAAK,CAAC,IAAI,CAAC,WAAW,UAAU,WAAW,UAAU,CAAC,GAAG,WAAW,UAAU,EAAE,CAAC,CAAC;MAClF,QAAQ,CAAC,IAAI,CAAC,WAAW,UAAU,WAAW,UAAU,CAAC,GAAG,WAAW,UAAU,EAAE,CAAC,CAAC;MACrF,aAAa,CAAC,IAAI,CAAC,WAAW,UAAU,WAAW,QAAQ,YAAY,CAAC,CAAC;MACzE,eAAe,CAAC,IAAI,CAAC,WAAW,QAAQ,CAAC;;MAGzC,kBAAkB,CAAC,IAAI,CAAC,WAAW,UAAU,WAAW,QAAQ,SAAS,CAAC,CAAC;MAC3E,eAAe,CAAC,IAAI,CAAC,WAAW,UAAU,WAAW,UAAU,CAAC,GAAG,WAAW,UAAU,GAAG,CAAC,CAAC;MAC7F,aAAa,CAAC,EAAE;MAChB,KAAK,CAAC,IAAI,CAAC,WAAW,UAAU,WAAW,QAAQ,UAAU,CAAC,CAAC;MAC/D,YAAY,CAAC,IAAI,CAAC,WAAW,UAAU,WAAW,QAAQ,SAAS,CAAC,CAAC;MACrE,YAAY,CAAC,MAAM,CAAC,WAAW,UAAU,WAAW,IAAI,GAAG,GAAG,WAAW,IAAI,CAAC,CAAC,CAAC;MAChF,uBAAuB,CAAC,GAAG,CAAC,WAAW,UAAU,WAAW,IAAI,CAAC,GAAG,WAAW,IAAI,EAAE,CAAC,CAAC;MACvF,oBAAoB,CAAC,MAAM,CAAC,WAAW,UAAU,WAAW,IAAI,CAAC,CAAC,CAAC;;MAGnE,KAAK,CAAC,IAAI,CAAC,WAAW,UAAU,WAAW,UAAU,CAAC,CAAC,CAAC;MACxD,OAAO,CAAC,IAAI,CAAC,WAAW,UAAU,WAAW,UAAU,CAAC,CAAC,CAAC;MAC1D,YAAY,CAAC,IAAI,CAAC,WAAW,UAAU,WAAW,QAAQ,SAAS,CAAC,CAAC;MACrE,MAAM,CAAC,WAAW,CAAC,WAAW,QAAQ,CAAC;MAEvC,UAAU,CAAC,IAAI;;OACd,EAAE,YAAY,KAAK,uBAAsB,CAAE;EAChD;EAEA,uBAAuB,MAAe;AACpC,UAAM,WAAW,KAAK,IAAI,UAAU;AACpC,UAAM,kBAAkB,KAAK,IAAI,iBAAiB;AAElD,QAAI,YAAY,mBAAmB,SAAS,UAAU,gBAAgB,OAAO;AAC3E,sBAAgB,UAAU,EAAE,kBAAkB,KAAI,CAAE;AACpD,aAAO,EAAE,kBAAkB,KAAI;IACjC;AAEA,WAAO;EACT;EAEA,iBAAiB,WAAc;AAE7B,QAAI,CAAC,UAAU,KAAK,UAAU,gBAAgB,GAAG;AAC/C,WAAK,eAAe;AACpB,aAAO;IACT;AAGA,QAAI,CAAC,UAAU,KAAK,UAAU,UAAU,GAAG;AACzC,WAAK,eAAe;AACpB,aAAO;IACT;AAGA,QAAI,CAAC,WAAW,KAAK,UAAU,GAAG,GAAG;AACnC,WAAK,eAAe;AACpB,aAAO;IACT;AAEA,QAAI,CAAC,UAAU,IAAI,WAAW,UAAU,UAAU,GAAG;AACnD,WAAK,eAAe;AACpB,aAAO;IACT;AAGA,QAAI,CAAC,aAAa,KAAK,UAAU,WAAW,GAAG;AAC7C,WAAK,eAAe;AACpB,aAAO;IACT;AAEA,WAAO;EACT;EAEA,oBAAoB,WAAiB;AACnC,UAAM,aAAwC;MAC5C,SAAS;MACT,YAAY;MACZ,OAAO;MACP,UAAU;MACV,eAAe;MACf,iBAAiB;MACjB,oBAAoB;MACpB,iBAAiB;MACjB,OAAO;MACP,cAAc;MACd,eAAe;MACf,cAAc;MACd,yBAAyB;MACzB,sBAAsB;MACtB,YAAY;;AAGd,WAAO,WAAW,SAAS,KAAK;EAClC;EAEA,WAAQ;AACN,QAAI,KAAK,aAAa,OAAO;AAC3B,WAAK,YAAY;AACjB,WAAK,eAAe;AACpB,WAAK,iBAAiB;AAEtB,YAAM,YAAY,KAAK,aAAa;AAGpC,UAAI,CAAC,KAAK,iBAAiB,SAAS,GAAG;AACrC,aAAK,YAAY;AACjB;MACF;AAGA,YAAM,WAAW,IAAI,SAAQ;AAG7B,eAAS,OAAO,SAAS,UAAU,KAAK;AACxC,eAAS,OAAO,YAAY,UAAU,QAAQ;AAC9C,eAAS,OAAO,OAAO,UAAU,GAAG;AACpC,eAAS,OAAO,UAAU,UAAU,MAAM;AAC1C,eAAS,OAAO,eAAe,UAAU,WAAW;AAGpD,UAAI,UAAU,eAAe;AAC3B,cAAM,OAAO,IAAI,KAAK,UAAU,aAAa;AAC7C,iBAAS,OAAO,iBAAiB,KAAK,YAAW,CAAE;MACrD;AAEA,eAAS,OAAO,oBAAoB,UAAU,gBAAgB;AAC9D,eAAS,OAAO,iBAAiB,UAAU,aAAa;AACxD,eAAS,OAAO,OAAO,UAAU,GAAG;AACpC,eAAS,OAAO,cAAc,UAAU,UAAU;AAElD,UAAI,UAAU,aAAa;AACzB,iBAAS,OAAO,eAAe,UAAU,WAAW;MACtD;AAEA,eAAS,OAAO,cAAc,UAAU,YAAY,SAAQ,KAAM,MAAM;AACxE,eAAS,OAAO,yBAAyB,UAAU,uBAAuB,SAAQ,KAAM,GAAG;AAC3F,eAAS,OAAO,sBAAsB,UAAU,oBAAoB,SAAQ,KAAM,MAAM;AACxF,eAAS,OAAO,YAAY,MAAM;AAGlC,UAAI,UAAU,KAAK;AACjB,iBAAS,OAAO,cAAc,UAAU,GAAG;MAC7C;AACA,UAAI,UAAU,OAAO;AACnB,iBAAS,OAAO,gBAAgB,UAAU,KAAK;MACjD;AACA,UAAI,UAAU,YAAY;AACxB,iBAAS,OAAO,qBAAqB,UAAU,UAAU;MAC3D;AACA,UAAI,UAAU,MAAM;AAClB,iBAAS,OAAO,eAAe,UAAU,IAAI;MAC/C;AACA,eAAS,OAAO,wBAAwB,MAAM;AAG9C,UAAI,UAAU,UAAU;AACtB,iBAAS,OAAO,YAAY,UAAU,QAAQ;MAChD,OAAO;AAEL,cAAM,kBAAkB,IAAI,KAAK;UAC/B,IAAI,WAAW;YACb;YAAM;YAAM;YAAM;YAAM;YAAM;YAAM;YAAM;YAAM;YAAM;YAAM;YAAM;YAClE;YAAM;YAAM;YAAM;YAAM;YAAM;YAAM;YAAM;YAAM;YAAM;YAAM;YAAM;YAClE;YAAM;YAAM;YAAM;YAAM;YAAM;YAAM;YAAM;YAAM;YAAM;YAAM;YAAM;YAClE;YAAM;YAAM;YAAM;YAAM;YAAM;YAAM;YAAM;YAAM;YAAM;YAAM;YAAM;YAClE;YAAM;YAAM;YAAM;YAAM;YAAM;YAAM;YAAM;YAAM;YAAM;YAAM;YAAM;YAClE;YAAM;YAAM;YAAM;YAAM;YAAM;YAAM;WACrC;WACA,EAAE,MAAM,YAAW,CAAE;AAExB,cAAM,kBAAkB,IAAI,KAAK,CAAC,eAAe,GAAG,oBAAoB,EAAE,MAAM,YAAW,CAAE;AAC7F,iBAAS,OAAO,YAAY,eAAe;AAC3C,gBAAQ,IAAI,uFAA+D;MAC7E;AAGA,cAAQ,IAAI,2BAAoB;AAChC,eAAS,QAAQ,SAAS,QAAO,GAAI;AACnC,cAAM,MAAM,KAAK,CAAC;AAClB,cAAM,QAAQ,KAAK,CAAC;AACpB,YAAI,iBAAiB,MAAM;AACzB,kBAAQ,IAAI,GAAG,GAAG,YAAY,MAAM,IAAI,KAAK,MAAM,IAAI,WAAW,MAAM,IAAI,GAAG;QACjF,OAAO;AACL,kBAAQ,IAAI,GAAG,GAAG,KAAK,KAAK,EAAE;QAChC;MACF;AAEA,WAAK,YAAY,oBAAoB,QAAQ,EAAE,UAAU;QACvD,MAAM,MAAK;AACT,eAAK,YAAY;AACjB,eAAK,iBAAiB;AAGtB,qBAAW,MAAK;AACd,iBAAK,OAAO,SAAS,CAAC,QAAQ,CAAC;UACjC,GAAG,GAAI;QACT;QACA,OAAO,CAAC,UAAS;AACf,eAAK,YAAY;AAGjB,cAAI,MAAM,WAAW,KAAK;AACxB,oBAAQ,MAAM,mCAA2B,MAAM,KAAK;AAGpD,gBAAI,MAAM,SAAS,OAAO,MAAM,UAAU,UAAU;AAClD,kBAAI,MAAM,MAAM,QAAQ;AAEtB,sBAAM,mBAAmB,MAAM,MAAM;AACrC,oBAAI,gBAA0B,CAAA;AAE9B,uBAAO,KAAK,gBAAgB,EAAE,QAAQ,SAAM;AAC1C,wBAAM,cAAc,iBAAiB,GAAG;AACxC,sBAAI,MAAM,QAAQ,WAAW,GAAG;AAC9B,kCAAc,KAAK,GAAG,KAAK,oBAAoB,GAAG,CAAC,KAAK,YAAY,KAAK,IAAI,CAAC,EAAE;kBAClF,WAAW,OAAO,gBAAgB,UAAU;AAC1C,kCAAc,KAAK,GAAG,KAAK,oBAAoB,GAAG,CAAC,KAAK,WAAW,EAAE;kBACvE;gBACF,CAAC;AAED,oBAAI,cAAc,SAAS,GAAG;AAC5B,uBAAK,eAAe,6BAA6B,cAAc,KAAK,IAAI;gBAC1E,OAAO;AACL,uBAAK,eAAe,MAAM,MAAM,WAAW;gBAC7C;cACF,OAAO;AAEL,sBAAM,mBAAmB,MAAM;AAC/B,oBAAI,gBAA0B,CAAA;AAE9B,uBAAO,KAAK,gBAAgB,EAAE,QAAQ,SAAM;AAC1C,wBAAM,cAAc,iBAAiB,GAAG;AACxC,sBAAI,MAAM,QAAQ,WAAW,GAAG;AAC9B,kCAAc,KAAK,GAAG,KAAK,oBAAoB,GAAG,CAAC,KAAK,YAAY,KAAK,IAAI,CAAC,EAAE;kBAClF,WAAW,OAAO,gBAAgB,UAAU;AAC1C,kCAAc,KAAK,GAAG,KAAK,oBAAoB,GAAG,CAAC,KAAK,WAAW,EAAE;kBACvE;gBACF,CAAC;AAED,oBAAI,cAAc,SAAS,GAAG;AAC5B,uBAAK,eAAe,6BAA6B,cAAc,KAAK,IAAI;gBAC1E,OAAO;AACL,uBAAK,eAAe,MAAM,OAAO,WAAW;gBAC9C;cACF;YACF,OAAO;AACL,mBAAK,eAAe,MAAM,OAAO,WAAW;YAC9C;UACF,OAAO;AACL,iBAAK,eAAe,MAAM,OAAO,WAAW;UAC9C;QACF;OACD;IACH,OAAO;AACL,WAAK,qBAAoB;IAC3B;EACF;EAEQ,uBAAoB;AAC1B,WAAO,KAAK,KAAK,aAAa,QAAQ,EAAE,QAAQ,SAAM;AACpD,YAAM,UAAU,KAAK,aAAa,IAAI,GAAG;AACzC,eAAS,cAAa;IACxB,CAAC;EACH;EAEA,YAAS;AACP,SAAK,OAAO,SAAS,CAAC,QAAQ,CAAC;EACjC;EAEA,eAAe,OAAU;AACvB,UAAM,OAAO,MAAM,OAAO,MAAM,CAAC;AACjC,QAAI,MAAM;AACR,WAAK,aAAa,WAAW;QAC3B,UAAU;OACX;IACH;EACF;EAEA,wBAAqB;AACnB,YAAQ,IAAI,8CAAoC;AAGhD,UAAM,YAAY,KAAK,aAAa;AAGpC,UAAM,WAAW;MACf,OAAO,UAAU,SAAS;MAC1B,UAAU,UAAU,YAAY;MAChC,KAAK,UAAU,OAAO;MACtB,QAAQ,UAAU,UAAU;MAC5B,aAAa,UAAU,eAAe;MACtC,eAAe,UAAU,iBAAiB;MAC1C,kBAAkB,UAAU,oBAAoB;MAChD,eAAe,UAAU,iBAAiB;MAC1C,KAAK,UAAU,OAAO;;MACtB,YAAY,UAAU,cAAc;MACpC,aAAa,UAAU,eAAe;MACtC,YAAY;MACZ,uBAAuB;MACvB,oBAAoB;MACpB,UAAU;;AAGZ,YAAQ,IAAI,wBAAiB,QAAQ;AAGrC,YAAQ,IAAI,uDAA6C;AACzD,SAAK,YAAY,wBAAwB,QAAQ,EAAE,UAAU;MAC3D,MAAM,CAAC,aAAY;AACjB,gBAAQ,IAAI,mCAA2B,QAAQ;AAC/C,cAAM,+HAAiH;AAGvH,aAAK,qBAAoB;MAC3B;MACA,OAAO,CAAC,UAAS;AACf,gBAAQ,MAAM,sCAA2B,KAAK;AAE9C,YAAI,MAAM,WAAW,KAAK;AACxB,gBAAM,kHAA0G;QAClH,WAAW,MAAM,WAAW,KAAK;AAC/B,gBAAM,kHAAoG;QAC5G,OAAO;AACL,gBAAM,sCAA2B,MAAM,MAAM,MAAM,MAAM,UAAU;yCAAyC;QAC9G;MACF;KACD;EACH;EAEA,uBAAoB;AAClB,YAAQ,IAAI,+DAAqD;AAEjE,QAAI,CAAC,KAAK,aAAa,OAAO;AAC5B,YAAM,iFAA6E;AACnF;IACF;AAEA,UAAM,YAAY,KAAK,aAAa;AACpC,UAAM,WAAW,IAAI,SAAQ;AAG7B,aAAS,OAAO,SAAS,UAAU,KAAK;AACxC,aAAS,OAAO,YAAY,UAAU,QAAQ;AAC9C,aAAS,OAAO,OAAO,UAAU,GAAG;AACpC,aAAS,OAAO,UAAU,UAAU,MAAM;AAC1C,aAAS,OAAO,aAAa,UAAU,WAAW;AAClD,QAAI,UAAU,eAAe;AAC3B,eAAS,OAAO,iBAAiB,UAAU,aAAa;IAC1D;AACA,aAAS,OAAO,oBAAoB,UAAU,gBAAgB;AAC9D,aAAS,OAAO,iBAAiB,UAAU,aAAa;AACxD,aAAS,OAAO,OAAO,UAAU,GAAG;AACpC,aAAS,OAAO,cAAc,UAAU,UAAU;AAElD,QAAI,UAAU,aAAa;AACzB,eAAS,OAAO,eAAe,UAAU,WAAW;IACtD;AAEA,aAAS,OAAO,cAAc,UAAU,YAAY,SAAQ,KAAM,MAAM;AACxE,aAAS,OAAO,yBAAyB,UAAU,uBAAuB,SAAQ,KAAM,GAAG;AAC3F,aAAS,OAAO,sBAAsB,UAAU,oBAAoB,SAAQ,KAAM,MAAM;AACxF,aAAS,OAAO,YAAY,MAAM;AAGlC,QAAI,UAAU,UAAU;AACtB,eAAS,OAAO,YAAY,UAAU,QAAQ;IAChD,OAAO;AAEL,YAAM,kBAAkB,IAAI,KAAK;QAC/B,IAAI,WAAW;UACb;UAAM;UAAM;UAAM;UAAM;UAAM;UAAM;UAAM;UAAM;UAAM;UAAM;UAAM;UAClE;UAAM;UAAM;UAAM;UAAM;UAAM;UAAM;UAAM;UAAM;UAAM;UAAM;UAAM;UAClE;UAAM;UAAM;UAAM;UAAM;UAAM;UAAM;UAAM;UAAM;UAAM;UAAM;UAAM;UAClE;UAAM;UAAM;UAAM;UAAM;UAAM;UAAM;UAAM;UAAM;UAAM;UAAM;UAAM;UAClE;UAAM;UAAM;UAAM;UAAM;UAAM;UAAM;UAAM;UAAM;UAAM;UAAM;UAAM;UAClE;UAAM;UAAM;UAAM;UAAM;UAAM;UAAM;SACrC;SACA,EAAE,MAAM,YAAW,CAAE;AAExB,YAAM,kBAAkB,IAAI,KAAK,CAAC,eAAe,GAAG,oBAAoB,EAAE,MAAM,YAAW,CAAE;AAC7F,eAAS,OAAO,YAAY,eAAe;IAC7C;AAEA,YAAQ,IAAI,4CAAkC;AAC9C,aAAS,QAAQ,SAAS,QAAO,GAAI;AACnC,YAAM,MAAM,KAAK,CAAC;AAClB,YAAM,QAAQ,KAAK,CAAC;AACpB,UAAI,iBAAiB,MAAM;AACzB,gBAAQ,IAAI,GAAG,GAAG,YAAY,MAAM,IAAI,KAAK,MAAM,IAAI,WAAW,MAAM,IAAI,GAAG;MACjF,OAAO;AACL,gBAAQ,IAAI,GAAG,GAAG,KAAK,KAAK,EAAE;MAChC;IACF;AAEA,SAAK,YAAY,oBAAoB,QAAQ,EAAE,UAAU;MACvD,MAAM,CAAC,aAAY;AACjB,gBAAQ,IAAI,iDAAsC,QAAQ;AAC1D,cAAM,+FAAqF;MAC7F;MACA,OAAO,CAAC,UAAS;AACf,gBAAQ,MAAM,oDAAsC,KAAK;AACzD,cAAM,oDAAsC,MAAM,MAAM,MAAM,MAAM,UAAU;yCAAyC;MACzH;KACD;EACH;EAEA,kBAAe;AACb,YAAQ,IAAI,6CAAmC;AAE/C,SAAK,YAAY,4BAA2B,EAAG,UAAU;MACvD,MAAM,CAAC,aAAY;AACjB,gBAAQ,IAAI,kCAA0B,QAAQ;AAC9C,cAAM,6FAAkF;MAC1F;MACA,OAAO,CAAC,UAAS;AACf,gBAAQ,MAAM,qCAA0B,KAAK;AAC7C,cAAM,qCAA0B,MAAM,MAAM,MAAM,MAAM,UAAU;yCAAyC;MAC7G;KACD;EACH;;EAGA,IAAI,QAAK;AAAK,WAAO,KAAK,aAAa,IAAI,OAAO;EAAG;EACrD,IAAI,WAAQ;AAAK,WAAO,KAAK,aAAa,IAAI,UAAU;EAAG;EAC3D,IAAI,kBAAe;AAAK,WAAO,KAAK,aAAa,IAAI,iBAAiB;EAAG;EACzE,IAAI,MAAG;AAAK,WAAO,KAAK,aAAa,IAAI,KAAK;EAAG;EACjD,IAAI,SAAM;AAAK,WAAO,KAAK,aAAa,IAAI,QAAQ;EAAG;EACvD,IAAI,cAAW;AAAK,WAAO,KAAK,aAAa,IAAI,aAAa;EAAG;EACjE,IAAI,gBAAa;AAAK,WAAO,KAAK,aAAa,IAAI,eAAe;EAAG;EACrE,IAAI,mBAAgB;AAAK,WAAO,KAAK,aAAa,IAAI,kBAAkB;EAAG;EAC3E,IAAI,gBAAa;AAAK,WAAO,KAAK,aAAa,IAAI,eAAe;EAAG;EACrE,IAAI,cAAW;AAAK,WAAO,KAAK,aAAa,IAAI,aAAa;EAAG;EACjE,IAAI,MAAG;AAAK,WAAO,KAAK,aAAa,IAAI,KAAK;EAAG;EACjD,IAAI,aAAU;AAAK,WAAO,KAAK,aAAa,IAAI,YAAY;EAAG;EAC/D,IAAI,aAAU;AAAK,WAAO,KAAK,aAAa,IAAI,YAAY;EAAG;EAC/D,IAAI,wBAAqB;AAAK,WAAO,KAAK,aAAa,IAAI,uBAAuB;EAAG;EACrF,IAAI,qBAAkB;AAAK,WAAO,KAAK,aAAa,IAAI,oBAAoB;EAAG;;EAG/E,IAAI,MAAG;AAAK,WAAO,KAAK,aAAa,IAAI,KAAK;EAAG;EACjD,IAAI,QAAK;AAAK,WAAO,KAAK,aAAa,IAAI,OAAO;EAAG;EACrD,IAAI,aAAU;AAAK,WAAO,KAAK,aAAa,IAAI,YAAY;EAAG;EAC/D,IAAI,OAAI;AAAK,WAAO,KAAK,aAAa,IAAI,MAAM;EAAG;EAEnD,IAAI,WAAQ;AAAK,WAAO,KAAK,aAAa,IAAI,UAAU;EAAG;;qCAzchD,oBAAiB,4BAAA,WAAA,GAAA,4BAAA,WAAA,GAAA,4BAAA,MAAA,CAAA;EAAA;yEAAjB,oBAAiB,WAAA,CAAA,CAAA,cAAA,CAAA,GAAA,OAAA,KAAA,MAAA,IAAA,QAAA,CAAA,CAAA,GAAA,oBAAA,GAAA,CAAA,GAAA,eAAA,GAAA,CAAA,GAAA,iBAAA,GAAA,CAAA,GAAA,iBAAA,GAAA,YAAA,WAAA,GAAA,CAAA,GAAA,cAAA,GAAA,CAAA,GAAA,UAAA,GAAA,CAAA,GAAA,YAAA,GAAA,CAAA,OAAA,QAAA,GAAA,CAAA,QAAA,QAAA,MAAA,UAAA,mBAAA,UAAA,eAAA,mBAAA,GAAA,cAAA,GAAA,CAAA,SAAA,oBAAA,GAAA,MAAA,GAAA,CAAA,OAAA,KAAA,GAAA,CAAA,QAAA,QAAA,MAAA,OAAA,mBAAA,OAAA,eAAA,aAAA,GAAA,cAAA,GAAA,CAAA,OAAA,OAAA,GAAA,CAAA,QAAA,SAAA,MAAA,SAAA,mBAAA,SAAA,eAAA,mBAAA,GAAA,cAAA,GAAA,CAAA,OAAA,aAAA,GAAA,CAAA,QAAA,OAAA,MAAA,eAAA,mBAAA,eAAA,eAAA,cAAA,GAAA,cAAA,GAAA,CAAA,OAAA,eAAA,GAAA,CAAA,QAAA,QAAA,MAAA,iBAAA,mBAAA,iBAAA,GAAA,cAAA,GAAA,CAAA,OAAA,UAAA,GAAA,CAAA,QAAA,YAAA,MAAA,YAAA,mBAAA,YAAA,eAAA,sBAAA,GAAA,cAAA,GAAA,CAAA,OAAA,iBAAA,GAAA,CAAA,QAAA,YAAA,MAAA,mBAAA,mBAAA,mBAAA,eAAA,gCAAA,GAAA,cAAA,GAAA,CAAA,OAAA,kBAAA,GAAA,CAAA,QAAA,QAAA,MAAA,oBAAA,mBAAA,oBAAA,eAAA,YAAA,aAAA,KAAA,GAAA,cAAA,GAAA,CAAA,OAAA,eAAA,GAAA,CAAA,QAAA,QAAA,MAAA,iBAAA,mBAAA,iBAAA,eAAA,2BAAA,aAAA,OAAA,GAAA,cAAA,GAAA,CAAA,OAAA,aAAA,GAAA,CAAA,MAAA,eAAA,mBAAA,eAAA,eAAA,mCAAA,QAAA,KAAA,GAAA,cAAA,GAAA,CAAA,OAAA,KAAA,GAAA,CAAA,QAAA,QAAA,MAAA,OAAA,mBAAA,OAAA,eAAA,wBAAA,aAAA,MAAA,GAAA,cAAA,GAAA,CAAA,OAAA,YAAA,GAAA,CAAA,QAAA,QAAA,MAAA,cAAA,mBAAA,cAAA,eAAA,OAAA,aAAA,KAAA,GAAA,cAAA,GAAA,CAAA,OAAA,YAAA,GAAA,CAAA,QAAA,UAAA,MAAA,cAAA,mBAAA,cAAA,eAAA,QAAA,OAAA,OAAA,OAAA,KAAA,QAAA,QAAA,GAAA,cAAA,GAAA,CAAA,GAAA,aAAA,YAAA,GAAA,CAAA,OAAA,uBAAA,GAAA,CAAA,QAAA,UAAA,MAAA,yBAAA,mBAAA,yBAAA,eAAA,KAAA,OAAA,KAAA,OAAA,MAAA,GAAA,cAAA,GAAA,CAAA,OAAA,oBAAA,GAAA,CAAA,QAAA,UAAA,MAAA,sBAAA,mBAAA,sBAAA,eAAA,QAAA,OAAA,KAAA,QAAA,QAAA,GAAA,cAAA,GAAA,CAAA,GAAA,eAAA,GAAA,CAAA,OAAA,KAAA,GAAA,CAAA,QAAA,QAAA,MAAA,OAAA,mBAAA,OAAA,eAAA,+BAAA,GAAA,cAAA,GAAA,CAAA,OAAA,YAAA,GAAA,CAAA,QAAA,QAAA,MAAA,cAAA,mBAAA,cAAA,eAAA,QAAA,aAAA,KAAA,GAAA,cAAA,GAAA,CAAA,OAAA,OAAA,GAAA,CAAA,QAAA,QAAA,MAAA,SAAA,mBAAA,SAAA,eAAA,SAAA,GAAA,cAAA,GAAA,CAAA,OAAA,MAAA,GAAA,CAAA,QAAA,QAAA,MAAA,QAAA,mBAAA,QAAA,SAAA,WAAA,YAAA,IAAA,GAAA,gBAAA,GAAA,oBAAA,WAAA,UAAA,aAAA,GAAA,CAAA,OAAA,UAAA,GAAA,CAAA,QAAA,QAAA,MAAA,YAAA,UAAA,WAAA,GAAA,gBAAA,GAAA,QAAA,GAAA,CAAA,SAAA,sBAAA,GAAA,MAAA,GAAA,CAAA,SAAA,uBAAA,GAAA,MAAA,GAAA,CAAA,GAAA,cAAA,GAAA,CAAA,QAAA,UAAA,GAAA,OAAA,iBAAA,GAAA,SAAA,UAAA,GAAA,CAAA,QAAA,UAAA,GAAA,OAAA,eAAA,GAAA,UAAA,GAAA,CAAA,SAAA,WAAA,GAAA,MAAA,GAAA,CAAA,GAAA,kBAAA,GAAA,CAAA,GAAA,MAAA,GAAA,CAAA,GAAA,SAAA,cAAA,GAAA,CAAA,GAAA,eAAA,UAAA,GAAA,CAAA,GAAA,SAAA,eAAA,GAAA,CAAA,GAAA,SAAA,CAAA,GAAA,UAAA,SAAA,2BAAA,IAAA,KAAA;AAAA,QAAA,KAAA,GAAA;AC1C9B,MAAA,yBAAA,GAAA,OAAA,CAAA,EAAgC,GAAA,OAAA,CAAA,EACH,GAAA,OAAA,CAAA,EACI,GAAA,IAAA;AACvB,MAAA,iBAAA,GAAA,yBAAA;AAAuB,MAAA,uBAAA;AAC3B,MAAA,yBAAA,GAAA,GAAA;AAAG,MAAA,iBAAA,GAAA,mCAAA;AAA8B,MAAA,uBAAA,EAAI;AAGvC,MAAA,yBAAA,GAAA,QAAA,CAAA;AAAiC,MAAA,qBAAA,YAAA,SAAA,sDAAA;AAAA,eAAY,IAAA,SAAA;MAAU,CAAA;AAGrD,MAAA,yBAAA,GAAA,OAAA,CAAA,EAA0B,GAAA,IAAA;AACpB,MAAA,iBAAA,IAAA,2BAAA;AAAyB,MAAA,uBAAA;AAE7B,MAAA,yBAAA,IAAA,OAAA,CAAA,EAAsB,IAAA,OAAA,CAAA,EACI,IAAA,SAAA,CAAA;AACF,MAAA,iBAAA,IAAA,aAAA;AAAQ,MAAA,uBAAA;AAC5B,MAAA,oBAAA,IAAA,SAAA,CAAA;AAQA,MAAA,qBAAA,IAAA,mCAAA,GAAA,GAAA,OAAA,CAAA;AAIF,MAAA,uBAAA;AAEA,MAAA,yBAAA,IAAA,OAAA,CAAA,EAAwB,IAAA,SAAA,EAAA;AACL,MAAA,iBAAA,IAAA,OAAA;AAAK,MAAA,uBAAA;AACtB,MAAA,oBAAA,IAAA,SAAA,EAAA;AAQA,MAAA,qBAAA,IAAA,mCAAA,GAAA,GAAA,OAAA,CAAA;AAIF,MAAA,uBAAA,EAAM;AAGR,MAAA,yBAAA,IAAA,OAAA,CAAA,EAAsB,IAAA,OAAA,CAAA,EACI,IAAA,SAAA,EAAA;AACH,MAAA,iBAAA,IAAA,SAAA;AAAO,MAAA,uBAAA;AAC1B,MAAA,oBAAA,IAAA,SAAA,EAAA;AAQA,MAAA,qBAAA,IAAA,mCAAA,GAAA,GAAA,OAAA,CAAA;AAIF,MAAA,uBAAA;AAEA,MAAA,yBAAA,IAAA,OAAA,CAAA,EAAwB,IAAA,SAAA,EAAA;AACG,MAAA,iBAAA,IAAA,mBAAA;AAAW,MAAA,uBAAA;AACpC,MAAA,oBAAA,IAAA,SAAA,EAAA;AAQA,MAAA,qBAAA,IAAA,mCAAA,GAAA,GAAA,OAAA,CAAA;AAIF,MAAA,uBAAA,EAAM;AAGR,MAAA,yBAAA,IAAA,OAAA,CAAA,EAAwB,IAAA,SAAA,EAAA;AACK,MAAA,iBAAA,IAAA,qBAAA;AAAmB,MAAA,uBAAA;AAC9C,MAAA,oBAAA,IAAA,SAAA,EAAA;AAOA,MAAA,qBAAA,IAAA,mCAAA,GAAA,GAAA,OAAA,CAAA;AAGF,MAAA,uBAAA;AAEA,MAAA,yBAAA,IAAA,OAAA,CAAA,EAAsB,IAAA,OAAA,CAAA,EACI,IAAA,SAAA,EAAA;AACA,MAAA,iBAAA,IAAA,gBAAA;AAAc,MAAA,uBAAA;AACpC,MAAA,oBAAA,IAAA,SAAA,EAAA;AAQA,MAAA,qBAAA,IAAA,mCAAA,GAAA,GAAA,OAAA,CAAA;AAIF,MAAA,uBAAA;AAEA,MAAA,yBAAA,IAAA,OAAA,CAAA,EAAwB,IAAA,SAAA,EAAA;AACO,MAAA,iBAAA,IAAA,6BAAA;AAA2B,MAAA,uBAAA;AACxD,MAAA,oBAAA,IAAA,SAAA,EAAA;AAQA,MAAA,qBAAA,IAAA,mCAAA,GAAA,GAAA,OAAA,CAAA;AAIF,MAAA,uBAAA,EAAM,EACF;AAIR,MAAA,yBAAA,IAAA,OAAA,CAAA,EAA0B,IAAA,IAAA;AACpB,MAAA,iBAAA,IAAA,yBAAA;AAAuB,MAAA,uBAAA;AAE3B,MAAA,yBAAA,IAAA,OAAA,CAAA,EAAsB,IAAA,OAAA,CAAA,EACI,IAAA,SAAA,EAAA;AACQ,MAAA,iBAAA,IAAA,qBAAA;AAAmB,MAAA,uBAAA;AACjD,MAAA,oBAAA,IAAA,SAAA,EAAA;AASA,MAAA,qBAAA,IAAA,mCAAA,GAAA,GAAA,OAAA,CAAA;AAIF,MAAA,uBAAA;AAEA,MAAA,yBAAA,IAAA,OAAA,CAAA,EAAwB,IAAA,SAAA,EAAA;AACK,MAAA,iBAAA,IAAA,kBAAA;AAAgB,MAAA,uBAAA;AAC3C,MAAA,oBAAA,IAAA,SAAA,EAAA;AASA,MAAA,qBAAA,IAAA,mCAAA,GAAA,GAAA,OAAA,CAAA;AAKF,MAAA,uBAAA,EAAM;AAGR,MAAA,yBAAA,IAAA,OAAA,CAAA,EAAwB,IAAA,SAAA,EAAA;AACG,MAAA,iBAAA,IAAA,yBAAA;AAAuB,MAAA,uBAAA;AAChD,MAAA,oBAAA,IAAA,YAAA,EAAA;AAQF,MAAA,uBAAA;AAEA,MAAA,yBAAA,IAAA,OAAA,CAAA,EAAsB,IAAA,OAAA,CAAA,EACI,IAAA,SAAA,EAAA;AACL,MAAA,iBAAA,IAAA,OAAA;AAAK,MAAA,uBAAA;AACtB,MAAA,oBAAA,IAAA,SAAA,EAAA;AASA,MAAA,qBAAA,IAAA,mCAAA,GAAA,GAAA,OAAA,CAAA;AAIF,MAAA,uBAAA;AAEA,MAAA,yBAAA,IAAA,OAAA,CAAA,EAAwB,IAAA,SAAA,EAAA;AACE,MAAA,iBAAA,IAAA,eAAA;AAAa,MAAA,uBAAA;AACrC,MAAA,oBAAA,IAAA,SAAA,EAAA;AASA,MAAA,qBAAA,IAAA,mCAAA,GAAA,GAAA,OAAA,CAAA;AAIF,MAAA,uBAAA,EAAM;AAIR,MAAA,yBAAA,IAAA,OAAA,CAAA,EAAsB,IAAA,OAAA,CAAA,EACI,IAAA,SAAA,EAAA;AACE,MAAA,iBAAA,IAAA,kBAAA;AAAgB,MAAA,uBAAA;AACxC,MAAA,oBAAA,IAAA,SAAA,EAAA;AAWA,MAAA,qBAAA,IAAA,mCAAA,GAAA,GAAA,OAAA,CAAA;AAKA,MAAA,yBAAA,IAAA,SAAA,EAAA;AAAoC,MAAA,iBAAA,IAAA,+CAAA;AAA6C,MAAA,uBAAA,EAAQ;AAG3F,MAAA,yBAAA,IAAA,OAAA,CAAA,EAAwB,IAAA,SAAA,EAAA;AACa,MAAA,iBAAA,IAAA,sCAAA;AAA8B,MAAA,uBAAA;AACjE,MAAA,oBAAA,IAAA,SAAA,EAAA;AAUA,MAAA,qBAAA,IAAA,mCAAA,GAAA,GAAA,OAAA,CAAA;AAIA,MAAA,yBAAA,IAAA,SAAA,EAAA;AAAoC,MAAA,iBAAA,IAAA,+CAAA;AAA0C,MAAA,uBAAA,EAAQ,EAClF;AAGR,MAAA,yBAAA,IAAA,OAAA,CAAA,EAAwB,IAAA,SAAA,EAAA;AACU,MAAA,iBAAA,IAAA,uCAAA;AAAgC,MAAA,uBAAA;AAChE,MAAA,oBAAA,IAAA,SAAA,EAAA;AAUA,MAAA,qBAAA,IAAA,mCAAA,GAAA,GAAA,OAAA,CAAA;AAIA,MAAA,yBAAA,IAAA,SAAA,EAAA;AAAoC,MAAA,iBAAA,IAAA,8CAAA;AAA4C,MAAA,uBAAA,EAAQ;AAI1F,MAAA,yBAAA,KAAA,MAAA,EAAA;AAA0B,MAAA,iBAAA,KAAA,mCAAA;AAA0B,MAAA,uBAAA;AAEpD,MAAA,yBAAA,KAAA,OAAA,CAAA,EAAwB,KAAA,SAAA,EAAA;AACL,MAAA,iBAAA,KAAA,OAAA;AAAK,MAAA,uBAAA;AACtB,MAAA,oBAAA,KAAA,SAAA,EAAA;AAQA,MAAA,qBAAA,KAAA,oCAAA,GAAA,GAAA,OAAA,CAAA;AAIF,MAAA,uBAAA;AAEA,MAAA,yBAAA,KAAA,OAAA,CAAA,EAAsB,KAAA,OAAA,CAAA,EACI,KAAA,SAAA,EAAA;AACE,MAAA,iBAAA,KAAA,eAAA;AAAa,MAAA,uBAAA;AACrC,MAAA,oBAAA,KAAA,SAAA,EAAA;AASA,MAAA,qBAAA,KAAA,oCAAA,GAAA,GAAA,OAAA,CAAA;AAIF,MAAA,uBAAA;AAEA,MAAA,yBAAA,KAAA,OAAA,CAAA,EAAwB,KAAA,SAAA,EAAA;AACH,MAAA,iBAAA,KAAA,SAAA;AAAO,MAAA,uBAAA;AAC1B,MAAA,oBAAA,KAAA,SAAA,EAAA;AAQA,MAAA,qBAAA,KAAA,oCAAA,GAAA,GAAA,OAAA,CAAA;AAIF,MAAA,uBAAA,EAAM;AAGR,MAAA,yBAAA,KAAA,OAAA,CAAA,EAAwB,KAAA,SAAA,EAAA;AACJ,MAAA,iBAAA,KAAA,QAAA;AAAM,MAAA,uBAAA;AACxB,MAAA,oBAAA,KAAA,SAAA,EAAA;AASA,MAAA,yBAAA,KAAA,SAAA,EAAA;AAAoC,MAAA,iBAAA,KAAA,mDAAA;AAA8C,MAAA,uBAAA,EAAQ;AAG5F,MAAA,yBAAA,KAAA,OAAA,CAAA,EAAwB,KAAA,SAAA,EAAA;AACA,MAAA,iBAAA,KAAA,kCAAA;AAAgC,MAAA,uBAAA;AACtD,MAAA,yBAAA,KAAA,SAAA,EAAA;AAME,MAAA,qBAAA,UAAA,SAAA,qDAAA,QAAA;AAAA,eAAU,IAAA,eAAA,MAAA;MAAsB,CAAA;AANlC,MAAA,uBAAA;AAQA,MAAA,yBAAA,KAAA,SAAA,EAAA;AAAoC,MAAA,iBAAA,KAAA,+EAAA;AAAiE,MAAA,uBAAA,EAAQ,EACzG;AAIR,MAAA,qBAAA,KAAA,oCAAA,GAAA,GAAA,OAAA,EAAA,EAAqD,KAAA,oCAAA,GAAA,GAAA,OAAA,EAAA;AASrD,MAAA,yBAAA,KAAA,OAAA,EAAA,EAA0B,KAAA,UAAA,EAAA;AAItB,MAAA,qBAAA,SAAA,SAAA,uDAAA;AAAA,eAAS,IAAA,UAAA;MAAW,CAAA;AAGpB,MAAA,iBAAA,KAAA,4BAAA;AACF,MAAA,uBAAA;AAEA,MAAA,yBAAA,KAAA,UAAA,EAAA;AAKE,MAAA,qBAAA,KAAA,qCAAA,GAAA,GAAA,QAAA,EAAA;AACA,MAAA,iBAAA,GAAA;AACF,MAAA,uBAAA,EAAS,EACL,EACD,EACH;;;AAlYE,MAAA,oBAAA,CAAA;AAAA,MAAA,qBAAA,aAAA,IAAA,YAAA;AAcI,MAAA,oBAAA,CAAA;AAAA,MAAA,sBAAA,eAAA,IAAA,UAAA,OAAA,OAAA,IAAA,OAAA,aAAA,IAAA,UAAA,OAAA,OAAA,IAAA,OAAA,QAAA;AAG6B,MAAA,oBAAA;AAAA,MAAA,qBAAA,SAAA,IAAA,UAAA,OAAA,OAAA,IAAA,OAAA,aAAA,IAAA,UAAA,OAAA,OAAA,IAAA,OAAA,QAAA;AAa7B,MAAA,oBAAA,CAAA;AAAA,MAAA,sBAAA,eAAA,IAAA,OAAA,OAAA,OAAA,IAAA,IAAA,aAAA,IAAA,OAAA,OAAA,OAAA,IAAA,IAAA,QAAA;AAG6B,MAAA,oBAAA;AAAA,MAAA,qBAAA,SAAA,IAAA,OAAA,OAAA,OAAA,IAAA,IAAA,aAAA,IAAA,OAAA,OAAA,OAAA,IAAA,IAAA,QAAA;AAe7B,MAAA,oBAAA,CAAA;AAAA,MAAA,sBAAA,eAAA,IAAA,SAAA,OAAA,OAAA,IAAA,MAAA,aAAA,IAAA,SAAA,OAAA,OAAA,IAAA,MAAA,QAAA;AAG6B,MAAA,oBAAA;AAAA,MAAA,qBAAA,SAAA,IAAA,SAAA,OAAA,OAAA,IAAA,MAAA,aAAA,IAAA,SAAA,OAAA,OAAA,IAAA,MAAA,QAAA;AAa7B,MAAA,oBAAA,CAAA;AAAA,MAAA,sBAAA,eAAA,IAAA,eAAA,OAAA,OAAA,IAAA,YAAA,aAAA,IAAA,eAAA,OAAA,OAAA,IAAA,YAAA,QAAA;AAG6B,MAAA,oBAAA;AAAA,MAAA,qBAAA,SAAA,IAAA,eAAA,OAAA,OAAA,IAAA,YAAA,aAAA,IAAA,eAAA,OAAA,OAAA,IAAA,YAAA,QAAA;AAc/B,MAAA,oBAAA,CAAA;AAAA,MAAA,sBAAA,eAAA,IAAA,iBAAA,OAAA,OAAA,IAAA,cAAA,aAAA,IAAA,iBAAA,OAAA,OAAA,IAAA,cAAA,QAAA;AAE6B,MAAA,oBAAA;AAAA,MAAA,qBAAA,SAAA,IAAA,iBAAA,OAAA,OAAA,IAAA,cAAA,aAAA,IAAA,iBAAA,OAAA,OAAA,IAAA,cAAA,QAAA;AAa3B,MAAA,oBAAA,CAAA;AAAA,MAAA,sBAAA,eAAA,IAAA,YAAA,OAAA,OAAA,IAAA,SAAA,aAAA,IAAA,YAAA,OAAA,OAAA,IAAA,SAAA,QAAA;AAG6B,MAAA,oBAAA;AAAA,MAAA,qBAAA,SAAA,IAAA,YAAA,OAAA,OAAA,IAAA,SAAA,aAAA,IAAA,YAAA,OAAA,OAAA,IAAA,SAAA,QAAA;AAa7B,MAAA,oBAAA,CAAA;AAAA,MAAA,sBAAA,eAAA,IAAA,mBAAA,OAAA,OAAA,IAAA,gBAAA,aAAA,IAAA,mBAAA,OAAA,OAAA,IAAA,gBAAA,QAAA;AAG6B,MAAA,oBAAA;AAAA,MAAA,qBAAA,SAAA,IAAA,mBAAA,OAAA,OAAA,IAAA,gBAAA,aAAA,IAAA,mBAAA,OAAA,OAAA,IAAA,gBAAA,QAAA;AAoB7B,MAAA,oBAAA,CAAA;AAAA,MAAA,sBAAA,eAAA,IAAA,oBAAA,OAAA,OAAA,IAAA,iBAAA,aAAA,IAAA,oBAAA,OAAA,OAAA,IAAA,iBAAA,QAAA;AAI6B,MAAA,oBAAA;AAAA,MAAA,qBAAA,SAAA,IAAA,oBAAA,OAAA,OAAA,IAAA,iBAAA,aAAA,IAAA,oBAAA,OAAA,OAAA,IAAA,iBAAA,QAAA;AAa7B,MAAA,oBAAA,CAAA;AAAA,MAAA,sBAAA,eAAA,IAAA,iBAAA,OAAA,OAAA,IAAA,cAAA,aAAA,IAAA,iBAAA,OAAA,OAAA,IAAA,cAAA,QAAA;AAI6B,MAAA,oBAAA;AAAA,MAAA,qBAAA,SAAA,IAAA,iBAAA,OAAA,OAAA,IAAA,cAAA,aAAA,IAAA,iBAAA,OAAA,OAAA,IAAA,cAAA,QAAA;AAc/B,MAAA,oBAAA,CAAA;AAAA,MAAA,sBAAA,eAAA,IAAA,eAAA,OAAA,OAAA,IAAA,YAAA,aAAA,IAAA,eAAA,OAAA,OAAA,IAAA,YAAA,QAAA;AAcE,MAAA,oBAAA,CAAA;AAAA,MAAA,sBAAA,eAAA,IAAA,OAAA,OAAA,OAAA,IAAA,IAAA,aAAA,IAAA,OAAA,OAAA,OAAA,IAAA,IAAA,QAAA;AAI6B,MAAA,oBAAA;AAAA,MAAA,qBAAA,SAAA,IAAA,OAAA,OAAA,OAAA,IAAA,IAAA,aAAA,IAAA,OAAA,OAAA,OAAA,IAAA,IAAA,QAAA;AAa7B,MAAA,oBAAA,CAAA;AAAA,MAAA,sBAAA,eAAA,IAAA,cAAA,OAAA,OAAA,IAAA,WAAA,aAAA,IAAA,cAAA,OAAA,OAAA,IAAA,WAAA,QAAA;AAI6B,MAAA,oBAAA;AAAA,MAAA,qBAAA,SAAA,IAAA,cAAA,OAAA,OAAA,IAAA,WAAA,aAAA,IAAA,cAAA,OAAA,OAAA,IAAA,WAAA,QAAA;AAgB7B,MAAA,oBAAA,CAAA;AAAA,MAAA,sBAAA,eAAA,IAAA,cAAA,OAAA,OAAA,IAAA,WAAA,aAAA,IAAA,cAAA,OAAA,OAAA,IAAA,WAAA,QAAA;AAM6B,MAAA,oBAAA;AAAA,MAAA,qBAAA,SAAA,IAAA,cAAA,OAAA,OAAA,IAAA,WAAA,aAAA,IAAA,cAAA,OAAA,OAAA,IAAA,WAAA,QAAA;AAe7B,MAAA,oBAAA,CAAA;AAAA,MAAA,sBAAA,eAAA,IAAA,yBAAA,OAAA,OAAA,IAAA,sBAAA,aAAA,IAAA,yBAAA,OAAA,OAAA,IAAA,sBAAA,QAAA;AAK6B,MAAA,oBAAA;AAAA,MAAA,qBAAA,SAAA,IAAA,yBAAA,OAAA,OAAA,IAAA,sBAAA,aAAA,IAAA,yBAAA,OAAA,OAAA,IAAA,sBAAA,QAAA;AAe/B,MAAA,oBAAA,CAAA;AAAA,MAAA,sBAAA,eAAA,IAAA,sBAAA,OAAA,OAAA,IAAA,mBAAA,aAAA,IAAA,sBAAA,OAAA,OAAA,IAAA,mBAAA,QAAA;AAK6B,MAAA,oBAAA;AAAA,MAAA,qBAAA,SAAA,IAAA,sBAAA,OAAA,OAAA,IAAA,mBAAA,aAAA,IAAA,sBAAA,OAAA,OAAA,IAAA,mBAAA,QAAA;AAiB7B,MAAA,oBAAA,CAAA;AAAA,MAAA,sBAAA,eAAA,IAAA,OAAA,OAAA,OAAA,IAAA,IAAA,aAAA,IAAA,OAAA,OAAA,OAAA,IAAA,IAAA,QAAA;AAG6B,MAAA,oBAAA;AAAA,MAAA,qBAAA,SAAA,IAAA,OAAA,OAAA,OAAA,IAAA,IAAA,aAAA,IAAA,OAAA,OAAA,OAAA,IAAA,IAAA,QAAA;AAc3B,MAAA,oBAAA,CAAA;AAAA,MAAA,sBAAA,eAAA,IAAA,cAAA,OAAA,OAAA,IAAA,WAAA,aAAA,IAAA,cAAA,OAAA,OAAA,IAAA,WAAA,QAAA;AAI6B,MAAA,oBAAA;AAAA,MAAA,qBAAA,SAAA,IAAA,cAAA,OAAA,OAAA,IAAA,WAAA,aAAA,IAAA,cAAA,OAAA,OAAA,IAAA,WAAA,QAAA;AAa7B,MAAA,oBAAA,CAAA;AAAA,MAAA,sBAAA,eAAA,IAAA,SAAA,OAAA,OAAA,IAAA,MAAA,aAAA,IAAA,SAAA,OAAA,OAAA,IAAA,MAAA,QAAA;AAG6B,MAAA,oBAAA;AAAA,MAAA,qBAAA,SAAA,IAAA,SAAA,OAAA,OAAA,IAAA,MAAA,aAAA,IAAA,SAAA,OAAA,OAAA,IAAA,MAAA,QAAA;AA2B/B,MAAA,oBAAA,EAAA;AAAA,MAAA,sBAAA,eAAA,IAAA,YAAA,OAAA,OAAA,IAAA,SAAA,aAAA,IAAA,YAAA,OAAA,OAAA,IAAA,SAAA,QAAA;AAS2B,MAAA,oBAAA,CAAA;AAAA,MAAA,qBAAA,QAAA,IAAA,YAAA;AAIC,MAAA,oBAAA;AAAA,MAAA,qBAAA,QAAA,IAAA,cAAA;AAU9B,MAAA,oBAAA,CAAA;AAAA,MAAA,qBAAA,YAAA,IAAA,SAAA;AAQA,MAAA,oBAAA,CAAA;AAAA,MAAA,qBAAA,YAAA,IAAA,SAAA;AAEO,MAAA,oBAAA;AAAA,MAAA,qBAAA,QAAA,IAAA,SAAA;AACP,MAAA,oBAAA;AAAA,MAAA,6BAAA,KAAA,IAAA,YAAA,mBAAA,cAAA,GAAA;;oBD/VE,cAAY,MAAE,qBAAmB,oBAAA,sBAAA,qBAAA,iBAAA,sBAAA,oBAAA,cAAA,cAAA,oBAAA,eAAA,GAAA,QAAA,CAAA,0mJAAA,EAAA,CAAA;;;sEAIhC,mBAAiB,CAAA;UAP7B;uBACW,gBAAc,YACZ,MAAI,SACP,CAAC,cAAc,mBAAmB,GAAC,UAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;GAAA,QAAA,CAAA,i4HAAA,EAAA,CAAA;;;;6EAIjC,mBAAiB,EAAA,WAAA,qBAAA,UAAA,0DAAA,YAAA,GAAA,CAAA;AAAA,GAAA;", "names": []}
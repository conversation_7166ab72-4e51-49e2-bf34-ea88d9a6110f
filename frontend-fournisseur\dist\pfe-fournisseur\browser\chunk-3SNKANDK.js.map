{"version": 3, "sources": ["src/app/services/fournisseur.service.ts", "src/app/components/profile/profile.component.ts", "src/app/components/profile/profile.component.html"], "sourcesContent": ["import { Injectable } from '@angular/core';\nimport { HttpClient, HttpParams } from '@angular/common/http';\nimport { Observable } from 'rxjs';\nimport { tap } from 'rxjs/operators';\nimport { Fournisseur, FournisseurCreate, FournisseurUpdate, RibValidation, Produit } from '../models';\nimport { environment } from '../../environments/environment';\n\n// Interfaces pour les réponses API\nexport interface FournisseurResponse {\n  success: boolean;\n  data?: Fournisseur;\n  message?: string;\n}\n\nexport interface FournisseurListResponse {\n  success: boolean;\n  data?: Fournisseur[];\n  total?: number;\n  page?: number;\n  pageSize?: number;\n  message?: string;\n}\n\nexport interface CommissionUpdateRequest {\n  commission: number;\n  motif?: string;\n  dateEffet?: Date;\n}\n\nexport interface StatusToggleRequest {\n  estActif: boolean;\n  motif?: string;\n}\n\n@Injectable({\n  providedIn: 'root'\n})\nexport class FournisseurService {\n  private readonly API_URL = `${environment.apiUrl || 'http://localhost:5014/api'}/Fournisseurs`;\n\n  constructor(private http: HttpClient) {}\n\n  /**\n   * GET /api/Fournisseurs - Obtenir tous les fournisseurs avec pagination et filtres\n   */\n  getAll(params?: {\n    page?: number;\n    pageSize?: number;\n    search?: string;\n    estActif?: boolean;\n    sortBy?: string;\n    sortOrder?: 'asc' | 'desc';\n  }): Observable<FournisseurListResponse> {\n    let httpParams = new HttpParams();\n\n    if (params) {\n      if (params.page) httpParams = httpParams.set('page', params.page.toString());\n      if (params.pageSize) httpParams = httpParams.set('pageSize', params.pageSize.toString());\n      if (params.search) httpParams = httpParams.set('search', params.search);\n      if (params.estActif !== undefined) httpParams = httpParams.set('estActif', params.estActif.toString());\n      if (params.sortBy) httpParams = httpParams.set('sortBy', params.sortBy);\n      if (params.sortOrder) httpParams = httpParams.set('sortOrder', params.sortOrder);\n    }\n\n    console.log('📋 Récupération des fournisseurs avec paramètres:', params);\n\n    return this.http.get<FournisseurListResponse>(this.API_URL, { params: httpParams })\n      .pipe(\n        tap(response => console.log('✅ Fournisseurs récupérés:', response))\n      );\n  }\n\n  /**\n   * GET /api/Fournisseurs/{id} - Obtenir un fournisseur par ID\n   */\n  getById(id: number): Observable<FournisseurResponse> {\n    console.log('🔍 Récupération du fournisseur ID:', id);\n\n    return this.http.get<FournisseurResponse>(`${this.API_URL}/${id}`)\n      .pipe(\n        tap(response => console.log('✅ Fournisseur récupéré:', response))\n      );\n  }\n\n  /**\n   * POST /api/Fournisseurs - Créer un nouveau fournisseur\n   */\n  create(fournisseur: FournisseurCreate): Observable<FournisseurResponse> {\n    console.log('➕ Création d\\'un nouveau fournisseur:', fournisseur);\n\n    return this.http.post<FournisseurResponse>(this.API_URL, fournisseur)\n      .pipe(\n        tap(response => console.log('✅ Fournisseur créé:', response))\n      );\n  }\n\n  /**\n   * PUT /api/Fournisseurs/{id} - Mettre à jour un fournisseur\n   */\n  update(id: number, fournisseur: FournisseurUpdate): Observable<FournisseurResponse> {\n    console.log('✏️ Mise à jour du fournisseur ID:', id, fournisseur);\n\n    return this.http.put<FournisseurResponse>(`${this.API_URL}/${id}`, fournisseur)\n      .pipe(\n        tap(response => console.log('✅ Fournisseur mis à jour:', response))\n      );\n  }\n\n  /**\n   * DELETE /api/Fournisseurs/{id} - Supprimer un fournisseur\n   */\n  delete(id: number): Observable<{ success: boolean; message?: string }> {\n    console.log('🗑️ Suppression du fournisseur ID:', id);\n\n    return this.http.delete<{ success: boolean; message?: string }>(`${this.API_URL}/${id}`)\n      .pipe(\n        tap(response => console.log('✅ Fournisseur supprimé:', response))\n      );\n  }\n\n  /**\n   * PATCH /api/Fournisseurs/{id}/toggle-status - Activer/Désactiver un fournisseur\n   */\n  toggleStatus(id: number, request: StatusToggleRequest): Observable<FournisseurResponse> {\n    console.log('🔄 Changement de statut du fournisseur ID:', id, request);\n\n    return this.http.patch<FournisseurResponse>(`${this.API_URL}/${id}/toggle-status`, request)\n      .pipe(\n        tap(response => console.log('✅ Statut du fournisseur modifié:', response))\n      );\n  }\n\n  /**\n   * GET /api/Fournisseurs/{id}/produits - Obtenir les produits d'un fournisseur\n   */\n  getProduits(id: number, params?: {\n    page?: number;\n    pageSize?: number;\n    search?: string;\n    category?: string;\n    estActif?: boolean;\n  }): Observable<{ success: boolean; data?: Produit[]; total?: number; message?: string }> {\n    let httpParams = new HttpParams();\n\n    if (params) {\n      if (params.page) httpParams = httpParams.set('page', params.page.toString());\n      if (params.pageSize) httpParams = httpParams.set('pageSize', params.pageSize.toString());\n      if (params.search) httpParams = httpParams.set('search', params.search);\n      if (params.category) httpParams = httpParams.set('category', params.category);\n      if (params.estActif !== undefined) httpParams = httpParams.set('estActif', params.estActif.toString());\n    }\n\n    console.log('📦 Récupération des produits du fournisseur ID:', id, params);\n\n    return this.http.get<{ success: boolean; data?: Produit[]; total?: number; message?: string }>(`${this.API_URL}/${id}/produits`, { params: httpParams })\n      .pipe(\n        tap(response => console.log('✅ Produits du fournisseur récupérés:', response))\n      );\n  }\n\n  /**\n   * GET /api/Fournisseurs/valider-rib - Valider un RIB\n   */\n  validerRib(rib: string, codeBanque: string): Observable<{ isValid: boolean; message?: string; details?: any }> {\n    const params = new HttpParams()\n      .set('rib', rib)\n      .set('codeBanque', codeBanque);\n\n    console.log('🏦 Validation du RIB:', { rib, codeBanque });\n\n    return this.http.get<{ isValid: boolean; message?: string; details?: any }>(`${this.API_URL}/valider-rib`, { params })\n      .pipe(\n        tap(response => console.log('✅ Validation RIB:', response))\n      );\n  }\n\n  /**\n   * GET /api/Fournisseurs/exists/{id} - Vérifier si un fournisseur existe\n   */\n  exists(id: number): Observable<{ exists: boolean; message?: string }> {\n    console.log('🔍 Vérification de l\\'existence du fournisseur ID:', id);\n\n    return this.http.get<{ exists: boolean; message?: string }>(`${this.API_URL}/exists/${id}`)\n      .pipe(\n        tap(response => console.log('✅ Vérification existence:', response))\n      );\n  }\n\n  /**\n   * PATCH /api/Fournisseurs/{id}/commission - Mettre à jour la commission d'un fournisseur\n   */\n  updateCommission(id: number, request: CommissionUpdateRequest): Observable<FournisseurResponse> {\n    console.log('💰 Mise à jour de la commission du fournisseur ID:', id, request);\n\n    return this.http.patch<FournisseurResponse>(`${this.API_URL}/${id}/commission`, request)\n      .pipe(\n        tap(response => console.log('✅ Commission mise à jour:', response))\n      );\n  }\n\n  /**\n   * Obtenir le fournisseur connecté (basé sur le token)\n   */\n  getCurrentFournisseur(): Observable<FournisseurResponse> {\n    console.log('👤 Récupération du fournisseur connecté');\n\n    return this.http.get<FournisseurResponse>(`${this.API_URL}/current`)\n      .pipe(\n        tap(response => console.log('✅ Fournisseur connecté récupéré:', response))\n      );\n  }\n\n  /**\n   * Valider les données RIB avec retour détaillé\n   */\n  validateRibDetails(rib: string, codeBanque: string): Observable<RibValidation> {\n    return new Observable(observer => {\n      this.validerRib(rib, codeBanque).subscribe({\n        next: (response) => {\n          observer.next({\n            rib,\n            codeBanque,\n            isValid: response.isValid\n          });\n          observer.complete();\n        },\n        error: (error) => observer.error(error)\n      });\n    });\n  }\n\n}\n\n\n", "import { Component, OnInit, signal } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { FormBuilder, FormGroup, FormArray, Validators, ReactiveFormsModule } from '@angular/forms';\nimport { AuthService } from '../../services/auth.service';\nimport { DashboardService, Fournisseur } from '../../services/dashboard.service';\nimport { FournisseurService } from '../../services/fournisseur.service';\nimport { AdresseDto } from '../../models/adresse.model';\nimport { ImageUrlService } from '../../services/image-url.service';\n\n@Component({\n  selector: 'app-profile',\n  standalone: true,\n  imports: [CommonModule, ReactiveFormsModule],\n  templateUrl: './profile.component.html',\n  styleUrls: ['./profile.component.css']\n})\nexport class ProfileComponent implements OnInit {\n  // Signaux pour la gestion d'état\n  fournisseurInfo = signal<Fournisseur | null>(null);\n  isLoading = signal(true);\n  isEditing = signal(false);\n  isSaving = signal(false);\n  error = signal<string | null>(null);\n  successMessage = signal<string | null>(null);\n\n  // Gestion des adresses\n  showOtherAddresses = false;\n  editingAddresses = new Set<number>();\n\n  // Formulaire\n  profileForm: FormGroup;\n\n  constructor(\n    private authService: AuthService,\n    private dashboardService: DashboardService,\n    private fb: FormBuilder,\n    private fournisseurService: FournisseurService,\n    public imageUrlService: ImageUrlService\n  ) {\n    this.profileForm = this.createForm();\n  }\n\n  ngOnInit(): void {\n    console.log('🔍 Contenu du localStorage:');\n    console.log('- token:', !!localStorage.getItem('token'));\n    console.log('- user:', !!localStorage.getItem('user'));\n    console.log('- fournisseur_profile:', !!localStorage.getItem('fournisseur_profile'));\n    console.log('- supplierId:', localStorage.getItem('supplierId'));\n\n    this.loadProfileData();\n  }\n\n  /**\n   * Getter pour le FormArray des adresses\n   */\n  get adresses(): FormArray {\n    return this.profileForm.get('adresses') as FormArray;\n  }\n\n  /**\n   * Créer le formulaire de profil\n   */\n  private createForm(): FormGroup {\n    return this.fb.group({\n      // Informations personnelles\n      nom: ['', [Validators.required, Validators.minLength(2)]],\n      prenom: ['', [Validators.required, Validators.minLength(2)]],\n      email: ['', [Validators.required, Validators.email]],\n      phoneNumber: ['', [Validators.required, Validators.pattern(/^[0-9+\\-\\s()]+$/)]],\n      dateNaissance: [''],\n\n      // Informations entreprise\n      raisonSociale: ['', [Validators.required, Validators.minLength(2)]],\n      matriculeFiscale: ['', [Validators.required]],\n      description: [''],\n      \n      // Informations bancaires (lecture seule)\n      ribMasque: [{value: '', disabled: true}],\n      codeBanque: [{value: '', disabled: true}],\n      \n      // Paramètres commerciaux\n      commission: [{value: 0, disabled: true}],\n      delaiPreparationJours: ['', [Validators.required, Validators.min(1)]],\n      fraisLivraisonBase: ['', [Validators.required, Validators.min(0)]],\n\n      // Adresses\n      adresses: this.fb.array([])\n    });\n  }\n\n  /**\n   * Charger les données du profil depuis localStorage et backend\n   */\n  private loadProfileData(): void {\n    this.isLoading.set(true);\n    this.error.set(null);\n\n    console.log('🔄 Chargement des données du profil...');\n\n    // Priorité 1: Récupérer les données depuis l'API\n    const currentUser = this.authService.getCurrentUser();\n    if (currentUser && currentUser.id) {\n      console.log('🌐 Récupération des données depuis l\\'API pour l\\'utilisateur ID:', currentUser.id);\n\n      this.fournisseurService.getById(currentUser.id).subscribe({\n        next: (response) => {\n          console.log('✅ Données du fournisseur reçues de l\\'API:', response);\n          this.handleApiResponse(response);\n        },\n        error: (error) => {\n          console.error('❌ Erreur lors de la récupération depuis l\\'API:', error);\n          // Fallback vers localStorage\n          this.loadFromLocalStorage();\n        }\n      });\n    } else {\n      console.warn('⚠️ Utilisateur non connecté, fallback vers localStorage');\n      this.loadFromLocalStorage();\n    }\n  }\n\n  /**\n   * Traiter la réponse de l'API\n   */\n  private handleApiResponse(response: any): void {\n    let fournisseur: Fournisseur;\n\n    if (response && response.data) {\n      // Réponse avec wrapper\n      const apiData = response.data;\n      fournisseur = this.mapApiDataToFournisseur(apiData);\n    } else if (response) {\n      // Réponse directe sans wrapper\n      fournisseur = this.mapApiDataToFournisseur(response);\n    } else {\n      console.error('❌ Réponse API vide');\n      this.loadFromLocalStorage();\n      return;\n    }\n\n    console.log('✅ Profil créé depuis l\\'API:', fournisseur);\n    this.fournisseurInfo.set(fournisseur);\n    this.populateForm(fournisseur);\n\n    // Sauvegarder dans localStorage pour les prochaines fois\n    localStorage.setItem('fournisseur_profile', JSON.stringify(fournisseur));\n    this.isLoading.set(false);\n  }\n\n  /**\n   * Mapper les données API vers l'interface Fournisseur\n   */\n  private mapApiDataToFournisseur(apiData: any): Fournisseur {\n    return {\n      id: apiData.id,\n      email: apiData.email,\n      nom: apiData.nom,\n      prenom: apiData.prenom,\n      phoneNumber: apiData.phoneNumber,\n      role: apiData.role,\n      dateNaissance: apiData.dateNaissance,\n      dateInscription: apiData.dateInscription,\n      derniereConnexion: apiData.derniereConnexion,\n      estActif: apiData.estActif,\n      matriculeFiscale: apiData.matriculeFiscale,\n      raisonSociale: apiData.raisonSociale,\n      description: apiData.description || '',\n      ribMasque: apiData.ribMasque || apiData.rib || '',\n      codeBanque: apiData.codeBanque,\n      commission: apiData.commission,\n      delaiPreparationJours: apiData.delaiPreparationJours,\n      fraisLivraisonBase: apiData.fraisLivraisonBase,\n      logoFile: apiData.logoFile || '',\n      adresses: apiData.adresses || []\n    };\n  }\n\n  /**\n   * Charger depuis localStorage en fallback\n   */\n  private loadFromLocalStorage(): void {\n    // Essayer d'abord les données complètes du profil fournisseur\n    const storedProfile = localStorage.getItem('fournisseur_profile');\n    const storedUser = localStorage.getItem('user');\n    const currentUser = this.authService.getCurrentUser();\n\n    if (storedProfile) {\n      try {\n        const profileData = JSON.parse(storedProfile);\n        console.log('🎯 Données complètes du profil depuis localStorage:', profileData);\n\n        // Utiliser directement les données complètes\n        const fournisseur: Fournisseur = {\n          id: profileData.id,\n          email: profileData.email,\n          nom: profileData.nom,\n          prenom: profileData.prenom,\n          phoneNumber: profileData.phoneNumber,\n          role: profileData.role,\n          dateNaissance: profileData.dateNaissance,\n          dateInscription: profileData.dateInscription,\n          derniereConnexion: profileData.derniereConnexion,\n          estActif: profileData.estActif,\n          matriculeFiscale: profileData.matriculeFiscale,\n          raisonSociale: profileData.raisonSociale,\n          description: profileData.description,\n          ribMasque: profileData.ribMasque,\n          codeBanque: profileData.codeBanque,\n          commission: profileData.commission,\n          delaiPreparationJours: profileData.delaiPreparationJours,\n          fraisLivraisonBase: profileData.fraisLivraisonBase,\n          logoFile: profileData.logoFile,\n          adresses: profileData.adresses\n        };\n\n        console.log('✅ Profil créé depuis données complètes:', fournisseur);\n        this.fournisseurInfo.set(fournisseur);\n        this.populateForm(fournisseur);\n        this.isLoading.set(false);\n        return;\n      } catch (error) {\n        console.warn('⚠️ Erreur parsing fournisseur_profile:', error);\n      }\n    }\n\n    // Fallback: essayer les données utilisateur de base\n    if (storedUser) {\n      try {\n        const userData = JSON.parse(storedUser);\n        console.log('📦 Fallback: Données utilisateur depuis localStorage:', userData);\n\n        // Créer un objet Fournisseur à partir des données localStorage\n        const fournisseurFromStorage: Fournisseur = {\n          id: userData.id || currentUser?.id || 0,\n          email: userData.email || currentUser?.email || '',\n          nom: userData.nom || currentUser?.nom || '',\n          prenom: userData.prenom || currentUser?.prenom || '',\n          phoneNumber: userData.phoneNumber || userData.telephone || '',\n          role: userData.role || 'Fournisseur',\n          dateNaissance: userData.dateNaissance || '',\n          dateInscription: userData.dateInscription || new Date().toISOString(),\n          derniereConnexion: userData.derniereConnexion || null,\n          estActif: userData.estActif !== undefined ? userData.estActif : true,\n          matriculeFiscale: userData.matriculeFiscale || '',\n          raisonSociale: userData.raisonSociale || '',\n          description: userData.description || '',\n          ribMasque: userData.ribMasque || userData.rib || '',\n          codeBanque: userData.codeBanque || '',\n          commission: userData.commission || 0,\n          delaiPreparationJours: userData.delaiPreparationJours || 3,\n          fraisLivraisonBase: userData.fraisLivraisonBase || 5.00,\n          logoFile: userData.logoFile || '',\n          adresses: userData.adresses || []\n        };\n\n        console.log('✅ Profil créé depuis localStorage:', fournisseurFromStorage);\n        this.fournisseurInfo.set(fournisseurFromStorage);\n        this.populateForm(fournisseurFromStorage);\n        this.isLoading.set(false);\n        return;\n      } catch (error) {\n        console.warn('⚠️ Erreur parsing localStorage user:', error);\n      }\n    }\n\n    // Fallback: essayer le service dashboard (qui utilise maintenant localStorage)\n    console.log('🔄 Fallback: tentative via DashboardService...');\n    this.dashboardService.getFournisseurInfo().subscribe({\n      next: (fournisseur) => {\n        if (fournisseur) {\n          console.log('✅ Données du profil chargées via DashboardService:', fournisseur);\n          this.fournisseurInfo.set(fournisseur);\n          this.populateForm(fournisseur);\n        } else {\n          // Dernier fallback: créer un profil minimal depuis currentUser\n          this.createMinimalProfile();\n        }\n        this.isLoading.set(false);\n      },\n      error: (error) => {\n        console.error('❌ Erreur lors du chargement du profil via DashboardService:', error);\n        // Dernier fallback: créer un profil minimal\n        this.createMinimalProfile();\n        this.isLoading.set(false);\n      }\n    });\n  }\n\n  /**\n   * Créer un profil minimal depuis les données disponibles\n   */\n  private createMinimalProfile(): void {\n    const currentUser = this.authService.getCurrentUser();\n\n    if (currentUser) {\n      const minimalProfile: Fournisseur = {\n        id: currentUser.id,\n        email: currentUser.email,\n        nom: currentUser.nom,\n        prenom: currentUser.prenom,\n        phoneNumber: (currentUser as any).phoneNumber || (currentUser as any).telephone || '',\n        role: currentUser.role || 'Fournisseur',\n        dateNaissance: '',\n        dateInscription: new Date().toISOString(),\n        derniereConnexion: null,\n        estActif: true,\n        matriculeFiscale: '',\n        raisonSociale: '',\n        description: '',\n        ribMasque: '',\n        codeBanque: '',\n        commission: 0,\n        delaiPreparationJours: 3,\n        fraisLivraisonBase: 5.00,\n        logoFile: '',\n        adresses: []\n      };\n\n      console.log('✅ Profil minimal créé:', minimalProfile);\n      this.fournisseurInfo.set(minimalProfile);\n      this.populateForm(minimalProfile);\n    } else {\n      this.error.set('Aucune donnée utilisateur disponible. Veuillez vous reconnecter.');\n    }\n  }\n\n  /**\n   * Remplir le formulaire avec les données du fournisseur\n   */\n  private populateForm(fournisseur: Fournisseur): void {\n    console.log('📝 Remplissage du formulaire avec:', fournisseur);\n\n    this.profileForm.patchValue({\n      nom: fournisseur.nom || '',\n      prenom: fournisseur.prenom || '',\n      email: fournisseur.email || '',\n      phoneNumber: fournisseur.phoneNumber || '',\n      dateNaissance: fournisseur.dateNaissance ? fournisseur.dateNaissance.split('T')[0] : '',\n      raisonSociale: fournisseur.raisonSociale || '',\n      matriculeFiscale: fournisseur.matriculeFiscale || '',\n      description: fournisseur.description || '',\n      ribMasque: fournisseur.ribMasque || 'Non renseigné',\n      codeBanque: fournisseur.codeBanque || 'Non renseigné',\n      commission: fournisseur.commission || 0,\n      delaiPreparationJours: fournisseur.delaiPreparationJours || 3,\n      fraisLivraisonBase: fournisseur.fraisLivraisonBase || 5.00\n    });\n\n    // Initialiser les adresses\n    this.initAdresses(fournisseur.adresses || []);\n\n    console.log('✅ Formulaire rempli avec les valeurs:', this.profileForm.value);\n  }\n\n  /**\n   * Initialiser les adresses dans le FormArray\n   */\n  private initAdresses(adresses: AdresseDto[]): void {\n    const adressesArray = this.adresses;\n    adressesArray.clear();\n\n    adresses.forEach(adresse => {\n      adressesArray.push(this.createAdresseFormGroup(adresse));\n    });\n  }\n\n  /**\n   * Créer un FormGroup pour une adresse\n   */\n  private createAdresseFormGroup(adresse?: AdresseDto): FormGroup {\n    return this.fb.group({\n      id: [adresse?.id || 0],\n      rue: [adresse?.rue || '', Validators.required],\n      ville: [adresse?.ville || '', Validators.required],\n      codePostal: [adresse?.codePostal || '', [Validators.required, Validators.pattern(/^[0-9]{4}$/)]],\n      pays: [adresse?.pays || 'Tunisie', Validators.required],\n      estPrincipale: [adresse?.estPrincipale || false]\n    });\n  }\n\n  /**\n   * Ajouter une nouvelle adresse\n   */\n  addAdresse(): void {\n    const newIndex = this.adresses.length;\n    this.adresses.push(this.createAdresseFormGroup());\n    this.editingAddresses.add(newIndex);\n  }\n\n  /**\n   * Supprimer une adresse\n   */\n  removeAdresse(index: number): void {\n    this.adresses.removeAt(index);\n    this.editingAddresses.delete(index);\n\n    // Réajuster les indices des adresses en édition\n    const newEditingAddresses = new Set<number>();\n    this.editingAddresses.forEach(editIndex => {\n      if (editIndex > index) {\n        newEditingAddresses.add(editIndex - 1);\n      } else if (editIndex < index) {\n        newEditingAddresses.add(editIndex);\n      }\n    });\n    this.editingAddresses = newEditingAddresses;\n  }\n\n  /**\n   * Basculer le mode édition d'une adresse\n   */\n  toggleEditAddress(index: number): void {\n    if (this.editingAddresses.has(index)) {\n      this.editingAddresses.delete(index);\n    } else {\n      this.editingAddresses.add(index);\n    }\n  }\n\n  /**\n   * Vérifier si une adresse est en mode édition\n   */\n  isEditingAddress(index: number): boolean {\n    return this.editingAddresses.has(index);\n  }\n\n  /**\n   * Définir une adresse comme principale\n   */\n  setAsMain(index: number): void {\n    // Désactiver toutes les autres adresses principales\n    for (let i = 0; i < this.adresses.length; i++) {\n      this.adresses.at(i).get('estPrincipale')?.setValue(i === index);\n    }\n  }\n\n  /**\n   * Basculer l'affichage des autres adresses\n   */\n  toggleOtherAddresses(): void {\n    this.showOtherAddresses = !this.showOtherAddresses;\n  }\n\n  /**\n   * Activer le mode édition\n   */\n  enableEdit(): void {\n    this.isEditing.set(true);\n    this.successMessage.set(null);\n    this.error.set(null);\n  }\n\n  /**\n   * Annuler l'édition\n   */\n  cancelEdit(): void {\n    this.isEditing.set(false);\n    const fournisseur = this.fournisseurInfo();\n    if (fournisseur) {\n      this.populateForm(fournisseur);\n    }\n    this.error.set(null);\n  }\n\n  /**\n   * Sauvegarder les modifications\n   */\n  saveProfile(): void {\n    if (this.profileForm.valid) {\n      this.isSaving.set(true);\n      this.error.set(null);\n\n      const formData = this.profileForm.value;\n      console.log('💾 Sauvegarde du profil:', formData);\n\n      // TODO: Implémenter l'appel API pour mettre à jour le profil\n      // Pour l'instant, simulation d'une sauvegarde\n      setTimeout(() => {\n        this.isSaving.set(false);\n        this.isEditing.set(false);\n        this.successMessage.set('Profil mis à jour avec succès !');\n        \n        // Masquer le message de succès après 3 secondes\n        setTimeout(() => {\n          this.successMessage.set(null);\n        }, 3000);\n      }, 1000);\n    } else {\n      this.error.set('Veuillez corriger les erreurs dans le formulaire');\n      this.markFormGroupTouched();\n    }\n  }\n\n  /**\n   * Marquer tous les champs comme touchés pour afficher les erreurs\n   */\n  private markFormGroupTouched(): void {\n    Object.keys(this.profileForm.controls).forEach(key => {\n      const control = this.profileForm.get(key);\n      control?.markAsTouched();\n    });\n  }\n\n  /**\n   * Vérifier si un champ a une erreur\n   */\n  hasFieldError(fieldName: string): boolean {\n    const field = this.profileForm.get(fieldName);\n    return !!(field && field.invalid && field.touched);\n  }\n\n  /**\n   * Obtenir le message d'erreur pour un champ\n   */\n  getFieldError(fieldName: string): string {\n    const field = this.profileForm.get(fieldName);\n    if (field && field.errors && field.touched) {\n      if (field.errors['required']) return 'Ce champ est obligatoire';\n      if (field.errors['email']) return 'Email invalide';\n      if (field.errors['minlength']) return `Minimum ${field.errors['minlength'].requiredLength} caractères`;\n      if (field.errors['pattern']) return 'Format invalide';\n      if (field.errors['min']) return `Valeur minimum: ${field.errors['min'].min}`;\n    }\n    return '';\n  }\n\n  /**\n   * Formater la date d'inscription\n   */\n  formatDate(dateString: string): string {\n    if (!dateString) return 'Non renseigné';\n    return new Date(dateString).toLocaleDateString('fr-FR');\n  }\n\n  /**\n   * Formater le statut\n   */\n  getStatusText(estActif: boolean): string {\n    return estActif ? 'Actif' : 'Inactif';\n  }\n\n  /**\n   * Obtenir la classe CSS pour le statut\n   */\n  getStatusClass(estActif: boolean): string {\n    return estActif ? 'status-active' : 'status-inactive';\n  }\n\n  /**\n   * Formater la commission\n   */\n  formatCommission(commission: number): string {\n    return `${commission}%`;\n  }\n\n  /**\n   * Formater les frais de livraison\n   */\n  formatCurrency(amount: number): string {\n    return new Intl.NumberFormat('fr-FR', {\n      style: 'currency',\n      currency: 'EUR'\n    }).format(amount);\n  }\n\n  /**\n   * Rafraîchir les données du profil depuis l'API\n   */\n  refreshProfile(): void {\n    console.log('🔄 Rafraîchissement du profil depuis l\\'API...');\n    // Forcer le rechargement depuis l'API\n    this.isLoading.set(true);\n    this.error.set(null);\n\n    const currentUser = this.authService.getCurrentUser();\n    if (currentUser && currentUser.id) {\n      this.fournisseurService.getById(currentUser.id).subscribe({\n        next: (response) => {\n          console.log('✅ Profil rafraîchi depuis l\\'API:', response);\n          this.handleApiResponse(response);\n        },\n        error: (error) => {\n          console.error('❌ Erreur lors du rafraîchissement:', error);\n          this.error.set('Erreur lors du rafraîchissement du profil');\n          this.isLoading.set(false);\n        }\n      });\n    } else {\n      this.error.set('Utilisateur non connecté');\n      this.isLoading.set(false);\n    }\n  }\n\n  /**\n   * Debug: afficher toutes les données disponibles\n   */\n  debugProfile(): void {\n    console.log('🐛 DEBUG PROFIL COMPLET:');\n    console.log('📦 localStorage:');\n    console.log('  - token:', !!localStorage.getItem('token'));\n    console.log('  - user:', localStorage.getItem('user'));\n    console.log('  - fournisseur_profile:', localStorage.getItem('fournisseur_profile'));\n    console.log('  - supplierId:', localStorage.getItem('supplierId'));\n    console.log('👤 AuthService:');\n    console.log('  - currentUser:', this.authService.getCurrentUser());\n    console.log('  - isAuthenticated:', this.authService.isAuthenticated());\n    console.log('🏪 Composant:');\n    console.log('  - fournisseurInfo:', this.fournisseurInfo());\n    console.log('  - form values:', this.profileForm.value);\n    console.log('  - isLoading:', this.isLoading());\n    console.log('  - error:', this.error());\n    console.log('📊 Statistiques localStorage:');\n    console.log('  - Nombre de clés:', Object.keys(localStorage).length);\n    console.log('  - Toutes les clés:', Object.keys(localStorage));\n  }\n\n  /**\n   * Forcer la création du profil depuis localStorage uniquement\n   */\n  loadFromLocalStorageOnly(): void {\n    console.log('🔄 Chargement forcé depuis localStorage uniquement...');\n    this.isLoading.set(true);\n    this.error.set(null);\n\n    // Essayer d'abord les données complètes\n    const storedProfile = localStorage.getItem('fournisseur_profile');\n    if (storedProfile) {\n      try {\n        const profileData = JSON.parse(storedProfile);\n        console.log('🎯 Données complètes trouvées:', profileData);\n\n        const fournisseur: Fournisseur = {\n          id: profileData.id || 0,\n          email: profileData.email || '',\n          nom: profileData.nom || '',\n          prenom: profileData.prenom || '',\n          phoneNumber: profileData.phoneNumber || '',\n          role: profileData.role || 'Fournisseur',\n          dateNaissance: profileData.dateNaissance || '',\n          dateInscription: profileData.dateInscription || new Date().toISOString(),\n          derniereConnexion: profileData.derniereConnexion || null,\n          estActif: profileData.estActif !== undefined ? profileData.estActif : true,\n          matriculeFiscale: profileData.matriculeFiscale || '',\n          raisonSociale: profileData.raisonSociale || '',\n          description: profileData.description || '',\n          ribMasque: profileData.ribMasque || 'Non renseigné',\n          codeBanque: profileData.codeBanque || 'Non renseigné',\n          commission: profileData.commission || 0,\n          delaiPreparationJours: profileData.delaiPreparationJours || 3,\n          fraisLivraisonBase: profileData.fraisLivraisonBase || 5.00,\n          logoFile: profileData.logoFile || '',\n          adresses: profileData.adresses || []\n        };\n\n        console.log('✅ Profil créé depuis données complètes:', fournisseur);\n        this.fournisseurInfo.set(fournisseur);\n        this.populateForm(fournisseur);\n        this.isLoading.set(false);\n        return;\n      } catch (error) {\n        console.error('❌ Erreur parsing fournisseur_profile:', error);\n      }\n    }\n\n    // Fallback: données utilisateur de base\n    const storedUser = localStorage.getItem('user');\n    if (storedUser) {\n      try {\n        const userData = JSON.parse(storedUser);\n        console.log('📦 Données localStorage:', userData);\n\n        const fournisseur: Fournisseur = {\n          id: userData.id || 0,\n          email: userData.email || '',\n          nom: userData.nom || '',\n          prenom: userData.prenom || '',\n          phoneNumber: userData.phoneNumber || userData.telephone || '',\n          role: userData.role || 'Fournisseur',\n          dateNaissance: userData.dateNaissance || '',\n          dateInscription: userData.dateInscription || new Date().toISOString(),\n          derniereConnexion: userData.derniereConnexion || null,\n          estActif: userData.estActif !== undefined ? userData.estActif : true,\n          matriculeFiscale: userData.matriculeFiscale || '',\n          raisonSociale: userData.raisonSociale || '',\n          description: userData.description || '',\n          ribMasque: userData.ribMasque || userData.rib || 'Non renseigné',\n          codeBanque: userData.codeBanque || 'Non renseigné',\n          commission: userData.commission || 0,\n          delaiPreparationJours: userData.delaiPreparationJours || 3,\n          fraisLivraisonBase: userData.fraisLivraisonBase || 5.00,\n          logoFile: userData.logoFile || '',\n          adresses: userData.adresses || []\n        };\n\n        console.log('✅ Profil créé depuis localStorage:', fournisseur);\n        this.fournisseurInfo.set(fournisseur);\n        this.populateForm(fournisseur);\n        this.isLoading.set(false);\n      } catch (error) {\n        console.error('❌ Erreur parsing localStorage:', error);\n        this.error.set('Erreur lors de la lecture des données localStorage');\n        this.isLoading.set(false);\n      }\n    } else {\n      this.error.set('Aucune donnée utilisateur trouvée dans localStorage');\n      this.isLoading.set(false);\n    }\n  }\n\n  /**\n   * Gérer les erreurs de chargement du logo\n   */\n  onLogoError(event: any): void {\n    // Remplacer par le placeholder du service en cas d'erreur\n    event.target.src = this.imageUrlService.getPlaceholderUrl();\n  }\n}\n", "<div class=\"profile-container\">\n  <!-- Header -->\n  <div class=\"profile-header\">\n    <div class=\"header-content\">\n      <div class=\"header-left\">\n        <!-- Logo du fournisseur -->\n        <div class=\"profile-logo\" *ngIf=\"fournisseurInfo()\">\n          <img\n            [src]=\"imageUrlService.getFournisseurLogoUrl(fournisseurInfo()?.logoFile)\"\n            [alt]=\"'Logo de ' + fournisseurInfo()?.raisonSociale\"\n            class=\"logo-image\"\n            (error)=\"onLogoError($event)\"\n            loading=\"lazy\">\n        </div>\n        <div class=\"header-text\">\n          <h1 class=\"profile-title\">\n            <span class=\"title-icon\">👤</span>\n            Mon Profil\n          </h1>\n          <p class=\"profile-subtitle\">Gérez vos informations personnelles et professionnelles</p>\n        </div>\n      </div>\n      <div class=\"header-actions\">\n        <div *ngIf=\"!isLoading() && fournisseurInfo()\">\n          <button\n            *ngIf=\"!isEditing()\"\n            class=\"btn btn-primary\"\n            (click)=\"enableEdit()\"\n            type=\"button\">\n            <span>✏️</span> Modifier\n          </button>\n          <div *ngIf=\"isEditing()\" class=\"edit-actions\">\n            <button\n              class=\"btn btn-secondary\"\n              (click)=\"cancelEdit()\"\n              type=\"button\"\n              [disabled]=\"isSaving()\">\n              Annuler\n            </button>\n            <button\n              class=\"btn btn-primary\"\n              (click)=\"saveProfile()\"\n              type=\"button\"\n              [disabled]=\"isSaving() || profileForm.invalid\">\n              <span *ngIf=\"isSaving()\" class=\"spinner\"></span>\n              {{ isSaving() ? 'Sauvegarde...' : 'Sauvegarder' }}\n            </button>\n          </div>\n        </div>\n      </div>\n    </div>\n  </div>\n\n  <!-- Messages -->\n  <div *ngIf=\"error()\" class=\"alert alert-error\">\n    <span class=\"alert-icon\">❌</span>\n    {{ error() }}\n  </div>\n\n  <div *ngIf=\"successMessage()\" class=\"alert alert-success\">\n    <span class=\"alert-icon\">✅</span>\n    {{ successMessage() }}\n  </div>\n\n  <!-- Loading State -->\n  <div *ngIf=\"isLoading()\" class=\"loading-container\">\n    <div class=\"loading-content\">\n      <div class=\"spinner large\"></div>\n      <p>Chargement de votre profil...</p>\n    </div>\n  </div>\n\n  <!-- Profile Content -->\n  <div *ngIf=\"!isLoading() && fournisseurInfo()\" class=\"profile-content\">\n    <form [formGroup]=\"profileForm\" class=\"profile-form\">\n      \n      <!-- Informations personnelles -->\n      <div class=\"form-section\">\n        <h3 class=\"section-title\">\n          <span class=\"section-icon\">👨‍💼</span>\n          Informations personnelles\n        </h3>\n        \n        <div class=\"form-row\">\n          <div class=\"form-group\">\n            <label for=\"prenom\">Prénom *</label>\n            <input\n              type=\"text\"\n              id=\"prenom\"\n              formControlName=\"prenom\"\n              class=\"form-control\"\n              [class.is-invalid]=\"hasFieldError('prenom')\"\n              [readonly]=\"!isEditing()\"\n            />\n            <div class=\"invalid-feedback\" *ngIf=\"hasFieldError('prenom')\">\n              {{ getFieldError('prenom') }}\n            </div>\n          </div>\n\n          <div class=\"form-group\">\n            <label for=\"nom\">Nom *</label>\n            <input\n              type=\"text\"\n              id=\"nom\"\n              formControlName=\"nom\"\n              class=\"form-control\"\n              [class.is-invalid]=\"hasFieldError('nom')\"\n              [readonly]=\"!isEditing()\"\n            />\n            <div class=\"invalid-feedback\" *ngIf=\"hasFieldError('nom')\">\n              {{ getFieldError('nom') }}\n            </div>\n          </div>\n        </div>\n\n        <div class=\"form-row\">\n          <div class=\"form-group\">\n            <label for=\"email\">Email *</label>\n            <input\n              type=\"email\"\n              id=\"email\"\n              formControlName=\"email\"\n              class=\"form-control\"\n              [class.is-invalid]=\"hasFieldError('email')\"\n              [readonly]=\"!isEditing()\"\n            />\n            <div class=\"invalid-feedback\" *ngIf=\"hasFieldError('email')\">\n              {{ getFieldError('email') }}\n            </div>\n          </div>\n\n          <div class=\"form-group\">\n            <label for=\"phoneNumber\">Téléphone *</label>\n            <input\n              type=\"tel\"\n              id=\"phoneNumber\"\n              formControlName=\"phoneNumber\"\n              class=\"form-control\"\n              [class.is-invalid]=\"hasFieldError('phoneNumber')\"\n              [readonly]=\"!isEditing()\"\n            />\n            <div class=\"invalid-feedback\" *ngIf=\"hasFieldError('phoneNumber')\">\n              {{ getFieldError('phoneNumber') }}\n            </div>\n          </div>\n        </div>\n\n        <div class=\"form-group\">\n          <label for=\"dateNaissance\">Date de naissance</label>\n          <input\n            type=\"date\"\n            id=\"dateNaissance\"\n            formControlName=\"dateNaissance\"\n            class=\"form-control\"\n            [readonly]=\"!isEditing()\"\n          />\n        </div>\n      </div>\n\n      <!-- Informations entreprise -->\n      <div class=\"form-section\">\n        <h3 class=\"section-title\">\n          <span class=\"section-icon\">🏢</span>\n          Informations entreprise\n        </h3>\n        \n        <div class=\"form-row\">\n          <div class=\"form-group\">\n            <label for=\"raisonSociale\">Raison sociale *</label>\n            <input\n              type=\"text\"\n              id=\"raisonSociale\"\n              formControlName=\"raisonSociale\"\n              class=\"form-control\"\n              [class.is-invalid]=\"hasFieldError('raisonSociale')\"\n              [readonly]=\"!isEditing()\"\n            />\n            <div class=\"invalid-feedback\" *ngIf=\"hasFieldError('raisonSociale')\">\n              {{ getFieldError('raisonSociale') }}\n            </div>\n          </div>\n\n          <div class=\"form-group\">\n            <label for=\"matriculeFiscale\">Matricule fiscal *</label>\n            <input\n              type=\"text\"\n              id=\"matriculeFiscale\"\n              formControlName=\"matriculeFiscale\"\n              class=\"form-control\"\n              [class.is-invalid]=\"hasFieldError('matriculeFiscale')\"\n              [readonly]=\"!isEditing()\"\n            />\n            <div class=\"invalid-feedback\" *ngIf=\"hasFieldError('matriculeFiscale')\">\n              {{ getFieldError('matriculeFiscale') }}\n            </div>\n          </div>\n        </div>\n\n        <div class=\"form-group\">\n          <label for=\"description\">Description</label>\n          <textarea\n            id=\"description\"\n            formControlName=\"description\"\n            class=\"form-control\"\n            rows=\"4\"\n            [readonly]=\"!isEditing()\"\n            placeholder=\"Décrivez votre entreprise et vos activités...\"\n          ></textarea>\n        </div>\n      </div>\n\n      <!-- Informations bancaires -->\n      <div class=\"form-section\">\n        <h3 class=\"section-title\">\n          <span class=\"section-icon\">🏦</span>\n          Informations bancaires\n        </h3>\n        \n        <div class=\"form-row\">\n          <div class=\"form-group\">\n            <label for=\"ribMasque\">RIB</label>\n            <input\n              type=\"text\"\n              id=\"ribMasque\"\n              formControlName=\"ribMasque\"\n              class=\"form-control\"\n              readonly\n            />\n            <small class=\"form-hint\">Contactez l'administration pour modifier vos informations bancaires</small>\n          </div>\n\n          <div class=\"form-group\">\n            <label for=\"codeBanque\">Code banque</label>\n            <input\n              type=\"text\"\n              id=\"codeBanque\"\n              formControlName=\"codeBanque\"\n              class=\"form-control\"\n              readonly\n            />\n          </div>\n        </div>\n      </div>\n\n      <!-- Paramètres commerciaux -->\n      <div class=\"form-section\">\n        <h3 class=\"section-title\">\n          <span class=\"section-icon\">💼</span>\n          Paramètres commerciaux\n        </h3>\n        \n        <div class=\"form-row\">\n          <div class=\"form-group\">\n            <label for=\"commission\">Commission</label>\n            <div class=\"input-group\">\n              <input\n                type=\"number\"\n                id=\"commission\"\n                formControlName=\"commission\"\n                class=\"form-control\"\n                readonly\n              />\n              <span class=\"input-suffix\">%</span>\n            </div>\n            <small class=\"form-hint\">Taux de commission défini par l'administration</small>\n          </div>\n\n          <div class=\"form-group\">\n            <label for=\"delaiPreparationJours\">Délai de préparation *</label>\n            <div class=\"input-group\">\n              <input\n                type=\"number\"\n                id=\"delaiPreparationJours\"\n                formControlName=\"delaiPreparationJours\"\n                class=\"form-control\"\n                [class.is-invalid]=\"hasFieldError('delaiPreparationJours')\"\n                [readonly]=\"!isEditing()\"\n                min=\"1\"\n              />\n              <span class=\"input-suffix\">jours</span>\n            </div>\n            <div class=\"invalid-feedback\" *ngIf=\"hasFieldError('delaiPreparationJours')\">\n              {{ getFieldError('delaiPreparationJours') }}\n            </div>\n          </div>\n        </div>\n\n        <div class=\"form-group\">\n          <label for=\"fraisLivraisonBase\">Frais de livraison de base *</label>\n          <div class=\"input-group\">\n            <input\n              type=\"number\"\n              id=\"fraisLivraisonBase\"\n              formControlName=\"fraisLivraisonBase\"\n              class=\"form-control\"\n              [class.is-invalid]=\"hasFieldError('fraisLivraisonBase')\"\n              [readonly]=\"!isEditing()\"\n              min=\"0\"\n              step=\"0.01\"\n            />\n            <span class=\"input-suffix\">DT</span>\n          </div>\n          <div class=\"invalid-feedback\" *ngIf=\"hasFieldError('fraisLivraisonBase')\">\n            {{ getFieldError('fraisLivraisonBase') }}\n          </div>\n        </div>\n      </div>\n\n      <!-- Section Adresses -->\n      <div class=\"form-section\">\n        <h3 class=\"section-title\">\n          <span class=\"section-icon\">📍</span>\n          Adresses\n          <button\n            type=\"button\"\n            class=\"btn btn-sm btn-outline-primary ms-auto\"\n            (click)=\"addAdresse()\"\n            *ngIf=\"isEditing()\"\n          >\n            <span>➕</span> Ajouter une adresse\n          </button>\n        </h3>\n\n        <div formArrayName=\"adresses\">\n          <div *ngFor=\"let adresseCtrl of adresses.controls; let i = index\"\n               [formGroupName]=\"i\"\n               class=\"address-item\">\n\n            <!-- Mode affichage -->\n            <div *ngIf=\"!isEditingAddress(i)\" class=\"address-display\">\n              <div class=\"address-content\">\n                <span *ngIf=\"adresseCtrl.get('rue')?.value\" class=\"address-text\">\n                  {{ adresseCtrl.get('rue')?.value }},\n                  {{ adresseCtrl.get('ville')?.value }},\n                  {{ adresseCtrl.get('codePostal')?.value }},\n                  {{ adresseCtrl.get('pays')?.value }}\n                </span>\n                <span *ngIf=\"!adresseCtrl.get('rue')?.value\" class=\"no-address\">\n                  Adresse vide\n                </span>\n                <span *ngIf=\"adresseCtrl.get('estPrincipale')?.value\" class=\"badge badge-primary ms-2\">\n                  Principale\n                </span>\n              </div>\n              <div class=\"address-actions\" *ngIf=\"isEditing()\">\n                <button\n                  type=\"button\"\n                  class=\"btn btn-sm btn-outline-secondary\"\n                  (click)=\"toggleEditAddress(i)\"\n                >\n                  ✏️ Modifier\n                </button>\n                <button\n                  type=\"button\"\n                  class=\"btn btn-sm btn-outline-danger\"\n                  (click)=\"removeAdresse(i)\"\n                >\n                  🗑️ Supprimer\n                </button>\n              </div>\n            </div>\n\n            <!-- Mode édition -->\n            <div *ngIf=\"isEditingAddress(i)\" class=\"address-edit\">\n              <div class=\"row\">\n                <div class=\"col-md-12 mb-3\">\n                  <label class=\"form-label\">Rue *</label>\n                  <input\n                    type=\"text\"\n                    formControlName=\"rue\"\n                    class=\"form-control\"\n                    placeholder=\"Numéro et nom de rue\"\n                  />\n                </div>\n              </div>\n\n              <div class=\"row\">\n                <div class=\"col-md-6 mb-3\">\n                  <label class=\"form-label\">Ville *</label>\n                  <input\n                    type=\"text\"\n                    formControlName=\"ville\"\n                    class=\"form-control\"\n                    placeholder=\"Ville\"\n                  />\n                </div>\n                <div class=\"col-md-6 mb-3\">\n                  <label class=\"form-label\">Code Postal *</label>\n                  <input\n                    type=\"text\"\n                    formControlName=\"codePostal\"\n                    class=\"form-control\"\n                    placeholder=\"1234\"\n                    maxlength=\"4\"\n                  />\n                </div>\n              </div>\n\n              <div class=\"row\">\n                <div class=\"col-md-6 mb-3\">\n                  <label class=\"form-label\">Pays *</label>\n                  <input\n                    type=\"text\"\n                    formControlName=\"pays\"\n                    class=\"form-control\"\n                    value=\"Tunisie\"\n                    readonly\n                  />\n                </div>\n                <div class=\"col-md-6 mb-3\">\n                  <div class=\"form-check\">\n                    <input\n                      type=\"checkbox\"\n                      formControlName=\"estPrincipale\"\n                      (change)=\"setAsMain(i)\"\n                      class=\"form-check-input\"\n                      id=\"principal-{{i}}\"\n                    />\n                    <label for=\"principal-{{i}}\" class=\"form-check-label\">\n                      Adresse principale\n                    </label>\n                  </div>\n                </div>\n              </div>\n\n              <div class=\"address-actions\">\n                <button\n                  type=\"button\"\n                  class=\"btn btn-sm btn-success\"\n                  (click)=\"toggleEditAddress(i)\"\n                >\n                  ✅ Valider\n                </button>\n              </div>\n            </div>\n          </div>\n\n          <div *ngIf=\"adresses.length === 0\" class=\"no-addresses\">\n            <p class=\"text-muted\">Aucune adresse enregistrée</p>\n            <button\n              type=\"button\"\n              class=\"btn btn-outline-primary\"\n              (click)=\"addAdresse()\"\n              *ngIf=\"isEditing()\"\n            >\n              <span>➕</span> Ajouter votre première adresse\n            </button>\n          </div>\n        </div>\n      </div>\n\n      <!-- Informations système -->\n      <div class=\"form-section\" *ngIf=\"fournisseurInfo()\">\n        <h3 class=\"section-title\">\n          <span class=\"section-icon\">⚙️</span>\n          Informations système\n        </h3>\n        \n        <div class=\"info-grid\">\n          <div class=\"info-item\">\n            <label>Statut du compte</label>\n            <span class=\"badge\" [ngClass]=\"getStatusClass(fournisseurInfo()!.estActif)\">\n              {{ getStatusText(fournisseurInfo()!.estActif) }}\n            </span>\n          </div>\n\n          <div class=\"info-item\">\n            <label>Date d'inscription</label>\n            <span>{{ formatDate(fournisseurInfo()!.dateInscription) }}</span>\n          </div>\n\n          <div class=\"info-item\">\n            <label>Dernière connexion</label>\n            <span>{{ fournisseurInfo()!.derniereConnexion ? formatDate(fournisseurInfo()!.derniereConnexion!) : 'Jamais' }}</span>\n          </div>          \n        </div>\n      </div>\n    </form>\n  </div>\n</div>\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAqCM,IAAO,qBAAP,MAAO,oBAAkB;EAGT;EAFH,UAAU,GAAG,YAAY,UAAU,2BAA2B;EAE/E,YAAoB,MAAgB;AAAhB,SAAA,OAAA;EAAmB;;;;EAKvC,OAAO,QAON;AACC,QAAI,aAAa,IAAI,WAAU;AAE/B,QAAI,QAAQ;AACV,UAAI,OAAO;AAAM,qBAAa,WAAW,IAAI,QAAQ,OAAO,KAAK,SAAQ,CAAE;AAC3E,UAAI,OAAO;AAAU,qBAAa,WAAW,IAAI,YAAY,OAAO,SAAS,SAAQ,CAAE;AACvF,UAAI,OAAO;AAAQ,qBAAa,WAAW,IAAI,UAAU,OAAO,MAAM;AACtE,UAAI,OAAO,aAAa;AAAW,qBAAa,WAAW,IAAI,YAAY,OAAO,SAAS,SAAQ,CAAE;AACrG,UAAI,OAAO;AAAQ,qBAAa,WAAW,IAAI,UAAU,OAAO,MAAM;AACtE,UAAI,OAAO;AAAW,qBAAa,WAAW,IAAI,aAAa,OAAO,SAAS;IACjF;AAEA,YAAQ,IAAI,qEAAqD,MAAM;AAEvE,WAAO,KAAK,KAAK,IAA6B,KAAK,SAAS,EAAE,QAAQ,WAAU,CAAE,EAC/E,KACC,IAAI,cAAY,QAAQ,IAAI,2CAA6B,QAAQ,CAAC,CAAC;EAEzE;;;;EAKA,QAAQ,IAAU;AAChB,YAAQ,IAAI,mDAAsC,EAAE;AAEpD,WAAO,KAAK,KAAK,IAAyB,GAAG,KAAK,OAAO,IAAI,EAAE,EAAE,EAC9D,KACC,IAAI,cAAY,QAAQ,IAAI,yCAA2B,QAAQ,CAAC,CAAC;EAEvE;;;;EAKA,OAAO,aAA8B;AACnC,YAAQ,IAAI,gDAAyC,WAAW;AAEhE,WAAO,KAAK,KAAK,KAA0B,KAAK,SAAS,WAAW,EACjE,KACC,IAAI,cAAY,QAAQ,IAAI,kCAAuB,QAAQ,CAAC,CAAC;EAEnE;;;;EAKA,OAAO,IAAY,aAA8B;AAC/C,YAAQ,IAAI,kDAAqC,IAAI,WAAW;AAEhE,WAAO,KAAK,KAAK,IAAyB,GAAG,KAAK,OAAO,IAAI,EAAE,IAAI,WAAW,EAC3E,KACC,IAAI,cAAY,QAAQ,IAAI,qCAA6B,QAAQ,CAAC,CAAC;EAEzE;;;;EAKA,OAAO,IAAU;AACf,YAAQ,IAAI,kDAAsC,EAAE;AAEpD,WAAO,KAAK,KAAK,OAA+C,GAAG,KAAK,OAAO,IAAI,EAAE,EAAE,EACpF,KACC,IAAI,cAAY,QAAQ,IAAI,mCAA2B,QAAQ,CAAC,CAAC;EAEvE;;;;EAKA,aAAa,IAAY,SAA4B;AACnD,YAAQ,IAAI,qDAA8C,IAAI,OAAO;AAErE,WAAO,KAAK,KAAK,MAA2B,GAAG,KAAK,OAAO,IAAI,EAAE,kBAAkB,OAAO,EACvF,KACC,IAAI,cAAY,QAAQ,IAAI,4CAAoC,QAAQ,CAAC,CAAC;EAEhF;;;;EAKA,YAAY,IAAY,QAMvB;AACC,QAAI,aAAa,IAAI,WAAU;AAE/B,QAAI,QAAQ;AACV,UAAI,OAAO;AAAM,qBAAa,WAAW,IAAI,QAAQ,OAAO,KAAK,SAAQ,CAAE;AAC3E,UAAI,OAAO;AAAU,qBAAa,WAAW,IAAI,YAAY,OAAO,SAAS,SAAQ,CAAE;AACvF,UAAI,OAAO;AAAQ,qBAAa,WAAW,IAAI,UAAU,OAAO,MAAM;AACtE,UAAI,OAAO;AAAU,qBAAa,WAAW,IAAI,YAAY,OAAO,QAAQ;AAC5E,UAAI,OAAO,aAAa;AAAW,qBAAa,WAAW,IAAI,YAAY,OAAO,SAAS,SAAQ,CAAE;IACvG;AAEA,YAAQ,IAAI,gEAAmD,IAAI,MAAM;AAEzE,WAAO,KAAK,KAAK,IAA8E,GAAG,KAAK,OAAO,IAAI,EAAE,aAAa,EAAE,QAAQ,WAAU,CAAE,EACpJ,KACC,IAAI,cAAY,QAAQ,IAAI,sDAAwC,QAAQ,CAAC,CAAC;EAEpF;;;;EAKA,WAAW,KAAa,YAAkB;AACxC,UAAM,SAAS,IAAI,WAAU,EAC1B,IAAI,OAAO,GAAG,EACd,IAAI,cAAc,UAAU;AAE/B,YAAQ,IAAI,gCAAyB,EAAE,KAAK,WAAU,CAAE;AAExD,WAAO,KAAK,KAAK,IAA2D,GAAG,KAAK,OAAO,gBAAgB,EAAE,OAAM,CAAE,EAClH,KACC,IAAI,cAAY,QAAQ,IAAI,0BAAqB,QAAQ,CAAC,CAAC;EAEjE;;;;EAKA,OAAO,IAAU;AACf,YAAQ,IAAI,+DAAsD,EAAE;AAEpE,WAAO,KAAK,KAAK,IAA2C,GAAG,KAAK,OAAO,WAAW,EAAE,EAAE,EACvF,KACC,IAAI,cAAY,QAAQ,IAAI,qCAA6B,QAAQ,CAAC,CAAC;EAEzE;;;;EAKA,iBAAiB,IAAY,SAAgC;AAC3D,YAAQ,IAAI,gEAAsD,IAAI,OAAO;AAE7E,WAAO,KAAK,KAAK,MAA2B,GAAG,KAAK,OAAO,IAAI,EAAE,eAAe,OAAO,EACpF,KACC,IAAI,cAAY,QAAQ,IAAI,qCAA6B,QAAQ,CAAC,CAAC;EAEzE;;;;EAKA,wBAAqB;AACnB,YAAQ,IAAI,yDAAyC;AAErD,WAAO,KAAK,KAAK,IAAyB,GAAG,KAAK,OAAO,UAAU,EAChE,KACC,IAAI,cAAY,QAAQ,IAAI,qDAAoC,QAAQ,CAAC,CAAC;EAEhF;;;;EAKA,mBAAmB,KAAa,YAAkB;AAChD,WAAO,IAAI,WAAW,cAAW;AAC/B,WAAK,WAAW,KAAK,UAAU,EAAE,UAAU;QACzC,MAAM,CAAC,aAAY;AACjB,mBAAS,KAAK;YACZ;YACA;YACA,SAAS,SAAS;WACnB;AACD,mBAAS,SAAQ;QACnB;QACA,OAAO,CAAC,UAAU,SAAS,MAAM,KAAK;OACvC;IACH,CAAC;EACH;;qCAhMW,qBAAkB,mBAAA,UAAA,CAAA;EAAA;4EAAlB,qBAAkB,SAAlB,oBAAkB,WAAA,YAFjB,OAAM,CAAA;;;sEAEP,oBAAkB,CAAA;UAH9B;WAAW;MACV,YAAY;KACb;;;;;;;;AE9BO,IAAA,yBAAA,GAAA,OAAA,EAAA,EAAoD,GAAA,OAAA,EAAA;AAKhD,IAAA,qBAAA,SAAA,SAAA,qDAAA,QAAA;AAAA,MAAA,wBAAA,GAAA;AAAA,YAAA,SAAA,wBAAA;AAAA,aAAA,sBAAS,OAAA,YAAA,MAAA,CAAmB;IAAA,CAAA;AAJ9B,IAAA,uBAAA,EAKiB;;;;;;AAJf,IAAA,oBAAA;AAAA,IAAA,qBAAA,OAAA,OAAA,gBAAA,uBAAA,UAAA,OAAA,gBAAA,MAAA,OAAA,OAAA,QAAA,QAAA,GAAA,uBAAA,EAA0E,OAAA,eAAA,UAAA,OAAA,gBAAA,MAAA,OAAA,OAAA,QAAA,cAAA;;;;;;AAgB5E,IAAA,yBAAA,GAAA,UAAA,EAAA;AAGE,IAAA,qBAAA,SAAA,SAAA,oEAAA;AAAA,MAAA,wBAAA,GAAA;AAAA,YAAA,SAAA,wBAAA,CAAA;AAAA,aAAA,sBAAS,OAAA,WAAA,CAAY;IAAA,CAAA;AAErB,IAAA,yBAAA,GAAA,MAAA;AAAM,IAAA,iBAAA,GAAA,cAAA;AAAE,IAAA,uBAAA;AAAQ,IAAA,iBAAA,GAAA,YAAA;AAClB,IAAA,uBAAA;;;;;AAcI,IAAA,oBAAA,GAAA,QAAA,EAAA;;;;;;AAbJ,IAAA,yBAAA,GAAA,OAAA,EAAA,EAA8C,GAAA,UAAA,EAAA;AAG1C,IAAA,qBAAA,SAAA,SAAA,iEAAA;AAAA,MAAA,wBAAA,GAAA;AAAA,YAAA,SAAA,wBAAA,CAAA;AAAA,aAAA,sBAAS,OAAA,WAAA,CAAY;IAAA,CAAA;AAGrB,IAAA,iBAAA,GAAA,WAAA;AACF,IAAA,uBAAA;AACA,IAAA,yBAAA,GAAA,UAAA,EAAA;AAEE,IAAA,qBAAA,SAAA,SAAA,iEAAA;AAAA,MAAA,wBAAA,GAAA;AAAA,YAAA,SAAA,wBAAA,CAAA;AAAA,aAAA,sBAAS,OAAA,YAAA,CAAa;IAAA,CAAA;AAGtB,IAAA,qBAAA,GAAA,+CAAA,GAAA,GAAA,QAAA,EAAA;AACA,IAAA,iBAAA,CAAA;AACF,IAAA,uBAAA,EAAS;;;;AAVP,IAAA,oBAAA;AAAA,IAAA,qBAAA,YAAA,OAAA,SAAA,CAAA;AAOA,IAAA,oBAAA,CAAA;AAAA,IAAA,qBAAA,YAAA,OAAA,SAAA,KAAA,OAAA,YAAA,OAAA;AACO,IAAA,oBAAA;AAAA,IAAA,qBAAA,QAAA,OAAA,SAAA,CAAA;AACP,IAAA,oBAAA;AAAA,IAAA,6BAAA,KAAA,OAAA,SAAA,IAAA,kBAAA,eAAA,GAAA;;;;;AAtBN,IAAA,yBAAA,GAAA,KAAA;AACE,IAAA,qBAAA,GAAA,2CAAA,GAAA,GAAA,UAAA,EAAA,EAIgB,GAAA,wCAAA,GAAA,GAAA,OAAA,EAAA;AAoBlB,IAAA,uBAAA;;;;AAvBK,IAAA,oBAAA;AAAA,IAAA,qBAAA,QAAA,CAAA,OAAA,UAAA,CAAA;AAMG,IAAA,oBAAA;AAAA,IAAA,qBAAA,QAAA,OAAA,UAAA,CAAA;;;;;AAuBd,IAAA,yBAAA,GAAA,OAAA,EAAA,EAA+C,GAAA,QAAA,EAAA;AACpB,IAAA,iBAAA,GAAA,QAAA;AAAC,IAAA,uBAAA;AAC1B,IAAA,iBAAA,CAAA;AACF,IAAA,uBAAA;;;;AADE,IAAA,oBAAA,CAAA;AAAA,IAAA,6BAAA,KAAA,OAAA,MAAA,GAAA,GAAA;;;;;AAGF,IAAA,yBAAA,GAAA,OAAA,EAAA,EAA0D,GAAA,QAAA,EAAA;AAC/B,IAAA,iBAAA,GAAA,QAAA;AAAC,IAAA,uBAAA;AAC1B,IAAA,iBAAA,CAAA;AACF,IAAA,uBAAA;;;;AADE,IAAA,oBAAA,CAAA;AAAA,IAAA,6BAAA,KAAA,OAAA,eAAA,GAAA,GAAA;;;;;AAIF,IAAA,yBAAA,GAAA,OAAA,EAAA,EAAmD,GAAA,OAAA,EAAA;AAE/C,IAAA,oBAAA,GAAA,OAAA,EAAA;AACA,IAAA,yBAAA,GAAA,GAAA;AAAG,IAAA,iBAAA,GAAA,+BAAA;AAA6B,IAAA,uBAAA,EAAI,EAChC;;;;;AAyBE,IAAA,yBAAA,GAAA,OAAA,EAAA;AACE,IAAA,iBAAA,CAAA;AACF,IAAA,uBAAA;;;;AADE,IAAA,oBAAA;AAAA,IAAA,6BAAA,KAAA,OAAA,cAAA,QAAA,GAAA,GAAA;;;;;AAcF,IAAA,yBAAA,GAAA,OAAA,EAAA;AACE,IAAA,iBAAA,CAAA;AACF,IAAA,uBAAA;;;;AADE,IAAA,oBAAA;AAAA,IAAA,6BAAA,KAAA,OAAA,cAAA,KAAA,GAAA,GAAA;;;;;AAgBF,IAAA,yBAAA,GAAA,OAAA,EAAA;AACE,IAAA,iBAAA,CAAA;AACF,IAAA,uBAAA;;;;AADE,IAAA,oBAAA;AAAA,IAAA,6BAAA,KAAA,OAAA,cAAA,OAAA,GAAA,GAAA;;;;;AAcF,IAAA,yBAAA,GAAA,OAAA,EAAA;AACE,IAAA,iBAAA,CAAA;AACF,IAAA,uBAAA;;;;AADE,IAAA,oBAAA;AAAA,IAAA,6BAAA,KAAA,OAAA,cAAA,aAAA,GAAA,GAAA;;;;;AAmCF,IAAA,yBAAA,GAAA,OAAA,EAAA;AACE,IAAA,iBAAA,CAAA;AACF,IAAA,uBAAA;;;;AADE,IAAA,oBAAA;AAAA,IAAA,6BAAA,KAAA,OAAA,cAAA,eAAA,GAAA,GAAA;;;;;AAcF,IAAA,yBAAA,GAAA,OAAA,EAAA;AACE,IAAA,iBAAA,CAAA;AACF,IAAA,uBAAA;;;;AADE,IAAA,oBAAA;AAAA,IAAA,6BAAA,KAAA,OAAA,cAAA,kBAAA,GAAA,GAAA;;;;;AAwFF,IAAA,yBAAA,GAAA,OAAA,EAAA;AACE,IAAA,iBAAA,CAAA;AACF,IAAA,uBAAA;;;;AADE,IAAA,oBAAA;AAAA,IAAA,6BAAA,KAAA,OAAA,cAAA,uBAAA,GAAA,GAAA;;;;;AAoBJ,IAAA,yBAAA,GAAA,OAAA,EAAA;AACE,IAAA,iBAAA,CAAA;AACF,IAAA,uBAAA;;;;AADE,IAAA,oBAAA;AAAA,IAAA,6BAAA,KAAA,OAAA,cAAA,oBAAA,GAAA,GAAA;;;;;;AAUF,IAAA,yBAAA,GAAA,UAAA,EAAA;AAGE,IAAA,qBAAA,SAAA,SAAA,sEAAA;AAAA,MAAA,wBAAA,GAAA;AAAA,YAAA,SAAA,wBAAA,CAAA;AAAA,aAAA,sBAAS,OAAA,WAAA,CAAY;IAAA,CAAA;AAGrB,IAAA,yBAAA,GAAA,MAAA;AAAM,IAAA,iBAAA,GAAA,QAAA;AAAC,IAAA,uBAAA;AAAQ,IAAA,iBAAA,GAAA,uBAAA;AACjB,IAAA,uBAAA;;;;;AAWM,IAAA,yBAAA,GAAA,QAAA,EAAA;AACE,IAAA,iBAAA,CAAA;AAIF,IAAA,uBAAA;;;;;AAJE,IAAA,oBAAA;AAAA,IAAA,6BAAA,MAAA,UAAA,eAAA,IAAA,KAAA,MAAA,OAAA,OAAA,QAAA,OAAA,OAAA,UAAA,eAAA,IAAA,OAAA,MAAA,OAAA,OAAA,QAAA,OAAA,OAAA,UAAA,eAAA,IAAA,YAAA,MAAA,OAAA,OAAA,QAAA,OAAA,OAAA,UAAA,eAAA,IAAA,MAAA,MAAA,OAAA,OAAA,QAAA,OAAA,GAAA;;;;;AAKF,IAAA,yBAAA,GAAA,QAAA,EAAA;AACE,IAAA,iBAAA,GAAA,gBAAA;AACF,IAAA,uBAAA;;;;;AACA,IAAA,yBAAA,GAAA,QAAA,EAAA;AACE,IAAA,iBAAA,GAAA,cAAA;AACF,IAAA,uBAAA;;;;;;AAEF,IAAA,yBAAA,GAAA,OAAA,EAAA,EAAiD,GAAA,UAAA,EAAA;AAI7C,IAAA,qBAAA,SAAA,SAAA,+EAAA;AAAA,MAAA,wBAAA,GAAA;AAAA,YAAA,OAAA,wBAAA,CAAA,EAAA;AAAA,YAAA,SAAA,wBAAA,CAAA;AAAA,aAAA,sBAAS,OAAA,kBAAA,IAAA,CAAoB;IAAA,CAAA;AAE7B,IAAA,iBAAA,GAAA,yBAAA;AACF,IAAA,uBAAA;AACA,IAAA,yBAAA,GAAA,UAAA,EAAA;AAGE,IAAA,qBAAA,SAAA,SAAA,+EAAA;AAAA,MAAA,wBAAA,GAAA;AAAA,YAAA,OAAA,wBAAA,CAAA,EAAA;AAAA,YAAA,SAAA,wBAAA,CAAA;AAAA,aAAA,sBAAS,OAAA,cAAA,IAAA,CAAgB;IAAA,CAAA;AAEzB,IAAA,iBAAA,GAAA,6BAAA;AACF,IAAA,uBAAA,EAAS;;;;;AA7Bb,IAAA,yBAAA,GAAA,OAAA,EAAA,EAA0D,GAAA,OAAA,EAAA;AAEtD,IAAA,qBAAA,GAAA,uDAAA,GAAA,GAAA,QAAA,EAAA,EAAiE,GAAA,uDAAA,GAAA,GAAA,QAAA,EAAA,EAMD,GAAA,uDAAA,GAAA,GAAA,QAAA,EAAA;AAMlE,IAAA,uBAAA;AACA,IAAA,qBAAA,GAAA,sDAAA,GAAA,GAAA,OAAA,EAAA;AAgBF,IAAA,uBAAA;;;;;;;;AA7BW,IAAA,oBAAA,CAAA;AAAA,IAAA,qBAAA,SAAA,UAAA,eAAA,IAAA,KAAA,MAAA,OAAA,OAAA,QAAA,KAAA;AAMA,IAAA,oBAAA;AAAA,IAAA,qBAAA,QAAA,GAAA,UAAA,eAAA,IAAA,KAAA,MAAA,OAAA,OAAA,QAAA,MAAA;AAGA,IAAA,oBAAA;AAAA,IAAA,qBAAA,SAAA,UAAA,eAAA,IAAA,eAAA,MAAA,OAAA,OAAA,QAAA,KAAA;AAIqB,IAAA,oBAAA;AAAA,IAAA,qBAAA,QAAA,OAAA,UAAA,CAAA;;;;;;AAmBhC,IAAA,yBAAA,GAAA,OAAA,EAAA,EAAsD,GAAA,OAAA,EAAA,EACnC,GAAA,OAAA,EAAA,EACa,GAAA,SAAA,EAAA;AACA,IAAA,iBAAA,GAAA,OAAA;AAAK,IAAA,uBAAA;AAC/B,IAAA,oBAAA,GAAA,SAAA,EAAA;AAMF,IAAA,uBAAA,EAAM;AAGR,IAAA,yBAAA,GAAA,OAAA,EAAA,EAAiB,GAAA,OAAA,EAAA,EACY,GAAA,SAAA,EAAA;AACC,IAAA,iBAAA,GAAA,SAAA;AAAO,IAAA,uBAAA;AACjC,IAAA,oBAAA,IAAA,SAAA,EAAA;AAMF,IAAA,uBAAA;AACA,IAAA,yBAAA,IAAA,OAAA,EAAA,EAA2B,IAAA,SAAA,EAAA;AACC,IAAA,iBAAA,IAAA,eAAA;AAAa,IAAA,uBAAA;AACvC,IAAA,oBAAA,IAAA,SAAA,EAAA;AAOF,IAAA,uBAAA,EAAM;AAGR,IAAA,yBAAA,IAAA,OAAA,EAAA,EAAiB,IAAA,OAAA,EAAA,EACY,IAAA,SAAA,EAAA;AACC,IAAA,iBAAA,IAAA,QAAA;AAAM,IAAA,uBAAA;AAChC,IAAA,oBAAA,IAAA,SAAA,EAAA;AAOF,IAAA,uBAAA;AACA,IAAA,yBAAA,IAAA,OAAA,EAAA,EAA2B,IAAA,OAAA,EAAA,EACD,IAAA,SAAA,GAAA;AAIpB,IAAA,qBAAA,UAAA,SAAA,0EAAA;AAAA,MAAA,wBAAA,GAAA;AAAA,YAAA,OAAA,wBAAA,EAAA;AAAA,YAAA,SAAA,wBAAA,CAAA;AAAA,aAAA,sBAAU,OAAA,UAAA,IAAA,CAAY;IAAA,CAAA;AAHxB,IAAA,uBAAA;AAOA,IAAA,yBAAA,IAAA,SAAA,GAAA;AACE,IAAA,iBAAA,IAAA,sBAAA;AACF,IAAA,uBAAA,EAAQ,EACJ,EACF;AAGR,IAAA,yBAAA,IAAA,OAAA,EAAA,EAA6B,IAAA,UAAA,GAAA;AAIzB,IAAA,qBAAA,SAAA,SAAA,0EAAA;AAAA,MAAA,wBAAA,GAAA;AAAA,YAAA,OAAA,wBAAA,EAAA;AAAA,YAAA,SAAA,wBAAA,CAAA;AAAA,aAAA,sBAAS,OAAA,kBAAA,IAAA,CAAoB;IAAA,CAAA;AAE7B,IAAA,iBAAA,IAAA,kBAAA;AACF,IAAA,uBAAA,EAAS,EACL;;;;AAjBE,IAAA,oBAAA,EAAA;AAAA,IAAA,iCAAA,MAAA,cAAA,MAAA,EAAA;AAEK,IAAA,oBAAA;AAAA,IAAA,iCAAA,OAAA,cAAA,MAAA,EAAA;;;;;AA9FjB,IAAA,yBAAA,GAAA,OAAA,EAAA;AAKE,IAAA,qBAAA,GAAA,gDAAA,GAAA,GAAA,OAAA,EAAA,EAA0D,GAAA,gDAAA,IAAA,GAAA,OAAA,EAAA;AA0G5D,IAAA,uBAAA;;;;;AA9GK,IAAA,qBAAA,iBAAA,IAAA;AAIG,IAAA,oBAAA;AAAA,IAAA,qBAAA,QAAA,CAAA,OAAA,iBAAA,IAAA,CAAA;AAkCA,IAAA,oBAAA;AAAA,IAAA,qBAAA,QAAA,OAAA,iBAAA,IAAA,CAAA;;;;;;AA4EN,IAAA,yBAAA,GAAA,UAAA,GAAA;AAGE,IAAA,qBAAA,SAAA,SAAA,4EAAA;AAAA,MAAA,wBAAA,IAAA;AAAA,YAAA,SAAA,wBAAA,CAAA;AAAA,aAAA,sBAAS,OAAA,WAAA,CAAY;IAAA,CAAA;AAGrB,IAAA,yBAAA,GAAA,MAAA;AAAM,IAAA,iBAAA,GAAA,QAAA;AAAC,IAAA,uBAAA;AAAQ,IAAA,iBAAA,GAAA,qCAAA;AACjB,IAAA,uBAAA;;;;;AATF,IAAA,yBAAA,GAAA,OAAA,GAAA,EAAwD,GAAA,KAAA,GAAA;AAChC,IAAA,iBAAA,GAAA,+BAAA;AAA0B,IAAA,uBAAA;AAChD,IAAA,qBAAA,GAAA,mDAAA,GAAA,GAAA,UAAA,GAAA;AAQF,IAAA,uBAAA;;;;AAJK,IAAA,oBAAA,CAAA;AAAA,IAAA,qBAAA,QAAA,OAAA,UAAA,CAAA;;;;;AAST,IAAA,yBAAA,GAAA,OAAA,EAAA,EAAoD,GAAA,MAAA,EAAA,EACxB,GAAA,QAAA,EAAA;AACG,IAAA,iBAAA,GAAA,cAAA;AAAE,IAAA,uBAAA;AAC7B,IAAA,iBAAA,GAAA,2BAAA;AACF,IAAA,uBAAA;AAEA,IAAA,yBAAA,GAAA,OAAA,GAAA,EAAuB,GAAA,OAAA,GAAA,EACE,GAAA,OAAA;AACd,IAAA,iBAAA,GAAA,kBAAA;AAAgB,IAAA,uBAAA;AACvB,IAAA,yBAAA,GAAA,QAAA,GAAA;AACE,IAAA,iBAAA,EAAA;AACF,IAAA,uBAAA,EAAO;AAGT,IAAA,yBAAA,IAAA,OAAA,GAAA,EAAuB,IAAA,OAAA;AACd,IAAA,iBAAA,IAAA,oBAAA;AAAkB,IAAA,uBAAA;AACzB,IAAA,yBAAA,IAAA,MAAA;AAAM,IAAA,iBAAA,EAAA;AAAoD,IAAA,uBAAA,EAAO;AAGnE,IAAA,yBAAA,IAAA,OAAA,GAAA,EAAuB,IAAA,OAAA;AACd,IAAA,iBAAA,IAAA,uBAAA;AAAkB,IAAA,uBAAA;AACzB,IAAA,yBAAA,IAAA,MAAA;AAAM,IAAA,iBAAA,EAAA;AAAyG,IAAA,uBAAA,EAAO,EAClH,EACF;;;;AAdkB,IAAA,oBAAA,CAAA;AAAA,IAAA,qBAAA,WAAA,OAAA,eAAA,OAAA,gBAAA,EAAA,QAAA,CAAA;AAClB,IAAA,oBAAA;AAAA,IAAA,6BAAA,KAAA,OAAA,cAAA,OAAA,gBAAA,EAAA,QAAA,GAAA,GAAA;AAMI,IAAA,oBAAA,CAAA;AAAA,IAAA,4BAAA,OAAA,WAAA,OAAA,gBAAA,EAAA,eAAA,CAAA;AAKA,IAAA,oBAAA,CAAA;AAAA,IAAA,4BAAA,OAAA,gBAAA,EAAA,oBAAA,OAAA,WAAA,OAAA,gBAAA,EAAA,iBAAA,IAAA,QAAA;;;;;AAhZhB,IAAA,yBAAA,GAAA,OAAA,EAAA,EAAuE,GAAA,QAAA,EAAA,EAChB,GAAA,OAAA,EAAA,EAGzB,GAAA,MAAA,EAAA,EACE,GAAA,QAAA,EAAA;AACG,IAAA,iBAAA,GAAA,0BAAA;AAAK,IAAA,uBAAA;AAChC,IAAA,iBAAA,GAAA,6BAAA;AACF,IAAA,uBAAA;AAEA,IAAA,yBAAA,GAAA,OAAA,EAAA,EAAsB,GAAA,OAAA,EAAA,EACI,GAAA,SAAA,EAAA;AACF,IAAA,iBAAA,IAAA,aAAA;AAAQ,IAAA,uBAAA;AAC5B,IAAA,oBAAA,IAAA,SAAA,EAAA;AAQA,IAAA,qBAAA,IAAA,yCAAA,GAAA,GAAA,OAAA,EAAA;AAGF,IAAA,uBAAA;AAEA,IAAA,yBAAA,IAAA,OAAA,EAAA,EAAwB,IAAA,SAAA,EAAA;AACL,IAAA,iBAAA,IAAA,OAAA;AAAK,IAAA,uBAAA;AACtB,IAAA,oBAAA,IAAA,SAAA,EAAA;AAQA,IAAA,qBAAA,IAAA,yCAAA,GAAA,GAAA,OAAA,EAAA;AAGF,IAAA,uBAAA,EAAM;AAGR,IAAA,yBAAA,IAAA,OAAA,EAAA,EAAsB,IAAA,OAAA,EAAA,EACI,IAAA,SAAA,EAAA;AACH,IAAA,iBAAA,IAAA,SAAA;AAAO,IAAA,uBAAA;AAC1B,IAAA,oBAAA,IAAA,SAAA,EAAA;AAQA,IAAA,qBAAA,IAAA,yCAAA,GAAA,GAAA,OAAA,EAAA;AAGF,IAAA,uBAAA;AAEA,IAAA,yBAAA,IAAA,OAAA,EAAA,EAAwB,IAAA,SAAA,EAAA;AACG,IAAA,iBAAA,IAAA,mBAAA;AAAW,IAAA,uBAAA;AACpC,IAAA,oBAAA,IAAA,SAAA,EAAA;AAQA,IAAA,qBAAA,IAAA,yCAAA,GAAA,GAAA,OAAA,EAAA;AAGF,IAAA,uBAAA,EAAM;AAGR,IAAA,yBAAA,IAAA,OAAA,EAAA,EAAwB,IAAA,SAAA,EAAA;AACK,IAAA,iBAAA,IAAA,mBAAA;AAAiB,IAAA,uBAAA;AAC5C,IAAA,oBAAA,IAAA,SAAA,EAAA;AAOF,IAAA,uBAAA,EAAM;AAIR,IAAA,yBAAA,IAAA,OAAA,EAAA,EAA0B,IAAA,MAAA,EAAA,EACE,IAAA,QAAA,EAAA;AACG,IAAA,iBAAA,IAAA,WAAA;AAAE,IAAA,uBAAA;AAC7B,IAAA,iBAAA,IAAA,2BAAA;AACF,IAAA,uBAAA;AAEA,IAAA,yBAAA,IAAA,OAAA,EAAA,EAAsB,IAAA,OAAA,EAAA,EACI,IAAA,SAAA,EAAA;AACK,IAAA,iBAAA,IAAA,kBAAA;AAAgB,IAAA,uBAAA;AAC3C,IAAA,oBAAA,IAAA,SAAA,EAAA;AAQA,IAAA,qBAAA,IAAA,yCAAA,GAAA,GAAA,OAAA,EAAA;AAGF,IAAA,uBAAA;AAEA,IAAA,yBAAA,IAAA,OAAA,EAAA,EAAwB,IAAA,SAAA,EAAA;AACQ,IAAA,iBAAA,IAAA,oBAAA;AAAkB,IAAA,uBAAA;AAChD,IAAA,oBAAA,IAAA,SAAA,EAAA;AAQA,IAAA,qBAAA,IAAA,yCAAA,GAAA,GAAA,OAAA,EAAA;AAGF,IAAA,uBAAA,EAAM;AAGR,IAAA,yBAAA,IAAA,OAAA,EAAA,EAAwB,IAAA,SAAA,EAAA;AACG,IAAA,iBAAA,IAAA,aAAA;AAAW,IAAA,uBAAA;AACpC,IAAA,oBAAA,IAAA,YAAA,EAAA;AAQF,IAAA,uBAAA,EAAM;AAIR,IAAA,yBAAA,IAAA,OAAA,EAAA,EAA0B,IAAA,MAAA,EAAA,EACE,IAAA,QAAA,EAAA;AACG,IAAA,iBAAA,IAAA,WAAA;AAAE,IAAA,uBAAA;AAC7B,IAAA,iBAAA,IAAA,0BAAA;AACF,IAAA,uBAAA;AAEA,IAAA,yBAAA,IAAA,OAAA,EAAA,EAAsB,IAAA,OAAA,EAAA,EACI,IAAA,SAAA,EAAA;AACC,IAAA,iBAAA,IAAA,KAAA;AAAG,IAAA,uBAAA;AAC1B,IAAA,oBAAA,IAAA,SAAA,EAAA;AAOA,IAAA,yBAAA,IAAA,SAAA,EAAA;AAAyB,IAAA,iBAAA,IAAA,qEAAA;AAAmE,IAAA,uBAAA,EAAQ;AAGtG,IAAA,yBAAA,IAAA,OAAA,EAAA,EAAwB,IAAA,SAAA,EAAA;AACE,IAAA,iBAAA,IAAA,aAAA;AAAW,IAAA,uBAAA;AACnC,IAAA,oBAAA,IAAA,SAAA,EAAA;AAOF,IAAA,uBAAA,EAAM,EACF;AAIR,IAAA,yBAAA,IAAA,OAAA,EAAA,EAA0B,IAAA,MAAA,EAAA,EACE,IAAA,QAAA,EAAA;AACG,IAAA,iBAAA,IAAA,WAAA;AAAE,IAAA,uBAAA;AAC7B,IAAA,iBAAA,IAAA,6BAAA;AACF,IAAA,uBAAA;AAEA,IAAA,yBAAA,IAAA,OAAA,EAAA,EAAsB,IAAA,OAAA,EAAA,EACI,IAAA,SAAA,EAAA;AACE,IAAA,iBAAA,IAAA,YAAA;AAAU,IAAA,uBAAA;AAClC,IAAA,yBAAA,IAAA,OAAA,EAAA;AACE,IAAA,oBAAA,IAAA,SAAA,EAAA;AAOA,IAAA,yBAAA,IAAA,QAAA,EAAA;AAA2B,IAAA,iBAAA,IAAA,GAAA;AAAC,IAAA,uBAAA,EAAO;AAErC,IAAA,yBAAA,IAAA,SAAA,EAAA;AAAyB,IAAA,iBAAA,IAAA,mDAAA;AAA8C,IAAA,uBAAA,EAAQ;AAGjF,IAAA,yBAAA,IAAA,OAAA,EAAA,EAAwB,IAAA,SAAA,EAAA;AACa,IAAA,iBAAA,IAAA,8BAAA;AAAsB,IAAA,uBAAA;AACzD,IAAA,yBAAA,IAAA,OAAA,EAAA;AACE,IAAA,oBAAA,IAAA,SAAA,EAAA;AASA,IAAA,yBAAA,IAAA,QAAA,EAAA;AAA2B,IAAA,iBAAA,IAAA,OAAA;AAAK,IAAA,uBAAA,EAAO;AAEzC,IAAA,qBAAA,IAAA,yCAAA,GAAA,GAAA,OAAA,EAAA;AAGF,IAAA,uBAAA,EAAM;AAGR,IAAA,yBAAA,IAAA,OAAA,EAAA,EAAwB,IAAA,SAAA,EAAA;AACU,IAAA,iBAAA,IAAA,8BAAA;AAA4B,IAAA,uBAAA;AAC5D,IAAA,yBAAA,IAAA,OAAA,EAAA;AACE,IAAA,oBAAA,IAAA,SAAA,EAAA;AAUA,IAAA,yBAAA,IAAA,QAAA,EAAA;AAA2B,IAAA,iBAAA,IAAA,IAAA;AAAE,IAAA,uBAAA,EAAO;AAEtC,IAAA,qBAAA,IAAA,yCAAA,GAAA,GAAA,OAAA,EAAA;AAGF,IAAA,uBAAA,EAAM;AAIR,IAAA,yBAAA,KAAA,OAAA,EAAA,EAA0B,KAAA,MAAA,EAAA,EACE,KAAA,QAAA,EAAA;AACG,IAAA,iBAAA,KAAA,WAAA;AAAE,IAAA,uBAAA;AAC7B,IAAA,iBAAA,KAAA,YAAA;AACA,IAAA,qBAAA,KAAA,6CAAA,GAAA,GAAA,UAAA,EAAA;AAQF,IAAA,uBAAA;AAEA,IAAA,yBAAA,KAAA,OAAA,EAAA;AACE,IAAA,qBAAA,KAAA,0CAAA,GAAA,GAAA,OAAA,EAAA,EAE0B,KAAA,0CAAA,GAAA,GAAA,OAAA,EAAA;AA0H5B,IAAA,uBAAA,EAAM;AAIR,IAAA,qBAAA,KAAA,0CAAA,IAAA,GAAA,OAAA,EAAA;AAyBF,IAAA,uBAAA,EAAO;;;;AAnZD,IAAA,oBAAA;AAAA,IAAA,qBAAA,aAAA,OAAA,WAAA;AAiBI,IAAA,oBAAA,EAAA;AAAA,IAAA,sBAAA,cAAA,OAAA,cAAA,QAAA,CAAA;AACA,IAAA,qBAAA,YAAA,CAAA,OAAA,UAAA,CAAA;AAE6B,IAAA,oBAAA;AAAA,IAAA,qBAAA,QAAA,OAAA,cAAA,QAAA,CAAA;AAY7B,IAAA,oBAAA,CAAA;AAAA,IAAA,sBAAA,cAAA,OAAA,cAAA,KAAA,CAAA;AACA,IAAA,qBAAA,YAAA,CAAA,OAAA,UAAA,CAAA;AAE6B,IAAA,oBAAA;AAAA,IAAA,qBAAA,QAAA,OAAA,cAAA,KAAA,CAAA;AAc7B,IAAA,oBAAA,CAAA;AAAA,IAAA,sBAAA,cAAA,OAAA,cAAA,OAAA,CAAA;AACA,IAAA,qBAAA,YAAA,CAAA,OAAA,UAAA,CAAA;AAE6B,IAAA,oBAAA;AAAA,IAAA,qBAAA,QAAA,OAAA,cAAA,OAAA,CAAA;AAY7B,IAAA,oBAAA,CAAA;AAAA,IAAA,sBAAA,cAAA,OAAA,cAAA,aAAA,CAAA;AACA,IAAA,qBAAA,YAAA,CAAA,OAAA,UAAA,CAAA;AAE6B,IAAA,oBAAA;AAAA,IAAA,qBAAA,QAAA,OAAA,cAAA,aAAA,CAAA;AAa/B,IAAA,oBAAA,CAAA;AAAA,IAAA,qBAAA,YAAA,CAAA,OAAA,UAAA,CAAA;AAoBE,IAAA,oBAAA,EAAA;AAAA,IAAA,sBAAA,cAAA,OAAA,cAAA,eAAA,CAAA;AACA,IAAA,qBAAA,YAAA,CAAA,OAAA,UAAA,CAAA;AAE6B,IAAA,oBAAA;AAAA,IAAA,qBAAA,QAAA,OAAA,cAAA,eAAA,CAAA;AAY7B,IAAA,oBAAA,CAAA;AAAA,IAAA,sBAAA,cAAA,OAAA,cAAA,kBAAA,CAAA;AACA,IAAA,qBAAA,YAAA,CAAA,OAAA,UAAA,CAAA;AAE6B,IAAA,oBAAA;AAAA,IAAA,qBAAA,QAAA,OAAA,cAAA,kBAAA,CAAA;AAa/B,IAAA,oBAAA,CAAA;AAAA,IAAA,qBAAA,YAAA,CAAA,OAAA,UAAA,CAAA;AAsEI,IAAA,oBAAA,EAAA;AAAA,IAAA,sBAAA,cAAA,OAAA,cAAA,uBAAA,CAAA;AACA,IAAA,qBAAA,YAAA,CAAA,OAAA,UAAA,CAAA;AAK2B,IAAA,oBAAA,CAAA;AAAA,IAAA,qBAAA,QAAA,OAAA,cAAA,uBAAA,CAAA;AAc7B,IAAA,oBAAA,CAAA;AAAA,IAAA,sBAAA,cAAA,OAAA,cAAA,oBAAA,CAAA;AACA,IAAA,qBAAA,YAAA,CAAA,OAAA,UAAA,CAAA;AAM2B,IAAA,oBAAA,CAAA;AAAA,IAAA,qBAAA,QAAA,OAAA,cAAA,oBAAA,CAAA;AAe5B,IAAA,oBAAA,CAAA;AAAA,IAAA,qBAAA,QAAA,OAAA,UAAA,CAAA;AAO0B,IAAA,oBAAA,CAAA;AAAA,IAAA,qBAAA,WAAA,OAAA,SAAA,QAAA;AAiHvB,IAAA,oBAAA;AAAA,IAAA,qBAAA,QAAA,OAAA,SAAA,WAAA,CAAA;AAeiB,IAAA,oBAAA;AAAA,IAAA,qBAAA,QAAA,OAAA,gBAAA,CAAA;;;ADpb3B,IAAO,mBAAP,MAAO,kBAAgB;EAiBjB;EACA;EACA;EACA;EACD;;EAnBT,kBAAkB,OAA2B,IAAI;EACjD,YAAY,OAAO,IAAI;EACvB,YAAY,OAAO,KAAK;EACxB,WAAW,OAAO,KAAK;EACvB,QAAQ,OAAsB,IAAI;EAClC,iBAAiB,OAAsB,IAAI;;EAG3C,qBAAqB;EACrB,mBAAmB,oBAAI,IAAG;;EAG1B;EAEA,YACU,aACA,kBACA,IACA,oBACD,iBAAgC;AAJ/B,SAAA,cAAA;AACA,SAAA,mBAAA;AACA,SAAA,KAAA;AACA,SAAA,qBAAA;AACD,SAAA,kBAAA;AAEP,SAAK,cAAc,KAAK,WAAU;EACpC;EAEA,WAAQ;AACN,YAAQ,IAAI,oCAA6B;AACzC,YAAQ,IAAI,YAAY,CAAC,CAAC,aAAa,QAAQ,OAAO,CAAC;AACvD,YAAQ,IAAI,WAAW,CAAC,CAAC,aAAa,QAAQ,MAAM,CAAC;AACrD,YAAQ,IAAI,0BAA0B,CAAC,CAAC,aAAa,QAAQ,qBAAqB,CAAC;AACnF,YAAQ,IAAI,iBAAiB,aAAa,QAAQ,YAAY,CAAC;AAE/D,SAAK,gBAAe;EACtB;;;;EAKA,IAAI,WAAQ;AACV,WAAO,KAAK,YAAY,IAAI,UAAU;EACxC;;;;EAKQ,aAAU;AAChB,WAAO,KAAK,GAAG,MAAM;;MAEnB,KAAK,CAAC,IAAI,CAAC,WAAW,UAAU,WAAW,UAAU,CAAC,CAAC,CAAC;MACxD,QAAQ,CAAC,IAAI,CAAC,WAAW,UAAU,WAAW,UAAU,CAAC,CAAC,CAAC;MAC3D,OAAO,CAAC,IAAI,CAAC,WAAW,UAAU,WAAW,KAAK,CAAC;MACnD,aAAa,CAAC,IAAI,CAAC,WAAW,UAAU,WAAW,QAAQ,iBAAiB,CAAC,CAAC;MAC9E,eAAe,CAAC,EAAE;;MAGlB,eAAe,CAAC,IAAI,CAAC,WAAW,UAAU,WAAW,UAAU,CAAC,CAAC,CAAC;MAClE,kBAAkB,CAAC,IAAI,CAAC,WAAW,QAAQ,CAAC;MAC5C,aAAa,CAAC,EAAE;;MAGhB,WAAW,CAAC,EAAC,OAAO,IAAI,UAAU,KAAI,CAAC;MACvC,YAAY,CAAC,EAAC,OAAO,IAAI,UAAU,KAAI,CAAC;;MAGxC,YAAY,CAAC,EAAC,OAAO,GAAG,UAAU,KAAI,CAAC;MACvC,uBAAuB,CAAC,IAAI,CAAC,WAAW,UAAU,WAAW,IAAI,CAAC,CAAC,CAAC;MACpE,oBAAoB,CAAC,IAAI,CAAC,WAAW,UAAU,WAAW,IAAI,CAAC,CAAC,CAAC;;MAGjE,UAAU,KAAK,GAAG,MAAM,CAAA,CAAE;KAC3B;EACH;;;;EAKQ,kBAAe;AACrB,SAAK,UAAU,IAAI,IAAI;AACvB,SAAK,MAAM,IAAI,IAAI;AAEnB,YAAQ,IAAI,kDAAwC;AAGpD,UAAM,cAAc,KAAK,YAAY,eAAc;AACnD,QAAI,eAAe,YAAY,IAAI;AACjC,cAAQ,IAAI,mFAAqE,YAAY,EAAE;AAE/F,WAAK,mBAAmB,QAAQ,YAAY,EAAE,EAAE,UAAU;QACxD,MAAM,CAAC,aAAY;AACjB,kBAAQ,IAAI,wDAA8C,QAAQ;AAClE,eAAK,kBAAkB,QAAQ;QACjC;QACA,OAAO,CAAC,UAAS;AACf,kBAAQ,MAAM,6DAAmD,KAAK;AAEtE,eAAK,qBAAoB;QAC3B;OACD;IACH,OAAO;AACL,cAAQ,KAAK,sEAAyD;AACtE,WAAK,qBAAoB;IAC3B;EACF;;;;EAKQ,kBAAkB,UAAa;AACrC,QAAI;AAEJ,QAAI,YAAY,SAAS,MAAM;AAE7B,YAAM,UAAU,SAAS;AACzB,oBAAc,KAAK,wBAAwB,OAAO;IACpD,WAAW,UAAU;AAEnB,oBAAc,KAAK,wBAAwB,QAAQ;IACrD,OAAO;AACL,cAAQ,MAAM,4BAAoB;AAClC,WAAK,qBAAoB;AACzB;IACF;AAEA,YAAQ,IAAI,0CAAgC,WAAW;AACvD,SAAK,gBAAgB,IAAI,WAAW;AACpC,SAAK,aAAa,WAAW;AAG7B,iBAAa,QAAQ,uBAAuB,KAAK,UAAU,WAAW,CAAC;AACvE,SAAK,UAAU,IAAI,KAAK;EAC1B;;;;EAKQ,wBAAwB,SAAY;AAC1C,WAAO;MACL,IAAI,QAAQ;MACZ,OAAO,QAAQ;MACf,KAAK,QAAQ;MACb,QAAQ,QAAQ;MAChB,aAAa,QAAQ;MACrB,MAAM,QAAQ;MACd,eAAe,QAAQ;MACvB,iBAAiB,QAAQ;MACzB,mBAAmB,QAAQ;MAC3B,UAAU,QAAQ;MAClB,kBAAkB,QAAQ;MAC1B,eAAe,QAAQ;MACvB,aAAa,QAAQ,eAAe;MACpC,WAAW,QAAQ,aAAa,QAAQ,OAAO;MAC/C,YAAY,QAAQ;MACpB,YAAY,QAAQ;MACpB,uBAAuB,QAAQ;MAC/B,oBAAoB,QAAQ;MAC5B,UAAU,QAAQ,YAAY;MAC9B,UAAU,QAAQ,YAAY,CAAA;;EAElC;;;;EAKQ,uBAAoB;AAE1B,UAAM,gBAAgB,aAAa,QAAQ,qBAAqB;AAChE,UAAM,aAAa,aAAa,QAAQ,MAAM;AAC9C,UAAM,cAAc,KAAK,YAAY,eAAc;AAEnD,QAAI,eAAe;AACjB,UAAI;AACF,cAAM,cAAc,KAAK,MAAM,aAAa;AAC5C,gBAAQ,IAAI,oEAAuD,WAAW;AAG9E,cAAM,cAA2B;UAC/B,IAAI,YAAY;UAChB,OAAO,YAAY;UACnB,KAAK,YAAY;UACjB,QAAQ,YAAY;UACpB,aAAa,YAAY;UACzB,MAAM,YAAY;UAClB,eAAe,YAAY;UAC3B,iBAAiB,YAAY;UAC7B,mBAAmB,YAAY;UAC/B,UAAU,YAAY;UACtB,kBAAkB,YAAY;UAC9B,eAAe,YAAY;UAC3B,aAAa,YAAY;UACzB,WAAW,YAAY;UACvB,YAAY,YAAY;UACxB,YAAY,YAAY;UACxB,uBAAuB,YAAY;UACnC,oBAAoB,YAAY;UAChC,UAAU,YAAY;UACtB,UAAU,YAAY;;AAGxB,gBAAQ,IAAI,4DAA2C,WAAW;AAClE,aAAK,gBAAgB,IAAI,WAAW;AACpC,aAAK,aAAa,WAAW;AAC7B,aAAK,UAAU,IAAI,KAAK;AACxB;MACF,SAAS,OAAO;AACd,gBAAQ,KAAK,oDAA0C,KAAK;MAC9D;IACF;AAGA,QAAI,YAAY;AACd,UAAI;AACF,cAAM,WAAW,KAAK,MAAM,UAAU;AACtC,gBAAQ,IAAI,mEAAyD,QAAQ;AAG7E,cAAM,yBAAsC;UAC1C,IAAI,SAAS,MAAM,aAAa,MAAM;UACtC,OAAO,SAAS,SAAS,aAAa,SAAS;UAC/C,KAAK,SAAS,OAAO,aAAa,OAAO;UACzC,QAAQ,SAAS,UAAU,aAAa,UAAU;UAClD,aAAa,SAAS,eAAe,SAAS,aAAa;UAC3D,MAAM,SAAS,QAAQ;UACvB,eAAe,SAAS,iBAAiB;UACzC,iBAAiB,SAAS,oBAAmB,oBAAI,KAAI,GAAG,YAAW;UACnE,mBAAmB,SAAS,qBAAqB;UACjD,UAAU,SAAS,aAAa,SAAY,SAAS,WAAW;UAChE,kBAAkB,SAAS,oBAAoB;UAC/C,eAAe,SAAS,iBAAiB;UACzC,aAAa,SAAS,eAAe;UACrC,WAAW,SAAS,aAAa,SAAS,OAAO;UACjD,YAAY,SAAS,cAAc;UACnC,YAAY,SAAS,cAAc;UACnC,uBAAuB,SAAS,yBAAyB;UACzD,oBAAoB,SAAS,sBAAsB;UACnD,UAAU,SAAS,YAAY;UAC/B,UAAU,SAAS,YAAY,CAAA;;AAGjC,gBAAQ,IAAI,iDAAsC,sBAAsB;AACxE,aAAK,gBAAgB,IAAI,sBAAsB;AAC/C,aAAK,aAAa,sBAAsB;AACxC,aAAK,UAAU,IAAI,KAAK;AACxB;MACF,SAAS,OAAO;AACd,gBAAQ,KAAK,kDAAwC,KAAK;MAC5D;IACF;AAGA,YAAQ,IAAI,uDAAgD;AAC5D,SAAK,iBAAiB,mBAAkB,EAAG,UAAU;MACnD,MAAM,CAAC,gBAAe;AACpB,YAAI,aAAa;AACf,kBAAQ,IAAI,iEAAsD,WAAW;AAC7E,eAAK,gBAAgB,IAAI,WAAW;AACpC,eAAK,aAAa,WAAW;QAC/B,OAAO;AAEL,eAAK,qBAAoB;QAC3B;AACA,aAAK,UAAU,IAAI,KAAK;MAC1B;MACA,OAAO,CAAC,UAAS;AACf,gBAAQ,MAAM,oEAA+D,KAAK;AAElF,aAAK,qBAAoB;AACzB,aAAK,UAAU,IAAI,KAAK;MAC1B;KACD;EACH;;;;EAKQ,uBAAoB;AAC1B,UAAM,cAAc,KAAK,YAAY,eAAc;AAEnD,QAAI,aAAa;AACf,YAAM,iBAA8B;QAClC,IAAI,YAAY;QAChB,OAAO,YAAY;QACnB,KAAK,YAAY;QACjB,QAAQ,YAAY;QACpB,aAAc,YAAoB,eAAgB,YAAoB,aAAa;QACnF,MAAM,YAAY,QAAQ;QAC1B,eAAe;QACf,kBAAiB,oBAAI,KAAI,GAAG,YAAW;QACvC,mBAAmB;QACnB,UAAU;QACV,kBAAkB;QAClB,eAAe;QACf,aAAa;QACb,WAAW;QACX,YAAY;QACZ,YAAY;QACZ,uBAAuB;QACvB,oBAAoB;QACpB,UAAU;QACV,UAAU,CAAA;;AAGZ,cAAQ,IAAI,qCAA0B,cAAc;AACpD,WAAK,gBAAgB,IAAI,cAAc;AACvC,WAAK,aAAa,cAAc;IAClC,OAAO;AACL,WAAK,MAAM,IAAI,qEAAkE;IACnF;EACF;;;;EAKQ,aAAa,aAAwB;AAC3C,YAAQ,IAAI,6CAAsC,WAAW;AAE7D,SAAK,YAAY,WAAW;MAC1B,KAAK,YAAY,OAAO;MACxB,QAAQ,YAAY,UAAU;MAC9B,OAAO,YAAY,SAAS;MAC5B,aAAa,YAAY,eAAe;MACxC,eAAe,YAAY,gBAAgB,YAAY,cAAc,MAAM,GAAG,EAAE,CAAC,IAAI;MACrF,eAAe,YAAY,iBAAiB;MAC5C,kBAAkB,YAAY,oBAAoB;MAClD,aAAa,YAAY,eAAe;MACxC,WAAW,YAAY,aAAa;MACpC,YAAY,YAAY,cAAc;MACtC,YAAY,YAAY,cAAc;MACtC,uBAAuB,YAAY,yBAAyB;MAC5D,oBAAoB,YAAY,sBAAsB;KACvD;AAGD,SAAK,aAAa,YAAY,YAAY,CAAA,CAAE;AAE5C,YAAQ,IAAI,8CAAyC,KAAK,YAAY,KAAK;EAC7E;;;;EAKQ,aAAa,UAAsB;AACzC,UAAM,gBAAgB,KAAK;AAC3B,kBAAc,MAAK;AAEnB,aAAS,QAAQ,aAAU;AACzB,oBAAc,KAAK,KAAK,uBAAuB,OAAO,CAAC;IACzD,CAAC;EACH;;;;EAKQ,uBAAuB,SAAoB;AACjD,WAAO,KAAK,GAAG,MAAM;MACnB,IAAI,CAAC,SAAS,MAAM,CAAC;MACrB,KAAK,CAAC,SAAS,OAAO,IAAI,WAAW,QAAQ;MAC7C,OAAO,CAAC,SAAS,SAAS,IAAI,WAAW,QAAQ;MACjD,YAAY,CAAC,SAAS,cAAc,IAAI,CAAC,WAAW,UAAU,WAAW,QAAQ,YAAY,CAAC,CAAC;MAC/F,MAAM,CAAC,SAAS,QAAQ,WAAW,WAAW,QAAQ;MACtD,eAAe,CAAC,SAAS,iBAAiB,KAAK;KAChD;EACH;;;;EAKA,aAAU;AACR,UAAM,WAAW,KAAK,SAAS;AAC/B,SAAK,SAAS,KAAK,KAAK,uBAAsB,CAAE;AAChD,SAAK,iBAAiB,IAAI,QAAQ;EACpC;;;;EAKA,cAAc,OAAa;AACzB,SAAK,SAAS,SAAS,KAAK;AAC5B,SAAK,iBAAiB,OAAO,KAAK;AAGlC,UAAM,sBAAsB,oBAAI,IAAG;AACnC,SAAK,iBAAiB,QAAQ,eAAY;AACxC,UAAI,YAAY,OAAO;AACrB,4BAAoB,IAAI,YAAY,CAAC;MACvC,WAAW,YAAY,OAAO;AAC5B,4BAAoB,IAAI,SAAS;MACnC;IACF,CAAC;AACD,SAAK,mBAAmB;EAC1B;;;;EAKA,kBAAkB,OAAa;AAC7B,QAAI,KAAK,iBAAiB,IAAI,KAAK,GAAG;AACpC,WAAK,iBAAiB,OAAO,KAAK;IACpC,OAAO;AACL,WAAK,iBAAiB,IAAI,KAAK;IACjC;EACF;;;;EAKA,iBAAiB,OAAa;AAC5B,WAAO,KAAK,iBAAiB,IAAI,KAAK;EACxC;;;;EAKA,UAAU,OAAa;AAErB,aAAS,IAAI,GAAG,IAAI,KAAK,SAAS,QAAQ,KAAK;AAC7C,WAAK,SAAS,GAAG,CAAC,EAAE,IAAI,eAAe,GAAG,SAAS,MAAM,KAAK;IAChE;EACF;;;;EAKA,uBAAoB;AAClB,SAAK,qBAAqB,CAAC,KAAK;EAClC;;;;EAKA,aAAU;AACR,SAAK,UAAU,IAAI,IAAI;AACvB,SAAK,eAAe,IAAI,IAAI;AAC5B,SAAK,MAAM,IAAI,IAAI;EACrB;;;;EAKA,aAAU;AACR,SAAK,UAAU,IAAI,KAAK;AACxB,UAAM,cAAc,KAAK,gBAAe;AACxC,QAAI,aAAa;AACf,WAAK,aAAa,WAAW;IAC/B;AACA,SAAK,MAAM,IAAI,IAAI;EACrB;;;;EAKA,cAAW;AACT,QAAI,KAAK,YAAY,OAAO;AAC1B,WAAK,SAAS,IAAI,IAAI;AACtB,WAAK,MAAM,IAAI,IAAI;AAEnB,YAAM,WAAW,KAAK,YAAY;AAClC,cAAQ,IAAI,mCAA4B,QAAQ;AAIhD,iBAAW,MAAK;AACd,aAAK,SAAS,IAAI,KAAK;AACvB,aAAK,UAAU,IAAI,KAAK;AACxB,aAAK,eAAe,IAAI,uCAAiC;AAGzD,mBAAW,MAAK;AACd,eAAK,eAAe,IAAI,IAAI;QAC9B,GAAG,GAAI;MACT,GAAG,GAAI;IACT,OAAO;AACL,WAAK,MAAM,IAAI,kDAAkD;AACjE,WAAK,qBAAoB;IAC3B;EACF;;;;EAKQ,uBAAoB;AAC1B,WAAO,KAAK,KAAK,YAAY,QAAQ,EAAE,QAAQ,SAAM;AACnD,YAAM,UAAU,KAAK,YAAY,IAAI,GAAG;AACxC,eAAS,cAAa;IACxB,CAAC;EACH;;;;EAKA,cAAc,WAAiB;AAC7B,UAAM,QAAQ,KAAK,YAAY,IAAI,SAAS;AAC5C,WAAO,CAAC,EAAE,SAAS,MAAM,WAAW,MAAM;EAC5C;;;;EAKA,cAAc,WAAiB;AAC7B,UAAM,QAAQ,KAAK,YAAY,IAAI,SAAS;AAC5C,QAAI,SAAS,MAAM,UAAU,MAAM,SAAS;AAC1C,UAAI,MAAM,OAAO,UAAU;AAAG,eAAO;AACrC,UAAI,MAAM,OAAO,OAAO;AAAG,eAAO;AAClC,UAAI,MAAM,OAAO,WAAW;AAAG,eAAO,WAAW,MAAM,OAAO,WAAW,EAAE,cAAc;AACzF,UAAI,MAAM,OAAO,SAAS;AAAG,eAAO;AACpC,UAAI,MAAM,OAAO,KAAK;AAAG,eAAO,mBAAmB,MAAM,OAAO,KAAK,EAAE,GAAG;IAC5E;AACA,WAAO;EACT;;;;EAKA,WAAW,YAAkB;AAC3B,QAAI,CAAC;AAAY,aAAO;AACxB,WAAO,IAAI,KAAK,UAAU,EAAE,mBAAmB,OAAO;EACxD;;;;EAKA,cAAc,UAAiB;AAC7B,WAAO,WAAW,UAAU;EAC9B;;;;EAKA,eAAe,UAAiB;AAC9B,WAAO,WAAW,kBAAkB;EACtC;;;;EAKA,iBAAiB,YAAkB;AACjC,WAAO,GAAG,UAAU;EACtB;;;;EAKA,eAAe,QAAc;AAC3B,WAAO,IAAI,KAAK,aAAa,SAAS;MACpC,OAAO;MACP,UAAU;KACX,EAAE,OAAO,MAAM;EAClB;;;;EAKA,iBAAc;AACZ,YAAQ,IAAI,yDAAgD;AAE5D,SAAK,UAAU,IAAI,IAAI;AACvB,SAAK,MAAM,IAAI,IAAI;AAEnB,UAAM,cAAc,KAAK,YAAY,eAAc;AACnD,QAAI,eAAe,YAAY,IAAI;AACjC,WAAK,mBAAmB,QAAQ,YAAY,EAAE,EAAE,UAAU;QACxD,MAAM,CAAC,aAAY;AACjB,kBAAQ,IAAI,4CAAqC,QAAQ;AACzD,eAAK,kBAAkB,QAAQ;QACjC;QACA,OAAO,CAAC,UAAS;AACf,kBAAQ,MAAM,8CAAsC,KAAK;AACzD,eAAK,MAAM,IAAI,8CAA2C;AAC1D,eAAK,UAAU,IAAI,KAAK;QAC1B;OACD;IACH,OAAO;AACL,WAAK,MAAM,IAAI,6BAA0B;AACzC,WAAK,UAAU,IAAI,KAAK;IAC1B;EACF;;;;EAKA,eAAY;AACV,YAAQ,IAAI,iCAA0B;AACtC,YAAQ,IAAI,yBAAkB;AAC9B,YAAQ,IAAI,cAAc,CAAC,CAAC,aAAa,QAAQ,OAAO,CAAC;AACzD,YAAQ,IAAI,aAAa,aAAa,QAAQ,MAAM,CAAC;AACrD,YAAQ,IAAI,4BAA4B,aAAa,QAAQ,qBAAqB,CAAC;AACnF,YAAQ,IAAI,mBAAmB,aAAa,QAAQ,YAAY,CAAC;AACjE,YAAQ,IAAI,wBAAiB;AAC7B,YAAQ,IAAI,oBAAoB,KAAK,YAAY,eAAc,CAAE;AACjE,YAAQ,IAAI,wBAAwB,KAAK,YAAY,gBAAe,CAAE;AACtE,YAAQ,IAAI,sBAAe;AAC3B,YAAQ,IAAI,wBAAwB,KAAK,gBAAe,CAAE;AAC1D,YAAQ,IAAI,oBAAoB,KAAK,YAAY,KAAK;AACtD,YAAQ,IAAI,kBAAkB,KAAK,UAAS,CAAE;AAC9C,YAAQ,IAAI,cAAc,KAAK,MAAK,CAAE;AACtC,YAAQ,IAAI,sCAA+B;AAC3C,YAAQ,IAAI,0BAAuB,OAAO,KAAK,YAAY,EAAE,MAAM;AACnE,YAAQ,IAAI,2BAAwB,OAAO,KAAK,YAAY,CAAC;EAC/D;;;;EAKA,2BAAwB;AACtB,YAAQ,IAAI,iEAAuD;AACnE,SAAK,UAAU,IAAI,IAAI;AACvB,SAAK,MAAM,IAAI,IAAI;AAGnB,UAAM,gBAAgB,aAAa,QAAQ,qBAAqB;AAChE,QAAI,eAAe;AACjB,UAAI;AACF,cAAM,cAAc,KAAK,MAAM,aAAa;AAC5C,gBAAQ,IAAI,kDAAkC,WAAW;AAEzD,cAAM,cAA2B;UAC/B,IAAI,YAAY,MAAM;UACtB,OAAO,YAAY,SAAS;UAC5B,KAAK,YAAY,OAAO;UACxB,QAAQ,YAAY,UAAU;UAC9B,aAAa,YAAY,eAAe;UACxC,MAAM,YAAY,QAAQ;UAC1B,eAAe,YAAY,iBAAiB;UAC5C,iBAAiB,YAAY,oBAAmB,oBAAI,KAAI,GAAG,YAAW;UACtE,mBAAmB,YAAY,qBAAqB;UACpD,UAAU,YAAY,aAAa,SAAY,YAAY,WAAW;UACtE,kBAAkB,YAAY,oBAAoB;UAClD,eAAe,YAAY,iBAAiB;UAC5C,aAAa,YAAY,eAAe;UACxC,WAAW,YAAY,aAAa;UACpC,YAAY,YAAY,cAAc;UACtC,YAAY,YAAY,cAAc;UACtC,uBAAuB,YAAY,yBAAyB;UAC5D,oBAAoB,YAAY,sBAAsB;UACtD,UAAU,YAAY,YAAY;UAClC,UAAU,YAAY,YAAY,CAAA;;AAGpC,gBAAQ,IAAI,4DAA2C,WAAW;AAClE,aAAK,gBAAgB,IAAI,WAAW;AACpC,aAAK,aAAa,WAAW;AAC7B,aAAK,UAAU,IAAI,KAAK;AACxB;MACF,SAAS,OAAO;AACd,gBAAQ,MAAM,8CAAyC,KAAK;MAC9D;IACF;AAGA,UAAM,aAAa,aAAa,QAAQ,MAAM;AAC9C,QAAI,YAAY;AACd,UAAI;AACF,cAAM,WAAW,KAAK,MAAM,UAAU;AACtC,gBAAQ,IAAI,sCAA4B,QAAQ;AAEhD,cAAM,cAA2B;UAC/B,IAAI,SAAS,MAAM;UACnB,OAAO,SAAS,SAAS;UACzB,KAAK,SAAS,OAAO;UACrB,QAAQ,SAAS,UAAU;UAC3B,aAAa,SAAS,eAAe,SAAS,aAAa;UAC3D,MAAM,SAAS,QAAQ;UACvB,eAAe,SAAS,iBAAiB;UACzC,iBAAiB,SAAS,oBAAmB,oBAAI,KAAI,GAAG,YAAW;UACnE,mBAAmB,SAAS,qBAAqB;UACjD,UAAU,SAAS,aAAa,SAAY,SAAS,WAAW;UAChE,kBAAkB,SAAS,oBAAoB;UAC/C,eAAe,SAAS,iBAAiB;UACzC,aAAa,SAAS,eAAe;UACrC,WAAW,SAAS,aAAa,SAAS,OAAO;UACjD,YAAY,SAAS,cAAc;UACnC,YAAY,SAAS,cAAc;UACnC,uBAAuB,SAAS,yBAAyB;UACzD,oBAAoB,SAAS,sBAAsB;UACnD,UAAU,SAAS,YAAY;UAC/B,UAAU,SAAS,YAAY,CAAA;;AAGjC,gBAAQ,IAAI,iDAAsC,WAAW;AAC7D,aAAK,gBAAgB,IAAI,WAAW;AACpC,aAAK,aAAa,WAAW;AAC7B,aAAK,UAAU,IAAI,KAAK;MAC1B,SAAS,OAAO;AACd,gBAAQ,MAAM,uCAAkC,KAAK;AACrD,aAAK,MAAM,IAAI,uDAAoD;AACnE,aAAK,UAAU,IAAI,KAAK;MAC1B;IACF,OAAO;AACL,WAAK,MAAM,IAAI,2DAAqD;AACpE,WAAK,UAAU,IAAI,KAAK;IAC1B;EACF;;;;EAKA,YAAY,OAAU;AAEpB,UAAM,OAAO,MAAM,KAAK,gBAAgB,kBAAiB;EAC3D;;qCA3rBW,mBAAgB,4BAAA,WAAA,GAAA,4BAAA,gBAAA,GAAA,4BAAA,WAAA,GAAA,4BAAA,kBAAA,GAAA,4BAAA,eAAA,CAAA;EAAA;yEAAhB,mBAAgB,WAAA,CAAA,CAAA,aAAA,CAAA,GAAA,OAAA,IAAA,MAAA,GAAA,QAAA,CAAA,CAAA,GAAA,mBAAA,GAAA,CAAA,GAAA,gBAAA,GAAA,CAAA,GAAA,gBAAA,GAAA,CAAA,GAAA,aAAA,GAAA,CAAA,SAAA,gBAAA,GAAA,MAAA,GAAA,CAAA,GAAA,aAAA,GAAA,CAAA,GAAA,eAAA,GAAA,CAAA,GAAA,YAAA,GAAA,CAAA,GAAA,kBAAA,GAAA,CAAA,GAAA,gBAAA,GAAA,CAAA,GAAA,MAAA,GAAA,CAAA,SAAA,qBAAA,GAAA,MAAA,GAAA,CAAA,SAAA,uBAAA,GAAA,MAAA,GAAA,CAAA,SAAA,qBAAA,GAAA,MAAA,GAAA,CAAA,SAAA,mBAAA,GAAA,MAAA,GAAA,CAAA,GAAA,cAAA,GAAA,CAAA,WAAA,QAAA,GAAA,cAAA,GAAA,SAAA,OAAA,KAAA,GAAA,CAAA,SAAA,mBAAA,QAAA,UAAA,GAAA,SAAA,GAAA,MAAA,GAAA,CAAA,SAAA,gBAAA,GAAA,MAAA,GAAA,CAAA,QAAA,UAAA,GAAA,OAAA,eAAA,GAAA,OAAA,GAAA,CAAA,GAAA,cAAA,GAAA,CAAA,QAAA,UAAA,GAAA,OAAA,iBAAA,GAAA,SAAA,UAAA,GAAA,CAAA,QAAA,UAAA,GAAA,OAAA,eAAA,GAAA,SAAA,UAAA,GAAA,CAAA,SAAA,WAAA,GAAA,MAAA,GAAA,CAAA,GAAA,SAAA,GAAA,CAAA,GAAA,SAAA,aAAA,GAAA,CAAA,GAAA,YAAA,GAAA,CAAA,GAAA,SAAA,eAAA,GAAA,CAAA,GAAA,mBAAA,GAAA,CAAA,GAAA,iBAAA,GAAA,CAAA,GAAA,WAAA,OAAA,GAAA,CAAA,GAAA,iBAAA,GAAA,CAAA,GAAA,gBAAA,GAAA,WAAA,GAAA,CAAA,GAAA,cAAA,GAAA,CAAA,GAAA,eAAA,GAAA,CAAA,GAAA,cAAA,GAAA,CAAA,GAAA,UAAA,GAAA,CAAA,GAAA,YAAA,GAAA,CAAA,OAAA,QAAA,GAAA,CAAA,QAAA,QAAA,MAAA,UAAA,mBAAA,UAAA,GAAA,gBAAA,GAAA,UAAA,GAAA,CAAA,SAAA,oBAAA,GAAA,MAAA,GAAA,CAAA,OAAA,KAAA,GAAA,CAAA,QAAA,QAAA,MAAA,OAAA,mBAAA,OAAA,GAAA,gBAAA,GAAA,UAAA,GAAA,CAAA,OAAA,OAAA,GAAA,CAAA,QAAA,SAAA,MAAA,SAAA,mBAAA,SAAA,GAAA,gBAAA,GAAA,UAAA,GAAA,CAAA,OAAA,aAAA,GAAA,CAAA,QAAA,OAAA,MAAA,eAAA,mBAAA,eAAA,GAAA,gBAAA,GAAA,UAAA,GAAA,CAAA,OAAA,eAAA,GAAA,CAAA,QAAA,QAAA,MAAA,iBAAA,mBAAA,iBAAA,GAAA,gBAAA,GAAA,UAAA,GAAA,CAAA,OAAA,eAAA,GAAA,CAAA,QAAA,QAAA,MAAA,iBAAA,mBAAA,iBAAA,GAAA,gBAAA,GAAA,UAAA,GAAA,CAAA,OAAA,kBAAA,GAAA,CAAA,QAAA,QAAA,MAAA,oBAAA,mBAAA,oBAAA,GAAA,gBAAA,GAAA,UAAA,GAAA,CAAA,OAAA,aAAA,GAAA,CAAA,MAAA,eAAA,mBAAA,eAAA,QAAA,KAAA,eAAA,uDAAA,GAAA,gBAAA,GAAA,UAAA,GAAA,CAAA,OAAA,WAAA,GAAA,CAAA,QAAA,QAAA,MAAA,aAAA,mBAAA,aAAA,YAAA,IAAA,GAAA,cAAA,GAAA,CAAA,GAAA,WAAA,GAAA,CAAA,OAAA,YAAA,GAAA,CAAA,QAAA,QAAA,MAAA,cAAA,mBAAA,cAAA,YAAA,IAAA,GAAA,cAAA,GAAA,CAAA,OAAA,YAAA,GAAA,CAAA,GAAA,aAAA,GAAA,CAAA,QAAA,UAAA,MAAA,cAAA,mBAAA,cAAA,YAAA,IAAA,GAAA,cAAA,GAAA,CAAA,GAAA,cAAA,GAAA,CAAA,OAAA,uBAAA,GAAA,CAAA,QAAA,UAAA,MAAA,yBAAA,mBAAA,yBAAA,OAAA,KAAA,GAAA,gBAAA,GAAA,UAAA,GAAA,CAAA,OAAA,oBAAA,GAAA,CAAA,QAAA,UAAA,MAAA,sBAAA,mBAAA,sBAAA,OAAA,KAAA,QAAA,QAAA,GAAA,gBAAA,GAAA,UAAA,GAAA,CAAA,QAAA,UAAA,SAAA,0CAAA,GAAA,SAAA,GAAA,MAAA,GAAA,CAAA,iBAAA,UAAA,GAAA,CAAA,SAAA,gBAAA,GAAA,iBAAA,GAAA,SAAA,SAAA,GAAA,CAAA,SAAA,gBAAA,GAAA,MAAA,GAAA,CAAA,SAAA,gBAAA,GAAA,MAAA,GAAA,CAAA,GAAA,kBAAA,GAAA,CAAA,QAAA,UAAA,GAAA,OAAA,UAAA,uBAAA,WAAA,GAAA,OAAA,GAAA,CAAA,GAAA,gBAAA,GAAA,eAAA,GAAA,CAAA,SAAA,mBAAA,GAAA,MAAA,GAAA,CAAA,SAAA,gBAAA,GAAA,MAAA,GAAA,CAAA,GAAA,iBAAA,GAAA,CAAA,GAAA,iBAAA,GAAA,CAAA,SAAA,gBAAA,GAAA,MAAA,GAAA,CAAA,SAAA,cAAA,GAAA,MAAA,GAAA,CAAA,SAAA,4BAAA,GAAA,MAAA,GAAA,CAAA,SAAA,mBAAA,GAAA,MAAA,GAAA,CAAA,GAAA,cAAA,GAAA,CAAA,GAAA,YAAA,GAAA,CAAA,GAAA,SAAA,iBAAA,MAAA,GAAA,CAAA,GAAA,iBAAA,GAAA,CAAA,QAAA,UAAA,GAAA,OAAA,UAAA,yBAAA,GAAA,OAAA,GAAA,CAAA,QAAA,UAAA,GAAA,OAAA,UAAA,sBAAA,GAAA,OAAA,GAAA,CAAA,GAAA,cAAA,GAAA,CAAA,GAAA,KAAA,GAAA,CAAA,GAAA,aAAA,MAAA,GAAA,CAAA,GAAA,YAAA,GAAA,CAAA,QAAA,QAAA,mBAAA,OAAA,eAAA,2BAAA,GAAA,cAAA,GAAA,CAAA,GAAA,YAAA,MAAA,GAAA,CAAA,QAAA,QAAA,mBAAA,SAAA,eAAA,SAAA,GAAA,cAAA,GAAA,CAAA,QAAA,QAAA,mBAAA,cAAA,eAAA,QAAA,aAAA,KAAA,GAAA,cAAA,GAAA,CAAA,QAAA,QAAA,mBAAA,QAAA,SAAA,WAAA,YAAA,IAAA,GAAA,cAAA,GAAA,CAAA,GAAA,YAAA,GAAA,CAAA,QAAA,YAAA,mBAAA,iBAAA,GAAA,oBAAA,GAAA,UAAA,IAAA,GAAA,CAAA,GAAA,oBAAA,GAAA,KAAA,GAAA,CAAA,QAAA,UAAA,GAAA,OAAA,UAAA,eAAA,GAAA,OAAA,GAAA,CAAA,GAAA,cAAA,GAAA,CAAA,GAAA,YAAA,GAAA,CAAA,QAAA,UAAA,SAAA,2BAAA,GAAA,SAAA,GAAA,MAAA,GAAA,CAAA,QAAA,UAAA,GAAA,OAAA,uBAAA,GAAA,OAAA,GAAA,CAAA,GAAA,WAAA,GAAA,CAAA,GAAA,WAAA,GAAA,CAAA,GAAA,SAAA,GAAA,SAAA,CAAA,GAAA,UAAA,SAAA,0BAAA,IAAA,KAAA;AAAA,QAAA,KAAA,GAAA;AChB7B,MAAA,yBAAA,GAAA,OAAA,CAAA,EAA+B,GAAA,OAAA,CAAA,EAED,GAAA,OAAA,CAAA,EACE,GAAA,OAAA,CAAA;AAGxB,MAAA,qBAAA,GAAA,iCAAA,GAAA,GAAA,OAAA,CAAA;AAQA,MAAA,yBAAA,GAAA,OAAA,CAAA,EAAyB,GAAA,MAAA,CAAA,EACG,GAAA,QAAA,CAAA;AACC,MAAA,iBAAA,GAAA,WAAA;AAAE,MAAA,uBAAA;AAC3B,MAAA,iBAAA,GAAA,cAAA;AACF,MAAA,uBAAA;AACA,MAAA,yBAAA,IAAA,KAAA,CAAA;AAA4B,MAAA,iBAAA,IAAA,4DAAA;AAAuD,MAAA,uBAAA,EAAI,EACnF;AAER,MAAA,yBAAA,IAAA,OAAA,CAAA;AACE,MAAA,qBAAA,IAAA,kCAAA,GAAA,GAAA,OAAA,EAAA;AA0BF,MAAA,uBAAA,EAAM,EACF;AAIR,MAAA,qBAAA,IAAA,kCAAA,GAAA,GAAA,OAAA,EAAA,EAA+C,IAAA,kCAAA,GAAA,GAAA,OAAA,EAAA,EAKW,IAAA,kCAAA,GAAA,GAAA,OAAA,EAAA,EAMP,IAAA,kCAAA,KAAA,IAAA,OAAA,EAAA;AA8ZrD,MAAA,uBAAA;;;AAzdmC,MAAA,oBAAA,CAAA;AAAA,MAAA,qBAAA,QAAA,IAAA,gBAAA,CAAA;AAiBrB,MAAA,oBAAA,CAAA;AAAA,MAAA,qBAAA,QAAA,CAAA,IAAA,UAAA,KAAA,IAAA,gBAAA,CAAA;AA+BN,MAAA,oBAAA;AAAA,MAAA,qBAAA,QAAA,IAAA,MAAA,CAAA;AAKA,MAAA,oBAAA;AAAA,MAAA,qBAAA,QAAA,IAAA,eAAA,CAAA;AAMA,MAAA,oBAAA;AAAA,MAAA,qBAAA,QAAA,IAAA,UAAA,CAAA;AAQA,MAAA,oBAAA;AAAA,MAAA,qBAAA,QAAA,CAAA,IAAA,UAAA,KAAA,IAAA,gBAAA,CAAA;;oBD7DI,cAAY,SAAA,SAAA,MAAE,qBAAmB,oBAAA,sBAAA,qBAAA,8BAAA,iBAAA,sBAAA,oBAAA,cAAA,oBAAA,iBAAA,eAAA,aAAA,GAAA,QAAA,CAAA,+rZAAA,EAAA,CAAA;;;sEAIhC,kBAAgB,CAAA;UAP5B;uBACW,eAAa,YACX,MAAI,SACP,CAAC,cAAc,mBAAmB,GAAC,UAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;GAAA,QAAA,CAAA,siWAAA,EAAA,CAAA;;;;6EAIjC,kBAAgB,EAAA,WAAA,oBAAA,UAAA,mDAAA,YAAA,GAAA,CAAA;AAAA,GAAA;", "names": []}
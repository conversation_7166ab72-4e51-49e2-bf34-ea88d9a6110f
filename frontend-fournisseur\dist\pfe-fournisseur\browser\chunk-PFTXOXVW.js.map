{"version": 3, "sources": ["src/app/notifications/notifications.component.ts"], "sourcesContent": ["import { Component, OnInit, OnD<PERSON>roy } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { Subscription } from 'rxjs';\nimport { NotificationService, NotificationDto } from '../services/notification.service';\nimport { AuthService } from '../services/auth.service';\n\n@Component({\n  selector: 'app-notifications',\n  standalone: true,\n  imports: [CommonModule],\n  template: `\n    <div class=\"notifications-container\">\n      <div class=\"notifications-header\">\n        <h2>\n          <svg width=\"24\" height=\"24\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" stroke-width=\"2\">\n            <path d=\"M18 8A6 6 0 0 0 6 8c0 7-3 9-3 9h18s-3-2-3-9\"></path>\n            <path d=\"M13.73 21a2 2 0 0 1-3.46 0\"></path>\n          </svg>\n          Mes Notifications\n        </h2>\n        <div class=\"header-actions\">\n          <button\n            class=\"primary-btn\"\n            *ngIf=\"unreadCount > 0\"\n            (click)=\"markAllAsRead()\">\n            <svg width=\"16\" height=\"16\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" stroke-width=\"2\">\n              <polyline points=\"9,11 12,14 22,4\"></polyline>\n              <path d=\"M21,12v7a2,2 0 0,1 -2,2H5a2,2 0 0,1 -2,-2V5a2,2 0 0,1 2,-2h11\"></path>\n            </svg>\n            Tout marquer comme lu ({{ unreadCount }})\n          </button>\n        </div>\n      </div>\n\n      <div class=\"notifications-stats\" *ngIf=\"notifications.length > 0\">\n        <div class=\"stats-chips\">\n          <div class=\"stat-chip\">\n            <svg width=\"16\" height=\"16\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" stroke-width=\"2\">\n              <path d=\"M18 8A6 6 0 0 0 6 8c0 7-3 9-3 9h18s-3-2-3-9\"></path>\n              <path d=\"M13.73 21a2 2 0 0 1-3.46 0\"></path>\n            </svg>\n            {{ notifications.length }} notification(s)\n          </div>\n          <div *ngIf=\"unreadCount > 0\" class=\"stat-chip warn\">\n            <svg width=\"16\" height=\"16\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" stroke-width=\"2\">\n              <circle cx=\"12\" cy=\"12\" r=\"10\"></circle>\n            </svg>\n            {{ unreadCount }} non lue(s)\n          </div>\n        </div>\n      </div>\n\n      <div class=\"notifications-content\">\n        <div *ngIf=\"loading\" class=\"loading-container\">\n          <div class=\"spinner\"></div>\n          <p>Chargement des notifications...</p>\n        </div>\n\n        <div *ngIf=\"!loading && notifications.length === 0\" class=\"empty-state\">\n          <svg class=\"empty-icon\" width=\"64\" height=\"64\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" stroke-width=\"2\">\n            <path d=\"M20 21v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2\"></path>\n            <circle cx=\"12\" cy=\"7\" r=\"4\"></circle>\n          </svg>\n          <h3>Aucune notification</h3>\n          <p>Vous n'avez aucune notification pour le moment.</p>\n          <p class=\"empty-subtitle\">Les notifications apparaîtront ici lorsque :</p>\n          <ul class=\"empty-list\">\n            <li>• Un client laisse un avis sur vos produits</li>\n            <li>• Une nouvelle commande est passée</li>\n            <li>• Un paiement est confirmé</li>\n            <li>• Des mises à jour importantes sont disponibles</li>\n          </ul>\n        </div>\n\n        <div *ngIf=\"!loading && notifications.length > 0\" class=\"notifications-list\">\n          <div\n            *ngFor=\"let notification of notifications; trackBy: trackByNotificationId\"\n            class=\"notification-card\"\n            [class.unread]=\"!notification.estLue\">\n\n            <div class=\"notification-header\">\n              <div class=\"notification-status\">\n                <svg\n                  *ngIf=\"!notification.estLue\"\n                  class=\"unread-indicator\"\n                  width=\"12\" height=\"12\" viewBox=\"0 0 24 24\" fill=\"currentColor\">\n                  <circle cx=\"12\" cy=\"12\" r=\"10\"></circle>\n                </svg>\n                <svg\n                  *ngIf=\"notification.estLue\"\n                  class=\"read-indicator\"\n                  width=\"16\" height=\"16\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" stroke-width=\"2\">\n                  <path d=\"M22 11.08V12a10 10 0 1 1-5.93-9.14\"></path>\n                  <polyline points=\"22,4 12,14.01 9,11.01\"></polyline>\n                </svg>\n              </div>\n\n              <div class=\"notification-content\">\n                {{ notification.contenu }}\n              </div>\n\n              <div class=\"notification-actions\">\n                <button\n                  *ngIf=\"!notification.estLue\"\n                  (click)=\"markAsRead(notification)\"\n                  class=\"action-btn primary\"\n                  title=\"Marquer comme lu\">\n                  <svg width=\"16\" height=\"16\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" stroke-width=\"2\">\n                    <polyline points=\"20,6 9,17 4,12\"></polyline>\n                  </svg>\n                </button>\n\n                <button\n                  (click)=\"deleteNotification(notification.id)\"\n                  class=\"action-btn delete\"\n                  title=\"Supprimer\">\n                  <svg width=\"16\" height=\"16\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" stroke-width=\"2\">\n                    <polyline points=\"3,6 5,6 21,6\"></polyline>\n                    <path d=\"M19,6v14a2,2 0 0,1 -2,2H7a2,2 0 0,1 -2,-2V6m3,0V4a2,2 0 0,1 2,-2h4a2,2 0 0,1 2,2v2\"></path>\n                  </svg>\n                </button>\n              </div>\n            </div>\n\n            <div class=\"notification-meta\">\n              <span class=\"notification-date\">\n                <svg width=\"14\" height=\"14\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" stroke-width=\"2\">\n                  <circle cx=\"12\" cy=\"12\" r=\"10\"></circle>\n                  <polyline points=\"12,6 12,12 16,14\"></polyline>\n                </svg>\n                {{ formatDate(notification.dateEnvoi) }}\n              </span>\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n  `,\n  styles: [`\n    .notifications-container {\n      max-width: 900px;\n      margin: 0 auto;\n      padding: 24px;\n    }\n\n    .notifications-header {\n      display: flex;\n      justify-content: space-between;\n      align-items: center;\n      margin-bottom: 24px;\n      padding: 20px;\n      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n      border-radius: 12px;\n      color: white;\n    }\n\n    .notifications-header h2 {\n      margin: 0;\n      display: flex;\n      align-items: center;\n      gap: 12px;\n      font-size: 24px;\n      font-weight: 500;\n    }\n\n    .primary-btn {\n      background: rgba(255, 255, 255, 0.2);\n      border: 1px solid rgba(255, 255, 255, 0.3);\n      color: white;\n      padding: 8px 16px;\n      border-radius: 6px;\n      cursor: pointer;\n      display: flex;\n      align-items: center;\n      gap: 8px;\n      font-size: 14px;\n      transition: all 0.2s;\n    }\n\n    .primary-btn:hover {\n      background: rgba(255, 255, 255, 0.3);\n    }\n\n    .notifications-stats {\n      margin-bottom: 24px;\n    }\n\n    .stats-chips {\n      display: flex;\n      gap: 12px;\n      flex-wrap: wrap;\n    }\n\n    .stat-chip {\n      display: flex;\n      align-items: center;\n      gap: 8px;\n      padding: 8px 16px;\n      background: #f5f5f5;\n      border-radius: 20px;\n      font-size: 14px;\n      color: #333;\n    }\n\n    .stat-chip.warn {\n      background: #ffebee;\n      color: #c62828;\n    }\n\n    .loading-container {\n      text-align: center;\n      padding: 48px;\n    }\n\n    .spinner {\n      width: 40px;\n      height: 40px;\n      border: 4px solid #f3f3f3;\n      border-top: 4px solid #667eea;\n      border-radius: 50%;\n      animation: spin 1s linear infinite;\n      margin: 0 auto 16px;\n    }\n\n    @keyframes spin {\n      0% { transform: rotate(0deg); }\n      100% { transform: rotate(360deg); }\n    }\n\n    .empty-state {\n      text-align: center;\n      padding: 48px;\n      color: #666;\n    }\n\n    .empty-icon {\n      margin-bottom: 16px;\n      color: #ccc;\n    }\n\n    .empty-subtitle {\n      margin-top: 24px;\n      font-weight: 500;\n      color: #333;\n    }\n\n    .empty-list {\n      text-align: left;\n      display: inline-block;\n      margin-top: 16px;\n      list-style: none;\n      padding: 0;\n    }\n\n    .empty-list li {\n      margin-bottom: 8px;\n      color: #666;\n    }\n\n    .notifications-list {\n      display: flex;\n      flex-direction: column;\n      gap: 16px;\n    }\n\n    .notification-card {\n      background: white;\n      border-radius: 12px;\n      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\n      transition: all 0.3s ease;\n      border-left: 4px solid transparent;\n      overflow: hidden;\n    }\n\n    .notification-card.unread {\n      border-left-color: #2196f3;\n      background-color: #f8fbff;\n      box-shadow: 0 2px 8px rgba(33, 150, 243, 0.1);\n    }\n\n    .notification-card:hover {\n      box-shadow: 0 4px 16px rgba(0,0,0,0.12);\n      transform: translateY(-2px);\n    }\n\n    .notification-header {\n      display: flex;\n      align-items: flex-start;\n      gap: 12px;\n      padding: 16px;\n    }\n\n    .notification-status {\n      flex-shrink: 0;\n      padding-top: 4px;\n    }\n\n    .unread-indicator {\n      color: #2196f3;\n    }\n\n    .read-indicator {\n      color: #4caf50;\n    }\n\n    .notification-content {\n      flex: 1;\n      font-size: 16px;\n      line-height: 1.5;\n      margin: 0;\n      color: #333;\n    }\n\n    .notification-actions {\n      display: flex;\n      gap: 4px;\n      flex-shrink: 0;\n    }\n\n    .action-btn {\n      background: none;\n      border: none;\n      cursor: pointer;\n      padding: 8px;\n      border-radius: 4px;\n      transition: all 0.2s;\n      display: flex;\n      align-items: center;\n      justify-content: center;\n    }\n\n    .action-btn.primary {\n      color: #2196f3;\n    }\n\n    .action-btn.primary:hover {\n      background: rgba(33, 150, 243, 0.1);\n    }\n\n    .action-btn.delete {\n      color: #f44336;\n    }\n\n    .action-btn.delete:hover {\n      background: rgba(244, 67, 54, 0.1);\n    }\n\n    .notification-meta {\n      display: flex;\n      align-items: center;\n      gap: 16px;\n      margin-top: 8px;\n      padding: 0 16px 16px;\n    }\n\n    .notification-date {\n      display: flex;\n      align-items: center;\n      gap: 4px;\n      font-size: 14px;\n      color: #666;\n    }\n\n    @media (max-width: 768px) {\n      .notifications-container {\n        padding: 16px;\n      }\n\n      .notifications-header {\n        flex-direction: column;\n        align-items: flex-start;\n        gap: 16px;\n        text-align: center;\n      }\n\n      .notifications-header h2 {\n        font-size: 20px;\n      }\n\n      .notification-header {\n        flex-direction: column;\n        align-items: flex-start;\n        gap: 8px;\n      }\n\n      .notification-actions {\n        align-self: flex-end;\n      }\n\n      .stats-chips {\n        justify-content: center;\n      }\n    }\n  `]\n})\nexport class NotificationsComponent implements OnInit, OnDestroy {\n  notifications: NotificationDto[] = [];\n  unreadCount = 0;\n  loading = true;\n  private subscriptions = new Subscription();\n\n  constructor(\n    private notificationService: NotificationService,\n    private authService: AuthService\n  ) {}\n\n  ngOnInit() {\n    this.subscriptions.add(\n      this.notificationService.notifications$.subscribe(notifications => {\n        this.notifications = notifications;\n        this.loading = false;\n      })\n    );\n\n    this.subscriptions.add(\n      this.notificationService.unreadCount$.subscribe(count => {\n        this.unreadCount = count;\n      })\n    );\n\n    this.loadNotifications();\n  }\n\n  ngOnDestroy() {\n    this.subscriptions.unsubscribe();\n  }\n\n  loadNotifications() {\n    const currentUser = this.authService.getCurrentUser();\n    if (currentUser?.id) {\n      this.notificationService.getUserNotifications(currentUser.id).subscribe({\n        error: () => this.loading = false\n      });\n    } else {\n      this.loading = false;\n    }\n  }\n\n  markAsRead(notification: NotificationDto) {\n    this.notificationService.markAsRead(notification.id).subscribe();\n  }\n\n  markAllAsRead() {\n    const unreadNotifications = this.notifications.filter(n => !n.estLue);\n    unreadNotifications.forEach(notification => {\n      this.notificationService.markAsRead(notification.id).subscribe();\n    });\n  }\n\n  deleteNotification(notificationId: number) {\n    this.notificationService.deleteNotification(notificationId).subscribe();\n  }\n\n  trackByNotificationId(index: number, notification: NotificationDto): number {\n    return notification.id;\n  }\n\n  formatDate(date: Date): string {\n    const now = new Date();\n    const notifDate = new Date(date);\n    const diffInMinutes = Math.floor((now.getTime() - notifDate.getTime()) / (1000 * 60));\n\n    if (diffInMinutes < 1) return 'À l\\'instant';\n    if (diffInMinutes < 60) return `Il y a ${diffInMinutes} minute${diffInMinutes > 1 ? 's' : ''}`;\n    \n    const diffInHours = Math.floor(diffInMinutes / 60);\n    if (diffInHours < 24) return `Il y a ${diffInHours} heure${diffInHours > 1 ? 's' : ''}`;\n    \n    const diffInDays = Math.floor(diffInHours / 24);\n    if (diffInDays < 7) return `Il y a ${diffInDays} jour${diffInDays > 1 ? 's' : ''}`;\n    \n    if (diffInDays < 30) {\n      const diffInWeeks = Math.floor(diffInDays / 7);\n      return `Il y a ${diffInWeeks} semaine${diffInWeeks > 1 ? 's' : ''}`;\n    }\n    \n    return notifDate.toLocaleDateString('fr-FR', {\n      day: 'numeric',\n      month: 'long',\n      year: 'numeric'\n    });\n  }\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAqBU,IAAA,yBAAA,GAAA,UAAA,EAAA;AAGE,IAAA,qBAAA,SAAA,SAAA,mEAAA;AAAA,MAAA,wBAAA,GAAA;AAAA,YAAA,SAAA,wBAAA;AAAA,aAAA,sBAAS,OAAA,cAAA,CAAe;IAAA,CAAA;;AACxB,IAAA,yBAAA,GAAA,OAAA,EAAA;AACE,IAAA,oBAAA,GAAA,YAAA,EAAA,EAA8C,GAAA,QAAA,EAAA;AAEhD,IAAA,uBAAA;AACA,IAAA,iBAAA,CAAA;AACF,IAAA,uBAAA;;;;AADE,IAAA,oBAAA,CAAA;AAAA,IAAA,6BAAA,4BAAA,OAAA,aAAA,IAAA;;;;;AAcF,IAAA,yBAAA,GAAA,OAAA,EAAA;;AACE,IAAA,yBAAA,GAAA,OAAA,EAAA;AACE,IAAA,oBAAA,GAAA,UAAA,EAAA;AACF,IAAA,uBAAA;AACA,IAAA,iBAAA,CAAA;AACF,IAAA,uBAAA;;;;AADE,IAAA,oBAAA,CAAA;AAAA,IAAA,6BAAA,KAAA,OAAA,aAAA,cAAA;;;;;AAbN,IAAA,yBAAA,GAAA,OAAA,EAAA,EAAkE,GAAA,OAAA,EAAA,EACvC,GAAA,OAAA,EAAA;;AAErB,IAAA,yBAAA,GAAA,OAAA,EAAA;AACE,IAAA,oBAAA,GAAA,QAAA,CAAA,EAA6D,GAAA,QAAA,CAAA;AAE/D,IAAA,uBAAA;AACA,IAAA,iBAAA,CAAA;AACF,IAAA,uBAAA;AACA,IAAA,qBAAA,GAAA,6CAAA,GAAA,GAAA,OAAA,EAAA;AAMF,IAAA,uBAAA,EAAM;;;;AARF,IAAA,oBAAA,CAAA;AAAA,IAAA,6BAAA,KAAA,OAAA,cAAA,QAAA,mBAAA;AAEI,IAAA,oBAAA;AAAA,IAAA,qBAAA,QAAA,OAAA,cAAA,CAAA;;;;;AAUR,IAAA,yBAAA,GAAA,OAAA,EAAA;AACE,IAAA,oBAAA,GAAA,OAAA,EAAA;AACA,IAAA,yBAAA,GAAA,GAAA;AAAG,IAAA,iBAAA,GAAA,iCAAA;AAA+B,IAAA,uBAAA,EAAI;;;;;AAGxC,IAAA,yBAAA,GAAA,OAAA,EAAA;;AACE,IAAA,yBAAA,GAAA,OAAA,EAAA;AACE,IAAA,oBAAA,GAAA,QAAA,EAAA,EAA2D,GAAA,UAAA,EAAA;AAE7D,IAAA,uBAAA;;AACA,IAAA,yBAAA,GAAA,IAAA;AAAI,IAAA,iBAAA,GAAA,qBAAA;AAAmB,IAAA,uBAAA;AACvB,IAAA,yBAAA,GAAA,GAAA;AAAG,IAAA,iBAAA,GAAA,iDAAA;AAA+C,IAAA,uBAAA;AAClD,IAAA,yBAAA,GAAA,KAAA,EAAA;AAA0B,IAAA,iBAAA,GAAA,iDAAA;AAA4C,IAAA,uBAAA;AACtE,IAAA,yBAAA,IAAA,MAAA,EAAA,EAAuB,IAAA,IAAA;AACjB,IAAA,iBAAA,IAAA,kDAAA;AAA2C,IAAA,uBAAA;AAC/C,IAAA,yBAAA,IAAA,IAAA;AAAI,IAAA,iBAAA,IAAA,4CAAA;AAAkC,IAAA,uBAAA;AACtC,IAAA,yBAAA,IAAA,IAAA;AAAI,IAAA,iBAAA,IAAA,oCAAA;AAA0B,IAAA,uBAAA;AAC9B,IAAA,yBAAA,IAAA,IAAA;AAAI,IAAA,iBAAA,IAAA,yDAAA;AAA+C,IAAA,uBAAA,EAAK,EACrD;;;;;;AAWC,IAAA,yBAAA,GAAA,OAAA,EAAA;AAIE,IAAA,oBAAA,GAAA,UAAA,EAAA;AACF,IAAA,uBAAA;;;;;;AACA,IAAA,yBAAA,GAAA,OAAA,EAAA;AAIE,IAAA,oBAAA,GAAA,QAAA,EAAA,EAAoD,GAAA,YAAA,EAAA;AAEtD,IAAA,uBAAA;;;;;;AAQA,IAAA,yBAAA,GAAA,UAAA,EAAA;AAEE,IAAA,qBAAA,SAAA,SAAA,gFAAA;AAAA,MAAA,wBAAA,GAAA;AAAA,YAAA,kBAAA,wBAAA,EAAA;AAAA,YAAA,SAAA,wBAAA,CAAA;AAAA,aAAA,sBAAS,OAAA,WAAA,eAAA,CAAwB;IAAA,CAAA;;AAGjC,IAAA,yBAAA,GAAA,OAAA,EAAA;AACE,IAAA,oBAAA,GAAA,YAAA,EAAA;AACF,IAAA,uBAAA,EAAM;;;;;;AAlCd,IAAA,yBAAA,GAAA,OAAA,EAAA,EAGwC,GAAA,OAAA,EAAA,EAEL,GAAA,OAAA,EAAA;AAE7B,IAAA,qBAAA,GAAA,yDAAA,GAAA,GAAA,OAAA,EAAA,EAGiE,GAAA,yDAAA,GAAA,GAAA,OAAA,EAAA;AAUnE,IAAA,uBAAA;AAEA,IAAA,yBAAA,GAAA,OAAA,EAAA;AACE,IAAA,iBAAA,CAAA;AACF,IAAA,uBAAA;AAEA,IAAA,yBAAA,GAAA,OAAA,EAAA;AACE,IAAA,qBAAA,GAAA,uDAAA,GAAA,GAAA,UAAA,EAAA;AAUA,IAAA,yBAAA,GAAA,UAAA,EAAA;AACE,IAAA,qBAAA,SAAA,SAAA,uEAAA;AAAA,YAAA,kBAAA,wBAAA,GAAA,EAAA;AAAA,YAAA,SAAA,wBAAA,CAAA;AAAA,aAAA,sBAAS,OAAA,mBAAA,gBAAA,EAAA,CAAmC;IAAA,CAAA;;AAG5C,IAAA,yBAAA,IAAA,OAAA,EAAA;AACE,IAAA,oBAAA,IAAA,YAAA,EAAA,EAA2C,IAAA,QAAA,EAAA;AAE7C,IAAA,uBAAA,EAAM,EACC,EACL;;AAGR,IAAA,yBAAA,IAAA,OAAA,EAAA,EAA+B,IAAA,QAAA,EAAA;;AAE3B,IAAA,yBAAA,IAAA,OAAA,EAAA;AACE,IAAA,oBAAA,IAAA,UAAA,EAAA,EAAwC,IAAA,YAAA,EAAA;AAE1C,IAAA,uBAAA;AACA,IAAA,iBAAA,EAAA;AACF,IAAA,uBAAA,EAAO,EACH;;;;;AAtDN,IAAA,sBAAA,UAAA,CAAA,gBAAA,MAAA;AAKO,IAAA,oBAAA,CAAA;AAAA,IAAA,qBAAA,QAAA,CAAA,gBAAA,MAAA;AAMA,IAAA,oBAAA;AAAA,IAAA,qBAAA,QAAA,gBAAA,MAAA;AASH,IAAA,oBAAA,CAAA;AAAA,IAAA,6BAAA,KAAA,gBAAA,SAAA,GAAA;AAKG,IAAA,oBAAA,CAAA;AAAA,IAAA,qBAAA,QAAA,CAAA,gBAAA,MAAA;AA2BH,IAAA,oBAAA,EAAA;AAAA,IAAA,6BAAA,KAAA,OAAA,WAAA,gBAAA,SAAA,GAAA,GAAA;;;;;AAxDR,IAAA,yBAAA,GAAA,OAAA,EAAA;AACE,IAAA,qBAAA,GAAA,8CAAA,IAAA,GAAA,OAAA,EAAA;AA2DF,IAAA,uBAAA;;;;AA1D6B,IAAA,oBAAA;AAAA,IAAA,qBAAA,WAAA,OAAA,aAAA,EAAkB,gBAAA,OAAA,qBAAA;;;AA+TjD,IAAO,yBAAP,MAAO,wBAAsB;EAOvB;EACA;EAPV,gBAAmC,CAAA;EACnC,cAAc;EACd,UAAU;EACF,gBAAgB,IAAI,aAAY;EAExC,YACU,qBACA,aAAwB;AADxB,SAAA,sBAAA;AACA,SAAA,cAAA;EACP;EAEH,WAAQ;AACN,SAAK,cAAc,IACjB,KAAK,oBAAoB,eAAe,UAAU,mBAAgB;AAChE,WAAK,gBAAgB;AACrB,WAAK,UAAU;IACjB,CAAC,CAAC;AAGJ,SAAK,cAAc,IACjB,KAAK,oBAAoB,aAAa,UAAU,WAAQ;AACtD,WAAK,cAAc;IACrB,CAAC,CAAC;AAGJ,SAAK,kBAAiB;EACxB;EAEA,cAAW;AACT,SAAK,cAAc,YAAW;EAChC;EAEA,oBAAiB;AACf,UAAM,cAAc,KAAK,YAAY,eAAc;AACnD,QAAI,aAAa,IAAI;AACnB,WAAK,oBAAoB,qBAAqB,YAAY,EAAE,EAAE,UAAU;QACtE,OAAO,MAAM,KAAK,UAAU;OAC7B;IACH,OAAO;AACL,WAAK,UAAU;IACjB;EACF;EAEA,WAAW,cAA6B;AACtC,SAAK,oBAAoB,WAAW,aAAa,EAAE,EAAE,UAAS;EAChE;EAEA,gBAAa;AACX,UAAM,sBAAsB,KAAK,cAAc,OAAO,OAAK,CAAC,EAAE,MAAM;AACpE,wBAAoB,QAAQ,kBAAe;AACzC,WAAK,oBAAoB,WAAW,aAAa,EAAE,EAAE,UAAS;IAChE,CAAC;EACH;EAEA,mBAAmB,gBAAsB;AACvC,SAAK,oBAAoB,mBAAmB,cAAc,EAAE,UAAS;EACvE;EAEA,sBAAsB,OAAe,cAA6B;AAChE,WAAO,aAAa;EACtB;EAEA,WAAW,MAAU;AACnB,UAAM,MAAM,oBAAI,KAAI;AACpB,UAAM,YAAY,IAAI,KAAK,IAAI;AAC/B,UAAM,gBAAgB,KAAK,OAAO,IAAI,QAAO,IAAK,UAAU,QAAO,MAAO,MAAO,GAAG;AAEpF,QAAI,gBAAgB;AAAG,aAAO;AAC9B,QAAI,gBAAgB;AAAI,aAAO,UAAU,aAAa,UAAU,gBAAgB,IAAI,MAAM,EAAE;AAE5F,UAAM,cAAc,KAAK,MAAM,gBAAgB,EAAE;AACjD,QAAI,cAAc;AAAI,aAAO,UAAU,WAAW,SAAS,cAAc,IAAI,MAAM,EAAE;AAErF,UAAM,aAAa,KAAK,MAAM,cAAc,EAAE;AAC9C,QAAI,aAAa;AAAG,aAAO,UAAU,UAAU,QAAQ,aAAa,IAAI,MAAM,EAAE;AAEhF,QAAI,aAAa,IAAI;AACnB,YAAM,cAAc,KAAK,MAAM,aAAa,CAAC;AAC7C,aAAO,UAAU,WAAW,WAAW,cAAc,IAAI,MAAM,EAAE;IACnE;AAEA,WAAO,UAAU,mBAAmB,SAAS;MAC3C,KAAK;MACL,OAAO;MACP,MAAM;KACP;EACH;;qCAtFW,yBAAsB,4BAAA,mBAAA,GAAA,4BAAA,WAAA,CAAA;EAAA;yEAAtB,yBAAsB,WAAA,CAAA,CAAA,mBAAA,CAAA,GAAA,OAAA,IAAA,MAAA,GAAA,QAAA,CAAA,CAAA,GAAA,yBAAA,GAAA,CAAA,GAAA,sBAAA,GAAA,CAAA,SAAA,MAAA,UAAA,MAAA,WAAA,aAAA,QAAA,QAAA,UAAA,gBAAA,gBAAA,GAAA,GAAA,CAAA,KAAA,6CAAA,GAAA,CAAA,KAAA,4BAAA,GAAA,CAAA,GAAA,gBAAA,GAAA,CAAA,SAAA,eAAA,GAAA,SAAA,GAAA,MAAA,GAAA,CAAA,SAAA,uBAAA,GAAA,MAAA,GAAA,CAAA,GAAA,uBAAA,GAAA,CAAA,SAAA,qBAAA,GAAA,MAAA,GAAA,CAAA,SAAA,eAAA,GAAA,MAAA,GAAA,CAAA,SAAA,sBAAA,GAAA,MAAA,GAAA,CAAA,GAAA,eAAA,GAAA,OAAA,GAAA,CAAA,SAAA,MAAA,UAAA,MAAA,WAAA,aAAA,QAAA,QAAA,UAAA,gBAAA,gBAAA,GAAA,GAAA,CAAA,UAAA,iBAAA,GAAA,CAAA,KAAA,+DAAA,GAAA,CAAA,GAAA,qBAAA,GAAA,CAAA,GAAA,aAAA,GAAA,CAAA,GAAA,WAAA,GAAA,CAAA,SAAA,kBAAA,GAAA,MAAA,GAAA,CAAA,GAAA,aAAA,MAAA,GAAA,CAAA,MAAA,MAAA,MAAA,MAAA,KAAA,IAAA,GAAA,CAAA,GAAA,mBAAA,GAAA,CAAA,GAAA,SAAA,GAAA,CAAA,GAAA,aAAA,GAAA,CAAA,SAAA,MAAA,UAAA,MAAA,WAAA,aAAA,QAAA,QAAA,UAAA,gBAAA,gBAAA,KAAA,GAAA,YAAA,GAAA,CAAA,KAAA,2CAAA,GAAA,CAAA,MAAA,MAAA,MAAA,KAAA,KAAA,GAAA,GAAA,CAAA,GAAA,gBAAA,GAAA,CAAA,GAAA,YAAA,GAAA,CAAA,GAAA,oBAAA,GAAA,CAAA,SAAA,qBAAA,GAAA,UAAA,GAAA,SAAA,WAAA,cAAA,GAAA,CAAA,GAAA,mBAAA,GAAA,CAAA,GAAA,qBAAA,GAAA,CAAA,GAAA,qBAAA,GAAA,CAAA,SAAA,oBAAA,SAAA,MAAA,UAAA,MAAA,WAAA,aAAA,QAAA,gBAAA,GAAA,MAAA,GAAA,CAAA,SAAA,kBAAA,SAAA,MAAA,UAAA,MAAA,WAAA,aAAA,QAAA,QAAA,UAAA,gBAAA,gBAAA,KAAA,GAAA,MAAA,GAAA,CAAA,GAAA,sBAAA,GAAA,CAAA,GAAA,sBAAA,GAAA,CAAA,SAAA,sBAAA,SAAA,oBAAA,GAAA,SAAA,GAAA,MAAA,GAAA,CAAA,SAAA,aAAA,GAAA,cAAA,UAAA,GAAA,OAAA,GAAA,CAAA,UAAA,cAAA,GAAA,CAAA,KAAA,oFAAA,GAAA,CAAA,GAAA,mBAAA,GAAA,CAAA,GAAA,mBAAA,GAAA,CAAA,SAAA,MAAA,UAAA,MAAA,WAAA,aAAA,QAAA,QAAA,UAAA,gBAAA,gBAAA,GAAA,GAAA,CAAA,UAAA,kBAAA,GAAA,CAAA,SAAA,MAAA,UAAA,MAAA,WAAA,aAAA,QAAA,gBAAA,GAAA,kBAAA,GAAA,CAAA,SAAA,MAAA,UAAA,MAAA,WAAA,aAAA,QAAA,QAAA,UAAA,gBAAA,gBAAA,KAAA,GAAA,gBAAA,GAAA,CAAA,KAAA,oCAAA,GAAA,CAAA,UAAA,uBAAA,GAAA,CAAA,SAAA,oBAAA,GAAA,cAAA,WAAA,GAAA,OAAA,GAAA,CAAA,UAAA,gBAAA,CAAA,GAAA,UAAA,SAAA,gCAAA,IAAA,KAAA;AAAA,QAAA,KAAA,GAAA;AAhY/B,MAAA,yBAAA,GAAA,OAAA,CAAA,EAAqC,GAAA,OAAA,CAAA,EACD,GAAA,IAAA;;AAE9B,MAAA,yBAAA,GAAA,OAAA,CAAA;AACE,MAAA,oBAAA,GAAA,QAAA,CAAA,EAA6D,GAAA,QAAA,CAAA;AAE/D,MAAA,uBAAA;AACA,MAAA,iBAAA,GAAA,qBAAA;AACF,MAAA,uBAAA;;AACA,MAAA,yBAAA,GAAA,OAAA,CAAA;AACE,MAAA,qBAAA,GAAA,0CAAA,GAAA,GAAA,UAAA,CAAA;AAUF,MAAA,uBAAA,EAAM;AAGR,MAAA,qBAAA,GAAA,uCAAA,GAAA,GAAA,OAAA,CAAA;AAkBA,MAAA,yBAAA,IAAA,OAAA,CAAA;AACE,MAAA,qBAAA,IAAA,wCAAA,GAAA,GAAA,OAAA,CAAA,EAA+C,IAAA,wCAAA,IAAA,GAAA,OAAA,EAAA,EAKyB,IAAA,wCAAA,GAAA,GAAA,OAAA,EAAA;AA6E1E,MAAA,uBAAA,EAAM;;;AAhHC,MAAA,oBAAA,CAAA;AAAA,MAAA,qBAAA,QAAA,IAAA,cAAA,CAAA;AAW2B,MAAA,oBAAA;AAAA,MAAA,qBAAA,QAAA,IAAA,cAAA,SAAA,CAAA;AAmB1B,MAAA,oBAAA,CAAA;AAAA,MAAA,qBAAA,QAAA,IAAA,OAAA;AAKA,MAAA,oBAAA;AAAA,MAAA,qBAAA,QAAA,CAAA,IAAA,WAAA,IAAA,cAAA,WAAA,CAAA;AAgBA,MAAA,oBAAA;AAAA,MAAA,qBAAA,QAAA,CAAA,IAAA,WAAA,IAAA,cAAA,SAAA,CAAA;;oBAjEF,cAAY,SAAA,IAAA,GAAA,QAAA,CAAA,+8JAAA,EAAA,CAAA;;;sEAkYX,wBAAsB,CAAA;UArYlC;uBACW,qBAAmB,YACjB,MAAI,SACP,CAAC,YAAY,GAAC,UACb;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;KA+HT,QAAA,CAAA,k1IAAA,EAAA,CAAA;;;;6EAkQU,wBAAsB,EAAA,WAAA,0BAAA,UAAA,oDAAA,YAAA,IAAA,CAAA;AAAA,GAAA;", "names": []}
import {
  CommonModule,
  Component,
  setClassMetadata,
  ɵsetClassDebugInfo,
  ɵɵdefineComponent,
  ɵɵelementEnd,
  ɵɵelementStart,
  ɵɵtext
} from "./chunk-UBZQS7JS.js";

// src/app/components/admin/business/commission-rules/commission-rules.component.ts
var CommissionRulesComponent = class _CommissionRulesComponent {
  static \u0275fac = function CommissionRulesComponent_Factory(__ngFactoryType__) {
    return new (__ngFactoryType__ || _CommissionRulesComponent)();
  };
  static \u0275cmp = /* @__PURE__ */ \u0275\u0275defineComponent({ type: _CommissionRulesComponent, selectors: [["app-commission-rules"]], decls: 5, vars: 0, template: function CommissionRulesComponent_Template(rf, ctx) {
    if (rf & 1) {
      \u0275\u0275elementStart(0, "div")(1, "h1");
      \u0275\u0275text(2, "\u{1F4B0} R\xE8gles de commission");
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(3, "p");
      \u0275\u0275text(4, "En d\xE9veloppement...");
      \u0275\u0275elementEnd()();
    }
  }, dependencies: [CommonModule], encapsulation: 2 });
};
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && setClassMetadata(CommissionRulesComponent, [{
    type: Component,
    args: [{
      selector: "app-commission-rules",
      standalone: true,
      imports: [CommonModule],
      template: `<div><h1>\u{1F4B0} R\xE8gles de commission</h1><p>En d\xE9veloppement...</p></div>`
    }]
  }], null, null);
})();
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && \u0275setClassDebugInfo(CommissionRulesComponent, { className: "CommissionRulesComponent", filePath: "src/app/components/admin/business/commission-rules/commission-rules.component.ts", lineNumber: 10 });
})();
export {
  CommissionRulesComponent
};
//# sourceMappingURL=chunk-7PVY5IDH.js.map

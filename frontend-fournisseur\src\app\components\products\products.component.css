.products-page {
  padding: 24px;
  max-width: 1400px;
  margin: 0 auto;
}

/* Header */
.page-header {
  margin-bottom: 32px;
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
}

.header-content h1 {
  margin: 0;
  font-size: 28px;
  font-weight: 600;
  color: #111827;
}

/* Statistiques */
.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 20px;
  margin-bottom: 24px;
}

.stat-card {
  background: white;
  border-radius: 12px;
  padding: 20px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  display: flex;
  align-items: center;
  gap: 16px;
}

.stat-icon {
  width: 48px;
  height: 48px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 20px;
}

.stat-icon.total { background: #dbeafe; }
.stat-icon.active { background: #dcfce7; }
.stat-icon.warning { background: #fef3c7; }
.stat-icon.value { background: #f3e8ff; }

.stat-number {
  font-size: 24px;
  font-weight: 700;
  color: #111827;
  margin-bottom: 4px;
}

.stat-label {
  font-size: 14px;
  color: #6b7280;
}

/* Filtres */
.filters-section {
  background: white;
  border-radius: 12px;
  padding: 24px;
  margin-bottom: 24px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.search-bar {
  display: flex;
  gap: 12px;
  margin-bottom: 20px;
}

.search-input {
  flex: 1;
  padding: 12px 16px;
  border: 1px solid #d1d5db;
  border-radius: 8px;
  font-size: 14px;
}

.search-btn {
  padding: 12px 16px;
  background: #3b82f6;
  color: white;
  border: none;
  border-radius: 8px;
  cursor: pointer;
}

.filters {
  display: flex;
  gap: 16px;
  align-items: center;
  flex-wrap: wrap;
}

.filter-select {
  padding: 8px 12px;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  font-size: 14px;
  min-width: 150px;
}

/* Boutons */
.btn {
  padding: 8px 16px;
  border: none;
  border-radius: 6px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s;
  display: inline-flex;
  align-items: center;
  gap: 8px;
}

.btn-primary {
  background: #3b82f6;
  color: white;
}

.btn-primary:hover {
  background: #2563eb;
}

.btn-secondary {
  background: #6b7280;
  color: white;
}

.btn-secondary:hover {
  background: #4b5563;
}

.btn-danger {
  background: #ef4444;
  color: white;
}

.btn-danger:hover {
  background: #dc2626;
}

.btn-sm {
  padding: 6px 12px;
  font-size: 12px;
}

/* Loading et erreurs */
.loading {
  text-align: center;
  padding: 60px 20px;
}

.spinner {
  width: 40px;
  height: 40px;
  border: 4px solid #f3f4f6;
  border-top: 4px solid #3b82f6;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin: 0 auto 16px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.error-message {
  background: #fef2f2;
  color: #dc2626;
  padding: 16px;
  border-radius: 8px;
  margin-bottom: 24px;
}

/* En-têtes de tri */
.products-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.sort-controls {
  display: flex;
  gap: 12px;
}

.sort-btn {
  padding: 8px 12px;
  background: white;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  cursor: pointer;
  font-size: 14px;
  display: flex;
  align-items: center;
  gap: 4px;
}

.sort-btn.active {
  background: #3b82f6;
  color: white;
  border-color: #3b82f6;
}

.sort-arrow {
  font-size: 12px;
}

.results-count {
  font-size: 14px;
  color: #6b7280;
}

/* Grille des produits */
.products-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
  gap: 20px;
  margin-bottom: 32px;
}

.product-card {
  background: white;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  transition: transform 0.2s, box-shadow 0.2s;
  display: flex;
  flex-direction: column;
}

.product-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.product-content {
  padding: 12px;
  flex: 1;
  display: flex;
  flex-direction: column;
}

.product-image {
  position: relative;
  height: 120px;
  overflow: hidden;
  border-radius: 8px;
  margin-bottom: 12px;
}

.product-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.3s ease;
  border-radius: 8px;
}

.product-card:hover .product-image img {
  transform: scale(1.05);
}

.product-image .promotion-badge {
  position: absolute;
  top: 12px;
  left: 12px;
  background: #ef4444;
  color: white;
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 600;
  z-index: 2;
}

.product-status {
  position: absolute;
  top: 12px;
  right: 12px;
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 500;
}

.status-active {
  background: #dcfce7;
  color: #166534;
}

.status-out-of-stock {
  background: #fef2f2;
  color: #dc2626;
}

.status-inactive {
  background: #f3f4f6;
  color: #6b7280;
}

.product-info {
  padding: 20px;
}

.product-name {
  font-size: 16px;
  font-weight: 600;
  color: #111827;
  margin: 0 0 6px 0;
  line-height: 1.3;
}

.product-description {
  font-size: 13px;
  color: #6b7280;
  margin: 0 0 8px 0;
  line-height: 1.4;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.product-category {
  display: inline-block;
  background: #f3f4f6;
  color: #374151;
  padding: 3px 6px;
  border-radius: 4px;
  font-size: 11px;
  margin-bottom: 10px;
}

.product-details {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.product-price {
  font-size: 18px;
  font-weight: 700;
  color: #059669;
}

.product-stock {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 14px;
}

.stock-input {
  width: 60px;
  padding: 4px 8px;
  border: 1px solid #d1d5db;
  border-radius: 4px;
  text-align: center;
}

.product-actions {
  display: flex;
  gap: 8px;
}

/* Message aucun produit */
.no-products {
  text-align: center;
  padding: 60px 20px;
}

.no-products-icon {
  font-size: 64px;
  margin-bottom: 16px;
}

.no-products h3 {
  font-size: 20px;
  color: #111827;
  margin-bottom: 8px;
}

.no-products p {
  color: #6b7280;
  margin-bottom: 24px;
}

/* Pagination */
.pagination {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 8px;
  margin-top: 32px;
}

.pagination-btn {
  padding: 8px 16px;
  background: white;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  cursor: pointer;
  font-size: 14px;
}

.pagination-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.pagination-pages {
  display: flex;
  gap: 4px;
}

.pagination-page {
  width: 36px;
  height: 36px;
  background: white;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  cursor: pointer;
  font-size: 14px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.pagination-page.active {
  background: #3b82f6;
  color: white;
  border-color: #3b82f6;
}

/* Modal */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.6);
  backdrop-filter: blur(4px);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  padding: 2rem;
  animation: fadeIn 0.3s ease-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

.modal-content {
  background: var(--white);
  border-radius: var(--border-radius-2xl);
  max-width: 98vw;
  max-height: 98vh;
  overflow-y: auto;
  box-shadow: var(--shadow-2xl);
  border: 1px solid var(--gray-200);
  animation: slideIn 0.3s ease-out;
}

@keyframes slideIn {
  from {
    transform: translateY(-2rem) scale(0.95);
    opacity: 0;
  }
  to {
    transform: translateY(0) scale(1);
    opacity: 1;
  }
}

/* Responsive */
@media (max-width: 768px) {
  .products-page {
    padding: 16px;
  }
  
  .header-content {
    flex-direction: column;
    gap: 16px;
    align-items: stretch;
  }
  
  .stats-grid {
    grid-template-columns: 1fr;
  }
  
  .filters {
    flex-direction: column;
    align-items: stretch;
  }
  
  .products-grid {
    grid-template-columns: 1fr;
  }
  
  .sort-controls {
    flex-wrap: wrap;
  }
  
  .products-header {
    flex-direction: column;
    gap: 12px;
    align-items: stretch;
  }
}

/* Formulaire de produit */
.product-form {
  padding: 3rem;
  max-width: 1000px;
  width: 100%;
  background: var(--white);
  border-radius: var(--border-radius-2xl);
  min-height: 80vh;
}

.product-form h3 {
  margin-bottom: 2rem;
  color: var(--gray-900);
  font-size: 1.75rem;
  font-weight: var(--font-weight-bold);
  text-align: center;
  padding-bottom: 1rem;
  border-bottom: 2px solid var(--gray-200);
}

.form-row {
  display: flex;
  gap: 2.5rem;
  margin-bottom: 2.5rem;
}

.form-group {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.form-group label {
  display: block;
  margin-bottom: 1rem;
  font-weight: var(--font-weight-semibold);
  color: var(--gray-700);
  font-size: 1rem;
  letter-spacing: 0.025em;
}

.form-control {
  width: 100%;
  padding: 1.25rem;
  border: 2px solid var(--gray-300);
  border-radius: var(--border-radius-lg);
  font-size: 1.1rem;
  transition: all var(--transition-base);
  box-sizing: border-box;
  background: var(--white);
  color: var(--gray-900);
  min-height: 3.5rem;
  text-align: center;
  font-weight: var(--font-weight-medium);
  box-shadow: inset 0 1px 3px rgba(0, 0, 0, 0.08);
}

.form-control:focus {
  outline: none;
  border-color: var(--primary-500);
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.15), inset 0 2px 4px rgba(0, 0, 0, 0.05);
  background: var(--primary-50);
  transform: scale(1.02);
}

.form-control:invalid {
  border-color: var(--error-500);
  box-shadow: 0 0 0 3px rgba(239, 68, 68, 0.15);
}

.form-control::placeholder {
  color: var(--gray-400);
  font-style: italic;
  text-align: center;
  font-weight: var(--font-weight-normal);
  opacity: 0.8;
}

/* Textarea spécifique */
textarea.form-control {
  min-height: 6rem;
  resize: vertical;
  line-height: 1.6;
  text-align: center;
  display: flex;
  align-items: center;
  justify-content: center;
}

.form-actions {
  display: flex;
  gap: 2rem;
  justify-content: center;
  margin-top: 4rem;
  padding-top: 2.5rem;
  border-top: 2px solid var(--gray-200);
}

.btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.btn-lg {
  padding: 1.25rem 2.5rem;
  font-size: 1.2rem;
  font-weight: var(--font-weight-semibold);
  min-width: 220px;
  min-height: 3.5rem;
}

/* Styles pour les sélecteurs */
select.form-control {
  cursor: pointer;
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='m6 8 4 4 4-4'/%3e%3c/svg%3e");
  background-position: right 0.75rem center;
  background-repeat: no-repeat;
  background-size: 1.5em 1.5em;
  padding-right: 3rem;
  text-align: center;
  text-align-last: center;
}

select.form-control:focus {
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%23667eea' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='m6 8 4 4 4-4'/%3e%3c/svg%3e");
}

/* Options des sélecteurs */
select.form-control option {
  text-align: center;
  padding: 0.75rem;
  font-weight: var(--font-weight-medium);
}

/* Boutons d'ajout rapide */
.quick-add-btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 2.5rem;
  height: 2.5rem;
  background: var(--primary-500);
  color: var(--white);
  border: none;
  border-radius: var(--border-radius-full);
  cursor: pointer;
  transition: all var(--transition-base);
  font-size: 1.25rem;
  margin-left: 0.75rem;
  box-shadow: var(--shadow-md);
}

.quick-add-btn:hover {
  background: var(--primary-600);
  transform: scale(1.1);
  box-shadow: var(--shadow-lg);
}

/* Groupes avec boutons d'ajout */
.form-group-with-add {
  display: flex;
  align-items: flex-end;
  gap: 0.75rem;
}

.form-group-with-add .form-group {
  flex: 1;
  margin: 0;
}

/* Checkbox styling */
.form-group input[type="checkbox"] {
  width: 1.25rem;
  height: 1.25rem;
  margin-right: 0.75rem;
  accent-color: var(--primary-500);
}

.form-group label:has(input[type="checkbox"]) {
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  font-weight: var(--font-weight-medium);
  padding: 1rem;
  background: var(--gray-50);
  border-radius: var(--border-radius-lg);
  border: 2px solid var(--gray-200);
  transition: all var(--transition-base);
  text-align: center;
  box-shadow: inset 0 1px 3px rgba(0, 0, 0, 0.08);
}

.form-group label:has(input[type="checkbox"]):hover {
  background: var(--primary-50);
  border-color: var(--primary-300);
}

/* Responsive pour le formulaire */
@media (max-width: 768px) {
  .form-row {
    flex-direction: column;
    gap: 1.5rem;
  }

  .product-form {
    padding: 1.5rem;
    max-width: 100%;
  }

  .form-actions {
    flex-direction: column;
    gap: 1rem;
  }

  .form-group-with-add {
    flex-direction: column;
    align-items: stretch;
  }

  .quick-add-btn {
    margin-left: 0;
    margin-top: 0.75rem;
    align-self: center;
  }
}

/* Styles pour les prix et remises */
.promotion-price {
  color: #059669;
  font-weight: 600;
}

.outlet-price {
  color: #dc2626;
  font-weight: 500;
}

.savings-badge {
  background: #dcfce7;
  color: #166534;
  padding: 2px 8px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 600;
  margin-right: 8px;
}

.form-text.text-muted {
  font-size: 12px;
  color: #6b7280;
  margin-top: 4px;
  display: block;
}

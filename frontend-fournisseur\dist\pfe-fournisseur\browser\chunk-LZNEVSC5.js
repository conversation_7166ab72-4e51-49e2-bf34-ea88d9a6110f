import {
  NotificationIconComponent
} from "./chunk-NEMJGOZR.js";
import "./chunk-2WHFEWR5.js";
import "./chunk-WQ24PYVH.js";
import {
  AuthService
} from "./chunk-ZSXXNL7U.js";
import {
  AdminAuthService
} from "./chunk-2RV3R4JN.js";
import {
  DomSanitizer,
  Router,
  RouterModule,
  RouterOutlet
} from "./chunk-6BVUYNW4.js";
import "./chunk-GFHHPDQ6.js";
import "./chunk-7JDDWGD3.js";
import {
  CommonModule,
  Component,
  EventEmitter,
  HostListener,
  Input,
  NgForOf,
  NgIf,
  Output,
  setClassMetadata,
  ɵsetClassDebugInfo,
  ɵɵadvance,
  ɵɵclassProp,
  ɵɵdefineComponent,
  ɵɵdirectiveInject,
  ɵɵelement,
  ɵɵelementEnd,
  ɵɵelementStart,
  ɵɵgetCurrentView,
  ɵɵlistener,
  ɵɵnamespaceHTML,
  ɵɵnamespaceSVG,
  ɵɵnextContext,
  ɵɵproperty,
  ɵɵreference,
  ɵɵresetView,
  ɵɵresolveWindow,
  ɵɵrestoreView,
  ɵɵsanitizeHtml,
  ɵɵtemplate,
  ɵɵtemplateRefExtractor,
  ɵɵtext,
  ɵɵtextInterpolate,
  ɵɵtextInterpolate1
} from "./chunk-UBZQS7JS.js";

// src/app/components/layout/header/header.component.ts
function HeaderComponent_div_10_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "div", 33);
    \u0275\u0275element(1, "app-notification-icon");
    \u0275\u0275elementEnd();
  }
}
var HeaderComponent = class _HeaderComponent {
  authService;
  adminAuthService;
  router;
  toggleSidebar = new EventEmitter();
  currentUser = null;
  showUserMenu = false;
  constructor(authService, adminAuthService, router) {
    this.authService = authService;
    this.adminAuthService = adminAuthService;
    this.router = router;
  }
  ngOnInit() {
    this.authService.currentUser$.subscribe((user) => {
      this.currentUser = user;
    });
  }
  onToggleSidebar() {
    this.toggleSidebar.emit();
  }
  toggleUserMenu() {
    this.showUserMenu = !this.showUserMenu;
  }
  closeUserMenu() {
    this.showUserMenu = false;
  }
  navigateToProfile() {
    this.closeUserMenu();
    this.router.navigate(["/dashboard/profil"]);
  }
  logout() {
    this.closeUserMenu();
    this.authService.logout();
    this.router.navigate(["/login"]);
  }
  getUserInitials() {
    if (!this.currentUser)
      return "";
    const firstInitial = this.currentUser.prenom?.charAt(0).toUpperCase() || "";
    const lastInitial = this.currentUser.nom?.charAt(0).toUpperCase() || "";
    return firstInitial + lastInitial;
  }
  getUserFullName() {
    if (!this.currentUser)
      return "";
    return `${this.currentUser.prenom} ${this.currentUser.nom}`;
  }
  get isUsingMockServices() {
    return false;
  }
  isAdmin() {
    return this.adminAuthService.getCurrentUser() !== null;
  }
  static \u0275fac = function HeaderComponent_Factory(__ngFactoryType__) {
    return new (__ngFactoryType__ || _HeaderComponent)(\u0275\u0275directiveInject(AuthService), \u0275\u0275directiveInject(AdminAuthService), \u0275\u0275directiveInject(Router));
  };
  static \u0275cmp = /* @__PURE__ */ \u0275\u0275defineComponent({ type: _HeaderComponent, selectors: [["app-header"]], outputs: { toggleSidebar: "toggleSidebar" }, decls: 50, vars: 10, consts: [[1, "header"], [1, "header-left"], ["type", "button", 1, "sidebar-toggle", 3, "click"], [1, "hamburger-line"], [1, "header-title"], [1, "header-right"], ["class", "header-notifications", 4, "ngIf"], [1, "user-menu", 3, "clickOutside"], ["type", "button", 1, "user-menu-trigger", 3, "click"], [1, "user-avatar"], [1, "user-info"], [1, "user-name"], [1, "user-role"], ["width", "16", "height", "16", "viewBox", "0 0 24 24", "fill", "none", "stroke", "currentColor", "stroke-width", "2", 1, "dropdown-arrow"], ["points", "6,9 12,15 18,9"], [1, "user-dropdown"], [1, "dropdown-header"], [1, "user-avatar-large"], [1, "user-details"], [1, "user-email"], [1, "dropdown-divider"], [1, "dropdown-menu"], ["type", "button", 1, "dropdown-item", 3, "click"], ["width", "16", "height", "16", "viewBox", "0 0 24 24", "fill", "none", "stroke", "currentColor", "stroke-width", "2"], ["d", "M20 21v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2"], ["cx", "12", "cy", "7", "r", "4"], ["type", "button", 1, "dropdown-item"], ["cx", "12", "cy", "12", "r", "3"], ["d", "M19.4 15a1.65 1.65 0 0 0 .33 1.82l.06.06a2 2 0 0 1 0 2.83 2 2 0 0 1-2.83 0l-.06-.06a1.65 1.65 0 0 0-1.82-.33 1.65 1.65 0 0 0-1 1.51V21a2 2 0 0 1-2 2 2 2 0 0 1-2-2v-.09A1.65 1.65 0 0 0 9 19.4a1.65 1.65 0 0 0-1.82.33l-.06.06a2 2 0 0 1-2.83 0 2 2 0 0 1 0-2.83l.06-.06a1.65 1.65 0 0 0 .33-1.82 1.65 1.65 0 0 0-1.51-1H3a2 2 0 0 1-2-2 2 2 0 0 1 2-2h.09A1.65 1.65 0 0 0 4.6 9a1.65 1.65 0 0 0-.33-1.82l-.06-.06a2 2 0 0 1 0-2.83 2 2 0 0 1 2.83 0l.06.06a1.65 1.65 0 0 0 1.82.33H9a1.65 1.65 0 0 0 1 1.51V3a2 2 0 0 1 2-2 2 2 0 0 1 2 2v.09a1.65 1.65 0 0 0 1 1.51 1.65 1.65 0 0 0 1.82-.33l.06-.06a2 2 0 0 1 2.83 0 2 2 0 0 1 0 2.83l-.06.06a1.65 1.65 0 0 0-.33 1.82V9a1.65 1.65 0 0 0 1.51 1H21a2 2 0 0 1 2 2 2 2 0 0 1-2 2h-.09a1.65 1.65 0 0 0-1.51 1z"], ["type", "button", 1, "dropdown-item", "logout-item", 3, "click"], ["d", "M9 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h4"], ["points", "16,17 21,12 16,7"], ["x1", "21", "y1", "12", "x2", "9", "y2", "12"], [1, "header-notifications"]], template: function HeaderComponent_Template(rf, ctx) {
    if (rf & 1) {
      \u0275\u0275elementStart(0, "header", 0)(1, "div", 1)(2, "button", 2);
      \u0275\u0275listener("click", function HeaderComponent_Template_button_click_2_listener() {
        return ctx.onToggleSidebar();
      });
      \u0275\u0275element(3, "span", 3)(4, "span", 3)(5, "span", 3);
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(6, "div", 4)(7, "h1");
      \u0275\u0275text(8, "Espace Fournisseur");
      \u0275\u0275elementEnd()()();
      \u0275\u0275elementStart(9, "div", 5);
      \u0275\u0275template(10, HeaderComponent_div_10_Template, 2, 0, "div", 6);
      \u0275\u0275elementStart(11, "div", 7);
      \u0275\u0275listener("clickOutside", function HeaderComponent_Template_div_clickOutside_11_listener() {
        return ctx.closeUserMenu();
      });
      \u0275\u0275elementStart(12, "button", 8);
      \u0275\u0275listener("click", function HeaderComponent_Template_button_click_12_listener() {
        return ctx.toggleUserMenu();
      });
      \u0275\u0275elementStart(13, "div", 9);
      \u0275\u0275text(14);
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(15, "div", 10)(16, "span", 11);
      \u0275\u0275text(17);
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(18, "span", 12);
      \u0275\u0275text(19, "Fournisseur");
      \u0275\u0275elementEnd()();
      \u0275\u0275namespaceSVG();
      \u0275\u0275elementStart(20, "svg", 13);
      \u0275\u0275element(21, "polyline", 14);
      \u0275\u0275elementEnd()();
      \u0275\u0275namespaceHTML();
      \u0275\u0275elementStart(22, "div", 15)(23, "div", 16)(24, "div", 17);
      \u0275\u0275text(25);
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(26, "div", 18)(27, "div", 11);
      \u0275\u0275text(28);
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(29, "div", 19);
      \u0275\u0275text(30);
      \u0275\u0275elementEnd()()();
      \u0275\u0275element(31, "div", 20);
      \u0275\u0275elementStart(32, "div", 21)(33, "button", 22);
      \u0275\u0275listener("click", function HeaderComponent_Template_button_click_33_listener() {
        return ctx.navigateToProfile();
      });
      \u0275\u0275namespaceSVG();
      \u0275\u0275elementStart(34, "svg", 23);
      \u0275\u0275element(35, "path", 24)(36, "circle", 25);
      \u0275\u0275elementEnd();
      \u0275\u0275text(37, " Mon Profil ");
      \u0275\u0275elementEnd();
      \u0275\u0275namespaceHTML();
      \u0275\u0275elementStart(38, "button", 26);
      \u0275\u0275namespaceSVG();
      \u0275\u0275elementStart(39, "svg", 23);
      \u0275\u0275element(40, "circle", 27)(41, "path", 28);
      \u0275\u0275elementEnd();
      \u0275\u0275text(42, " Param\xE8tres ");
      \u0275\u0275elementEnd()();
      \u0275\u0275namespaceHTML();
      \u0275\u0275element(43, "div", 20);
      \u0275\u0275elementStart(44, "button", 29);
      \u0275\u0275listener("click", function HeaderComponent_Template_button_click_44_listener() {
        return ctx.logout();
      });
      \u0275\u0275namespaceSVG();
      \u0275\u0275elementStart(45, "svg", 23);
      \u0275\u0275element(46, "path", 30)(47, "polyline", 31)(48, "line", 32);
      \u0275\u0275elementEnd();
      \u0275\u0275text(49, " D\xE9connexion ");
      \u0275\u0275elementEnd()()()()();
    }
    if (rf & 2) {
      \u0275\u0275advance(10);
      \u0275\u0275property("ngIf", !ctx.isAdmin());
      \u0275\u0275advance(4);
      \u0275\u0275textInterpolate1(" ", ctx.getUserInitials(), " ");
      \u0275\u0275advance(3);
      \u0275\u0275textInterpolate(ctx.getUserFullName());
      \u0275\u0275advance(3);
      \u0275\u0275classProp("rotated", ctx.showUserMenu);
      \u0275\u0275advance(2);
      \u0275\u0275classProp("show", ctx.showUserMenu);
      \u0275\u0275advance(3);
      \u0275\u0275textInterpolate1(" ", ctx.getUserInitials(), " ");
      \u0275\u0275advance(3);
      \u0275\u0275textInterpolate(ctx.getUserFullName());
      \u0275\u0275advance(2);
      \u0275\u0275textInterpolate(ctx.currentUser == null ? null : ctx.currentUser.email);
    }
  }, dependencies: [CommonModule, NgIf, NotificationIconComponent], styles: ["\n\n.header[_ngcontent-%COMP%] {\n  background:\n    linear-gradient(\n      135deg,\n      #ffffff 0%,\n      #f8fafc 100%);\n  border-bottom: 1px solid #e2e8f0;\n  padding: 0 24px;\n  height: 64px;\n  display: flex;\n  align-items: center;\n  justify-content: space-between;\n  position: sticky;\n  top: 0;\n  z-index: 1000;\n  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);\n  -webkit-backdrop-filter: blur(8px);\n  backdrop-filter: blur(8px);\n  transition: all 0.3s ease;\n}\n.header-left[_ngcontent-%COMP%] {\n  display: flex;\n  align-items: center;\n  gap: 16px;\n}\n.sidebar-toggle[_ngcontent-%COMP%] {\n  background:\n    linear-gradient(\n      135deg,\n      #f8fafc 0%,\n      #e2e8f0 100%);\n  border: 1px solid #e2e8f0;\n  padding: 8px;\n  cursor: pointer;\n  border-radius: 12px;\n  transition: all 0.3s ease;\n  display: flex;\n  flex-direction: column;\n  gap: 3px;\n  width: 40px;\n  height: 40px;\n  justify-content: center;\n  align-items: center;\n  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);\n}\n.sidebar-toggle[_ngcontent-%COMP%]:hover {\n  background:\n    linear-gradient(\n      135deg,\n      #e2e8f0 0%,\n      #cbd5e1 100%);\n  transform: translateY(-1px);\n  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);\n}\n.sidebar-toggle[_ngcontent-%COMP%]:active {\n  transform: translateY(0);\n  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);\n}\n.hamburger-line[_ngcontent-%COMP%] {\n  width: 18px;\n  height: 2px;\n  background-color: #374151;\n  border-radius: 1px;\n  transition: all 0.3s ease;\n}\n.header-title[_ngcontent-%COMP%]   h1[_ngcontent-%COMP%] {\n  font-size: 20px;\n  font-weight: 600;\n  color: #111827;\n  margin: 0;\n}\n.mock-indicator[_ngcontent-%COMP%] {\n  background:\n    linear-gradient(\n      45deg,\n      #ff6b35,\n      #f7931e);\n  color: white;\n  padding: 4px 8px;\n  border-radius: 12px;\n  font-size: 0.75rem;\n  font-weight: 600;\n  margin-left: 12px;\n  animation: _ngcontent-%COMP%_pulse 2s infinite;\n  box-shadow: 0 2px 4px rgba(255, 107, 53, 0.3);\n}\n@keyframes _ngcontent-%COMP%_pulse {\n  0% {\n    opacity: 1;\n  }\n  50% {\n    opacity: 0.7;\n  }\n  100% {\n    opacity: 1;\n  }\n}\n.header-right[_ngcontent-%COMP%] {\n  display: flex;\n  align-items: center;\n  gap: 16px;\n}\n.header-notifications[_ngcontent-%COMP%] {\n  position: relative;\n}\n.notification-btn[_ngcontent-%COMP%] {\n  background:\n    linear-gradient(\n      135deg,\n      #f8fafc 0%,\n      #e2e8f0 100%);\n  border: 1px solid #e2e8f0;\n  padding: 10px;\n  cursor: pointer;\n  border-radius: 12px;\n  transition: all 0.3s ease;\n  color: #6b7280;\n  position: relative;\n  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);\n  width: 40px;\n  height: 40px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n}\n.notification-btn[_ngcontent-%COMP%]:hover {\n  background:\n    linear-gradient(\n      135deg,\n      #e2e8f0 0%,\n      #cbd5e1 100%);\n  color: #374151;\n  transform: translateY(-1px);\n  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);\n}\n.notification-btn[_ngcontent-%COMP%]:active {\n  transform: translateY(0);\n  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);\n}\n.notification-badge[_ngcontent-%COMP%] {\n  position: absolute;\n  top: 4px;\n  right: 4px;\n  background: #ef4444;\n  color: white;\n  font-size: 10px;\n  font-weight: 600;\n  padding: 2px 5px;\n  border-radius: 10px;\n  min-width: 16px;\n  text-align: center;\n  line-height: 1;\n}\n.user-menu[_ngcontent-%COMP%] {\n  position: relative;\n}\n.user-menu-trigger[_ngcontent-%COMP%] {\n  background:\n    linear-gradient(\n      135deg,\n      #f8fafc 0%,\n      #e2e8f0 100%);\n  border: 1px solid #e2e8f0;\n  padding: 8px 12px;\n  cursor: pointer;\n  border-radius: 12px;\n  transition: all 0.3s ease;\n  display: flex;\n  align-items: center;\n  gap: 12px;\n  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);\n}\n.user-menu-trigger[_ngcontent-%COMP%]:hover {\n  background:\n    linear-gradient(\n      135deg,\n      #e2e8f0 0%,\n      #cbd5e1 100%);\n  transform: translateY(-1px);\n  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);\n}\n.user-menu-trigger[_ngcontent-%COMP%]:active {\n  transform: translateY(0);\n  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);\n}\n.user-avatar[_ngcontent-%COMP%] {\n  width: 36px;\n  height: 36px;\n  border-radius: 50%;\n  background:\n    linear-gradient(\n      135deg,\n      #667eea 0%,\n      #764ba2 100%);\n  color: white;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  font-weight: 600;\n  font-size: 14px;\n}\n.user-info[_ngcontent-%COMP%] {\n  display: flex;\n  flex-direction: column;\n  align-items: flex-start;\n  text-align: left;\n}\n.user-name[_ngcontent-%COMP%] {\n  font-weight: 500;\n  color: #111827;\n  font-size: 14px;\n  line-height: 1.2;\n}\n.user-role[_ngcontent-%COMP%] {\n  font-size: 12px;\n  color: #6b7280;\n  line-height: 1.2;\n}\n.dropdown-arrow[_ngcontent-%COMP%] {\n  color: #6b7280;\n  transition: transform 0.2s;\n}\n.dropdown-arrow.rotated[_ngcontent-%COMP%] {\n  transform: rotate(180deg);\n}\n.user-dropdown[_ngcontent-%COMP%] {\n  position: absolute;\n  top: 100%;\n  right: 0;\n  background:\n    linear-gradient(\n      135deg,\n      #ffffff 0%,\n      #f8fafc 100%);\n  border: 1px solid #e2e8f0;\n  border-radius: 16px;\n  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);\n  min-width: 280px;\n  opacity: 0;\n  visibility: hidden;\n  transform: translateY(-10px) scale(0.95);\n  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);\n  z-index: 1001;\n  margin-top: 8px;\n  -webkit-backdrop-filter: blur(8px);\n  backdrop-filter: blur(8px);\n}\n.user-dropdown.show[_ngcontent-%COMP%] {\n  opacity: 1;\n  visibility: visible;\n  transform: translateY(0) scale(1);\n}\n.dropdown-header[_ngcontent-%COMP%] {\n  padding: 16px;\n  display: flex;\n  align-items: center;\n  gap: 12px;\n}\n.user-avatar-large[_ngcontent-%COMP%] {\n  width: 48px;\n  height: 48px;\n  border-radius: 50%;\n  background:\n    linear-gradient(\n      135deg,\n      #667eea 0%,\n      #764ba2 100%);\n  color: white;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  font-weight: 600;\n  font-size: 16px;\n}\n.user-details[_ngcontent-%COMP%] {\n  flex: 1;\n}\n.user-details[_ngcontent-%COMP%]   .user-name[_ngcontent-%COMP%] {\n  font-weight: 600;\n  color: #111827;\n  font-size: 16px;\n  margin-bottom: 2px;\n}\n.user-email[_ngcontent-%COMP%] {\n  font-size: 13px;\n  color: #6b7280;\n}\n.dropdown-divider[_ngcontent-%COMP%] {\n  height: 1px;\n  background: #e5e7eb;\n  margin: 0;\n}\n.dropdown-menu[_ngcontent-%COMP%] {\n  padding: 8px 0;\n}\n.dropdown-item[_ngcontent-%COMP%] {\n  width: 100%;\n  background: none;\n  border: none;\n  padding: 12px 16px;\n  text-align: left;\n  cursor: pointer;\n  transition: all 0.2s ease;\n  display: flex;\n  align-items: center;\n  gap: 12px;\n  font-size: 14px;\n  color: #374151;\n  border-radius: 8px;\n  margin: 2px 8px;\n  width: calc(100% - 16px);\n}\n.dropdown-item[_ngcontent-%COMP%]:hover {\n  background:\n    linear-gradient(\n      135deg,\n      #f1f5f9 0%,\n      #e2e8f0 100%);\n  transform: translateX(4px);\n  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);\n}\n.dropdown-item[_ngcontent-%COMP%]   svg[_ngcontent-%COMP%] {\n  color: #6b7280;\n}\n.logout-item[_ngcontent-%COMP%] {\n  color: #dc2626;\n}\n.logout-item[_ngcontent-%COMP%]:hover {\n  background-color: #fef2f2;\n}\n.logout-item[_ngcontent-%COMP%]   svg[_ngcontent-%COMP%] {\n  color: #dc2626;\n}\n@media (max-width: 767px) {\n  .header[_ngcontent-%COMP%] {\n    padding: 0 16px;\n    height: 56px;\n  }\n  .header-title[_ngcontent-%COMP%]   h1[_ngcontent-%COMP%] {\n    font-size: 18px;\n  }\n  .mock-indicator[_ngcontent-%COMP%] {\n    font-size: 0.7rem;\n    padding: 3px 6px;\n    margin-left: 8px;\n  }\n  .user-info[_ngcontent-%COMP%] {\n    display: none;\n  }\n  .user-dropdown[_ngcontent-%COMP%] {\n    min-width: 240px;\n    right: -8px;\n  }\n  .sidebar-toggle[_ngcontent-%COMP%] {\n    width: 28px;\n    height: 28px;\n  }\n  .hamburger-line[_ngcontent-%COMP%] {\n    width: 16px;\n  }\n  .notification-btn[_ngcontent-%COMP%], \n   .user-menu-trigger[_ngcontent-%COMP%] {\n    padding: 6px;\n  }\n  .user-avatar[_ngcontent-%COMP%] {\n    width: 32px;\n    height: 32px;\n    font-size: 12px;\n  }\n}\n@media (min-width: 768px) and (max-width: 1023px) {\n  .header[_ngcontent-%COMP%] {\n    padding: 0 20px;\n    height: 60px;\n  }\n  .header-title[_ngcontent-%COMP%]   h1[_ngcontent-%COMP%] {\n    font-size: 19px;\n  }\n  .mock-indicator[_ngcontent-%COMP%] {\n    font-size: 0.7rem;\n    margin-left: 10px;\n  }\n  .user-dropdown[_ngcontent-%COMP%] {\n    min-width: 260px;\n  }\n  .user-name[_ngcontent-%COMP%] {\n    font-size: 13px;\n  }\n  .user-role[_ngcontent-%COMP%] {\n    font-size: 11px;\n  }\n}\n@media (min-width: 1024px) {\n  .header[_ngcontent-%COMP%] {\n    padding: 0 24px;\n    height: 64px;\n  }\n  .user-dropdown[_ngcontent-%COMP%] {\n    min-width: 280px;\n  }\n}\n@media (max-width: 1023px) {\n  .sidebar-toggle[_ngcontent-%COMP%], \n   .notification-btn[_ngcontent-%COMP%], \n   .user-menu-trigger[_ngcontent-%COMP%], \n   .dropdown-item[_ngcontent-%COMP%] {\n    min-height: 44px;\n    min-width: 44px;\n  }\n  .dropdown-item[_ngcontent-%COMP%] {\n    padding: 14px 16px;\n    min-width: auto;\n  }\n}\n@media (max-width: 480px) {\n  .header-title[_ngcontent-%COMP%]   h1[_ngcontent-%COMP%] {\n    font-size: 16px;\n  }\n  .mock-indicator[_ngcontent-%COMP%] {\n    display: none;\n  }\n  .header-notifications[_ngcontent-%COMP%] {\n    margin-right: 4px;\n  }\n}\n/*# sourceMappingURL=header.component.css.map */"] });
};
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && setClassMetadata(HeaderComponent, [{
    type: Component,
    args: [{ selector: "app-header", standalone: true, imports: [CommonModule, NotificationIconComponent], template: '<header class="header">\n  <div class="header-left">\n    <button class="sidebar-toggle" (click)="onToggleSidebar()" type="button">\n      <span class="hamburger-line"></span>\n      <span class="hamburger-line"></span>\n      <span class="hamburger-line"></span>\n    </button>\n    \n    <div class="header-title">\n      <h1>Espace Fournisseur</h1>\n    </div>\n  </div>\n\n  <div class="header-right">\n    <!-- Notifications (seulement pour les fournisseurs, pas les admins) -->\n    <div class="header-notifications" *ngIf="!isAdmin()">\n      <app-notification-icon></app-notification-icon>\n    </div>\n\n    <!-- User Menu -->\n    <div class="user-menu" (clickOutside)="closeUserMenu()">\n      <button class="user-menu-trigger" (click)="toggleUserMenu()" type="button">\n        <div class="user-avatar">\n          {{ getUserInitials() }}\n        </div>\n        <div class="user-info">\n          <span class="user-name">{{ getUserFullName() }}</span>\n          <span class="user-role">Fournisseur</span>\n        </div>\n        <svg class="dropdown-arrow" [class.rotated]="showUserMenu" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">\n          <polyline points="6,9 12,15 18,9"></polyline>\n        </svg>\n      </button>\n\n      <div class="user-dropdown" [class.show]="showUserMenu">\n        <div class="dropdown-header">\n          <div class="user-avatar-large">\n            {{ getUserInitials() }}\n          </div>\n          <div class="user-details">\n            <div class="user-name">{{ getUserFullName() }}</div>\n            <div class="user-email">{{ currentUser?.email }}</div>\n          </div>\n        </div>\n        \n        <div class="dropdown-divider"></div>\n        \n        <div class="dropdown-menu">\n          <button class="dropdown-item" (click)="navigateToProfile()" type="button">\n            <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">\n              <path d="M20 21v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2"></path>\n              <circle cx="12" cy="7" r="4"></circle>\n            </svg>\n            Mon Profil\n          </button>\n          \n          <button class="dropdown-item" type="button">\n            <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">\n              <circle cx="12" cy="12" r="3"></circle>\n              <path d="M19.4 15a1.65 1.65 0 0 0 .33 1.82l.06.06a2 2 0 0 1 0 2.83 2 2 0 0 1-2.83 0l-.06-.06a1.65 1.65 0 0 0-1.82-.33 1.65 1.65 0 0 0-1 1.51V21a2 2 0 0 1-2 2 2 2 0 0 1-2-2v-.09A1.65 1.65 0 0 0 9 19.4a1.65 1.65 0 0 0-1.82.33l-.06.06a2 2 0 0 1-2.83 0 2 2 0 0 1 0-2.83l.06-.06a1.65 1.65 0 0 0 .33-1.82 1.65 1.65 0 0 0-1.51-1H3a2 2 0 0 1-2-2 2 2 0 0 1 2-2h.09A1.65 1.65 0 0 0 4.6 9a1.65 1.65 0 0 0-.33-1.82l-.06-.06a2 2 0 0 1 0-2.83 2 2 0 0 1 2.83 0l.06.06a1.65 1.65 0 0 0 1.82.33H9a1.65 1.65 0 0 0 1 1.51V3a2 2 0 0 1 2-2 2 2 0 0 1 2 2v.09a1.65 1.65 0 0 0 1 1.51 1.65 1.65 0 0 0 1.82-.33l.06-.06a2 2 0 0 1 2.83 0 2 2 0 0 1 0 2.83l-.06.06a1.65 1.65 0 0 0-.33 1.82V9a1.65 1.65 0 0 0 1.51 1H21a2 2 0 0 1 2 2 2 2 0 0 1-2 2h-.09a1.65 1.65 0 0 0-1.51 1z"></path>\n            </svg>\n            Param\xE8tres\n          </button>\n        </div>\n        \n        <div class="dropdown-divider"></div>\n        \n        <button class="dropdown-item logout-item" (click)="logout()" type="button">\n          <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">\n            <path d="M9 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h4"></path>\n            <polyline points="16,17 21,12 16,7"></polyline>\n            <line x1="21" y1="12" x2="9" y2="12"></line>\n          </svg>\n          D\xE9connexion\n        </button>\n      </div>\n    </div>\n  </div>\n</header>\n', styles: ["/* src/app/components/layout/header/header.component.css */\n.header {\n  background:\n    linear-gradient(\n      135deg,\n      #ffffff 0%,\n      #f8fafc 100%);\n  border-bottom: 1px solid #e2e8f0;\n  padding: 0 24px;\n  height: 64px;\n  display: flex;\n  align-items: center;\n  justify-content: space-between;\n  position: sticky;\n  top: 0;\n  z-index: 1000;\n  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);\n  -webkit-backdrop-filter: blur(8px);\n  backdrop-filter: blur(8px);\n  transition: all 0.3s ease;\n}\n.header-left {\n  display: flex;\n  align-items: center;\n  gap: 16px;\n}\n.sidebar-toggle {\n  background:\n    linear-gradient(\n      135deg,\n      #f8fafc 0%,\n      #e2e8f0 100%);\n  border: 1px solid #e2e8f0;\n  padding: 8px;\n  cursor: pointer;\n  border-radius: 12px;\n  transition: all 0.3s ease;\n  display: flex;\n  flex-direction: column;\n  gap: 3px;\n  width: 40px;\n  height: 40px;\n  justify-content: center;\n  align-items: center;\n  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);\n}\n.sidebar-toggle:hover {\n  background:\n    linear-gradient(\n      135deg,\n      #e2e8f0 0%,\n      #cbd5e1 100%);\n  transform: translateY(-1px);\n  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);\n}\n.sidebar-toggle:active {\n  transform: translateY(0);\n  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);\n}\n.hamburger-line {\n  width: 18px;\n  height: 2px;\n  background-color: #374151;\n  border-radius: 1px;\n  transition: all 0.3s ease;\n}\n.header-title h1 {\n  font-size: 20px;\n  font-weight: 600;\n  color: #111827;\n  margin: 0;\n}\n.mock-indicator {\n  background:\n    linear-gradient(\n      45deg,\n      #ff6b35,\n      #f7931e);\n  color: white;\n  padding: 4px 8px;\n  border-radius: 12px;\n  font-size: 0.75rem;\n  font-weight: 600;\n  margin-left: 12px;\n  animation: pulse 2s infinite;\n  box-shadow: 0 2px 4px rgba(255, 107, 53, 0.3);\n}\n@keyframes pulse {\n  0% {\n    opacity: 1;\n  }\n  50% {\n    opacity: 0.7;\n  }\n  100% {\n    opacity: 1;\n  }\n}\n.header-right {\n  display: flex;\n  align-items: center;\n  gap: 16px;\n}\n.header-notifications {\n  position: relative;\n}\n.notification-btn {\n  background:\n    linear-gradient(\n      135deg,\n      #f8fafc 0%,\n      #e2e8f0 100%);\n  border: 1px solid #e2e8f0;\n  padding: 10px;\n  cursor: pointer;\n  border-radius: 12px;\n  transition: all 0.3s ease;\n  color: #6b7280;\n  position: relative;\n  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);\n  width: 40px;\n  height: 40px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n}\n.notification-btn:hover {\n  background:\n    linear-gradient(\n      135deg,\n      #e2e8f0 0%,\n      #cbd5e1 100%);\n  color: #374151;\n  transform: translateY(-1px);\n  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);\n}\n.notification-btn:active {\n  transform: translateY(0);\n  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);\n}\n.notification-badge {\n  position: absolute;\n  top: 4px;\n  right: 4px;\n  background: #ef4444;\n  color: white;\n  font-size: 10px;\n  font-weight: 600;\n  padding: 2px 5px;\n  border-radius: 10px;\n  min-width: 16px;\n  text-align: center;\n  line-height: 1;\n}\n.user-menu {\n  position: relative;\n}\n.user-menu-trigger {\n  background:\n    linear-gradient(\n      135deg,\n      #f8fafc 0%,\n      #e2e8f0 100%);\n  border: 1px solid #e2e8f0;\n  padding: 8px 12px;\n  cursor: pointer;\n  border-radius: 12px;\n  transition: all 0.3s ease;\n  display: flex;\n  align-items: center;\n  gap: 12px;\n  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);\n}\n.user-menu-trigger:hover {\n  background:\n    linear-gradient(\n      135deg,\n      #e2e8f0 0%,\n      #cbd5e1 100%);\n  transform: translateY(-1px);\n  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);\n}\n.user-menu-trigger:active {\n  transform: translateY(0);\n  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);\n}\n.user-avatar {\n  width: 36px;\n  height: 36px;\n  border-radius: 50%;\n  background:\n    linear-gradient(\n      135deg,\n      #667eea 0%,\n      #764ba2 100%);\n  color: white;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  font-weight: 600;\n  font-size: 14px;\n}\n.user-info {\n  display: flex;\n  flex-direction: column;\n  align-items: flex-start;\n  text-align: left;\n}\n.user-name {\n  font-weight: 500;\n  color: #111827;\n  font-size: 14px;\n  line-height: 1.2;\n}\n.user-role {\n  font-size: 12px;\n  color: #6b7280;\n  line-height: 1.2;\n}\n.dropdown-arrow {\n  color: #6b7280;\n  transition: transform 0.2s;\n}\n.dropdown-arrow.rotated {\n  transform: rotate(180deg);\n}\n.user-dropdown {\n  position: absolute;\n  top: 100%;\n  right: 0;\n  background:\n    linear-gradient(\n      135deg,\n      #ffffff 0%,\n      #f8fafc 100%);\n  border: 1px solid #e2e8f0;\n  border-radius: 16px;\n  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);\n  min-width: 280px;\n  opacity: 0;\n  visibility: hidden;\n  transform: translateY(-10px) scale(0.95);\n  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);\n  z-index: 1001;\n  margin-top: 8px;\n  -webkit-backdrop-filter: blur(8px);\n  backdrop-filter: blur(8px);\n}\n.user-dropdown.show {\n  opacity: 1;\n  visibility: visible;\n  transform: translateY(0) scale(1);\n}\n.dropdown-header {\n  padding: 16px;\n  display: flex;\n  align-items: center;\n  gap: 12px;\n}\n.user-avatar-large {\n  width: 48px;\n  height: 48px;\n  border-radius: 50%;\n  background:\n    linear-gradient(\n      135deg,\n      #667eea 0%,\n      #764ba2 100%);\n  color: white;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  font-weight: 600;\n  font-size: 16px;\n}\n.user-details {\n  flex: 1;\n}\n.user-details .user-name {\n  font-weight: 600;\n  color: #111827;\n  font-size: 16px;\n  margin-bottom: 2px;\n}\n.user-email {\n  font-size: 13px;\n  color: #6b7280;\n}\n.dropdown-divider {\n  height: 1px;\n  background: #e5e7eb;\n  margin: 0;\n}\n.dropdown-menu {\n  padding: 8px 0;\n}\n.dropdown-item {\n  width: 100%;\n  background: none;\n  border: none;\n  padding: 12px 16px;\n  text-align: left;\n  cursor: pointer;\n  transition: all 0.2s ease;\n  display: flex;\n  align-items: center;\n  gap: 12px;\n  font-size: 14px;\n  color: #374151;\n  border-radius: 8px;\n  margin: 2px 8px;\n  width: calc(100% - 16px);\n}\n.dropdown-item:hover {\n  background:\n    linear-gradient(\n      135deg,\n      #f1f5f9 0%,\n      #e2e8f0 100%);\n  transform: translateX(4px);\n  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);\n}\n.dropdown-item svg {\n  color: #6b7280;\n}\n.logout-item {\n  color: #dc2626;\n}\n.logout-item:hover {\n  background-color: #fef2f2;\n}\n.logout-item svg {\n  color: #dc2626;\n}\n@media (max-width: 767px) {\n  .header {\n    padding: 0 16px;\n    height: 56px;\n  }\n  .header-title h1 {\n    font-size: 18px;\n  }\n  .mock-indicator {\n    font-size: 0.7rem;\n    padding: 3px 6px;\n    margin-left: 8px;\n  }\n  .user-info {\n    display: none;\n  }\n  .user-dropdown {\n    min-width: 240px;\n    right: -8px;\n  }\n  .sidebar-toggle {\n    width: 28px;\n    height: 28px;\n  }\n  .hamburger-line {\n    width: 16px;\n  }\n  .notification-btn,\n  .user-menu-trigger {\n    padding: 6px;\n  }\n  .user-avatar {\n    width: 32px;\n    height: 32px;\n    font-size: 12px;\n  }\n}\n@media (min-width: 768px) and (max-width: 1023px) {\n  .header {\n    padding: 0 20px;\n    height: 60px;\n  }\n  .header-title h1 {\n    font-size: 19px;\n  }\n  .mock-indicator {\n    font-size: 0.7rem;\n    margin-left: 10px;\n  }\n  .user-dropdown {\n    min-width: 260px;\n  }\n  .user-name {\n    font-size: 13px;\n  }\n  .user-role {\n    font-size: 11px;\n  }\n}\n@media (min-width: 1024px) {\n  .header {\n    padding: 0 24px;\n    height: 64px;\n  }\n  .user-dropdown {\n    min-width: 280px;\n  }\n}\n@media (max-width: 1023px) {\n  .sidebar-toggle,\n  .notification-btn,\n  .user-menu-trigger,\n  .dropdown-item {\n    min-height: 44px;\n    min-width: 44px;\n  }\n  .dropdown-item {\n    padding: 14px 16px;\n    min-width: auto;\n  }\n}\n@media (max-width: 480px) {\n  .header-title h1 {\n    font-size: 16px;\n  }\n  .mock-indicator {\n    display: none;\n  }\n  .header-notifications {\n    margin-right: 4px;\n  }\n}\n/*# sourceMappingURL=header.component.css.map */\n"] }]
  }], () => [{ type: AuthService }, { type: AdminAuthService }, { type: Router }], { toggleSidebar: [{
    type: Output
  }] });
})();
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && \u0275setClassDebugInfo(HeaderComponent, { className: "HeaderComponent", filePath: "src/app/components/layout/header/header.component.ts", lineNumber: 16 });
})();

// src/app/components/layout/sidebar/sidebar.component.ts
function SidebarComponent_div_1_Template(rf, ctx) {
  if (rf & 1) {
    const _r1 = \u0275\u0275getCurrentView();
    \u0275\u0275elementStart(0, "div", 16);
    \u0275\u0275listener("click", function SidebarComponent_div_1_Template_div_click_0_listener() {
      \u0275\u0275restoreView(_r1);
      const ctx_r1 = \u0275\u0275nextContext();
      return \u0275\u0275resetView(ctx_r1.onCloseSidebar());
    });
    \u0275\u0275elementEnd();
  }
}
function SidebarComponent_div_9_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "div", 17)(1, "span", 18);
    \u0275\u0275text(2, "FournisseurApp");
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(3, "span", 19);
    \u0275\u0275text(4, "Gestion");
    \u0275\u0275elementEnd()();
  }
}
function SidebarComponent_button_10_Template(rf, ctx) {
  if (rf & 1) {
    const _r3 = \u0275\u0275getCurrentView();
    \u0275\u0275elementStart(0, "button", 20);
    \u0275\u0275listener("click", function SidebarComponent_button_10_Template_button_click_0_listener() {
      \u0275\u0275restoreView(_r3);
      const ctx_r1 = \u0275\u0275nextContext();
      return \u0275\u0275resetView(ctx_r1.onCloseSidebar());
    });
    \u0275\u0275namespaceSVG();
    \u0275\u0275elementStart(1, "svg", 21);
    \u0275\u0275element(2, "line", 22)(3, "line", 23);
    \u0275\u0275elementEnd()();
  }
}
function SidebarComponent_li_13_div_1_span_3_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "span", 33);
    \u0275\u0275text(1);
    \u0275\u0275elementEnd();
  }
  if (rf & 2) {
    const item_r5 = \u0275\u0275nextContext(2).$implicit;
    \u0275\u0275advance();
    \u0275\u0275textInterpolate(item_r5.label);
  }
}
function SidebarComponent_li_13_div_1_span_4_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "span", 34);
    \u0275\u0275namespaceSVG();
    \u0275\u0275elementStart(1, "svg", 35);
    \u0275\u0275element(2, "polyline", 36);
    \u0275\u0275elementEnd()();
  }
}
function SidebarComponent_li_13_div_1_li_6_Template(rf, ctx) {
  if (rf & 1) {
    const _r6 = \u0275\u0275getCurrentView();
    \u0275\u0275elementStart(0, "li", 37)(1, "button", 38);
    \u0275\u0275listener("click", function SidebarComponent_li_13_div_1_li_6_Template_button_click_1_listener() {
      const child_r7 = \u0275\u0275restoreView(_r6).$implicit;
      const ctx_r1 = \u0275\u0275nextContext(3);
      return \u0275\u0275resetView(ctx_r1.navigateTo(child_r7.route));
    });
    \u0275\u0275element(2, "span", 39);
    \u0275\u0275elementStart(3, "span", 40);
    \u0275\u0275text(4);
    \u0275\u0275elementEnd()()();
  }
  if (rf & 2) {
    const child_r7 = ctx.$implicit;
    const ctx_r1 = \u0275\u0275nextContext(3);
    \u0275\u0275advance();
    \u0275\u0275classProp("active", ctx_r1.isActiveRoute(child_r7.route));
    \u0275\u0275advance();
    \u0275\u0275property("innerHTML", ctx_r1.getIconSvg(child_r7.icon), \u0275\u0275sanitizeHtml);
    \u0275\u0275advance(2);
    \u0275\u0275textInterpolate(child_r7.label);
  }
}
function SidebarComponent_li_13_div_1_Template(rf, ctx) {
  if (rf & 1) {
    const _r4 = \u0275\u0275getCurrentView();
    \u0275\u0275elementStart(0, "div", 26)(1, "button", 27);
    \u0275\u0275listener("click", function SidebarComponent_li_13_div_1_Template_button_click_1_listener() {
      \u0275\u0275restoreView(_r4);
      const item_r5 = \u0275\u0275nextContext().$implicit;
      const ctx_r1 = \u0275\u0275nextContext();
      return \u0275\u0275resetView(ctx_r1.toggleSubmenu(item_r5));
    });
    \u0275\u0275element(2, "span", 28);
    \u0275\u0275template(3, SidebarComponent_li_13_div_1_span_3_Template, 2, 1, "span", 29)(4, SidebarComponent_li_13_div_1_span_4_Template, 3, 0, "span", 30);
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(5, "ul", 31);
    \u0275\u0275template(6, SidebarComponent_li_13_div_1_li_6_Template, 5, 4, "li", 32);
    \u0275\u0275elementEnd()();
  }
  if (rf & 2) {
    const item_r5 = \u0275\u0275nextContext().$implicit;
    const ctx_r1 = \u0275\u0275nextContext();
    \u0275\u0275advance();
    \u0275\u0275classProp("active", ctx_r1.isParentActive(item_r5))("expanded", item_r5.expanded);
    \u0275\u0275advance();
    \u0275\u0275property("innerHTML", ctx_r1.getIconSvg(item_r5.icon), \u0275\u0275sanitizeHtml);
    \u0275\u0275advance();
    \u0275\u0275property("ngIf", !ctx_r1.isCollapsed);
    \u0275\u0275advance();
    \u0275\u0275property("ngIf", !ctx_r1.isCollapsed);
    \u0275\u0275advance();
    \u0275\u0275classProp("expanded", item_r5.expanded && !ctx_r1.isCollapsed);
    \u0275\u0275advance();
    \u0275\u0275property("ngForOf", item_r5.children);
  }
}
function SidebarComponent_li_13_ng_template_2_span_2_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "span", 33);
    \u0275\u0275text(1);
    \u0275\u0275elementEnd();
  }
  if (rf & 2) {
    const item_r5 = \u0275\u0275nextContext(2).$implicit;
    \u0275\u0275advance();
    \u0275\u0275textInterpolate(item_r5.label);
  }
}
function SidebarComponent_li_13_ng_template_2_Template(rf, ctx) {
  if (rf & 1) {
    const _r8 = \u0275\u0275getCurrentView();
    \u0275\u0275elementStart(0, "button", 41);
    \u0275\u0275listener("click", function SidebarComponent_li_13_ng_template_2_Template_button_click_0_listener() {
      \u0275\u0275restoreView(_r8);
      const item_r5 = \u0275\u0275nextContext().$implicit;
      const ctx_r1 = \u0275\u0275nextContext();
      return \u0275\u0275resetView(ctx_r1.navigateTo(item_r5.route));
    });
    \u0275\u0275element(1, "span", 28);
    \u0275\u0275template(2, SidebarComponent_li_13_ng_template_2_span_2_Template, 2, 1, "span", 29);
    \u0275\u0275elementEnd();
  }
  if (rf & 2) {
    const item_r5 = \u0275\u0275nextContext().$implicit;
    const ctx_r1 = \u0275\u0275nextContext();
    \u0275\u0275classProp("active", ctx_r1.isActiveRoute(item_r5.route));
    \u0275\u0275advance();
    \u0275\u0275property("innerHTML", ctx_r1.getIconSvg(item_r5.icon), \u0275\u0275sanitizeHtml);
    \u0275\u0275advance();
    \u0275\u0275property("ngIf", !ctx_r1.isCollapsed);
  }
}
function SidebarComponent_li_13_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "li", 24);
    \u0275\u0275template(1, SidebarComponent_li_13_div_1_Template, 7, 10, "div", 25)(2, SidebarComponent_li_13_ng_template_2_Template, 3, 4, "ng-template", null, 0, \u0275\u0275templateRefExtractor);
    \u0275\u0275elementEnd();
  }
  if (rf & 2) {
    const item_r5 = ctx.$implicit;
    const singleItem_r9 = \u0275\u0275reference(3);
    \u0275\u0275advance();
    \u0275\u0275property("ngIf", item_r5.children)("ngIfElse", singleItem_r9);
  }
}
function SidebarComponent_div_14_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "div", 42)(1, "div", 43)(2, "p", 44);
    \u0275\u0275text(3, "Version 1.0.0");
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(4, "p", 45);
    \u0275\u0275text(5, "\xA9 2024 FournisseurApp");
    \u0275\u0275elementEnd()()();
  }
}
var SidebarComponent = class _SidebarComponent {
  router;
  sanitizer;
  isCollapsed = false;
  toggleSidebar = new EventEmitter();
  closeSidebar = new EventEmitter();
  isMobile = false;
  isTablet = false;
  menuItems = [
    {
      label: "Dashboard",
      icon: "dashboard",
      route: "/dashboard/overview"
    },
    {
      label: "Mon Profil",
      icon: "user",
      children: [
        {
          label: "Informations",
          icon: "info",
          route: "/dashboard/profil"
        }
      ]
    },
    {
      label: "Produits",
      icon: "package",
      children: [
        {
          label: "Liste des produits",
          icon: "list",
          route: "/dashboard/products"
        },
        {
          label: "Gestion du stock",
          icon: "inventory",
          route: "/dashboard/stock"
        }
      ]
    },
    {
      label: "R\xE9f\xE9rentiels",
      icon: "settings",
      children: [
        {
          label: "Gestion compl\xE8te",
          icon: "database",
          route: "/dashboard/referentiels"
        },
        {
          label: "Demandes Cat\xE9gories",
          icon: "request",
          route: "/dashboard/demandes-categories"
        }
      ]
    },
    {
      label: "Commandes",
      icon: "orders",
      children: [
        {
          label: "Historique",
          icon: "history",
          route: "/dashboard/orders"
        }
      ]
    },
    {
      label: "Livraisons",
      icon: "truck",
      route: "/dashboard/deliveries"
    },
    {
      label: "Avis Clients",
      icon: "star",
      route: "/dashboard/avis"
    }
  ];
  constructor(router, sanitizer) {
    this.router = router;
    this.sanitizer = sanitizer;
  }
  ngOnInit() {
    this.checkScreenSize();
  }
  ngOnDestroy() {
  }
  onResize() {
    this.checkScreenSize();
  }
  /**
   * Vérifier la taille de l'écran et ajuster la responsivité
   */
  checkScreenSize() {
    const width = window.innerWidth;
    this.isMobile = width < 768;
    this.isTablet = width >= 768 && width < 1024;
    if (this.isMobile && !this.isCollapsed) {
      this.isCollapsed = true;
    }
  }
  toggleSubmenu(item) {
    if (item.children) {
      item.expanded = !item.expanded;
    }
  }
  navigateTo(route) {
    this.router.navigate([route]);
    if (this.isMobile) {
      this.closeSidebar.emit();
    }
  }
  /**
   * Toggle sidebar (pour le bouton hamburger)
   */
  onToggleSidebar() {
    this.toggleSidebar.emit();
  }
  /**
   * Fermer la sidebar (pour l'overlay mobile)
   */
  onCloseSidebar() {
    this.closeSidebar.emit();
  }
  isActiveRoute(route) {
    return this.router.url === route;
  }
  isParentActive(item) {
    if (!item.children)
      return false;
    return item.children.some((child) => child.route && this.isActiveRoute(child.route));
  }
  getIconSvg(iconName) {
    const icons = {
      dashboard: `<svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
        <rect x="3" y="3" width="7" height="7"></rect>
        <rect x="14" y="3" width="7" height="7"></rect>
        <rect x="14" y="14" width="7" height="7"></rect>
        <rect x="3" y="14" width="7" height="7"></rect>
      </svg>`,
      user: `<svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
        <path d="M20 21v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2"></path>
        <circle cx="12" cy="7" r="4"></circle>
      </svg>`,
      package: `<svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
        <line x1="16.5" y1="9.4" x2="7.5" y2="4.21"></line>
        <path d="M21 16V8a2 2 0 0 0-1-1.73l-7-4a2 2 0 0 0-2 0l-7 4A2 2 0 0 0 3 8v8a2 2 0 0 0 1 1.73l7 4a2 2 0 0 0 2 0l7-4A2 2 0 0 0 21 16z"></path>
        <polyline points="3.27,6.96 12,12.01 20.73,6.96"></polyline>
        <line x1="12" y1="22.08" x2="12" y2="12"></line>
      </svg>`,
      orders: `<svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
        <path d="M9 11H5a2 2 0 0 0-2 2v7a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7a2 2 0 0 0-2-2h-4"></path>
        <rect x="9" y="7" width="6" height="6"></rect>
      </svg>`,
      truck: `<svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
        <rect x="1" y="3" width="15" height="13"></rect>
        <polygon points="16,8 20,8 23,11 23,16 16,16 16,8"></polygon>
        <circle cx="5.5" cy="18.5" r="2.5"></circle>
        <circle cx="18.5" cy="18.5" r="2.5"></circle>
      </svg>`,
      info: `<svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
        <circle cx="12" cy="12" r="10"></circle>
        <line x1="12" y1="16" x2="12" y2="12"></line>
        <line x1="12" y1="8" x2="12.01" y2="8"></line>
      </svg>`,
      bank: `<svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
        <line x1="12" y1="1" x2="12" y2="23"></line>
        <path d="M17 5H9.5a3.5 3.5 0 0 0 0 7h5a3.5 3.5 0 0 1 0 7H6"></path>
      </svg>`,
      list: `<svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
        <line x1="8" y1="6" x2="21" y2="6"></line>
        <line x1="8" y1="12" x2="21" y2="12"></line>
        <line x1="8" y1="18" x2="21" y2="18"></line>
        <line x1="3" y1="6" x2="3.01" y2="6"></line>
        <line x1="3" y1="12" x2="3.01" y2="12"></line>
        <line x1="3" y1="18" x2="3.01" y2="18"></line>
      </svg>`,
      plus: `<svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
        <line x1="12" y1="5" x2="12" y2="19"></line>
        <line x1="5" y1="12" x2="19" y2="12"></line>
      </svg>`,
      inventory: `<svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
        <path d="M21 8a2 2 0 0 0-1-1.73l-7-4a2 2 0 0 0-2 0l-7 4A2 2 0 0 0 3 8v8a2 2 0 0 0 1 1.73l7 4a2 2 0 0 0 2 0l7-4A2 2 0 0 0 21 16V8z"></path>
        <path d="M3.27 6.96L12 12.01l8.73-5.05"></path>
        <line x1="12" y1="22.08" x2="12" y2="12"></line>
      </svg>`,
      inbox: `<svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
        <polyline points="22,12 18,12 15,21 9,21 6,12 2,12"></polyline>
        <path d="M5.45 5.11L2 12v6a2 2 0 0 0 2 2h16a2 2 0 0 0 2-2v-6l-3.45-6.89A2 2 0 0 0 16.76 4H7.24a2 2 0 0 0-1.79 1.11z"></path>
      </svg>`,
      history: `<svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
        <circle cx="12" cy="12" r="10"></circle>
        <polyline points="12,6 12,12 16,14"></polyline>
      </svg>`,
      settings: `<svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
        <circle cx="12" cy="12" r="3"></circle>
        <path d="M19.4 15a1.65 1.65 0 0 0 .33 1.82l.06.06a2 2 0 0 1 0 2.83 2 2 0 0 1-2.83 0l-.06-.06a1.65 1.65 0 0 0-1.82-.33 1.65 1.65 0 0 0-1 1.51V21a2 2 0 0 1-2 2 2 2 0 0 1-2-2v-.09A1.65 1.65 0 0 0 9 19.4a1.65 1.65 0 0 0-1.82.33l-.06.06a2 2 0 0 1-2.83 0 2 2 0 0 1 0-2.83l.06-.06a1.65 1.65 0 0 0 .33-1.82 1.65 1.65 0 0 0-1.51-1H3a2 2 0 0 1-2-2 2 2 0 0 1 2-2h.09A1.65 1.65 0 0 0 4.6 9a1.65 1.65 0 0 0-.33-1.82l-.06-.06a2 2 0 0 1 0-2.83 2 2 0 0 1 2.83 0l.06.06a1.65 1.65 0 0 0 1.82.33H9a1.65 1.65 0 0 0 1 1.51V3a2 2 0 0 1 2-2 2 2 0 0 1 2 2v.09a1.65 1.65 0 0 0 1 1.51 1.65 1.65 0 0 0 1.82-.33l.06-.06a2 2 0 0 1 2.83 0 2 2 0 0 1 0 2.83l-.06.06a1.65 1.65 0 0 0-.33 1.82V9a1.65 1.65 0 0 0 1.51 1H21a2 2 0 0 1 2 2 2 2 0 0 1-2 2h-.09a1.65 1.65 0 0 0-1.51 1z"></path>
      </svg>`,
      database: `<svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
        <ellipse cx="12" cy="5" rx="9" ry="3"></ellipse>
        <path d="M21 12c0 1.66-4 3-9 3s-9-1.34-9-3"></path>
        <path d="M3 5v14c0 1.66 4 3 9 3s9-1.34 9-3V5"></path>
      </svg>`,
      star: `<svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
        <polygon points="12,2 15.09,8.26 22,9.27 17,14.14 18.18,21.02 12,17.77 5.82,21.02 7,14.14 2,9.27 8.91,8.26"></polygon>
      </svg>`,
      request: `<svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
        <path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z"></path>
        <polyline points="14,2 14,8 20,8"></polyline>
        <line x1="16" y1="13" x2="8" y2="13"></line>
        <line x1="16" y1="17" x2="8" y2="17"></line>
        <polyline points="10,9 9,9 8,9"></polyline>
      </svg>`
    };
    const iconSvg = icons[iconName] || "";
    return this.sanitizer.bypassSecurityTrustHtml(iconSvg);
  }
  static \u0275fac = function SidebarComponent_Factory(__ngFactoryType__) {
    return new (__ngFactoryType__ || _SidebarComponent)(\u0275\u0275directiveInject(Router), \u0275\u0275directiveInject(DomSanitizer));
  };
  static \u0275cmp = /* @__PURE__ */ \u0275\u0275defineComponent({ type: _SidebarComponent, selectors: [["app-sidebar"]], hostBindings: function SidebarComponent_HostBindings(rf, ctx) {
    if (rf & 1) {
      \u0275\u0275listener("resize", function SidebarComponent_resize_HostBindingHandler($event) {
        return ctx.onResize($event);
      }, false, \u0275\u0275resolveWindow);
    }
  }, inputs: { isCollapsed: "isCollapsed" }, outputs: { toggleSidebar: "toggleSidebar", closeSidebar: "closeSidebar" }, decls: 15, vars: 11, consts: [["singleItem", ""], [1, "sidebar"], ["class", "sidebar-overlay", 3, "click", 4, "ngIf"], [1, "sidebar-header"], [1, "logo"], [1, "logo-icon"], ["width", "32", "height", "32", "viewBox", "0 0 24 24", "fill", "none", "stroke", "currentColor", "stroke-width", "2"], ["d", "M21 16V8a2 2 0 0 0-1-1.73l-7-4a2 2 0 0 0-2 0l-7 4A2 2 0 0 0 3 8v8a2 2 0 0 0 1 1.73l7 4a2 2 0 0 0 2 0l7-4A2 2 0 0 0 21 16z"], ["points", "3.27,6.96 12,12.01 20.73,6.96"], ["x1", "12", "y1", "22.08", "x2", "12", "y2", "12"], ["class", "logo-text", 4, "ngIf"], ["class", "close-btn", "type", "button", "aria-label", "Fermer le menu", 3, "click", 4, "ngIf"], [1, "sidebar-nav"], [1, "nav-list"], ["class", "nav-item", 4, "ngFor", "ngForOf"], ["class", "sidebar-footer", 4, "ngIf"], [1, "sidebar-overlay", 3, "click"], [1, "logo-text"], [1, "logo-title"], [1, "logo-subtitle"], ["type", "button", "aria-label", "Fermer le menu", 1, "close-btn", 3, "click"], ["width", "24", "height", "24", "viewBox", "0 0 24 24", "fill", "none", "stroke", "currentColor", "stroke-width", "2"], ["x1", "18", "y1", "6", "x2", "6", "y2", "18"], ["x1", "6", "y1", "6", "x2", "18", "y2", "18"], [1, "nav-item"], ["class", "nav-item-wrapper", 4, "ngIf", "ngIfElse"], [1, "nav-item-wrapper"], ["type", "button", 1, "nav-link", "parent-link", 3, "click"], [1, "nav-icon", 3, "innerHTML"], ["class", "nav-text", 4, "ngIf"], ["class", "nav-arrow", 4, "ngIf"], [1, "submenu"], ["class", "submenu-item", 4, "ngFor", "ngForOf"], [1, "nav-text"], [1, "nav-arrow"], ["width", "16", "height", "16", "viewBox", "0 0 24 24", "fill", "none", "stroke", "currentColor", "stroke-width", "2"], ["points", "6,9 12,15 18,9"], [1, "submenu-item"], ["type", "button", 1, "submenu-link", 3, "click"], [1, "submenu-icon", 3, "innerHTML"], [1, "submenu-text"], ["type", "button", 1, "nav-link", 3, "click"], [1, "sidebar-footer"], [1, "footer-content"], [1, "footer-text"], [1, "footer-copyright"]], template: function SidebarComponent_Template(rf, ctx) {
    if (rf & 1) {
      \u0275\u0275elementStart(0, "aside", 1);
      \u0275\u0275template(1, SidebarComponent_div_1_Template, 1, 0, "div", 2);
      \u0275\u0275elementStart(2, "div", 3)(3, "div", 4)(4, "div", 5);
      \u0275\u0275namespaceSVG();
      \u0275\u0275elementStart(5, "svg", 6);
      \u0275\u0275element(6, "path", 7)(7, "polyline", 8)(8, "line", 9);
      \u0275\u0275elementEnd()();
      \u0275\u0275template(9, SidebarComponent_div_9_Template, 5, 0, "div", 10);
      \u0275\u0275elementEnd();
      \u0275\u0275template(10, SidebarComponent_button_10_Template, 4, 0, "button", 11);
      \u0275\u0275elementEnd();
      \u0275\u0275namespaceHTML();
      \u0275\u0275elementStart(11, "nav", 12)(12, "ul", 13);
      \u0275\u0275template(13, SidebarComponent_li_13_Template, 4, 2, "li", 14);
      \u0275\u0275elementEnd()();
      \u0275\u0275template(14, SidebarComponent_div_14_Template, 6, 0, "div", 15);
      \u0275\u0275elementEnd();
    }
    if (rf & 2) {
      \u0275\u0275classProp("collapsed", ctx.isCollapsed)("mobile", ctx.isMobile)("tablet", ctx.isTablet);
      \u0275\u0275advance();
      \u0275\u0275property("ngIf", ctx.isMobile && !ctx.isCollapsed);
      \u0275\u0275advance(8);
      \u0275\u0275property("ngIf", !ctx.isCollapsed);
      \u0275\u0275advance();
      \u0275\u0275property("ngIf", ctx.isMobile && !ctx.isCollapsed);
      \u0275\u0275advance(3);
      \u0275\u0275property("ngForOf", ctx.menuItems);
      \u0275\u0275advance();
      \u0275\u0275property("ngIf", !ctx.isCollapsed);
    }
  }, dependencies: [CommonModule, NgForOf, NgIf, RouterModule], styles: [`

.sidebar[_ngcontent-%COMP%] {
  width: 280px;
  height: 100vh;
  background: var(--gradient-primary);
  color: var(--white);
  display: flex;
  flex-direction: column;
  position: fixed;
  left: 0;
  top: 0;
  z-index: 1000;
  transition: width 0.3s ease;
  overflow-y: auto;
  box-shadow: 0 4px 20px rgba(59, 130, 246, 0.3);
}
.sidebar.collapsed[_ngcontent-%COMP%] {
  width: 70px;
}
.sidebar-header[_ngcontent-%COMP%] {
  padding: var(--spacing-6);
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  display: flex;
  justify-content: space-between;
  align-items: center;
  position: relative;
}
.sidebar-header[_ngcontent-%COMP%]::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="dots" width="20" height="20" patternUnits="userSpaceOnUse"><circle cx="10" cy="10" r="1" fill="rgba(255,255,255,0.05)"/></pattern></defs><rect width="100" height="100" fill="url(%23dots)"/></svg>');
  opacity: 0.3;
}
.logo[_ngcontent-%COMP%] {
  display: flex;
  align-items: center;
  gap: var(--spacing-3);
  position: relative;
  z-index: 1;
}
.logo-icon[_ngcontent-%COMP%] {
  width: 48px;
  height: 48px;
  background:
    linear-gradient(
      135deg,
      var(--primary-400) 0%,
      var(--primary-300) 100%);
  border-radius: var(--border-radius-xl);
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
  box-shadow: var(--shadow-lg);
  border: 2px solid rgba(255, 255, 255, 0.2);
  animation: _ngcontent-%COMP%_float 3s ease-in-out infinite;
}
@keyframes _ngcontent-%COMP%_float {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-3px);
  }
}
.logo-icon[_ngcontent-%COMP%]   svg[_ngcontent-%COMP%] {
  color: var(--white);
  font-size: var(--font-size-xl);
}
.logo-text[_ngcontent-%COMP%] {
  display: flex;
  flex-direction: column;
  min-width: 0;
  position: relative;
  z-index: 1;
}
.logo-title[_ngcontent-%COMP%] {
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-extrabold);
  color: var(--white);
  line-height: var(--line-height-tight);
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}
.logo-subtitle[_ngcontent-%COMP%] {
  font-size: var(--font-size-xs);
  color: rgba(255, 255, 255, 0.8);
  line-height: var(--line-height-tight);
  font-weight: var(--font-weight-medium);
}
.close-btn[_ngcontent-%COMP%] {
  background: none;
  border: none;
  color: rgba(255, 255, 255, 0.8);
  cursor: pointer;
  padding: 8px;
  border-radius: 6px;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
}
.close-btn[_ngcontent-%COMP%]:hover {
  background: rgba(255, 255, 255, 0.1);
  color: white;
}
.sidebar-overlay[_ngcontent-%COMP%] {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  background: rgba(0, 0, 0, 0.5);
  z-index: 999;
  -webkit-backdrop-filter: blur(2px);
  backdrop-filter: blur(2px);
}
.sidebar-nav[_ngcontent-%COMP%] {
  flex: 1;
  padding: 20px 0;
  overflow-y: auto;
}
.nav-list[_ngcontent-%COMP%] {
  list-style: none;
  margin: 0;
  padding: 0;
}
.nav-item[_ngcontent-%COMP%] {
  margin-bottom: 4px;
}
.nav-item-wrapper[_ngcontent-%COMP%] {
  display: flex;
  flex-direction: column;
}
.nav-link[_ngcontent-%COMP%] {
  display: flex;
  align-items: center;
  padding: var(--spacing-3) var(--spacing-5);
  color: rgba(255, 255, 255, 0.9);
  text-decoration: none;
  transition: all var(--transition-base);
  border: none;
  background: none;
  width: 100%;
  text-align: left;
  cursor: pointer;
  position: relative;
  border-radius: var(--border-radius-lg);
  margin: var(--spacing-1) var(--spacing-3);
  font-weight: var(--font-weight-medium);
  overflow: hidden;
}
.nav-link[_ngcontent-%COMP%]::before {
  content: "";
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background:
    linear-gradient(
      90deg,
      transparent,
      rgba(255, 255, 255, 0.1),
      transparent);
  transition: var(--transition-slow);
}
.nav-link[_ngcontent-%COMP%]:hover::before {
  left: 100%;
}
.nav-link[_ngcontent-%COMP%]:hover {
  background: rgba(255, 255, 255, 0.15);
  color: var(--white);
  transform: translateX(4px);
  box-shadow: var(--shadow-md);
}
.nav-link.active[_ngcontent-%COMP%] {
  background: rgba(255, 255, 255, 0.2);
  color: var(--white);
  font-weight: var(--font-weight-semibold);
  box-shadow: var(--shadow-lg);
}
.nav-link.active[_ngcontent-%COMP%]::after {
  content: "";
  position: absolute;
  left: 0;
  top: 0;
  bottom: 0;
  width: 4px;
  background: var(--primary-200);
}
.parent-link[_ngcontent-%COMP%] {
  justify-content: space-between;
}
.parent-link.expanded[_ngcontent-%COMP%] {
  background: rgba(255, 255, 255, 0.05);
}
.nav-icon[_ngcontent-%COMP%] {
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
  font-size: var(--font-size-lg);
  transition: var(--transition-fast);
}
.nav-link[_ngcontent-%COMP%]:hover   .nav-icon[_ngcontent-%COMP%] {
  transform: scale(1.1);
}
.nav-text[_ngcontent-%COMP%] {
  margin-left: var(--spacing-3);
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
  flex: 1;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}
.nav-arrow[_ngcontent-%COMP%] {
  width: 20px;
  height: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: transform var(--transition-base);
  flex-shrink: 0;
  opacity: 0.7;
}
.parent-link.expanded[_ngcontent-%COMP%]   .nav-arrow[_ngcontent-%COMP%] {
  transform: rotate(180deg);
  opacity: 1;
}
.parent-link.expanded[_ngcontent-%COMP%]   .nav-arrow[_ngcontent-%COMP%] {
  transform: rotate(180deg);
}
.submenu[_ngcontent-%COMP%] {
  list-style: none;
  margin: 0;
  padding: 0;
  max-height: 0;
  overflow: hidden;
  transition: max-height 0.3s ease;
  background: rgba(0, 0, 0, 0.2);
}
.submenu.expanded[_ngcontent-%COMP%] {
  max-height: 300px;
}
.submenu-item[_ngcontent-%COMP%] {
  margin: 0;
}
.submenu-link[_ngcontent-%COMP%] {
  display: flex;
  align-items: center;
  padding: var(--spacing-3) var(--spacing-5) var(--spacing-3) var(--spacing-12);
  color: rgba(255, 255, 255, 0.95);
  text-decoration: none;
  transition: all var(--transition-base);
  font-size: var(--font-size-sm);
  position: relative;
  border-radius: var(--border-radius-lg);
  margin: var(--spacing-1) var(--spacing-3);
  font-weight: var(--font-weight-medium);
  background: rgba(255, 255, 255, 0.08);
  border-left: 3px solid rgba(255, 255, 255, 0.2);
}
.submenu-link[_ngcontent-%COMP%]::before {
  content: "";
  position: absolute;
  left: var(--spacing-6);
  top: 50%;
  transform: translateY(-50%);
  width: 4px;
  height: 4px;
  background: rgba(255, 255, 255, 0.4);
  border-radius: var(--border-radius-full);
  transition: var(--transition-fast);
}
.submenu-link[_ngcontent-%COMP%]:hover::before {
  background: var(--primary-200);
  transform: translateY(-50%) scale(1.5);
}
.submenu-link[_ngcontent-%COMP%]:hover {
  background: rgba(255, 255, 255, 0.2);
  color: var(--white);
  transform: translateX(6px);
  border-left-color: rgba(255, 255, 255, 0.6);
  box-shadow: var(--shadow-md);
}
.submenu-link.active[_ngcontent-%COMP%] {
  background: rgba(255, 255, 255, 0.25);
  color: var(--white);
  font-weight: var(--font-weight-semibold);
  border-left-color: var(--primary-200);
  box-shadow: var(--shadow-lg);
}
.submenu-link.active[_ngcontent-%COMP%]::after {
  content: "";
  position: absolute;
  left: 0;
  top: 0;
  bottom: 0;
  width: 3px;
  background: var(--primary-200);
}
.submenu-link.active[_ngcontent-%COMP%]::before {
  background: var(--primary-200);
  transform: translateY(-50%) scale(1.5);
}
.submenu-icon[_ngcontent-%COMP%] {
  width: 16px;
  height: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}
.submenu-text[_ngcontent-%COMP%] {
  margin-left: 8px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}
.sidebar-footer[_ngcontent-%COMP%] {
  padding: var(--spacing-5);
  border-top: 1px solid rgba(255, 255, 255, 0.1);
  margin-top: auto;
  background: rgba(0, 0, 0, 0.2);
  -webkit-backdrop-filter: blur(10px);
  backdrop-filter: blur(10px);
}
.footer-content[_ngcontent-%COMP%] {
  text-align: center;
  position: relative;
  z-index: 1;
}
.footer-text[_ngcontent-%COMP%] {
  font-size: var(--font-size-xs);
  color: rgba(255, 255, 255, 0.7);
  margin: 0 0 var(--spacing-1) 0;
  font-weight: var(--font-weight-medium);
}
.footer-copyright[_ngcontent-%COMP%] {
  font-size: var(--font-size-xs);
  color: rgba(255, 255, 255, 0.5);
  margin: 0;
  font-weight: var(--font-weight-normal);
}
.sidebar.collapsed[_ngcontent-%COMP%]   .nav-link[_ngcontent-%COMP%] {
  padding: 12px 25px;
  justify-content: center;
}
.sidebar.collapsed[_ngcontent-%COMP%]   .submenu[_ngcontent-%COMP%] {
  display: none;
}
.sidebar.collapsed[_ngcontent-%COMP%]   .nav-arrow[_ngcontent-%COMP%] {
  display: none;
}
.sidebar-nav[_ngcontent-%COMP%]::-webkit-scrollbar {
  width: 4px;
}
.sidebar-nav[_ngcontent-%COMP%]::-webkit-scrollbar-track {
  background: rgba(255, 255, 255, 0.1);
}
.sidebar-nav[_ngcontent-%COMP%]::-webkit-scrollbar-thumb {
  background: rgba(255, 255, 255, 0.3);
  border-radius: 2px;
}
.sidebar-nav[_ngcontent-%COMP%]::-webkit-scrollbar-thumb:hover {
  background: rgba(255, 255, 255, 0.5);
}
@media (max-width: 767px) {
  .sidebar[_ngcontent-%COMP%] {
    width: 280px;
    transform: translateX(-100%);
    transition: transform 0.3s ease, width 0.3s ease;
    box-shadow: 2px 0 10px rgba(0, 0, 0, 0.3);
  }
  .sidebar[_ngcontent-%COMP%]:not(.collapsed) {
    transform: translateX(0);
  }
  .sidebar.collapsed[_ngcontent-%COMP%] {
    transform: translateX(-100%);
    width: 280px;
  }
  .sidebar-header[_ngcontent-%COMP%] {
    padding: 16px 20px;
  }
  .logo-title[_ngcontent-%COMP%] {
    font-size: 16px;
  }
  .nav-link[_ngcontent-%COMP%] {
    padding: 14px 20px;
    font-size: 15px;
  }
  .nav-text[_ngcontent-%COMP%] {
    font-size: 15px;
  }
  .submenu-link[_ngcontent-%COMP%] {
    padding: 12px 20px 12px 52px;
    font-size: 14px;
  }
}
@media (min-width: 768px) and (max-width: 1023px) {
  .sidebar[_ngcontent-%COMP%] {
    width: 240px;
  }
  .sidebar.collapsed[_ngcontent-%COMP%] {
    width: 60px;
  }
  .sidebar-header[_ngcontent-%COMP%] {
    padding: 16px;
  }
  .logo-title[_ngcontent-%COMP%] {
    font-size: 16px;
  }
  .nav-link[_ngcontent-%COMP%] {
    padding: 10px 16px;
  }
  .nav-text[_ngcontent-%COMP%] {
    font-size: 13px;
  }
  .submenu-link[_ngcontent-%COMP%] {
    padding: 8px 16px 8px 44px;
    font-size: 12px;
  }
  .sidebar.collapsed[_ngcontent-%COMP%]   .nav-link[_ngcontent-%COMP%] {
    padding: 10px 20px;
  }
}
@media (min-width: 1024px) {
  .sidebar[_ngcontent-%COMP%] {
    width: 280px;
  }
  .sidebar.collapsed[_ngcontent-%COMP%] {
    width: 70px;
  }
}
@media (max-width: 767px) {
  .nav-link[_ngcontent-%COMP%], 
   .submenu-link[_ngcontent-%COMP%] {
    border: none;
    background: none;
    width: 100%;
    text-align: left;
    cursor: pointer;
    font-family: inherit;
  }
  .nav-link[_ngcontent-%COMP%]:focus, 
   .submenu-link[_ngcontent-%COMP%]:focus {
    outline: 2px solid rgba(255, 255, 255, 0.3);
    outline-offset: -2px;
  }
}
@media (prefers-reduced-motion: no-preference) {
  .sidebar[_ngcontent-%COMP%] {
    transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1), width 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  }
  .submenu[_ngcontent-%COMP%] {
    transition: max-height 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  }
  .nav-arrow[_ngcontent-%COMP%] {
    transition: transform 0.2s cubic-bezier(0.4, 0, 0.2, 1);
  }
}
/*# sourceMappingURL=sidebar.component.css.map */`] });
};
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && setClassMetadata(SidebarComponent, [{
    type: Component,
    args: [{ selector: "app-sidebar", standalone: true, imports: [CommonModule, RouterModule], template: '<aside class="sidebar"\n       [class.collapsed]="isCollapsed"\n       [class.mobile]="isMobile"\n       [class.tablet]="isTablet">\n\n  <!-- Overlay pour mobile -->\n  <div class="sidebar-overlay"\n       *ngIf="isMobile && !isCollapsed"\n       (click)="onCloseSidebar()"></div>\n\n  <div class="sidebar-header">\n    <div class="logo">\n      <div class="logo-icon">\n        <svg width="32" height="32" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">\n          <path d="M21 16V8a2 2 0 0 0-1-1.73l-7-4a2 2 0 0 0-2 0l-7 4A2 2 0 0 0 3 8v8a2 2 0 0 0 1 1.73l7 4a2 2 0 0 0 2 0l7-4A2 2 0 0 0 21 16z"></path>\n          <polyline points="3.27,6.96 12,12.01 20.73,6.96"></polyline>\n          <line x1="12" y1="22.08" x2="12" y2="12"></line>\n        </svg>\n      </div>\n      <div class="logo-text" *ngIf="!isCollapsed">\n        <span class="logo-title">FournisseurApp</span>\n        <span class="logo-subtitle">Gestion</span>\n      </div>\n    </div>\n\n    <!-- Bouton de fermeture pour mobile -->\n    <button class="close-btn"\n            *ngIf="isMobile && !isCollapsed"\n            (click)="onCloseSidebar()"\n            type="button"\n            aria-label="Fermer le menu">\n      <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">\n        <line x1="18" y1="6" x2="6" y2="18"></line>\n        <line x1="6" y1="6" x2="18" y2="18"></line>\n      </svg>\n    </button>\n  </div>\n\n  <nav class="sidebar-nav">\n    <ul class="nav-list">\n      <li class="nav-item" *ngFor="let item of menuItems">\n        <!-- Menu item with children -->\n        <div *ngIf="item.children; else singleItem" class="nav-item-wrapper">\n          <button \n            class="nav-link parent-link"\n            [class.active]="isParentActive(item)"\n            [class.expanded]="item.expanded"\n            (click)="toggleSubmenu(item)"\n            type="button"\n          >\n            <span class="nav-icon" [innerHTML]="getIconSvg(item.icon)"></span>\n            <span class="nav-text" *ngIf="!isCollapsed">{{ item.label }}</span>\n            <span class="nav-arrow" *ngIf="!isCollapsed">\n              <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">\n                <polyline points="6,9 12,15 18,9"></polyline>\n              </svg>\n            </span>\n          </button>\n          \n          <ul class="submenu" [class.expanded]="item.expanded && !isCollapsed">\n            <li class="submenu-item" *ngFor="let child of item.children">\n              <button\n                class="submenu-link"\n                [class.active]="isActiveRoute(child.route!)"\n                (click)="navigateTo(child.route!)"\n                type="button"\n              >\n                <span class="submenu-icon" [innerHTML]="getIconSvg(child.icon)"></span>\n                <span class="submenu-text">{{ child.label }}</span>\n              </button>\n            </li>\n          </ul>\n        </div>\n\n        <!-- Single menu item -->\n        <ng-template #singleItem>\n          <button\n            class="nav-link"\n            [class.active]="isActiveRoute(item.route!)"\n            (click)="navigateTo(item.route!)"\n            type="button"\n          >\n            <span class="nav-icon" [innerHTML]="getIconSvg(item.icon)"></span>\n            <span class="nav-text" *ngIf="!isCollapsed">{{ item.label }}</span>\n          </button>\n        </ng-template>\n      </li>\n    </ul>\n  </nav>\n\n  <div class="sidebar-footer" *ngIf="!isCollapsed">\n    <div class="footer-content">\n      <p class="footer-text">Version 1.0.0</p>\n      <p class="footer-copyright">\xA9 2024 FournisseurApp</p>\n    </div>\n  </div>\n</aside>\n', styles: [`/* src/app/components/layout/sidebar/sidebar.component.css */
.sidebar {
  width: 280px;
  height: 100vh;
  background: var(--gradient-primary);
  color: var(--white);
  display: flex;
  flex-direction: column;
  position: fixed;
  left: 0;
  top: 0;
  z-index: 1000;
  transition: width 0.3s ease;
  overflow-y: auto;
  box-shadow: 0 4px 20px rgba(59, 130, 246, 0.3);
}
.sidebar.collapsed {
  width: 70px;
}
.sidebar-header {
  padding: var(--spacing-6);
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  display: flex;
  justify-content: space-between;
  align-items: center;
  position: relative;
}
.sidebar-header::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="dots" width="20" height="20" patternUnits="userSpaceOnUse"><circle cx="10" cy="10" r="1" fill="rgba(255,255,255,0.05)"/></pattern></defs><rect width="100" height="100" fill="url(%23dots)"/></svg>');
  opacity: 0.3;
}
.logo {
  display: flex;
  align-items: center;
  gap: var(--spacing-3);
  position: relative;
  z-index: 1;
}
.logo-icon {
  width: 48px;
  height: 48px;
  background:
    linear-gradient(
      135deg,
      var(--primary-400) 0%,
      var(--primary-300) 100%);
  border-radius: var(--border-radius-xl);
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
  box-shadow: var(--shadow-lg);
  border: 2px solid rgba(255, 255, 255, 0.2);
  animation: float 3s ease-in-out infinite;
}
@keyframes float {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-3px);
  }
}
.logo-icon svg {
  color: var(--white);
  font-size: var(--font-size-xl);
}
.logo-text {
  display: flex;
  flex-direction: column;
  min-width: 0;
  position: relative;
  z-index: 1;
}
.logo-title {
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-extrabold);
  color: var(--white);
  line-height: var(--line-height-tight);
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}
.logo-subtitle {
  font-size: var(--font-size-xs);
  color: rgba(255, 255, 255, 0.8);
  line-height: var(--line-height-tight);
  font-weight: var(--font-weight-medium);
}
.close-btn {
  background: none;
  border: none;
  color: rgba(255, 255, 255, 0.8);
  cursor: pointer;
  padding: 8px;
  border-radius: 6px;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
}
.close-btn:hover {
  background: rgba(255, 255, 255, 0.1);
  color: white;
}
.sidebar-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  background: rgba(0, 0, 0, 0.5);
  z-index: 999;
  -webkit-backdrop-filter: blur(2px);
  backdrop-filter: blur(2px);
}
.sidebar-nav {
  flex: 1;
  padding: 20px 0;
  overflow-y: auto;
}
.nav-list {
  list-style: none;
  margin: 0;
  padding: 0;
}
.nav-item {
  margin-bottom: 4px;
}
.nav-item-wrapper {
  display: flex;
  flex-direction: column;
}
.nav-link {
  display: flex;
  align-items: center;
  padding: var(--spacing-3) var(--spacing-5);
  color: rgba(255, 255, 255, 0.9);
  text-decoration: none;
  transition: all var(--transition-base);
  border: none;
  background: none;
  width: 100%;
  text-align: left;
  cursor: pointer;
  position: relative;
  border-radius: var(--border-radius-lg);
  margin: var(--spacing-1) var(--spacing-3);
  font-weight: var(--font-weight-medium);
  overflow: hidden;
}
.nav-link::before {
  content: "";
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background:
    linear-gradient(
      90deg,
      transparent,
      rgba(255, 255, 255, 0.1),
      transparent);
  transition: var(--transition-slow);
}
.nav-link:hover::before {
  left: 100%;
}
.nav-link:hover {
  background: rgba(255, 255, 255, 0.15);
  color: var(--white);
  transform: translateX(4px);
  box-shadow: var(--shadow-md);
}
.nav-link.active {
  background: rgba(255, 255, 255, 0.2);
  color: var(--white);
  font-weight: var(--font-weight-semibold);
  box-shadow: var(--shadow-lg);
}
.nav-link.active::after {
  content: "";
  position: absolute;
  left: 0;
  top: 0;
  bottom: 0;
  width: 4px;
  background: var(--primary-200);
}
.parent-link {
  justify-content: space-between;
}
.parent-link.expanded {
  background: rgba(255, 255, 255, 0.05);
}
.nav-icon {
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
  font-size: var(--font-size-lg);
  transition: var(--transition-fast);
}
.nav-link:hover .nav-icon {
  transform: scale(1.1);
}
.nav-text {
  margin-left: var(--spacing-3);
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
  flex: 1;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}
.nav-arrow {
  width: 20px;
  height: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: transform var(--transition-base);
  flex-shrink: 0;
  opacity: 0.7;
}
.parent-link.expanded .nav-arrow {
  transform: rotate(180deg);
  opacity: 1;
}
.parent-link.expanded .nav-arrow {
  transform: rotate(180deg);
}
.submenu {
  list-style: none;
  margin: 0;
  padding: 0;
  max-height: 0;
  overflow: hidden;
  transition: max-height 0.3s ease;
  background: rgba(0, 0, 0, 0.2);
}
.submenu.expanded {
  max-height: 300px;
}
.submenu-item {
  margin: 0;
}
.submenu-link {
  display: flex;
  align-items: center;
  padding: var(--spacing-3) var(--spacing-5) var(--spacing-3) var(--spacing-12);
  color: rgba(255, 255, 255, 0.95);
  text-decoration: none;
  transition: all var(--transition-base);
  font-size: var(--font-size-sm);
  position: relative;
  border-radius: var(--border-radius-lg);
  margin: var(--spacing-1) var(--spacing-3);
  font-weight: var(--font-weight-medium);
  background: rgba(255, 255, 255, 0.08);
  border-left: 3px solid rgba(255, 255, 255, 0.2);
}
.submenu-link::before {
  content: "";
  position: absolute;
  left: var(--spacing-6);
  top: 50%;
  transform: translateY(-50%);
  width: 4px;
  height: 4px;
  background: rgba(255, 255, 255, 0.4);
  border-radius: var(--border-radius-full);
  transition: var(--transition-fast);
}
.submenu-link:hover::before {
  background: var(--primary-200);
  transform: translateY(-50%) scale(1.5);
}
.submenu-link:hover {
  background: rgba(255, 255, 255, 0.2);
  color: var(--white);
  transform: translateX(6px);
  border-left-color: rgba(255, 255, 255, 0.6);
  box-shadow: var(--shadow-md);
}
.submenu-link.active {
  background: rgba(255, 255, 255, 0.25);
  color: var(--white);
  font-weight: var(--font-weight-semibold);
  border-left-color: var(--primary-200);
  box-shadow: var(--shadow-lg);
}
.submenu-link.active::after {
  content: "";
  position: absolute;
  left: 0;
  top: 0;
  bottom: 0;
  width: 3px;
  background: var(--primary-200);
}
.submenu-link.active::before {
  background: var(--primary-200);
  transform: translateY(-50%) scale(1.5);
}
.submenu-icon {
  width: 16px;
  height: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}
.submenu-text {
  margin-left: 8px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}
.sidebar-footer {
  padding: var(--spacing-5);
  border-top: 1px solid rgba(255, 255, 255, 0.1);
  margin-top: auto;
  background: rgba(0, 0, 0, 0.2);
  -webkit-backdrop-filter: blur(10px);
  backdrop-filter: blur(10px);
}
.footer-content {
  text-align: center;
  position: relative;
  z-index: 1;
}
.footer-text {
  font-size: var(--font-size-xs);
  color: rgba(255, 255, 255, 0.7);
  margin: 0 0 var(--spacing-1) 0;
  font-weight: var(--font-weight-medium);
}
.footer-copyright {
  font-size: var(--font-size-xs);
  color: rgba(255, 255, 255, 0.5);
  margin: 0;
  font-weight: var(--font-weight-normal);
}
.sidebar.collapsed .nav-link {
  padding: 12px 25px;
  justify-content: center;
}
.sidebar.collapsed .submenu {
  display: none;
}
.sidebar.collapsed .nav-arrow {
  display: none;
}
.sidebar-nav::-webkit-scrollbar {
  width: 4px;
}
.sidebar-nav::-webkit-scrollbar-track {
  background: rgba(255, 255, 255, 0.1);
}
.sidebar-nav::-webkit-scrollbar-thumb {
  background: rgba(255, 255, 255, 0.3);
  border-radius: 2px;
}
.sidebar-nav::-webkit-scrollbar-thumb:hover {
  background: rgba(255, 255, 255, 0.5);
}
@media (max-width: 767px) {
  .sidebar {
    width: 280px;
    transform: translateX(-100%);
    transition: transform 0.3s ease, width 0.3s ease;
    box-shadow: 2px 0 10px rgba(0, 0, 0, 0.3);
  }
  .sidebar:not(.collapsed) {
    transform: translateX(0);
  }
  .sidebar.collapsed {
    transform: translateX(-100%);
    width: 280px;
  }
  .sidebar-header {
    padding: 16px 20px;
  }
  .logo-title {
    font-size: 16px;
  }
  .nav-link {
    padding: 14px 20px;
    font-size: 15px;
  }
  .nav-text {
    font-size: 15px;
  }
  .submenu-link {
    padding: 12px 20px 12px 52px;
    font-size: 14px;
  }
}
@media (min-width: 768px) and (max-width: 1023px) {
  .sidebar {
    width: 240px;
  }
  .sidebar.collapsed {
    width: 60px;
  }
  .sidebar-header {
    padding: 16px;
  }
  .logo-title {
    font-size: 16px;
  }
  .nav-link {
    padding: 10px 16px;
  }
  .nav-text {
    font-size: 13px;
  }
  .submenu-link {
    padding: 8px 16px 8px 44px;
    font-size: 12px;
  }
  .sidebar.collapsed .nav-link {
    padding: 10px 20px;
  }
}
@media (min-width: 1024px) {
  .sidebar {
    width: 280px;
  }
  .sidebar.collapsed {
    width: 70px;
  }
}
@media (max-width: 767px) {
  .nav-link,
  .submenu-link {
    border: none;
    background: none;
    width: 100%;
    text-align: left;
    cursor: pointer;
    font-family: inherit;
  }
  .nav-link:focus,
  .submenu-link:focus {
    outline: 2px solid rgba(255, 255, 255, 0.3);
    outline-offset: -2px;
  }
}
@media (prefers-reduced-motion: no-preference) {
  .sidebar {
    transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1), width 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  }
  .submenu {
    transition: max-height 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  }
  .nav-arrow {
    transition: transform 0.2s cubic-bezier(0.4, 0, 0.2, 1);
  }
}
/*# sourceMappingURL=sidebar.component.css.map */
`] }]
  }], () => [{ type: Router }, { type: DomSanitizer }], { isCollapsed: [{
    type: Input
  }], toggleSidebar: [{
    type: Output
  }], closeSidebar: [{
    type: Output
  }], onResize: [{
    type: HostListener,
    args: ["window:resize", ["$event"]]
  }] });
})();
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && \u0275setClassDebugInfo(SidebarComponent, { className: "SidebarComponent", filePath: "src/app/components/layout/sidebar/sidebar.component.ts", lineNumber: 21 });
})();

// src/app/components/layout/dashboard-layout/dashboard-layout.component.ts
var DashboardLayoutComponent = class _DashboardLayoutComponent {
  isSidebarCollapsed = false;
  isMobile = false;
  showMobileSidebar = false;
  ngOnInit() {
    this.checkScreenSize();
  }
  onResize(event) {
    this.checkScreenSize();
  }
  checkScreenSize() {
    this.isMobile = window.innerWidth < 768;
    if (this.isMobile) {
      this.isSidebarCollapsed = false;
      this.showMobileSidebar = false;
    }
  }
  onToggleSidebar() {
    if (this.isMobile) {
      this.showMobileSidebar = !this.showMobileSidebar;
    } else {
      this.isSidebarCollapsed = !this.isSidebarCollapsed;
    }
  }
  onCloseMobileSidebar() {
    if (this.isMobile) {
      this.showMobileSidebar = false;
    }
  }
  static \u0275fac = function DashboardLayoutComponent_Factory(__ngFactoryType__) {
    return new (__ngFactoryType__ || _DashboardLayoutComponent)();
  };
  static \u0275cmp = /* @__PURE__ */ \u0275\u0275defineComponent({ type: _DashboardLayoutComponent, selectors: [["app-dashboard-layout"]], hostBindings: function DashboardLayoutComponent_HostBindings(rf, ctx) {
    if (rf & 1) {
      \u0275\u0275listener("resize", function DashboardLayoutComponent_resize_HostBindingHandler($event) {
        return ctx.onResize($event);
      }, false, \u0275\u0275resolveWindow);
    }
  }, decls: 9, vars: 7, consts: [[1, "dashboard-layout"], [1, "mobile-overlay", 3, "click"], [1, "sidebar-container"], [3, "toggleSidebar", "closeSidebar", "isCollapsed"], [1, "main-container"], [3, "toggleSidebar"], [1, "main-content"], [1, "content-wrapper"]], template: function DashboardLayoutComponent_Template(rf, ctx) {
    if (rf & 1) {
      \u0275\u0275elementStart(0, "div", 0)(1, "div", 1);
      \u0275\u0275listener("click", function DashboardLayoutComponent_Template_div_click_1_listener() {
        return ctx.onCloseMobileSidebar();
      });
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(2, "div", 2)(3, "app-sidebar", 3);
      \u0275\u0275listener("toggleSidebar", function DashboardLayoutComponent_Template_app_sidebar_toggleSidebar_3_listener() {
        return ctx.onToggleSidebar();
      })("closeSidebar", function DashboardLayoutComponent_Template_app_sidebar_closeSidebar_3_listener() {
        return ctx.onCloseMobileSidebar();
      });
      \u0275\u0275elementEnd()();
      \u0275\u0275elementStart(4, "div", 4)(5, "app-header", 5);
      \u0275\u0275listener("toggleSidebar", function DashboardLayoutComponent_Template_app_header_toggleSidebar_5_listener() {
        return ctx.onToggleSidebar();
      });
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(6, "main", 6)(7, "div", 7);
      \u0275\u0275element(8, "router-outlet");
      \u0275\u0275elementEnd()()()();
    }
    if (rf & 2) {
      \u0275\u0275advance();
      \u0275\u0275classProp("show", ctx.showMobileSidebar && ctx.isMobile);
      \u0275\u0275advance();
      \u0275\u0275classProp("mobile-show", ctx.showMobileSidebar && ctx.isMobile);
      \u0275\u0275advance();
      \u0275\u0275property("isCollapsed", ctx.isMobile ? !ctx.showMobileSidebar : ctx.isSidebarCollapsed);
      \u0275\u0275advance();
      \u0275\u0275classProp("sidebar-collapsed", ctx.isSidebarCollapsed && !ctx.isMobile);
    }
  }, dependencies: [CommonModule, RouterOutlet, HeaderComponent, SidebarComponent], styles: ["\n\n.dashboard-layout[_ngcontent-%COMP%] {\n  display: flex;\n  height: 100vh;\n  overflow: hidden;\n}\n.mobile-overlay[_ngcontent-%COMP%] {\n  position: fixed;\n  top: 0;\n  left: 0;\n  right: 0;\n  bottom: 0;\n  background: rgba(0, 0, 0, 0.5);\n  z-index: 999;\n  opacity: 0;\n  visibility: hidden;\n  transition: all 0.3s ease;\n}\n.mobile-overlay.show[_ngcontent-%COMP%] {\n  opacity: 1;\n  visibility: visible;\n}\n.sidebar-container[_ngcontent-%COMP%] {\n  position: relative;\n  z-index: 1000;\n}\n.main-container[_ngcontent-%COMP%] {\n  flex: 1;\n  display: flex;\n  flex-direction: column;\n  margin-left: 280px;\n  transition: margin-left 0.3s ease;\n  min-width: 0;\n}\n.main-container.sidebar-collapsed[_ngcontent-%COMP%] {\n  margin-left: 70px;\n}\n.main-content[_ngcontent-%COMP%] {\n  flex: 1;\n  overflow-y: auto;\n  background: #f8fafc;\n}\n.content-wrapper[_ngcontent-%COMP%] {\n  padding: 24px;\n  min-height: calc(100vh - 64px);\n}\n@media (max-width: 767px) {\n  .dashboard-layout[_ngcontent-%COMP%] {\n    position: relative;\n  }\n  .sidebar-container[_ngcontent-%COMP%] {\n    position: fixed;\n    top: 0;\n    left: 0;\n    height: 100vh;\n    transform: translateX(-100%);\n    transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1);\n    z-index: 1000;\n  }\n  .sidebar-container.mobile-show[_ngcontent-%COMP%] {\n    transform: translateX(0);\n  }\n  .main-container[_ngcontent-%COMP%] {\n    margin-left: 0;\n    width: 100%;\n  }\n  .main-container.sidebar-collapsed[_ngcontent-%COMP%] {\n    margin-left: 0;\n  }\n  .content-wrapper[_ngcontent-%COMP%] {\n    padding: 16px;\n    min-height: calc(100vh - 56px);\n  }\n}\n@media (min-width: 768px) and (max-width: 1023px) {\n  .main-container[_ngcontent-%COMP%] {\n    margin-left: 240px;\n  }\n  .main-container.sidebar-collapsed[_ngcontent-%COMP%] {\n    margin-left: 60px;\n  }\n  .content-wrapper[_ngcontent-%COMP%] {\n    padding: 20px;\n    min-height: calc(100vh - 60px);\n  }\n}\n@media (min-width: 1024px) {\n  .main-container[_ngcontent-%COMP%] {\n    margin-left: 280px;\n  }\n  .main-container.sidebar-collapsed[_ngcontent-%COMP%] {\n    margin-left: 70px;\n  }\n  .content-wrapper[_ngcontent-%COMP%] {\n    padding: 24px;\n    min-height: calc(100vh - 64px);\n  }\n}\n.main-content[_ngcontent-%COMP%]::-webkit-scrollbar {\n  width: 6px;\n}\n.main-content[_ngcontent-%COMP%]::-webkit-scrollbar-track {\n  background: #f1f5f9;\n}\n.main-content[_ngcontent-%COMP%]::-webkit-scrollbar-thumb {\n  background: #cbd5e1;\n  border-radius: 3px;\n}\n.main-content[_ngcontent-%COMP%]::-webkit-scrollbar-thumb:hover {\n  background: #94a3b8;\n}\n/*# sourceMappingURL=dashboard-layout.component.css.map */"] });
};
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && setClassMetadata(DashboardLayoutComponent, [{
    type: Component,
    args: [{ selector: "app-dashboard-layout", standalone: true, imports: [CommonModule, RouterOutlet, HeaderComponent, SidebarComponent], template: '<div class="dashboard-layout">\n  <!-- Mobile Overlay -->\n  <div \n    class="mobile-overlay" \n    [class.show]="showMobileSidebar && isMobile"\n    (click)="onCloseMobileSidebar()"\n  ></div>\n\n  <!-- Sidebar -->\n  <div class="sidebar-container" [class.mobile-show]="showMobileSidebar && isMobile">\n    <app-sidebar\n      [isCollapsed]="isMobile ? !showMobileSidebar : isSidebarCollapsed"\n      (toggleSidebar)="onToggleSidebar()"\n      (closeSidebar)="onCloseMobileSidebar()"\n    ></app-sidebar>\n  </div>\n\n  <!-- Main Content Area -->\n  <div class="main-container" [class.sidebar-collapsed]="isSidebarCollapsed && !isMobile">\n    <!-- Header -->\n    <app-header (toggleSidebar)="onToggleSidebar()"></app-header>\n\n    <!-- Page Content -->\n    <main class="main-content">\n      <div class="content-wrapper">\n        <router-outlet></router-outlet>\n      </div>\n    </main>\n  </div>\n</div>\n', styles: ["/* src/app/components/layout/dashboard-layout/dashboard-layout.component.css */\n.dashboard-layout {\n  display: flex;\n  height: 100vh;\n  overflow: hidden;\n}\n.mobile-overlay {\n  position: fixed;\n  top: 0;\n  left: 0;\n  right: 0;\n  bottom: 0;\n  background: rgba(0, 0, 0, 0.5);\n  z-index: 999;\n  opacity: 0;\n  visibility: hidden;\n  transition: all 0.3s ease;\n}\n.mobile-overlay.show {\n  opacity: 1;\n  visibility: visible;\n}\n.sidebar-container {\n  position: relative;\n  z-index: 1000;\n}\n.main-container {\n  flex: 1;\n  display: flex;\n  flex-direction: column;\n  margin-left: 280px;\n  transition: margin-left 0.3s ease;\n  min-width: 0;\n}\n.main-container.sidebar-collapsed {\n  margin-left: 70px;\n}\n.main-content {\n  flex: 1;\n  overflow-y: auto;\n  background: #f8fafc;\n}\n.content-wrapper {\n  padding: 24px;\n  min-height: calc(100vh - 64px);\n}\n@media (max-width: 767px) {\n  .dashboard-layout {\n    position: relative;\n  }\n  .sidebar-container {\n    position: fixed;\n    top: 0;\n    left: 0;\n    height: 100vh;\n    transform: translateX(-100%);\n    transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1);\n    z-index: 1000;\n  }\n  .sidebar-container.mobile-show {\n    transform: translateX(0);\n  }\n  .main-container {\n    margin-left: 0;\n    width: 100%;\n  }\n  .main-container.sidebar-collapsed {\n    margin-left: 0;\n  }\n  .content-wrapper {\n    padding: 16px;\n    min-height: calc(100vh - 56px);\n  }\n}\n@media (min-width: 768px) and (max-width: 1023px) {\n  .main-container {\n    margin-left: 240px;\n  }\n  .main-container.sidebar-collapsed {\n    margin-left: 60px;\n  }\n  .content-wrapper {\n    padding: 20px;\n    min-height: calc(100vh - 60px);\n  }\n}\n@media (min-width: 1024px) {\n  .main-container {\n    margin-left: 280px;\n  }\n  .main-container.sidebar-collapsed {\n    margin-left: 70px;\n  }\n  .content-wrapper {\n    padding: 24px;\n    min-height: calc(100vh - 64px);\n  }\n}\n.main-content::-webkit-scrollbar {\n  width: 6px;\n}\n.main-content::-webkit-scrollbar-track {\n  background: #f1f5f9;\n}\n.main-content::-webkit-scrollbar-thumb {\n  background: #cbd5e1;\n  border-radius: 3px;\n}\n.main-content::-webkit-scrollbar-thumb:hover {\n  background: #94a3b8;\n}\n/*# sourceMappingURL=dashboard-layout.component.css.map */\n"] }]
  }], null, { onResize: [{
    type: HostListener,
    args: ["window:resize", ["$event"]]
  }] });
})();
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && \u0275setClassDebugInfo(DashboardLayoutComponent, { className: "DashboardLayoutComponent", filePath: "src/app/components/layout/dashboard-layout/dashboard-layout.component.ts", lineNumber: 14 });
})();
export {
  DashboardLayoutComponent
};
//# sourceMappingURL=chunk-LZNEVSC5.js.map

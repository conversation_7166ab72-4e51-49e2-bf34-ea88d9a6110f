import {
  NotificationService
} from "./chunk-2WHFEWR5.js";
import {
  DemandeService,
  StatutDemande
} from "./chunk-WQ24PYVH.js";
import {
  AuthService
} from "./chunk-ZSXXNL7U.js";
import {
  AdminAuthService
} from "./chunk-2RV3R4JN.js";
import {
  Router
} from "./chunk-6BVUYNW4.js";
import {
  environment
} from "./chunk-GFHHPDQ6.js";
import {
  HttpClient
} from "./chunk-7JDDWGD3.js";
import {
  BehaviorSubject,
  CommonModule,
  Component,
  Injectable,
  NgForOf,
  NgIf,
  Subscription,
  forkJoin,
  interval,
  setClassMetadata,
  ɵsetClassDebugInfo,
  ɵɵadvance,
  ɵɵclassProp,
  ɵɵdefineComponent,
  ɵɵdefineInjectable,
  ɵɵdirectiveInject,
  ɵɵelement,
  ɵɵelementEnd,
  ɵɵelementStart,
  ɵɵgetCurrentView,
  ɵɵinject,
  ɵɵlistener,
  ɵɵnamespaceHTML,
  ɵɵnamespaceSVG,
  ɵɵnextContext,
  ɵɵproperty,
  ɵɵresetView,
  ɵɵrestoreView,
  ɵɵtemplate,
  ɵɵtext,
  ɵɵtextInterpolate,
  ɵɵtextInterpolate1
} from "./chunk-UBZQS7JS.js";

// src/app/services/admin-notification.service.ts
var AdminNotificationService = class _AdminNotificationService {
  http;
  apiUrl = `${environment.apiUrl}`;
  notificationsSubject = new BehaviorSubject([]);
  notifications$ = this.notificationsSubject.asObservable();
  unreadCountSubject = new BehaviorSubject(0);
  unreadCount$ = this.unreadCountSubject.asObservable();
  constructor(http) {
    this.http = http;
    interval(12e4).subscribe(() => {
      if (document.visibilityState === "visible") {
        this.loadNotifications();
      }
    });
  }
  loadNotifications() {
    this.getAdminNotifications().subscribe({
      next: (notifications) => {
        this.notificationsSubject.next(notifications);
        const unreadCount = notifications.filter((n) => !n.estLue).length;
        this.unreadCountSubject.next(unreadCount);
      },
      error: (error) => {
        console.error("Erreur lors du chargement des notifications admin:", error);
      }
    });
  }
  getAdminNotifications() {
    return this.http.get(`${this.apiUrl}/admin/notifications`);
  }
  getDemandesNotifications() {
    return this.http.get(`${this.apiUrl}/admin/notifications/demandes`);
  }
  markAsRead(notificationId) {
    return this.http.put(`${this.apiUrl}/admin/notifications/${notificationId}/read`, {});
  }
  markAllAsRead() {
    return this.http.put(`${this.apiUrl}/admin/notifications/mark-all-read`, {});
  }
  deleteNotification(notificationId) {
    return this.http.delete(`${this.apiUrl}/admin/notifications/${notificationId}`);
  }
  // Méthodes pour créer des notifications spécifiques
  createDemandeNotification(demande, type) {
    const typeText = type === "CATEGORIE" ? "cat\xE9gorie" : "sous-cat\xE9gorie";
    return {
      id: Date.now(),
      // Temporaire
      type: type === "CATEGORIE" ? "DEMANDE_CATEGORIE" : "DEMANDE_SOUS_CATEGORIE",
      titre: `Nouvelle demande de ${typeText}`,
      message: `Le fournisseur ${demande.fournisseurRaisonSociale} souhaite cr\xE9er une nouvelle ${typeText} nomm\xE9e "${demande.nom}"`,
      fournisseurNom: demande.fournisseurRaisonSociale,
      fournisseurId: demande.fournisseurId,
      referenceId: demande.id,
      dateCreation: new Date(demande.dateCreation),
      estLue: false,
      priority: "HIGH",
      actionUrl: `/admin/dashboard/demandes`
    };
  }
  // Simuler des notifications pour test (à supprimer en production)
  getMockNotifications() {
    return [
      {
        id: 1,
        type: "DEMANDE_CATEGORIE",
        titre: "Nouvelle demande de cat\xE9gorie",
        message: 'Le fournisseur Optique Vision Plus souhaite cr\xE9er une nouvelle cat\xE9gorie nomm\xE9e "Lunettes de Sport"',
        fournisseurNom: "Optique Vision Plus",
        fournisseurId: 11,
        referenceId: 1,
        dateCreation: /* @__PURE__ */ new Date(),
        estLue: false,
        priority: "HIGH",
        actionUrl: "/admin/dashboard/demandes"
      },
      {
        id: 2,
        type: "DEMANDE_SOUS_CATEGORIE",
        titre: "Nouvelle demande de sous-cat\xE9gorie",
        message: 'Le fournisseur Optique El Manar souhaite cr\xE9er une nouvelle sous-cat\xE9gorie nomm\xE9e "Lunettes de Natation"',
        fournisseurNom: "Optique El Manar",
        fournisseurId: 12,
        referenceId: 2,
        dateCreation: new Date(Date.now() - 36e5),
        // Il y a 1 heure
        estLue: false,
        priority: "HIGH",
        actionUrl: "/admin/dashboard/demandes"
      },
      {
        id: 3,
        type: "NOUVEAU_PRODUIT",
        titre: "Nouveau produit en attente",
        message: 'Le fournisseur Optique Centrale a ajout\xE9 un nouveau produit "Ray-Ban Aviator" en attente de validation',
        fournisseurNom: "Optique Centrale",
        fournisseurId: 13,
        referenceId: 101,
        dateCreation: new Date(Date.now() - 72e5),
        // Il y a 2 heures
        estLue: true,
        priority: "MEDIUM",
        actionUrl: "/admin/dashboard/products"
      }
    ];
  }
  static \u0275fac = function AdminNotificationService_Factory(__ngFactoryType__) {
    return new (__ngFactoryType__ || _AdminNotificationService)(\u0275\u0275inject(HttpClient));
  };
  static \u0275prov = /* @__PURE__ */ \u0275\u0275defineInjectable({ token: _AdminNotificationService, factory: _AdminNotificationService.\u0275fac, providedIn: "root" });
};
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && setClassMetadata(AdminNotificationService, [{
    type: Injectable,
    args: [{
      providedIn: "root"
    }]
  }], () => [{ type: HttpClient }], null);
})();

// src/app/components/notification-icon/notification-icon.component.ts
function NotificationIconComponent_span_5_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "span", 7);
    \u0275\u0275text(1);
    \u0275\u0275elementEnd();
  }
  if (rf & 2) {
    const ctx_r0 = \u0275\u0275nextContext();
    \u0275\u0275advance();
    \u0275\u0275textInterpolate(ctx_r0.totalUnreadCount);
  }
}
function NotificationIconComponent_div_6_button_5_Template(rf, ctx) {
  if (rf & 1) {
    const _r3 = \u0275\u0275getCurrentView();
    \u0275\u0275elementStart(0, "button", 20);
    \u0275\u0275listener("click", function NotificationIconComponent_div_6_button_5_Template_button_click_0_listener() {
      \u0275\u0275restoreView(_r3);
      const ctx_r0 = \u0275\u0275nextContext(2);
      return \u0275\u0275resetView(ctx_r0.markAllAsRead());
    });
    \u0275\u0275text(1, " Tout marquer comme lu ");
    \u0275\u0275elementEnd();
  }
}
function NotificationIconComponent_div_6_div_7_div_6_span_2_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "span");
    \u0275\u0275text(1, "\u{1F4C1}");
    \u0275\u0275elementEnd();
  }
}
function NotificationIconComponent_div_6_div_7_div_6_span_3_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "span");
    \u0275\u0275text(1, "\u{1F4C2}");
    \u0275\u0275elementEnd();
  }
}
function NotificationIconComponent_div_6_div_7_div_6_span_4_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "span");
    \u0275\u0275text(1, "\u{1F4E6}");
    \u0275\u0275elementEnd();
  }
}
function NotificationIconComponent_div_6_div_7_div_6_span_5_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "span");
    \u0275\u0275text(1, "\u{1F6D2}");
    \u0275\u0275elementEnd();
  }
}
function NotificationIconComponent_div_6_div_7_div_6_span_6_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "span");
    \u0275\u0275text(1, "\u{1F514}");
    \u0275\u0275elementEnd();
  }
}
function NotificationIconComponent_div_6_div_7_div_6_span_15_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "span", 40);
    \u0275\u0275text(1, "Urgent");
    \u0275\u0275elementEnd();
  }
}
function NotificationIconComponent_div_6_div_7_div_6_span_16_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "span", 41);
    \u0275\u0275text(1, "Moyen");
    \u0275\u0275elementEnd();
  }
}
function NotificationIconComponent_div_6_div_7_div_6_Template(rf, ctx) {
  if (rf & 1) {
    const _r4 = \u0275\u0275getCurrentView();
    \u0275\u0275elementStart(0, "div", 26);
    \u0275\u0275listener("click", function NotificationIconComponent_div_6_div_7_div_6_Template_div_click_0_listener() {
      const adminNotif_r5 = \u0275\u0275restoreView(_r4).$implicit;
      const ctx_r0 = \u0275\u0275nextContext(3);
      return \u0275\u0275resetView(ctx_r0.handleAdminNotificationClick(adminNotif_r5));
    });
    \u0275\u0275elementStart(1, "div", 27);
    \u0275\u0275template(2, NotificationIconComponent_div_6_div_7_div_6_span_2_Template, 2, 0, "span", 28)(3, NotificationIconComponent_div_6_div_7_div_6_span_3_Template, 2, 0, "span", 28)(4, NotificationIconComponent_div_6_div_7_div_6_span_4_Template, 2, 0, "span", 28)(5, NotificationIconComponent_div_6_div_7_div_6_span_5_Template, 2, 0, "span", 28)(6, NotificationIconComponent_div_6_div_7_div_6_span_6_Template, 2, 0, "span", 28);
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(7, "div", 29)(8, "div", 30);
    \u0275\u0275text(9);
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(10, "div", 31);
    \u0275\u0275text(11);
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(12, "div", 32)(13, "span", 33);
    \u0275\u0275text(14);
    \u0275\u0275elementEnd();
    \u0275\u0275template(15, NotificationIconComponent_div_6_div_7_div_6_span_15_Template, 2, 0, "span", 34)(16, NotificationIconComponent_div_6_div_7_div_6_span_16_Template, 2, 0, "span", 35);
    \u0275\u0275elementEnd()();
    \u0275\u0275elementStart(17, "button", 36);
    \u0275\u0275listener("click", function NotificationIconComponent_div_6_div_7_div_6_Template_button_click_17_listener($event) {
      const adminNotif_r5 = \u0275\u0275restoreView(_r4).$implicit;
      const ctx_r0 = \u0275\u0275nextContext(3);
      return \u0275\u0275resetView(ctx_r0.deleteAdminNotification(adminNotif_r5.id, $event));
    });
    \u0275\u0275namespaceSVG();
    \u0275\u0275elementStart(18, "svg", 37);
    \u0275\u0275element(19, "line", 38)(20, "line", 39);
    \u0275\u0275elementEnd()()();
  }
  if (rf & 2) {
    const adminNotif_r5 = ctx.$implicit;
    const ctx_r0 = \u0275\u0275nextContext(3);
    \u0275\u0275classProp("unread", !adminNotif_r5.estLue)("high-priority", adminNotif_r5.priority === "HIGH");
    \u0275\u0275advance(2);
    \u0275\u0275property("ngIf", adminNotif_r5.type === "DEMANDE_CATEGORIE");
    \u0275\u0275advance();
    \u0275\u0275property("ngIf", adminNotif_r5.type === "DEMANDE_SOUS_CATEGORIE");
    \u0275\u0275advance();
    \u0275\u0275property("ngIf", adminNotif_r5.type === "NOUVEAU_PRODUIT");
    \u0275\u0275advance();
    \u0275\u0275property("ngIf", adminNotif_r5.type === "COMMANDE");
    \u0275\u0275advance();
    \u0275\u0275property("ngIf", adminNotif_r5.type === "AUTRE");
    \u0275\u0275advance(3);
    \u0275\u0275textInterpolate(adminNotif_r5.titre);
    \u0275\u0275advance(2);
    \u0275\u0275textInterpolate(adminNotif_r5.message);
    \u0275\u0275advance(3);
    \u0275\u0275textInterpolate(ctx_r0.formatDate(adminNotif_r5.dateCreation));
    \u0275\u0275advance();
    \u0275\u0275property("ngIf", adminNotif_r5.priority === "HIGH");
    \u0275\u0275advance();
    \u0275\u0275property("ngIf", adminNotif_r5.priority === "MEDIUM");
  }
}
function NotificationIconComponent_div_6_div_7_div_7_Template(rf, ctx) {
  if (rf & 1) {
    const _r6 = \u0275\u0275getCurrentView();
    \u0275\u0275elementStart(0, "div", 42)(1, "button", 43);
    \u0275\u0275listener("click", function NotificationIconComponent_div_6_div_7_div_7_Template_button_click_1_listener() {
      \u0275\u0275restoreView(_r6);
      const ctx_r0 = \u0275\u0275nextContext(3);
      return \u0275\u0275resetView(ctx_r0.viewAllAdminNotifications());
    });
    \u0275\u0275text(2);
    \u0275\u0275elementEnd()();
  }
  if (rf & 2) {
    const ctx_r0 = \u0275\u0275nextContext(3);
    \u0275\u0275advance(2);
    \u0275\u0275textInterpolate1(" Voir toutes les notifications admin (", ctx_r0.adminNotifications.length, ") ");
  }
}
function NotificationIconComponent_div_6_div_7_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "div", 21)(1, "div", 22)(2, "span");
    \u0275\u0275text(3, "\u{1F514} Notifications Admin");
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(4, "span", 23);
    \u0275\u0275text(5);
    \u0275\u0275elementEnd()();
    \u0275\u0275template(6, NotificationIconComponent_div_6_div_7_div_6_Template, 21, 14, "div", 24)(7, NotificationIconComponent_div_6_div_7_div_7_Template, 3, 1, "div", 25);
    \u0275\u0275elementEnd();
  }
  if (rf & 2) {
    const ctx_r0 = \u0275\u0275nextContext(2);
    \u0275\u0275advance(5);
    \u0275\u0275textInterpolate(ctx_r0.adminUnreadCount);
    \u0275\u0275advance();
    \u0275\u0275property("ngForOf", ctx_r0.adminNotifications.slice(0, 3));
    \u0275\u0275advance();
    \u0275\u0275property("ngIf", ctx_r0.adminNotifications.length > 3);
  }
}
function NotificationIconComponent_div_6_div_8_Template(rf, ctx) {
  if (rf & 1) {
    const _r7 = \u0275\u0275getCurrentView();
    \u0275\u0275elementStart(0, "div", 44)(1, "div", 22)(2, "span");
    \u0275\u0275text(3, "\u{1F514} Demandes en attente");
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(4, "span", 45);
    \u0275\u0275text(5);
    \u0275\u0275elementEnd()();
    \u0275\u0275elementStart(6, "div", 46);
    \u0275\u0275listener("click", function NotificationIconComponent_div_6_div_8_Template_div_click_6_listener() {
      \u0275\u0275restoreView(_r7);
      const ctx_r0 = \u0275\u0275nextContext(2);
      return \u0275\u0275resetView(ctx_r0.viewDemandes());
    });
    \u0275\u0275elementStart(7, "div", 47)(8, "div", 48);
    \u0275\u0275text(9, "Nouvelles demandes de cat\xE9gories");
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(10, "div", 49);
    \u0275\u0275text(11);
    \u0275\u0275elementEnd()()()();
  }
  if (rf & 2) {
    const ctx_r0 = \u0275\u0275nextContext(2);
    \u0275\u0275advance(5);
    \u0275\u0275textInterpolate(ctx_r0.demandesEnAttente);
    \u0275\u0275advance(6);
    \u0275\u0275textInterpolate1("", ctx_r0.demandesEnAttente, " demande(s) en attente de traitement");
  }
}
function NotificationIconComponent_div_6_div_9_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275element(0, "div", 12);
  }
}
function NotificationIconComponent_div_6_div_11_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "div", 50);
    \u0275\u0275namespaceSVG();
    \u0275\u0275elementStart(1, "svg", 51);
    \u0275\u0275element(2, "path", 52)(3, "circle", 53);
    \u0275\u0275elementEnd();
    \u0275\u0275namespaceHTML();
    \u0275\u0275elementStart(4, "p");
    \u0275\u0275text(5, "Aucune notification");
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(6, "span", 54);
    \u0275\u0275text(7, "Vous \xEAtes \xE0 jour !");
    \u0275\u0275elementEnd()();
  }
}
function NotificationIconComponent_div_6_div_12_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "div", 50);
    \u0275\u0275namespaceSVG();
    \u0275\u0275elementStart(1, "svg", 51);
    \u0275\u0275element(2, "path", 55);
    \u0275\u0275elementEnd();
    \u0275\u0275namespaceHTML();
    \u0275\u0275elementStart(3, "p");
    \u0275\u0275text(4, "Aucune notification");
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(5, "span", 54);
    \u0275\u0275text(6, "Tout est sous contr\xF4le !");
    \u0275\u0275elementEnd()();
  }
}
function NotificationIconComponent_div_6_div_13_Template(rf, ctx) {
  if (rf & 1) {
    const _r8 = \u0275\u0275getCurrentView();
    \u0275\u0275elementStart(0, "div", 56);
    \u0275\u0275listener("click", function NotificationIconComponent_div_6_div_13_Template_div_click_0_listener() {
      const notification_r9 = \u0275\u0275restoreView(_r8).$implicit;
      const ctx_r0 = \u0275\u0275nextContext(2);
      return \u0275\u0275resetView(ctx_r0.markAsRead(notification_r9));
    });
    \u0275\u0275elementStart(1, "div", 57)(2, "p", 58);
    \u0275\u0275text(3);
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(4, "span", 59);
    \u0275\u0275text(5);
    \u0275\u0275elementEnd()();
    \u0275\u0275elementStart(6, "button", 60);
    \u0275\u0275listener("click", function NotificationIconComponent_div_6_div_13_Template_button_click_6_listener($event) {
      const notification_r9 = \u0275\u0275restoreView(_r8).$implicit;
      const ctx_r0 = \u0275\u0275nextContext(2);
      return \u0275\u0275resetView(ctx_r0.deleteNotification(notification_r9.id, $event));
    });
    \u0275\u0275namespaceSVG();
    \u0275\u0275elementStart(7, "svg", 61);
    \u0275\u0275element(8, "line", 38)(9, "line", 39);
    \u0275\u0275elementEnd()()();
  }
  if (rf & 2) {
    const notification_r9 = ctx.$implicit;
    const ctx_r0 = \u0275\u0275nextContext(2);
    \u0275\u0275classProp("unread", !notification_r9.estLue);
    \u0275\u0275advance(3);
    \u0275\u0275textInterpolate(notification_r9.contenu);
    \u0275\u0275advance(2);
    \u0275\u0275textInterpolate(ctx_r0.formatDate(notification_r9.dateEnvoi));
  }
}
function NotificationIconComponent_div_6_div_14_Template(rf, ctx) {
  if (rf & 1) {
    const _r10 = \u0275\u0275getCurrentView();
    \u0275\u0275elementStart(0, "div", 62)(1, "button", 43);
    \u0275\u0275listener("click", function NotificationIconComponent_div_6_div_14_Template_button_click_1_listener() {
      \u0275\u0275restoreView(_r10);
      const ctx_r0 = \u0275\u0275nextContext(2);
      return \u0275\u0275resetView(ctx_r0.viewAllNotifications());
    });
    \u0275\u0275text(2, " Voir toutes les notifications ");
    \u0275\u0275elementEnd()();
  }
}
function NotificationIconComponent_div_6_Template(rf, ctx) {
  if (rf & 1) {
    const _r2 = \u0275\u0275getCurrentView();
    \u0275\u0275elementStart(0, "div", 8);
    \u0275\u0275listener("click", function NotificationIconComponent_div_6_Template_div_click_0_listener($event) {
      \u0275\u0275restoreView(_r2);
      return \u0275\u0275resetView($event.stopPropagation());
    });
    \u0275\u0275element(1, "div", 9);
    \u0275\u0275elementStart(2, "div", 10)(3, "h4");
    \u0275\u0275text(4, "Notifications");
    \u0275\u0275elementEnd();
    \u0275\u0275template(5, NotificationIconComponent_div_6_button_5_Template, 2, 0, "button", 11);
    \u0275\u0275elementEnd();
    \u0275\u0275element(6, "div", 12);
    \u0275\u0275template(7, NotificationIconComponent_div_6_div_7_Template, 8, 3, "div", 13)(8, NotificationIconComponent_div_6_div_8_Template, 12, 2, "div", 14)(9, NotificationIconComponent_div_6_div_9_Template, 1, 0, "div", 15);
    \u0275\u0275elementStart(10, "div", 16);
    \u0275\u0275template(11, NotificationIconComponent_div_6_div_11_Template, 8, 0, "div", 17)(12, NotificationIconComponent_div_6_div_12_Template, 7, 0, "div", 17)(13, NotificationIconComponent_div_6_div_13_Template, 10, 4, "div", 18)(14, NotificationIconComponent_div_6_div_14_Template, 3, 0, "div", 19);
    \u0275\u0275elementEnd()();
  }
  if (rf & 2) {
    const ctx_r0 = \u0275\u0275nextContext();
    \u0275\u0275advance(5);
    \u0275\u0275property("ngIf", ctx_r0.unreadCount > 0);
    \u0275\u0275advance(2);
    \u0275\u0275property("ngIf", ctx_r0.isAdmin() && ctx_r0.adminNotifications.length > 0);
    \u0275\u0275advance();
    \u0275\u0275property("ngIf", ctx_r0.isAdmin() && ctx_r0.demandesEnAttente > 0 && ctx_r0.adminNotifications.length === 0);
    \u0275\u0275advance();
    \u0275\u0275property("ngIf", ctx_r0.isAdmin() && (ctx_r0.adminNotifications.length > 0 || ctx_r0.demandesEnAttente > 0));
    \u0275\u0275advance(2);
    \u0275\u0275property("ngIf", !ctx_r0.isAdmin() && ctx_r0.notifications.length === 0);
    \u0275\u0275advance();
    \u0275\u0275property("ngIf", ctx_r0.isAdmin() && ctx_r0.notifications.length === 0 && ctx_r0.adminNotifications.length === 0 && ctx_r0.demandesEnAttente === 0);
    \u0275\u0275advance();
    \u0275\u0275property("ngForOf", ctx_r0.notifications.slice(0, 5));
    \u0275\u0275advance();
    \u0275\u0275property("ngIf", ctx_r0.notifications.length > 5);
  }
}
var NotificationIconComponent = class _NotificationIconComponent {
  notificationService;
  authService;
  router;
  demandeService;
  adminAuthService;
  adminNotificationService;
  notifications = [];
  adminNotifications = [];
  unreadCount = 0;
  showMenu = false;
  demandesEnAttente = 0;
  subscriptions = new Subscription();
  get adminUnreadCount() {
    const count = this.adminNotifications.filter((n) => !n.estLue).length;
    console.log("\u{1F4CA} adminUnreadCount:", count, "sur", this.adminNotifications.length, "notifications admin");
    return count;
  }
  constructor(notificationService, authService, router, demandeService, adminAuthService, adminNotificationService) {
    this.notificationService = notificationService;
    this.authService = authService;
    this.router = router;
    this.demandeService = demandeService;
    this.adminAuthService = adminAuthService;
    this.adminNotificationService = adminNotificationService;
  }
  ngOnInit() {
    this.subscriptions.add(this.notificationService.notifications$.subscribe((notifications) => {
      this.notifications = notifications;
    }));
    this.subscriptions.add(this.notificationService.unreadCount$.subscribe((count) => {
      this.unreadCount = count;
    }));
    if (this.isAdmin()) {
      this.subscriptions.add(this.adminNotificationService.notifications$.subscribe((adminNotifications) => {
        this.adminNotifications = adminNotifications;
      }));
      this.loadAdminNotifications();
    }
    this.loadNotifications();
    this.loadDemandesEnAttente();
    if (this.isAdmin()) {
      this.subscriptions.add(interval(3e4).subscribe(() => {
        this.loadDemandesEnAttente();
        this.loadAdminNotifications();
      }));
    }
  }
  ngOnDestroy() {
    this.subscriptions.unsubscribe();
  }
  toggleMenu(event) {
    console.log("\u{1F504} toggleMenu appel\xE9, showMenu avant:", this.showMenu);
    if (event) {
      event.stopPropagation();
      event.preventDefault();
    }
    this.showMenu = !this.showMenu;
    console.log("\u{1F504} showMenu apr\xE8s:", this.showMenu);
    if (this.showMenu) {
      console.log("\u{1F4C2} Menu ouvert, chargement des notifications...");
      this.loadNotifications();
      if (this.isAdmin()) {
        console.log("\u{1F451} Utilisateur admin d\xE9tect\xE9, chargement des notifications admin...");
        this.loadAdminNotifications();
      }
      setTimeout(() => {
        document.addEventListener("click", this.closeMenu.bind(this));
      }, 100);
    } else {
      console.log("\u{1F4C2} Menu ferm\xE9");
      document.removeEventListener("click", this.closeMenu.bind(this));
    }
  }
  closeMenu(event) {
    console.log("\u{1F504} closeMenu appel\xE9");
    this.showMenu = false;
    document.removeEventListener("click", this.closeMenu.bind(this));
  }
  onButtonMouseDown(event) {
    console.log("\u{1F5B1}\uFE0F Bouton notification mousedown d\xE9tect\xE9 !");
    event.stopPropagation();
  }
  convertDemandestoNotifications(demandesCategories, demandesSousCategories) {
    const notifications = [];
    console.log("\u{1F504} Conversion des demandes en notifications...");
    console.log("\u{1F4C1} Traitement des demandes de cat\xE9gories:", demandesCategories.length);
    demandesCategories.forEach((demande, index) => {
      console.log(`\u{1F50D} Demande cat\xE9gorie ${index + 1}:`, {
        id: demande.id,
        nom: demande.nom,
        statut: demande.statut,
        fournisseurId: demande.fournisseurId,
        fournisseurRaisonSociale: demande.fournisseurRaisonSociale,
        dateCreation: demande.dateCreation,
        dateDemande: demande.dateDemande
      });
      if (demande.statut === "EN_ATTENTE" || demande.statut === 0) {
        const fournisseurNom = demande.fournisseurRaisonSociale || demande.fournisseur?.raisonSociale || demande.nomFournisseur || `Fournisseur #${demande.fournisseurId}`;
        notifications.push({
          id: parseInt(`1${demande.id}`),
          // Préfixe 1 pour catégories
          type: "DEMANDE_CATEGORIE",
          titre: "Nouvelle demande de cat\xE9gorie",
          message: `Le fournisseur ${fournisseurNom} souhaite cr\xE9er une nouvelle cat\xE9gorie nomm\xE9e "${demande.nom}"`,
          fournisseurNom,
          fournisseurId: demande.fournisseurId,
          referenceId: demande.id,
          dateCreation: new Date(demande.dateCreation || demande.dateDemande || Date.now()),
          estLue: false,
          priority: "HIGH",
          actionUrl: "/admin/dashboard/demandes"
        });
        console.log(`\u2705 Notification cr\xE9\xE9e pour cat\xE9gorie: ${demande.nom}`);
      } else {
        console.log(`\u23ED\uFE0F Demande cat\xE9gorie ignor\xE9e (statut: ${demande.statut})`);
      }
    });
    console.log("\u{1F4C2} Traitement des demandes de sous-cat\xE9gories:", demandesSousCategories.length);
    demandesSousCategories.forEach((demande, index) => {
      console.log(`\u{1F50D} Demande sous-cat\xE9gorie ${index + 1}:`, {
        id: demande.id,
        nom: demande.nom,
        statut: demande.statut,
        fournisseurId: demande.fournisseurId,
        fournisseurRaisonSociale: demande.fournisseurRaisonSociale
      });
      if (demande.statut === "EN_ATTENTE" || demande.statut === 0) {
        const fournisseurNom = demande.fournisseurRaisonSociale || demande.fournisseur?.raisonSociale || demande.nomFournisseur || `Fournisseur #${demande.fournisseurId}`;
        notifications.push({
          id: parseInt(`2${demande.id}`),
          // Préfixe 2 pour sous-catégories
          type: "DEMANDE_SOUS_CATEGORIE",
          titre: "Nouvelle demande de sous-cat\xE9gorie",
          message: `Le fournisseur ${fournisseurNom} souhaite cr\xE9er une nouvelle sous-cat\xE9gorie nomm\xE9e "${demande.nom}"`,
          fournisseurNom,
          fournisseurId: demande.fournisseurId,
          referenceId: demande.id,
          dateCreation: new Date(demande.dateCreation || demande.dateDemande || Date.now()),
          estLue: false,
          priority: "HIGH",
          actionUrl: "/admin/dashboard/demandes"
        });
        console.log(`\u2705 Notification cr\xE9\xE9e pour sous-cat\xE9gorie: ${demande.nom}`);
      } else {
        console.log(`\u23ED\uFE0F Demande sous-cat\xE9gorie ignor\xE9e (statut: ${demande.statut})`);
      }
    });
    notifications.sort((a, b) => new Date(b.dateCreation).getTime() - new Date(a.dateCreation).getTime());
    console.log(`\u{1F4CA} Total notifications cr\xE9\xE9es: ${notifications.length}`);
    return notifications;
  }
  loadNotifications() {
    console.log("\u{1F4E5} loadNotifications appel\xE9");
    const currentUser = this.authService.getCurrentUser();
    console.log("\u{1F464} Utilisateur actuel:", currentUser);
    if (currentUser?.id) {
      console.log("\u{1F504} Chargement des notifications pour utilisateur ID:", currentUser.id);
      this.notificationService.getUserNotifications(currentUser.id).subscribe({
        next: (notifications) => {
          console.log("\u2705 Notifications re\xE7ues:", notifications);
        },
        error: (error) => {
          console.error("\u274C Erreur lors du chargement des notifications:", error);
        }
      });
    } else {
      console.log("\u274C Aucun utilisateur connect\xE9");
    }
  }
  loadAdminNotifications() {
    console.log("\u{1F4E5} loadAdminNotifications appel\xE9");
    if (this.isAdmin()) {
      console.log("\u{1F451} Chargement des notifications admin depuis la base de donn\xE9es...");
      forkJoin({
        demandesCategories: this.demandeService.getMesDemandesCategories(),
        demandesSousCategories: this.demandeService.getMesDemandesSousCategories()
      }).subscribe({
        next: (result) => {
          console.log("\u2705 Demandes r\xE9cup\xE9r\xE9es:", result);
          console.log("\u{1F4CA} Nombre de demandes cat\xE9gories:", result.demandesCategories?.length || 0);
          console.log("\u{1F4CA} Nombre de demandes sous-cat\xE9gories:", result.demandesSousCategories?.length || 0);
          if (result.demandesCategories?.length > 0) {
            console.log("\u{1F50D} Exemple demande cat\xE9gorie:", result.demandesCategories[0]);
          }
          if (result.demandesSousCategories?.length > 0) {
            console.log("\u{1F50D} Exemple demande sous-cat\xE9gorie:", result.demandesSousCategories[0]);
          }
          this.adminNotifications = this.convertDemandestoNotifications(result.demandesCategories || [], result.demandesSousCategories || []);
          console.log("\u2705 Notifications admin cr\xE9\xE9es:", this.adminNotifications);
          console.log("\u{1F4CA} Nombre total de notifications admin:", this.adminNotifications.length);
        },
        error: (error) => {
          console.error("\u274C Erreur lors du chargement des demandes:", error);
          this.adminNotifications = this.adminNotificationService.getMockNotifications();
        }
      });
    }
  }
  markAsRead(notification) {
    if (!notification.estLue) {
      this.notificationService.markAsRead(notification.id).subscribe();
    }
  }
  markAllAsRead() {
    const unreadNotifications = this.notifications.filter((n) => !n.estLue);
    unreadNotifications.forEach((notification) => {
      this.notificationService.markAsRead(notification.id).subscribe();
    });
  }
  deleteNotification(notificationId, event) {
    event.stopPropagation();
    this.notificationService.deleteNotification(notificationId).subscribe();
  }
  viewAllNotifications() {
    this.router.navigate(["/notifications"]);
  }
  viewDemandes() {
    this.router.navigate(["/admin/dashboard/demandes"]);
    this.closeMenu();
  }
  isAdmin() {
    const isAdminViaAuth = this.authService.isAdmin();
    const isAdminViaAdminAuth = this.adminAuthService.getCurrentUser() !== null;
    console.log("\u{1F50D} V\xE9rification admin - AuthService:", isAdminViaAuth);
    console.log("\u{1F50D} V\xE9rification admin - AdminAuthService:", isAdminViaAdminAuth);
    const result = isAdminViaAuth || isAdminViaAdminAuth;
    console.log("\u{1F50D} R\xE9sultat final isAdmin:", result);
    return result;
  }
  handleAdminNotificationClick(notification) {
    console.log("\u{1F514} Clic sur notification admin:", notification);
    notification.estLue = true;
    if (notification.actionUrl) {
      this.router.navigate([notification.actionUrl]);
    }
    this.closeMenu();
  }
  deleteAdminNotification(notificationId, event) {
    event.stopPropagation();
    console.log("\u{1F5D1}\uFE0F Suppression notification admin:", notificationId);
    this.adminNotifications = this.adminNotifications.filter((n) => n.id !== notificationId);
    console.log("\u2705 Notification supprim\xE9e localement");
  }
  viewAllAdminNotifications() {
    this.router.navigate(["/admin/notifications"]);
    this.closeMenu();
  }
  loadDemandesEnAttente() {
    console.log("\u{1F50D} loadDemandesEnAttente appel\xE9");
    console.log("\u{1F50D} isAdmin():", this.isAdmin());
    console.log("\u{1F50D} authService.isAuthenticated():", this.authService.isAuthenticated());
    console.log("\u{1F50D} authService.getToken():", !!this.authService.getToken());
    console.log("\u{1F50D} adminAuthService.getCurrentUser():", !!this.adminAuthService.getCurrentUser());
    if (this.isAdmin()) {
      console.log("\u2705 Utilisateur admin d\xE9tect\xE9, chargement des demandes...");
      forkJoin({
        categories: this.demandeService.getDemandesCategoriesByStatut(StatutDemande.EnAttente),
        sousCategories: this.demandeService.getDemandesSousCategoriesByStatut(StatutDemande.EnAttente)
      }).subscribe({
        next: (result) => {
          console.log("\u2705 Demandes charg\xE9es:", result);
          this.demandesEnAttente = result.categories.length + result.sousCategories.length;
        },
        error: (error) => {
          console.error("\u274C Erreur lors du chargement des demandes en attente:", error);
        }
      });
    } else {
      console.log("\u274C Utilisateur non admin, pas de chargement des demandes");
    }
  }
  get totalUnreadCount() {
    const adminUnreadCount = this.isAdmin() ? this.adminUnreadCount : 0;
    const normalUnreadCount = this.unreadCount || 0;
    const total = this.isAdmin() ? adminUnreadCount : normalUnreadCount;
    console.log("\u{1F522} Calcul badge:", {
      isAdmin: this.isAdmin(),
      normalUnreadCount,
      adminUnreadCount,
      total
    });
    return Math.max(0, total);
  }
  formatDate(date) {
    const now = /* @__PURE__ */ new Date();
    const notifDate = new Date(date);
    const diffInMinutes = Math.floor((now.getTime() - notifDate.getTime()) / (1e3 * 60));
    if (diffInMinutes < 1)
      return "\xC0 l'instant";
    if (diffInMinutes < 60)
      return `Il y a ${diffInMinutes} min`;
    const diffInHours = Math.floor(diffInMinutes / 60);
    if (diffInHours < 24)
      return `Il y a ${diffInHours}h`;
    const diffInDays = Math.floor(diffInHours / 24);
    if (diffInDays < 7)
      return `Il y a ${diffInDays}j`;
    return notifDate.toLocaleDateString("fr-FR");
  }
  static \u0275fac = function NotificationIconComponent_Factory(__ngFactoryType__) {
    return new (__ngFactoryType__ || _NotificationIconComponent)(\u0275\u0275directiveInject(NotificationService), \u0275\u0275directiveInject(AuthService), \u0275\u0275directiveInject(Router), \u0275\u0275directiveInject(DemandeService), \u0275\u0275directiveInject(AdminAuthService), \u0275\u0275directiveInject(AdminNotificationService));
  };
  static \u0275cmp = /* @__PURE__ */ \u0275\u0275defineComponent({ type: _NotificationIconComponent, selectors: [["app-notification-icon"]], decls: 7, vars: 3, consts: [[1, "notification-wrapper"], ["type", "button", 1, "notification-btn", 3, "click", "mousedown", "title"], ["width", "20", "height", "20", "viewBox", "0 0 24 24", "fill", "none", "stroke", "currentColor", "stroke-width", "2"], ["d", "M18 8A6 6 0 0 0 6 8c0 7-3 9-3 9h18s-3-2-3-9"], ["d", "M13.73 21a2 2 0 0 1-3.46 0"], ["class", "notification-badge", 4, "ngIf"], ["class", "notification-menu", 3, "click", 4, "ngIf"], [1, "notification-badge"], [1, "notification-menu", 3, "click"], [1, "notification-arrow"], [1, "notification-header"], ["class", "mark-all-read-btn", 3, "click", 4, "ngIf"], [1, "notification-divider"], ["class", "admin-notifications-section", 4, "ngIf"], ["class", "demandes-section", 4, "ngIf"], ["class", "notification-divider", 4, "ngIf"], [1, "notification-list"], ["class", "no-notifications", 4, "ngIf"], ["class", "notification-item", 3, "unread", "click", 4, "ngFor", "ngForOf"], ["class", "view-all", 4, "ngIf"], [1, "mark-all-read-btn", 3, "click"], [1, "admin-notifications-section"], [1, "section-header"], [1, "admin-count"], ["class", "admin-notification-item", 3, "unread", "high-priority", "click", 4, "ngFor", "ngForOf"], ["class", "view-all-admin", 4, "ngIf"], [1, "admin-notification-item", 3, "click"], [1, "admin-notification-icon"], [4, "ngIf"], [1, "admin-notification-content"], [1, "admin-notification-title"], [1, "admin-notification-message"], [1, "admin-notification-meta"], [1, "admin-notification-date"], ["class", "priority-badge high", 4, "ngIf"], ["class", "priority-badge medium", 4, "ngIf"], [1, "admin-delete-btn", 3, "click"], ["width", "14", "height", "14", "viewBox", "0 0 24 24", "fill", "none", "stroke", "currentColor", "stroke-width", "2"], ["x1", "18", "y1", "6", "x2", "6", "y2", "18"], ["x1", "6", "y1", "6", "x2", "18", "y2", "18"], [1, "priority-badge", "high"], [1, "priority-badge", "medium"], [1, "view-all-admin"], [3, "click"], [1, "demandes-section"], [1, "demandes-count"], [1, "demande-item", 3, "click"], [1, "demande-content"], [1, "demande-title"], [1, "demande-message"], [1, "no-notifications"], ["width", "48", "height", "48", "viewBox", "0 0 24 24", "fill", "none", "stroke", "currentColor", "stroke-width", "2", 1, "empty-icon"], ["d", "M20 21v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2"], ["cx", "12", "cy", "7", "r", "4"], [1, "no-notifications-subtext"], ["d", "M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"], [1, "notification-item", 3, "click"], [1, "notification-content"], [1, "notification-text"], [1, "notification-date"], [1, "delete-btn", 3, "click"], ["width", "16", "height", "16", "viewBox", "0 0 24 24", "fill", "none", "stroke", "currentColor", "stroke-width", "2"], [1, "view-all"]], template: function NotificationIconComponent_Template(rf, ctx) {
    if (rf & 1) {
      \u0275\u0275elementStart(0, "div", 0)(1, "button", 1);
      \u0275\u0275listener("click", function NotificationIconComponent_Template_button_click_1_listener($event) {
        return ctx.toggleMenu($event);
      })("mousedown", function NotificationIconComponent_Template_button_mousedown_1_listener($event) {
        return ctx.onButtonMouseDown($event);
      });
      \u0275\u0275namespaceSVG();
      \u0275\u0275elementStart(2, "svg", 2);
      \u0275\u0275element(3, "path", 3)(4, "path", 4);
      \u0275\u0275elementEnd();
      \u0275\u0275template(5, NotificationIconComponent_span_5_Template, 2, 1, "span", 5);
      \u0275\u0275elementEnd();
      \u0275\u0275template(6, NotificationIconComponent_div_6_Template, 15, 8, "div", 6);
      \u0275\u0275elementEnd();
    }
    if (rf & 2) {
      \u0275\u0275advance();
      \u0275\u0275property("title", "Notifications");
      \u0275\u0275advance(4);
      \u0275\u0275property("ngIf", ctx.totalUnreadCount > 0);
      \u0275\u0275advance();
      \u0275\u0275property("ngIf", ctx.showMenu);
    }
  }, dependencies: [CommonModule, NgForOf, NgIf], styles: ['\n\n.notification-wrapper[_ngcontent-%COMP%] {\n  position: relative;\n  display: inline-block;\n  z-index: 999;\n}\n.notification-btn[_ngcontent-%COMP%] {\n  position: relative;\n  background:\n    linear-gradient(\n      135deg,\n      #667eea 0%,\n      #764ba2 100%);\n  border: none;\n  cursor: pointer;\n  padding: 12px;\n  border-radius: 50%;\n  transition: all 0.3s ease;\n  color: white;\n  z-index: 1001;\n  outline: none;\n  font-size: 18px;\n  width: 48px;\n  height: 48px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);\n}\n.notification-btn[_ngcontent-%COMP%]:hover {\n  background:\n    linear-gradient(\n      135deg,\n      #5a67d8 0%,\n      #6b46c1 100%);\n  transform: translateY(-2px);\n  box-shadow: 0 6px 20px rgba(102, 126, 234, 0.4);\n}\n.notification-btn[_ngcontent-%COMP%]:focus {\n  outline: none;\n  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.3);\n}\n.notification-btn[_ngcontent-%COMP%]:active {\n  transform: translateY(0) scale(0.95);\n  box-shadow: 0 2px 8px rgba(102, 126, 234, 0.3);\n}\n.notification-badge[_ngcontent-%COMP%] {\n  position: absolute;\n  top: -2px;\n  right: -2px;\n  background:\n    linear-gradient(\n      135deg,\n      #ff4757 0%,\n      #ff3742 100%);\n  color: white;\n  border: 2px solid white;\n  border-radius: 50%;\n  min-width: 22px;\n  height: 22px;\n  font-size: 11px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  font-weight: bold;\n  box-shadow: 0 2px 8px rgba(255, 71, 87, 0.4);\n  animation: _ngcontent-%COMP%_pulse 2s infinite;\n}\n@keyframes _ngcontent-%COMP%_pulse {\n  0% {\n    box-shadow: 0 2px 8px rgba(255, 71, 87, 0.4);\n  }\n  50% {\n    box-shadow: 0 2px 8px rgba(255, 71, 87, 0.6), 0 0 0 4px rgba(255, 71, 87, 0.2);\n  }\n  100% {\n    box-shadow: 0 2px 8px rgba(255, 71, 87, 0.4);\n  }\n}\n.notification-menu[_ngcontent-%COMP%] {\n  position: absolute;\n  top: calc(100% + 8px);\n  right: 0;\n  width: 380px;\n  max-width: 90vw;\n  max-height: 500px;\n  background: white;\n  border: 1px solid #e5e7eb;\n  border-radius: 12px;\n  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);\n  z-index: 1000;\n  overflow: hidden;\n  transform-origin: top right;\n  animation: _ngcontent-%COMP%_slideDown 0.2s ease-out;\n}\n@keyframes _ngcontent-%COMP%_slideDown {\n  from {\n    opacity: 0;\n    transform: translateY(-10px) scale(0.95);\n  }\n  to {\n    opacity: 1;\n    transform: translateY(0) scale(1);\n  }\n}\n@media (max-width: 768px) {\n  .notification-menu[_ngcontent-%COMP%] {\n    position: fixed;\n    top: 60px;\n    left: 10px;\n    right: 10px;\n    width: auto;\n    max-width: none;\n    transform-origin: top center;\n  }\n}\n@media (max-width: 480px) {\n  .notification-menu[_ngcontent-%COMP%] {\n    top: 50px;\n    left: 5px;\n    right: 5px;\n    max-height: 70vh;\n  }\n}\n.notification-arrow[_ngcontent-%COMP%] {\n  position: absolute;\n  top: -8px;\n  right: 20px;\n  width: 0;\n  height: 0;\n  border-left: 8px solid transparent;\n  border-right: 8px solid transparent;\n  border-bottom: 8px solid white;\n  z-index: 1001;\n}\n.notification-arrow[_ngcontent-%COMP%]::before {\n  content: "";\n  position: absolute;\n  top: 1px;\n  left: -8px;\n  width: 0;\n  height: 0;\n  border-left: 8px solid transparent;\n  border-right: 8px solid transparent;\n  border-bottom: 8px solid #e5e7eb;\n}\n@media (max-width: 768px) {\n  .notification-arrow[_ngcontent-%COMP%] {\n    display: none;\n  }\n}\n.notification-header[_ngcontent-%COMP%] {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  padding: 16px;\n  background:\n    linear-gradient(\n      135deg,\n      #667eea 0%,\n      #764ba2 100%);\n  color: white;\n}\n.notification-header[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%] {\n  margin: 0;\n  font-size: 16px;\n  font-weight: 500;\n}\n.mark-all-read-btn[_ngcontent-%COMP%] {\n  background: none;\n  border: none;\n  color: white;\n  font-size: 12px;\n  cursor: pointer;\n  padding: 4px 8px;\n  border-radius: 4px;\n  transition: background-color 0.2s;\n}\n.mark-all-read-btn[_ngcontent-%COMP%]:hover {\n  background-color: rgba(255, 255, 255, 0.2);\n}\n.notification-divider[_ngcontent-%COMP%] {\n  height: 1px;\n  background-color: #e0e0e0;\n}\n.notification-list[_ngcontent-%COMP%] {\n  max-height: 300px;\n  overflow-y: auto;\n}\n.no-notifications[_ngcontent-%COMP%] {\n  text-align: center;\n  padding: 32px 16px;\n  color: #666;\n}\n.empty-icon[_ngcontent-%COMP%] {\n  margin-bottom: 16px;\n  color: #ccc;\n}\n.no-notifications[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\n  margin: 0;\n  font-size: 16px;\n  font-weight: 500;\n}\n.no-notifications-subtext[_ngcontent-%COMP%] {\n  display: block;\n  margin-top: 8px;\n  font-size: 14px;\n  color: #cbd5e1;\n  font-style: italic;\n}\n.notification-item[_ngcontent-%COMP%] {\n  display: flex;\n  align-items: flex-start;\n  padding: 12px 16px;\n  border-bottom: 1px solid #e0e0e0;\n  cursor: pointer;\n  transition: background-color 0.2s;\n}\n.notification-item[_ngcontent-%COMP%]:hover {\n  background-color: #f5f5f5;\n}\n.notification-item.unread[_ngcontent-%COMP%] {\n  background-color: #e3f2fd;\n  border-left: 3px solid #2196f3;\n}\n.notification-content[_ngcontent-%COMP%] {\n  flex: 1;\n  margin-right: 8px;\n}\n.notification-text[_ngcontent-%COMP%] {\n  margin: 0 0 4px 0;\n  font-size: 14px;\n  line-height: 1.4;\n}\n.notification-date[_ngcontent-%COMP%] {\n  font-size: 12px;\n  color: #666;\n}\n.delete-btn[_ngcontent-%COMP%] {\n  background: none;\n  border: none;\n  cursor: pointer;\n  opacity: 0;\n  transition: opacity 0.2s;\n  width: 32px;\n  height: 32px;\n  border-radius: 4px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  color: #666;\n}\n.delete-btn[_ngcontent-%COMP%]:hover {\n  background-color: rgba(244, 67, 54, 0.1);\n  color: #f44336;\n}\n.notification-item[_ngcontent-%COMP%]:hover   .delete-btn[_ngcontent-%COMP%] {\n  opacity: 1;\n}\n.view-all[_ngcontent-%COMP%] {\n  text-align: center;\n  padding: 16px;\n  border-top: 1px solid #e0e0e0;\n}\n.view-all[_ngcontent-%COMP%]   button[_ngcontent-%COMP%] {\n  background: none;\n  border: none;\n  color: #2196f3;\n  cursor: pointer;\n  font-size: 14px;\n  padding: 8px 16px;\n  border-radius: 4px;\n  transition: background-color 0.2s;\n}\n.view-all[_ngcontent-%COMP%]   button[_ngcontent-%COMP%]:hover {\n  background-color: rgba(33, 150, 243, 0.1);\n}\n.demandes-section[_ngcontent-%COMP%] {\n  padding: 12px 16px;\n  background: #f8f9ff;\n  border-left: 4px solid #3b82f6;\n}\n.section-header[_ngcontent-%COMP%] {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 8px;\n  font-weight: 500;\n  color: #1e293b;\n  font-size: 14px;\n}\n.demandes-count[_ngcontent-%COMP%] {\n  background: #3b82f6;\n  color: white;\n  border-radius: 12px;\n  padding: 2px 8px;\n  font-size: 12px;\n  font-weight: bold;\n}\n.demande-item[_ngcontent-%COMP%] {\n  cursor: pointer;\n  padding: 8px 0;\n  transition: opacity 0.2s;\n}\n.demande-item[_ngcontent-%COMP%]:hover {\n  opacity: 0.8;\n}\n.demande-content[_ngcontent-%COMP%] {\n  display: flex;\n  flex-direction: column;\n  gap: 4px;\n}\n.demande-title[_ngcontent-%COMP%] {\n  font-weight: 500;\n  color: #1e293b;\n  font-size: 14px;\n}\n.demande-message[_ngcontent-%COMP%] {\n  color: #64748b;\n  font-size: 12px;\n}\n.admin-notifications-section[_ngcontent-%COMP%] {\n  padding: 12px 16px;\n  background: #f0f9ff;\n  border-left: 4px solid #3b82f6;\n  margin-bottom: 8px;\n}\n.admin-count[_ngcontent-%COMP%] {\n  background: #3b82f6;\n  color: white;\n  border-radius: 12px;\n  padding: 2px 8px;\n  font-size: 12px;\n  font-weight: bold;\n}\n.admin-notification-item[_ngcontent-%COMP%] {\n  display: flex;\n  align-items: flex-start;\n  gap: 12px;\n  padding: 12px;\n  margin: 8px 0;\n  background: white;\n  border-radius: 8px;\n  cursor: pointer;\n  transition: all 0.2s ease;\n  border-left: 3px solid transparent;\n}\n.admin-notification-item[_ngcontent-%COMP%]:hover {\n  background: #f8fafc;\n  transform: translateX(2px);\n}\n.admin-notification-item.unread[_ngcontent-%COMP%] {\n  border-left-color: #3b82f6;\n  background: #f0f9ff;\n}\n.admin-notification-item.high-priority[_ngcontent-%COMP%] {\n  border-left-color: #ef4444;\n  background: #fef2f2;\n}\n.admin-notification-icon[_ngcontent-%COMP%] {\n  font-size: 18px;\n  min-width: 24px;\n  text-align: center;\n}\n.admin-notification-content[_ngcontent-%COMP%] {\n  flex: 1;\n  min-width: 0;\n}\n.admin-notification-title[_ngcontent-%COMP%] {\n  font-weight: 600;\n  color: #1e293b;\n  margin-bottom: 4px;\n  font-size: 14px;\n}\n.admin-notification-message[_ngcontent-%COMP%] {\n  color: #64748b;\n  font-size: 12px;\n  line-height: 1.4;\n  margin-bottom: 8px;\n}\n.admin-notification-meta[_ngcontent-%COMP%] {\n  display: flex;\n  align-items: center;\n  gap: 8px;\n}\n.admin-notification-date[_ngcontent-%COMP%] {\n  color: #94a3b8;\n  font-size: 11px;\n}\n.priority-badge[_ngcontent-%COMP%] {\n  padding: 2px 6px;\n  border-radius: 10px;\n  font-size: 10px;\n  font-weight: 600;\n  text-transform: uppercase;\n}\n.priority-badge.high[_ngcontent-%COMP%] {\n  background: #fee2e2;\n  color: #dc2626;\n}\n.priority-badge.medium[_ngcontent-%COMP%] {\n  background: #fef3c7;\n  color: #d97706;\n}\n.admin-delete-btn[_ngcontent-%COMP%] {\n  background: none;\n  border: none;\n  cursor: pointer;\n  padding: 4px;\n  border-radius: 4px;\n  color: #94a3b8;\n  transition: all 0.2s ease;\n}\n.admin-delete-btn[_ngcontent-%COMP%]:hover {\n  background: #fee2e2;\n  color: #dc2626;\n}\n.view-all-admin[_ngcontent-%COMP%] {\n  text-align: center;\n  margin-top: 12px;\n  padding-top: 12px;\n  border-top: 1px solid rgba(59, 130, 246, 0.2);\n}\n.view-all-admin[_ngcontent-%COMP%]   button[_ngcontent-%COMP%] {\n  background: none;\n  border: 1px solid #3b82f6;\n  color: #3b82f6;\n  padding: 8px 16px;\n  border-radius: 6px;\n  cursor: pointer;\n  font-size: 12px;\n  transition: all 0.2s ease;\n}\n.view-all-admin[_ngcontent-%COMP%]   button[_ngcontent-%COMP%]:hover {\n  background: #3b82f6;\n  color: white;\n}\n/*# sourceMappingURL=notification-icon.component.css.map */'] });
};
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && setClassMetadata(NotificationIconComponent, [{
    type: Component,
    args: [{ selector: "app-notification-icon", standalone: true, imports: [CommonModule], template: `
    <div class="notification-wrapper">
      <button
        type="button"
        class="notification-btn"
        [title]="'Notifications'"
        (click)="toggleMenu($event)"
        (mousedown)="onButtonMouseDown($event)">
        <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
          <path d="M18 8A6 6 0 0 0 6 8c0 7-3 9-3 9h18s-3-2-3-9"></path>
          <path d="M13.73 21a2 2 0 0 1-3.46 0"></path>
        </svg>
        <span *ngIf="totalUnreadCount > 0" class="notification-badge">{{ totalUnreadCount }}</span>
      </button>

      <div *ngIf="showMenu" class="notification-menu" (click)="$event.stopPropagation()">
        <!-- Fl\xE8che pointant vers le bouton -->
        <div class="notification-arrow"></div>
        <div class="notification-header">
          <h4>Notifications</h4>
          <button
            *ngIf="unreadCount > 0"
            (click)="markAllAsRead()"
            class="mark-all-read-btn">
            Tout marquer comme lu
          </button>
        </div>

        <div class="notification-divider"></div>

        <!-- Section notifications admin d\xE9taill\xE9es -->
        <div *ngIf="isAdmin() && adminNotifications.length > 0" class="admin-notifications-section">
          <div class="section-header">
            <span>\u{1F514} Notifications Admin</span>
            <span class="admin-count">{{ adminUnreadCount }}</span>
          </div>

          <div
            *ngFor="let adminNotif of adminNotifications.slice(0, 3)"
            class="admin-notification-item"
            [class.unread]="!adminNotif.estLue"
            [class.high-priority]="adminNotif.priority === 'HIGH'"
            (click)="handleAdminNotificationClick(adminNotif)">

            <div class="admin-notification-icon">
              <span *ngIf="adminNotif.type === 'DEMANDE_CATEGORIE'">\u{1F4C1}</span>
              <span *ngIf="adminNotif.type === 'DEMANDE_SOUS_CATEGORIE'">\u{1F4C2}</span>
              <span *ngIf="adminNotif.type === 'NOUVEAU_PRODUIT'">\u{1F4E6}</span>
              <span *ngIf="adminNotif.type === 'COMMANDE'">\u{1F6D2}</span>
              <span *ngIf="adminNotif.type === 'AUTRE'">\u{1F514}</span>
            </div>

            <div class="admin-notification-content">
              <div class="admin-notification-title">{{ adminNotif.titre }}</div>
              <div class="admin-notification-message">{{ adminNotif.message }}</div>
              <div class="admin-notification-meta">
                <span class="admin-notification-date">{{ formatDate(adminNotif.dateCreation) }}</span>
                <span *ngIf="adminNotif.priority === 'HIGH'" class="priority-badge high">Urgent</span>
                <span *ngIf="adminNotif.priority === 'MEDIUM'" class="priority-badge medium">Moyen</span>
              </div>
            </div>

            <button
              class="admin-delete-btn"
              (click)="deleteAdminNotification(adminNotif.id, $event)">
              <svg width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                <line x1="18" y1="6" x2="6" y2="18"></line>
                <line x1="6" y1="6" x2="18" y2="18"></line>
              </svg>
            </button>
          </div>

          <div *ngIf="adminNotifications.length > 3" class="view-all-admin">
            <button (click)="viewAllAdminNotifications()">
              Voir toutes les notifications admin ({{ adminNotifications.length }})
            </button>
          </div>
        </div>

        <!-- Section demandes pour les admins (version simplifi\xE9e) -->
        <div *ngIf="isAdmin() && demandesEnAttente > 0 && adminNotifications.length === 0" class="demandes-section">
          <div class="section-header">
            <span>\u{1F514} Demandes en attente</span>
            <span class="demandes-count">{{ demandesEnAttente }}</span>
          </div>
          <div class="demande-item" (click)="viewDemandes()">
            <div class="demande-content">
              <div class="demande-title">Nouvelles demandes de cat\xE9gories</div>
              <div class="demande-message">{{ demandesEnAttente }} demande(s) en attente de traitement</div>
            </div>
          </div>
        </div>

        <div *ngIf="isAdmin() && (adminNotifications.length > 0 || demandesEnAttente > 0)" class="notification-divider"></div>

        <div class="notification-list">
          <!-- Message quand aucune notification pour utilisateurs normaux -->
          <div *ngIf="!isAdmin() && notifications.length === 0" class="no-notifications">
            <svg class="empty-icon" width="48" height="48" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
              <path d="M20 21v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2"></path>
              <circle cx="12" cy="7" r="4"></circle>
            </svg>
            <p>Aucune notification</p>
            <span class="no-notifications-subtext">Vous \xEAtes \xE0 jour !</span>
          </div>

          <!-- Message quand aucune notification pour admins -->
          <div *ngIf="isAdmin() && notifications.length === 0 && adminNotifications.length === 0 && demandesEnAttente === 0" class="no-notifications">
            <svg class="empty-icon" width="48" height="48" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
              <path d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
            </svg>
            <p>Aucune notification</p>
            <span class="no-notifications-subtext">Tout est sous contr\xF4le !</span>
          </div>

          <div
            *ngFor="let notification of notifications.slice(0, 5)"
            class="notification-item"
            [class.unread]="!notification.estLue"
            (click)="markAsRead(notification)">

            <div class="notification-content">
              <p class="notification-text">{{ notification.contenu }}</p>
              <span class="notification-date">{{ formatDate(notification.dateEnvoi) }}</span>
            </div>

            <button
              class="delete-btn"
              (click)="deleteNotification(notification.id, $event)">
              <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                <line x1="18" y1="6" x2="6" y2="18"></line>
                <line x1="6" y1="6" x2="18" y2="18"></line>
              </svg>
            </button>
          </div>

          <div *ngIf="notifications.length > 5" class="view-all">
            <button (click)="viewAllNotifications()">
              Voir toutes les notifications
            </button>
          </div>
        </div>
      </div>
    </div>
  `, styles: ['/* angular:styles/component:css;19ca2ec987c9df18e28f8f172fd7ae736b83ef4fbce9173e10fcd315a82e9432;C:/Users/<USER>/Desktop/STE/PFE/PFE/frontend-fournisseur/src/app/components/notification-icon/notification-icon.component.ts */\n.notification-wrapper {\n  position: relative;\n  display: inline-block;\n  z-index: 999;\n}\n.notification-btn {\n  position: relative;\n  background:\n    linear-gradient(\n      135deg,\n      #667eea 0%,\n      #764ba2 100%);\n  border: none;\n  cursor: pointer;\n  padding: 12px;\n  border-radius: 50%;\n  transition: all 0.3s ease;\n  color: white;\n  z-index: 1001;\n  outline: none;\n  font-size: 18px;\n  width: 48px;\n  height: 48px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);\n}\n.notification-btn:hover {\n  background:\n    linear-gradient(\n      135deg,\n      #5a67d8 0%,\n      #6b46c1 100%);\n  transform: translateY(-2px);\n  box-shadow: 0 6px 20px rgba(102, 126, 234, 0.4);\n}\n.notification-btn:focus {\n  outline: none;\n  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.3);\n}\n.notification-btn:active {\n  transform: translateY(0) scale(0.95);\n  box-shadow: 0 2px 8px rgba(102, 126, 234, 0.3);\n}\n.notification-badge {\n  position: absolute;\n  top: -2px;\n  right: -2px;\n  background:\n    linear-gradient(\n      135deg,\n      #ff4757 0%,\n      #ff3742 100%);\n  color: white;\n  border: 2px solid white;\n  border-radius: 50%;\n  min-width: 22px;\n  height: 22px;\n  font-size: 11px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  font-weight: bold;\n  box-shadow: 0 2px 8px rgba(255, 71, 87, 0.4);\n  animation: pulse 2s infinite;\n}\n@keyframes pulse {\n  0% {\n    box-shadow: 0 2px 8px rgba(255, 71, 87, 0.4);\n  }\n  50% {\n    box-shadow: 0 2px 8px rgba(255, 71, 87, 0.6), 0 0 0 4px rgba(255, 71, 87, 0.2);\n  }\n  100% {\n    box-shadow: 0 2px 8px rgba(255, 71, 87, 0.4);\n  }\n}\n.notification-menu {\n  position: absolute;\n  top: calc(100% + 8px);\n  right: 0;\n  width: 380px;\n  max-width: 90vw;\n  max-height: 500px;\n  background: white;\n  border: 1px solid #e5e7eb;\n  border-radius: 12px;\n  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);\n  z-index: 1000;\n  overflow: hidden;\n  transform-origin: top right;\n  animation: slideDown 0.2s ease-out;\n}\n@keyframes slideDown {\n  from {\n    opacity: 0;\n    transform: translateY(-10px) scale(0.95);\n  }\n  to {\n    opacity: 1;\n    transform: translateY(0) scale(1);\n  }\n}\n@media (max-width: 768px) {\n  .notification-menu {\n    position: fixed;\n    top: 60px;\n    left: 10px;\n    right: 10px;\n    width: auto;\n    max-width: none;\n    transform-origin: top center;\n  }\n}\n@media (max-width: 480px) {\n  .notification-menu {\n    top: 50px;\n    left: 5px;\n    right: 5px;\n    max-height: 70vh;\n  }\n}\n.notification-arrow {\n  position: absolute;\n  top: -8px;\n  right: 20px;\n  width: 0;\n  height: 0;\n  border-left: 8px solid transparent;\n  border-right: 8px solid transparent;\n  border-bottom: 8px solid white;\n  z-index: 1001;\n}\n.notification-arrow::before {\n  content: "";\n  position: absolute;\n  top: 1px;\n  left: -8px;\n  width: 0;\n  height: 0;\n  border-left: 8px solid transparent;\n  border-right: 8px solid transparent;\n  border-bottom: 8px solid #e5e7eb;\n}\n@media (max-width: 768px) {\n  .notification-arrow {\n    display: none;\n  }\n}\n.notification-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  padding: 16px;\n  background:\n    linear-gradient(\n      135deg,\n      #667eea 0%,\n      #764ba2 100%);\n  color: white;\n}\n.notification-header h4 {\n  margin: 0;\n  font-size: 16px;\n  font-weight: 500;\n}\n.mark-all-read-btn {\n  background: none;\n  border: none;\n  color: white;\n  font-size: 12px;\n  cursor: pointer;\n  padding: 4px 8px;\n  border-radius: 4px;\n  transition: background-color 0.2s;\n}\n.mark-all-read-btn:hover {\n  background-color: rgba(255, 255, 255, 0.2);\n}\n.notification-divider {\n  height: 1px;\n  background-color: #e0e0e0;\n}\n.notification-list {\n  max-height: 300px;\n  overflow-y: auto;\n}\n.no-notifications {\n  text-align: center;\n  padding: 32px 16px;\n  color: #666;\n}\n.empty-icon {\n  margin-bottom: 16px;\n  color: #ccc;\n}\n.no-notifications p {\n  margin: 0;\n  font-size: 16px;\n  font-weight: 500;\n}\n.no-notifications-subtext {\n  display: block;\n  margin-top: 8px;\n  font-size: 14px;\n  color: #cbd5e1;\n  font-style: italic;\n}\n.notification-item {\n  display: flex;\n  align-items: flex-start;\n  padding: 12px 16px;\n  border-bottom: 1px solid #e0e0e0;\n  cursor: pointer;\n  transition: background-color 0.2s;\n}\n.notification-item:hover {\n  background-color: #f5f5f5;\n}\n.notification-item.unread {\n  background-color: #e3f2fd;\n  border-left: 3px solid #2196f3;\n}\n.notification-content {\n  flex: 1;\n  margin-right: 8px;\n}\n.notification-text {\n  margin: 0 0 4px 0;\n  font-size: 14px;\n  line-height: 1.4;\n}\n.notification-date {\n  font-size: 12px;\n  color: #666;\n}\n.delete-btn {\n  background: none;\n  border: none;\n  cursor: pointer;\n  opacity: 0;\n  transition: opacity 0.2s;\n  width: 32px;\n  height: 32px;\n  border-radius: 4px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  color: #666;\n}\n.delete-btn:hover {\n  background-color: rgba(244, 67, 54, 0.1);\n  color: #f44336;\n}\n.notification-item:hover .delete-btn {\n  opacity: 1;\n}\n.view-all {\n  text-align: center;\n  padding: 16px;\n  border-top: 1px solid #e0e0e0;\n}\n.view-all button {\n  background: none;\n  border: none;\n  color: #2196f3;\n  cursor: pointer;\n  font-size: 14px;\n  padding: 8px 16px;\n  border-radius: 4px;\n  transition: background-color 0.2s;\n}\n.view-all button:hover {\n  background-color: rgba(33, 150, 243, 0.1);\n}\n.demandes-section {\n  padding: 12px 16px;\n  background: #f8f9ff;\n  border-left: 4px solid #3b82f6;\n}\n.section-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 8px;\n  font-weight: 500;\n  color: #1e293b;\n  font-size: 14px;\n}\n.demandes-count {\n  background: #3b82f6;\n  color: white;\n  border-radius: 12px;\n  padding: 2px 8px;\n  font-size: 12px;\n  font-weight: bold;\n}\n.demande-item {\n  cursor: pointer;\n  padding: 8px 0;\n  transition: opacity 0.2s;\n}\n.demande-item:hover {\n  opacity: 0.8;\n}\n.demande-content {\n  display: flex;\n  flex-direction: column;\n  gap: 4px;\n}\n.demande-title {\n  font-weight: 500;\n  color: #1e293b;\n  font-size: 14px;\n}\n.demande-message {\n  color: #64748b;\n  font-size: 12px;\n}\n.admin-notifications-section {\n  padding: 12px 16px;\n  background: #f0f9ff;\n  border-left: 4px solid #3b82f6;\n  margin-bottom: 8px;\n}\n.admin-count {\n  background: #3b82f6;\n  color: white;\n  border-radius: 12px;\n  padding: 2px 8px;\n  font-size: 12px;\n  font-weight: bold;\n}\n.admin-notification-item {\n  display: flex;\n  align-items: flex-start;\n  gap: 12px;\n  padding: 12px;\n  margin: 8px 0;\n  background: white;\n  border-radius: 8px;\n  cursor: pointer;\n  transition: all 0.2s ease;\n  border-left: 3px solid transparent;\n}\n.admin-notification-item:hover {\n  background: #f8fafc;\n  transform: translateX(2px);\n}\n.admin-notification-item.unread {\n  border-left-color: #3b82f6;\n  background: #f0f9ff;\n}\n.admin-notification-item.high-priority {\n  border-left-color: #ef4444;\n  background: #fef2f2;\n}\n.admin-notification-icon {\n  font-size: 18px;\n  min-width: 24px;\n  text-align: center;\n}\n.admin-notification-content {\n  flex: 1;\n  min-width: 0;\n}\n.admin-notification-title {\n  font-weight: 600;\n  color: #1e293b;\n  margin-bottom: 4px;\n  font-size: 14px;\n}\n.admin-notification-message {\n  color: #64748b;\n  font-size: 12px;\n  line-height: 1.4;\n  margin-bottom: 8px;\n}\n.admin-notification-meta {\n  display: flex;\n  align-items: center;\n  gap: 8px;\n}\n.admin-notification-date {\n  color: #94a3b8;\n  font-size: 11px;\n}\n.priority-badge {\n  padding: 2px 6px;\n  border-radius: 10px;\n  font-size: 10px;\n  font-weight: 600;\n  text-transform: uppercase;\n}\n.priority-badge.high {\n  background: #fee2e2;\n  color: #dc2626;\n}\n.priority-badge.medium {\n  background: #fef3c7;\n  color: #d97706;\n}\n.admin-delete-btn {\n  background: none;\n  border: none;\n  cursor: pointer;\n  padding: 4px;\n  border-radius: 4px;\n  color: #94a3b8;\n  transition: all 0.2s ease;\n}\n.admin-delete-btn:hover {\n  background: #fee2e2;\n  color: #dc2626;\n}\n.view-all-admin {\n  text-align: center;\n  margin-top: 12px;\n  padding-top: 12px;\n  border-top: 1px solid rgba(59, 130, 246, 0.2);\n}\n.view-all-admin button {\n  background: none;\n  border: 1px solid #3b82f6;\n  color: #3b82f6;\n  padding: 8px 16px;\n  border-radius: 6px;\n  cursor: pointer;\n  font-size: 12px;\n  transition: all 0.2s ease;\n}\n.view-all-admin button:hover {\n  background: #3b82f6;\n  color: white;\n}\n/*# sourceMappingURL=notification-icon.component.css.map */\n'] }]
  }], () => [{ type: NotificationService }, { type: AuthService }, { type: Router }, { type: DemandeService }, { type: AdminAuthService }, { type: AdminNotificationService }], null);
})();
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && \u0275setClassDebugInfo(NotificationIconComponent, { className: "NotificationIconComponent", filePath: "src/app/components/notification-icon/notification-icon.component.ts", lineNumber: 652 });
})();

export {
  NotificationIconComponent
};
//# sourceMappingURL=chunk-NEMJGOZR.js.map

{"version": 3, "sources": ["src/app/components/admin/category-management/category-management.component.css"], "sourcesContent": [".category-management-container {\n  padding: 2rem;\n  max-width: 1400px;\n  margin: 0 auto;\n}\n\n/* Header */\n.page-header {\n  margin-bottom: 2rem;\n}\n\n.header-content {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  flex-wrap: wrap;\n  gap: 1rem;\n}\n\n.page-title {\n  display: flex;\n  align-items: center;\n  gap: 0.75rem;\n  font-size: 2rem;\n  font-weight: 600;\n  color: #1a202c;\n  margin: 0;\n}\n\n.header-actions {\n  display: flex;\n  gap: 1rem;\n}\n\n/* Tabs */\n.tabs-container {\n  margin-bottom: 2rem;\n}\n\n.tabs {\n  display: flex;\n  border-bottom: 2px solid #e5e7eb;\n}\n\n.tab-button {\n  background: none;\n  border: none;\n  padding: 1rem 2rem;\n  cursor: pointer;\n  font-weight: 500;\n  color: #64748b;\n  border-bottom: 2px solid transparent;\n  transition: all 0.2s;\n  display: flex;\n  align-items: center;\n  gap: 0.5rem;\n}\n\n.tab-button:hover {\n  color: #3b82f6;\n}\n\n.tab-button.active {\n  color: #3b82f6;\n  border-bottom-color: #3b82f6;\n}\n\n/* Section Header */\n.section-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 1.5rem;\n  padding: 0 1rem;\n}\n\n.section-header h2 {\n  font-size: 1.5rem;\n  font-weight: 600;\n  color: #1a202c;\n  margin: 0;\n}\n\n/* Table */\n.table-container {\n  background: white;\n  border-radius: 12px;\n  overflow: hidden;\n  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);\n}\n\n.data-table {\n  width: 100%;\n  border-collapse: collapse;\n}\n\n.data-table th {\n  background: #f8fafc;\n  padding: 1rem;\n  text-align: left;\n  font-weight: 600;\n  color: #374151;\n  border-bottom: 1px solid #e5e7eb;\n}\n\n.data-row {\n  border-bottom: 1px solid #f3f4f6;\n  transition: background-color 0.2s;\n}\n\n.data-row:hover {\n  background: #f9fafb;\n}\n\n.data-table td {\n  padding: 1rem;\n  vertical-align: middle;\n}\n\n/* Cellules spécifiques */\n.name-cell {\n  display: flex;\n  flex-direction: column;\n}\n\n.item-name {\n  font-weight: 600;\n  color: #1a202c;\n}\n\n.item-id {\n  font-size: 0.875rem;\n  color: #64748b;\n}\n\n.description-cell {\n  max-width: 300px;\n}\n\n.no-description {\n  color: #9ca3af;\n  font-style: italic;\n}\n\n.parent-cell {\n  color: #4f46e5;\n  font-weight: 500;\n}\n\n.count-cell {\n  text-align: center;\n  font-weight: 600;\n  color: #059669;\n}\n\n.date-cell {\n  color: #64748b;\n  font-size: 0.875rem;\n}\n\n/* Status badges */\n.status-badge {\n  padding: 0.25rem 0.75rem;\n  border-radius: 9999px;\n  font-size: 0.875rem;\n  font-weight: 500;\n}\n\n.status-validated {\n  background: #d1fae5;\n  color: #059669;\n}\n\n.status-pending {\n  background: #fef3c7;\n  color: #d97706;\n}\n\n/* Actions */\n.actions-cell {\n  width: 160px;\n}\n\n.action-buttons {\n  display: flex;\n  gap: 0.5rem;\n}\n\n.btn-action {\n  width: 32px;\n  height: 32px;\n  border: none;\n  border-radius: 6px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  cursor: pointer;\n  transition: all 0.2s;\n}\n\n.btn-edit {\n  background: #dbeafe;\n  color: #2563eb;\n}\n\n.btn-edit:hover {\n  background: #bfdbfe;\n}\n\n.btn-validate {\n  background: #d1fae5;\n  color: #059669;\n}\n\n.btn-validate:hover {\n  background: #a7f3d0;\n}\n\n.btn-reject {\n  background: #fef3c7;\n  color: #d97706;\n}\n\n.btn-reject:hover {\n  background: #fde68a;\n}\n\n.btn-delete {\n  background: #fee2e2;\n  color: #dc2626;\n}\n\n.btn-delete:hover {\n  background: #fecaca;\n}\n\n/* Buttons */\n.btn {\n  padding: 0.75rem 1.5rem;\n  border: none;\n  border-radius: 8px;\n  font-weight: 500;\n  cursor: pointer;\n  transition: all 0.2s;\n  display: flex;\n  align-items: center;\n  gap: 0.5rem;\n}\n\n.btn-primary {\n  background: #3b82f6;\n  color: white;\n}\n\n.btn-primary:hover {\n  background: #2563eb;\n}\n\n.btn-secondary {\n  background: #f3f4f6;\n  color: #374151;\n}\n\n.btn-secondary:hover {\n  background: #e5e7eb;\n}\n\n.btn:disabled {\n  opacity: 0.5;\n  cursor: not-allowed;\n}\n\n/* Modal */\n.modal-overlay {\n  position: fixed;\n  top: 0;\n  left: 0;\n  right: 0;\n  bottom: 0;\n  background: rgba(0, 0, 0, 0.5);\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  z-index: 1000;\n}\n\n.modal-content {\n  background: white;\n  border-radius: 12px;\n  width: 90%;\n  max-width: 500px;\n  max-height: 90vh;\n  overflow-y: auto;\n}\n\n.modal-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  padding: 1.5rem;\n  border-bottom: 1px solid #e5e7eb;\n}\n\n.modal-header h3 {\n  margin: 0;\n  font-size: 1.25rem;\n  font-weight: 600;\n  color: #1a202c;\n}\n\n.btn-close {\n  background: none;\n  border: none;\n  font-size: 1.5rem;\n  cursor: pointer;\n  color: #64748b;\n  padding: 0.25rem;\n}\n\n.btn-close:hover {\n  color: #374151;\n}\n\n.modal-form {\n  padding: 1.5rem;\n}\n\n.form-group {\n  margin-bottom: 1.5rem;\n}\n\n.form-group label {\n  display: block;\n  margin-bottom: 0.5rem;\n  font-weight: 500;\n  color: #374151;\n}\n\n.form-input,\n.form-textarea,\n.form-select {\n  width: 100%;\n  padding: 0.75rem;\n  border: 2px solid #e5e7eb;\n  border-radius: 8px;\n  font-size: 1rem;\n  transition: border-color 0.2s;\n}\n\n.form-input:focus,\n.form-textarea:focus,\n.form-select:focus {\n  outline: none;\n  border-color: #3b82f6;\n}\n\n.form-textarea {\n  resize: vertical;\n  min-height: 80px;\n}\n\n.error-text {\n  color: #dc2626;\n  font-size: 0.875rem;\n  margin-top: 0.25rem;\n}\n\n.modal-actions {\n  display: flex;\n  gap: 1rem;\n  justify-content: flex-end;\n  margin-top: 2rem;\n}\n\n/* Messages */\n.error-message {\n  background: #fef2f2;\n  border: 1px solid #fecaca;\n  color: #dc2626;\n  padding: 1rem;\n  border-radius: 8px;\n  margin-bottom: 2rem;\n  display: flex;\n  align-items: center;\n  gap: 0.5rem;\n}\n\n.loading-container {\n  text-align: center;\n  padding: 3rem;\n}\n\n.loading-spinner {\n  width: 40px;\n  height: 40px;\n  border: 4px solid #e2e8f0;\n  border-top: 4px solid #3b82f6;\n  border-radius: 50%;\n  animation: spin 1s linear infinite;\n  margin: 0 auto 1rem;\n}\n\n@keyframes spin {\n  0% { transform: rotate(0deg); }\n  100% { transform: rotate(360deg); }\n}\n\n.no-data {\n  text-align: center;\n  padding: 3rem;\n  color: #64748b;\n}\n\n.no-data i {\n  font-size: 3rem;\n  margin-bottom: 1rem;\n  opacity: 0.5;\n}\n\n.no-data h3 {\n  margin: 0 0 0.5rem 0;\n  color: #374151;\n}\n\n/* Icons */\n.icon-folder::before { content: \"📁\"; }\n.icon-folder-open::before { content: \"📂\"; }\n.icon-refresh::before { content: \"🔄\"; }\n.icon-plus::before { content: \"➕\"; }\n.icon-edit::before { content: \"✏️\"; }\n.icon-check::before { content: \"✅\"; }\n.icon-x::before { content: \"❌\"; }\n.icon-trash::before { content: \"🗑️\"; }\n.icon-alert::before { content: \"⚠️\"; }\n\n/* Responsive */\n@media (max-width: 768px) {\n  .category-management-container {\n    padding: 1rem;\n  }\n  \n  .header-content {\n    flex-direction: column;\n    align-items: stretch;\n  }\n  \n  .tabs {\n    flex-direction: column;\n  }\n  \n  .tab-button {\n    padding: 0.75rem 1rem;\n  }\n  \n  .section-header {\n    flex-direction: column;\n    gap: 1rem;\n    align-items: stretch;\n  }\n  \n  .data-table {\n    font-size: 0.875rem;\n  }\n  \n  .data-table th,\n  .data-table td {\n    padding: 0.75rem 0.5rem;\n  }\n  \n  .modal-content {\n    width: 95%;\n    margin: 1rem;\n  }\n}\n"], "mappings": ";AAAA,CAAC;AACC,WAAS;AACT,aAAW;AACX,UAAQ,EAAE;AACZ;AAGA,CAAC;AACC,iBAAe;AACjB;AAEA,CAAC;AACC,WAAS;AACT,mBAAiB;AACjB,eAAa;AACb,aAAW;AACX,OAAK;AACP;AAEA,CAAC;AACC,WAAS;AACT,eAAa;AACb,OAAK;AACL,aAAW;AACX,eAAa;AACb,SAAO;AACP,UAAQ;AACV;AAEA,CAAC;AACC,WAAS;AACT,OAAK;AACP;AAGA,CAAC;AACC,iBAAe;AACjB;AAEA,CAAC;AACC,WAAS;AACT,iBAAe,IAAI,MAAM;AAC3B;AAEA,CAAC;AACC,cAAY;AACZ,UAAQ;AACR,WAAS,KAAK;AACd,UAAQ;AACR,eAAa;AACb,SAAO;AACP,iBAAe,IAAI,MAAM;AACzB,cAAY,IAAI;AAChB,WAAS;AACT,eAAa;AACb,OAAK;AACP;AAEA,CAdC,UAcU;AACT,SAAO;AACT;AAEA,CAlBC,UAkBU,CAAC;AACV,SAAO;AACP,uBAAqB;AACvB;AAGA,CAAC;AACC,WAAS;AACT,mBAAiB;AACjB,eAAa;AACb,iBAAe;AACf,WAAS,EAAE;AACb;AAEA,CARC,eAQe;AACd,aAAW;AACX,eAAa;AACb,SAAO;AACP,UAAQ;AACV;AAGA,CAAC;AACC,cAAY;AACZ,iBAAe;AACf,YAAU;AACV,cAAY,EAAE,IAAI,IAAI,KAAK,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE;AACtC;AAEA,CAAC;AACC,SAAO;AACP,mBAAiB;AACnB;AAEA,CALC,WAKW;AACV,cAAY;AACZ,WAAS;AACT,cAAY;AACZ,eAAa;AACb,SAAO;AACP,iBAAe,IAAI,MAAM;AAC3B;AAEA,CAAC;AACC,iBAAe,IAAI,MAAM;AACzB,cAAY,iBAAiB;AAC/B;AAEA,CALC,QAKQ;AACP,cAAY;AACd;AAEA,CAvBC,WAuBW;AACV,WAAS;AACT,kBAAgB;AAClB;AAGA,CAAC;AACC,WAAS;AACT,kBAAgB;AAClB;AAEA,CAAC;AACC,eAAa;AACb,SAAO;AACT;AAEA,CAAC;AACC,aAAW;AACX,SAAO;AACT;AAEA,CAAC;AACC,aAAW;AACb;AAEA,CAAC;AACC,SAAO;AACP,cAAY;AACd;AAEA,CAAC;AACC,SAAO;AACP,eAAa;AACf;AAEA,CAAC;AACC,cAAY;AACZ,eAAa;AACb,SAAO;AACT;AAEA,CAAC;AACC,SAAO;AACP,aAAW;AACb;AAGA,CAAC;AACC,WAAS,QAAQ;AACjB,iBAAe;AACf,aAAW;AACX,eAAa;AACf;AAEA,CAAC;AACC,cAAY;AACZ,SAAO;AACT;AAEA,CAAC;AACC,cAAY;AACZ,SAAO;AACT;AAGA,CAAC;AACC,SAAO;AACT;AAEA,CAAC;AACC,WAAS;AACT,OAAK;AACP;AAEA,CAAC;AACC,SAAO;AACP,UAAQ;AACR,UAAQ;AACR,iBAAe;AACf,WAAS;AACT,eAAa;AACb,mBAAiB;AACjB,UAAQ;AACR,cAAY,IAAI;AAClB;AAEA,CAAC;AACC,cAAY;AACZ,SAAO;AACT;AAEA,CALC,QAKQ;AACP,cAAY;AACd;AAEA,CAAC;AACC,cAAY;AACZ,SAAO;AACT;AAEA,CALC,YAKY;AACX,cAAY;AACd;AAEA,CAAC;AACC,cAAY;AACZ,SAAO;AACT;AAEA,CALC,UAKU;AACT,cAAY;AACd;AAEA,CAAC;AACC,cAAY;AACZ,SAAO;AACT;AAEA,CALC,UAKU;AACT,cAAY;AACd;AAGA,CAAC;AACC,WAAS,QAAQ;AACjB,UAAQ;AACR,iBAAe;AACf,eAAa;AACb,UAAQ;AACR,cAAY,IAAI;AAChB,WAAS;AACT,eAAa;AACb,OAAK;AACP;AAEA,CAAC;AACC,cAAY;AACZ,SAAO;AACT;AAEA,CALC,WAKW;AACV,cAAY;AACd;AAEA,CAAC;AACC,cAAY;AACZ,SAAO;AACT;AAEA,CALC,aAKa;AACZ,cAAY;AACd;AAEA,CA9BC,GA8BG;AACF,WAAS;AACT,UAAQ;AACV;AAGA,CAAC;AACC,YAAU;AACV,OAAK;AACL,QAAM;AACN,SAAO;AACP,UAAQ;AACR,cAAY,KAAK,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE;AAC1B,WAAS;AACT,eAAa;AACb,mBAAiB;AACjB,WAAS;AACX;AAEA,CAAC;AACC,cAAY;AACZ,iBAAe;AACf,SAAO;AACP,aAAW;AACX,cAAY;AACZ,cAAY;AACd;AAEA,CAAC;AACC,WAAS;AACT,mBAAiB;AACjB,eAAa;AACb,WAAS;AACT,iBAAe,IAAI,MAAM;AAC3B;AAEA,CARC,aAQa;AACZ,UAAQ;AACR,aAAW;AACX,eAAa;AACb,SAAO;AACT;AAEA,CAAC;AACC,cAAY;AACZ,UAAQ;AACR,aAAW;AACX,UAAQ;AACR,SAAO;AACP,WAAS;AACX;AAEA,CATC,SASS;AACR,SAAO;AACT;AAEA,CAAC;AACC,WAAS;AACX;AAEA,CAAC;AACC,iBAAe;AACjB;AAEA,CAJC,WAIW;AACV,WAAS;AACT,iBAAe;AACf,eAAa;AACb,SAAO;AACT;AAEA,CAAC;AACD,CAAC;AACD,CAAC;AACC,SAAO;AACP,WAAS;AACT,UAAQ,IAAI,MAAM;AAClB,iBAAe;AACf,aAAW;AACX,cAAY,aAAa;AAC3B;AAEA,CAXC,UAWU;AACX,CAXC,aAWa;AACd,CAXC,WAWW;AACV,WAAS;AACT,gBAAc;AAChB;AAEA,CAjBC;AAkBC,UAAQ;AACR,cAAY;AACd;AAEA,CAAC;AACC,SAAO;AACP,aAAW;AACX,cAAY;AACd;AAEA,CAAC;AACC,WAAS;AACT,OAAK;AACL,mBAAiB;AACjB,cAAY;AACd;AAGA,CAAC;AACC,cAAY;AACZ,UAAQ,IAAI,MAAM;AAClB,SAAO;AACP,WAAS;AACT,iBAAe;AACf,iBAAe;AACf,WAAS;AACT,eAAa;AACb,OAAK;AACP;AAEA,CAAC;AACC,cAAY;AACZ,WAAS;AACX;AAEA,CAAC;AACC,SAAO;AACP,UAAQ;AACR,UAAQ,IAAI,MAAM;AAClB,cAAY,IAAI,MAAM;AACtB,iBAAe;AACf,aAAW,KAAK,GAAG,OAAO;AAC1B,UAAQ,EAAE,KAAK;AACjB;AAEA,WAJa;AAKX;AAAK,eAAW,OAAO;AAAO;AAC9B;AAAO,eAAW,OAAO;AAAS;AACpC;AAEA,CAAC;AACC,cAAY;AACZ,WAAS;AACT,SAAO;AACT;AAEA,CANC,QAMQ;AACP,aAAW;AACX,iBAAe;AACf,WAAS;AACX;AAEA,CAZC,QAYQ;AACP,UAAQ,EAAE,EAAE,OAAO;AACnB,SAAO;AACT;AAGA,CAAC,WAAW;AAAW,WAAS;AAAM;AACtC,CAAC,gBAAgB;AAAW,WAAS;AAAM;AAC3C,CAAC,YAAY;AAAW,WAAS;AAAM;AACvC,CAAC,SAAS;AAAW,WAAS;AAAK;AACnC,CAAC,SAAS;AAAW,WAAS;AAAM;AACpC,CAAC,UAAU;AAAW,WAAS;AAAK;AACpC,CAAC,MAAM;AAAW,WAAS;AAAK;AAChC,CAAC,UAAU;AAAW,WAAS;AAAO;AACtC,CAAC,UAAU;AAAW,WAAS;AAAM;AAGrC,OAAO,CAAC,SAAS,EAAE;AACjB,GArbD;AAsbG,aAAS;AACX;AAEA,GA9aD;AA+aG,oBAAgB;AAChB,iBAAa;AACf;AAEA,GAvZD;AAwZG,oBAAgB;AAClB;AAEA,GAtZD;AAuZG,aAAS,QAAQ;AACnB;AAEA,GAlYD;AAmYG,oBAAgB;AAChB,SAAK;AACL,iBAAa;AACf;AAEA,GAjXD;AAkXG,eAAW;AACb;AAEA,GArXD,WAqXa;AAAA,EACZ,CAtXD,WAsXa;AACV,aAAS,QAAQ;AACnB;AAEA,GAvLD;AAwLG,WAAO;AACP,YAAQ;AACV;AACF;", "names": []}
import {
  AvisModerationService,
  StatutAvis
} from "./chunk-6YR6544A.js";
import {
  DefaultV<PERSON>ueAccessor,
  FormsModule,
  NgControlStatus,
  NgControlStatusGroup,
  NgForm,
  NgModel,
  NgSelectOption,
  RequiredValidator,
  SelectControlValueAccessor,
  ɵNgNoValidate,
  ɵNgSelectMultipleOption
} from "./chunk-HQBVYEOO.js";
import "./chunk-GFHHPDQ6.js";
import "./chunk-7JDDWGD3.js";
import {
  CommonModule,
  Component,
  DatePipe,
  DecimalPipe,
  NgForOf,
  NgIf,
  setClassMetadata,
  ɵsetClassDebugInfo,
  ɵɵadvance,
  ɵɵclassMap,
  ɵɵclassProp,
  ɵɵdefineComponent,
  ɵɵdirectiveInject,
  ɵɵelement,
  ɵɵelementEnd,
  ɵɵelementStart,
  ɵɵgetCurrentView,
  ɵɵlistener,
  ɵɵnextContext,
  ɵɵpipe,
  ɵɵpipeBind2,
  ɵɵproperty,
  ɵɵpureFunction0,
  ɵɵresetView,
  ɵɵrestoreView,
  ɵɵstyleProp,
  ɵɵtemplate,
  ɵɵtext,
  ɵɵtextInterpolate,
  ɵɵtextInterpolate1,
  ɵɵtextInterpolate2,
  ɵɵtwoWayBindingSet,
  ɵɵtwoWayListener,
  ɵɵtwoWayProperty
} from "./chunk-UBZQS7JS.js";

// src/app/components/fournisseur/avis-fournisseur/avis-fournisseur.component.ts
var _c0 = () => [];
function AvisFournisseurComponent_div_15_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "div", 51)(1, "div", 52)(2, "div", 53);
    \u0275\u0275element(3, "i", 54);
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(4, "div", 55)(5, "div", 56);
    \u0275\u0275text(6);
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(7, "div", 57);
    \u0275\u0275text(8, "Total Avis");
    \u0275\u0275elementEnd()()();
    \u0275\u0275elementStart(9, "div", 58)(10, "div", 53);
    \u0275\u0275element(11, "i", 59);
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(12, "div", 55)(13, "div", 56);
    \u0275\u0275text(14);
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(15, "div", 57);
    \u0275\u0275text(16, "Signal\xE9s");
    \u0275\u0275elementEnd()()();
    \u0275\u0275elementStart(17, "div", 60)(18, "div", 53);
    \u0275\u0275element(19, "i", 6);
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(20, "div", 55)(21, "div", 56);
    \u0275\u0275text(22);
    \u0275\u0275pipe(23, "number");
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(24, "div", 57);
    \u0275\u0275text(25, "Note Moyenne");
    \u0275\u0275elementEnd()()()();
  }
  if (rf & 2) {
    const ctx_r0 = \u0275\u0275nextContext();
    \u0275\u0275advance(6);
    \u0275\u0275textInterpolate(ctx_r0.stats.totalAvis);
    \u0275\u0275advance(8);
    \u0275\u0275textInterpolate(ctx_r0.stats.avisSignales || 0);
    \u0275\u0275advance(8);
    \u0275\u0275textInterpolate(\u0275\u0275pipeBind2(23, 3, ctx_r0.stats.noteMoyenneGlobale, "1.1-1"));
  }
}
function AvisFournisseurComponent_div_63_Template(rf, ctx) {
  if (rf & 1) {
    const _r2 = \u0275\u0275getCurrentView();
    \u0275\u0275elementStart(0, "div", 61);
    \u0275\u0275element(1, "i", 62);
    \u0275\u0275text(2);
    \u0275\u0275elementStart(3, "button", 63);
    \u0275\u0275listener("click", function AvisFournisseurComponent_div_63_Template_button_click_3_listener() {
      \u0275\u0275restoreView(_r2);
      const ctx_r0 = \u0275\u0275nextContext();
      return \u0275\u0275resetView(ctx_r0.error = null);
    });
    \u0275\u0275elementEnd()();
  }
  if (rf & 2) {
    const ctx_r0 = \u0275\u0275nextContext();
    \u0275\u0275advance(2);
    \u0275\u0275textInterpolate1(" ", ctx_r0.error, " ");
  }
}
function AvisFournisseurComponent_div_64_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "div", 64)(1, "div", 65)(2, "div", 66)(3, "span", 67);
    \u0275\u0275text(4, "Chargement...");
    \u0275\u0275elementEnd()();
    \u0275\u0275elementStart(5, "p", 68);
    \u0275\u0275text(6, "Chargement des avis...");
    \u0275\u0275elementEnd()()();
  }
}
function AvisFournisseurComponent_div_65_div_1_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "div", 72)(1, "div", 73);
    \u0275\u0275element(2, "i", 54);
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(3, "h3");
    \u0275\u0275text(4, "Aucun avis trouv\xE9");
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(5, "p");
    \u0275\u0275text(6, "Il n'y a pas encore d'avis correspondant \xE0 vos crit\xE8res.");
    \u0275\u0275elementEnd()();
  }
}
function AvisFournisseurComponent_div_65_div_2_div_1_span_19_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275element(0, "span", 98);
  }
  if (rf & 2) {
    const star_r4 = ctx.$implicit;
    \u0275\u0275classMap("bi-" + star_r4);
  }
}
function AvisFournisseurComponent_div_65_div_2_div_1_div_23_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "div", 102);
    \u0275\u0275element(1, "i", 103);
    \u0275\u0275elementStart(2, "span");
    \u0275\u0275text(3);
    \u0275\u0275elementEnd()();
  }
  if (rf & 2) {
    const avisItem_r5 = \u0275\u0275nextContext().$implicit;
    \u0275\u0275advance(3);
    \u0275\u0275textInterpolate(avisItem_r5.commentaire);
  }
}
function AvisFournisseurComponent_div_65_div_2_div_1_div_24_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "div", 104);
    \u0275\u0275element(1, "i", 105);
    \u0275\u0275elementStart(2, "span");
    \u0275\u0275text(3, "Commentaire supprim\xE9");
    \u0275\u0275elementEnd()();
  }
}
function AvisFournisseurComponent_div_65_div_2_div_1_div_25_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "div", 106);
    \u0275\u0275element(1, "i", 107);
    \u0275\u0275elementStart(2, "span");
    \u0275\u0275text(3, "Note sans commentaire");
    \u0275\u0275elementEnd()();
  }
}
function AvisFournisseurComponent_div_65_div_2_div_1_Template(rf, ctx) {
  if (rf & 1) {
    const _r3 = \u0275\u0275getCurrentView();
    \u0275\u0275elementStart(0, "div", 76)(1, "div", 77)(2, "div", 78)(3, "h6", 79);
    \u0275\u0275text(4);
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(5, "small", 80);
    \u0275\u0275text(6);
    \u0275\u0275elementEnd()();
    \u0275\u0275elementStart(7, "div", 81)(8, "small", 82);
    \u0275\u0275text(9);
    \u0275\u0275pipe(10, "date");
    \u0275\u0275elementEnd()()();
    \u0275\u0275elementStart(11, "div", 83)(12, "div", 84)(13, "div", 85);
    \u0275\u0275element(14, "i", 86);
    \u0275\u0275elementStart(15, "span");
    \u0275\u0275text(16);
    \u0275\u0275elementEnd()();
    \u0275\u0275elementStart(17, "div", 87)(18, "div", 88);
    \u0275\u0275template(19, AvisFournisseurComponent_div_65_div_2_div_1_span_19_Template, 1, 2, "span", 89);
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(20, "span", 90);
    \u0275\u0275text(21);
    \u0275\u0275elementEnd()()();
    \u0275\u0275elementStart(22, "div", 91);
    \u0275\u0275template(23, AvisFournisseurComponent_div_65_div_2_div_1_div_23_Template, 4, 1, "div", 92)(24, AvisFournisseurComponent_div_65_div_2_div_1_div_24_Template, 4, 0, "div", 93)(25, AvisFournisseurComponent_div_65_div_2_div_1_div_25_Template, 4, 0, "div", 94);
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(26, "div", 95)(27, "div", 96)(28, "span", 97);
    \u0275\u0275element(29, "i", 98);
    \u0275\u0275text(30);
    \u0275\u0275elementEnd()();
    \u0275\u0275elementStart(31, "div", 99)(32, "button", 100);
    \u0275\u0275listener("click", function AvisFournisseurComponent_div_65_div_2_div_1_Template_button_click_32_listener() {
      const avisItem_r5 = \u0275\u0275restoreView(_r3).$implicit;
      const ctx_r0 = \u0275\u0275nextContext(3);
      return \u0275\u0275resetView(ctx_r0.signalerAvis(avisItem_r5));
    });
    \u0275\u0275element(33, "i", 101);
    \u0275\u0275elementStart(34, "span");
    \u0275\u0275text(35, "Signaler");
    \u0275\u0275elementEnd()()()()()();
  }
  if (rf & 2) {
    const avisItem_r5 = ctx.$implicit;
    const ctx_r0 = \u0275\u0275nextContext(3);
    \u0275\u0275advance(4);
    \u0275\u0275textInterpolate(avisItem_r5.produitNom);
    \u0275\u0275advance(2);
    \u0275\u0275textInterpolate1("R\xE9f: ", avisItem_r5.produitReference, "");
    \u0275\u0275advance(3);
    \u0275\u0275textInterpolate(\u0275\u0275pipeBind2(10, 15, avisItem_r5.datePublication, "dd/MM/yyyy HH:mm"));
    \u0275\u0275advance(7);
    \u0275\u0275textInterpolate2("", avisItem_r5.clientPrenom, " ", avisItem_r5.clientNom, "");
    \u0275\u0275advance(3);
    \u0275\u0275property("ngForOf", ctx_r0.getStars(avisItem_r5.note));
    \u0275\u0275advance(2);
    \u0275\u0275textInterpolate1("", avisItem_r5.note, "/5");
    \u0275\u0275advance(2);
    \u0275\u0275property("ngIf", avisItem_r5.commentaire && avisItem_r5.commentaire.trim() && !avisItem_r5.commentaireSupprime);
    \u0275\u0275advance();
    \u0275\u0275property("ngIf", avisItem_r5.commentaireSupprime);
    \u0275\u0275advance();
    \u0275\u0275property("ngIf", (!avisItem_r5.commentaire || !avisItem_r5.commentaire.trim()) && !avisItem_r5.commentaireSupprime);
    \u0275\u0275advance(3);
    \u0275\u0275classMap("badge-" + ctx_r0.getStatutClass(avisItem_r5.statut));
    \u0275\u0275advance();
    \u0275\u0275classMap(ctx_r0.getStatutIcon(avisItem_r5.statut));
    \u0275\u0275advance();
    \u0275\u0275textInterpolate1(" ", avisItem_r5.statutLibelle, " ");
  }
}
function AvisFournisseurComponent_div_65_div_2_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "div", 74);
    \u0275\u0275template(1, AvisFournisseurComponent_div_65_div_2_div_1_Template, 36, 18, "div", 75);
    \u0275\u0275elementEnd();
  }
  if (rf & 2) {
    const ctx_r0 = \u0275\u0275nextContext(2);
    \u0275\u0275advance();
    \u0275\u0275property("ngForOf", ctx_r0.avis);
  }
}
function AvisFournisseurComponent_div_65_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "div", 69);
    \u0275\u0275template(1, AvisFournisseurComponent_div_65_div_1_Template, 7, 0, "div", 70)(2, AvisFournisseurComponent_div_65_div_2_Template, 2, 1, "div", 71);
    \u0275\u0275elementEnd();
  }
  if (rf & 2) {
    const ctx_r0 = \u0275\u0275nextContext();
    \u0275\u0275advance();
    \u0275\u0275property("ngIf", ctx_r0.avis.length === 0);
    \u0275\u0275advance();
    \u0275\u0275property("ngIf", ctx_r0.avis.length > 0);
  }
}
function AvisFournisseurComponent_div_74_i_17_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275element(0, "i", 131);
  }
}
function AvisFournisseurComponent_div_74_i_18_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275element(0, "i", 132);
  }
}
function AvisFournisseurComponent_div_74_Template(rf, ctx) {
  if (rf & 1) {
    const _r6 = \u0275\u0275getCurrentView();
    \u0275\u0275elementStart(0, "div", 108)(1, "div", 109)(2, "h6");
    \u0275\u0275element(3, "i", 110);
    \u0275\u0275text(4, "Avis \xE0 signaler :");
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(5, "p")(6, "strong");
    \u0275\u0275text(7, "Produit :");
    \u0275\u0275elementEnd();
    \u0275\u0275text(8);
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(9, "p")(10, "strong");
    \u0275\u0275text(11, "Client :");
    \u0275\u0275elementEnd();
    \u0275\u0275text(12);
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(13, "p")(14, "strong");
    \u0275\u0275text(15, "Note :");
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(16, "span", 111);
    \u0275\u0275template(17, AvisFournisseurComponent_div_74_i_17_Template, 1, 0, "i", 112)(18, AvisFournisseurComponent_div_74_i_18_Template, 1, 0, "i", 113);
    \u0275\u0275elementEnd()();
    \u0275\u0275elementStart(19, "p")(20, "strong");
    \u0275\u0275text(21, "Commentaire :");
    \u0275\u0275elementEnd()();
    \u0275\u0275elementStart(22, "div", 114);
    \u0275\u0275text(23);
    \u0275\u0275elementEnd()();
    \u0275\u0275elementStart(24, "form", 115, 0);
    \u0275\u0275listener("ngSubmit", function AvisFournisseurComponent_div_74_Template_form_ngSubmit_24_listener() {
      \u0275\u0275restoreView(_r6);
      const ctx_r0 = \u0275\u0275nextContext();
      return \u0275\u0275resetView(ctx_r0.signalerAvis());
    });
    \u0275\u0275elementStart(26, "div", 116)(27, "label", 117);
    \u0275\u0275element(28, "i", 62);
    \u0275\u0275elementStart(29, "strong");
    \u0275\u0275text(30, "Raison du signalement *");
    \u0275\u0275elementEnd()();
    \u0275\u0275elementStart(31, "select", 118);
    \u0275\u0275twoWayListener("ngModelChange", function AvisFournisseurComponent_div_74_Template_select_ngModelChange_31_listener($event) {
      \u0275\u0275restoreView(_r6);
      const ctx_r0 = \u0275\u0275nextContext();
      \u0275\u0275twoWayBindingSet(ctx_r0.signalementData.raisonSignalement, $event) || (ctx_r0.signalementData.raisonSignalement = $event);
      return \u0275\u0275resetView($event);
    });
    \u0275\u0275elementStart(32, "option", 119);
    \u0275\u0275text(33, "-- S\xE9lectionnez une raison --");
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(34, "option", 120);
    \u0275\u0275text(35, "Contenu inappropri\xE9 ou offensant");
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(36, "option", 121);
    \u0275\u0275text(37, "Faux avis ou spam");
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(38, "option", 122);
    \u0275\u0275text(39, "Informations incorrectes sur le produit");
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(40, "option", 123);
    \u0275\u0275text(41, "Violation des r\xE8gles de la plateforme");
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(42, "option", 124);
    \u0275\u0275text(43, "Diffamation ou attaque personnelle");
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(44, "option", 125);
    \u0275\u0275text(45, "Autre raison");
    \u0275\u0275elementEnd()()();
    \u0275\u0275elementStart(46, "div", 116)(47, "label", 126);
    \u0275\u0275element(48, "i", 127);
    \u0275\u0275elementStart(49, "strong");
    \u0275\u0275text(50, "D\xE9tails suppl\xE9mentaires");
    \u0275\u0275elementEnd()();
    \u0275\u0275elementStart(51, "textarea", 128);
    \u0275\u0275twoWayListener("ngModelChange", function AvisFournisseurComponent_div_74_Template_textarea_ngModelChange_51_listener($event) {
      \u0275\u0275restoreView(_r6);
      const ctx_r0 = \u0275\u0275nextContext();
      \u0275\u0275twoWayBindingSet(ctx_r0.signalementData.detailsSignalement, $event) || (ctx_r0.signalementData.detailsSignalement = $event);
      return \u0275\u0275resetView($event);
    });
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(52, "div", 129);
    \u0275\u0275text(53, " Fournissez des d\xE9tails sp\xE9cifiques pour aider l'\xE9quipe de mod\xE9ration \xE0 traiter votre signalement. ");
    \u0275\u0275elementEnd()();
    \u0275\u0275elementStart(54, "div", 130);
    \u0275\u0275element(55, "i", 110);
    \u0275\u0275elementStart(56, "strong");
    \u0275\u0275text(57, "Important :");
    \u0275\u0275elementEnd();
    \u0275\u0275text(58, " Votre signalement sera envoy\xE9 \xE0 l'\xE9quipe de mod\xE9ration qui l'examinera dans les plus brefs d\xE9lais. Les signalements abusifs peuvent entra\xEEner des sanctions. ");
    \u0275\u0275elementEnd()()();
  }
  if (rf & 2) {
    const ctx_r0 = \u0275\u0275nextContext();
    \u0275\u0275advance(8);
    \u0275\u0275textInterpolate1(" ", ctx_r0.selectedAvisForSignalement.produitNom, "");
    \u0275\u0275advance(4);
    \u0275\u0275textInterpolate1(" ", ctx_r0.selectedAvisForSignalement.clientNom, "");
    \u0275\u0275advance(5);
    \u0275\u0275property("ngForOf", \u0275\u0275pureFunction0(7, _c0).constructor(ctx_r0.selectedAvisForSignalement.note));
    \u0275\u0275advance();
    \u0275\u0275property("ngForOf", \u0275\u0275pureFunction0(8, _c0).constructor(5 - ctx_r0.selectedAvisForSignalement.note));
    \u0275\u0275advance(5);
    \u0275\u0275textInterpolate1(" ", ctx_r0.selectedAvisForSignalement.commentaire || "Aucun commentaire", " ");
    \u0275\u0275advance(8);
    \u0275\u0275twoWayProperty("ngModel", ctx_r0.signalementData.raisonSignalement);
    \u0275\u0275advance(20);
    \u0275\u0275twoWayProperty("ngModel", ctx_r0.signalementData.detailsSignalement);
  }
}
function AvisFournisseurComponent_span_81_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "span");
    \u0275\u0275text(1, "Signaler cet avis");
    \u0275\u0275elementEnd();
  }
}
function AvisFournisseurComponent_span_82_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "span");
    \u0275\u0275element(1, "span", 133);
    \u0275\u0275text(2, " Signalement en cours... ");
    \u0275\u0275elementEnd();
  }
}
function AvisFournisseurComponent_div_83_Template(rf, ctx) {
  if (rf & 1) {
    const _r7 = \u0275\u0275getCurrentView();
    \u0275\u0275elementStart(0, "div", 134);
    \u0275\u0275listener("click", function AvisFournisseurComponent_div_83_Template_div_click_0_listener() {
      \u0275\u0275restoreView(_r7);
      const ctx_r0 = \u0275\u0275nextContext();
      return \u0275\u0275resetView(ctx_r0.closeSignalementModal());
    });
    \u0275\u0275elementEnd();
  }
  if (rf & 2) {
    const ctx_r0 = \u0275\u0275nextContext();
    \u0275\u0275classProp("show", ctx_r0.showSignalementModal);
  }
}
var AvisFournisseurComponent = class _AvisFournisseurComponent {
  avisModerationService;
  avis = [];
  stats = null;
  loading = false;
  error = null;
  // Filtres
  filter = {
    page: 1,
    pageSize: 10,
    sortBy: "datePublication",
    sortDesc: true
  };
  // Énumérations pour le template
  StatutAvis = StatutAvis;
  // Réponse aux avis
  selectedAvis = null;
  reponseForm = {
    reponse: ""
  };
  // Signalement d'avis
  showSignalementModal = false;
  signalementData = {
    raisonSignalement: "",
    detailsSignalement: ""
  };
  selectedAvisForSignalement = null;
  constructor(avisModerationService) {
    this.avisModerationService = avisModerationService;
  }
  ngOnInit() {
    console.log("\u{1F50D} AvisFournisseurComponent - Initialisation");
    console.log("\u{1F50D} Utilisateur actuel:", this.getCurrentUserInfo());
    this.loadAvis();
    this.loadStats();
  }
  getCurrentUserInfo() {
    const token = localStorage.getItem("auth_token");
    const user = localStorage.getItem("current_user");
    return {
      hasToken: !!token,
      tokenPreview: token ? token.substring(0, 20) + "..." : null,
      user: user ? JSON.parse(user) : null
    };
  }
  loadAvis() {
    console.log("\u{1F50D} Chargement des avis avec filtre:", this.filter);
    this.loading = true;
    this.error = null;
    this.avisModerationService.getAvisFournisseur(this.filter).subscribe({
      next: (data) => {
        console.log("\u2705 Avis charg\xE9s avec succ\xE8s:", data.length, "avis");
        this.avis = data;
        this.loading = false;
      },
      error: (error) => {
        console.error("\u274C Erreur lors du chargement des avis:", error);
        console.error("\u274C D\xE9tails de l'erreur:", {
          status: error.status,
          statusText: error.statusText,
          url: error.url,
          message: error.message
        });
        this.error = "Erreur lors du chargement des avis";
        this.loading = false;
      }
    });
  }
  loadStats() {
    console.log("\u{1F50D} Chargement des statistiques");
    this.avisModerationService.getAvisStatsFournisseur().subscribe({
      next: (stats) => {
        console.log("\u2705 Statistiques charg\xE9es avec succ\xE8s:", stats);
        this.stats = stats;
      },
      error: (error) => {
        console.error("\u274C Erreur lors du chargement des statistiques:", error);
        console.error("\u274C D\xE9tails de l'erreur:", {
          status: error.status,
          statusText: error.statusText,
          url: error.url,
          message: error.message
        });
      }
    });
  }
  onFilterChange() {
    this.filter.page = 1;
    this.loadAvis();
  }
  onSortChange(sortBy) {
    if (this.filter.sortBy === sortBy) {
      this.filter.sortDesc = !this.filter.sortDesc;
    } else {
      this.filter.sortBy = sortBy;
      this.filter.sortDesc = true;
    }
    this.loadAvis();
  }
  openReponseModal(avis) {
    this.selectedAvis = avis;
    this.reponseForm = {
      reponse: avis.commentaireModeration || ""
    };
  }
  closeReponseModal() {
    this.selectedAvis = null;
    this.reponseForm = {
      reponse: ""
    };
  }
  repondreAvis(avis) {
    if (avis) {
      this.openReponseModal(avis);
      return;
    }
    if (!this.selectedAvis || !this.reponseForm.reponse.trim())
      return;
    this.avisModerationService.repondreAvis(this.selectedAvis.id, this.reponseForm.reponse).subscribe({
      next: (updatedAvis) => {
        const index = this.avis.findIndex((a) => a.id === updatedAvis.id);
        if (index !== -1) {
          this.avis[index] = updatedAvis;
        }
        this.closeReponseModal();
        this.loadStats();
      },
      error: (error) => {
        console.error("Erreur lors de la r\xE9ponse:", error);
        this.error = "Erreur lors de l'envoi de la r\xE9ponse";
      }
    });
  }
  getStatutLibelle(statut) {
    return this.avisModerationService.getStatutLibelle(statut);
  }
  getStatutColor(statut) {
    return this.avisModerationService.getStatutColor(statut);
  }
  getStatutIcon(statut) {
    return this.avisModerationService.getStatutIcon(statut);
  }
  getStars(note) {
    const stars = [];
    for (let i = 1; i <= 5; i++) {
      stars.push(i <= note ? "star-fill" : "star");
    }
    return stars;
  }
  resetFilters() {
    this.filter = {
      page: 1,
      pageSize: 10,
      sortBy: "datePublication",
      sortDesc: true
    };
    this.loadAvis();
  }
  exportAvis() {
    console.log("Export des avis \xE0 impl\xE9menter");
  }
  // Méthodes pour le signalement d'avis
  openSignalementModal(avis) {
    this.selectedAvisForSignalement = avis;
    this.signalementData.raisonSignalement = "";
    this.signalementData.detailsSignalement = "";
    this.showSignalementModal = true;
  }
  closeSignalementModal() {
    this.showSignalementModal = false;
    this.selectedAvisForSignalement = null;
    this.signalementData.raisonSignalement = "";
    this.signalementData.detailsSignalement = "";
  }
  signalerAvis(avis) {
    if (avis) {
      this.openSignalementModal(avis);
      return;
    }
    if (!this.selectedAvisForSignalement || !this.signalementData.raisonSignalement.trim()) {
      return;
    }
    this.loading = true;
    const signalementInfo = {
      raisonSignalement: this.signalementData.raisonSignalement,
      detailsSignalement: this.signalementData.detailsSignalement || "",
      avisId: this.selectedAvisForSignalement.id,
      produitNom: this.selectedAvisForSignalement.produitNom,
      clientNom: this.selectedAvisForSignalement.clientNom
    };
    console.log("\u{1F6A8} Envoi du signalement:", signalementInfo);
    this.avisModerationService.signalerAvis(this.selectedAvisForSignalement.id, this.signalementData.raisonSignalement.trim(), this.signalementData.detailsSignalement.trim()).subscribe({
      next: (avisUpdated) => {
        const index = this.avis.findIndex((a) => a.id === avisUpdated.id);
        if (index !== -1) {
          this.avis[index] = avisUpdated;
        }
        this.closeSignalementModal();
        this.loading = false;
        console.log("\u2705 Avis signal\xE9 avec succ\xE8s. Notification envoy\xE9e \xE0 l'administrateur.");
        alert("Votre signalement a \xE9t\xE9 envoy\xE9 avec succ\xE8s. L'\xE9quipe de mod\xE9ration l'examinera dans les plus brefs d\xE9lais.");
      },
      error: (error) => {
        console.error("\u274C Erreur lors du signalement de l'avis:", error);
        this.loading = false;
        alert("Erreur lors de l'envoi du signalement. Veuillez r\xE9essayer.");
      }
    });
  }
  // Pagination
  previousPage() {
    if (this.filter.page && this.filter.page > 1) {
      this.filter.page--;
      this.loadAvis();
    }
  }
  nextPage() {
    if (this.filter.page) {
      this.filter.page++;
      this.loadAvis();
    }
  }
  // Méthodes pour les statistiques
  getStatsArray() {
    if (!this.stats)
      return [];
    const total = this.stats.totalAvis;
    return [5, 4, 3, 2, 1].map((note) => ({
      note,
      count: this.stats.avisParNote[note] || 0,
      percentage: total > 0 ? (this.stats.avisParNote[note] || 0) / total * 100 : 0
    }));
  }
  getProgressBarWidth(percentage) {
    return `${Math.max(percentage, 2)}%`;
  }
  // Nouvelles méthodes pour le nouveau design
  getStatutClass(statut) {
    switch (statut) {
      case StatutAvis.Publie:
        return "success";
      case StatutAvis.Signale:
        return "warning";
      case StatutAvis.CommentaireSupprime:
        return "danger";
      default:
        return "secondary";
    }
  }
  voirDetails(avis) {
    this.selectedAvis = avis;
  }
  static \u0275fac = function AvisFournisseurComponent_Factory(__ngFactoryType__) {
    return new (__ngFactoryType__ || _AvisFournisseurComponent)(\u0275\u0275directiveInject(AvisModerationService));
  };
  static \u0275cmp = /* @__PURE__ */ \u0275\u0275defineComponent({ type: _AvisFournisseurComponent, selectors: [["app-avis-fournisseur"]], decls: 84, vars: 22, consts: [["signalementForm", "ngForm"], [1, "container-fluid"], [1, "page-header", "mb-4"], [1, "header-content"], [1, "header-title"], [1, "title-icon"], [1, "bi", "bi-star-fill"], [1, "title-text"], [1, "page-title"], [1, "page-subtitle"], [1, "header-actions"], ["title", "Actualiser", 1, "btn", "btn-outline-primary", 3, "click"], [1, "bi", "bi-arrow-clockwise", "me-2"], ["class", "stats-container mb-4", 4, "ngIf"], [1, "filters-section", "mb-4"], [1, "filters-card"], [1, "filters-header"], [1, "bi", "bi-funnel", "me-2"], [1, "filters-content"], [1, "row", "g-3"], [1, "col-md-3"], [1, "form-label"], [1, "form-select", 3, "ngModelChange", "change", "ngModel"], [3, "value"], [1, "input-group"], [1, "input-group-text"], [1, "bi", "bi-search"], ["type", "text", "placeholder", "Rechercher...", 1, "form-control", 3, "ngModelChange", "keyup.enter", "ngModel"], [1, "col-md-2"], ["value", "datePublication"], ["value", "note"], ["value", "produitNom"], [1, "col-md-2", "d-flex", "align-items-end"], [1, "btn", "btn-outline-secondary", "w-100", 3, "click"], [1, "bi", "bi-x-circle", "me-2"], ["class", "alert alert-danger alert-dismissible fade show", 4, "ngIf"], ["class", "loading-container", 4, "ngIf"], ["class", "avis-container", 4, "ngIf"], ["id", "signalementModal", "tabindex", "-1", "aria-labelledby", "signalementModalLabel", "aria-hidden", "true", 1, "modal", "fade"], [1, "modal-dialog", "modal-lg"], [1, "modal-content"], [1, "modal-header", "bg-warning", "text-white"], ["id", "signalementModalLabel", 1, "modal-title"], [1, "bi", "bi-flag-fill", "me-2"], ["type", "button", "aria-label", "Close", 1, "btn-close", "btn-close-white", 3, "click"], ["class", "modal-body", 4, "ngIf"], [1, "modal-footer"], ["type", "button", 1, "btn", "btn-secondary", 3, "click"], ["type", "button", 1, "btn", "btn-warning", 3, "click", "disabled"], [4, "ngIf"], ["class", "modal-backdrop fade", 3, "show", "click", 4, "ngIf"], [1, "stats-container", "mb-4"], [1, "stats-card", "stats-primary"], [1, "stats-icon"], [1, "bi", "bi-chat-square-text"], [1, "stats-content"], [1, "stats-number"], [1, "stats-label"], [1, "stats-card", "stats-warning"], [1, "bi", "bi-exclamation-triangle"], [1, "stats-card", "stats-info"], [1, "alert", "alert-danger", "alert-dismissible", "fade", "show"], [1, "bi", "bi-exclamation-triangle", "me-2"], ["type", "button", 1, "btn-close", 3, "click"], [1, "loading-container"], [1, "loading-spinner"], ["role", "status", 1, "spinner-border", "text-primary"], [1, "visually-hidden"], [1, "loading-text"], [1, "avis-container"], ["class", "empty-state", 4, "ngIf"], ["class", "avis-list", 4, "ngIf"], [1, "empty-state"], [1, "empty-icon"], [1, "avis-list"], ["class", "avis-card", 4, "ngFor", "ngForOf"], [1, "avis-card"], [1, "avis-header"], [1, "avis-product"], [1, "product-name"], [1, "product-ref"], [1, "avis-date"], [1, "text-muted"], [1, "avis-content"], [1, "avis-client"], [1, "client-info"], [1, "bi", "bi-person-circle", "me-2"], [1, "avis-rating"], [1, "stars"], ["class", "bi", 3, "class", 4, "ngFor", "ngForOf"], [1, "rating-value"], [1, "avis-comment"], ["class", "comment-content", 4, "ngIf"], ["class", "comment-deleted", 4, "ngIf"], ["class", "comment-none", 4, "ngIf"], [1, "avis-footer"], [1, "avis-status"], [1, "badge"], [1, "bi"], [1, "avis-actions"], ["title", "Signaler", 1, "btn", "btn-sm", "btn-outline-warning", 3, "click"], [1, "bi", "bi-flag", "me-1"], [1, "comment-content"], [1, "bi", "bi-chat-quote", "me-2"], [1, "comment-deleted"], [1, "bi", "bi-chat-square-x", "me-2"], [1, "comment-none"], [1, "bi", "bi-star", "me-2"], [1, "modal-body"], [1, "alert", "alert-info"], [1, "bi", "bi-info-circle", "me-2"], [1, "ms-2"], ["class", "bi bi-star-fill text-warning", 4, "ngFor", "ngForOf"], ["class", "bi bi-star text-muted", 4, "ngFor", "ngForOf"], [1, "bg-light", "p-3", "rounded"], [3, "ngSubmit"], [1, "mb-3"], ["for", "raisonSignalement", 1, "form-label"], ["id", "raisonSignalement", "name", "raisonSignalement", "required", "", 1, "form-select", 3, "ngModelChange", "ngModel"], ["value", ""], ["value", "contenu_inapproprie"], ["value", "faux_avis"], ["value", "information_incorrecte"], ["value", "violation_regles"], ["value", "diffamation"], ["value", "autre"], ["for", "detailsSignalement", 1, "form-label"], [1, "bi", "bi-chat-text", "me-2"], ["id", "detailsSignalement", "rows", "4", "name", "detailsSignalement", "placeholder", "Expliquez en d\xE9tail pourquoi vous signalez cet avis...", 1, "form-control", 3, "ngModelChange", "ngModel"], [1, "form-text"], [1, "alert", "alert-warning"], [1, "bi", "bi-star-fill", "text-warning"], [1, "bi", "bi-star", "text-muted"], ["role", "status", 1, "spinner-border", "spinner-border-sm", "me-2"], [1, "modal-backdrop", "fade", 3, "click"]], template: function AvisFournisseurComponent_Template(rf, ctx) {
    if (rf & 1) {
      \u0275\u0275elementStart(0, "div", 1)(1, "div", 2)(2, "div", 3)(3, "div", 4)(4, "div", 5);
      \u0275\u0275element(5, "i", 6);
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(6, "div", 7)(7, "h1", 8);
      \u0275\u0275text(8, "Mes Avis Clients");
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(9, "p", 9);
      \u0275\u0275text(10, "Consultez et r\xE9pondez aux avis de vos clients");
      \u0275\u0275elementEnd()()();
      \u0275\u0275elementStart(11, "div", 10)(12, "button", 11);
      \u0275\u0275listener("click", function AvisFournisseurComponent_Template_button_click_12_listener() {
        return ctx.loadAvis();
      });
      \u0275\u0275element(13, "i", 12);
      \u0275\u0275text(14, " Actualiser ");
      \u0275\u0275elementEnd()()()();
      \u0275\u0275template(15, AvisFournisseurComponent_div_15_Template, 26, 6, "div", 13);
      \u0275\u0275elementStart(16, "div", 14)(17, "div", 15)(18, "div", 16)(19, "h5");
      \u0275\u0275element(20, "i", 17);
      \u0275\u0275text(21, "Filtres");
      \u0275\u0275elementEnd()();
      \u0275\u0275elementStart(22, "div", 18)(23, "div", 19)(24, "div", 20)(25, "label", 21);
      \u0275\u0275text(26, "Statut");
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(27, "select", 22);
      \u0275\u0275twoWayListener("ngModelChange", function AvisFournisseurComponent_Template_select_ngModelChange_27_listener($event) {
        \u0275\u0275twoWayBindingSet(ctx.filter.statut, $event) || (ctx.filter.statut = $event);
        return $event;
      });
      \u0275\u0275listener("change", function AvisFournisseurComponent_Template_select_change_27_listener() {
        return ctx.onFilterChange();
      });
      \u0275\u0275elementStart(28, "option", 23);
      \u0275\u0275text(29, "Tous les statuts");
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(30, "option", 23);
      \u0275\u0275text(31, "Signal\xE9s");
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(32, "option", 23);
      \u0275\u0275text(33, "Commentaire supprim\xE9");
      \u0275\u0275elementEnd()()();
      \u0275\u0275elementStart(34, "div", 20)(35, "label", 21);
      \u0275\u0275text(36, "Recherche");
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(37, "div", 24)(38, "span", 25);
      \u0275\u0275element(39, "i", 26);
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(40, "input", 27);
      \u0275\u0275twoWayListener("ngModelChange", function AvisFournisseurComponent_Template_input_ngModelChange_40_listener($event) {
        \u0275\u0275twoWayBindingSet(ctx.filter.recherche, $event) || (ctx.filter.recherche = $event);
        return $event;
      });
      \u0275\u0275listener("keyup.enter", function AvisFournisseurComponent_Template_input_keyup_enter_40_listener() {
        return ctx.onFilterChange();
      });
      \u0275\u0275elementEnd()()();
      \u0275\u0275elementStart(41, "div", 28)(42, "label", 21);
      \u0275\u0275text(43, "Trier par");
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(44, "select", 22);
      \u0275\u0275twoWayListener("ngModelChange", function AvisFournisseurComponent_Template_select_ngModelChange_44_listener($event) {
        \u0275\u0275twoWayBindingSet(ctx.filter.sortBy, $event) || (ctx.filter.sortBy = $event);
        return $event;
      });
      \u0275\u0275listener("change", function AvisFournisseurComponent_Template_select_change_44_listener() {
        return ctx.onFilterChange();
      });
      \u0275\u0275elementStart(45, "option", 29);
      \u0275\u0275text(46, "Date");
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(47, "option", 30);
      \u0275\u0275text(48, "Note");
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(49, "option", 31);
      \u0275\u0275text(50, "Produit");
      \u0275\u0275elementEnd()()();
      \u0275\u0275elementStart(51, "div", 28)(52, "label", 21);
      \u0275\u0275text(53, "Ordre");
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(54, "select", 22);
      \u0275\u0275twoWayListener("ngModelChange", function AvisFournisseurComponent_Template_select_ngModelChange_54_listener($event) {
        \u0275\u0275twoWayBindingSet(ctx.filter.sortDesc, $event) || (ctx.filter.sortDesc = $event);
        return $event;
      });
      \u0275\u0275listener("change", function AvisFournisseurComponent_Template_select_change_54_listener() {
        return ctx.onFilterChange();
      });
      \u0275\u0275elementStart(55, "option", 23);
      \u0275\u0275text(56, "D\xE9croissant");
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(57, "option", 23);
      \u0275\u0275text(58, "Croissant");
      \u0275\u0275elementEnd()()();
      \u0275\u0275elementStart(59, "div", 32)(60, "button", 33);
      \u0275\u0275listener("click", function AvisFournisseurComponent_Template_button_click_60_listener() {
        return ctx.resetFilters();
      });
      \u0275\u0275element(61, "i", 34);
      \u0275\u0275text(62, "Reset ");
      \u0275\u0275elementEnd()()()()()();
      \u0275\u0275template(63, AvisFournisseurComponent_div_63_Template, 4, 1, "div", 35)(64, AvisFournisseurComponent_div_64_Template, 7, 0, "div", 36)(65, AvisFournisseurComponent_div_65_Template, 3, 2, "div", 37);
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(66, "div", 38)(67, "div", 39)(68, "div", 40)(69, "div", 41)(70, "h5", 42);
      \u0275\u0275element(71, "i", 43);
      \u0275\u0275text(72, " Signaler un probl\xE8me avec cet avis ");
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(73, "button", 44);
      \u0275\u0275listener("click", function AvisFournisseurComponent_Template_button_click_73_listener() {
        return ctx.closeSignalementModal();
      });
      \u0275\u0275elementEnd()();
      \u0275\u0275template(74, AvisFournisseurComponent_div_74_Template, 59, 9, "div", 45);
      \u0275\u0275elementStart(75, "div", 46)(76, "button", 47);
      \u0275\u0275listener("click", function AvisFournisseurComponent_Template_button_click_76_listener() {
        return ctx.closeSignalementModal();
      });
      \u0275\u0275element(77, "i", 34);
      \u0275\u0275text(78, "Annuler ");
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(79, "button", 48);
      \u0275\u0275listener("click", function AvisFournisseurComponent_Template_button_click_79_listener() {
        return ctx.signalerAvis();
      });
      \u0275\u0275element(80, "i", 43);
      \u0275\u0275template(81, AvisFournisseurComponent_span_81_Template, 2, 0, "span", 49)(82, AvisFournisseurComponent_span_82_Template, 3, 0, "span", 49);
      \u0275\u0275elementEnd()()()()();
      \u0275\u0275template(83, AvisFournisseurComponent_div_83_Template, 1, 2, "div", 50);
    }
    if (rf & 2) {
      \u0275\u0275advance(15);
      \u0275\u0275property("ngIf", ctx.stats);
      \u0275\u0275advance(12);
      \u0275\u0275twoWayProperty("ngModel", ctx.filter.statut);
      \u0275\u0275advance();
      \u0275\u0275property("value", void 0);
      \u0275\u0275advance(2);
      \u0275\u0275property("value", ctx.StatutAvis.Signale);
      \u0275\u0275advance(2);
      \u0275\u0275property("value", ctx.StatutAvis.CommentaireSupprime);
      \u0275\u0275advance(8);
      \u0275\u0275twoWayProperty("ngModel", ctx.filter.recherche);
      \u0275\u0275advance(4);
      \u0275\u0275twoWayProperty("ngModel", ctx.filter.sortBy);
      \u0275\u0275advance(10);
      \u0275\u0275twoWayProperty("ngModel", ctx.filter.sortDesc);
      \u0275\u0275advance();
      \u0275\u0275property("value", true);
      \u0275\u0275advance(2);
      \u0275\u0275property("value", false);
      \u0275\u0275advance(6);
      \u0275\u0275property("ngIf", ctx.error);
      \u0275\u0275advance();
      \u0275\u0275property("ngIf", ctx.loading);
      \u0275\u0275advance();
      \u0275\u0275property("ngIf", !ctx.loading);
      \u0275\u0275advance();
      \u0275\u0275styleProp("display", ctx.showSignalementModal ? "block" : "none");
      \u0275\u0275classProp("show", ctx.showSignalementModal);
      \u0275\u0275advance(8);
      \u0275\u0275property("ngIf", ctx.selectedAvisForSignalement);
      \u0275\u0275advance(5);
      \u0275\u0275property("disabled", !ctx.signalementData.raisonSignalement || ctx.loading);
      \u0275\u0275advance(2);
      \u0275\u0275property("ngIf", !ctx.loading);
      \u0275\u0275advance();
      \u0275\u0275property("ngIf", ctx.loading);
      \u0275\u0275advance();
      \u0275\u0275property("ngIf", ctx.showSignalementModal);
    }
  }, dependencies: [CommonModule, NgForOf, NgIf, DecimalPipe, DatePipe, FormsModule, \u0275NgNoValidate, NgSelectOption, \u0275NgSelectMultipleOption, DefaultValueAccessor, SelectControlValueAccessor, NgControlStatus, NgControlStatusGroup, RequiredValidator, NgModel, NgForm], styles: ['\n\n[_ngcontent-%COMP%]:root {\n  --primary-color: #007bff;\n  --secondary-color: #6c757d;\n  --success-color: #28a745;\n  --error-color: #dc3545;\n  --warning-color: #ffc107;\n  --info-color: #17a2b8;\n  --accent-color: #28a745;\n  --text-color: #333333;\n  --background-color: #ffffff;\n  --border-color: #e0e0e0;\n  --gradient-primary:\n    linear-gradient(\n      135deg,\n      #007bff,\n      rgb(51, 149.4, 255));\n  --primary-color-hover: rgb(0, 98.4, 204);\n  --accent-color-hover: rgb(30.1449275362, 125.8550724638, 52);\n}\n.btn-primary[_ngcontent-%COMP%] {\n  background: var(--gradient-primary);\n  border: none;\n  transition: all 0.3s ease;\n}\n.btn-primary[_ngcontent-%COMP%]:hover {\n  background: var(--primary-color-hover);\n  transform: translateY(-1px);\n}\n.btn-success[_ngcontent-%COMP%] {\n  background-color: var(--success-color);\n  border-color: var(--success-color);\n}\n.btn-success[_ngcontent-%COMP%]:hover {\n  background-color: var(--accent-color-hover);\n  border-color: var(--accent-color-hover);\n}\n.alert-success[_ngcontent-%COMP%] {\n  background-color: rgba(40, 167, 69, 0.1);\n  border-color: var(--success-color);\n  color: var(--success-color);\n}\n.alert-danger[_ngcontent-%COMP%] {\n  background-color: rgba(220, 53, 69, 0.1);\n  border-color: var(--error-color);\n  color: var(--error-color);\n}\n.alert-warning[_ngcontent-%COMP%] {\n  background-color: rgba(255, 193, 7, 0.1);\n  border-color: var(--warning-color);\n  color: #856404;\n}\n.alert-info[_ngcontent-%COMP%] {\n  background-color: rgba(23, 162, 184, 0.1);\n  border-color: var(--info-color);\n  color: var(--info-color);\n}\n.form-control[_ngcontent-%COMP%] {\n  background-color: var(--card-background-color);\n  border-color: var(--border-color);\n  color: var(--text-color);\n  transition: all 0.3s ease;\n}\n.form-control[_ngcontent-%COMP%]:focus {\n  background-color: var(--card-background-color);\n  border-color: var(--primary-color);\n  color: var(--text-color);\n  box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);\n}\n.table[_ngcontent-%COMP%] {\n  background-color: var(--card-background-color);\n  color: var(--text-color);\n}\n.table[_ngcontent-%COMP%]   th[_ngcontent-%COMP%] {\n  background-color: var(--primary-color);\n  color: white;\n  border: none;\n}\n.table[_ngcontent-%COMP%]   td[_ngcontent-%COMP%] {\n  border-color: var(--border-color);\n}\n.table-striped[_ngcontent-%COMP%]   tbody[_ngcontent-%COMP%]   tr[_ngcontent-%COMP%]:nth-of-type(odd) {\n  background-color: rgba(0, 0, 0, 0.05);\n}\n[data-theme=dark][_ngcontent-%COMP%]   .table-striped[_ngcontent-%COMP%]   tbody[_ngcontent-%COMP%]   tr[_ngcontent-%COMP%]:nth-of-type(odd) {\n  background-color: rgba(255, 255, 255, 0.05);\n}\n.navbar[_ngcontent-%COMP%] {\n  background-color: var(--card-background-color) !important;\n  border-bottom: 1px solid var(--border-color);\n  box-shadow: var(--card-shadow);\n}\n.navbar-brand[_ngcontent-%COMP%], \n.navbar-nav[_ngcontent-%COMP%]   .nav-link[_ngcontent-%COMP%] {\n  color: var(--text-color) !important;\n  transition: color 0.3s ease;\n}\n.navbar-nav[_ngcontent-%COMP%]   .nav-link[_ngcontent-%COMP%]:hover {\n  color: var(--primary-color) !important;\n}\n.sidebar[_ngcontent-%COMP%] {\n  background-color: var(--sidebar-bg);\n  color: var(--sidebar-text);\n  transition: all 0.3s ease;\n}\n.sidebar[_ngcontent-%COMP%]   .nav-link[_ngcontent-%COMP%] {\n  color: var(--sidebar-text);\n  transition: all 0.3s ease;\n  border-radius: 0.375rem;\n  margin: 0.25rem 0;\n}\n.sidebar[_ngcontent-%COMP%]   .nav-link[_ngcontent-%COMP%]:hover {\n  background-color: var(--sidebar-hover);\n  color: var(--sidebar-text);\n}\n.sidebar[_ngcontent-%COMP%]   .nav-link.active[_ngcontent-%COMP%] {\n  background-color: var(--primary-color);\n  color: white;\n}\n.badge[_ngcontent-%COMP%] {\n  font-size: 0.75em;\n  padding: 0.375rem 0.75rem;\n}\n.badge-success[_ngcontent-%COMP%] {\n  background-color: var(--success-color);\n}\n.badge-danger[_ngcontent-%COMP%] {\n  background-color: var(--error-color);\n}\n.badge-warning[_ngcontent-%COMP%] {\n  background-color: var(--warning-color);\n  color: #212529;\n}\n.badge-info[_ngcontent-%COMP%] {\n  background-color: var(--info-color);\n}\n@keyframes _ngcontent-%COMP%_fadeIn {\n  from {\n    opacity: 0;\n    transform: translateY(20px);\n  }\n  to {\n    opacity: 1;\n    transform: translateY(0);\n  }\n}\n@keyframes _ngcontent-%COMP%_slideIn {\n  from {\n    transform: translateX(-100%);\n  }\n  to {\n    transform: translateX(0);\n  }\n}\n.fade-in[_ngcontent-%COMP%] {\n  animation: _ngcontent-%COMP%_fadeIn 0.5s ease-out;\n}\n.slide-in[_ngcontent-%COMP%] {\n  animation: _ngcontent-%COMP%_slideIn 0.3s ease-out;\n}\n@media (max-width: 768px) {\n  .card[_ngcontent-%COMP%] {\n    margin-bottom: 1rem;\n  }\n  .table-responsive[_ngcontent-%COMP%] {\n    font-size: 0.875rem;\n  }\n}\n.modal-content[_ngcontent-%COMP%] {\n  background-color: var(--card-background-color);\n  border: 1px solid var(--border-color);\n}\n.modal-header[_ngcontent-%COMP%] {\n  border-bottom: 1px solid var(--border-color);\n}\n.modal-footer[_ngcontent-%COMP%] {\n  border-top: 1px solid var(--border-color);\n}\n.dropdown-menu[_ngcontent-%COMP%] {\n  background-color: var(--card-background-color);\n  border: 1px solid var(--border-color);\n  box-shadow: var(--card-shadow);\n}\n.dropdown-item[_ngcontent-%COMP%] {\n  color: var(--text-color);\n  transition: all 0.3s ease;\n}\n.dropdown-item[_ngcontent-%COMP%]:hover {\n  background-color: var(--card-background-color-hover);\n  color: var(--text-color-hover);\n}\n.tooltip[_ngcontent-%COMP%]   .tooltip-inner[_ngcontent-%COMP%] {\n  background-color: var(--sidebar-bg);\n  color: var(--sidebar-text);\n}\n.progress[_ngcontent-%COMP%] {\n  background-color: var(--border-color);\n}\n.progress-bar[_ngcontent-%COMP%] {\n  background-color: var(--primary-color);\n}\n.container-fluid[_ngcontent-%COMP%] {\n  background:\n    linear-gradient(\n      135deg,\n      #f5f7fa 0%,\n      #c3cfe2 100%);\n  min-height: 100vh;\n  padding: 2rem;\n  position: relative;\n}\n.container-fluid[_ngcontent-%COMP%]::before {\n  content: "";\n  position: absolute;\n  top: 0;\n  left: 0;\n  right: 0;\n  height: 300px;\n  background:\n    linear-gradient(\n      135deg,\n      rgba(102, 126, 234, 0.1) 0%,\n      rgba(118, 75, 162, 0.1) 100%);\n  z-index: 0;\n}\n.container-fluid[_ngcontent-%COMP%]    > *[_ngcontent-%COMP%] {\n  position: relative;\n  z-index: 1;\n}\n@media (max-width: 768px) {\n  .container-fluid[_ngcontent-%COMP%] {\n    padding: 1rem;\n  }\n}\n.page-header[_ngcontent-%COMP%] {\n  background:\n    linear-gradient(\n      135deg,\n      #667eea 0%,\n      #764ba2 100%);\n  border-radius: 24px;\n  padding: 3rem 2.5rem;\n  margin-bottom: 3rem;\n  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.1);\n  color: white;\n  position: relative;\n  overflow: hidden;\n}\n.page-header[_ngcontent-%COMP%]   .header-content[_ngcontent-%COMP%] {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  position: relative;\n  z-index: 2;\n}\n@media (max-width: 768px) {\n  .page-header[_ngcontent-%COMP%]   .header-content[_ngcontent-%COMP%] {\n    flex-direction: column;\n    gap: 2rem;\n    text-align: center;\n  }\n}\n.page-header[_ngcontent-%COMP%]   .header-title[_ngcontent-%COMP%] {\n  display: flex;\n  align-items: center;\n  gap: 1.5rem;\n}\n.page-header[_ngcontent-%COMP%]   .header-title[_ngcontent-%COMP%]   .title-icon[_ngcontent-%COMP%] {\n  width: 80px;\n  height: 80px;\n  background: rgba(255, 255, 255, 0.2);\n  border-radius: 50%;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  font-size: 2.5rem;\n  -webkit-backdrop-filter: blur(10px);\n  backdrop-filter: blur(10px);\n  border: 2px solid rgba(255, 255, 255, 0.3);\n}\n.page-header[_ngcontent-%COMP%]   .header-title[_ngcontent-%COMP%]   .title-text[_ngcontent-%COMP%]   .page-title[_ngcontent-%COMP%] {\n  font-size: 2.5rem;\n  font-weight: 700;\n  margin: 0;\n  text-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);\n}\n@media (max-width: 768px) {\n  .page-header[_ngcontent-%COMP%]   .header-title[_ngcontent-%COMP%]   .title-text[_ngcontent-%COMP%]   .page-title[_ngcontent-%COMP%] {\n    font-size: 2rem;\n  }\n}\n.page-header[_ngcontent-%COMP%]   .header-title[_ngcontent-%COMP%]   .title-text[_ngcontent-%COMP%]   .page-subtitle[_ngcontent-%COMP%] {\n  font-size: 1.2rem;\n  margin: 0.5rem 0 0 0;\n  opacity: 0.9;\n  font-weight: 300;\n}\n.page-header[_ngcontent-%COMP%]   .header-actions[_ngcontent-%COMP%]   .btn[_ngcontent-%COMP%] {\n  background: rgba(255, 255, 255, 0.2);\n  border: 2px solid rgba(255, 255, 255, 0.3);\n  color: white;\n  padding: 0.75rem 1.5rem;\n  border-radius: 50px;\n  font-weight: 600;\n  -webkit-backdrop-filter: blur(10px);\n  backdrop-filter: blur(10px);\n  transition: all 0.2s ease-out;\n}\n.page-header[_ngcontent-%COMP%]   .header-actions[_ngcontent-%COMP%]   .btn[_ngcontent-%COMP%]:hover {\n  background: rgba(255, 255, 255, 0.3);\n  border-color: rgba(255, 255, 255, 0.5);\n  transform: translateY(-2px);\n  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2);\n}\n.stats-container[_ngcontent-%COMP%] {\n  display: grid;\n  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));\n  gap: 2rem;\n  margin-bottom: 3rem;\n}\n@media (max-width: 768px) {\n  .stats-container[_ngcontent-%COMP%] {\n    grid-template-columns: 1fr;\n    gap: 1rem;\n  }\n}\n.stats-card[_ngcontent-%COMP%] {\n  background: white;\n  border-radius: 16px;\n  padding: 2rem;\n  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);\n  border: 1px solid rgba(255, 255, 255, 0.8);\n  -webkit-backdrop-filter: blur(10px);\n  backdrop-filter: blur(10px);\n  transition: all 0.4s cubic-bezier(0.25, 0.8, 0.25, 1);\n  position: relative;\n  overflow: hidden;\n}\n.stats-card[_ngcontent-%COMP%]::before {\n  content: "";\n  position: absolute;\n  top: 0;\n  left: 0;\n  right: 0;\n  height: 4px;\n  background:\n    linear-gradient(\n      135deg,\n      #007bff 0%,\n      rgb(76.5, 162.6, 255) 50%,\n      rgb(0, 110.7, 229.5) 100%);\n}\n.stats-card[_ngcontent-%COMP%]:hover {\n  transform: translateY(-8px);\n  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.12);\n}\n.stats-card.stats-primary[_ngcontent-%COMP%]::before {\n  background:\n    linear-gradient(\n      135deg,\n      #007bff 0%,\n      rgb(76.5, 162.6, 255) 50%,\n      rgb(0, 110.7, 229.5) 100%);\n}\n.stats-card.stats-success[_ngcontent-%COMP%]::before {\n  background:\n    linear-gradient(\n      135deg,\n      #11998e 0%,\n      #38ef7d 100%);\n}\n.stats-card.stats-warning[_ngcontent-%COMP%]::before {\n  background:\n    linear-gradient(\n      135deg,\n      #f093fb 0%,\n      #f5576c 100%);\n}\n.stats-card.stats-danger[_ngcontent-%COMP%]::before {\n  background:\n    linear-gradient(\n      135deg,\n      #fc466b 0%,\n      #3f5efb 100%);\n}\n.stats-card.stats-info[_ngcontent-%COMP%]::before {\n  background:\n    linear-gradient(\n      135deg,\n      #667eea 0%,\n      #764ba2 100%);\n}\n.stats-card[_ngcontent-%COMP%]   .stats-icon[_ngcontent-%COMP%] {\n  width: 60px;\n  height: 60px;\n  border-radius: 50%;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  font-size: 1.8rem;\n  margin-bottom: 1.5rem;\n  background: rgba(102, 126, 234, 0.1);\n  color: #667eea;\n}\n.stats-card[_ngcontent-%COMP%]   .stats-content[_ngcontent-%COMP%]   .stats-number[_ngcontent-%COMP%] {\n  font-size: 2.5rem;\n  font-weight: 800;\n  color: #2d3748;\n  margin-bottom: 0.5rem;\n  line-height: 1;\n}\n.stats-card[_ngcontent-%COMP%]   .stats-content[_ngcontent-%COMP%]   .stats-label[_ngcontent-%COMP%] {\n  font-size: 0.95rem;\n  color: #718096;\n  font-weight: 600;\n  text-transform: uppercase;\n  letter-spacing: 0.5px;\n}\n.filters-section[_ngcontent-%COMP%] {\n  margin-bottom: 2rem;\n}\n.filters-card[_ngcontent-%COMP%] {\n  background: white;\n  border-radius: 16px;\n  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);\n  overflow: hidden;\n  border: 1px solid rgba(255, 255, 255, 0.8);\n}\n.filters-card[_ngcontent-%COMP%]   .filters-header[_ngcontent-%COMP%] {\n  background:\n    linear-gradient(\n      135deg,\n      #007bff 0%,\n      rgb(76.5, 162.6, 255) 50%,\n      rgb(0, 110.7, 229.5) 100%);\n  color: white;\n  padding: 1.5rem 2rem;\n}\n.filters-card[_ngcontent-%COMP%]   .filters-header[_ngcontent-%COMP%]   h5[_ngcontent-%COMP%] {\n  margin: 0;\n  font-weight: 600;\n  font-size: 1.1rem;\n}\n.filters-card[_ngcontent-%COMP%]   .filters-content[_ngcontent-%COMP%] {\n  padding: 2rem;\n}\n.filters-card[_ngcontent-%COMP%]   .filters-content[_ngcontent-%COMP%]   .form-label[_ngcontent-%COMP%] {\n  font-weight: 600;\n  color: #4a5568;\n  margin-bottom: 0.5rem;\n  font-size: 0.9rem;\n}\n.filters-card[_ngcontent-%COMP%]   .filters-content[_ngcontent-%COMP%]   .form-select[_ngcontent-%COMP%], \n.filters-card[_ngcontent-%COMP%]   .filters-content[_ngcontent-%COMP%]   .form-control[_ngcontent-%COMP%] {\n  border: 2px solid #e2e8f0;\n  border-radius: 12px;\n  padding: 0.75rem 1rem;\n  font-size: 0.95rem;\n  transition: all 0.2s ease-out;\n}\n.filters-card[_ngcontent-%COMP%]   .filters-content[_ngcontent-%COMP%]   .form-select[_ngcontent-%COMP%]:focus, \n.filters-card[_ngcontent-%COMP%]   .filters-content[_ngcontent-%COMP%]   .form-control[_ngcontent-%COMP%]:focus {\n  border-color: #007bff;\n  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);\n}\n.filters-card[_ngcontent-%COMP%]   .filters-content[_ngcontent-%COMP%]   .input-group-text[_ngcontent-%COMP%] {\n  background: #f7fafc;\n  border: 2px solid #e2e8f0;\n  border-right: none;\n  color: #718096;\n}\n.filters-card[_ngcontent-%COMP%]   .filters-content[_ngcontent-%COMP%]   .btn-outline-secondary[_ngcontent-%COMP%] {\n  border: 2px solid #e2e8f0;\n  color: #718096;\n  border-radius: 12px;\n  font-weight: 600;\n  transition: all 0.2s ease-out;\n}\n.filters-card[_ngcontent-%COMP%]   .filters-content[_ngcontent-%COMP%]   .btn-outline-secondary[_ngcontent-%COMP%]:hover {\n  background: #f7fafc;\n  border-color: #cbd5e0;\n  color: #4a5568;\n}\n.alert[_ngcontent-%COMP%] {\n  border-radius: 16px;\n  border: none;\n  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);\n}\n.alert.alert-danger[_ngcontent-%COMP%] {\n  background:\n    linear-gradient(\n      135deg,\n      rgba(252, 70, 107, 0.1) 0%,\n      rgba(63, 94, 251, 0.1) 100%);\n  color: #e53e3e;\n  border-left: 4px solid #e53e3e;\n}\n.loading-container[_ngcontent-%COMP%] {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  justify-content: center;\n  padding: 4rem 2rem;\n}\n.loading-container[_ngcontent-%COMP%]   .loading-spinner[_ngcontent-%COMP%] {\n  text-align: center;\n}\n.loading-container[_ngcontent-%COMP%]   .loading-spinner[_ngcontent-%COMP%]   .spinner-border[_ngcontent-%COMP%] {\n  width: 3rem;\n  height: 3rem;\n  border-width: 0.3em;\n}\n.loading-container[_ngcontent-%COMP%]   .loading-spinner[_ngcontent-%COMP%]   .loading-text[_ngcontent-%COMP%] {\n  margin-top: 1rem;\n  color: #718096;\n  font-weight: 600;\n}\n.empty-state[_ngcontent-%COMP%] {\n  text-align: center;\n  padding: 4rem 2rem;\n  color: #718096;\n}\n.empty-state[_ngcontent-%COMP%]   .empty-icon[_ngcontent-%COMP%] {\n  font-size: 4rem;\n  margin-bottom: 1.5rem;\n  opacity: 0.5;\n}\n.empty-state[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%] {\n  font-size: 1.5rem;\n  font-weight: 600;\n  margin-bottom: 1rem;\n  color: #4a5568;\n}\n.empty-state[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\n  font-size: 1.1rem;\n  margin: 0;\n}\n.avis-container[_ngcontent-%COMP%]   .avis-list[_ngcontent-%COMP%] {\n  display: flex;\n  flex-direction: column;\n  gap: 1.5rem;\n}\n.avis-card[_ngcontent-%COMP%] {\n  background: white;\n  border-radius: 16px;\n  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);\n  border: 1px solid rgba(255, 255, 255, 0.8);\n  overflow: hidden;\n  transition: all 0.4s cubic-bezier(0.25, 0.8, 0.25, 1);\n}\n.avis-card[_ngcontent-%COMP%]:hover {\n  transform: translateY(-4px);\n  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.12);\n}\n.avis-card[_ngcontent-%COMP%]   .avis-header[_ngcontent-%COMP%] {\n  background:\n    linear-gradient(\n      135deg,\n      #f8fafc 0%,\n      #e2e8f0 100%);\n  padding: 1.5rem 2rem;\n  border-bottom: 1px solid #e2e8f0;\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n}\n.avis-card[_ngcontent-%COMP%]   .avis-header[_ngcontent-%COMP%]   .avis-product[_ngcontent-%COMP%]   .product-name[_ngcontent-%COMP%] {\n  font-size: 1.1rem;\n  font-weight: 600;\n  color: #2d3748;\n  margin: 0 0 0.25rem 0;\n}\n.avis-card[_ngcontent-%COMP%]   .avis-header[_ngcontent-%COMP%]   .avis-product[_ngcontent-%COMP%]   .product-ref[_ngcontent-%COMP%] {\n  color: #718096;\n  font-size: 0.9rem;\n}\n.avis-card[_ngcontent-%COMP%]   .avis-header[_ngcontent-%COMP%]   .avis-date[_ngcontent-%COMP%] {\n  color: #718096;\n  font-size: 0.9rem;\n}\n.avis-card[_ngcontent-%COMP%]   .avis-content[_ngcontent-%COMP%] {\n  padding: 2rem;\n}\n.avis-card[_ngcontent-%COMP%]   .avis-content[_ngcontent-%COMP%]   .avis-client[_ngcontent-%COMP%] {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 1.5rem;\n}\n.avis-card[_ngcontent-%COMP%]   .avis-content[_ngcontent-%COMP%]   .avis-client[_ngcontent-%COMP%]   .client-info[_ngcontent-%COMP%] {\n  display: flex;\n  align-items: center;\n  color: #4a5568;\n  font-weight: 600;\n}\n.avis-card[_ngcontent-%COMP%]   .avis-content[_ngcontent-%COMP%]   .avis-client[_ngcontent-%COMP%]   .client-info[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\n  color: #718096;\n}\n.avis-card[_ngcontent-%COMP%]   .avis-content[_ngcontent-%COMP%]   .avis-client[_ngcontent-%COMP%]   .avis-rating[_ngcontent-%COMP%] {\n  display: flex;\n  align-items: center;\n  gap: 0.5rem;\n}\n.avis-card[_ngcontent-%COMP%]   .avis-content[_ngcontent-%COMP%]   .avis-client[_ngcontent-%COMP%]   .avis-rating[_ngcontent-%COMP%]   .stars[_ngcontent-%COMP%] {\n  display: flex;\n  gap: 0.2rem;\n}\n.avis-card[_ngcontent-%COMP%]   .avis-content[_ngcontent-%COMP%]   .avis-client[_ngcontent-%COMP%]   .avis-rating[_ngcontent-%COMP%]   .stars[_ngcontent-%COMP%]   .bi-star-fill[_ngcontent-%COMP%] {\n  color: #ffc107;\n}\n.avis-card[_ngcontent-%COMP%]   .avis-content[_ngcontent-%COMP%]   .avis-client[_ngcontent-%COMP%]   .avis-rating[_ngcontent-%COMP%]   .stars[_ngcontent-%COMP%]   .bi-star[_ngcontent-%COMP%] {\n  color: #e2e8f0;\n}\n.avis-card[_ngcontent-%COMP%]   .avis-content[_ngcontent-%COMP%]   .avis-client[_ngcontent-%COMP%]   .avis-rating[_ngcontent-%COMP%]   .rating-value[_ngcontent-%COMP%] {\n  font-weight: 600;\n  color: #4a5568;\n}\n.avis-card[_ngcontent-%COMP%]   .avis-content[_ngcontent-%COMP%]   .avis-comment[_ngcontent-%COMP%] {\n  margin-bottom: 1.5rem;\n}\n.avis-card[_ngcontent-%COMP%]   .avis-content[_ngcontent-%COMP%]   .avis-comment[_ngcontent-%COMP%]   .comment-content[_ngcontent-%COMP%] {\n  display: flex;\n  align-items: flex-start;\n  gap: 0.75rem;\n  padding: 1rem;\n  background: #f7fafc;\n  border-radius: 12px;\n  border-left: 4px solid #4299e1;\n}\n.avis-card[_ngcontent-%COMP%]   .avis-content[_ngcontent-%COMP%]   .avis-comment[_ngcontent-%COMP%]   .comment-content[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\n  color: #4299e1;\n  margin-top: 0.2rem;\n}\n.avis-card[_ngcontent-%COMP%]   .avis-content[_ngcontent-%COMP%]   .avis-comment[_ngcontent-%COMP%]   .comment-content[_ngcontent-%COMP%]   span[_ngcontent-%COMP%] {\n  color: #2d3748;\n  line-height: 1.6;\n}\n.avis-card[_ngcontent-%COMP%]   .avis-content[_ngcontent-%COMP%]   .avis-comment[_ngcontent-%COMP%]   .comment-deleted[_ngcontent-%COMP%] {\n  display: flex;\n  align-items: center;\n  gap: 0.75rem;\n  padding: 1rem;\n  background: #fed7d7;\n  border-radius: 12px;\n  border-left: 4px solid #e53e3e;\n}\n.avis-card[_ngcontent-%COMP%]   .avis-content[_ngcontent-%COMP%]   .avis-comment[_ngcontent-%COMP%]   .comment-deleted[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\n  color: #e53e3e;\n}\n.avis-card[_ngcontent-%COMP%]   .avis-content[_ngcontent-%COMP%]   .avis-comment[_ngcontent-%COMP%]   .comment-deleted[_ngcontent-%COMP%]   span[_ngcontent-%COMP%] {\n  color: #c53030;\n  font-style: italic;\n}\n.avis-card[_ngcontent-%COMP%]   .avis-content[_ngcontent-%COMP%]   .avis-comment[_ngcontent-%COMP%]   .comment-none[_ngcontent-%COMP%] {\n  display: flex;\n  align-items: center;\n  gap: 0.75rem;\n  padding: 1rem;\n  background: #f0fff4;\n  border-radius: 12px;\n  border-left: 4px solid #38a169;\n}\n.avis-card[_ngcontent-%COMP%]   .avis-content[_ngcontent-%COMP%]   .avis-comment[_ngcontent-%COMP%]   .comment-none[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\n  color: #38a169;\n}\n.avis-card[_ngcontent-%COMP%]   .avis-content[_ngcontent-%COMP%]   .avis-comment[_ngcontent-%COMP%]   .comment-none[_ngcontent-%COMP%]   span[_ngcontent-%COMP%] {\n  color: #2f855a;\n  font-style: italic;\n}\n.avis-card[_ngcontent-%COMP%]   .avis-content[_ngcontent-%COMP%]   .avis-footer[_ngcontent-%COMP%] {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n}\n.avis-card[_ngcontent-%COMP%]   .avis-content[_ngcontent-%COMP%]   .avis-footer[_ngcontent-%COMP%]   .avis-status[_ngcontent-%COMP%]   .badge[_ngcontent-%COMP%] {\n  padding: 0.5rem 1rem;\n  border-radius: 50px;\n  font-weight: 600;\n  font-size: 0.85rem;\n}\n.avis-card[_ngcontent-%COMP%]   .avis-content[_ngcontent-%COMP%]   .avis-footer[_ngcontent-%COMP%]   .avis-status[_ngcontent-%COMP%]   .badge.badge-success[_ngcontent-%COMP%] {\n  background:\n    linear-gradient(\n      135deg,\n      #11998e 0%,\n      #38ef7d 100%);\n  color: white;\n}\n.avis-card[_ngcontent-%COMP%]   .avis-content[_ngcontent-%COMP%]   .avis-footer[_ngcontent-%COMP%]   .avis-status[_ngcontent-%COMP%]   .badge.badge-warning[_ngcontent-%COMP%] {\n  background:\n    linear-gradient(\n      135deg,\n      #f093fb 0%,\n      #f5576c 100%);\n  color: white;\n}\n.avis-card[_ngcontent-%COMP%]   .avis-content[_ngcontent-%COMP%]   .avis-footer[_ngcontent-%COMP%]   .avis-status[_ngcontent-%COMP%]   .badge.badge-danger[_ngcontent-%COMP%] {\n  background:\n    linear-gradient(\n      135deg,\n      #fc466b 0%,\n      #3f5efb 100%);\n  color: white;\n}\n.avis-card[_ngcontent-%COMP%]   .avis-content[_ngcontent-%COMP%]   .avis-footer[_ngcontent-%COMP%]   .avis-status[_ngcontent-%COMP%]   .badge.badge-secondary[_ngcontent-%COMP%] {\n  background: #e2e8f0;\n  color: #4a5568;\n}\n.avis-card[_ngcontent-%COMP%]   .avis-content[_ngcontent-%COMP%]   .avis-footer[_ngcontent-%COMP%]   .avis-actions[_ngcontent-%COMP%] {\n  display: flex;\n  gap: 0.75rem;\n  flex-wrap: wrap;\n  align-items: center;\n  justify-content: flex-end;\n}\n@media (max-width: 768px) {\n  .avis-card[_ngcontent-%COMP%]   .avis-content[_ngcontent-%COMP%]   .avis-footer[_ngcontent-%COMP%]   .avis-actions[_ngcontent-%COMP%] {\n    justify-content: center;\n    gap: 0.5rem;\n  }\n}\n.avis-card[_ngcontent-%COMP%]   .avis-content[_ngcontent-%COMP%]   .avis-footer[_ngcontent-%COMP%]   .avis-actions[_ngcontent-%COMP%]   .btn[_ngcontent-%COMP%] {\n  border-radius: 25px;\n  padding: 0.5rem 1rem;\n  font-size: 0.85rem;\n  font-weight: 600;\n  transition: all 0.2s ease-out;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  gap: 0.5rem;\n  border-width: 2px;\n  white-space: nowrap;\n}\n.avis-card[_ngcontent-%COMP%]   .avis-content[_ngcontent-%COMP%]   .avis-footer[_ngcontent-%COMP%]   .avis-actions[_ngcontent-%COMP%]   .btn[_ngcontent-%COMP%]:hover {\n  transform: translateY(-2px);\n  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);\n}\n.avis-card[_ngcontent-%COMP%]   .avis-content[_ngcontent-%COMP%]   .avis-footer[_ngcontent-%COMP%]   .avis-actions[_ngcontent-%COMP%]   .btn[_ngcontent-%COMP%]:active {\n  transform: translateY(0);\n}\n.avis-card[_ngcontent-%COMP%]   .avis-content[_ngcontent-%COMP%]   .avis-footer[_ngcontent-%COMP%]   .avis-actions[_ngcontent-%COMP%]   .btn[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\n  font-size: 0.9rem;\n}\n.avis-card[_ngcontent-%COMP%]   .avis-content[_ngcontent-%COMP%]   .avis-footer[_ngcontent-%COMP%]   .avis-actions[_ngcontent-%COMP%]   .btn[_ngcontent-%COMP%]   span[_ngcontent-%COMP%] {\n  font-size: 0.85rem;\n}\n@media (max-width: 576px) {\n  .avis-card[_ngcontent-%COMP%]   .avis-content[_ngcontent-%COMP%]   .avis-footer[_ngcontent-%COMP%]   .avis-actions[_ngcontent-%COMP%]   .btn[_ngcontent-%COMP%]   span[_ngcontent-%COMP%] {\n    display: none;\n  }\n}\n.avis-card[_ngcontent-%COMP%]   .avis-content[_ngcontent-%COMP%]   .avis-footer[_ngcontent-%COMP%]   .avis-actions[_ngcontent-%COMP%]   .btn.btn-outline-primary[_ngcontent-%COMP%] {\n  border-color: #4299e1;\n  color: #4299e1;\n}\n.avis-card[_ngcontent-%COMP%]   .avis-content[_ngcontent-%COMP%]   .avis-footer[_ngcontent-%COMP%]   .avis-actions[_ngcontent-%COMP%]   .btn.btn-outline-primary[_ngcontent-%COMP%]:hover {\n  background: #4299e1;\n  color: white;\n}\n.avis-card[_ngcontent-%COMP%]   .avis-content[_ngcontent-%COMP%]   .avis-footer[_ngcontent-%COMP%]   .avis-actions[_ngcontent-%COMP%]   .btn.btn-outline-success[_ngcontent-%COMP%] {\n  border-color: #48bb78;\n  color: #48bb78;\n}\n.avis-card[_ngcontent-%COMP%]   .avis-content[_ngcontent-%COMP%]   .avis-footer[_ngcontent-%COMP%]   .avis-actions[_ngcontent-%COMP%]   .btn.btn-outline-success[_ngcontent-%COMP%]:hover {\n  background: #48bb78;\n  color: white;\n}\n.avis-card[_ngcontent-%COMP%]   .avis-content[_ngcontent-%COMP%]   .avis-footer[_ngcontent-%COMP%]   .avis-actions[_ngcontent-%COMP%]   .btn.btn-outline-warning[_ngcontent-%COMP%] {\n  border-color: #ed8936;\n  color: #ed8936;\n}\n.avis-card[_ngcontent-%COMP%]   .avis-content[_ngcontent-%COMP%]   .avis-footer[_ngcontent-%COMP%]   .avis-actions[_ngcontent-%COMP%]   .btn.btn-outline-warning[_ngcontent-%COMP%]:hover {\n  background: #ed8936;\n  color: white;\n}\n.modal.show[_ngcontent-%COMP%] {\n  display: block !important;\n}\n.modal[_ngcontent-%COMP%]   .modal-dialog[_ngcontent-%COMP%] {\n  margin: 2rem auto;\n  max-width: 600px;\n}\n.modal[_ngcontent-%COMP%]   .modal-content[_ngcontent-%COMP%] {\n  border: none;\n  border-radius: 16px;\n  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.1);\n  overflow: hidden;\n}\n.modal[_ngcontent-%COMP%]   .modal-content[_ngcontent-%COMP%]   .modal-header[_ngcontent-%COMP%] {\n  background:\n    linear-gradient(\n      135deg,\n      #ed8936 0%,\n      #f6ad55 100%);\n  border-bottom: none;\n  padding: 1.5rem 2rem;\n}\n.modal[_ngcontent-%COMP%]   .modal-content[_ngcontent-%COMP%]   .modal-header[_ngcontent-%COMP%]   .modal-title[_ngcontent-%COMP%] {\n  font-size: 1.25rem;\n  font-weight: 600;\n  margin: 0;\n  display: flex;\n  align-items: center;\n}\n.modal[_ngcontent-%COMP%]   .modal-content[_ngcontent-%COMP%]   .modal-header[_ngcontent-%COMP%]   .btn-close[_ngcontent-%COMP%] {\n  filter: brightness(0) invert(1);\n  opacity: 0.8;\n}\n.modal[_ngcontent-%COMP%]   .modal-content[_ngcontent-%COMP%]   .modal-header[_ngcontent-%COMP%]   .btn-close[_ngcontent-%COMP%]:hover {\n  opacity: 1;\n}\n.modal[_ngcontent-%COMP%]   .modal-content[_ngcontent-%COMP%]   .modal-body[_ngcontent-%COMP%] {\n  padding: 2rem;\n}\n.modal[_ngcontent-%COMP%]   .modal-content[_ngcontent-%COMP%]   .modal-body[_ngcontent-%COMP%]   .alert[_ngcontent-%COMP%] {\n  border-radius: 12px;\n  border: none;\n  padding: 1.5rem;\n}\n.modal[_ngcontent-%COMP%]   .modal-content[_ngcontent-%COMP%]   .modal-body[_ngcontent-%COMP%]   .alert.alert-info[_ngcontent-%COMP%] {\n  background:\n    linear-gradient(\n      135deg,\n      rgba(66, 153, 225, 0.1) 0%,\n      rgba(49, 130, 206, 0.1) 100%);\n  border-left: 4px solid #4299e1;\n  color: #2b6cb0;\n}\n.modal[_ngcontent-%COMP%]   .modal-content[_ngcontent-%COMP%]   .modal-body[_ngcontent-%COMP%]   .alert.alert-info[_ngcontent-%COMP%]   h6[_ngcontent-%COMP%] {\n  color: #2b6cb0;\n  font-weight: 600;\n  margin-bottom: 1rem;\n}\n.modal[_ngcontent-%COMP%]   .modal-content[_ngcontent-%COMP%]   .modal-body[_ngcontent-%COMP%]   .alert.alert-info[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\n  margin-bottom: 0.5rem;\n}\n.modal[_ngcontent-%COMP%]   .modal-content[_ngcontent-%COMP%]   .modal-body[_ngcontent-%COMP%]   .alert.alert-info[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]:last-child {\n  margin-bottom: 0;\n}\n.modal[_ngcontent-%COMP%]   .modal-content[_ngcontent-%COMP%]   .modal-body[_ngcontent-%COMP%]   .alert.alert-info[_ngcontent-%COMP%]   .bg-light[_ngcontent-%COMP%] {\n  background: rgba(255, 255, 255, 0.8) !important;\n  border-radius: 8px;\n  padding: 1rem;\n  margin-top: 0.5rem;\n  font-style: italic;\n  color: #4a5568;\n}\n.modal[_ngcontent-%COMP%]   .modal-content[_ngcontent-%COMP%]   .modal-body[_ngcontent-%COMP%]   .alert.alert-warning[_ngcontent-%COMP%] {\n  background:\n    linear-gradient(\n      135deg,\n      rgba(237, 137, 54, 0.1) 0%,\n      rgba(246, 173, 85, 0.1) 100%);\n  border-left: 4px solid #ed8936;\n  color: #c05621;\n}\n.modal[_ngcontent-%COMP%]   .modal-content[_ngcontent-%COMP%]   .modal-body[_ngcontent-%COMP%]   .form-label[_ngcontent-%COMP%] {\n  font-weight: 600;\n  color: #4a5568;\n  margin-bottom: 0.75rem;\n  display: flex;\n  align-items: center;\n}\n.modal[_ngcontent-%COMP%]   .modal-content[_ngcontent-%COMP%]   .modal-body[_ngcontent-%COMP%]   .form-label[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\n  color: #ed8936;\n}\n.modal[_ngcontent-%COMP%]   .modal-content[_ngcontent-%COMP%]   .modal-body[_ngcontent-%COMP%]   .form-select[_ngcontent-%COMP%], \n.modal[_ngcontent-%COMP%]   .modal-content[_ngcontent-%COMP%]   .modal-body[_ngcontent-%COMP%]   .form-control[_ngcontent-%COMP%] {\n  border: 2px solid #e2e8f0;\n  border-radius: 12px;\n  padding: 0.75rem 1rem;\n  font-size: 0.95rem;\n  transition: all 0.2s ease-out;\n}\n.modal[_ngcontent-%COMP%]   .modal-content[_ngcontent-%COMP%]   .modal-body[_ngcontent-%COMP%]   .form-select[_ngcontent-%COMP%]:focus, \n.modal[_ngcontent-%COMP%]   .modal-content[_ngcontent-%COMP%]   .modal-body[_ngcontent-%COMP%]   .form-control[_ngcontent-%COMP%]:focus {\n  border-color: #ed8936;\n  box-shadow: 0 0 0 3px rgba(237, 137, 54, 0.1);\n}\n.modal[_ngcontent-%COMP%]   .modal-content[_ngcontent-%COMP%]   .modal-body[_ngcontent-%COMP%]   .form-text[_ngcontent-%COMP%] {\n  color: #718096;\n  font-size: 0.85rem;\n  margin-top: 0.5rem;\n}\n.modal[_ngcontent-%COMP%]   .modal-content[_ngcontent-%COMP%]   .modal-footer[_ngcontent-%COMP%] {\n  padding: 1.5rem 2rem;\n  background: #f7fafc;\n  border-top: 1px solid #e2e8f0;\n}\n.modal[_ngcontent-%COMP%]   .modal-content[_ngcontent-%COMP%]   .modal-footer[_ngcontent-%COMP%]   .btn[_ngcontent-%COMP%] {\n  border-radius: 25px;\n  padding: 0.75rem 1.5rem;\n  font-weight: 600;\n  transition: all 0.2s ease-out;\n}\n.modal[_ngcontent-%COMP%]   .modal-content[_ngcontent-%COMP%]   .modal-footer[_ngcontent-%COMP%]   .btn.btn-secondary[_ngcontent-%COMP%] {\n  background: #e2e8f0;\n  border-color: #e2e8f0;\n  color: #4a5568;\n}\n.modal[_ngcontent-%COMP%]   .modal-content[_ngcontent-%COMP%]   .modal-footer[_ngcontent-%COMP%]   .btn.btn-secondary[_ngcontent-%COMP%]:hover {\n  background: #cbd5e0;\n  border-color: #cbd5e0;\n  transform: translateY(-2px);\n}\n.modal[_ngcontent-%COMP%]   .modal-content[_ngcontent-%COMP%]   .modal-footer[_ngcontent-%COMP%]   .btn.btn-warning[_ngcontent-%COMP%] {\n  background:\n    linear-gradient(\n      135deg,\n      #ed8936 0%,\n      #f6ad55 100%);\n  border: none;\n  color: white;\n}\n.modal[_ngcontent-%COMP%]   .modal-content[_ngcontent-%COMP%]   .modal-footer[_ngcontent-%COMP%]   .btn.btn-warning[_ngcontent-%COMP%]:hover:not(:disabled) {\n  background:\n    linear-gradient(\n      135deg,\n      #dd7724 0%,\n      #ed8936 100%);\n  transform: translateY(-2px);\n  box-shadow: 0 4px 12px rgba(237, 137, 54, 0.3);\n}\n.modal[_ngcontent-%COMP%]   .modal-content[_ngcontent-%COMP%]   .modal-footer[_ngcontent-%COMP%]   .btn.btn-warning[_ngcontent-%COMP%]:disabled {\n  opacity: 0.6;\n  cursor: not-allowed;\n}\n.modal-backdrop[_ngcontent-%COMP%] {\n  background-color: rgba(0, 0, 0, 0.5);\n  -webkit-backdrop-filter: blur(4px);\n  backdrop-filter: blur(4px);\n}\n.modal-backdrop.show[_ngcontent-%COMP%] {\n  opacity: 1;\n}\n/*# sourceMappingURL=avis-fournisseur.component.css.map */'] });
};
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && setClassMetadata(AvisFournisseurComponent, [{
    type: Component,
    args: [{ selector: "app-avis-fournisseur", standalone: true, imports: [CommonModule, FormsModule], template: `<div class="container-fluid">
  <!-- En-t\xEAte moderne -->
  <div class="page-header mb-4">
    <div class="header-content">
      <div class="header-title">
        <div class="title-icon">
          <i class="bi bi-star-fill"></i>
        </div>
        <div class="title-text">
          <h1 class="page-title">Mes Avis Clients</h1>
          <p class="page-subtitle">Consultez et r\xE9pondez aux avis de vos clients</p>
        </div>
      </div>
      <div class="header-actions">
        <button class="btn btn-outline-primary" (click)="loadAvis()" title="Actualiser">
          <i class="bi bi-arrow-clockwise me-2"></i>
          Actualiser
        </button>
      </div>
    </div>
  </div>

  <!-- Statistiques en ligne -->
  <div class="stats-container mb-4" *ngIf="stats">
    <div class="stats-card stats-primary">
      <div class="stats-icon">
        <i class="bi bi-chat-square-text"></i>
      </div>
      <div class="stats-content">
        <div class="stats-number">{{ stats.totalAvis }}</div>
        <div class="stats-label">Total Avis</div>
      </div>
    </div>
    <div class="stats-card stats-warning">
      <div class="stats-icon">
        <i class="bi bi-exclamation-triangle"></i>
      </div>
      <div class="stats-content">
        <div class="stats-number">{{ stats.avisSignales || 0 }}</div>
        <div class="stats-label">Signal\xE9s</div>
      </div>
    </div>

    <div class="stats-card stats-info">
      <div class="stats-icon">
        <i class="bi bi-star-fill"></i>
      </div>
      <div class="stats-content">
        <div class="stats-number">{{ stats.noteMoyenneGlobale | number:'1.1-1' }}</div>
        <div class="stats-label">Note Moyenne</div>
      </div>
    </div>
  </div>

  <!-- Filtres et recherche -->
  <div class="filters-section mb-4">
    <div class="filters-card">
      <div class="filters-header">
        <h5><i class="bi bi-funnel me-2"></i>Filtres</h5>
      </div>
      <div class="filters-content">
        <div class="row g-3">
          <div class="col-md-3">
            <label class="form-label">Statut</label>
            <select class="form-select" [(ngModel)]="filter.statut" (change)="onFilterChange()">
              <option [value]="undefined">Tous les statuts</option>
              <option [value]="StatutAvis.Signale">Signal\xE9s</option>
              <option [value]="StatutAvis.CommentaireSupprime">Commentaire supprim\xE9</option>
            </select>
          </div>
          
          <div class="col-md-3">
            <label class="form-label">Recherche</label>
            <div class="input-group">
              <span class="input-group-text"><i class="bi bi-search"></i></span>
              <input type="text" class="form-control" placeholder="Rechercher..." 
                     [(ngModel)]="filter.recherche" (keyup.enter)="onFilterChange()">
            </div>
          </div>
          
          <div class="col-md-2">
            <label class="form-label">Trier par</label>
            <select class="form-select" [(ngModel)]="filter.sortBy" (change)="onFilterChange()">
              <option value="datePublication">Date</option>
              <option value="note">Note</option>
              <option value="produitNom">Produit</option>
            </select>
          </div>
          
          <div class="col-md-2">
            <label class="form-label">Ordre</label>
            <select class="form-select" [(ngModel)]="filter.sortDesc" (change)="onFilterChange()">
              <option [value]="true">D\xE9croissant</option>
              <option [value]="false">Croissant</option>
            </select>
          </div>
          
          <div class="col-md-2 d-flex align-items-end">
            <button class="btn btn-outline-secondary w-100" (click)="resetFilters()">
              <i class="bi bi-x-circle me-2"></i>Reset
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Message d'erreur -->
  <div *ngIf="error" class="alert alert-danger alert-dismissible fade show">
    <i class="bi bi-exclamation-triangle me-2"></i>
    {{ error }}
    <button type="button" class="btn-close" (click)="error = null"></button>
  </div>

  <!-- Loading -->
  <div *ngIf="loading" class="loading-container">
    <div class="loading-spinner">
      <div class="spinner-border text-primary" role="status">
        <span class="visually-hidden">Chargement...</span>
      </div>
      <p class="loading-text">Chargement des avis...</p>
    </div>
  </div>

  <!-- Liste des avis -->
  <div *ngIf="!loading" class="avis-container">
    <div *ngIf="avis.length === 0" class="empty-state">
      <div class="empty-icon">
        <i class="bi bi-chat-square-text"></i>
      </div>
      <h3>Aucun avis trouv\xE9</h3>
      <p>Il n'y a pas encore d'avis correspondant \xE0 vos crit\xE8res.</p>
    </div>

    <div *ngIf="avis.length > 0" class="avis-list">
      <div *ngFor="let avisItem of avis" class="avis-card">
        <div class="avis-header">
          <div class="avis-product">
            <h6 class="product-name">{{ avisItem.produitNom }}</h6>
            <small class="product-ref">R\xE9f: {{ avisItem.produitReference }}</small>
          </div>
          <div class="avis-date">
            <small class="text-muted">{{ avisItem.datePublication | date:'dd/MM/yyyy HH:mm' }}</small>
          </div>
        </div>

        <div class="avis-content">
          <div class="avis-client">
            <div class="client-info">
              <i class="bi bi-person-circle me-2"></i>
              <span>{{ avisItem.clientPrenom }} {{ avisItem.clientNom }}</span>
            </div>
            <div class="avis-rating">
              <div class="stars">
                <span *ngFor="let star of getStars(avisItem.note)" 
                      class="bi" [class]="'bi-' + star"></span>
              </div>
              <span class="rating-value">{{ avisItem.note }}/5</span>
            </div>
          </div>

          <!-- Commentaire -->
          <div class="avis-comment">
            <!-- Commentaire pr\xE9sent et non supprim\xE9 -->
            <div *ngIf="avisItem.commentaire && avisItem.commentaire.trim() && !avisItem.commentaireSupprime"
                 class="comment-content">
              <i class="bi bi-chat-quote me-2"></i>
              <span>{{ avisItem.commentaire }}</span>
            </div>
            <!-- Commentaire supprim\xE9 par l'admin -->
            <div *ngIf="avisItem.commentaireSupprime"
                 class="comment-deleted">
              <i class="bi bi-chat-square-x me-2"></i>
              <span>Commentaire supprim\xE9</span>
            </div>
            <!-- Avis sans commentaire (note seule) -->
            <div *ngIf="(!avisItem.commentaire || !avisItem.commentaire.trim()) && !avisItem.commentaireSupprime"
                 class="comment-none">
              <i class="bi bi-star me-2"></i>
              <span>Note sans commentaire</span>
            </div>
          </div>

          <!-- Statut et actions -->
          <div class="avis-footer">
            <div class="avis-status">
              <span class="badge" [class]="'badge-' + getStatutClass(avisItem.statut)">
                <i class="bi" [class]="getStatutIcon(avisItem.statut)"></i>
                {{ avisItem.statutLibelle }}
              </span>
            </div>
            <div class="avis-actions">
              <button class="btn btn-sm btn-outline-warning" (click)="signalerAvis(avisItem)" title="Signaler">
                <i class="bi bi-flag me-1"></i>
                <span>Signaler</span>
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

<!-- Modale de signalement -->
<div class="modal fade" id="signalementModal" tabindex="-1" aria-labelledby="signalementModalLabel" aria-hidden="true"
     [class.show]="showSignalementModal" [style.display]="showSignalementModal ? 'block' : 'none'">
  <div class="modal-dialog modal-lg">
    <div class="modal-content">
      <div class="modal-header bg-warning text-white">
        <h5 class="modal-title" id="signalementModalLabel">
          <i class="bi bi-flag-fill me-2"></i>
          Signaler un probl\xE8me avec cet avis
        </h5>
        <button type="button" class="btn-close btn-close-white" (click)="closeSignalementModal()" aria-label="Close"></button>
      </div>

      <div class="modal-body" *ngIf="selectedAvisForSignalement">
        <!-- Informations sur l'avis \xE0 signaler -->
        <div class="alert alert-info">
          <h6><i class="bi bi-info-circle me-2"></i>Avis \xE0 signaler :</h6>
          <p><strong>Produit :</strong> {{ selectedAvisForSignalement.produitNom }}</p>
          <p><strong>Client :</strong> {{ selectedAvisForSignalement.clientNom }}</p>
          <p><strong>Note :</strong>
            <span class="ms-2">
              <i class="bi bi-star-fill text-warning" *ngFor="let star of [].constructor(selectedAvisForSignalement.note)"></i>
              <i class="bi bi-star text-muted" *ngFor="let star of [].constructor(5 - selectedAvisForSignalement.note)"></i>
            </span>
          </p>
          <p><strong>Commentaire :</strong></p>
          <div class="bg-light p-3 rounded">
            {{ selectedAvisForSignalement.commentaire || 'Aucun commentaire' }}
          </div>
        </div>

        <!-- Formulaire de signalement -->
        <form (ngSubmit)="signalerAvis()" #signalementForm="ngForm">
          <div class="mb-3">
            <label for="raisonSignalement" class="form-label">
              <i class="bi bi-exclamation-triangle me-2"></i>
              <strong>Raison du signalement *</strong>
            </label>
            <select class="form-select" id="raisonSignalement"
                    [(ngModel)]="signalementData.raisonSignalement"
                    name="raisonSignalement" required>
              <option value="">-- S\xE9lectionnez une raison --</option>
              <option value="contenu_inapproprie">Contenu inappropri\xE9 ou offensant</option>
              <option value="faux_avis">Faux avis ou spam</option>
              <option value="information_incorrecte">Informations incorrectes sur le produit</option>
              <option value="violation_regles">Violation des r\xE8gles de la plateforme</option>
              <option value="diffamation">Diffamation ou attaque personnelle</option>
              <option value="autre">Autre raison</option>
            </select>
          </div>

          <div class="mb-3">
            <label for="detailsSignalement" class="form-label">
              <i class="bi bi-chat-text me-2"></i>
              <strong>D\xE9tails suppl\xE9mentaires</strong>
            </label>
            <textarea class="form-control" id="detailsSignalement" rows="4"
                      [(ngModel)]="signalementData.detailsSignalement"
                      name="detailsSignalement"
                      placeholder="Expliquez en d\xE9tail pourquoi vous signalez cet avis..."></textarea>
            <div class="form-text">
              Fournissez des d\xE9tails sp\xE9cifiques pour aider l'\xE9quipe de mod\xE9ration \xE0 traiter votre signalement.
            </div>
          </div>

          <div class="alert alert-warning">
            <i class="bi bi-info-circle me-2"></i>
            <strong>Important :</strong> Votre signalement sera envoy\xE9 \xE0 l'\xE9quipe de mod\xE9ration qui l'examinera dans les plus brefs d\xE9lais.
            Les signalements abusifs peuvent entra\xEEner des sanctions.
          </div>
        </form>
      </div>

      <div class="modal-footer">
        <button type="button" class="btn btn-secondary" (click)="closeSignalementModal()">
          <i class="bi bi-x-circle me-2"></i>Annuler
        </button>
        <button type="button" class="btn btn-warning"
                (click)="signalerAvis()"
                [disabled]="!signalementData.raisonSignalement || loading">
          <i class="bi bi-flag-fill me-2"></i>
          <span *ngIf="!loading">Signaler cet avis</span>
          <span *ngIf="loading">
            <span class="spinner-border spinner-border-sm me-2" role="status"></span>
            Signalement en cours...
          </span>
        </button>
      </div>
    </div>
  </div>
</div>

<!-- Backdrop pour la modale -->
<div class="modal-backdrop fade" [class.show]="showSignalementModal" *ngIf="showSignalementModal"
     (click)="closeSignalementModal()"></div>
`, styles: ['/* src/app/components/fournisseur/avis-fournisseur/avis-fournisseur.component.scss */\n:root {\n  --primary-color: #007bff;\n  --secondary-color: #6c757d;\n  --success-color: #28a745;\n  --error-color: #dc3545;\n  --warning-color: #ffc107;\n  --info-color: #17a2b8;\n  --accent-color: #28a745;\n  --text-color: #333333;\n  --background-color: #ffffff;\n  --border-color: #e0e0e0;\n  --gradient-primary:\n    linear-gradient(\n      135deg,\n      #007bff,\n      rgb(51, 149.4, 255));\n  --primary-color-hover: rgb(0, 98.4, 204);\n  --accent-color-hover: rgb(30.1449275362, 125.8550724638, 52);\n}\n.btn-primary {\n  background: var(--gradient-primary);\n  border: none;\n  transition: all 0.3s ease;\n}\n.btn-primary:hover {\n  background: var(--primary-color-hover);\n  transform: translateY(-1px);\n}\n.btn-success {\n  background-color: var(--success-color);\n  border-color: var(--success-color);\n}\n.btn-success:hover {\n  background-color: var(--accent-color-hover);\n  border-color: var(--accent-color-hover);\n}\n.alert-success {\n  background-color: rgba(40, 167, 69, 0.1);\n  border-color: var(--success-color);\n  color: var(--success-color);\n}\n.alert-danger {\n  background-color: rgba(220, 53, 69, 0.1);\n  border-color: var(--error-color);\n  color: var(--error-color);\n}\n.alert-warning {\n  background-color: rgba(255, 193, 7, 0.1);\n  border-color: var(--warning-color);\n  color: #856404;\n}\n.alert-info {\n  background-color: rgba(23, 162, 184, 0.1);\n  border-color: var(--info-color);\n  color: var(--info-color);\n}\n.form-control {\n  background-color: var(--card-background-color);\n  border-color: var(--border-color);\n  color: var(--text-color);\n  transition: all 0.3s ease;\n}\n.form-control:focus {\n  background-color: var(--card-background-color);\n  border-color: var(--primary-color);\n  color: var(--text-color);\n  box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);\n}\n.table {\n  background-color: var(--card-background-color);\n  color: var(--text-color);\n}\n.table th {\n  background-color: var(--primary-color);\n  color: white;\n  border: none;\n}\n.table td {\n  border-color: var(--border-color);\n}\n.table-striped tbody tr:nth-of-type(odd) {\n  background-color: rgba(0, 0, 0, 0.05);\n}\n[data-theme=dark] .table-striped tbody tr:nth-of-type(odd) {\n  background-color: rgba(255, 255, 255, 0.05);\n}\n.navbar {\n  background-color: var(--card-background-color) !important;\n  border-bottom: 1px solid var(--border-color);\n  box-shadow: var(--card-shadow);\n}\n.navbar-brand,\n.navbar-nav .nav-link {\n  color: var(--text-color) !important;\n  transition: color 0.3s ease;\n}\n.navbar-nav .nav-link:hover {\n  color: var(--primary-color) !important;\n}\n.sidebar {\n  background-color: var(--sidebar-bg);\n  color: var(--sidebar-text);\n  transition: all 0.3s ease;\n}\n.sidebar .nav-link {\n  color: var(--sidebar-text);\n  transition: all 0.3s ease;\n  border-radius: 0.375rem;\n  margin: 0.25rem 0;\n}\n.sidebar .nav-link:hover {\n  background-color: var(--sidebar-hover);\n  color: var(--sidebar-text);\n}\n.sidebar .nav-link.active {\n  background-color: var(--primary-color);\n  color: white;\n}\n.badge {\n  font-size: 0.75em;\n  padding: 0.375rem 0.75rem;\n}\n.badge-success {\n  background-color: var(--success-color);\n}\n.badge-danger {\n  background-color: var(--error-color);\n}\n.badge-warning {\n  background-color: var(--warning-color);\n  color: #212529;\n}\n.badge-info {\n  background-color: var(--info-color);\n}\n@keyframes fadeIn {\n  from {\n    opacity: 0;\n    transform: translateY(20px);\n  }\n  to {\n    opacity: 1;\n    transform: translateY(0);\n  }\n}\n@keyframes slideIn {\n  from {\n    transform: translateX(-100%);\n  }\n  to {\n    transform: translateX(0);\n  }\n}\n.fade-in {\n  animation: fadeIn 0.5s ease-out;\n}\n.slide-in {\n  animation: slideIn 0.3s ease-out;\n}\n@media (max-width: 768px) {\n  .card {\n    margin-bottom: 1rem;\n  }\n  .table-responsive {\n    font-size: 0.875rem;\n  }\n}\n.modal-content {\n  background-color: var(--card-background-color);\n  border: 1px solid var(--border-color);\n}\n.modal-header {\n  border-bottom: 1px solid var(--border-color);\n}\n.modal-footer {\n  border-top: 1px solid var(--border-color);\n}\n.dropdown-menu {\n  background-color: var(--card-background-color);\n  border: 1px solid var(--border-color);\n  box-shadow: var(--card-shadow);\n}\n.dropdown-item {\n  color: var(--text-color);\n  transition: all 0.3s ease;\n}\n.dropdown-item:hover {\n  background-color: var(--card-background-color-hover);\n  color: var(--text-color-hover);\n}\n.tooltip .tooltip-inner {\n  background-color: var(--sidebar-bg);\n  color: var(--sidebar-text);\n}\n.progress {\n  background-color: var(--border-color);\n}\n.progress-bar {\n  background-color: var(--primary-color);\n}\n.container-fluid {\n  background:\n    linear-gradient(\n      135deg,\n      #f5f7fa 0%,\n      #c3cfe2 100%);\n  min-height: 100vh;\n  padding: 2rem;\n  position: relative;\n}\n.container-fluid::before {\n  content: "";\n  position: absolute;\n  top: 0;\n  left: 0;\n  right: 0;\n  height: 300px;\n  background:\n    linear-gradient(\n      135deg,\n      rgba(102, 126, 234, 0.1) 0%,\n      rgba(118, 75, 162, 0.1) 100%);\n  z-index: 0;\n}\n.container-fluid > * {\n  position: relative;\n  z-index: 1;\n}\n@media (max-width: 768px) {\n  .container-fluid {\n    padding: 1rem;\n  }\n}\n.page-header {\n  background:\n    linear-gradient(\n      135deg,\n      #667eea 0%,\n      #764ba2 100%);\n  border-radius: 24px;\n  padding: 3rem 2.5rem;\n  margin-bottom: 3rem;\n  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.1);\n  color: white;\n  position: relative;\n  overflow: hidden;\n}\n.page-header .header-content {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  position: relative;\n  z-index: 2;\n}\n@media (max-width: 768px) {\n  .page-header .header-content {\n    flex-direction: column;\n    gap: 2rem;\n    text-align: center;\n  }\n}\n.page-header .header-title {\n  display: flex;\n  align-items: center;\n  gap: 1.5rem;\n}\n.page-header .header-title .title-icon {\n  width: 80px;\n  height: 80px;\n  background: rgba(255, 255, 255, 0.2);\n  border-radius: 50%;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  font-size: 2.5rem;\n  -webkit-backdrop-filter: blur(10px);\n  backdrop-filter: blur(10px);\n  border: 2px solid rgba(255, 255, 255, 0.3);\n}\n.page-header .header-title .title-text .page-title {\n  font-size: 2.5rem;\n  font-weight: 700;\n  margin: 0;\n  text-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);\n}\n@media (max-width: 768px) {\n  .page-header .header-title .title-text .page-title {\n    font-size: 2rem;\n  }\n}\n.page-header .header-title .title-text .page-subtitle {\n  font-size: 1.2rem;\n  margin: 0.5rem 0 0 0;\n  opacity: 0.9;\n  font-weight: 300;\n}\n.page-header .header-actions .btn {\n  background: rgba(255, 255, 255, 0.2);\n  border: 2px solid rgba(255, 255, 255, 0.3);\n  color: white;\n  padding: 0.75rem 1.5rem;\n  border-radius: 50px;\n  font-weight: 600;\n  -webkit-backdrop-filter: blur(10px);\n  backdrop-filter: blur(10px);\n  transition: all 0.2s ease-out;\n}\n.page-header .header-actions .btn:hover {\n  background: rgba(255, 255, 255, 0.3);\n  border-color: rgba(255, 255, 255, 0.5);\n  transform: translateY(-2px);\n  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2);\n}\n.stats-container {\n  display: grid;\n  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));\n  gap: 2rem;\n  margin-bottom: 3rem;\n}\n@media (max-width: 768px) {\n  .stats-container {\n    grid-template-columns: 1fr;\n    gap: 1rem;\n  }\n}\n.stats-card {\n  background: white;\n  border-radius: 16px;\n  padding: 2rem;\n  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);\n  border: 1px solid rgba(255, 255, 255, 0.8);\n  -webkit-backdrop-filter: blur(10px);\n  backdrop-filter: blur(10px);\n  transition: all 0.4s cubic-bezier(0.25, 0.8, 0.25, 1);\n  position: relative;\n  overflow: hidden;\n}\n.stats-card::before {\n  content: "";\n  position: absolute;\n  top: 0;\n  left: 0;\n  right: 0;\n  height: 4px;\n  background:\n    linear-gradient(\n      135deg,\n      #007bff 0%,\n      rgb(76.5, 162.6, 255) 50%,\n      rgb(0, 110.7, 229.5) 100%);\n}\n.stats-card:hover {\n  transform: translateY(-8px);\n  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.12);\n}\n.stats-card.stats-primary::before {\n  background:\n    linear-gradient(\n      135deg,\n      #007bff 0%,\n      rgb(76.5, 162.6, 255) 50%,\n      rgb(0, 110.7, 229.5) 100%);\n}\n.stats-card.stats-success::before {\n  background:\n    linear-gradient(\n      135deg,\n      #11998e 0%,\n      #38ef7d 100%);\n}\n.stats-card.stats-warning::before {\n  background:\n    linear-gradient(\n      135deg,\n      #f093fb 0%,\n      #f5576c 100%);\n}\n.stats-card.stats-danger::before {\n  background:\n    linear-gradient(\n      135deg,\n      #fc466b 0%,\n      #3f5efb 100%);\n}\n.stats-card.stats-info::before {\n  background:\n    linear-gradient(\n      135deg,\n      #667eea 0%,\n      #764ba2 100%);\n}\n.stats-card .stats-icon {\n  width: 60px;\n  height: 60px;\n  border-radius: 50%;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  font-size: 1.8rem;\n  margin-bottom: 1.5rem;\n  background: rgba(102, 126, 234, 0.1);\n  color: #667eea;\n}\n.stats-card .stats-content .stats-number {\n  font-size: 2.5rem;\n  font-weight: 800;\n  color: #2d3748;\n  margin-bottom: 0.5rem;\n  line-height: 1;\n}\n.stats-card .stats-content .stats-label {\n  font-size: 0.95rem;\n  color: #718096;\n  font-weight: 600;\n  text-transform: uppercase;\n  letter-spacing: 0.5px;\n}\n.filters-section {\n  margin-bottom: 2rem;\n}\n.filters-card {\n  background: white;\n  border-radius: 16px;\n  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);\n  overflow: hidden;\n  border: 1px solid rgba(255, 255, 255, 0.8);\n}\n.filters-card .filters-header {\n  background:\n    linear-gradient(\n      135deg,\n      #007bff 0%,\n      rgb(76.5, 162.6, 255) 50%,\n      rgb(0, 110.7, 229.5) 100%);\n  color: white;\n  padding: 1.5rem 2rem;\n}\n.filters-card .filters-header h5 {\n  margin: 0;\n  font-weight: 600;\n  font-size: 1.1rem;\n}\n.filters-card .filters-content {\n  padding: 2rem;\n}\n.filters-card .filters-content .form-label {\n  font-weight: 600;\n  color: #4a5568;\n  margin-bottom: 0.5rem;\n  font-size: 0.9rem;\n}\n.filters-card .filters-content .form-select,\n.filters-card .filters-content .form-control {\n  border: 2px solid #e2e8f0;\n  border-radius: 12px;\n  padding: 0.75rem 1rem;\n  font-size: 0.95rem;\n  transition: all 0.2s ease-out;\n}\n.filters-card .filters-content .form-select:focus,\n.filters-card .filters-content .form-control:focus {\n  border-color: #007bff;\n  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);\n}\n.filters-card .filters-content .input-group-text {\n  background: #f7fafc;\n  border: 2px solid #e2e8f0;\n  border-right: none;\n  color: #718096;\n}\n.filters-card .filters-content .btn-outline-secondary {\n  border: 2px solid #e2e8f0;\n  color: #718096;\n  border-radius: 12px;\n  font-weight: 600;\n  transition: all 0.2s ease-out;\n}\n.filters-card .filters-content .btn-outline-secondary:hover {\n  background: #f7fafc;\n  border-color: #cbd5e0;\n  color: #4a5568;\n}\n.alert {\n  border-radius: 16px;\n  border: none;\n  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);\n}\n.alert.alert-danger {\n  background:\n    linear-gradient(\n      135deg,\n      rgba(252, 70, 107, 0.1) 0%,\n      rgba(63, 94, 251, 0.1) 100%);\n  color: #e53e3e;\n  border-left: 4px solid #e53e3e;\n}\n.loading-container {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  justify-content: center;\n  padding: 4rem 2rem;\n}\n.loading-container .loading-spinner {\n  text-align: center;\n}\n.loading-container .loading-spinner .spinner-border {\n  width: 3rem;\n  height: 3rem;\n  border-width: 0.3em;\n}\n.loading-container .loading-spinner .loading-text {\n  margin-top: 1rem;\n  color: #718096;\n  font-weight: 600;\n}\n.empty-state {\n  text-align: center;\n  padding: 4rem 2rem;\n  color: #718096;\n}\n.empty-state .empty-icon {\n  font-size: 4rem;\n  margin-bottom: 1.5rem;\n  opacity: 0.5;\n}\n.empty-state h3 {\n  font-size: 1.5rem;\n  font-weight: 600;\n  margin-bottom: 1rem;\n  color: #4a5568;\n}\n.empty-state p {\n  font-size: 1.1rem;\n  margin: 0;\n}\n.avis-container .avis-list {\n  display: flex;\n  flex-direction: column;\n  gap: 1.5rem;\n}\n.avis-card {\n  background: white;\n  border-radius: 16px;\n  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);\n  border: 1px solid rgba(255, 255, 255, 0.8);\n  overflow: hidden;\n  transition: all 0.4s cubic-bezier(0.25, 0.8, 0.25, 1);\n}\n.avis-card:hover {\n  transform: translateY(-4px);\n  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.12);\n}\n.avis-card .avis-header {\n  background:\n    linear-gradient(\n      135deg,\n      #f8fafc 0%,\n      #e2e8f0 100%);\n  padding: 1.5rem 2rem;\n  border-bottom: 1px solid #e2e8f0;\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n}\n.avis-card .avis-header .avis-product .product-name {\n  font-size: 1.1rem;\n  font-weight: 600;\n  color: #2d3748;\n  margin: 0 0 0.25rem 0;\n}\n.avis-card .avis-header .avis-product .product-ref {\n  color: #718096;\n  font-size: 0.9rem;\n}\n.avis-card .avis-header .avis-date {\n  color: #718096;\n  font-size: 0.9rem;\n}\n.avis-card .avis-content {\n  padding: 2rem;\n}\n.avis-card .avis-content .avis-client {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 1.5rem;\n}\n.avis-card .avis-content .avis-client .client-info {\n  display: flex;\n  align-items: center;\n  color: #4a5568;\n  font-weight: 600;\n}\n.avis-card .avis-content .avis-client .client-info i {\n  color: #718096;\n}\n.avis-card .avis-content .avis-client .avis-rating {\n  display: flex;\n  align-items: center;\n  gap: 0.5rem;\n}\n.avis-card .avis-content .avis-client .avis-rating .stars {\n  display: flex;\n  gap: 0.2rem;\n}\n.avis-card .avis-content .avis-client .avis-rating .stars .bi-star-fill {\n  color: #ffc107;\n}\n.avis-card .avis-content .avis-client .avis-rating .stars .bi-star {\n  color: #e2e8f0;\n}\n.avis-card .avis-content .avis-client .avis-rating .rating-value {\n  font-weight: 600;\n  color: #4a5568;\n}\n.avis-card .avis-content .avis-comment {\n  margin-bottom: 1.5rem;\n}\n.avis-card .avis-content .avis-comment .comment-content {\n  display: flex;\n  align-items: flex-start;\n  gap: 0.75rem;\n  padding: 1rem;\n  background: #f7fafc;\n  border-radius: 12px;\n  border-left: 4px solid #4299e1;\n}\n.avis-card .avis-content .avis-comment .comment-content i {\n  color: #4299e1;\n  margin-top: 0.2rem;\n}\n.avis-card .avis-content .avis-comment .comment-content span {\n  color: #2d3748;\n  line-height: 1.6;\n}\n.avis-card .avis-content .avis-comment .comment-deleted {\n  display: flex;\n  align-items: center;\n  gap: 0.75rem;\n  padding: 1rem;\n  background: #fed7d7;\n  border-radius: 12px;\n  border-left: 4px solid #e53e3e;\n}\n.avis-card .avis-content .avis-comment .comment-deleted i {\n  color: #e53e3e;\n}\n.avis-card .avis-content .avis-comment .comment-deleted span {\n  color: #c53030;\n  font-style: italic;\n}\n.avis-card .avis-content .avis-comment .comment-none {\n  display: flex;\n  align-items: center;\n  gap: 0.75rem;\n  padding: 1rem;\n  background: #f0fff4;\n  border-radius: 12px;\n  border-left: 4px solid #38a169;\n}\n.avis-card .avis-content .avis-comment .comment-none i {\n  color: #38a169;\n}\n.avis-card .avis-content .avis-comment .comment-none span {\n  color: #2f855a;\n  font-style: italic;\n}\n.avis-card .avis-content .avis-footer {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n}\n.avis-card .avis-content .avis-footer .avis-status .badge {\n  padding: 0.5rem 1rem;\n  border-radius: 50px;\n  font-weight: 600;\n  font-size: 0.85rem;\n}\n.avis-card .avis-content .avis-footer .avis-status .badge.badge-success {\n  background:\n    linear-gradient(\n      135deg,\n      #11998e 0%,\n      #38ef7d 100%);\n  color: white;\n}\n.avis-card .avis-content .avis-footer .avis-status .badge.badge-warning {\n  background:\n    linear-gradient(\n      135deg,\n      #f093fb 0%,\n      #f5576c 100%);\n  color: white;\n}\n.avis-card .avis-content .avis-footer .avis-status .badge.badge-danger {\n  background:\n    linear-gradient(\n      135deg,\n      #fc466b 0%,\n      #3f5efb 100%);\n  color: white;\n}\n.avis-card .avis-content .avis-footer .avis-status .badge.badge-secondary {\n  background: #e2e8f0;\n  color: #4a5568;\n}\n.avis-card .avis-content .avis-footer .avis-actions {\n  display: flex;\n  gap: 0.75rem;\n  flex-wrap: wrap;\n  align-items: center;\n  justify-content: flex-end;\n}\n@media (max-width: 768px) {\n  .avis-card .avis-content .avis-footer .avis-actions {\n    justify-content: center;\n    gap: 0.5rem;\n  }\n}\n.avis-card .avis-content .avis-footer .avis-actions .btn {\n  border-radius: 25px;\n  padding: 0.5rem 1rem;\n  font-size: 0.85rem;\n  font-weight: 600;\n  transition: all 0.2s ease-out;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  gap: 0.5rem;\n  border-width: 2px;\n  white-space: nowrap;\n}\n.avis-card .avis-content .avis-footer .avis-actions .btn:hover {\n  transform: translateY(-2px);\n  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);\n}\n.avis-card .avis-content .avis-footer .avis-actions .btn:active {\n  transform: translateY(0);\n}\n.avis-card .avis-content .avis-footer .avis-actions .btn i {\n  font-size: 0.9rem;\n}\n.avis-card .avis-content .avis-footer .avis-actions .btn span {\n  font-size: 0.85rem;\n}\n@media (max-width: 576px) {\n  .avis-card .avis-content .avis-footer .avis-actions .btn span {\n    display: none;\n  }\n}\n.avis-card .avis-content .avis-footer .avis-actions .btn.btn-outline-primary {\n  border-color: #4299e1;\n  color: #4299e1;\n}\n.avis-card .avis-content .avis-footer .avis-actions .btn.btn-outline-primary:hover {\n  background: #4299e1;\n  color: white;\n}\n.avis-card .avis-content .avis-footer .avis-actions .btn.btn-outline-success {\n  border-color: #48bb78;\n  color: #48bb78;\n}\n.avis-card .avis-content .avis-footer .avis-actions .btn.btn-outline-success:hover {\n  background: #48bb78;\n  color: white;\n}\n.avis-card .avis-content .avis-footer .avis-actions .btn.btn-outline-warning {\n  border-color: #ed8936;\n  color: #ed8936;\n}\n.avis-card .avis-content .avis-footer .avis-actions .btn.btn-outline-warning:hover {\n  background: #ed8936;\n  color: white;\n}\n.modal.show {\n  display: block !important;\n}\n.modal .modal-dialog {\n  margin: 2rem auto;\n  max-width: 600px;\n}\n.modal .modal-content {\n  border: none;\n  border-radius: 16px;\n  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.1);\n  overflow: hidden;\n}\n.modal .modal-content .modal-header {\n  background:\n    linear-gradient(\n      135deg,\n      #ed8936 0%,\n      #f6ad55 100%);\n  border-bottom: none;\n  padding: 1.5rem 2rem;\n}\n.modal .modal-content .modal-header .modal-title {\n  font-size: 1.25rem;\n  font-weight: 600;\n  margin: 0;\n  display: flex;\n  align-items: center;\n}\n.modal .modal-content .modal-header .btn-close {\n  filter: brightness(0) invert(1);\n  opacity: 0.8;\n}\n.modal .modal-content .modal-header .btn-close:hover {\n  opacity: 1;\n}\n.modal .modal-content .modal-body {\n  padding: 2rem;\n}\n.modal .modal-content .modal-body .alert {\n  border-radius: 12px;\n  border: none;\n  padding: 1.5rem;\n}\n.modal .modal-content .modal-body .alert.alert-info {\n  background:\n    linear-gradient(\n      135deg,\n      rgba(66, 153, 225, 0.1) 0%,\n      rgba(49, 130, 206, 0.1) 100%);\n  border-left: 4px solid #4299e1;\n  color: #2b6cb0;\n}\n.modal .modal-content .modal-body .alert.alert-info h6 {\n  color: #2b6cb0;\n  font-weight: 600;\n  margin-bottom: 1rem;\n}\n.modal .modal-content .modal-body .alert.alert-info p {\n  margin-bottom: 0.5rem;\n}\n.modal .modal-content .modal-body .alert.alert-info p:last-child {\n  margin-bottom: 0;\n}\n.modal .modal-content .modal-body .alert.alert-info .bg-light {\n  background: rgba(255, 255, 255, 0.8) !important;\n  border-radius: 8px;\n  padding: 1rem;\n  margin-top: 0.5rem;\n  font-style: italic;\n  color: #4a5568;\n}\n.modal .modal-content .modal-body .alert.alert-warning {\n  background:\n    linear-gradient(\n      135deg,\n      rgba(237, 137, 54, 0.1) 0%,\n      rgba(246, 173, 85, 0.1) 100%);\n  border-left: 4px solid #ed8936;\n  color: #c05621;\n}\n.modal .modal-content .modal-body .form-label {\n  font-weight: 600;\n  color: #4a5568;\n  margin-bottom: 0.75rem;\n  display: flex;\n  align-items: center;\n}\n.modal .modal-content .modal-body .form-label i {\n  color: #ed8936;\n}\n.modal .modal-content .modal-body .form-select,\n.modal .modal-content .modal-body .form-control {\n  border: 2px solid #e2e8f0;\n  border-radius: 12px;\n  padding: 0.75rem 1rem;\n  font-size: 0.95rem;\n  transition: all 0.2s ease-out;\n}\n.modal .modal-content .modal-body .form-select:focus,\n.modal .modal-content .modal-body .form-control:focus {\n  border-color: #ed8936;\n  box-shadow: 0 0 0 3px rgba(237, 137, 54, 0.1);\n}\n.modal .modal-content .modal-body .form-text {\n  color: #718096;\n  font-size: 0.85rem;\n  margin-top: 0.5rem;\n}\n.modal .modal-content .modal-footer {\n  padding: 1.5rem 2rem;\n  background: #f7fafc;\n  border-top: 1px solid #e2e8f0;\n}\n.modal .modal-content .modal-footer .btn {\n  border-radius: 25px;\n  padding: 0.75rem 1.5rem;\n  font-weight: 600;\n  transition: all 0.2s ease-out;\n}\n.modal .modal-content .modal-footer .btn.btn-secondary {\n  background: #e2e8f0;\n  border-color: #e2e8f0;\n  color: #4a5568;\n}\n.modal .modal-content .modal-footer .btn.btn-secondary:hover {\n  background: #cbd5e0;\n  border-color: #cbd5e0;\n  transform: translateY(-2px);\n}\n.modal .modal-content .modal-footer .btn.btn-warning {\n  background:\n    linear-gradient(\n      135deg,\n      #ed8936 0%,\n      #f6ad55 100%);\n  border: none;\n  color: white;\n}\n.modal .modal-content .modal-footer .btn.btn-warning:hover:not(:disabled) {\n  background:\n    linear-gradient(\n      135deg,\n      #dd7724 0%,\n      #ed8936 100%);\n  transform: translateY(-2px);\n  box-shadow: 0 4px 12px rgba(237, 137, 54, 0.3);\n}\n.modal .modal-content .modal-footer .btn.btn-warning:disabled {\n  opacity: 0.6;\n  cursor: not-allowed;\n}\n.modal-backdrop {\n  background-color: rgba(0, 0, 0, 0.5);\n  -webkit-backdrop-filter: blur(4px);\n  backdrop-filter: blur(4px);\n}\n.modal-backdrop.show {\n  opacity: 1;\n}\n/*# sourceMappingURL=avis-fournisseur.component.css.map */\n'] }]
  }], () => [{ type: AvisModerationService }], null);
})();
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && \u0275setClassDebugInfo(AvisFournisseurComponent, { className: "AvisFournisseurComponent", filePath: "src/app/components/fournisseur/avis-fournisseur/avis-fournisseur.component.ts", lineNumber: 13 });
})();
export {
  AvisFournisseurComponent
};
//# sourceMappingURL=chunk-JD3PSUQL.js.map

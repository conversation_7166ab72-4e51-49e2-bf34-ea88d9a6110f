import {
  RouterLink,
  RouterModule
} from "./chunk-6BVUYNW4.js";
import "./chunk-7JDDWGD3.js";
import {
  CommonModule,
  Component,
  setClassMetadata,
  ɵsetClassDebugInfo,
  ɵɵdefineComponent,
  ɵɵelement,
  ɵɵelementEnd,
  ɵɵelementStart,
  ɵɵtext
} from "./chunk-UBZQS7JS.js";

// src/app/components/home/<USER>
var HomeComponent = class _HomeComponent {
  static \u0275fac = function HomeComponent_Factory(__ngFactoryType__) {
    return new (__ngFactoryType__ || _HomeComponent)();
  };
  static \u0275cmp = /* @__PURE__ */ \u0275\u0275defineComponent({ type: _HomeComponent, selectors: [["app-home"]], decls: 151, vars: 0, consts: [[1, "home-container"], [1, "hero-section"], [1, "hero-content"], [1, "hero-text"], [1, "hero-title"], [1, "gradient-text"], [1, "highlight"], [1, "hero-description"], [1, "hero-actions"], ["routerLink", "/login", 1, "btn", "btn-primary"], [1, "btn-icon"], ["routerLink", "/admin-simple", 1, "btn", "btn-secondary"], [1, "hero-visual"], [1, "floating-card"], [1, "card-header"], [1, "card-dots"], [1, "card-content"], [1, "stat-item"], [1, "stat-number"], [1, "stat-label"], [1, "features-section"], [1, "container"], [1, "section-title"], [1, "features-grid"], [1, "feature-card"], [1, "feature-icon"], ["routerLink", "/login", 1, "feature-link"], [1, "feature-link", "disabled"], ["routerLink", "/admin-simple", 1, "feature-link"], [1, "feature-link"], [1, "tech-section"], [1, "tech-grid"], [1, "tech-item"], [1, "tech-icon"], [1, "footer"], [1, "footer-content"], [1, "footer-text"], [1, "footer-links"], ["routerLink", "/login", 1, "footer-link"], ["routerLink", "/admin-simple", 1, "footer-link"]], template: function HomeComponent_Template(rf, ctx) {
    if (rf & 1) {
      \u0275\u0275elementStart(0, "div", 0)(1, "section", 1)(2, "div", 2)(3, "div", 3)(4, "h1", 4)(5, "span", 5);
      \u0275\u0275text(6, "Plateforme Fournisseur");
      \u0275\u0275elementEnd();
      \u0275\u0275element(7, "br");
      \u0275\u0275elementStart(8, "span", 6);
      \u0275\u0275text(9, "Angular 19");
      \u0275\u0275elementEnd()();
      \u0275\u0275elementStart(10, "p", 7);
      \u0275\u0275text(11, " Interface moderne et cr\xE9ative pour la gestion de votre activit\xE9 de fournisseur. D\xE9couvrez une exp\xE9rience utilisateur repens\xE9e avec les derni\xE8res technologies. ");
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(12, "div", 8)(13, "a", 9)(14, "span", 10);
      \u0275\u0275text(15, "\u{1F680}");
      \u0275\u0275elementEnd();
      \u0275\u0275text(16, " Commencer ");
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(17, "a", 11)(18, "span", 10);
      \u0275\u0275text(19, "\u{1F451}");
      \u0275\u0275elementEnd();
      \u0275\u0275text(20, " Administration ");
      \u0275\u0275elementEnd()()();
      \u0275\u0275elementStart(21, "div", 12)(22, "div", 13)(23, "div", 14)(24, "div", 15);
      \u0275\u0275element(25, "span")(26, "span")(27, "span");
      \u0275\u0275elementEnd()();
      \u0275\u0275elementStart(28, "div", 16)(29, "div", 17)(30, "span", 18);
      \u0275\u0275text(31, "100%");
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(32, "span", 19);
      \u0275\u0275text(33, "Angular 19");
      \u0275\u0275elementEnd()();
      \u0275\u0275elementStart(34, "div", 17)(35, "span", 18);
      \u0275\u0275text(36, "\u2728");
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(37, "span", 19);
      \u0275\u0275text(38, "Design Cr\xE9atif");
      \u0275\u0275elementEnd()();
      \u0275\u0275elementStart(39, "div", 17)(40, "span", 18);
      \u0275\u0275text(41, "\u{1F680}");
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(42, "span", 19);
      \u0275\u0275text(43, "Performance");
      \u0275\u0275elementEnd()()()()()()();
      \u0275\u0275elementStart(44, "section", 20)(45, "div", 21)(46, "h2", 22);
      \u0275\u0275text(47, "Fonctionnalit\xE9s Principales");
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(48, "div", 23)(49, "div", 24)(50, "div", 25);
      \u0275\u0275text(51, "\u{1F510}");
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(52, "h3");
      \u0275\u0275text(53, "Authentification S\xE9curis\xE9e");
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(54, "p");
      \u0275\u0275text(55, "Syst\xE8me de connexion moderne avec interface cr\xE9ative et s\xE9curit\xE9 renforc\xE9e.");
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(56, "a", 26);
      \u0275\u0275text(57, "D\xE9couvrir \u2192");
      \u0275\u0275elementEnd()();
      \u0275\u0275elementStart(58, "div", 24)(59, "div", 25);
      \u0275\u0275text(60, "\u{1F4CA}");
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(61, "h3");
      \u0275\u0275text(62, "Tableau de Bord");
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(63, "p");
      \u0275\u0275text(64, "Vue d'ensemble compl\xE8te de votre activit\xE9 avec m\xE9triques en temps r\xE9el.");
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(65, "span", 27);
      \u0275\u0275text(66, "Bient\xF4t disponible");
      \u0275\u0275elementEnd()();
      \u0275\u0275elementStart(67, "div", 24)(68, "div", 25);
      \u0275\u0275text(69, "\u{1F4E6}");
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(70, "h3");
      \u0275\u0275text(71, "Gestion Produits");
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(72, "p");
      \u0275\u0275text(73, "Interface intuitive pour g\xE9rer votre catalogue et vos stocks efficacement.");
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(74, "span", 27);
      \u0275\u0275text(75, "En d\xE9veloppement");
      \u0275\u0275elementEnd()();
      \u0275\u0275elementStart(76, "div", 24)(77, "div", 25);
      \u0275\u0275text(78, "\u{1F69A}");
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(79, "h3");
      \u0275\u0275text(80, "Suivi Commandes");
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(81, "p");
      \u0275\u0275text(82, "Syst\xE8me complet de gestion et suivi des commandes et livraisons.");
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(83, "span", 27);
      \u0275\u0275text(84, "\xC0 venir");
      \u0275\u0275elementEnd()();
      \u0275\u0275elementStart(85, "div", 24)(86, "div", 25);
      \u0275\u0275text(87, "\u{1F451}");
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(88, "h3");
      \u0275\u0275text(89, "Interface Admin");
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(90, "p");
      \u0275\u0275text(91, "Panneau d'administration avec double authentification et gestion avanc\xE9e.");
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(92, "a", 28);
      \u0275\u0275text(93, "Acc\xE9der \u2192");
      \u0275\u0275elementEnd()();
      \u0275\u0275elementStart(94, "div", 24)(95, "div", 25);
      \u0275\u0275text(96, "\u{1F4F1}");
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(97, "h3");
      \u0275\u0275text(98, "Design Responsive");
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(99, "p");
      \u0275\u0275text(100, "Interface adaptative qui fonctionne parfaitement sur tous les appareils.");
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(101, "span", 29);
      \u0275\u0275text(102, "\u2713 Actif");
      \u0275\u0275elementEnd()()()()();
      \u0275\u0275elementStart(103, "section", 30)(104, "div", 21)(105, "h2", 22);
      \u0275\u0275text(106, "Technologies Utilis\xE9es");
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(107, "div", 31)(108, "div", 32)(109, "div", 33);
      \u0275\u0275text(110, "\u{1F170}\uFE0F");
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(111, "span");
      \u0275\u0275text(112, "Angular 19");
      \u0275\u0275elementEnd()();
      \u0275\u0275elementStart(113, "div", 32)(114, "div", 33);
      \u0275\u0275text(115, "\u{1F4E1}");
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(116, "span");
      \u0275\u0275text(117, "Signals");
      \u0275\u0275elementEnd()();
      \u0275\u0275elementStart(118, "div", 32)(119, "div", 33);
      \u0275\u0275text(120, "\u{1F3A8}");
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(121, "span");
      \u0275\u0275text(122, "CSS Cr\xE9atif");
      \u0275\u0275elementEnd()();
      \u0275\u0275elementStart(123, "div", 32)(124, "div", 33);
      \u0275\u0275text(125, "\u26A1");
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(126, "span");
      \u0275\u0275text(127, "Performance");
      \u0275\u0275elementEnd()();
      \u0275\u0275elementStart(128, "div", 32)(129, "div", 33);
      \u0275\u0275text(130, "\u{1F4F1}");
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(131, "span");
      \u0275\u0275text(132, "Responsive");
      \u0275\u0275elementEnd()();
      \u0275\u0275elementStart(133, "div", 32)(134, "div", 33);
      \u0275\u0275text(135, "\u{1F512}");
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(136, "span");
      \u0275\u0275text(137, "S\xE9curis\xE9");
      \u0275\u0275elementEnd()()()()();
      \u0275\u0275elementStart(138, "footer", 34)(139, "div", 21)(140, "div", 35)(141, "div", 36)(142, "h3");
      \u0275\u0275text(143, "Plateforme Fournisseur Angular 19");
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(144, "p");
      \u0275\u0275text(145, "Interface moderne pour la gestion de votre activit\xE9 commerciale");
      \u0275\u0275elementEnd()();
      \u0275\u0275elementStart(146, "div", 37)(147, "a", 38);
      \u0275\u0275text(148, "Connexion");
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(149, "a", 39);
      \u0275\u0275text(150, "Administration");
      \u0275\u0275elementEnd()()()()()();
    }
  }, dependencies: [CommonModule, RouterModule, RouterLink], styles: [`

.home-container[_ngcontent-%COMP%] {
  min-height: 100vh;
  background:
    linear-gradient(
      135deg,
      #667eea 0%,
      #764ba2 100%);
  position: relative;
  overflow-x: hidden;
}
.home-container[_ngcontent-%COMP%]::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="rgba(255,255,255,0.1)"/><circle cx="75" cy="75" r="1" fill="rgba(255,255,255,0.1)"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
  pointer-events: none;
}
.hero-section[_ngcontent-%COMP%] {
  padding: 4rem 2rem;
  min-height: 100vh;
  display: flex;
  align-items: center;
  position: relative;
}
.hero-content[_ngcontent-%COMP%] {
  max-width: 1200px;
  margin: 0 auto;
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 4rem;
  align-items: center;
}
.hero-title[_ngcontent-%COMP%] {
  font-size: 3.5rem;
  font-weight: 800;
  line-height: 1.1;
  margin: 0 0 1.5rem 0;
  color: white;
}
.gradient-text[_ngcontent-%COMP%] {
  background:
    linear-gradient(
      135deg,
      #ffffff 0%,
      #f0f9ff 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}
.highlight[_ngcontent-%COMP%] {
  background:
    linear-gradient(
      135deg,
      #fbbf24 0%,
      #f59e0b 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}
.hero-description[_ngcontent-%COMP%] {
  font-size: 1.25rem;
  line-height: 1.6;
  color: rgba(255, 255, 255, 0.9);
  margin: 0 0 2rem 0;
}
.hero-actions[_ngcontent-%COMP%] {
  display: flex;
  gap: 1rem;
  flex-wrap: wrap;
}
.btn[_ngcontent-%COMP%] {
  padding: 1rem 2rem;
  border-radius: 12px;
  text-decoration: none;
  font-weight: 600;
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  transition: all 0.3s ease;
  border: none;
  cursor: pointer;
}
.btn-primary[_ngcontent-%COMP%] {
  background: rgba(255, 255, 255, 0.2);
  color: white;
  -webkit-backdrop-filter: blur(10px);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.3);
}
.btn-primary[_ngcontent-%COMP%]:hover {
  background: rgba(255, 255, 255, 0.3);
  transform: translateY(-2px);
}
.btn-secondary[_ngcontent-%COMP%] {
  background: transparent;
  color: white;
  border: 2px solid rgba(255, 255, 255, 0.3);
}
.btn-secondary[_ngcontent-%COMP%]:hover {
  background: rgba(255, 255, 255, 0.1);
  transform: translateY(-2px);
}
.btn-icon[_ngcontent-%COMP%] {
  font-size: 1.25rem;
}
.floating-card[_ngcontent-%COMP%] {
  background: rgba(255, 255, 255, 0.1);
  -webkit-backdrop-filter: blur(20px);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 20px;
  padding: 2rem;
  animation: _ngcontent-%COMP%_float 6s ease-in-out infinite;
}
@keyframes _ngcontent-%COMP%_float {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-20px);
  }
}
.card-header[_ngcontent-%COMP%] {
  display: flex;
  justify-content: flex-end;
  margin-bottom: 1.5rem;
}
.card-dots[_ngcontent-%COMP%] {
  display: flex;
  gap: 0.5rem;
}
.card-dots[_ngcontent-%COMP%]   span[_ngcontent-%COMP%] {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.3);
}
.card-content[_ngcontent-%COMP%] {
  display: grid;
  gap: 1.5rem;
}
.stat-item[_ngcontent-%COMP%] {
  text-align: center;
  color: white;
}
.stat-number[_ngcontent-%COMP%] {
  display: block;
  font-size: 2rem;
  font-weight: bold;
  margin-bottom: 0.5rem;
}
.stat-label[_ngcontent-%COMP%] {
  font-size: 0.875rem;
  opacity: 0.8;
}
.features-section[_ngcontent-%COMP%], 
.tech-section[_ngcontent-%COMP%] {
  padding: 4rem 2rem;
  background: white;
}
.container[_ngcontent-%COMP%] {
  max-width: 1200px;
  margin: 0 auto;
}
.section-title[_ngcontent-%COMP%] {
  text-align: center;
  font-size: 2.5rem;
  font-weight: 700;
  margin: 0 0 3rem 0;
  color: #1e293b;
}
.features-grid[_ngcontent-%COMP%] {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 2rem;
}
.feature-card[_ngcontent-%COMP%] {
  background: white;
  border-radius: 16px;
  padding: 2rem;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
  border: 2px solid transparent;
}
.feature-card[_ngcontent-%COMP%]:hover {
  transform: translateY(-4px);
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1);
  border-color: #3b82f6;
}
.feature-icon[_ngcontent-%COMP%] {
  font-size: 3rem;
  margin-bottom: 1rem;
}
.feature-card[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%] {
  margin: 0 0 1rem 0;
  color: #1e293b;
  font-size: 1.25rem;
}
.feature-card[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {
  margin: 0 0 1.5rem 0;
  color: #64748b;
  line-height: 1.6;
}
.feature-link[_ngcontent-%COMP%] {
  color: #3b82f6;
  text-decoration: none;
  font-weight: 600;
}
.feature-link[_ngcontent-%COMP%]:hover {
  text-decoration: underline;
}
.feature-link.disabled[_ngcontent-%COMP%] {
  color: #94a3b8;
  cursor: not-allowed;
}
.tech-grid[_ngcontent-%COMP%] {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  gap: 1.5rem;
}
.tech-item[_ngcontent-%COMP%] {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.5rem;
  padding: 1.5rem;
  background: #f8fafc;
  border-radius: 12px;
  transition: all 0.3s ease;
}
.tech-item[_ngcontent-%COMP%]:hover {
  background: #e2e8f0;
  transform: translateY(-2px);
}
.tech-icon[_ngcontent-%COMP%] {
  font-size: 2rem;
}
.footer[_ngcontent-%COMP%] {
  background: #1e293b;
  color: white;
  padding: 3rem 2rem;
}
.footer-content[_ngcontent-%COMP%] {
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
  gap: 2rem;
}
.footer-text[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%] {
  margin: 0 0 0.5rem 0;
  font-size: 1.25rem;
}
.footer-text[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {
  margin: 0;
  opacity: 0.8;
}
.footer-links[_ngcontent-%COMP%] {
  display: flex;
  gap: 2rem;
}
.footer-link[_ngcontent-%COMP%] {
  color: white;
  text-decoration: none;
  transition: opacity 0.3s ease;
}
.footer-link[_ngcontent-%COMP%]:hover {
  opacity: 0.8;
}
@media (max-width: 768px) {
  .hero-content[_ngcontent-%COMP%] {
    grid-template-columns: 1fr;
    text-align: center;
  }
  .hero-title[_ngcontent-%COMP%] {
    font-size: 2.5rem;
  }
  .features-grid[_ngcontent-%COMP%] {
    grid-template-columns: 1fr;
  }
  .tech-grid[_ngcontent-%COMP%] {
    grid-template-columns: repeat(2, 1fr);
  }
  .footer-content[_ngcontent-%COMP%] {
    flex-direction: column;
    text-align: center;
  }
}
/*# sourceMappingURL=home.component.css.map */`] });
};
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && setClassMetadata(HomeComponent, [{
    type: Component,
    args: [{ selector: "app-home", standalone: true, imports: [CommonModule, RouterModule], template: `
    <div class="home-container">
      <!-- Hero Section -->
      <section class="hero-section">
        <div class="hero-content">
          <div class="hero-text">
            <h1 class="hero-title">
              <span class="gradient-text">Plateforme Fournisseur</span>
              <br>
              <span class="highlight">Angular 19</span>
            </h1>
            <p class="hero-description">
              Interface moderne et cr\xE9ative pour la gestion de votre activit\xE9 de fournisseur. 
              D\xE9couvrez une exp\xE9rience utilisateur repens\xE9e avec les derni\xE8res technologies.
            </p>
            <div class="hero-actions">
              <a routerLink="/login" class="btn btn-primary">
                <span class="btn-icon">\u{1F680}</span>
                Commencer
              </a>
              <a routerLink="/admin-simple" class="btn btn-secondary">
                <span class="btn-icon">\u{1F451}</span>
                Administration
              </a>
            </div>
          </div>
          <div class="hero-visual">
            <div class="floating-card">
              <div class="card-header">
                <div class="card-dots">
                  <span></span>
                  <span></span>
                  <span></span>
                </div>
              </div>
              <div class="card-content">
                <div class="stat-item">
                  <span class="stat-number">100%</span>
                  <span class="stat-label">Angular 19</span>
                </div>
                <div class="stat-item">
                  <span class="stat-number">\u2728</span>
                  <span class="stat-label">Design Cr\xE9atif</span>
                </div>
                <div class="stat-item">
                  <span class="stat-number">\u{1F680}</span>
                  <span class="stat-label">Performance</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      <!-- Features Section -->
      <section class="features-section">
        <div class="container">
          <h2 class="section-title">Fonctionnalit\xE9s Principales</h2>
          <div class="features-grid">
            <div class="feature-card">
              <div class="feature-icon">\u{1F510}</div>
              <h3>Authentification S\xE9curis\xE9e</h3>
              <p>Syst\xE8me de connexion moderne avec interface cr\xE9ative et s\xE9curit\xE9 renforc\xE9e.</p>
              <a routerLink="/login" class="feature-link">D\xE9couvrir \u2192</a>
            </div>
            
            <div class="feature-card">
              <div class="feature-icon">\u{1F4CA}</div>
              <h3>Tableau de Bord</h3>
              <p>Vue d'ensemble compl\xE8te de votre activit\xE9 avec m\xE9triques en temps r\xE9el.</p>
              <span class="feature-link disabled">Bient\xF4t disponible</span>
            </div>
            
            <div class="feature-card">
              <div class="feature-icon">\u{1F4E6}</div>
              <h3>Gestion Produits</h3>
              <p>Interface intuitive pour g\xE9rer votre catalogue et vos stocks efficacement.</p>
              <span class="feature-link disabled">En d\xE9veloppement</span>
            </div>
            
            <div class="feature-card">
              <div class="feature-icon">\u{1F69A}</div>
              <h3>Suivi Commandes</h3>
              <p>Syst\xE8me complet de gestion et suivi des commandes et livraisons.</p>
              <span class="feature-link disabled">\xC0 venir</span>
            </div>
            
            <div class="feature-card">
              <div class="feature-icon">\u{1F451}</div>
              <h3>Interface Admin</h3>
              <p>Panneau d'administration avec double authentification et gestion avanc\xE9e.</p>
              <a routerLink="/admin-simple" class="feature-link">Acc\xE9der \u2192</a>
            </div>
            
            <div class="feature-card">
              <div class="feature-icon">\u{1F4F1}</div>
              <h3>Design Responsive</h3>
              <p>Interface adaptative qui fonctionne parfaitement sur tous les appareils.</p>
              <span class="feature-link">\u2713 Actif</span>
            </div>
          </div>
        </div>
      </section>

      <!-- Tech Stack Section -->
      <section class="tech-section">
        <div class="container">
          <h2 class="section-title">Technologies Utilis\xE9es</h2>
          <div class="tech-grid">
            <div class="tech-item">
              <div class="tech-icon">\u{1F170}\uFE0F</div>
              <span>Angular 19</span>
            </div>
            <div class="tech-item">
              <div class="tech-icon">\u{1F4E1}</div>
              <span>Signals</span>
            </div>
            <div class="tech-item">
              <div class="tech-icon">\u{1F3A8}</div>
              <span>CSS Cr\xE9atif</span>
            </div>
            <div class="tech-item">
              <div class="tech-icon">\u26A1</div>
              <span>Performance</span>
            </div>
            <div class="tech-item">
              <div class="tech-icon">\u{1F4F1}</div>
              <span>Responsive</span>
            </div>
            <div class="tech-item">
              <div class="tech-icon">\u{1F512}</div>
              <span>S\xE9curis\xE9</span>
            </div>
          </div>
        </div>
      </section>

      <!-- Footer -->
      <footer class="footer">
        <div class="container">
          <div class="footer-content">
            <div class="footer-text">
              <h3>Plateforme Fournisseur Angular 19</h3>
              <p>Interface moderne pour la gestion de votre activit\xE9 commerciale</p>
            </div>
            <div class="footer-links">
              <a routerLink="/login" class="footer-link">Connexion</a>
              <a routerLink="/admin-simple" class="footer-link">Administration</a>
            </div>
          </div>
        </div>
      </footer>
    </div>
  `, styles: [`/* angular:styles/component:css;1a4e80d64094d2895a8bd5fbf72713863a39ed9a6ca08e95e5d2cfe6d16b11b8;C:/Users/<USER>/Desktop/STE/PFE/PFE/frontend-fournisseur/src/app/components/home/<USER>/
.home-container {
  min-height: 100vh;
  background:
    linear-gradient(
      135deg,
      #667eea 0%,
      #764ba2 100%);
  position: relative;
  overflow-x: hidden;
}
.home-container::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="rgba(255,255,255,0.1)"/><circle cx="75" cy="75" r="1" fill="rgba(255,255,255,0.1)"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
  pointer-events: none;
}
.hero-section {
  padding: 4rem 2rem;
  min-height: 100vh;
  display: flex;
  align-items: center;
  position: relative;
}
.hero-content {
  max-width: 1200px;
  margin: 0 auto;
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 4rem;
  align-items: center;
}
.hero-title {
  font-size: 3.5rem;
  font-weight: 800;
  line-height: 1.1;
  margin: 0 0 1.5rem 0;
  color: white;
}
.gradient-text {
  background:
    linear-gradient(
      135deg,
      #ffffff 0%,
      #f0f9ff 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}
.highlight {
  background:
    linear-gradient(
      135deg,
      #fbbf24 0%,
      #f59e0b 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}
.hero-description {
  font-size: 1.25rem;
  line-height: 1.6;
  color: rgba(255, 255, 255, 0.9);
  margin: 0 0 2rem 0;
}
.hero-actions {
  display: flex;
  gap: 1rem;
  flex-wrap: wrap;
}
.btn {
  padding: 1rem 2rem;
  border-radius: 12px;
  text-decoration: none;
  font-weight: 600;
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  transition: all 0.3s ease;
  border: none;
  cursor: pointer;
}
.btn-primary {
  background: rgba(255, 255, 255, 0.2);
  color: white;
  -webkit-backdrop-filter: blur(10px);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.3);
}
.btn-primary:hover {
  background: rgba(255, 255, 255, 0.3);
  transform: translateY(-2px);
}
.btn-secondary {
  background: transparent;
  color: white;
  border: 2px solid rgba(255, 255, 255, 0.3);
}
.btn-secondary:hover {
  background: rgba(255, 255, 255, 0.1);
  transform: translateY(-2px);
}
.btn-icon {
  font-size: 1.25rem;
}
.floating-card {
  background: rgba(255, 255, 255, 0.1);
  -webkit-backdrop-filter: blur(20px);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 20px;
  padding: 2rem;
  animation: float 6s ease-in-out infinite;
}
@keyframes float {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-20px);
  }
}
.card-header {
  display: flex;
  justify-content: flex-end;
  margin-bottom: 1.5rem;
}
.card-dots {
  display: flex;
  gap: 0.5rem;
}
.card-dots span {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.3);
}
.card-content {
  display: grid;
  gap: 1.5rem;
}
.stat-item {
  text-align: center;
  color: white;
}
.stat-number {
  display: block;
  font-size: 2rem;
  font-weight: bold;
  margin-bottom: 0.5rem;
}
.stat-label {
  font-size: 0.875rem;
  opacity: 0.8;
}
.features-section,
.tech-section {
  padding: 4rem 2rem;
  background: white;
}
.container {
  max-width: 1200px;
  margin: 0 auto;
}
.section-title {
  text-align: center;
  font-size: 2.5rem;
  font-weight: 700;
  margin: 0 0 3rem 0;
  color: #1e293b;
}
.features-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 2rem;
}
.feature-card {
  background: white;
  border-radius: 16px;
  padding: 2rem;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
  border: 2px solid transparent;
}
.feature-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1);
  border-color: #3b82f6;
}
.feature-icon {
  font-size: 3rem;
  margin-bottom: 1rem;
}
.feature-card h3 {
  margin: 0 0 1rem 0;
  color: #1e293b;
  font-size: 1.25rem;
}
.feature-card p {
  margin: 0 0 1.5rem 0;
  color: #64748b;
  line-height: 1.6;
}
.feature-link {
  color: #3b82f6;
  text-decoration: none;
  font-weight: 600;
}
.feature-link:hover {
  text-decoration: underline;
}
.feature-link.disabled {
  color: #94a3b8;
  cursor: not-allowed;
}
.tech-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  gap: 1.5rem;
}
.tech-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.5rem;
  padding: 1.5rem;
  background: #f8fafc;
  border-radius: 12px;
  transition: all 0.3s ease;
}
.tech-item:hover {
  background: #e2e8f0;
  transform: translateY(-2px);
}
.tech-icon {
  font-size: 2rem;
}
.footer {
  background: #1e293b;
  color: white;
  padding: 3rem 2rem;
}
.footer-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
  gap: 2rem;
}
.footer-text h3 {
  margin: 0 0 0.5rem 0;
  font-size: 1.25rem;
}
.footer-text p {
  margin: 0;
  opacity: 0.8;
}
.footer-links {
  display: flex;
  gap: 2rem;
}
.footer-link {
  color: white;
  text-decoration: none;
  transition: opacity 0.3s ease;
}
.footer-link:hover {
  opacity: 0.8;
}
@media (max-width: 768px) {
  .hero-content {
    grid-template-columns: 1fr;
    text-align: center;
  }
  .hero-title {
    font-size: 2.5rem;
  }
  .features-grid {
    grid-template-columns: 1fr;
  }
  .tech-grid {
    grid-template-columns: repeat(2, 1fr);
  }
  .footer-content {
    flex-direction: column;
    text-align: center;
  }
}
/*# sourceMappingURL=home.component.css.map */
`] }]
  }], null, null);
})();
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && \u0275setClassDebugInfo(HomeComponent, { className: "HomeComponent", filePath: "src/app/components/home/<USER>", lineNumber: 495 });
})();
export {
  HomeComponent
};
//# sourceMappingURL=chunk-D5EM4YND.js.map

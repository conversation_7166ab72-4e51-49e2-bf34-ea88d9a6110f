{"version": 3, "sources": ["node_modules/@angular/platform-browser/fesm2022/animations/async.mjs", "src/app/guards/fournisseur.guard.ts", "src/app/test-images.component.ts", "src/app/app.routes.ts", "src/app/app.config.ts", "src/app/app.component.ts", "src/app/app.component.html", "src/main.ts"], "sourcesContent": ["/**\n * @license Angular v19.2.14\n * (c) 2010-2025 Google LLC. https://angular.io/\n * License: MIT\n */\n\nimport { DOCUMENT } from '@angular/common';\nimport * as i0 from '@angular/core';\nimport { InjectionToken, inject, Injector, ɵRuntimeError as _RuntimeError, ɵChangeDetectionScheduler as _ChangeDetectionScheduler, Injectable, ɵperformanceMarkFeature as _performanceMarkFeature, makeEnvironmentProviders, NgZone, RendererFactory2, ANIMATION_MODULE_TYPE } from '@angular/core';\nimport { DomRendererFactory2 } from '../dom_renderer-DGKzginR.mjs';\nconst ANIMATION_PREFIX = '@';\nclass AsyncAnimationRendererFactory {\n  doc;\n  delegate;\n  zone;\n  animationType;\n  moduleImpl;\n  _rendererFactoryPromise = null;\n  scheduler = null;\n  injector = inject(Injector);\n  loadingSchedulerFn = inject(ɵASYNC_ANIMATION_LOADING_SCHEDULER_FN, {\n    optional: true\n  });\n  _engine;\n  /**\n   *\n   * @param moduleImpl allows to provide a mock implmentation (or will load the animation module)\n   */\n  constructor(doc, delegate, zone, animationType, moduleImpl) {\n    this.doc = doc;\n    this.delegate = delegate;\n    this.zone = zone;\n    this.animationType = animationType;\n    this.moduleImpl = moduleImpl;\n  }\n  /** @docs-private */\n  ngOnDestroy() {\n    // When the root view is removed, the renderer defers the actual work to the\n    // `TransitionAnimationEngine` to do this, and the `TransitionAnimationEngine` doesn't actually\n    // remove the DOM node, but just calls `markElementAsRemoved()`. The actual DOM node is not\n    // removed until `TransitionAnimationEngine` \"flushes\".\n    // Note: we already flush on destroy within the `InjectableAnimationEngine`. The injectable\n    // engine is not provided when async animations are used.\n    this._engine?.flush();\n  }\n  /**\n   * @internal\n   */\n  loadImpl() {\n    // Note on the `.then(m => m)` part below: Closure compiler optimizations in g3 require\n    // `.then` to be present for a dynamic import (or an import should be `await`ed) to detect\n    // the set of imported symbols.\n    const loadFn = () => this.moduleImpl ?? import('@angular/animations/browser').then(m => m);\n    let moduleImplPromise;\n    if (this.loadingSchedulerFn) {\n      moduleImplPromise = this.loadingSchedulerFn(loadFn);\n    } else {\n      moduleImplPromise = loadFn();\n    }\n    return moduleImplPromise.catch(e => {\n      throw new _RuntimeError(5300 /* RuntimeErrorCode.ANIMATION_RENDERER_ASYNC_LOADING_FAILURE */, (typeof ngDevMode === 'undefined' || ngDevMode) && 'Async loading for animations package was ' + 'enabled, but loading failed. Angular falls back to using regular rendering. ' + \"No animations will be displayed and their styles won't be applied.\");\n    }).then(({\n      ɵcreateEngine,\n      ɵAnimationRendererFactory\n    }) => {\n      // We can't create the renderer yet because we might need the hostElement and the type\n      // Both are provided in createRenderer().\n      this._engine = ɵcreateEngine(this.animationType, this.doc);\n      const rendererFactory = new ɵAnimationRendererFactory(this.delegate, this._engine, this.zone);\n      this.delegate = rendererFactory;\n      return rendererFactory;\n    });\n  }\n  /**\n   * This method is delegating the renderer creation to the factories.\n   * It uses default factory while the animation factory isn't loaded\n   * and will rely on the animation factory once it is loaded.\n   *\n   * Calling this method will trigger as side effect the loading of the animation module\n   * if the renderered component uses animations.\n   */\n  createRenderer(hostElement, rendererType) {\n    const renderer = this.delegate.createRenderer(hostElement, rendererType);\n    if (renderer.ɵtype === 0 /* AnimationRendererType.Regular */) {\n      // The factory is already loaded, this is an animation renderer\n      return renderer;\n    }\n    // We need to prevent the DomRenderer to throw an error because of synthetic properties\n    if (typeof renderer.throwOnSyntheticProps === 'boolean') {\n      renderer.throwOnSyntheticProps = false;\n    }\n    // Using a dynamic renderer to switch the renderer implementation once the module is loaded.\n    const dynamicRenderer = new DynamicDelegationRenderer(renderer);\n    // Kick off the module loading if the component uses animations but the module hasn't been\n    // loaded yet.\n    if (rendererType?.data?.['animation'] && !this._rendererFactoryPromise) {\n      this._rendererFactoryPromise = this.loadImpl();\n    }\n    this._rendererFactoryPromise?.then(animationRendererFactory => {\n      const animationRenderer = animationRendererFactory.createRenderer(hostElement, rendererType);\n      dynamicRenderer.use(animationRenderer);\n      this.scheduler ??= this.injector.get(_ChangeDetectionScheduler, null, {\n        optional: true\n      });\n      this.scheduler?.notify(10 /* NotificationSource.AsyncAnimationsLoaded */);\n    }).catch(e => {\n      // Permanently use regular renderer when loading fails.\n      dynamicRenderer.use(renderer);\n    });\n    return dynamicRenderer;\n  }\n  begin() {\n    this.delegate.begin?.();\n  }\n  end() {\n    this.delegate.end?.();\n  }\n  whenRenderingDone() {\n    return this.delegate.whenRenderingDone?.() ?? Promise.resolve();\n  }\n  /**\n   * Used during HMR to clear any cached data about a component.\n   * @param componentId ID of the component that is being replaced.\n   */\n  componentReplaced(componentId) {\n    // Flush the engine since the renderer destruction waits for animations to be done.\n    this._engine?.flush();\n    this.delegate.componentReplaced?.(componentId);\n  }\n  static ɵfac = function AsyncAnimationRendererFactory_Factory(__ngFactoryType__) {\n    i0.ɵɵinvalidFactory();\n  };\n  static ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n    token: AsyncAnimationRendererFactory,\n    factory: AsyncAnimationRendererFactory.ɵfac\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(AsyncAnimationRendererFactory, [{\n    type: Injectable\n  }], () => [{\n    type: Document\n  }, {\n    type: i0.RendererFactory2\n  }, {\n    type: i0.NgZone\n  }, {\n    type: undefined\n  }, {\n    type: Promise\n  }], null);\n})();\n/**\n * The class allows to dynamicly switch between different renderer implementations\n * by changing the delegate renderer.\n */\nclass DynamicDelegationRenderer {\n  delegate;\n  // List of callbacks that need to be replayed on the animation renderer once its loaded\n  replay = [];\n  ɵtype = 1 /* AnimationRendererType.Delegated */;\n  constructor(delegate) {\n    this.delegate = delegate;\n  }\n  use(impl) {\n    this.delegate = impl;\n    if (this.replay !== null) {\n      // Replay queued actions using the animation renderer to apply\n      // all events and properties collected while loading was in progress.\n      for (const fn of this.replay) {\n        fn(impl);\n      }\n      // Set to `null` to indicate that the queue was processed\n      // and we no longer need to collect events and properties.\n      this.replay = null;\n    }\n  }\n  get data() {\n    return this.delegate.data;\n  }\n  destroy() {\n    this.replay = null;\n    this.delegate.destroy();\n  }\n  createElement(name, namespace) {\n    return this.delegate.createElement(name, namespace);\n  }\n  createComment(value) {\n    return this.delegate.createComment(value);\n  }\n  createText(value) {\n    return this.delegate.createText(value);\n  }\n  get destroyNode() {\n    return this.delegate.destroyNode;\n  }\n  appendChild(parent, newChild) {\n    this.delegate.appendChild(parent, newChild);\n  }\n  insertBefore(parent, newChild, refChild, isMove) {\n    this.delegate.insertBefore(parent, newChild, refChild, isMove);\n  }\n  removeChild(parent, oldChild, isHostElement) {\n    this.delegate.removeChild(parent, oldChild, isHostElement);\n  }\n  selectRootElement(selectorOrNode, preserveContent) {\n    return this.delegate.selectRootElement(selectorOrNode, preserveContent);\n  }\n  parentNode(node) {\n    return this.delegate.parentNode(node);\n  }\n  nextSibling(node) {\n    return this.delegate.nextSibling(node);\n  }\n  setAttribute(el, name, value, namespace) {\n    this.delegate.setAttribute(el, name, value, namespace);\n  }\n  removeAttribute(el, name, namespace) {\n    this.delegate.removeAttribute(el, name, namespace);\n  }\n  addClass(el, name) {\n    this.delegate.addClass(el, name);\n  }\n  removeClass(el, name) {\n    this.delegate.removeClass(el, name);\n  }\n  setStyle(el, style, value, flags) {\n    this.delegate.setStyle(el, style, value, flags);\n  }\n  removeStyle(el, style, flags) {\n    this.delegate.removeStyle(el, style, flags);\n  }\n  setProperty(el, name, value) {\n    // We need to keep track of animation properties set on default renderer\n    // So we can also set them also on the animation renderer\n    if (this.shouldReplay(name)) {\n      this.replay.push(renderer => renderer.setProperty(el, name, value));\n    }\n    this.delegate.setProperty(el, name, value);\n  }\n  setValue(node, value) {\n    this.delegate.setValue(node, value);\n  }\n  listen(target, eventName, callback, options) {\n    // We need to keep track of animation events registred by the default renderer\n    // So we can also register them against the animation renderer\n    if (this.shouldReplay(eventName)) {\n      this.replay.push(renderer => renderer.listen(target, eventName, callback, options));\n    }\n    return this.delegate.listen(target, eventName, callback, options);\n  }\n  shouldReplay(propOrEventName) {\n    //`null` indicates that we no longer need to collect events and properties\n    return this.replay !== null && propOrEventName.startsWith(ANIMATION_PREFIX);\n  }\n}\n/**\n * Provides a custom scheduler function for the async loading of the animation package.\n *\n * Private token for investigation purposes\n */\nconst ɵASYNC_ANIMATION_LOADING_SCHEDULER_FN = new InjectionToken(ngDevMode ? 'async_animation_loading_scheduler_fn' : '');\n\n/**\n * Returns the set of dependency-injection providers\n * to enable animations in an application. See [animations guide](guide/animations)\n * to learn more about animations in Angular.\n *\n * When you use this function instead of the eager `provideAnimations()`, animations won't be\n * rendered until the renderer is loaded.\n *\n * @usageNotes\n *\n * The function is useful when you want to enable animations in an application\n * bootstrapped using the `bootstrapApplication` function. In this scenario there\n * is no need to import the `BrowserAnimationsModule` NgModule at all, just add\n * providers returned by this function to the `providers` list as show below.\n *\n * ```ts\n * bootstrapApplication(RootComponent, {\n *   providers: [\n *     provideAnimationsAsync()\n *   ]\n * });\n * ```\n *\n * @param type pass `'noop'` as argument to disable animations.\n *\n * @publicApi\n */\nfunction provideAnimationsAsync(type = 'animations') {\n  _performanceMarkFeature('NgAsyncAnimations');\n  // Animations don't work on the server so we switch them over to no-op automatically.\n  if (typeof ngServerMode !== 'undefined' && ngServerMode) {\n    type = 'noop';\n  }\n  return makeEnvironmentProviders([{\n    provide: RendererFactory2,\n    useFactory: (doc, renderer, zone) => {\n      return new AsyncAnimationRendererFactory(doc, renderer, zone, type);\n    },\n    deps: [DOCUMENT, DomRendererFactory2, NgZone]\n  }, {\n    provide: ANIMATION_MODULE_TYPE,\n    useValue: type === 'noop' ? 'NoopAnimations' : 'BrowserAnimations'\n  }]);\n}\nexport { provideAnimationsAsync, ɵASYNC_ANIMATION_LOADING_SCHEDULER_FN, AsyncAnimationRendererFactory as ɵAsyncAnimationRendererFactory };\n", "import { Injectable } from '@angular/core';\nimport { CanActivate, Router, ActivatedRouteSnapshot, RouterStateSnapshot } from '@angular/router';\nimport { Observable } from 'rxjs';\nimport { map, take } from 'rxjs/operators';\nimport { AuthService } from '../services/auth.service';\n\n@Injectable({\n  providedIn: 'root'\n})\nexport class FournisseurGuard implements CanActivate {\n\n  constructor(\n    private authService: AuthService,\n    private router: Router\n  ) {}\n\n  canActivate(\n    route: ActivatedRouteSnapshot,\n    state: RouterStateSnapshot\n  ): Observable<boolean> | Promise<boolean> | boolean {\n    \n    return this.authService.isAuthenticated$.pipe(\n      take(1),\n      map(isAuthenticated => {\n        if (isAuthenticated && this.authService.isFournisseur()) {\n          return true;\n        } else if (isAuthenticated && this.authService.isAdmin()) {\n          // Rediriger les admins vers leur dashboard\n          this.router.navigate(['/admin']);\n          return false;\n        } else {\n          // Rediriger les non-authentifiés vers login\n          this.router.navigate(['/login'], { \n            queryParams: { returnUrl: state.url } \n          });\n          return false;\n        }\n      })\n    );\n  }\n}\n", "import { Component } from '@angular/core';\nimport { CommonModule } from '@angular/common';\n\n@Component({\n  selector: 'app-test-images',\n  standalone: true,\n  imports: [CommonModule],\n  template: `\n    <div style=\"padding: 2rem;\">\n      <h1>🖼️ Test d'affichage des images</h1>\n      \n      <div style=\"margin: 2rem 0;\">\n        <h2>📸 Images de produits depuis le backend :</h2>\n        <div style=\"display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 1rem;\">\n          <div *ngFor=\"let image of testImages\" style=\"border: 1px solid #ccc; padding: 1rem; border-radius: 8px;\">\n            <h3>{{ image.name }}</h3>\n            <img \n              [src]=\"image.url\" \n              [alt]=\"image.name\"\n              style=\"width: 100%; height: 150px; object-fit: cover; border-radius: 4px;\"\n              (load)=\"onImageLoad(image.name)\"\n              (error)=\"onImageError(image.name, $event)\">\n            <p style=\"font-size: 0.8rem; color: #666; margin-top: 0.5rem;\">{{ image.url }}</p>\n          </div>\n        </div>\n      </div>\n\n      <div style=\"margin: 2rem 0;\">\n        <h2>🏪 Logo fournisseur par défaut :</h2>\n        <img \n          src=\"assets/logos/suppliers/default-supplier.svg\" \n          alt=\"Logo par défaut\"\n          style=\"width: 100px; height: 100px; border: 1px solid #ccc; border-radius: 8px;\"\n          (load)=\"onImageLoad('Logo par défaut')\"\n          (error)=\"onImageError('Logo par défaut', $event)\">\n      </div>\n\n      <div style=\"margin: 2rem 0;\">\n        <h2>📊 Résultats des tests :</h2>\n        <div style=\"background: #f5f5f5; padding: 1rem; border-radius: 8px;\">\n          <div *ngFor=\"let result of testResults\" \n               [style.color]=\"result.success ? 'green' : 'red'\"\n               style=\"margin: 0.5rem 0;\">\n            {{ result.success ? '✅' : '❌' }} {{ result.name }}: {{ result.message }}\n          </div>\n        </div>\n      </div>\n    </div>\n  `\n})\nexport class TestImagesComponent {\n  testImages = [\n    { name: 'Image 1', url: '/uploads/2d8a2c0a-6b33-486e-bc3f-de8b4fc4e917.jpg' },\n    { name: 'Image 2', url: '/uploads/3061901f-1db5-48b9-92d3-6dbd1aa85c21.jpg' },\n    { name: 'Image 3', url: '/uploads/3b318ca1-8e52-4965-a93e-86e40b2de5d3.jpg' },\n    { name: 'Image 4', url: '/uploads/4581739a-ed87-4799-a98b-1ced05354f90.jpg' },\n    { name: 'Image 5', url: '/uploads/4c82526b-b29c-4618-bdd1-25ea82c7251f.jpg' }\n  ];\n\n  testResults: Array<{name: string, success: boolean, message: string}> = [];\n\n  onImageLoad(imageName: string): void {\n    console.log(`✅ Image chargée avec succès: ${imageName}`);\n    this.testResults.push({\n      name: imageName,\n      success: true,\n      message: 'Chargée avec succès'\n    });\n  }\n\n  onImageError(imageName: string, event: any): void {\n    console.error(`❌ Erreur de chargement: ${imageName}`, event);\n    this.testResults.push({\n      name: imageName,\n      success: false,\n      message: `Erreur: ${event.target.src}`\n    });\n  }\n}\n", "import { Routes } from '@angular/router';\r\nimport { AuthGuard } from './guards/auth.guard';\r\nimport { AdminAuthGuard } from './guards/admin-auth.guard';\r\nimport { FournisseurGuard } from './guards/fournisseur.guard';\r\nimport { TestImagesComponent } from './test-images.component';\r\n\r\nexport const routes: Routes = [\r\n  {\r\n    path: 'login',\r\n    loadComponent: () => import('./components/auth/login/login.component').then(m => m.LoginComponent)\r\n  },\r\n  {\r\n    path: 'register',\r\n    loadComponent: () => import('./components/auth/register/register.component').then(m => m.RegisterComponent)\r\n  },\r\n  {\r\n    path: 'adminOptiLet',\r\n    loadComponent: () => import('./components/admin/auth/admin-login/admin-login.component').then(m => m.AdminLoginComponent),\r\n    title: 'Connexion Administrateur - OptiLet'\r\n  },\r\n  {\r\n    path: 'test-images',\r\n    component: TestImagesComponent,\r\n    title: 'Test Images'\r\n  },\r\n  {\r\n    path: 'admin',\r\n    loadChildren: () => import('./routes/admin.routes').then(m => m.adminRoutes)\r\n  },\r\n\r\n\r\n  // Composants Angular 19 - Temporairement commentés pour résoudre les erreurs\r\n  // {\r\n  //   path: 'dashboard-ng19',\r\n  //   loadComponent: () => import('./components/dashboard/dashboard-ng19.component').then(m => m.DashboardNg19Component),\r\n  //   title: 'Dashboard Angular 19',\r\n  //   canActivate: [AuthGuard]\r\n  // },\r\n  // {\r\n  //   path: 'login-ng19',\r\n  //   loadComponent: () => import('./components/auth/login-ng19/login-ng19.component').then(m => m.LoginNg19Component),\r\n  //   title: 'Connexion Angular 19'\r\n  // },\r\n  // {\r\n  //   path: 'angular19-nav',\r\n  //   loadComponent: () => import('./components/navigation/angular19-nav.component').then(m => m.Angular19NavComponent),\r\n  //   title: 'Navigation Angular 19'\r\n  // },\r\n  // Dashboard du fournisseur avec layout et sidebar\r\n  {\r\n    path: 'dashboard',\r\n    loadComponent: () => import('./components/layout/dashboard-layout/dashboard-layout.component').then(m => m.DashboardLayoutComponent),\r\n    canActivate: [FournisseurGuard],\r\n    children: [\r\n      {\r\n        path: '',\r\n        redirectTo: 'overview',\r\n        pathMatch: 'full'\r\n      },\r\n      {\r\n        path: 'overview',\r\n        loadComponent: () => import('./components/dashboard/dashboard.component').then(m => m.DashboardComponent)\r\n      },\r\n      {\r\n        path: 'products',\r\n        loadComponent: () => import('./components/products/products.component').then(m => m.ProductsComponent),\r\n        title: 'Gestion des Produits'\r\n      },\r\n      {\r\n        path: 'orders',\r\n        loadComponent: () => import('./components/orders/orders.component').then(m => m.OrdersComponent),\r\n        title: 'Gestion des Commandes'\r\n      },\r\n      {\r\n        path: 'referentiels',\r\n        loadComponent: () => import('./components/referentiels/referentiels.component').then(m => m.ReferentielsComponent),\r\n        title: 'Gestion des Référentiels'\r\n      },\r\n      {\r\n        path: 'profil',\r\n        loadComponent: () => import('./components/profile/profile.component').then(m => m.ProfileComponent),\r\n        title: 'Mon Profil'\r\n      },\r\n      {\r\n        path: 'notifications',\r\n        loadComponent: () => import('./notifications/notifications.component').then(m => m.NotificationsComponent),\r\n        title: 'Mes Notifications'\r\n      },\r\n      {\r\n        path: 'avis',\r\n        loadComponent: () => import('./components/fournisseur/avis-fournisseur/avis-fournisseur.component').then(m => m.AvisFournisseurComponent),\r\n        title: 'Mes Avis Clients'\r\n      },\r\n      {\r\n        path: 'demandes-categories',\r\n        loadComponent: () => import('./components/fournisseur/demande-categorie/demande-categorie.component').then(m => m.DemandeCategorieComponent),\r\n        title: 'Demandes de Catégories'\r\n      }\r\n    ]\r\n  },\r\n  {\r\n    path: '',\r\n    redirectTo: '/login',\r\n    pathMatch: 'full'\r\n  },\r\n  {\r\n    path: 'home',\r\n    loadComponent: () => import('./components/home/<USER>').then(m => m.HomeComponent),\r\n    title: 'Accueil - Plateforme Fournisseur Angular 19'\r\n  },\r\n  {\r\n    path: '**',\r\n    redirectTo: '/login'\r\n  }\r\n];\r\n", "import { ApplicationConfig, provideZoneChangeDetection, inject } from '@angular/core';\r\nimport { provideRouter, withEnabledBlockingInitialNavigation, withInMemoryScrolling, withRouterConfig } from '@angular/router';\r\nimport { provideHttpClient, withInterceptors, withF<PERSON>ch, HTTP_INTERCEPTORS, HttpInterceptorFn } from '@angular/common/http';\r\nimport { provideAnimationsAsync } from '@angular/platform-browser/animations/async';\r\n\r\nimport { routes } from './app.routes';\r\nimport { HttpInterceptorService } from './services';\r\nimport { HttpErrorInterceptor } from './interceptors/http-error.interceptor';\r\nimport { CorsInterceptor } from './interceptors/cors.interceptor';\r\nimport { AuthService } from './services/auth.service';\r\nimport { AdminAuthService } from './services/admin-auth.service';\r\n\r\n// Intercepteur fonctionnel pour l'authentification (Angular 19)\r\nconst authInterceptor: HttpInterceptorFn = (req, next) => {\r\n  const authService = inject(AuthService);\r\n  const adminAuthService = inject(AdminAuthService);\r\n\r\n  // Vérifier d'abord si c'est une requête admin\r\n  const isAdminRequest = req.url.includes('/admin/') || req.url.includes('/Admin/') || req.url.includes('/DemandesCategories');\r\n\r\n  let token: string | null = null;\r\n\r\n  if (isAdminRequest) {\r\n    // Pour les requêtes admin, utiliser le token admin\r\n    token = localStorage.getItem('admin_access_token');\r\n    console.log('🔍 Requête Admin détectée:', {\r\n      url: req.url,\r\n      hasAdminToken: !!token,\r\n      tokenPreview: token ? token.substring(0, 20) + '...' : 'Aucun'\r\n    });\r\n  } else {\r\n    // Pour les autres requêtes, utiliser le token normal\r\n    token = authService.getToken();\r\n    console.log('🔍 Requête normale:', {\r\n      url: req.url,\r\n      hasToken: !!token,\r\n      tokenPreview: token ? token.substring(0, 20) + '...' : 'Aucun'\r\n    });\r\n  }\r\n\r\n  if (token) {\r\n    const authReq = req.clone({\r\n      headers: req.headers.set('Authorization', `Bearer ${token}`)\r\n    });\r\n    console.log('✅ Token ajouté à la requête');\r\n    return next(authReq);\r\n  }\r\n\r\n  console.warn('⚠️ Aucun token disponible pour:', req.url);\r\n  return next(req);\r\n};\r\n\r\nexport const appConfig: ApplicationConfig = {\r\n  providers: [\r\n    // Angular 19: Nouvelle détection de changement optimisée\r\n    provideZoneChangeDetection({\r\n      eventCoalescing: true,\r\n      runCoalescing: true\r\n    }),\r\n\r\n    // Angular 19: Router amélioré avec nouvelles options\r\n    provideRouter(\r\n      routes,\r\n      withEnabledBlockingInitialNavigation(),\r\n      withInMemoryScrolling({\r\n        scrollPositionRestoration: 'top',\r\n        anchorScrolling: 'enabled'\r\n      }),\r\n      withRouterConfig({\r\n        onSameUrlNavigation: 'reload'\r\n      })\r\n    ),\r\n\r\n    // Angular 19: HTTP Client avec fetch API et intercepteur fonctionnel\r\n    provideHttpClient(\r\n      withFetch(), // Nouvelle API fetch native\r\n      withInterceptors([authInterceptor]) // Intercepteur fonctionnel pour l'authentification\r\n    ),\r\n\r\n    // Angular 19: Animations asynchrones pour de meilleures performances\r\n    provideAnimationsAsync(),\r\n\r\n    // Les intercepteurs legacy sont remplacés par l'intercepteur fonctionnel authInterceptor\r\n  ]\r\n};\r\n", "import { Component } from '@angular/core';\r\nimport { RouterOutlet } from '@angular/router';\r\n\r\n@Component({\r\n  selector: 'app-root',\r\n  imports: [RouterOutlet],\r\n  templateUrl: './app.component.html',\r\n  styleUrl: './app.component.css'\r\n})\r\nexport class AppComponent {\r\n  title = 'pfe-fournisseur';\r\n}\r\n", "<router-outlet></router-outlet>\r\n", "import { bootstrapApplication } from '@angular/platform-browser';\r\nimport { appConfig } from './app/app.config';\r\nimport { AppComponent } from './app/app.component';\r\n\r\nbootstrapApplication(AppComponent, appConfig)\r\n  .catch((err) => console.error(err));\r\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAUA,IAAM,mBAAmB;AACzB,IAAM,gCAAN,MAAM,+BAA8B;AAAA,EAClC;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA,0BAA0B;AAAA,EAC1B,YAAY;AAAA,EACZ,WAAW,OAAO,QAAQ;AAAA,EAC1B,qBAAqB,OAAO,4CAAuC;AAAA,IACjE,UAAU;AAAA,EACZ,CAAC;AAAA,EACD;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,YAAY,KAAK,UAAU,MAAM,eAAe,YAAY;AAC1D,SAAK,MAAM;AACX,SAAK,WAAW;AAChB,SAAK,OAAO;AACZ,SAAK,gBAAgB;AACrB,SAAK,aAAa;AAAA,EACpB;AAAA;AAAA,EAEA,cAAc;AAOZ,SAAK,SAAS,MAAM;AAAA,EACtB;AAAA;AAAA;AAAA;AAAA,EAIA,WAAW;AAIT,UAAM,SAAS,MAAM,KAAK,cAAc,OAAO,qBAA6B,EAAE,KAAK,OAAK,CAAC;AACzF,QAAI;AACJ,QAAI,KAAK,oBAAoB;AAC3B,0BAAoB,KAAK,mBAAmB,MAAM;AAAA,IACpD,OAAO;AACL,0BAAoB,OAAO;AAAA,IAC7B;AACA,WAAO,kBAAkB,MAAM,OAAK;AAClC,YAAM,IAAI,aAAc,OAAuE,OAAO,cAAc,eAAe,cAAc,yLAAmM;AAAA,IACtV,CAAC,EAAE,KAAK,CAAC;AAAA,MACP;AAAA,MACA;AAAA,IACF,MAAM;AAGJ,WAAK,UAAU,mBAAc,KAAK,eAAe,KAAK,GAAG;AACzD,YAAM,kBAAkB,IAAI,+BAA0B,KAAK,UAAU,KAAK,SAAS,KAAK,IAAI;AAC5F,WAAK,WAAW;AAChB,aAAO;AAAA,IACT,CAAC;AAAA,EACH;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASA,eAAe,aAAa,cAAc;AACxC,UAAM,WAAW,KAAK,SAAS,eAAe,aAAa,YAAY;AACvE,QAAI,SAAS,eAAU,GAAuC;AAE5D,aAAO;AAAA,IACT;AAEA,QAAI,OAAO,SAAS,0BAA0B,WAAW;AACvD,eAAS,wBAAwB;AAAA,IACnC;AAEA,UAAM,kBAAkB,IAAI,0BAA0B,QAAQ;AAG9D,QAAI,cAAc,OAAO,WAAW,KAAK,CAAC,KAAK,yBAAyB;AACtE,WAAK,0BAA0B,KAAK,SAAS;AAAA,IAC/C;AACA,SAAK,yBAAyB,KAAK,8BAA4B;AAC7D,YAAM,oBAAoB,yBAAyB,eAAe,aAAa,YAAY;AAC3F,sBAAgB,IAAI,iBAAiB;AACrC,WAAK,cAAc,KAAK,SAAS,IAAI,0BAA2B,MAAM;AAAA,QACpE,UAAU;AAAA,MACZ,CAAC;AACD,WAAK,WAAW;AAAA,QAAO;AAAA;AAAA,MAAiD;AAAA,IAC1E,CAAC,EAAE,MAAM,OAAK;AAEZ,sBAAgB,IAAI,QAAQ;AAAA,IAC9B,CAAC;AACD,WAAO;AAAA,EACT;AAAA,EACA,QAAQ;AACN,SAAK,SAAS,QAAQ;AAAA,EACxB;AAAA,EACA,MAAM;AACJ,SAAK,SAAS,MAAM;AAAA,EACtB;AAAA,EACA,oBAAoB;AAClB,WAAO,KAAK,SAAS,oBAAoB,KAAK,QAAQ,QAAQ;AAAA,EAChE;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,kBAAkB,aAAa;AAE7B,SAAK,SAAS,MAAM;AACpB,SAAK,SAAS,oBAAoB,WAAW;AAAA,EAC/C;AAAA,EACA,OAAO,YAAO,SAAS,sCAAsC,mBAAmB;AAC9E,IAAG,2BAAiB;AAAA,EACtB;AAAA,EACA,OAAO,aAAuB,gBAAG,6BAAmB;AAAA,IAClD,OAAO;AAAA,IACP,SAAS,+BAA8B;AAAA,EACzC,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,+BAA+B,CAAC;AAAA,IACtG,MAAM;AAAA,EACR,CAAC,GAAG,MAAM,CAAC;AAAA,IACT,MAAM;AAAA,EACR,GAAG;AAAA,IACD,MAAS;AAAA,EACX,GAAG;AAAA,IACD,MAAS;AAAA,EACX,GAAG;AAAA,IACD,MAAM;AAAA,EACR,GAAG;AAAA,IACD,MAAM;AAAA,EACR,CAAC,GAAG,IAAI;AACV,GAAG;AAKH,IAAM,4BAAN,MAAgC;AAAA,EAC9B;AAAA;AAAA,EAEA,SAAS,CAAC;AAAA,EACV,aAAQ;AAAA,EACR,YAAY,UAAU;AACpB,SAAK,WAAW;AAAA,EAClB;AAAA,EACA,IAAI,MAAM;AACR,SAAK,WAAW;AAChB,QAAI,KAAK,WAAW,MAAM;AAGxB,iBAAW,MAAM,KAAK,QAAQ;AAC5B,WAAG,IAAI;AAAA,MACT;AAGA,WAAK,SAAS;AAAA,IAChB;AAAA,EACF;AAAA,EACA,IAAI,OAAO;AACT,WAAO,KAAK,SAAS;AAAA,EACvB;AAAA,EACA,UAAU;AACR,SAAK,SAAS;AACd,SAAK,SAAS,QAAQ;AAAA,EACxB;AAAA,EACA,cAAc,MAAM,WAAW;AAC7B,WAAO,KAAK,SAAS,cAAc,MAAM,SAAS;AAAA,EACpD;AAAA,EACA,cAAc,OAAO;AACnB,WAAO,KAAK,SAAS,cAAc,KAAK;AAAA,EAC1C;AAAA,EACA,WAAW,OAAO;AAChB,WAAO,KAAK,SAAS,WAAW,KAAK;AAAA,EACvC;AAAA,EACA,IAAI,cAAc;AAChB,WAAO,KAAK,SAAS;AAAA,EACvB;AAAA,EACA,YAAY,QAAQ,UAAU;AAC5B,SAAK,SAAS,YAAY,QAAQ,QAAQ;AAAA,EAC5C;AAAA,EACA,aAAa,QAAQ,UAAU,UAAU,QAAQ;AAC/C,SAAK,SAAS,aAAa,QAAQ,UAAU,UAAU,MAAM;AAAA,EAC/D;AAAA,EACA,YAAY,QAAQ,UAAU,eAAe;AAC3C,SAAK,SAAS,YAAY,QAAQ,UAAU,aAAa;AAAA,EAC3D;AAAA,EACA,kBAAkB,gBAAgB,iBAAiB;AACjD,WAAO,KAAK,SAAS,kBAAkB,gBAAgB,eAAe;AAAA,EACxE;AAAA,EACA,WAAW,MAAM;AACf,WAAO,KAAK,SAAS,WAAW,IAAI;AAAA,EACtC;AAAA,EACA,YAAY,MAAM;AAChB,WAAO,KAAK,SAAS,YAAY,IAAI;AAAA,EACvC;AAAA,EACA,aAAa,IAAI,MAAM,OAAO,WAAW;AACvC,SAAK,SAAS,aAAa,IAAI,MAAM,OAAO,SAAS;AAAA,EACvD;AAAA,EACA,gBAAgB,IAAI,MAAM,WAAW;AACnC,SAAK,SAAS,gBAAgB,IAAI,MAAM,SAAS;AAAA,EACnD;AAAA,EACA,SAAS,IAAI,MAAM;AACjB,SAAK,SAAS,SAAS,IAAI,IAAI;AAAA,EACjC;AAAA,EACA,YAAY,IAAI,MAAM;AACpB,SAAK,SAAS,YAAY,IAAI,IAAI;AAAA,EACpC;AAAA,EACA,SAAS,IAAI,OAAO,OAAO,OAAO;AAChC,SAAK,SAAS,SAAS,IAAI,OAAO,OAAO,KAAK;AAAA,EAChD;AAAA,EACA,YAAY,IAAI,OAAO,OAAO;AAC5B,SAAK,SAAS,YAAY,IAAI,OAAO,KAAK;AAAA,EAC5C;AAAA,EACA,YAAY,IAAI,MAAM,OAAO;AAG3B,QAAI,KAAK,aAAa,IAAI,GAAG;AAC3B,WAAK,OAAO,KAAK,cAAY,SAAS,YAAY,IAAI,MAAM,KAAK,CAAC;AAAA,IACpE;AACA,SAAK,SAAS,YAAY,IAAI,MAAM,KAAK;AAAA,EAC3C;AAAA,EACA,SAAS,MAAM,OAAO;AACpB,SAAK,SAAS,SAAS,MAAM,KAAK;AAAA,EACpC;AAAA,EACA,OAAO,QAAQ,WAAW,UAAU,SAAS;AAG3C,QAAI,KAAK,aAAa,SAAS,GAAG;AAChC,WAAK,OAAO,KAAK,cAAY,SAAS,OAAO,QAAQ,WAAW,UAAU,OAAO,CAAC;AAAA,IACpF;AACA,WAAO,KAAK,SAAS,OAAO,QAAQ,WAAW,UAAU,OAAO;AAAA,EAClE;AAAA,EACA,aAAa,iBAAiB;AAE5B,WAAO,KAAK,WAAW,QAAQ,gBAAgB,WAAW,gBAAgB;AAAA,EAC5E;AACF;AAMA,IAAM,6CAAwC,IAAI,eAAe,YAAY,yCAAyC,EAAE;AA6BxH,SAAS,uBAAuB,OAAO,cAAc;AACnD,yBAAwB,mBAAmB;AAE3C,MAA2C,OAAc;AACvD,WAAO;AAAA,EACT;AACA,SAAO,yBAAyB,CAAC;AAAA,IAC/B,SAAS;AAAA,IACT,YAAY,CAAC,KAAK,UAAU,SAAS;AACnC,aAAO,IAAI,8BAA8B,KAAK,UAAU,MAAM,IAAI;AAAA,IACpE;AAAA,IACA,MAAM,CAAC,UAAU,qBAAqB,MAAM;AAAA,EAC9C,GAAG;AAAA,IACD,SAAS;AAAA,IACT,UAAU,SAAS,SAAS,mBAAmB;AAAA,EACjD,CAAC,CAAC;AACJ;;;ACzSM,IAAO,mBAAP,MAAO,kBAAgB;EAGjB;EACA;EAFV,YACU,aACA,QAAc;AADd,SAAA,cAAA;AACA,SAAA,SAAA;EACP;EAEH,YACE,OACA,OAA0B;AAG1B,WAAO,KAAK,YAAY,iBAAiB,KACvC,KAAK,CAAC,GACN,IAAI,qBAAkB;AACpB,UAAI,mBAAmB,KAAK,YAAY,cAAa,GAAI;AACvD,eAAO;MACT,WAAW,mBAAmB,KAAK,YAAY,QAAO,GAAI;AAExD,aAAK,OAAO,SAAS,CAAC,QAAQ,CAAC;AAC/B,eAAO;MACT,OAAO;AAEL,aAAK,OAAO,SAAS,CAAC,QAAQ,GAAG;UAC/B,aAAa,EAAE,WAAW,MAAM,IAAG;SACpC;AACD,eAAO;MACT;IACF,CAAC,CAAC;EAEN;;qCA9BW,mBAAgB,mBAAA,WAAA,GAAA,mBAAA,MAAA,CAAA;EAAA;4EAAhB,mBAAgB,SAAhB,kBAAgB,WAAA,YAFf,OAAM,CAAA;;;sEAEP,kBAAgB,CAAA;UAH5B;WAAW;MACV,YAAY;KACb;;;;;;;;ACMS,IAAA,yBAAA,GAAA,OAAA,CAAA,EAAyG,GAAA,IAAA;AACnG,IAAA,iBAAA,CAAA;AAAgB,IAAA,uBAAA;AACpB,IAAA,yBAAA,GAAA,OAAA,CAAA;AAIE,IAAA,qBAAA,QAAA,SAAA,yDAAA;AAAA,YAAA,WAAA,wBAAA,GAAA,EAAA;AAAA,YAAA,SAAA,wBAAA;AAAA,aAAA,sBAAQ,OAAA,YAAA,SAAA,IAAA,CAAuB;IAAA,CAAA,EAAC,SAAA,SAAA,wDAAA,QAAA;AAAA,YAAA,WAAA,wBAAA,GAAA,EAAA;AAAA,YAAA,SAAA,wBAAA;AAAA,aAAA,sBACvB,OAAA,aAAA,SAAA,MAAA,MAAA,CAAgC;IAAA,CAAA;AAL3C,IAAA,uBAAA;AAMA,IAAA,yBAAA,GAAA,KAAA,CAAA;AAA+D,IAAA,iBAAA,CAAA;AAAe,IAAA,uBAAA,EAAI;;;;AAP9E,IAAA,oBAAA,CAAA;AAAA,IAAA,4BAAA,SAAA,IAAA;AAEF,IAAA,oBAAA;AAAA,IAAA,qBAAA,OAAA,SAAA,KAAA,uBAAA,EAAiB,OAAA,SAAA,IAAA;AAK4C,IAAA,oBAAA,CAAA;AAAA,IAAA,4BAAA,SAAA,GAAA;;;;;AAkBjE,IAAA,yBAAA,GAAA,OAAA,EAAA;AAGE,IAAA,iBAAA,CAAA;AACF,IAAA,uBAAA;;;;AAHK,IAAA,sBAAA,SAAA,UAAA,UAAA,UAAA,KAAA;AAEH,IAAA,oBAAA;AAAA,IAAA,6BAAA,KAAA,UAAA,UAAA,WAAA,UAAA,KAAA,UAAA,MAAA,MAAA,UAAA,SAAA,GAAA;;;AAON,IAAO,sBAAP,MAAO,qBAAmB;EAC9B,aAAa;IACX,EAAE,MAAM,WAAW,KAAK,oDAAmD;IAC3E,EAAE,MAAM,WAAW,KAAK,oDAAmD;IAC3E,EAAE,MAAM,WAAW,KAAK,oDAAmD;IAC3E,EAAE,MAAM,WAAW,KAAK,oDAAmD;IAC3E,EAAE,MAAM,WAAW,KAAK,oDAAmD;;EAG7E,cAAwE,CAAA;EAExE,YAAY,WAAiB;AAC3B,YAAQ,IAAI,2CAAgC,SAAS,EAAE;AACvD,SAAK,YAAY,KAAK;MACpB,MAAM;MACN,SAAS;MACT,SAAS;KACV;EACH;EAEA,aAAa,WAAmB,OAAU;AACxC,YAAQ,MAAM,gCAA2B,SAAS,IAAI,KAAK;AAC3D,SAAK,YAAY,KAAK;MACpB,MAAM;MACN,SAAS;MACT,SAAS,WAAW,MAAM,OAAO,GAAG;KACrC;EACH;;qCA3BW,sBAAmB;EAAA;yEAAnB,sBAAmB,WAAA,CAAA,CAAA,iBAAA,CAAA,GAAA,OAAA,IAAA,MAAA,GAAA,QAAA,CAAA,CAAA,GAAA,WAAA,MAAA,GAAA,CAAA,GAAA,UAAA,QAAA,GAAA,CAAA,GAAA,WAAA,QAAA,yBAAA,wCAAA,OAAA,MAAA,GAAA,CAAA,SAAA,8DAAA,GAAA,SAAA,SAAA,GAAA,CAAA,OAAA,+CAAA,OAAA,sBAAA,GAAA,SAAA,SAAA,UAAA,SAAA,UAAA,kBAAA,iBAAA,OAAA,GAAA,QAAA,OAAA,GAAA,CAAA,GAAA,cAAA,WAAA,WAAA,QAAA,iBAAA,KAAA,GAAA,CAAA,SAAA,qBAAA,GAAA,SAAA,GAAA,SAAA,SAAA,GAAA,CAAA,GAAA,UAAA,kBAAA,WAAA,QAAA,iBAAA,KAAA,GAAA,CAAA,GAAA,SAAA,QAAA,UAAA,SAAA,cAAA,SAAA,iBAAA,OAAA,GAAA,QAAA,SAAA,OAAA,KAAA,GAAA,CAAA,GAAA,aAAA,UAAA,SAAA,QAAA,cAAA,QAAA,GAAA,CAAA,GAAA,UAAA,UAAA,CAAA,GAAA,UAAA,SAAA,6BAAA,IAAA,KAAA;AAAA,QAAA,KAAA,GAAA;AA1C5B,MAAA,yBAAA,GAAA,OAAA,CAAA,EAA4B,GAAA,IAAA;AACtB,MAAA,iBAAA,GAAA,6CAAA;AAA+B,MAAA,uBAAA;AAEnC,MAAA,yBAAA,GAAA,OAAA,CAAA,EAA6B,GAAA,IAAA;AACvB,MAAA,iBAAA,GAAA,kDAAA;AAAyC,MAAA,uBAAA;AAC7C,MAAA,yBAAA,GAAA,OAAA,CAAA;AACE,MAAA,qBAAA,GAAA,oCAAA,GAAA,GAAA,OAAA,CAAA;AAUF,MAAA,uBAAA,EAAM;AAGR,MAAA,yBAAA,GAAA,OAAA,CAAA,EAA6B,GAAA,IAAA;AACvB,MAAA,iBAAA,IAAA,4CAAA;AAAgC,MAAA,uBAAA;AACpC,MAAA,yBAAA,IAAA,OAAA,CAAA;AAIE,MAAA,qBAAA,QAAA,SAAA,oDAAA;AAAA,eAAQ,IAAA,YAAY,oBAAiB;MAAC,CAAA,EAAC,SAAA,SAAA,mDAAA,QAAA;AAAA,eAC9B,IAAA,aAAa,sBAAiB,MAAA;MAAS,CAAA;AALlD,MAAA,uBAAA,EAKoD;AAGtD,MAAA,yBAAA,IAAA,OAAA,CAAA,EAA6B,IAAA,IAAA;AACvB,MAAA,iBAAA,IAAA,oCAAA;AAAwB,MAAA,uBAAA;AAC5B,MAAA,yBAAA,IAAA,OAAA,CAAA;AACE,MAAA,qBAAA,IAAA,qCAAA,GAAA,GAAA,OAAA,CAAA;AAKF,MAAA,uBAAA,EAAM,EACF;;;AAhCqB,MAAA,oBAAA,CAAA;AAAA,MAAA,qBAAA,WAAA,IAAA,UAAA;AA0BC,MAAA,oBAAA,CAAA;AAAA,MAAA,qBAAA,WAAA,IAAA,WAAA;;oBAlCtB,cAAY,OAAA,GAAA,eAAA,EAAA,CAAA;;;sEA4CX,qBAAmB,CAAA;UA/C/B;WAAU;MACT,UAAU;MACV,YAAY;MACZ,SAAS,CAAC,YAAY;MACtB,UAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;KA0CX;;;;6EACY,qBAAmB,EAAA,WAAA,uBAAA,UAAA,oCAAA,YAAA,GAAA,CAAA;AAAA,GAAA;;;AC5CzB,IAAM,SAAiB;EAC5B;IACE,MAAM;IACN,eAAe,MAAM,OAAO,qBAAyC,EAAE,KAAK,OAAK,EAAE,cAAc;;EAEnG;IACE,MAAM;IACN,eAAe,MAAM,OAAO,qBAA+C,EAAE,KAAK,OAAK,EAAE,iBAAiB;;EAE5G;IACE,MAAM;IACN,eAAe,MAAM,OAAO,qBAA2D,EAAE,KAAK,OAAK,EAAE,mBAAmB;IACxH,OAAO;;EAET;IACE,MAAM;IACN,WAAW;IACX,OAAO;;EAET;IACE,MAAM;IACN,cAAc,MAAM,OAAO,qBAAuB,EAAE,KAAK,OAAK,EAAE,WAAW;;;;;;;;;;;;;;;;;;;;EAsB7E;IACE,MAAM;IACN,eAAe,MAAM,OAAO,qBAAiE,EAAE,KAAK,OAAK,EAAE,wBAAwB;IACnI,aAAa,CAAC,gBAAgB;IAC9B,UAAU;MACR;QACE,MAAM;QACN,YAAY;QACZ,WAAW;;MAEb;QACE,MAAM;QACN,eAAe,MAAM,OAAO,qBAA4C,EAAE,KAAK,OAAK,EAAE,kBAAkB;;MAE1G;QACE,MAAM;QACN,eAAe,MAAM,OAAO,qBAA0C,EAAE,KAAK,OAAK,EAAE,iBAAiB;QACrG,OAAO;;MAET;QACE,MAAM;QACN,eAAe,MAAM,OAAO,qBAAsC,EAAE,KAAK,OAAK,EAAE,eAAe;QAC/F,OAAO;;MAET;QACE,MAAM;QACN,eAAe,MAAM,OAAO,qBAAkD,EAAE,KAAK,OAAK,EAAE,qBAAqB;QACjH,OAAO;;MAET;QACE,MAAM;QACN,eAAe,MAAM,OAAO,qBAAwC,EAAE,KAAK,OAAK,EAAE,gBAAgB;QAClG,OAAO;;MAET;QACE,MAAM;QACN,eAAe,MAAM,OAAO,qBAAyC,EAAE,KAAK,OAAK,EAAE,sBAAsB;QACzG,OAAO;;MAET;QACE,MAAM;QACN,eAAe,MAAM,OAAO,qBAAsE,EAAE,KAAK,OAAK,EAAE,wBAAwB;QACxI,OAAO;;MAET;QACE,MAAM;QACN,eAAe,MAAM,OAAO,qBAAwE,EAAE,KAAK,OAAK,EAAE,yBAAyB;QAC3I,OAAO;;;;EAIb;IACE,MAAM;IACN,YAAY;IACZ,WAAW;;EAEb;IACE,MAAM;IACN,eAAe,MAAM,OAAO,qBAAkC,EAAE,KAAK,OAAK,EAAE,aAAa;IACzF,OAAO;;EAET;IACE,MAAM;IACN,YAAY;;;;;ACnGhB,IAAM,kBAAqC,CAAC,KAAK,SAAQ;AACvD,QAAM,cAAc,OAAO,WAAW;AACtC,QAAM,mBAAmB,OAAO,gBAAgB;AAGhD,QAAM,iBAAiB,IAAI,IAAI,SAAS,SAAS,KAAK,IAAI,IAAI,SAAS,SAAS,KAAK,IAAI,IAAI,SAAS,qBAAqB;AAE3H,MAAI,QAAuB;AAE3B,MAAI,gBAAgB;AAElB,YAAQ,aAAa,QAAQ,oBAAoB;AACjD,YAAQ,IAAI,8CAA8B;MACxC,KAAK,IAAI;MACT,eAAe,CAAC,CAAC;MACjB,cAAc,QAAQ,MAAM,UAAU,GAAG,EAAE,IAAI,QAAQ;KACxD;EACH,OAAO;AAEL,YAAQ,YAAY,SAAQ;AAC5B,YAAQ,IAAI,iCAAuB;MACjC,KAAK,IAAI;MACT,UAAU,CAAC,CAAC;MACZ,cAAc,QAAQ,MAAM,UAAU,GAAG,EAAE,IAAI,QAAQ;KACxD;EACH;AAEA,MAAI,OAAO;AACT,UAAM,UAAU,IAAI,MAAM;MACxB,SAAS,IAAI,QAAQ,IAAI,iBAAiB,UAAU,KAAK,EAAE;KAC5D;AACD,YAAQ,IAAI,2CAA6B;AACzC,WAAO,KAAK,OAAO;EACrB;AAEA,UAAQ,KAAK,6CAAmC,IAAI,GAAG;AACvD,SAAO,KAAK,GAAG;AACjB;AAEO,IAAM,YAA+B;EAC1C,WAAW;;IAET,2BAA2B;MACzB,iBAAiB;MACjB,eAAe;KAChB;;IAGD,cACE,QACA,qCAAoC,GACpC,sBAAsB;MACpB,2BAA2B;MAC3B,iBAAiB;KAClB,GACD,iBAAiB;MACf,qBAAqB;KACtB,CAAC;;IAIJ;MACE,UAAS;;MACT,iBAAiB,CAAC,eAAe,CAAC;;;;IAIpC,uBAAsB;;;;;;ACvEpB,IAAO,eAAP,MAAO,cAAY;EACvB,QAAQ;;qCADG,eAAY;EAAA;yEAAZ,eAAY,WAAA,CAAA,CAAA,UAAA,CAAA,GAAA,OAAA,GAAA,MAAA,GAAA,UAAA,SAAA,sBAAA,IAAA,KAAA;AAAA,QAAA,KAAA,GAAA;ACTzB,MAAA,oBAAA,GAAA,eAAA;;oBDKY,YAAY,GAAA,eAAA,EAAA,CAAA;;;sEAIX,cAAY,CAAA;UANxB;uBACW,YAAU,SACX,CAAC,YAAY,GAAC,UAAA,sCAAA,CAAA;;;;6EAIZ,cAAY,EAAA,WAAA,gBAAA,UAAA,4BAAA,YAAA,GAAA,CAAA;AAAA,GAAA;;;AELzB,qBAAqB,cAAc,SAAS,EACzC,MAAM,CAAC,QAAQ,QAAQ,MAAM,GAAG,CAAC;", "names": [], "x_google_ignoreList": [0]}
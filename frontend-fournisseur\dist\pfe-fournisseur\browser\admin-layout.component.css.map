{"version": 3, "sources": ["angular:styles/component:css;0978fe960c77ec66545c59bdedbcdfc2284822a9f60a3ee37c77f4f054cd9a5e;C:/Users/<USER>/Desktop/STE/PFE/PFE/frontend-fournisseur/src/app/components/admin/layout/admin-layout/admin-layout.component.ts"], "sourcesContent": ["\n    .admin-layout {\n      display: flex;\n      flex-direction: column;\n      min-height: 100vh;\n      background: #f8fafc;\n    }\n    \n    .admin-body {\n      display: flex;\n      flex: 1;\n    }\n\n    .admin-sidebar {\n      width: 280px;\n      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n      color: white;\n      display: flex;\n      flex-direction: column;\n      box-shadow: 4px 0 20px rgba(102, 126, 234, 0.2);\n    }\n    \n\n    \n    .sidebar-nav {\n      flex: 1;\n      padding: 1rem 0;\n      display: flex;\n      flex-direction: column;\n    }\n    \n    .nav-item {\n      display: flex;\n      align-items: center;\n      padding: 1rem 2rem;\n      color: rgba(255, 255, 255, 0.9);\n      text-decoration: none;\n      transition: all 0.3s ease;\n      border-left: 3px solid transparent;\n      font-weight: 500;\n      font-size: 0.95rem;\n      margin: 0;\n      border-bottom: 1px solid rgba(255, 255, 255, 0.1);\n    }\n\n    .nav-item:hover {\n      background: rgba(255, 255, 255, 0.15);\n      color: white;\n      border-left-color: #667eea;\n      transform: translateX(4px);\n      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);\n    }\n\n    .nav-item.active {\n      background: rgba(102, 126, 234, 0.2);\n      color: white;\n      border-left-color: #667eea;\n      font-weight: 600;\n    }\n    \n\n    \n    .admin-main {\n      flex: 1;\n      display: flex;\n      flex-direction: column;\n    }\n    \n    .admin-header {\n      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n      padding: 1.5rem 2rem;\n      border-bottom: none;\n      display: flex;\n      justify-content: space-between;\n      align-items: center;\n      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);\n      width: 100%;\n      position: sticky;\n      top: 0;\n      z-index: 1000;\n    }\n\n    .header-actions {\n      display: flex;\n      align-items: center;\n      gap: 1.5rem;\n    }\n\n    .admin-header h1 {\n      margin: 0;\n      font-size: 1.75rem;\n      color: white;\n      font-weight: 600;\n      text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);\n    }\n\n    .user-role {\n      padding: 0.75rem 1.25rem;\n      background: rgba(255, 255, 255, 0.2);\n      color: white;\n      border-radius: 25px;\n      font-size: 0.875rem;\n      font-weight: 500;\n      border: 1px solid rgba(255, 255, 255, 0.3);\n      backdrop-filter: blur(10px);\n      transition: all 0.3s ease;\n    }\n\n    .user-role:hover {\n      background: rgba(255, 255, 255, 0.3);\n      transform: translateY(-1px);\n    }\n    \n    .admin-content {\n      flex: 1;\n      padding: 0;\n      overflow-y: auto;\n      background: #f8fafc;\n    }\n    \n    @media (max-width: 768px) {\n      .admin-header {\n        padding: 1rem;\n      }\n\n      .admin-header h1 {\n        font-size: 1.25rem;\n      }\n\n      .admin-body {\n        flex-direction: column;\n      }\n\n      .admin-sidebar {\n        width: 100%;\n        height: auto;\n        order: 2;\n      }\n\n      .admin-content {\n        order: 1;\n      }\n\n      .sidebar-nav {\n        display: flex;\n        overflow-x: auto;\n        padding: 1rem;\n        gap: 0.5rem;\n      }\n\n      .nav-item {\n        white-space: nowrap;\n        min-width: 120px;\n        text-align: center;\n        padding: 0.75rem 1rem;\n        border-radius: 12px;\n        border-left: none;\n        border-bottom: 3px solid transparent;\n      }\n\n      .nav-item:hover {\n        border-left: none;\n        border-bottom-color: #667eea;\n        transform: translateX(0);\n        transform: translateY(-2px);\n      }\n    }\n  "], "mappings": ";AACI,CAAC;AACC,WAAS;AACT,kBAAgB;AAChB,cAAY;AACZ,cAAY;AACd;AAEA,CAAC;AACC,WAAS;AACT,QAAM;AACR;AAEA,CAAC;AACC,SAAO;AACP;AAAA,IAAY;AAAA,MAAgB,MAAhB;AAAA,MAAwB,QAAQ,EAAhC;AAAA,MAAoC,QAAQ;AACxD,SAAO;AACP,WAAS;AACT,kBAAgB;AAChB,cAAY,IAAI,EAAE,KAAK,KAAK,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE;AAC7C;AAIA,CAAC;AACC,QAAM;AACN,WAAS,KAAK;AACd,WAAS;AACT,kBAAgB;AAClB;AAEA,CAAC;AACC,WAAS;AACT,eAAa;AACb,WAAS,KAAK;AACd,SAAO,KAAK,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE;AAC3B,mBAAiB;AACjB,cAAY,IAAI,KAAK;AACrB,eAAa,IAAI,MAAM;AACvB,eAAa;AACb,aAAW;AACX,UAAQ;AACR,iBAAe,IAAI,MAAM,KAAK,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE;AAC/C;AAEA,CAdC,QAcQ;AACP,cAAY,KAAK,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE;AAChC,SAAO;AACP,qBAAmB;AACnB,aAAW,WAAW;AACtB,cAAY,EAAE,IAAI,KAAK,KAAK,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE;AACvC;AAEA,CAtBC,QAsBQ,CAAC;AACR,cAAY,KAAK,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE;AAChC,SAAO;AACP,qBAAmB;AACnB,eAAa;AACf;AAIA,CAAC;AACC,QAAM;AACN,WAAS;AACT,kBAAgB;AAClB;AAEA,CAAC;AACC;AAAA,IAAY;AAAA,MAAgB,MAAhB;AAAA,MAAwB,QAAQ,EAAhC;AAAA,MAAoC,QAAQ;AACxD,WAAS,OAAO;AAChB,iBAAe;AACf,WAAS;AACT,mBAAiB;AACjB,eAAa;AACb,cAAY,EAAE,IAAI,KAAK,KAAK,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE;AACrC,SAAO;AACP,YAAU;AACV,OAAK;AACL,WAAS;AACX;AAEA,CAAC;AACC,WAAS;AACT,eAAa;AACb,OAAK;AACP;AAEA,CApBC,aAoBa;AACZ,UAAQ;AACR,aAAW;AACX,SAAO;AACP,eAAa;AACb,eAAa,EAAE,IAAI,IAAI,KAAK,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE;AACvC;AAEA,CAAC;AACC,WAAS,QAAQ;AACjB,cAAY,KAAK,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE;AAChC,SAAO;AACP,iBAAe;AACf,aAAW;AACX,eAAa;AACb,UAAQ,IAAI,MAAM,KAAK,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE;AACtC,2BAAiB,KAAK;AAAtB,mBAAiB,KAAK;AACtB,cAAY,IAAI,KAAK;AACvB;AAEA,CAZC,SAYS;AACR,cAAY,KAAK,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE;AAChC,aAAW,WAAW;AACxB;AAEA,CAAC;AACC,QAAM;AACN,WAAS;AACT,cAAY;AACZ,cAAY;AACd;AAEA,OAAO,CAAC,SAAS,EAAE;AACjB,GArDD;AAsDG,aAAS;AACX;AAEA,GAzDD,aAyDe;AACZ,eAAW;AACb;AAEA,GAzHD;AA0HG,oBAAgB;AAClB;AAEA,GAxHD;AAyHG,WAAO;AACP,YAAQ;AACR,WAAO;AACT;AAEA,GA1BD;AA2BG,WAAO;AACT;AAEA,GAvHD;AAwHG,aAAS;AACT,gBAAY;AACZ,aAAS;AACT,SAAK;AACP;AAEA,GAvHD;AAwHG,iBAAa;AACb,eAAW;AACX,gBAAY;AACZ,aAAS,QAAQ;AACjB,mBAAe;AACf,iBAAa;AACb,mBAAe,IAAI,MAAM;AAC3B;AAEA,GAjID,QAiIU;AACP,iBAAa;AACb,yBAAqB;AACrB,eAAW,WAAW;AACtB,eAAW,WAAW;AACxB;AACF;", "names": []}
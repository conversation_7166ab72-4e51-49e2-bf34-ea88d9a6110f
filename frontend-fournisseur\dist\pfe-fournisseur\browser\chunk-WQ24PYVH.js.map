{"version": 3, "sources": ["src/app/services/demande.service.ts"], "sourcesContent": ["import { Injectable } from '@angular/core';\nimport { HttpClient } from '@angular/common/http';\nimport { Observable } from 'rxjs';\nimport { tap, catchError } from 'rxjs/operators';\nimport { environment } from '../../environments/environment';\n\nexport interface DemandeCategorieDto {\n  id: number;\n  nom: string;\n  description: string;\n  imageUrl?: string;\n  fournisseurId: number;\n  fournisseurNom: string;\n  statut: StatutDemande;\n  dateDemande: Date;\n  dateTraitement?: Date;\n  adminTraitantId?: number;\n  commentaireAdmin?: string;\n  categorieCreeeId?: number;\n  nomCategorieCreee?: string;\n}\n\nexport interface CreateDemandeCategorieDto {\n  nom: string;\n  description: string;\n}\n\nexport interface TraiterDemandeCategorieDto {\n  statut: StatutDemande;\n  commentaireAdmin?: string;\n}\n\nexport interface DemandeSousCategorieDto {\n  id: number;\n  nom: string;\n  description: string;\n  imageUrl?: string;\n  categorieId: number;\n  categorieNom: string;\n  fournisseurId: number;\n  fournisseurNom: string;\n  statut: StatutDemande;\n  dateDemande: Date;\n  dateTraitement?: Date;\n  adminTraitantId?: number;\n  commentaireAdmin?: string;\n  sousCategorieCreeeId?: number;\n}\n\nexport interface CreateDemandeSousCategorieDto {\n  nom: string;\n  description: string;\n  categorieId: number;\n}\n\nexport interface TraiterDemandeSousCategorieDto {\n  statut: StatutDemande;\n  commentaireAdmin?: string;\n}\n\nexport enum StatutDemande {\n  EnAttente = 0,\n  Approuvee = 1,\n  Rejetee = 2\n}\n\n@Injectable({\n  providedIn: 'root'\n})\nexport class DemandeService {\n  private apiUrl = environment.apiUrl;\n\n  constructor(private http: HttpClient) {}\n\n  // Demandes de catégories\n  getAllDemandesCategories(): Observable<DemandeCategorieDto[]> {\n    return this.http.get<DemandeCategorieDto[]>(`${this.apiUrl}/DemandesCategories`);\n  }\n\n  getDemandesCategoriesByStatut(statut: StatutDemande): Observable<DemandeCategorieDto[]> {\n    return this.http.get<DemandeCategorieDto[]>(`${this.apiUrl}/DemandesCategories/statut/${statut}`);\n  }\n\n  getMesDemandesCategories(): Observable<DemandeCategorieDto[]> {\n    return this.http.get<DemandeCategorieDto[]>(`${this.apiUrl}/DemandesCategories/mes-demandes`);\n  }\n\n  getDemandeCategorie(id: number): Observable<DemandeCategorieDto> {\n    return this.http.get<DemandeCategorieDto>(`${this.apiUrl}/DemandesCategories/${id}`);\n  }\n\n  createDemandeCategorie(demande: CreateDemandeCategorieDto): Observable<DemandeCategorieDto> {\n    console.log('🚀 Service createDemandeCategorie appelé');\n    console.log('📝 URL:', `${this.apiUrl}/DemandesCategories`);\n    console.log('📤 Données à envoyer:', demande);\n\n    return this.http.post<DemandeCategorieDto>(`${this.apiUrl}/DemandesCategories`, demande).pipe(\n      tap(response => {\n        console.log('✅ Réponse du service reçue:', response);\n      }),\n      catchError(error => {\n        console.error('❌ Erreur dans le service:', error);\n        console.error('📊 Status:', error.status);\n        console.error('📋 Message:', error.message);\n        console.error('🔍 Error complet:', error);\n        throw error;\n      })\n    );\n  }\n\n  traiterDemandeCategorie(id: number, traitement: TraiterDemandeCategorieDto): Observable<DemandeCategorieDto> {\n    return this.http.put<DemandeCategorieDto>(`${this.apiUrl}/DemandesCategories/${id}/traiter`, traitement);\n  }\n\n  deleteDemandeCategorie(id: number): Observable<void> {\n    return this.http.delete<void>(`${this.apiUrl}/DemandesCategories/${id}`);\n  }\n\n  // Demandes de sous-catégories\n  getAllDemandesSousCategories(): Observable<DemandeSousCategorieDto[]> {\n    return this.http.get<DemandeSousCategorieDto[]>(`${this.apiUrl}/DemandesSousCategories`);\n  }\n\n  getDemandesSousCategoriesByStatut(statut: StatutDemande): Observable<DemandeSousCategorieDto[]> {\n    return this.http.get<DemandeSousCategorieDto[]>(`${this.apiUrl}/DemandesSousCategories/statut/${statut}`);\n  }\n\n  getMesDemandesSousCategories(): Observable<DemandeSousCategorieDto[]> {\n    return this.http.get<DemandeSousCategorieDto[]>(`${this.apiUrl}/DemandesSousCategories/mes-demandes`);\n  }\n\n  getDemandeSousCategorie(id: number): Observable<DemandeSousCategorieDto> {\n    return this.http.get<DemandeSousCategorieDto>(`${this.apiUrl}/DemandesSousCategories/${id}`);\n  }\n\n  createDemandeSousCategorie(demande: CreateDemandeSousCategorieDto): Observable<DemandeSousCategorieDto> {\n    return this.http.post<DemandeSousCategorieDto>(`${this.apiUrl}/DemandesSousCategories`, demande);\n  }\n\n  traiterDemandeSousCategorie(id: number, traitement: TraiterDemandeSousCategorieDto): Observable<DemandeSousCategorieDto> {\n    return this.http.put<DemandeSousCategorieDto>(`${this.apiUrl}/DemandesSousCategories/${id}/traiter`, traitement);\n  }\n\n  deleteDemandeSousCategorie(id: number): Observable<void> {\n    return this.http.delete<void>(`${this.apiUrl}/DemandesSousCategories/${id}`);\n  }\n\n  // Méthodes utilitaires\n  getStatutLabel(statut: StatutDemande): string {\n    switch (statut) {\n      case StatutDemande.EnAttente:\n        return 'En attente';\n      case StatutDemande.Approuvee:\n        return 'Approuvée';\n      case StatutDemande.Rejetee:\n        return 'Rejetée';\n      default:\n        return 'Inconnu';\n    }\n  }\n\n  getStatutColor(statut: StatutDemande): string {\n    switch (statut) {\n      case StatutDemande.EnAttente:\n        return '#ff9800';\n      case StatutDemande.Approuvee:\n        return '#4caf50';\n      case StatutDemande.Rejetee:\n        return '#f44336';\n      default:\n        return '#666';\n    }\n  }\n}\n"], "mappings": ";;;;;;;;;;;;;;;;AA4DA,IAAY;CAAZ,SAAYA,gBAAa;AACvB,EAAAA,eAAAA,eAAA,WAAA,IAAA,CAAA,IAAA;AACA,EAAAA,eAAAA,eAAA,WAAA,IAAA,CAAA,IAAA;AACA,EAAAA,eAAAA,eAAA,SAAA,IAAA,CAAA,IAAA;AACF,GAJY,kBAAA,gBAAa,CAAA,EAAA;AASnB,IAAO,iBAAP,MAAO,gBAAc;EAGL;EAFZ,SAAS,YAAY;EAE7B,YAAoB,MAAgB;AAAhB,SAAA,OAAA;EAAmB;;EAGvC,2BAAwB;AACtB,WAAO,KAAK,KAAK,IAA2B,GAAG,KAAK,MAAM,qBAAqB;EACjF;EAEA,8BAA8B,QAAqB;AACjD,WAAO,KAAK,KAAK,IAA2B,GAAG,KAAK,MAAM,8BAA8B,MAAM,EAAE;EAClG;EAEA,2BAAwB;AACtB,WAAO,KAAK,KAAK,IAA2B,GAAG,KAAK,MAAM,kCAAkC;EAC9F;EAEA,oBAAoB,IAAU;AAC5B,WAAO,KAAK,KAAK,IAAyB,GAAG,KAAK,MAAM,uBAAuB,EAAE,EAAE;EACrF;EAEA,uBAAuB,SAAkC;AACvD,YAAQ,IAAI,oDAA0C;AACtD,YAAQ,IAAI,kBAAW,GAAG,KAAK,MAAM,qBAAqB;AAC1D,YAAQ,IAAI,sCAAyB,OAAO;AAE5C,WAAO,KAAK,KAAK,KAA0B,GAAG,KAAK,MAAM,uBAAuB,OAAO,EAAE,KACvF,IAAI,cAAW;AACb,cAAQ,IAAI,0CAA+B,QAAQ;IACrD,CAAC,GACD,WAAW,WAAQ;AACjB,cAAQ,MAAM,kCAA6B,KAAK;AAChD,cAAQ,MAAM,qBAAc,MAAM,MAAM;AACxC,cAAQ,MAAM,sBAAe,MAAM,OAAO;AAC1C,cAAQ,MAAM,4BAAqB,KAAK;AACxC,YAAM;IACR,CAAC,CAAC;EAEN;EAEA,wBAAwB,IAAY,YAAsC;AACxE,WAAO,KAAK,KAAK,IAAyB,GAAG,KAAK,MAAM,uBAAuB,EAAE,YAAY,UAAU;EACzG;EAEA,uBAAuB,IAAU;AAC/B,WAAO,KAAK,KAAK,OAAa,GAAG,KAAK,MAAM,uBAAuB,EAAE,EAAE;EACzE;;EAGA,+BAA4B;AAC1B,WAAO,KAAK,KAAK,IAA+B,GAAG,KAAK,MAAM,yBAAyB;EACzF;EAEA,kCAAkC,QAAqB;AACrD,WAAO,KAAK,KAAK,IAA+B,GAAG,KAAK,MAAM,kCAAkC,MAAM,EAAE;EAC1G;EAEA,+BAA4B;AAC1B,WAAO,KAAK,KAAK,IAA+B,GAAG,KAAK,MAAM,sCAAsC;EACtG;EAEA,wBAAwB,IAAU;AAChC,WAAO,KAAK,KAAK,IAA6B,GAAG,KAAK,MAAM,2BAA2B,EAAE,EAAE;EAC7F;EAEA,2BAA2B,SAAsC;AAC/D,WAAO,KAAK,KAAK,KAA8B,GAAG,KAAK,MAAM,2BAA2B,OAAO;EACjG;EAEA,4BAA4B,IAAY,YAA0C;AAChF,WAAO,KAAK,KAAK,IAA6B,GAAG,KAAK,MAAM,2BAA2B,EAAE,YAAY,UAAU;EACjH;EAEA,2BAA2B,IAAU;AACnC,WAAO,KAAK,KAAK,OAAa,GAAG,KAAK,MAAM,2BAA2B,EAAE,EAAE;EAC7E;;EAGA,eAAe,QAAqB;AAClC,YAAQ,QAAQ;MACd,KAAK,cAAc;AACjB,eAAO;MACT,KAAK,cAAc;AACjB,eAAO;MACT,KAAK,cAAc;AACjB,eAAO;MACT;AACE,eAAO;IACX;EACF;EAEA,eAAe,QAAqB;AAClC,YAAQ,QAAQ;MACd,KAAK,cAAc;AACjB,eAAO;MACT,KAAK,cAAc;AACjB,eAAO;MACT,KAAK,cAAc;AACjB,eAAO;MACT;AACE,eAAO;IACX;EACF;;qCAvGW,iBAAc,mBAAA,UAAA,CAAA;EAAA;4EAAd,iBAAc,SAAd,gBAAc,WAAA,YAFb,OAAM,CAAA;;;sEAEP,gBAAc,CAAA;UAH1B;WAAW;MACV,YAAY;KACb;;;", "names": ["StatutDemande"]}
import {
  CommonModule,
  Component,
  setClassMetadata,
  ɵsetClassDebugInfo,
  ɵɵdefineComponent,
  ɵɵelementEnd,
  ɵɵelementStart,
  ɵɵtext
} from "./chunk-UBZQS7JS.js";

// src/app/components/admin/users/role-assignment/role-assignment.component.ts
var RoleAssignmentComponent = class _RoleAssignmentComponent {
  static \u0275fac = function RoleAssignmentComponent_Factory(__ngFactoryType__) {
    return new (__ngFactoryType__ || _RoleAssignmentComponent)();
  };
  static \u0275cmp = /* @__PURE__ */ \u0275\u0275defineComponent({ type: _RoleAssignmentComponent, selectors: [["app-role-assignment"]], decls: 21, vars: 0, consts: [[1, "role-assignment"], [1, "coming-soon"], [1, "icon"]], template: function RoleAssignmentComponent_Template(rf, ctx) {
    if (rf & 1) {
      \u0275\u0275elementStart(0, "div", 0)(1, "h1");
      \u0275\u0275text(2, "\u{1F6E1}\uFE0F Attribution des r\xF4les");
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(3, "p");
      \u0275\u0275text(4, "Gestion des r\xF4les et permissions - R\xE9serv\xE9 aux Super Administrateurs");
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(5, "div", 1)(6, "div", 2);
      \u0275\u0275text(7, "\u{1F451}");
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(8, "h2");
      \u0275\u0275text(9, "Gestion avanc\xE9e des r\xF4les");
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(10, "p");
      \u0275\u0275text(11, "Fonctionnalit\xE9s \xE0 venir :");
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(12, "ul")(13, "li");
      \u0275\u0275text(14, "Attribution granulaire des permissions");
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(15, "li");
      \u0275\u0275text(16, "Cr\xE9ation de r\xF4les personnalis\xE9s");
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(17, "li");
      \u0275\u0275text(18, "Audit des changements de permissions");
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(19, "li");
      \u0275\u0275text(20, "Workflow d'approbation");
      \u0275\u0275elementEnd()()()();
    }
  }, dependencies: [CommonModule], styles: ["\n\n.role-assignment[_ngcontent-%COMP%] {\n  max-width: 800px;\n}\nh1[_ngcontent-%COMP%] {\n  color: #1e293b;\n  margin-bottom: 0.5rem;\n}\n.coming-soon[_ngcontent-%COMP%] {\n  background: white;\n  padding: 3rem;\n  border-radius: 12px;\n  text-align: center;\n  margin-top: 2rem;\n  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);\n}\n.icon[_ngcontent-%COMP%] {\n  font-size: 4rem;\n  margin-bottom: 1rem;\n}\n.coming-soon[_ngcontent-%COMP%]   h2[_ngcontent-%COMP%] {\n  color: #1e293b;\n  margin-bottom: 1rem;\n}\n.coming-soon[_ngcontent-%COMP%]   ul[_ngcontent-%COMP%] {\n  text-align: left;\n  max-width: 300px;\n  margin: 1rem auto;\n}\n.coming-soon[_ngcontent-%COMP%]   li[_ngcontent-%COMP%] {\n  margin-bottom: 0.5rem;\n  color: #64748b;\n}\n/*# sourceMappingURL=role-assignment.component.css.map */"] });
};
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && setClassMetadata(RoleAssignmentComponent, [{
    type: Component,
    args: [{ selector: "app-role-assignment", standalone: true, imports: [CommonModule], template: `
    <div class="role-assignment">
      <h1>\u{1F6E1}\uFE0F Attribution des r\xF4les</h1>
      <p>Gestion des r\xF4les et permissions - R\xE9serv\xE9 aux Super Administrateurs</p>
      
      <div class="coming-soon">
        <div class="icon">\u{1F451}</div>
        <h2>Gestion avanc\xE9e des r\xF4les</h2>
        <p>Fonctionnalit\xE9s \xE0 venir :</p>
        <ul>
          <li>Attribution granulaire des permissions</li>
          <li>Cr\xE9ation de r\xF4les personnalis\xE9s</li>
          <li>Audit des changements de permissions</li>
          <li>Workflow d'approbation</li>
        </ul>
      </div>
    </div>
  `, styles: ["/* angular:styles/component:css;a2a55411dbb9e19525989cbddf576bfe87d824ef836f90af54d913b76dd0853f;C:/Users/<USER>/Desktop/STE/PFE/PFE/frontend-fournisseur/src/app/components/admin/users/role-assignment/role-assignment.component.ts */\n.role-assignment {\n  max-width: 800px;\n}\nh1 {\n  color: #1e293b;\n  margin-bottom: 0.5rem;\n}\n.coming-soon {\n  background: white;\n  padding: 3rem;\n  border-radius: 12px;\n  text-align: center;\n  margin-top: 2rem;\n  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);\n}\n.icon {\n  font-size: 4rem;\n  margin-bottom: 1rem;\n}\n.coming-soon h2 {\n  color: #1e293b;\n  margin-bottom: 1rem;\n}\n.coming-soon ul {\n  text-align: left;\n  max-width: 300px;\n  margin: 1rem auto;\n}\n.coming-soon li {\n  margin-bottom: 0.5rem;\n  color: #64748b;\n}\n/*# sourceMappingURL=role-assignment.component.css.map */\n"] }]
  }], null, null);
})();
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && \u0275setClassDebugInfo(RoleAssignmentComponent, { className: "RoleAssignmentComponent", filePath: "src/app/components/admin/users/role-assignment/role-assignment.component.ts", lineNumber: 67 });
})();
export {
  RoleAssignmentComponent
};
//# sourceMappingURL=chunk-P7SHLRZE.js.map

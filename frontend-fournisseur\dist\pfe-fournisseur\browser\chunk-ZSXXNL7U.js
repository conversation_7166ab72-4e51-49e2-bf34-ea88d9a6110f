import {
  HttpClient,
  HttpHeaders
} from "./chunk-7JDDWGD3.js";
import {
  BehaviorSubject,
  Injectable,
  catchError,
  computed,
  effect,
  setClassMetadata,
  signal,
  tap,
  ɵɵdefineInjectable,
  ɵɵinject
} from "./chunk-UBZQS7JS.js";

// src/app/services/auth.service.ts
var AuthService = class _AuthService {
  http;
  // URLs possibles du backend
  POSSIBLE_URLS = [
    "http://localhost:5014/api",
    "http://localhost:7264/api",
    "https://localhost:7264/api",
    "http://localhost:5000/api",
    "https://localhost:5001/api",
    "http://localhost:7265/api",
    "https://localhost:7265/api"
  ];
  API_URL = "http://localhost:5014/api";
  // Backend HTTP
  TOKEN_KEY = "auth_token";
  USER_KEY = "current_user";
  // Angular 19: Signals pour l'état d'authentification
  currentUser = signal(this.getCurrentUser());
  isAuthenticated = signal(this.hasValidToken());
  // Angular 19: Computed signals pour des propriétés dérivées
  userRole = computed(() => this.currentUser()?.role || null);
  isFournisseur = computed(() => this.userRole() === "Fournisseur");
  isAdmin = computed(() => this.userRole() === "Admin");
  userName = computed(() => {
    const user = this.currentUser();
    return user ? `${user.prenom} ${user.nom}` : null;
  });
  // Compatibilité avec l'ancien système (pour migration progressive)
  currentUserSubject = new BehaviorSubject(this.getCurrentUser());
  currentUser$ = this.currentUserSubject.asObservable();
  isAuthenticatedSubject = new BehaviorSubject(this.hasValidToken());
  isAuthenticated$ = this.isAuthenticatedSubject.asObservable();
  constructor(http) {
    this.http = http;
    effect(() => {
      const user = this.currentUser();
      this.currentUserSubject.next(user);
      console.log("\u{1F464} Utilisateur connect\xE9:", user ? this.userName() : "Aucun");
    });
    effect(() => {
      const authenticated = this.isAuthenticated();
      this.isAuthenticatedSubject.next(authenticated);
      console.log("\u{1F510} \xC9tat d'authentification:", authenticated ? "Connect\xE9" : "D\xE9connect\xE9");
    });
  }
  /**
   * Connexion utilisateur
   */
  login(credentials) {
    console.log("\u{1F510} Tentative de connexion pour:", credentials.email);
    console.log("\u{1F310} URL API utilis\xE9e:", this.API_URL);
    console.log("\u{1F310} URL compl\xE8te:", `${this.API_URL}/Auth/login`);
    const headers = new HttpHeaders({
      "Content-Type": "application/json",
      "Accept": "application/json"
    });
    return this.http.post(`${this.API_URL}/Auth/login`, credentials, { headers }).pipe(tap((response) => {
      console.log("\u2705 Connexion r\xE9ussie");
      this.setSession(response);
    }));
  }
  /**
   * Inscription fournisseur
   */
  registerFournisseur(formData) {
    const url = `${this.API_URL}/Auth/register/fournisseur`;
    console.log("\u{1F4DD} Tentative d'inscription \xE0 l'URL:", url);
    console.log("\u{1F4CB} FormData contents:");
    const formDataEntries = {};
    for (let pair of formData.entries()) {
      const key = pair[0];
      const value = pair[1];
      formDataEntries[key] = value;
      if (value instanceof File) {
        console.log(`${key}: [File] ${value.name} (${value.size} bytes, ${value.type})`);
      } else {
        console.log(`${key}: ${value}`);
      }
    }
    console.log("\u{1F4CA} FormData summary:", formDataEntries);
    const headers = new HttpHeaders();
    console.log("\u{1F680} Envoi de la requ\xEAte...");
    return this.http.post(url, formData, {
      headers,
      reportProgress: true,
      observe: "response"
    }).pipe(tap((response) => {
      console.log("\u2705 Inscription r\xE9ussie - Response compl\xE8te:", response);
      console.log("\u2705 Status:", response.status);
      console.log("\u2705 Body:", response.body);
      console.log("\u2705 Headers:", response.headers);
    }), catchError((error) => {
      console.error("\u274C Erreur inscription d\xE9taill\xE9e:");
      console.error("\u274C Status:", error.status);
      console.error("\u274C Status Text:", error.statusText);
      console.error("\u274C Error body:", error.error);
      console.error("\u274C Error message:", error.message);
      console.error("\u274C URL:", error.url);
      if (error.headers) {
        console.error("\u274C Response headers:");
        error.headers.keys().forEach((key) => {
          console.error(`   ${key}: ${error.headers.get(key)}`);
        });
      }
      if (error.error) {
        try {
          const errorDetails = typeof error.error === "string" ? JSON.parse(error.error) : error.error;
          console.error("\u274C Parsed error details:", errorDetails);
        } catch (e) {
          console.error("\u274C Raw error:", error.error);
        }
      }
      throw error;
    }));
  }
  /**
   * Test d'inscription fournisseur avec FormData (pour debug)
   */
  registerFournisseurJSON(data) {
    const url = `${this.API_URL}/Fournisseurs`;
    console.log("\u{1F9EA} Test inscription avec FormData \xE0 l'URL:", url);
    console.log("\u{1F9EA} Data:", data);
    const formData = new FormData();
    const defaultLogoBlob = new Blob([
      new Uint8Array([
        137,
        80,
        78,
        71,
        13,
        10,
        26,
        10,
        0,
        0,
        0,
        13,
        73,
        72,
        68,
        82,
        0,
        0,
        0,
        1,
        0,
        0,
        0,
        1,
        8,
        6,
        0,
        0,
        0,
        31,
        21,
        196,
        137,
        0,
        0,
        0,
        10,
        73,
        68,
        65,
        84,
        120,
        156,
        99,
        0,
        1,
        0,
        0,
        5,
        0,
        1,
        13,
        10,
        45,
        180,
        0,
        0,
        0,
        0,
        73,
        69,
        78,
        68,
        174,
        66,
        96,
        130
      ])
    ], { type: "image/png" });
    const defaultLogoFile = new File([defaultLogoBlob], "default-logo.png", { type: "image/png" });
    Object.keys(data).forEach((key) => {
      if (data[key] !== null && data[key] !== void 0) {
        formData.append(key, data[key].toString());
      }
    });
    formData.append("LogoFile", defaultLogoFile);
    console.log("\u{1F9EA} FormData avec logo par d\xE9faut:");
    for (let pair of formData.entries()) {
      const key = pair[0];
      const value = pair[1];
      if (value instanceof File) {
        console.log(`${key}: [File] ${value.name} (${value.size} bytes, ${value.type})`);
      } else {
        console.log(`${key}: ${value}`);
      }
    }
    const headers = new HttpHeaders();
    return this.http.post(url, formData, { headers }).pipe(tap((response) => {
      console.log("\u2705 Test FormData r\xE9ussi:", response);
    }), catchError((error) => {
      console.error("\u274C Test FormData \xE9chou\xE9:", error);
      console.error("\u274C Status:", error.status);
      console.error("\u274C Error body:", error.error);
      throw error;
    }));
  }
  /**
   * Version alternative sans fichier pour test
   */
  registerFournisseurWithoutFile(data) {
    const url = `${this.API_URL}/Fournisseurs`;
    console.log("\u{1F9EA} Test inscription sans fichier \xE0 l'URL:", url);
    const formData = new FormData();
    Object.keys(data).forEach((key) => {
      if (data[key] !== null && data[key] !== void 0) {
        formData.append(key, data[key].toString());
      }
    });
    console.log("\u{1F9EA} FormData sans fichier:");
    for (let pair of formData.entries()) {
      console.log(`${pair[0]}: ${pair[1]}`);
    }
    const headers = new HttpHeaders();
    return this.http.post(url, formData, { headers }).pipe(tap((response) => {
      console.log("\u2705 Test sans fichier r\xE9ussi:", response);
    }), catchError((error) => {
      console.error("\u274C Test sans fichier \xE9chou\xE9:", error);
      throw error;
    }));
  }
  /**
   * Test simple de l'endpoint avec des données minimales
   */
  testEndpointWithMinimalData() {
    const url = `${this.API_URL}/Fournisseurs`;
    console.log("\u{1F9EA} Test endpoint avec donn\xE9es minimales \xE0 l'URL:", url);
    const formData = new FormData();
    const defaultLogoBlob = new Blob([
      new Uint8Array([
        137,
        80,
        78,
        71,
        13,
        10,
        26,
        10,
        0,
        0,
        0,
        13,
        73,
        72,
        68,
        82,
        0,
        0,
        0,
        1,
        0,
        0,
        0,
        1,
        8,
        6,
        0,
        0,
        0,
        31,
        21,
        196,
        137,
        0,
        0,
        0,
        10,
        73,
        68,
        65,
        84,
        120,
        156,
        99,
        0,
        1,
        0,
        0,
        5,
        0,
        1,
        13,
        10,
        45,
        180,
        0,
        0,
        0,
        0,
        73,
        69,
        78,
        68,
        174,
        66,
        96,
        130
      ])
    ], { type: "image/png" });
    const defaultLogoFile = new File([defaultLogoBlob], "default-logo.png", { type: "image/png" });
    formData.append("Email", "<EMAIL>");
    formData.append("Password", "Test123!");
    formData.append("Nom", "Test");
    formData.append("Prenom", "User");
    formData.append("Telephone", "1234567890");
    formData.append("DateNaissance", "1990-01-01");
    formData.append("MatriculeFiscale", "12345678");
    formData.append("RaisonSociale", "Test Company");
    formData.append("Rib", "12345678901234567890");
    formData.append("CodeBanque", "123");
    formData.append("Commission", "0.15");
    formData.append("DelaiPreparationJours", "2");
    formData.append("FraisLivraisonBase", "9.99");
    formData.append("EstActif", "true");
    formData.append("LogoFile", defaultLogoFile);
    console.log("\u{1F9EA} FormData avec logo par d\xE9faut:");
    for (let pair of formData.entries()) {
      const key = pair[0];
      const value = pair[1];
      if (value instanceof File) {
        console.log(`${key}: [File] ${value.name} (${value.size} bytes, ${value.type})`);
      } else {
        console.log(`${key}: ${value}`);
      }
    }
    const headers = new HttpHeaders();
    return this.http.post(url, formData, { headers }).pipe(tap((response) => {
      console.log("\u2705 Test minimal r\xE9ussi:", response);
    }), catchError((error) => {
      console.error("\u274C Test minimal \xE9chou\xE9:", error);
      console.error("\u274C Status:", error.status);
      console.error("\u274C Error body:", error.error);
      throw error;
    }));
  }
  /**
   * Déconnexion avec Angular 19 signals et nettoyage complet
   */
  logout() {
    console.log("\u{1F6AA} D\xE9connexion et nettoyage du localStorage...");
    localStorage.removeItem(this.TOKEN_KEY);
    localStorage.removeItem(this.USER_KEY);
    localStorage.removeItem("fournisseur_profile");
    localStorage.removeItem("supplierId");
    localStorage.removeItem("currentUser");
    console.log("\u2705 localStorage nettoy\xE9");
    this.currentUser.set(null);
    this.isAuthenticated.set(false);
  }
  /**
   * Obtenir le token actuel
   */
  getToken() {
    const token = localStorage.getItem(this.TOKEN_KEY);
    console.log("\u{1F50D} AuthService.getToken() appel\xE9:", {
      hasToken: !!token,
      tokenPreview: token ? token.substring(0, 20) + "..." : "Aucun",
      tokenKey: this.TOKEN_KEY
    });
    return token;
  }
  /**
   * Obtenir l'utilisateur actuel
   */
  getCurrentUser() {
    const userStr = localStorage.getItem(this.USER_KEY);
    if (userStr) {
      try {
        return JSON.parse(userStr);
      } catch {
        return null;
      }
    }
    return null;
  }
  /**
   * Vérifier si l'utilisateur est connecté (méthode legacy)
   */
  isAuthenticatedLegacy() {
    return this.hasValidToken();
  }
  /**
   * Vérifier si l'utilisateur est un fournisseur (méthode legacy)
   */
  isFournisseurLegacy() {
    const user = this.getCurrentUser();
    return user?.role === "Fournisseur";
  }
  /**
   * Rafraîchir les informations utilisateur
   */
  refreshUser() {
    const currentUser = this.getCurrentUser();
    if (!currentUser) {
      throw new Error("Aucun utilisateur connect\xE9");
    }
    return this.http.get(`${this.API_URL}/Utilisateurs/${currentUser.id}`).pipe(tap((user) => {
      localStorage.setItem(this.USER_KEY, JSON.stringify(user));
      this.currentUserSubject.next(user);
    }));
  }
  /**
   * Définir la session utilisateur avec Angular 19 signals et stockage complet
   */
  setSession(authResult) {
    console.log("\u{1F4BE} Stockage des donn\xE9es de session compl\xE8tes...");
    console.log("\u{1F4E6} Donn\xE9es utilisateur re\xE7ues:", authResult.utilisateur);
    localStorage.setItem(this.TOKEN_KEY, authResult.token);
    localStorage.setItem(this.USER_KEY, JSON.stringify(authResult.utilisateur));
    const fournisseurData = {
      // Données de base
      id: authResult.utilisateur.id,
      email: authResult.utilisateur.email,
      nom: authResult.utilisateur.nom,
      prenom: authResult.utilisateur.prenom,
      role: authResult.utilisateur.role,
      // Données étendues (si disponibles dans la réponse)
      phoneNumber: authResult.utilisateur.phoneNumber || authResult.utilisateur.telephone || "",
      dateNaissance: authResult.utilisateur.dateNaissance || "",
      dateInscription: authResult.utilisateur.dateInscription || (/* @__PURE__ */ new Date()).toISOString(),
      derniereConnexion: (/* @__PURE__ */ new Date()).toISOString(),
      estActif: authResult.utilisateur.estActif !== void 0 ? authResult.utilisateur.estActif : true,
      // Données entreprise
      matriculeFiscale: authResult.utilisateur.matriculeFiscale || "",
      raisonSociale: authResult.utilisateur.raisonSociale || "",
      description: authResult.utilisateur.description || "",
      // Données bancaires
      ribMasque: authResult.utilisateur.ribMasque || authResult.utilisateur.rib || "",
      codeBanque: authResult.utilisateur.codeBanque || "",
      // Paramètres commerciaux
      commission: authResult.utilisateur.commission || 0,
      delaiPreparationJours: authResult.utilisateur.delaiPreparationJours || 3,
      fraisLivraisonBase: authResult.utilisateur.fraisLivraisonBase || 5,
      // Autres données
      logoFile: authResult.utilisateur.logoFile || "",
      adresses: authResult.utilisateur.adresses || [],
      // Métadonnées de session
      sessionTimestamp: (/* @__PURE__ */ new Date()).toISOString(),
      tokenExpiry: this.calculateTokenExpiry(authResult.token)
    };
    localStorage.setItem("fournisseur_profile", JSON.stringify(fournisseurData));
    localStorage.setItem("supplierId", authResult.utilisateur.id.toString());
    console.log("\u2705 Donn\xE9es compl\xE8tes stock\xE9es:", fournisseurData);
    console.log("\u{1F4CA} Cl\xE9s localStorage cr\xE9\xE9es:");
    console.log("- token:", !!localStorage.getItem(this.TOKEN_KEY));
    console.log("- user:", !!localStorage.getItem(this.USER_KEY));
    console.log("- fournisseur_profile:", !!localStorage.getItem("fournisseur_profile"));
    console.log("- supplierId:", localStorage.getItem("supplierId"));
    this.currentUser.set(authResult.utilisateur);
    this.isAuthenticated.set(true);
  }
  /**
   * Calculer l'expiration du token (estimation)
   */
  calculateTokenExpiry(token) {
    try {
      const payload = JSON.parse(atob(token.split(".")[1]));
      if (payload.exp) {
        return new Date(payload.exp * 1e3).toISOString();
      }
    } catch (error) {
      console.warn("\u26A0\uFE0F Impossible de d\xE9coder le token JWT:", error);
    }
    const expiry = /* @__PURE__ */ new Date();
    expiry.setHours(expiry.getHours() + 24);
    return expiry.toISOString();
  }
  /**
   * Vérifier si le token est valide
   */
  hasValidToken() {
    const token = this.getToken();
    if (!token) {
      return false;
    }
    try {
      const payload = JSON.parse(atob(token.split(".")[1]));
      const currentTime = Math.floor(Date.now() / 1e3);
      return payload.exp > currentTime;
    } catch {
      return false;
    }
  }
  static \u0275fac = function AuthService_Factory(__ngFactoryType__) {
    return new (__ngFactoryType__ || _AuthService)(\u0275\u0275inject(HttpClient));
  };
  static \u0275prov = /* @__PURE__ */ \u0275\u0275defineInjectable({ token: _AuthService, factory: _AuthService.\u0275fac, providedIn: "root" });
};
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && setClassMetadata(AuthService, [{
    type: Injectable,
    args: [{
      providedIn: "root"
    }]
  }], () => [{ type: HttpClient }], null);
})();

export {
  AuthService
};
//# sourceMappingURL=chunk-ZSXXNL7U.js.map

using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using System.Security.Claims;
using WebApiPfe.DTOs.Admin;
using WebApiPfe.Services.Interfaces;

namespace WebApiPfe.Controllers
{
    [ApiController]
    [Route("api/[controller]")]
    [Authorize]
    public class AvisModerationController : ControllerBase
    {
        private readonly IAvisService _avisService;
        private readonly ILogger<AvisModerationController> _logger;

        public AvisModerationController(
            IAvisService avisService,
            ILogger<AvisModerationController> logger)
        {
            _avisService = avisService;
            _logger = logger;
        }

        /// <summary>
        /// Obtenir tous les avis pour modération (Admin et Fournisseur)
        /// </summary>
        [HttpGet]
        [AllowAnonymous] // TEMPORAIRE POUR DEBUG
        public async Task<ActionResult<List<AvisModerationDto>>> GetAvisForModeration([FromQuery] AvisFilterDto filter)
        {
            try
            {
                _logger.LogInformation("🔍 GetAvisForModeration - Début de la méthode");
                _logger.LogInformation("🔍 User.Identity.IsAuthenticated: {IsAuthenticated}", User.Identity.IsAuthenticated);
                _logger.LogInformation("🔍 User.Identity.Name: {Name}", User.Identity.Name);
                _logger.LogInformation("🔍 User Claims: {Claims}", string.Join(", ", User.Claims.Select(c => $"{c.Type}={c.Value}")));

                var avis = await _avisService.GetAvisForModerationAsync(filter);
                _logger.LogInformation("✅ Avis pour modération récupérés avec succès: {Count} avis trouvés", avis.Count);

                return Ok(avis);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "❌ Erreur lors de la récupération des avis pour modération");
                return StatusCode(500, new { message = "Erreur interne du serveur", details = ex.Message });
            }
        }

        /// <summary>
        /// Obtenir un avis spécifique pour modération
        /// </summary>
        [HttpGet("{id}")]
        [Authorize(Roles = "Admin,Fournisseur")]
        public async Task<ActionResult<AvisModerationDto>> GetAvisModeration(int id)
        {
            try
            {
                var userId = GetCurrentUserId();
                var userRole = GetCurrentUserRole();

                var avis = await _avisService.GetAvisModerationAsync(id);
                if (avis == null)
                    return NotFound(new { message = "Avis non trouvé" });

                // Vérifier les permissions pour les fournisseurs
                if (userRole == "Fournisseur" && avis.FournisseurId != userId)
                    return Forbid();

                return Ok(avis);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Erreur lors de la récupération de l'avis {Id}", id);
                return StatusCode(500, new { message = "Erreur interne du serveur" });
            }
        }

        /// <summary>
        /// Modérer un avis (Admin uniquement)
        /// </summary>
        [HttpPut("{id}/moderer")]
        [Authorize(Roles = "Admin")]
        public async Task<ActionResult<AvisModerationDto>> ModererAvis(int id, [FromBody] ModererAvisDto dto)
        {
            try
            {
                var moderateurId = GetCurrentUserId();
                var avis = await _avisService.ModererAvisAsync(id, dto, moderateurId);
                return Ok(avis);
            }
            catch (KeyNotFoundException ex)
            {
                return NotFound(new { message = ex.Message });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Erreur lors de la modération de l'avis {Id}", id);
                return StatusCode(500, new { message = "Erreur interne du serveur" });
            }
        }

        /// <summary>
        /// Obtenir les avis d'un fournisseur (Fournisseur uniquement)
        /// </summary>
        [HttpGet("fournisseur")]
        [AllowAnonymous] // TEMPORAIRE POUR DEBUG
        public async Task<ActionResult<List<AvisModerationDto>>> GetAvisFournisseur([FromQuery] AvisFilterDto filter)
        {
            try
            {
                _logger.LogInformation("🔍 GetAvisFournisseur - Début de la méthode");
                _logger.LogInformation("🔍 User.Identity.IsAuthenticated: {IsAuthenticated}", User.Identity.IsAuthenticated);
                _logger.LogInformation("🔍 User.Identity.Name: {Name}", User.Identity.Name);
                _logger.LogInformation("🔍 User Claims: {Claims}", string.Join(", ", User.Claims.Select(c => $"{c.Type}={c.Value}")));

                var fournisseurId = GetCurrentUserId();
                _logger.LogInformation("🔍 FournisseurId récupéré: {FournisseurId}", fournisseurId);

                if (fournisseurId == 0)
                {
                    _logger.LogWarning("⚠️ FournisseurId est 0 - problème d'authentification");
                    return Unauthorized(new { message = "Utilisateur non authentifié ou ID invalide" });
                }

                _logger.LogInformation("🔍 Appel du service avec filtre: {@Filter}", filter);
                var avis = await _avisService.GetAvisFournisseurAsync(fournisseurId, filter);
                _logger.LogInformation("✅ Avis récupérés avec succès: {Count} avis trouvés", avis.Count);

                return Ok(avis);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "❌ Erreur lors de la récupération des avis du fournisseur");
                return StatusCode(500, new { message = "Erreur interne du serveur", details = ex.Message });
            }
        }

        /// <summary>
        /// Répondre à un avis (Fournisseur uniquement)
        /// </summary>
        [HttpPut("{id}/repondre")]
        [Authorize(Roles = "Fournisseur")]
        public async Task<ActionResult<AvisModerationDto>> RepondreAvis(int id, [FromBody] string reponse)
        {
            try
            {
                var fournisseurId = GetCurrentUserId();
                var avis = await _avisService.RepondreAvisAsync(id, reponse, fournisseurId);
                return Ok(avis);
            }
            catch (KeyNotFoundException ex)
            {
                return NotFound(new { message = ex.Message });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Erreur lors de la réponse à l'avis {Id}", id);
                return StatusCode(500, new { message = "Erreur interne du serveur" });
            }
        }

        /// <summary>
        /// Signaler un avis problématique (Fournisseur)
        /// </summary>
        [HttpPut("{id}/signaler")]
        [AllowAnonymous] // TEMPORAIRE POUR DEBUG
        public async Task<ActionResult<AvisModerationDto>> SignalerAvis(int id, [FromBody] SignalementAvisDto signalementDto)
        {
            try
            {
                _logger.LogInformation("🚨 SignalerAvis - Début du signalement pour l'avis {AvisId}", id);
                _logger.LogInformation("🚨 Données de signalement: {@SignalementDto}", signalementDto);
                _logger.LogInformation("🚨 User.Identity.IsAuthenticated: {IsAuthenticated}", User.Identity.IsAuthenticated);
                _logger.LogInformation("🚨 User.Identity.Name: {Name}", User.Identity.Name);
                _logger.LogInformation("🚨 User Claims: {Claims}", string.Join(", ", User.Claims.Select(c => $"{c.Type}={c.Value}")));

                var fournisseurId = GetCurrentUserId();
                _logger.LogInformation("🚨 FournisseurId: {FournisseurId}", fournisseurId);

                // TEMPORAIRE : Si pas d'authentification, utiliser le fournisseur ID 2070 pour les tests
                if (fournisseurId == 0)
                {
                    fournisseurId = 2070; // OpticFatma2
                    _logger.LogWarning("⚠️ Utilisation du fournisseur par défaut pour les tests: {FournisseurId}", fournisseurId);
                }

                var avis = await _avisService.SignalerAvisAsync(id, fournisseurId, signalementDto.RaisonSignalement, signalementDto.DetailsSignalement);

                _logger.LogInformation("✅ Avis signalé avec succès. Notification envoyée à l'admin.");

                return Ok(avis);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "❌ Erreur lors du signalement de l'avis {AvisId}", id);
                return StatusCode(500, new { message = "Erreur interne du serveur", details = ex.Message });
            }
        }

        /// <summary>
        /// Obtenir les statistiques des avis (Admin et Fournisseur)
        /// </summary>
        [HttpGet("statistiques")]
        [AllowAnonymous] // TEMPORAIRE POUR DEBUG
        public async Task<ActionResult<AvisStatsDto>> GetAvisStats()
        {
            try
            {
                _logger.LogInformation("🔍 GetAvisStats - Début de la méthode");
                _logger.LogInformation("🔍 User.Identity.IsAuthenticated: {IsAuthenticated}", User.Identity.IsAuthenticated);
                _logger.LogInformation("🔍 User.Identity.Name: {Name}", User.Identity.Name);
                _logger.LogInformation("🔍 User Claims: {Claims}", string.Join(", ", User.Claims.Select(c => $"{c.Type}={c.Value}")));

                var stats = await _avisService.GetAvisStatsAsync();
                _logger.LogInformation("✅ Statistiques générales récupérées avec succès: {TotalAvis} avis total", stats.TotalAvis);

                return Ok(stats);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "❌ Erreur lors de la récupération des statistiques des avis");
                return StatusCode(500, new { message = "Erreur interne du serveur", details = ex.Message });
            }
        }

        /// <summary>
        /// Obtenir les statistiques des avis pour un fournisseur
        /// </summary>
        [HttpGet("statistiques/fournisseur")]
        [AllowAnonymous] // TEMPORAIRE POUR DEBUG
        public async Task<ActionResult<AvisStatsDto>> GetAvisStatsFournisseur()
        {
            try
            {
                _logger.LogInformation("🔍 GetAvisStatsFournisseur - Début de la méthode");
                _logger.LogInformation("🔍 User.Identity.IsAuthenticated: {IsAuthenticated}", User.Identity.IsAuthenticated);
                _logger.LogInformation("🔍 User.Identity.Name: {Name}", User.Identity.Name);

                var fournisseurId = GetCurrentUserId();
                _logger.LogInformation("🔍 FournisseurId récupéré: {FournisseurId}", fournisseurId);

                if (fournisseurId == 0)
                {
                    _logger.LogWarning("⚠️ FournisseurId est 0 - problème d'authentification");
                    return Unauthorized(new { message = "Utilisateur non authentifié ou ID invalide" });
                }

                var stats = await _avisService.GetAvisStatsFournisseurAsync(fournisseurId);
                _logger.LogInformation("✅ Statistiques récupérées avec succès: {TotalAvis} avis total", stats.TotalAvis);

                return Ok(stats);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "❌ Erreur lors de la récupération des statistiques des avis du fournisseur");
                return StatusCode(500, new { message = "Erreur interne du serveur", details = ex.Message });
            }
        }

        // Méthodes d'aide privées
        private int GetCurrentUserId()
        {
            var userIdClaim = User.FindFirst(ClaimTypes.NameIdentifier)?.Value;
            return int.TryParse(userIdClaim, out var userId) ? userId : 0;
        }

        private string GetCurrentUserRole()
        {
            return User.FindFirst(ClaimTypes.Role)?.Value ?? "";
        }
    }
}

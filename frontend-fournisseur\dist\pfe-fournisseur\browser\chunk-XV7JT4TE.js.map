{"version": 3, "sources": ["src/app/components/referentiels/referentiels.component.ts"], "sourcesContent": ["import { Component, OnInit, signal } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { FormsModule } from '@angular/forms';\nimport { Router } from '@angular/router';\nimport { CategorieService } from '../../services/categorie.service';\nimport { SousCategorieService } from '../../services/sous-categorie.service';\nimport { MarqueService } from '../../services/marque.service';\nimport { FormeService } from '../../services/forme.service';\nimport { TauxTVAService } from '../../services/taux-tva.service';\nimport { AuthService } from '../../services/auth.service';\nimport { ImageUrlService } from '../../services/image-url.service';\nimport {\n  Categorie,\n  SousCategorie,\n  Marque,\n  Forme,\n  TauxTVA,\n  CategorieCreate,\n  SousCategorieCreate,\n  MarqueCreate,\n  FormeCreate,\n  TauxTVACreate\n} from '../../models';\n\n@Component({\n  selector: 'app-referentiels',\n  standalone: true,\n  imports: [CommonModule, FormsModule],\n  template: `\n    <div class=\"referentiels-container\">\n      <!-- Header moderne -->\n      <div class=\"referentiels-header\">\n        <h1 class=\"referentiels-title\">\n          <span>🗂️</span>\n          {{ isAdmin() ? 'Gestion des Référentiels' : 'Consultation des Référentiels' }}\n        </h1>\n        <p>{{ isAdmin() ? 'Configurez vos catégories, marques et paramètres' : 'Consultez les catégories, marques et paramètres disponibles' }}</p>\n      </div>\n\n      <!-- Onglets -->\n      <div class=\"tabs\">\n        <button\n          *ngFor=\"let tab of tabs\"\n          class=\"tab-button\"\n          [class.active]=\"activeTab() === tab.id\"\n          (click)=\"setActiveTab(tab.id)\"\n        >\n          {{ tab.icon }} {{ tab.label }}\n        </button>\n      </div>\n\n      <!-- Contenu des onglets -->\n      <div class=\"tab-content\">\n\n        <!-- Catégories -->\n        <div *ngIf=\"activeTab() === 'categories'\" class=\"tab-panel\">\n          <div class=\"panel-header\">\n            <h3>📁 Catégories</h3>\n            <button\n              *ngIf=\"isAdmin()\"\n              class=\"btn btn-primary\"\n              (click)=\"openCategorieForm()\"\n            >\n              ➕ Nouvelle Catégorie\n            </button>\n            <div *ngIf=\"!isAdmin()\" class=\"supplier-actions\">\n              <button\n                class=\"btn btn-outline-primary\"\n                (click)=\"requestNewCategorie()\"\n              >\n                📝 Demander une nouvelle catégorie\n              </button>\n              <span class=\"badge badge-info\">👁️ Mode consultation</span>\n            </div>\n          </div>\n\n          <div class=\"items-grid\">\n            <div *ngFor=\"let categorie of categories()\" class=\"item-card\">\n              <div class=\"item-header\">\n                <h4>{{ categorie.nom }}</h4>\n                <span class=\"badge\" [class]=\"categorie.estValidee ? 'badge-success' : 'badge-warning'\">\n                  {{ categorie.estValidee ? 'Validée' : 'En attente' }}\n                </span>\n              </div>\n              <p *ngIf=\"categorie.description\">{{ categorie.description }}</p>\n              <div class=\"item-stats\">\n                <span>{{ categorie.sousCategoriesCount || 0 }} sous-catégories</span>\n              </div>\n              <div class=\"item-actions\" *ngIf=\"isAdmin()\">\n                <button class=\"btn btn-sm btn-secondary\" (click)=\"editCategorie(categorie)\">\n                  ✏️ Modifier\n                </button>\n                <button class=\"btn btn-sm btn-danger\" (click)=\"deleteCategorie(categorie)\">\n                  🗑️ Supprimer\n                </button>\n              </div>\n            </div>\n          </div>\n        </div>\n\n        <!-- Sous-catégories -->\n        <div *ngIf=\"activeTab() === 'sous-categories'\" class=\"tab-panel\">\n          <div class=\"panel-header\">\n            <h3>📂 Sous-catégories</h3>\n            <button\n              *ngIf=\"isAdmin()\"\n              class=\"btn btn-primary\"\n              (click)=\"openSousCategorieForm()\"\n            >\n              ➕ Nouvelle Sous-catégorie\n            </button>\n            <div *ngIf=\"!isAdmin()\" class=\"supplier-actions\">\n              <button\n                class=\"btn btn-outline-primary\"\n                (click)=\"requestNewSousCategorie()\"\n              >\n                📝 Demander une nouvelle sous-catégorie\n              </button>\n              <span class=\"badge badge-info\">👁️ Mode consultation</span>\n            </div>\n          </div>\n\n          <div class=\"items-grid\">\n            <div *ngFor=\"let sousCategorie of sousCategories()\" class=\"item-card\">\n              <div class=\"item-header\">\n                <h4>{{ sousCategorie.nom }}</h4>\n                <span class=\"badge\" [class]=\"sousCategorie.estValidee ? 'badge-success' : 'badge-warning'\">\n                  {{ sousCategorie.estValidee ? 'Validée' : 'En attente' }}\n                </span>\n              </div>\n              <p *ngIf=\"sousCategorie.description\">{{ sousCategorie.description }}</p>\n              <div class=\"item-stats\">\n                <span>Catégorie: {{ sousCategorie.categorieNom || 'Non définie' }}</span><br>\n                <span>{{ sousCategorie.produitsCount || 0 }} produits</span>\n              </div>\n              <div class=\"item-actions\" *ngIf=\"isAdmin()\">\n                <button class=\"btn btn-sm btn-secondary\" (click)=\"editSousCategorie(sousCategorie)\">\n                  ✏️ Modifier\n                </button>\n                <button class=\"btn btn-sm btn-danger\" (click)=\"deleteSousCategorie(sousCategorie)\">\n                  🗑️ Supprimer\n                </button>\n              </div>\n            </div>\n          </div>\n        </div>\n\n        <!-- Marques -->\n        <div *ngIf=\"activeTab() === 'marques'\" class=\"tab-panel\">\n          <div class=\"panel-header\">\n            <h3>🏷️ Marques</h3>\n            <button\n              *ngIf=\"isAdmin()\"\n              class=\"btn btn-primary\"\n              (click)=\"openMarqueForm()\"\n            >\n              ➕ Nouvelle Marque\n            </button>\n            <div *ngIf=\"!isAdmin()\" class=\"supplier-actions\">\n              <button\n                class=\"btn btn-primary\"\n                (click)=\"openMarqueForm()\"\n              >\n                ➕ Ajouter une marque\n              </button>\n              <span class=\"badge badge-success\">✅ Ajout autorisé</span>\n            </div>\n          </div>\n\n          <div class=\"items-grid\">\n            <div *ngFor=\"let marque of marques()\" class=\"item-card\">\n              <div class=\"item-header\">\n                <h4>{{ marque.name }}</h4>\n              </div>\n              <div class=\"marque-logo\">\n                <img\n                  [src]=\"getMarqueLogoUrl(marque.logo)\"\n                  [alt]=\"marque.name\"\n                  class=\"logo-img\"\n                  (error)=\"onImageError($event)\"\n                >\n              </div>\n              <div class=\"item-actions\" *ngIf=\"isAdmin()\">\n                <button class=\"btn btn-sm btn-secondary\" (click)=\"editMarque(marque)\">\n                  ✏️ Modifier\n                </button>\n                <button class=\"btn btn-sm btn-danger\" (click)=\"deleteMarque(marque)\">\n                  🗑️ Supprimer\n                </button>\n              </div>\n            </div>\n          </div>\n        </div>\n\n        <!-- Formes -->\n        <div *ngIf=\"activeTab() === 'formes'\" class=\"tab-panel\">\n          <div class=\"panel-header\">\n            <h3>🔷 Formes</h3>\n            <button\n              *ngIf=\"isAdmin()\"\n              class=\"btn btn-primary\"\n              (click)=\"openFormeForm()\"\n            >\n              ➕ Nouvelle Forme\n            </button>\n            <div *ngIf=\"!isAdmin()\" class=\"supplier-actions\">\n              <button\n                class=\"btn btn-primary\"\n                (click)=\"openFormeForm()\"\n              >\n                ➕ Ajouter une forme\n              </button>\n              <span class=\"badge badge-success\">✅ Ajout autorisé</span>\n            </div>\n          </div>\n\n          <div class=\"items-grid\">\n            <div *ngFor=\"let forme of formes()\" class=\"item-card\">\n              <div class=\"item-header\">\n                <h4>{{ forme.nom }}</h4>\n              </div>\n              <div class=\"forme-image\">\n                <img\n                  [src]=\"getFormeImageUrl(forme.imageUrl)\"\n                  [alt]=\"forme.nom\"\n                  class=\"forme-img\"\n                  (error)=\"onImageError($event)\"\n                >\n              </div>\n              <div class=\"item-stats\">\n                <span>Catégorie: {{ forme.categorieNom || 'Non définie' }}</span>\n              </div>\n              <div class=\"item-actions\" *ngIf=\"isAdmin()\">\n                <button class=\"btn btn-sm btn-secondary\" (click)=\"editForme(forme)\">\n                  ✏️ Modifier\n                </button>\n                <button class=\"btn btn-sm btn-danger\" (click)=\"deleteForme(forme)\">\n                  🗑️ Supprimer\n                </button>\n              </div>\n            </div>\n          </div>\n        </div>\n\n        <!-- Taux TVA -->\n        <div *ngIf=\"activeTab() === 'tva'\" class=\"tab-panel\">\n          <div class=\"panel-header\">\n            <h3>💰 Taux TVA</h3>\n            <button\n              *ngIf=\"isAdmin()\"\n              class=\"btn btn-primary\"\n              (click)=\"openTauxTVAForm()\"\n            >\n              ➕ Nouveau Taux TVA\n            </button>\n            <div *ngIf=\"!isAdmin()\" class=\"read-only-notice\">\n              <span class=\"badge badge-info\">👁️ Mode consultation seule</span>\n            </div>\n          </div>\n\n          <div class=\"items-grid\">\n            <div *ngFor=\"let taux of tauxTVA()\" class=\"item-card\">\n              <div class=\"item-header\">\n                <h4>{{ taux.libelle }}</h4>\n                <span class=\"badge\" [class]=\"taux.estActif ? 'badge-success' : 'badge-danger'\">\n                  {{ taux.estActif ? 'Actif' : 'Inactif' }}\n                </span>\n              </div>\n              <div class=\"tva-rate\">\n                <span class=\"rate\">{{ taux.taux }}%</span>\n              </div>\n              <p *ngIf=\"taux.description\">{{ taux.description }}</p>\n              <div class=\"item-stats\">\n                <span>Effet: {{ formatDate(taux.dateEffet) }}</span>\n                <span *ngIf=\"taux.dateFin\">Fin: {{ formatDate(taux.dateFin) }}</span>\n              </div>\n              <div class=\"item-actions\" *ngIf=\"isAdmin()\">\n                <button class=\"btn btn-sm btn-secondary\" (click)=\"editTauxTVA(taux)\">\n                  ✏️ Modifier\n                </button>\n                <button class=\"btn btn-sm btn-danger\" (click)=\"deleteTauxTVA(taux)\">\n                  🗑️ Supprimer\n                </button>\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n\n    <!-- Modal pour les formulaires -->\n    <div class=\"modal-overlay\" *ngIf=\"showModal()\" (click)=\"closeModal()\">\n      <div class=\"modal-content\" (click)=\"$event.stopPropagation()\">\n        <div class=\"modal-header\">\n          <h3>{{ modalTitle() }}</h3>\n          <button class=\"close-btn\" (click)=\"closeModal()\">✕</button>\n        </div>\n\n        <form #form=\"ngForm\" (ngSubmit)=\"onSubmit(form)\" class=\"modal-form\">\n          <!-- Formulaire dynamique selon le type -->\n          <ng-container [ngSwitch]=\"modalType()\">\n\n            <!-- Formulaire Catégorie -->\n            <div *ngSwitchCase=\"'categorie'\">\n              <div class=\"form-group\">\n                <label>Nom de la catégorie *</label>\n                <input\n                  type=\"text\"\n                  name=\"nom\"\n                  ngModel\n                  required\n                  class=\"form-control\"\n                  placeholder=\"Ex: Lunettes de Vue\"\n                />\n              </div>\n              <div class=\"form-group\">\n                <label>Description</label>\n                <textarea\n                  name=\"description\"\n                  ngModel\n                  class=\"form-control\"\n                  placeholder=\"Description de la catégorie...\"\n                ></textarea>\n              </div>\n            </div>\n\n            <!-- Formulaire Sous-catégorie -->\n            <div *ngSwitchCase=\"'sous-categorie'\">\n              <div class=\"form-group\">\n                <label>Catégorie parente *</label>\n                <select name=\"categorieId\" ngModel required class=\"form-control\">\n                  <option value=\"\">Sélectionner une catégorie</option>\n                  <option *ngFor=\"let cat of categories()\" [value]=\"cat.id\">\n                    {{ cat.nom }}\n                  </option>\n                </select>\n              </div>\n              <div class=\"form-group\">\n                <label>Nom de la sous-catégorie *</label>\n                <input\n                  type=\"text\"\n                  name=\"nom\"\n                  ngModel\n                  required\n                  class=\"form-control\"\n                  placeholder=\"Ex: Montures Classiques\"\n                />\n              </div>\n              <div class=\"form-group\">\n                <label>Description</label>\n                <textarea\n                  name=\"description\"\n                  ngModel\n                  class=\"form-control\"\n                  placeholder=\"Description de la sous-catégorie...\"\n                ></textarea>\n              </div>\n              <div class=\"form-group\">\n                <label>\n                  <input type=\"checkbox\" name=\"estValidee\" ngModel>\n                  Validée\n                </label>\n              </div>\n            </div>\n\n            <!-- Formulaire Marque -->\n            <div *ngSwitchCase=\"'marque'\">\n              <div class=\"form-group\">\n                <label>Nom de la marque *</label>\n                <input\n                  type=\"text\"\n                  name=\"name\"\n                  ngModel\n                  required\n                  class=\"form-control\"\n                  placeholder=\"Ex: Ray-Ban\"\n                />\n              </div>\n              <div class=\"form-group\">\n                <label>URL du logo</label>\n                <input\n                  type=\"text\"\n                  name=\"logo\"\n                  ngModel\n                  class=\"form-control\"\n                  placeholder=\"Ex: rayban-logo.png\"\n                />\n              </div>\n            </div>\n\n            <!-- Formulaire Forme -->\n            <div *ngSwitchCase=\"'forme'\">\n              <div class=\"form-group\">\n                <label>Catégorie *</label>\n                <select name=\"categorieId\" ngModel required class=\"form-control\">\n                  <option value=\"\">Sélectionner une catégorie</option>\n                  <option *ngFor=\"let cat of categories()\" [value]=\"cat.id\">\n                    {{ cat.nom }}\n                  </option>\n                </select>\n              </div>\n              <div class=\"form-group\">\n                <label>Nom de la forme *</label>\n                <input\n                  type=\"text\"\n                  name=\"nom\"\n                  ngModel\n                  required\n                  class=\"form-control\"\n                  placeholder=\"Ex: Rectangulaire\"\n                />\n              </div>\n              <div class=\"form-group\">\n                <label>URL de l'image</label>\n                <input\n                  type=\"text\"\n                  name=\"imageUrl\"\n                  ngModel\n                  class=\"form-control\"\n                  placeholder=\"Ex: forme-rectangulaire.png\"\n                />\n              </div>\n            </div>\n\n            <!-- Formulaire Taux TVA -->\n            <div *ngSwitchCase=\"'tva'\">\n              <div class=\"form-group\">\n                <label>Libellé *</label>\n                <input\n                  type=\"text\"\n                  name=\"libelle\"\n                  ngModel\n                  required\n                  class=\"form-control\"\n                  placeholder=\"Ex: TVA Standard\"\n                />\n              </div>\n              <div class=\"form-group\">\n                <label>Taux (%) *</label>\n                <input\n                  type=\"number\"\n                  name=\"taux\"\n                  ngModel\n                  required\n                  min=\"0\"\n                  max=\"100\"\n                  step=\"0.1\"\n                  class=\"form-control\"\n                  placeholder=\"Ex: 20\"\n                />\n              </div>\n              <div class=\"form-group\">\n                <label>Description</label>\n                <textarea\n                  name=\"description\"\n                  ngModel\n                  class=\"form-control\"\n                  placeholder=\"Description du taux TVA...\"\n                ></textarea>\n              </div>\n              <div class=\"form-group\">\n                <label>Date d'effet *</label>\n                <input\n                  type=\"date\"\n                  name=\"dateEffet\"\n                  ngModel\n                  required\n                  class=\"form-control\"\n                />\n              </div>\n              <div class=\"form-group\">\n                <label>Date de fin</label>\n                <input\n                  type=\"date\"\n                  name=\"dateFin\"\n                  ngModel\n                  class=\"form-control\"\n                />\n              </div>\n              <div class=\"form-group\">\n                <label>\n                  <input type=\"checkbox\" name=\"estActif\" ngModel checked>\n                  Actif\n                </label>\n              </div>\n            </div>\n          </ng-container>\n\n          <div class=\"form-actions\">\n            <button type=\"button\" class=\"btn btn-secondary\" (click)=\"closeModal()\">\n              Annuler\n            </button>\n            <button type=\"submit\" class=\"btn btn-primary\" [disabled]=\"!form.valid || isLoading()\">\n              {{ isEditMode() ? 'Modifier' : 'Créer' }}\n            </button>\n          </div>\n        </form>\n      </div>\n    </div>\n  `,\n  styles: [`\n    /* ===== RÉFÉRENTIELS MODERNE - DESIGN SYSTEM ===== */\n    .referentiels-container {\n      padding: var(--spacing-8);\n      max-width: 1400px;\n      margin: 0 auto;\n      background: transparent;\n    }\n\n    /* === HEADER === */\n    .referentiels-header {\n      background: var(--gradient-primary);\n      border-radius: var(--border-radius-2xl);\n      padding: var(--spacing-8);\n      margin-bottom: var(--spacing-8);\n      color: var(--white);\n      box-shadow: var(--shadow-blue-lg);\n      position: relative;\n      overflow: hidden;\n    }\n\n    .referentiels-header::before {\n      content: '';\n      position: absolute;\n      top: 0;\n      left: 0;\n      right: 0;\n      bottom: 0;\n      background: url('data:image/svg+xml,<svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 100 100\"><defs><pattern id=\"dots\" width=\"20\" height=\"20\" patternUnits=\"userSpaceOnUse\"><circle cx=\"10\" cy=\"10\" r=\"1\" fill=\"rgba(255,255,255,0.1)\"/></pattern></defs><rect width=\"100\" height=\"100\" fill=\"url(%23dots)\"/></svg>');\n      opacity: 0.3;\n    }\n\n    .referentiels-title {\n      font-size: var(--font-size-3xl);\n      font-weight: var(--font-weight-extrabold);\n      margin: 0 0 var(--spacing-2) 0;\n      display: flex;\n      align-items: center;\n      gap: var(--spacing-3);\n    }\n\n    /* === ONGLETS MODERNES === */\n    .tabs {\n      display: flex;\n      background: var(--white);\n      border-radius: var(--border-radius-xl);\n      padding: var(--spacing-2);\n      margin-bottom: var(--spacing-8);\n      box-shadow: var(--shadow-base);\n      overflow-x: auto;\n    }\n\n    .tab-button {\n      padding: var(--spacing-3) var(--spacing-6);\n      border: none;\n      background: transparent;\n      cursor: pointer;\n      border-radius: var(--border-radius-lg);\n      font-weight: var(--font-weight-medium);\n      color: var(--gray-600);\n      transition: all var(--transition-fast);\n      white-space: nowrap;\n      display: flex;\n      align-items: center;\n      gap: var(--spacing-2);\n    }\n\n    .tab-button.active {\n      background: var(--primary-600);\n      color: var(--white);\n      box-shadow: var(--shadow-md);\n    }\n\n    .tab-button:hover:not(.active) {\n      background: var(--primary-50);\n      color: var(--primary-600);\n    }\n\n    /* === PANEL HEADER === */\n    .panel-header {\n      display: flex;\n      justify-content: space-between;\n      align-items: center;\n      margin-bottom: var(--spacing-8);\n      background: var(--white);\n      padding: var(--spacing-6);\n      border-radius: var(--border-radius-xl);\n      box-shadow: var(--shadow-base);\n    }\n\n    .panel-header h3 {\n      font-size: var(--font-size-xl);\n      font-weight: var(--font-weight-bold);\n      color: var(--gray-900);\n      margin: 0;\n      display: flex;\n      align-items: center;\n      gap: var(--spacing-2);\n    }\n\n    /* === GRILLE D'ÉLÉMENTS === */\n    .items-grid {\n      display: grid;\n      grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));\n      gap: var(--spacing-6);\n    }\n\n    .item-card {\n      background: var(--white);\n      padding: var(--spacing-6);\n      border-radius: var(--border-radius-xl);\n      box-shadow: var(--shadow-base);\n      transition: all var(--transition-base);\n      position: relative;\n      overflow: hidden;\n    }\n\n    .item-card::before {\n      content: '';\n      position: absolute;\n      top: 0;\n      left: 0;\n      right: 0;\n      height: 4px;\n      background: var(--gradient-primary);\n    }\n\n    .item-card:hover {\n      transform: translateY(-4px);\n      box-shadow: var(--shadow-lg);\n    }\n\n    .item-header {\n      display: flex;\n      justify-content: space-between;\n      align-items: center;\n      margin-bottom: var(--spacing-4);\n    }\n\n    .item-header h4 {\n      font-size: var(--font-size-lg);\n      font-weight: var(--font-weight-bold);\n      color: var(--gray-900);\n      margin: 0;\n    }\n\n    /* === BADGES MODERNES === */\n    .badge {\n      padding: var(--spacing-1) var(--spacing-3);\n      border-radius: var(--border-radius-full);\n      font-size: var(--font-size-xs);\n      font-weight: var(--font-weight-medium);\n      text-transform: uppercase;\n      letter-spacing: 0.025em;\n    }\n\n    .badge-success {\n      background: var(--success-100);\n      color: var(--success-800);\n      border: 1px solid var(--success-200);\n    }\n\n    .badge-warning {\n      background: var(--warning-100);\n      color: var(--warning-800);\n      border: 1px solid var(--warning-200);\n    }\n\n    .badge-danger {\n      background: var(--error-100);\n      color: var(--error-800);\n      border: 1px solid var(--error-200);\n    }\n\n    .item-stats {\n      margin: var(--spacing-4) 0;\n      color: var(--gray-600);\n      font-size: var(--font-size-sm);\n      font-weight: var(--font-weight-medium);\n    }\n\n    .item-actions {\n      display: flex;\n      gap: var(--spacing-2);\n      margin-top: var(--spacing-4);\n    }\n\n    /* === TAUX TVA === */\n    .tva-rate {\n      text-align: center;\n      margin: var(--spacing-4) 0;\n      padding: var(--spacing-4);\n      background: var(--primary-50);\n      border-radius: var(--border-radius-lg);\n    }\n\n    .rate {\n      font-size: var(--font-size-4xl);\n      font-weight: var(--font-weight-extrabold);\n      color: var(--primary-600);\n      display: block;\n    }\n\n    /* === IMAGES === */\n    .marque-logo, .forme-image {\n      text-align: center;\n      margin: var(--spacing-4) 0;\n      padding: var(--spacing-3);\n      background: var(--gray-50);\n      border-radius: var(--border-radius-lg);\n    }\n\n    .logo-img, .forme-img {\n      max-width: 120px;\n      max-height: 80px;\n      object-fit: contain;\n      border-radius: var(--border-radius-md);\n    }\n\n    /* === MODAL MODERNE === */\n    .modal-overlay {\n      position: fixed;\n      top: 0;\n      left: 0;\n      width: 100%;\n      height: 100%;\n      background: rgba(0, 0, 0, 0.6);\n      backdrop-filter: blur(4px);\n      display: flex;\n      align-items: center;\n      justify-content: center;\n      z-index: var(--z-modal);\n      animation: fadeIn 0.2s ease-out;\n    }\n\n    .modal-content {\n      background: var(--white);\n      border-radius: var(--border-radius-2xl);\n      box-shadow: var(--shadow-2xl);\n      max-width: 600px;\n      width: 90%;\n      max-height: 90vh;\n      overflow-y: auto;\n      animation: slideUp 0.3s ease-out;\n    }\n\n    .modal-header {\n      display: flex;\n      justify-content: space-between;\n      align-items: center;\n      padding: var(--spacing-6);\n      border-bottom: 1px solid var(--gray-200);\n      background: linear-gradient(135deg, var(--gray-50) 0%, var(--white) 100%);\n    }\n\n    .modal-header h3 {\n      font-size: var(--font-size-xl);\n      font-weight: var(--font-weight-bold);\n      color: var(--gray-900);\n      margin: 0;\n    }\n\n    .close-btn {\n      background: none;\n      border: none;\n      font-size: var(--font-size-xl);\n      cursor: pointer;\n      color: var(--gray-400);\n      padding: var(--spacing-2);\n      border-radius: var(--border-radius-lg);\n      transition: var(--transition-fast);\n    }\n\n    .close-btn:hover {\n      background: var(--gray-100);\n      color: var(--gray-600);\n    }\n\n    .modal-form {\n      padding: var(--spacing-6);\n    }\n\n    /* === FORMULAIRES === */\n    .form-group {\n      margin-bottom: var(--spacing-6);\n    }\n\n    .form-group label {\n      display: block;\n      margin-bottom: var(--spacing-2);\n      font-weight: var(--font-weight-semibold);\n      color: var(--gray-700);\n      font-size: var(--font-size-sm);\n    }\n\n    .form-control {\n      width: 100%;\n      padding: var(--spacing-3) var(--spacing-4);\n      border: 2px solid var(--gray-300);\n      border-radius: var(--border-radius-lg);\n      font-size: var(--font-size-base);\n      transition: all var(--transition-fast);\n      background: var(--white);\n    }\n\n    .form-control:focus {\n      outline: none;\n      border-color: var(--primary-500);\n      box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);\n      transform: translateY(-1px);\n    }\n\n    .form-actions {\n      display: flex;\n      gap: var(--spacing-3);\n      justify-content: flex-end;\n      padding: var(--spacing-6);\n      border-top: 1px solid var(--gray-200);\n      background: var(--gray-50);\n    }\n\n    /* === BOUTONS === */\n    .btn {\n      padding: var(--spacing-3) var(--spacing-4);\n      border: none;\n      border-radius: var(--border-radius-lg);\n      cursor: pointer;\n      font-weight: var(--font-weight-medium);\n      transition: all var(--transition-fast);\n      display: inline-flex;\n      align-items: center;\n      gap: var(--spacing-2);\n    }\n\n    .btn-primary {\n      background: var(--gradient-primary);\n      color: var(--white);\n      box-shadow: var(--shadow-blue);\n    }\n\n    .btn-primary:hover {\n      background: linear-gradient(135deg, #5a67d8 0%, #667eea 100%);\n      transform: translateY(-1px);\n      box-shadow: var(--shadow-blue-lg);\n    }\n\n    .btn-secondary {\n      background: var(--white);\n      color: var(--gray-700);\n      border: 2px solid var(--gray-300);\n      box-shadow: var(--shadow-sm);\n    }\n\n    .btn-secondary:hover {\n      background: var(--gray-50);\n      border-color: var(--gray-400);\n      transform: translateY(-1px);\n    }\n\n    .btn-danger {\n      background: linear-gradient(135deg, var(--error-600), var(--error-500));\n      color: var(--white);\n      box-shadow: var(--shadow-sm);\n    }\n\n    .btn-danger:hover {\n      background: linear-gradient(135deg, var(--error-700), var(--error-600));\n      transform: translateY(-1px);\n      box-shadow: var(--shadow-md);\n    }\n\n    .btn-sm {\n      padding: var(--spacing-2) var(--spacing-3);\n      font-size: var(--font-size-xs);\n    }\n\n    /* === MODE CONSULTATION === */\n    .read-only-notice {\n      display: flex;\n      align-items: center;\n      gap: var(--spacing-2);\n    }\n\n    .badge-info {\n      background: linear-gradient(135deg, #3b82f6, #1d4ed8);\n      color: var(--white);\n      padding: var(--spacing-2) var(--spacing-3);\n      border-radius: var(--border-radius-full);\n      font-size: var(--font-size-xs);\n      font-weight: 600;\n      display: inline-flex;\n      align-items: center;\n      gap: var(--spacing-1);\n      box-shadow: var(--shadow-sm);\n    }\n\n    .badge-success {\n      background: linear-gradient(135deg, #10b981, #059669);\n      color: var(--white);\n      padding: var(--spacing-2) var(--spacing-3);\n      border-radius: var(--border-radius-full);\n      font-size: var(--font-size-xs);\n      font-weight: 600;\n      display: inline-flex;\n      align-items: center;\n      gap: var(--spacing-1);\n      box-shadow: var(--shadow-sm);\n    }\n\n    .supplier-actions {\n      display: flex;\n      align-items: center;\n      gap: var(--spacing-3);\n      flex-wrap: wrap;\n    }\n\n    .btn-outline-primary {\n      background: transparent;\n      color: var(--primary-600);\n      border: 2px solid var(--primary-600);\n      padding: var(--spacing-2) var(--spacing-4);\n      border-radius: var(--border-radius-lg);\n      font-weight: 600;\n      transition: all 0.3s ease;\n    }\n\n    .btn-outline-primary:hover {\n      background: var(--primary-600);\n      color: var(--white);\n      transform: translateY(-1px);\n      box-shadow: var(--shadow-md);\n    }\n\n    /* === RESPONSIVE === */\n    @media (max-width: 768px) {\n      .referentiels-container {\n        padding: var(--spacing-4);\n      }\n\n      .items-grid {\n        grid-template-columns: 1fr;\n      }\n\n      .panel-header {\n        flex-direction: column;\n        gap: var(--spacing-4);\n        align-items: stretch;\n      }\n\n      .tabs {\n        flex-wrap: wrap;\n      }\n\n      .modal-content {\n        margin: var(--spacing-4);\n        max-width: calc(100vw - 2rem);\n      }\n\n      .item-actions {\n        flex-direction: column;\n      }\n    }\n  `]\n})\nexport class ReferentielsComponent implements OnInit {\n  // Signals pour les données\n  categories = signal<Categorie[]>([]);\n  sousCategories = signal<SousCategorie[]>([]);\n  marques = signal<Marque[]>([]);\n  formes = signal<Forme[]>([]);\n  tauxTVA = signal<TauxTVA[]>([]);\n\n  // Signals pour l'interface\n  activeTab = signal('categories');\n  showModal = signal(false);\n  modalType = signal('');\n  modalTitle = signal('');\n  isEditMode = signal(false);\n  isLoading = signal(false);\n  selectedItem = signal<any>(null);\n\n  // Vérification des permissions\n  isAdmin = signal(false);\n\n  // Configuration des onglets\n  tabs = [\n    { id: 'categories', label: 'Catégories', icon: '📁' },\n    { id: 'sous-categories', label: 'Sous-catégories', icon: '📂' },\n    { id: 'marques', label: 'Marques', icon: '🏷️' },\n    { id: 'formes', label: 'Formes', icon: '🔷' },\n    { id: 'tva', label: 'Taux TVA', icon: '💰' }\n  ];\n\n  constructor(\n    private categorieService: CategorieService,\n    private sousCategorieService: SousCategorieService,\n    private marqueService: MarqueService,\n    private formeService: FormeService,\n    private tauxTVAService: TauxTVAService,\n    private authService: AuthService,\n    private router: Router,\n    private imageUrlService: ImageUrlService\n  ) {}\n\n  ngOnInit(): void {\n    this.checkUserRole();\n    this.loadAllData();\n  }\n\n  /**\n   * Vérifier le rôle de l'utilisateur\n   */\n  private checkUserRole(): void {\n    const currentUser = this.authService.getCurrentUser();\n    this.isAdmin.set(currentUser?.role === 'Admin');\n    console.log('👤 Rôle utilisateur:', currentUser?.role, '- Admin:', this.isAdmin());\n  }\n\n  /**\n   * Charger toutes les données\n   */\n  loadAllData(): void {\n    this.loadCategories();\n    this.loadSousCategories();\n    this.loadMarques();\n    this.loadFormes();\n    this.loadTauxTVA();\n  }\n\n  /**\n   * Charger les catégories\n   */\n  loadCategories(): void {\n    this.categorieService.getAll().subscribe({\n      next: (data) => {\n        console.log('🔍 Catégories reçues du backend:', data);\n        this.categories.set(Array.isArray(data) ? data : []);\n        console.log('✅ Catégories chargées:', this.categories().length);\n      },\n      error: (error) => {\n        console.error('❌ Erreur catégories:', error);\n        this.categories.set([]);\n      }\n    });\n  }\n\n  /**\n   * Charger les sous-catégories\n   */\n  loadSousCategories(): void {\n    // Récupérer l'ID du fournisseur connecté\n    const currentUser = this.authService.currentUser();\n    const fournisseurId = currentUser?.role === 'Fournisseur' ? currentUser.id : undefined;\n\n    console.log('🔍 Chargement sous-catégories...');\n    console.log('👤 Utilisateur connecté:', currentUser);\n    console.log('🏢 Fournisseur ID à utiliser:', fournisseurId);\n\n    // Alert pour être sûr de voir l'ID\n    if (currentUser?.role === 'Fournisseur') {\n      console.log('🚨 FOURNISSEUR CONNECTÉ - ID:', currentUser.id);\n      // Décommentez la ligne suivante pour voir l'ID dans une popup\n      // alert(`Fournisseur connecté - ID: ${currentUser.id}`);\n    }\n\n    this.sousCategorieService.getAll(fournisseurId).subscribe({\n      next: (data) => {\n        console.log('🔍 Sous-catégories reçues du backend:', data);\n        console.log('👤 Fournisseur ID utilisé:', fournisseurId);\n\n        // Log détaillé pour chaque sous-catégorie\n        data.forEach(sc => {\n          console.log(`  📋 ${sc.nom}: ${sc.produitsCount} produits`);\n        });\n\n        this.sousCategories.set(Array.isArray(data) ? data : []);\n        console.log('✅ Sous-catégories chargées:', this.sousCategories().length);\n      },\n      error: (error) => {\n        console.error('❌ Erreur sous-catégories:', error);\n        this.sousCategories.set([]);\n      }\n    });\n  }\n\n  /**\n   * Charger les marques\n   */\n  loadMarques(): void {\n    this.marqueService.getAll().subscribe({\n      next: (data) => {\n        console.log('🔍 Marques reçues du backend:', data);\n        this.marques.set(Array.isArray(data) ? data : []);\n        console.log('✅ Marques chargées:', this.marques().length);\n      },\n      error: (error) => {\n        console.error('❌ Erreur marques:', error);\n        this.marques.set([]);\n      }\n    });\n  }\n\n  /**\n   * Charger les formes\n   */\n  loadFormes(): void {\n    this.formeService.getAll().subscribe({\n      next: (data) => {\n        console.log('🔍 Formes reçues du backend:', data);\n        this.formes.set(Array.isArray(data) ? data : []);\n        console.log('✅ Formes chargées:', this.formes().length);\n      },\n      error: (error) => {\n        console.error('❌ Erreur formes:', error);\n        this.formes.set([]);\n      }\n    });\n  }\n\n  /**\n   * Charger les taux TVA\n   */\n  loadTauxTVA(): void {\n    this.tauxTVAService.getAll().subscribe({\n      next: (data) => {\n        console.log('✅ Taux TVA chargés:', data);\n        this.tauxTVA.set(Array.isArray(data) ? data : []);\n      },\n      error: (error) => {\n        console.error('❌ Erreur taux TVA:', error);\n        this.tauxTVA.set([]);\n      }\n    });\n  }\n\n  /**\n   * Changer d'onglet\n   */\n  setActiveTab(tabId: string): void {\n    this.activeTab.set(tabId);\n  }\n\n  /**\n   * Ouvrir le formulaire de catégorie\n   */\n  openCategorieForm(): void {\n    this.modalType.set('categorie');\n    this.modalTitle.set('Nouvelle Catégorie');\n    this.isEditMode.set(false);\n    this.selectedItem.set(null);\n    this.showModal.set(true);\n  }\n\n  /**\n   * Ouvrir le formulaire de sous-catégorie\n   */\n  openSousCategorieForm(): void {\n    this.modalType.set('sous-categorie');\n    this.modalTitle.set('Nouvelle Sous-catégorie');\n    this.isEditMode.set(false);\n    this.selectedItem.set(null);\n    this.showModal.set(true);\n  }\n\n  /**\n   * Ouvrir le formulaire de marque\n   */\n  openMarqueForm(): void {\n    this.modalType.set('marque');\n    this.modalTitle.set('Nouvelle Marque');\n    this.isEditMode.set(false);\n    this.selectedItem.set(null);\n    this.showModal.set(true);\n  }\n\n  /**\n   * Ouvrir le formulaire de forme\n   */\n  openFormeForm(): void {\n    this.modalType.set('forme');\n    this.modalTitle.set('Nouvelle Forme');\n    this.isEditMode.set(false);\n    this.selectedItem.set(null);\n    this.showModal.set(true);\n  }\n\n  /**\n   * Ouvrir le formulaire de taux TVA\n   */\n  openTauxTVAForm(): void {\n    this.modalType.set('tva');\n    this.modalTitle.set('Nouveau Taux TVA');\n    this.isEditMode.set(false);\n    this.selectedItem.set(null);\n    this.showModal.set(true);\n  }\n\n  /**\n   * Éditer une catégorie\n   */\n  editCategorie(categorie: Categorie): void {\n    this.modalType.set('categorie');\n    this.modalTitle.set('Modifier Catégorie');\n    this.isEditMode.set(true);\n    this.selectedItem.set(categorie);\n    this.showModal.set(true);\n  }\n\n  /**\n   * Éditer une sous-catégorie\n   */\n  editSousCategorie(sousCategorie: SousCategorie): void {\n    this.modalType.set('sous-categorie');\n    this.modalTitle.set('Modifier Sous-catégorie');\n    this.isEditMode.set(true);\n    this.selectedItem.set(sousCategorie);\n    this.showModal.set(true);\n  }\n\n  /**\n   * Éditer une marque\n   */\n  editMarque(marque: Marque): void {\n    this.modalType.set('marque');\n    this.modalTitle.set('Modifier Marque');\n    this.isEditMode.set(true);\n    this.selectedItem.set(marque);\n    this.showModal.set(true);\n  }\n\n  /**\n   * Éditer une forme\n   */\n  editForme(forme: Forme): void {\n    this.modalType.set('forme');\n    this.modalTitle.set('Modifier Forme');\n    this.isEditMode.set(true);\n    this.selectedItem.set(forme);\n    this.showModal.set(true);\n  }\n\n  /**\n   * Éditer un taux TVA\n   */\n  editTauxTVA(taux: TauxTVA): void {\n    this.modalType.set('tva');\n    this.modalTitle.set('Modifier Taux TVA');\n    this.isEditMode.set(true);\n    this.selectedItem.set(taux);\n    this.showModal.set(true);\n  }\n\n  /**\n   * Fermer le modal\n   */\n  closeModal(): void {\n    this.showModal.set(false);\n    this.modalType.set('');\n    this.modalTitle.set('');\n    this.isEditMode.set(false);\n    this.selectedItem.set(null);\n  }\n\n  /**\n   * Soumettre le formulaire\n   */\n  onSubmit(form: any): void {\n    if (!form.valid) return;\n\n    this.isLoading.set(true);\n    const formData = form.value;\n    const modalType = this.modalType();\n\n    switch (modalType) {\n      case 'categorie':\n        this.handleCategorieSubmit(formData);\n        break;\n      case 'sous-categorie':\n        this.handleSousCategorieSubmit(formData);\n        break;\n      case 'marque':\n        this.handleMarqueSubmit(formData);\n        break;\n      case 'forme':\n        this.handleFormeSubmit(formData);\n        break;\n      case 'tva':\n        this.handleTauxTVASubmit(formData);\n        break;\n    }\n  }\n\n  /**\n   * Gérer la soumission de catégorie\n   */\n  private handleCategorieSubmit(formData: any): void {\n    const categorieData: CategorieCreate = {\n      nom: formData.nom,\n      description: formData.description\n    };\n\n    if (this.isEditMode()) {\n      const selectedItem = this.selectedItem();\n      this.categorieService.update(selectedItem.id, categorieData).subscribe({\n        next: () => {\n          this.loadCategories();\n          this.closeModal();\n          this.isLoading.set(false);\n          alert('Catégorie mise à jour avec succès');\n        },\n        error: (error) => {\n          console.error('Erreur mise à jour catégorie:', error);\n          this.isLoading.set(false);\n          alert('Erreur lors de la mise à jour');\n        }\n      });\n    } else {\n      this.categorieService.create(categorieData).subscribe({\n        next: () => {\n          this.loadCategories();\n          this.closeModal();\n          this.isLoading.set(false);\n          alert('Catégorie créée avec succès');\n        },\n        error: (error) => {\n          console.error('Erreur création catégorie:', error);\n          this.isLoading.set(false);\n          alert('Erreur lors de la création');\n        }\n      });\n    }\n  }\n\n  /**\n   * Gérer la soumission de sous-catégorie\n   */\n  private handleSousCategorieSubmit(formData: any): void {\n    const sousCategorieData: SousCategorieCreate = {\n      nom: formData.nom,\n      categorieId: +formData.categorieId,\n      description: formData.description,\n      estValidee: formData.estValidee || false\n    };\n\n    if (this.isEditMode()) {\n      const selectedItem = this.selectedItem();\n      this.sousCategorieService.update(selectedItem.id, sousCategorieData).subscribe({\n        next: () => {\n          this.loadSousCategories();\n          this.closeModal();\n          this.isLoading.set(false);\n          alert('Sous-catégorie mise à jour avec succès');\n        },\n        error: (error) => {\n          console.error('Erreur mise à jour sous-catégorie:', error);\n          this.isLoading.set(false);\n          alert('Erreur lors de la mise à jour');\n        }\n      });\n    } else {\n      this.sousCategorieService.create(sousCategorieData).subscribe({\n        next: () => {\n          this.loadSousCategories();\n          this.closeModal();\n          this.isLoading.set(false);\n          alert('Sous-catégorie créée avec succès');\n        },\n        error: (error) => {\n          console.error('Erreur création sous-catégorie:', error);\n          this.isLoading.set(false);\n          alert('Erreur lors de la création');\n        }\n      });\n    }\n  }\n\n  /**\n   * Gérer la soumission de marque\n   */\n  private handleMarqueSubmit(formData: any): void {\n    const marqueData: MarqueCreate = {\n      name: formData.name,\n      logo: formData.logo\n    };\n\n    if (this.isEditMode()) {\n      const selectedItem = this.selectedItem();\n      this.marqueService.update(selectedItem.id, marqueData).subscribe({\n        next: () => {\n          this.loadMarques();\n          this.closeModal();\n          this.isLoading.set(false);\n          alert('Marque mise à jour avec succès');\n        },\n        error: (error) => {\n          console.error('Erreur mise à jour marque:', error);\n          this.isLoading.set(false);\n          alert('Erreur lors de la mise à jour');\n        }\n      });\n    } else {\n      this.marqueService.create(marqueData).subscribe({\n        next: () => {\n          this.loadMarques();\n          this.closeModal();\n          this.isLoading.set(false);\n          alert('Marque créée avec succès');\n        },\n        error: (error) => {\n          console.error('Erreur création marque:', error);\n          this.isLoading.set(false);\n          alert('Erreur lors de la création');\n        }\n      });\n    }\n  }\n\n  /**\n   * Gérer la soumission de forme\n   */\n  private handleFormeSubmit(formData: any): void {\n    const formeData: FormeCreate = {\n      nom: formData.nom,\n      categorieId: +formData.categorieId,\n      imageUrl: formData.imageUrl\n    };\n\n    if (this.isEditMode()) {\n      const selectedItem = this.selectedItem();\n      this.formeService.update(selectedItem.id, formeData).subscribe({\n        next: () => {\n          this.loadFormes();\n          this.closeModal();\n          this.isLoading.set(false);\n          alert('Forme mise à jour avec succès');\n        },\n        error: (error) => {\n          console.error('Erreur mise à jour forme:', error);\n          this.isLoading.set(false);\n          alert('Erreur lors de la mise à jour');\n        }\n      });\n    } else {\n      this.formeService.create(formeData).subscribe({\n        next: () => {\n          this.loadFormes();\n          this.closeModal();\n          this.isLoading.set(false);\n          alert('Forme créée avec succès');\n        },\n        error: (error) => {\n          console.error('Erreur création forme:', error);\n          this.isLoading.set(false);\n          alert('Erreur lors de la création');\n        }\n      });\n    }\n  }\n\n  /**\n   * Gérer la soumission de taux TVA\n   */\n  private handleTauxTVASubmit(formData: any): void {\n    const tauxTVAData: TauxTVACreate = {\n      libelle: formData.libelle,\n      taux: +formData.taux,\n      description: formData.description,\n      estActif: formData.estActif || false,\n      dateEffet: formData.dateEffet,\n      dateFin: formData.dateFin\n    };\n\n    if (this.isEditMode()) {\n      const selectedItem = this.selectedItem();\n      this.tauxTVAService.update(selectedItem.id, tauxTVAData).subscribe({\n        next: () => {\n          this.loadTauxTVA();\n          this.closeModal();\n          this.isLoading.set(false);\n          alert('Taux TVA mis à jour avec succès');\n        },\n        error: (error) => {\n          console.error('Erreur mise à jour taux TVA:', error);\n          this.isLoading.set(false);\n          alert('Erreur lors de la mise à jour');\n        }\n      });\n    } else {\n      this.tauxTVAService.create(tauxTVAData).subscribe({\n        next: () => {\n          this.loadTauxTVA();\n          this.closeModal();\n          this.isLoading.set(false);\n          alert('Taux TVA créé avec succès');\n        },\n        error: (error) => {\n          console.error('Erreur création taux TVA:', error);\n          this.isLoading.set(false);\n          alert('Erreur lors de la création');\n        }\n      });\n    }\n  }\n\n  /**\n   * Supprimer une catégorie\n   */\n  deleteCategorie(categorie: Categorie): void {\n    if (confirm(`Êtes-vous sûr de vouloir supprimer la catégorie \"${categorie.nom}\" ?`)) {\n      this.categorieService.delete(categorie.id).subscribe({\n        next: () => {\n          this.loadCategories();\n          alert('Catégorie supprimée avec succès');\n        },\n        error: (error) => {\n          console.error('Erreur suppression catégorie:', error);\n          alert('Erreur lors de la suppression');\n        }\n      });\n    }\n  }\n\n  /**\n   * Supprimer une sous-catégorie\n   */\n  deleteSousCategorie(sousCategorie: SousCategorie): void {\n    if (confirm(`Êtes-vous sûr de vouloir supprimer la sous-catégorie \"${sousCategorie.nom}\" ?`)) {\n      this.sousCategorieService.delete(sousCategorie.id).subscribe({\n        next: () => {\n          this.loadSousCategories();\n          alert('Sous-catégorie supprimée avec succès');\n        },\n        error: (error) => {\n          console.error('Erreur suppression sous-catégorie:', error);\n          alert('Erreur lors de la suppression');\n        }\n      });\n    }\n  }\n\n  /**\n   * Supprimer une marque\n   */\n  deleteMarque(marque: Marque): void {\n    if (confirm(`Êtes-vous sûr de vouloir supprimer la marque \"${marque.name}\" ?`)) {\n      this.marqueService.delete(marque.id).subscribe({\n        next: () => {\n          this.loadMarques();\n          alert('Marque supprimée avec succès');\n        },\n        error: (error) => {\n          console.error('Erreur suppression marque:', error);\n          alert('Erreur lors de la suppression');\n        }\n      });\n    }\n  }\n\n  /**\n   * Supprimer une forme\n   */\n  deleteForme(forme: Forme): void {\n    if (confirm(`Êtes-vous sûr de vouloir supprimer la forme \"${forme.nom}\" ?`)) {\n      this.formeService.delete(forme.id).subscribe({\n        next: () => {\n          this.loadFormes();\n          alert('Forme supprimée avec succès');\n        },\n        error: (error) => {\n          console.error('Erreur suppression forme:', error);\n          alert('Erreur lors de la suppression');\n        }\n      });\n    }\n  }\n\n  /**\n   * Supprimer un taux TVA\n   */\n  deleteTauxTVA(taux: TauxTVA): void {\n    if (confirm(`Êtes-vous sûr de vouloir supprimer le taux TVA \"${taux.libelle}\" ?`)) {\n      this.tauxTVAService.delete(taux.id).subscribe({\n        next: () => {\n          this.loadTauxTVA();\n          alert('Taux TVA supprimé avec succès');\n        },\n        error: (error) => {\n          console.error('Erreur suppression taux TVA:', error);\n          alert('Erreur lors de la suppression');\n        }\n      });\n    }\n  }\n\n  /**\n   * Demander l'ajout d'une nouvelle catégorie (pour fournisseurs)\n   */\n  requestNewCategorie(): void {\n    console.log('🔄 Redirection vers la page de demande de catégorie...');\n    this.router.navigate(['/dashboard/demandes-categories']);\n  }\n\n  /**\n   * Demander l'ajout d'une nouvelle sous-catégorie (pour fournisseurs)\n   */\n  requestNewSousCategorie(): void {\n    console.log('🔄 Redirection vers la page de demande de catégorie (onglet sous-catégorie)...');\n    this.router.navigate(['/dashboard/demandes-categories']);\n  }\n\n  /**\n   * Obtenir l'URL du logo d'une marque\n   */\n  getMarqueLogoUrl(logoPath: string | null | undefined): string {\n    return this.imageUrlService.getMarqueLogoUrl(logoPath);\n  }\n\n  /**\n   * Obtenir l'URL de l'image d'une forme\n   */\n  getFormeImageUrl(imagePath: string | null | undefined): string {\n    return this.imageUrlService.getFullImageUrl(imagePath);\n  }\n\n  /**\n   * Gérer les erreurs d'images\n   */\n  onImageError(event: any): void {\n    console.warn('Erreur de chargement d\\'image:', event.target.src);\n    event.target.src = 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNDAiIGhlaWdodD0iNDAiIHZpZXdCb3g9IjAgMCA0MCA0MCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHJlY3Qgd2lkdGg9IjQwIiBoZWlnaHQ9IjQwIiBmaWxsPSIjRjNGNEY2Ii8+CjxwYXRoIGQ9Ik0yMCAyOEMyNC40MTgzIDI4IDI4IDI0LjQxODMgMjggMjBDMjggMTUuNTgxNyAyNC40MTgzIDEyIDIwIDEyQzE1LjU4MTcgMTIgMTIgMTUuNTgxNyAxMiAyMEMxMiAyNC40MTgzIDE1LjU4MTcgMjggMjAgMjhaIiBmaWxsPSIjOUNBM0FGIi8+Cjwvc3ZnPgo=';\n  }\n\n  /**\n   * Formater une date\n   */\n  formatDate(dateString: string): string {\n    return new Date(dateString).toLocaleDateString('fr-FR');\n  }\n}"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAyCQ,IAAA,yBAAA,GAAA,UAAA,CAAA;AAIE,IAAA,qBAAA,SAAA,SAAA,kEAAA;AAAA,YAAA,SAAA,wBAAA,GAAA,EAAA;AAAA,YAAA,SAAA,wBAAA;AAAA,aAAA,sBAAS,OAAA,aAAA,OAAA,EAAA,CAAoB;IAAA,CAAA;AAE7B,IAAA,iBAAA,CAAA;AACF,IAAA,uBAAA;;;;;AAJE,IAAA,sBAAA,UAAA,OAAA,UAAA,MAAA,OAAA,EAAA;AAGA,IAAA,oBAAA;AAAA,IAAA,6BAAA,KAAA,OAAA,MAAA,KAAA,OAAA,OAAA,GAAA;;;;;;AAWE,IAAA,yBAAA,GAAA,UAAA,EAAA;AAGE,IAAA,qBAAA,SAAA,SAAA,yEAAA;AAAA,MAAA,wBAAA,GAAA;AAAA,YAAA,SAAA,wBAAA,CAAA;AAAA,aAAA,sBAAS,OAAA,kBAAA,CAAmB;IAAA,CAAA;AAE5B,IAAA,iBAAA,GAAA,gCAAA;AACF,IAAA,uBAAA;;;;;;AACA,IAAA,yBAAA,GAAA,OAAA,EAAA,EAAiD,GAAA,UAAA,EAAA;AAG7C,IAAA,qBAAA,SAAA,SAAA,sEAAA;AAAA,MAAA,wBAAA,GAAA;AAAA,YAAA,SAAA,wBAAA,CAAA;AAAA,aAAA,sBAAS,OAAA,oBAAA,CAAqB;IAAA,CAAA;AAE9B,IAAA,iBAAA,GAAA,gDAAA;AACF,IAAA,uBAAA;AACA,IAAA,yBAAA,GAAA,QAAA,EAAA;AAA+B,IAAA,iBAAA,GAAA,mCAAA;AAAqB,IAAA,uBAAA,EAAO;;;;;AAY3D,IAAA,yBAAA,GAAA,GAAA;AAAiC,IAAA,iBAAA,CAAA;AAA2B,IAAA,uBAAA;;;;AAA3B,IAAA,oBAAA;AAAA,IAAA,4BAAA,aAAA,WAAA;;;;;;AAIjC,IAAA,yBAAA,GAAA,OAAA,EAAA,EAA4C,GAAA,UAAA,EAAA;AACD,IAAA,qBAAA,SAAA,SAAA,6EAAA;AAAA,MAAA,wBAAA,GAAA;AAAA,YAAA,eAAA,wBAAA,EAAA;AAAA,YAAA,SAAA,wBAAA,CAAA;AAAA,aAAA,sBAAS,OAAA,cAAA,YAAA,CAAwB;IAAA,CAAA;AACxE,IAAA,iBAAA,GAAA,yBAAA;AACF,IAAA,uBAAA;AACA,IAAA,yBAAA,GAAA,UAAA,EAAA;AAAsC,IAAA,qBAAA,SAAA,SAAA,6EAAA;AAAA,MAAA,wBAAA,GAAA;AAAA,YAAA,eAAA,wBAAA,EAAA;AAAA,YAAA,SAAA,wBAAA,CAAA;AAAA,aAAA,sBAAS,OAAA,gBAAA,YAAA,CAA0B;IAAA,CAAA;AACvE,IAAA,iBAAA,GAAA,6BAAA;AACF,IAAA,uBAAA,EAAS;;;;;AAjBb,IAAA,yBAAA,GAAA,OAAA,EAAA,EAA8D,GAAA,OAAA,EAAA,EACnC,GAAA,IAAA;AACnB,IAAA,iBAAA,CAAA;AAAmB,IAAA,uBAAA;AACvB,IAAA,yBAAA,GAAA,QAAA,EAAA;AACE,IAAA,iBAAA,CAAA;AACF,IAAA,uBAAA,EAAO;AAET,IAAA,qBAAA,GAAA,iDAAA,GAAA,GAAA,KAAA,EAAA;AACA,IAAA,yBAAA,GAAA,OAAA,EAAA,EAAwB,GAAA,MAAA;AAChB,IAAA,iBAAA,CAAA;AAAwD,IAAA,uBAAA,EAAO;AAEvE,IAAA,qBAAA,IAAA,oDAAA,GAAA,GAAA,OAAA,EAAA;AAQF,IAAA,uBAAA;;;;;AAjBQ,IAAA,oBAAA,CAAA;AAAA,IAAA,4BAAA,aAAA,GAAA;AACgB,IAAA,oBAAA;AAAA,IAAA,qBAAA,aAAA,aAAA,kBAAA,eAAA;AAClB,IAAA,oBAAA;AAAA,IAAA,6BAAA,KAAA,aAAA,aAAA,eAAA,cAAA,GAAA;AAGA,IAAA,oBAAA;AAAA,IAAA,qBAAA,QAAA,aAAA,WAAA;AAEI,IAAA,oBAAA,CAAA;AAAA,IAAA,6BAAA,IAAA,aAAA,uBAAA,GAAA,qBAAA;AAEmB,IAAA,oBAAA;AAAA,IAAA,qBAAA,QAAA,OAAA,QAAA,CAAA;;;;;AAjCjC,IAAA,yBAAA,GAAA,OAAA,EAAA,EAA4D,GAAA,OAAA,EAAA,EAChC,GAAA,IAAA;AACpB,IAAA,iBAAA,GAAA,yBAAA;AAAa,IAAA,uBAAA;AACjB,IAAA,qBAAA,GAAA,gDAAA,GAAA,GAAA,UAAA,EAAA,EAIC,GAAA,6CAAA,GAAA,GAAA,OAAA,EAAA;AAYH,IAAA,uBAAA;AAEA,IAAA,yBAAA,GAAA,OAAA,EAAA;AACE,IAAA,qBAAA,GAAA,6CAAA,IAAA,GAAA,OAAA,EAAA;AAoBF,IAAA,uBAAA,EAAM;;;;AAtCD,IAAA,oBAAA,CAAA;AAAA,IAAA,qBAAA,QAAA,OAAA,QAAA,CAAA;AAMG,IAAA,oBAAA;AAAA,IAAA,qBAAA,QAAA,CAAA,OAAA,QAAA,CAAA;AAYqB,IAAA,oBAAA,CAAA;AAAA,IAAA,qBAAA,WAAA,OAAA,WAAA,CAAA;;;;;;AA2B3B,IAAA,yBAAA,GAAA,UAAA,EAAA;AAGE,IAAA,qBAAA,SAAA,SAAA,yEAAA;AAAA,MAAA,wBAAA,GAAA;AAAA,YAAA,SAAA,wBAAA,CAAA;AAAA,aAAA,sBAAS,OAAA,sBAAA,CAAuB;IAAA,CAAA;AAEhC,IAAA,iBAAA,GAAA,qCAAA;AACF,IAAA,uBAAA;;;;;;AACA,IAAA,yBAAA,GAAA,OAAA,EAAA,EAAiD,GAAA,UAAA,EAAA;AAG7C,IAAA,qBAAA,SAAA,SAAA,sEAAA;AAAA,MAAA,wBAAA,GAAA;AAAA,YAAA,SAAA,wBAAA,CAAA;AAAA,aAAA,sBAAS,OAAA,wBAAA,CAAyB;IAAA,CAAA;AAElC,IAAA,iBAAA,GAAA,qDAAA;AACF,IAAA,uBAAA;AACA,IAAA,yBAAA,GAAA,QAAA,EAAA;AAA+B,IAAA,iBAAA,GAAA,mCAAA;AAAqB,IAAA,uBAAA,EAAO;;;;;AAY3D,IAAA,yBAAA,GAAA,GAAA;AAAqC,IAAA,iBAAA,CAAA;AAA+B,IAAA,uBAAA;;;;AAA/B,IAAA,oBAAA;AAAA,IAAA,4BAAA,kBAAA,WAAA;;;;;;AAKrC,IAAA,yBAAA,GAAA,OAAA,EAAA,EAA4C,GAAA,UAAA,EAAA;AACD,IAAA,qBAAA,SAAA,SAAA,6EAAA;AAAA,MAAA,wBAAA,IAAA;AAAA,YAAA,oBAAA,wBAAA,EAAA;AAAA,YAAA,SAAA,wBAAA,CAAA;AAAA,aAAA,sBAAS,OAAA,kBAAA,iBAAA,CAAgC;IAAA,CAAA;AAChF,IAAA,iBAAA,GAAA,yBAAA;AACF,IAAA,uBAAA;AACA,IAAA,yBAAA,GAAA,UAAA,EAAA;AAAsC,IAAA,qBAAA,SAAA,SAAA,6EAAA;AAAA,MAAA,wBAAA,IAAA;AAAA,YAAA,oBAAA,wBAAA,EAAA;AAAA,YAAA,SAAA,wBAAA,CAAA;AAAA,aAAA,sBAAS,OAAA,oBAAA,iBAAA,CAAkC;IAAA,CAAA;AAC/E,IAAA,iBAAA,GAAA,6BAAA;AACF,IAAA,uBAAA,EAAS;;;;;AAlBb,IAAA,yBAAA,GAAA,OAAA,EAAA,EAAsE,GAAA,OAAA,EAAA,EAC3C,GAAA,IAAA;AACnB,IAAA,iBAAA,CAAA;AAAuB,IAAA,uBAAA;AAC3B,IAAA,yBAAA,GAAA,QAAA,EAAA;AACE,IAAA,iBAAA,CAAA;AACF,IAAA,uBAAA,EAAO;AAET,IAAA,qBAAA,GAAA,iDAAA,GAAA,GAAA,KAAA,EAAA;AACA,IAAA,yBAAA,GAAA,OAAA,EAAA,EAAwB,GAAA,MAAA;AAChB,IAAA,iBAAA,CAAA;AAA4D,IAAA,uBAAA;AAAO,IAAA,oBAAA,IAAA,IAAA;AACzE,IAAA,yBAAA,IAAA,MAAA;AAAM,IAAA,iBAAA,EAAA;AAA+C,IAAA,uBAAA,EAAO;AAE9D,IAAA,qBAAA,IAAA,oDAAA,GAAA,GAAA,OAAA,EAAA;AAQF,IAAA,uBAAA;;;;;AAlBQ,IAAA,oBAAA,CAAA;AAAA,IAAA,4BAAA,kBAAA,GAAA;AACgB,IAAA,oBAAA;AAAA,IAAA,qBAAA,kBAAA,aAAA,kBAAA,eAAA;AAClB,IAAA,oBAAA;AAAA,IAAA,6BAAA,KAAA,kBAAA,aAAA,eAAA,cAAA,GAAA;AAGA,IAAA,oBAAA;AAAA,IAAA,qBAAA,QAAA,kBAAA,WAAA;AAEI,IAAA,oBAAA,CAAA;AAAA,IAAA,6BAAA,kBAAA,kBAAA,gBAAA,kBAAA,EAAA;AACA,IAAA,oBAAA,CAAA;AAAA,IAAA,6BAAA,IAAA,kBAAA,iBAAA,GAAA,WAAA;AAEmB,IAAA,oBAAA;AAAA,IAAA,qBAAA,QAAA,OAAA,QAAA,CAAA;;;;;AAlCjC,IAAA,yBAAA,GAAA,OAAA,EAAA,EAAiE,GAAA,OAAA,EAAA,EACrC,GAAA,IAAA;AACpB,IAAA,iBAAA,GAAA,8BAAA;AAAkB,IAAA,uBAAA;AACtB,IAAA,qBAAA,GAAA,gDAAA,GAAA,GAAA,UAAA,EAAA,EAIC,GAAA,6CAAA,GAAA,GAAA,OAAA,EAAA;AAYH,IAAA,uBAAA;AAEA,IAAA,yBAAA,GAAA,OAAA,EAAA;AACE,IAAA,qBAAA,GAAA,6CAAA,IAAA,GAAA,OAAA,EAAA;AAqBF,IAAA,uBAAA,EAAM;;;;AAvCD,IAAA,oBAAA,CAAA;AAAA,IAAA,qBAAA,QAAA,OAAA,QAAA,CAAA;AAMG,IAAA,oBAAA;AAAA,IAAA,qBAAA,QAAA,CAAA,OAAA,QAAA,CAAA;AAYyB,IAAA,oBAAA,CAAA;AAAA,IAAA,qBAAA,WAAA,OAAA,eAAA,CAAA;;;;;;AA4B/B,IAAA,yBAAA,GAAA,UAAA,EAAA;AAGE,IAAA,qBAAA,SAAA,SAAA,yEAAA;AAAA,MAAA,wBAAA,IAAA;AAAA,YAAA,SAAA,wBAAA,CAAA;AAAA,aAAA,sBAAS,OAAA,eAAA,CAAgB;IAAA,CAAA;AAEzB,IAAA,iBAAA,GAAA,0BAAA;AACF,IAAA,uBAAA;;;;;;AACA,IAAA,yBAAA,GAAA,OAAA,EAAA,EAAiD,GAAA,UAAA,EAAA;AAG7C,IAAA,qBAAA,SAAA,SAAA,sEAAA;AAAA,MAAA,wBAAA,IAAA;AAAA,YAAA,SAAA,wBAAA,CAAA;AAAA,aAAA,sBAAS,OAAA,eAAA,CAAgB;IAAA,CAAA;AAEzB,IAAA,iBAAA,GAAA,6BAAA;AACF,IAAA,uBAAA;AACA,IAAA,yBAAA,GAAA,QAAA,EAAA;AAAkC,IAAA,iBAAA,GAAA,0BAAA;AAAgB,IAAA,uBAAA,EAAO;;;;;;AAiBzD,IAAA,yBAAA,GAAA,OAAA,EAAA,EAA4C,GAAA,UAAA,EAAA;AACD,IAAA,qBAAA,SAAA,SAAA,4EAAA;AAAA,MAAA,wBAAA,IAAA;AAAA,YAAA,aAAA,wBAAA,EAAA;AAAA,YAAA,SAAA,wBAAA,CAAA;AAAA,aAAA,sBAAS,OAAA,WAAA,UAAA,CAAkB;IAAA,CAAA;AAClE,IAAA,iBAAA,GAAA,yBAAA;AACF,IAAA,uBAAA;AACA,IAAA,yBAAA,GAAA,UAAA,EAAA;AAAsC,IAAA,qBAAA,SAAA,SAAA,4EAAA;AAAA,MAAA,wBAAA,IAAA;AAAA,YAAA,aAAA,wBAAA,EAAA;AAAA,YAAA,SAAA,wBAAA,CAAA;AAAA,aAAA,sBAAS,OAAA,aAAA,UAAA,CAAoB;IAAA,CAAA;AACjE,IAAA,iBAAA,GAAA,6BAAA;AACF,IAAA,uBAAA,EAAS;;;;;;AAlBb,IAAA,yBAAA,GAAA,OAAA,EAAA,EAAwD,GAAA,OAAA,EAAA,EAC7B,GAAA,IAAA;AACnB,IAAA,iBAAA,CAAA;AAAiB,IAAA,uBAAA,EAAK;AAE5B,IAAA,yBAAA,GAAA,OAAA,EAAA,EAAyB,GAAA,OAAA,EAAA;AAKrB,IAAA,qBAAA,SAAA,SAAA,iEAAA,QAAA;AAAA,MAAA,wBAAA,IAAA;AAAA,YAAA,SAAA,wBAAA,CAAA;AAAA,aAAA,sBAAS,OAAA,aAAA,MAAA,CAAoB;IAAA,CAAA;AAJ/B,IAAA,uBAAA,EAKC;AAEH,IAAA,qBAAA,GAAA,mDAAA,GAAA,GAAA,OAAA,EAAA;AAQF,IAAA,uBAAA;;;;;AAlBQ,IAAA,oBAAA,CAAA;AAAA,IAAA,4BAAA,WAAA,IAAA;AAIF,IAAA,oBAAA,CAAA;AAAA,IAAA,qBAAA,OAAA,OAAA,iBAAA,WAAA,IAAA,GAAA,uBAAA,EAAqC,OAAA,WAAA,IAAA;AAMd,IAAA,oBAAA;AAAA,IAAA,qBAAA,QAAA,OAAA,QAAA,CAAA;;;;;AAlCjC,IAAA,yBAAA,GAAA,OAAA,EAAA,EAAyD,GAAA,OAAA,EAAA,EAC7B,GAAA,IAAA;AACpB,IAAA,iBAAA,GAAA,yBAAA;AAAW,IAAA,uBAAA;AACf,IAAA,qBAAA,GAAA,gDAAA,GAAA,GAAA,UAAA,EAAA,EAIC,GAAA,6CAAA,GAAA,GAAA,OAAA,EAAA;AAYH,IAAA,uBAAA;AAEA,IAAA,yBAAA,GAAA,OAAA,EAAA;AACE,IAAA,qBAAA,GAAA,6CAAA,GAAA,GAAA,OAAA,EAAA;AAqBF,IAAA,uBAAA,EAAM;;;;AAvCD,IAAA,oBAAA,CAAA;AAAA,IAAA,qBAAA,QAAA,OAAA,QAAA,CAAA;AAMG,IAAA,oBAAA;AAAA,IAAA,qBAAA,QAAA,CAAA,OAAA,QAAA,CAAA;AAYkB,IAAA,oBAAA,CAAA;AAAA,IAAA,qBAAA,WAAA,OAAA,QAAA,CAAA;;;;;;AA4BxB,IAAA,yBAAA,GAAA,UAAA,EAAA;AAGE,IAAA,qBAAA,SAAA,SAAA,yEAAA;AAAA,MAAA,wBAAA,IAAA;AAAA,YAAA,SAAA,wBAAA,CAAA;AAAA,aAAA,sBAAS,OAAA,cAAA,CAAe;IAAA,CAAA;AAExB,IAAA,iBAAA,GAAA,yBAAA;AACF,IAAA,uBAAA;;;;;;AACA,IAAA,yBAAA,GAAA,OAAA,EAAA,EAAiD,GAAA,UAAA,EAAA;AAG7C,IAAA,qBAAA,SAAA,SAAA,sEAAA;AAAA,MAAA,wBAAA,IAAA;AAAA,YAAA,SAAA,wBAAA,CAAA;AAAA,aAAA,sBAAS,OAAA,cAAA,CAAe;IAAA,CAAA;AAExB,IAAA,iBAAA,GAAA,4BAAA;AACF,IAAA,uBAAA;AACA,IAAA,yBAAA,GAAA,QAAA,EAAA;AAAkC,IAAA,iBAAA,GAAA,0BAAA;AAAgB,IAAA,uBAAA,EAAO;;;;;;AAoBzD,IAAA,yBAAA,GAAA,OAAA,EAAA,EAA4C,GAAA,UAAA,EAAA;AACD,IAAA,qBAAA,SAAA,SAAA,4EAAA;AAAA,MAAA,wBAAA,IAAA;AAAA,YAAA,YAAA,wBAAA,EAAA;AAAA,YAAA,SAAA,wBAAA,CAAA;AAAA,aAAA,sBAAS,OAAA,UAAA,SAAA,CAAgB;IAAA,CAAA;AAChE,IAAA,iBAAA,GAAA,yBAAA;AACF,IAAA,uBAAA;AACA,IAAA,yBAAA,GAAA,UAAA,EAAA;AAAsC,IAAA,qBAAA,SAAA,SAAA,4EAAA;AAAA,MAAA,wBAAA,IAAA;AAAA,YAAA,YAAA,wBAAA,EAAA;AAAA,YAAA,SAAA,wBAAA,CAAA;AAAA,aAAA,sBAAS,OAAA,YAAA,SAAA,CAAkB;IAAA,CAAA;AAC/D,IAAA,iBAAA,GAAA,6BAAA;AACF,IAAA,uBAAA,EAAS;;;;;;AArBb,IAAA,yBAAA,GAAA,OAAA,EAAA,EAAsD,GAAA,OAAA,EAAA,EAC3B,GAAA,IAAA;AACnB,IAAA,iBAAA,CAAA;AAAe,IAAA,uBAAA,EAAK;AAE1B,IAAA,yBAAA,GAAA,OAAA,EAAA,EAAyB,GAAA,OAAA,EAAA;AAKrB,IAAA,qBAAA,SAAA,SAAA,iEAAA,QAAA;AAAA,MAAA,wBAAA,IAAA;AAAA,YAAA,SAAA,wBAAA,CAAA;AAAA,aAAA,sBAAS,OAAA,aAAA,MAAA,CAAoB;IAAA,CAAA;AAJ/B,IAAA,uBAAA,EAKC;AAEH,IAAA,yBAAA,GAAA,OAAA,EAAA,EAAwB,GAAA,MAAA;AAChB,IAAA,iBAAA,CAAA;AAAoD,IAAA,uBAAA,EAAO;AAEnE,IAAA,qBAAA,GAAA,mDAAA,GAAA,GAAA,OAAA,EAAA;AAQF,IAAA,uBAAA;;;;;AArBQ,IAAA,oBAAA,CAAA;AAAA,IAAA,4BAAA,UAAA,GAAA;AAIF,IAAA,oBAAA,CAAA;AAAA,IAAA,qBAAA,OAAA,OAAA,iBAAA,UAAA,QAAA,GAAA,uBAAA,EAAwC,OAAA,UAAA,GAAA;AAOpC,IAAA,oBAAA,CAAA;AAAA,IAAA,6BAAA,kBAAA,UAAA,gBAAA,kBAAA,EAAA;AAEmB,IAAA,oBAAA;AAAA,IAAA,qBAAA,QAAA,OAAA,QAAA,CAAA;;;;;AArCjC,IAAA,yBAAA,GAAA,OAAA,EAAA,EAAwD,GAAA,OAAA,EAAA,EAC5B,GAAA,IAAA;AACpB,IAAA,iBAAA,GAAA,kBAAA;AAAS,IAAA,uBAAA;AACb,IAAA,qBAAA,GAAA,gDAAA,GAAA,GAAA,UAAA,EAAA,EAIC,GAAA,6CAAA,GAAA,GAAA,OAAA,EAAA;AAYH,IAAA,uBAAA;AAEA,IAAA,yBAAA,GAAA,OAAA,EAAA;AACE,IAAA,qBAAA,GAAA,6CAAA,IAAA,GAAA,OAAA,EAAA;AAwBF,IAAA,uBAAA,EAAM;;;;AA1CD,IAAA,oBAAA,CAAA;AAAA,IAAA,qBAAA,QAAA,OAAA,QAAA,CAAA;AAMG,IAAA,oBAAA;AAAA,IAAA,qBAAA,QAAA,CAAA,OAAA,QAAA,CAAA;AAYiB,IAAA,oBAAA,CAAA;AAAA,IAAA,qBAAA,WAAA,OAAA,OAAA,CAAA;;;;;;AA+BvB,IAAA,yBAAA,GAAA,UAAA,EAAA;AAGE,IAAA,qBAAA,SAAA,SAAA,yEAAA;AAAA,MAAA,wBAAA,IAAA;AAAA,YAAA,SAAA,wBAAA,CAAA;AAAA,aAAA,sBAAS,OAAA,gBAAA,CAAiB;IAAA,CAAA;AAE1B,IAAA,iBAAA,GAAA,2BAAA;AACF,IAAA,uBAAA;;;;;AACA,IAAA,yBAAA,GAAA,OAAA,EAAA,EAAiD,GAAA,QAAA,EAAA;AAChB,IAAA,iBAAA,GAAA,yCAAA;AAA2B,IAAA,uBAAA,EAAO;;;;;AAejE,IAAA,yBAAA,GAAA,GAAA;AAA4B,IAAA,iBAAA,CAAA;AAAsB,IAAA,uBAAA;;;;AAAtB,IAAA,oBAAA;AAAA,IAAA,4BAAA,SAAA,WAAA;;;;;AAG1B,IAAA,yBAAA,GAAA,MAAA;AAA2B,IAAA,iBAAA,CAAA;AAAmC,IAAA,uBAAA;;;;;AAAnC,IAAA,oBAAA;AAAA,IAAA,6BAAA,SAAA,OAAA,WAAA,SAAA,OAAA,GAAA,EAAA;;;;;;AAE7B,IAAA,yBAAA,GAAA,OAAA,EAAA,EAA4C,GAAA,UAAA,EAAA;AACD,IAAA,qBAAA,SAAA,SAAA,6EAAA;AAAA,MAAA,wBAAA,IAAA;AAAA,YAAA,WAAA,wBAAA,EAAA;AAAA,YAAA,SAAA,wBAAA,CAAA;AAAA,aAAA,sBAAS,OAAA,YAAA,QAAA,CAAiB;IAAA,CAAA;AACjE,IAAA,iBAAA,GAAA,yBAAA;AACF,IAAA,uBAAA;AACA,IAAA,yBAAA,GAAA,UAAA,EAAA;AAAsC,IAAA,qBAAA,SAAA,SAAA,6EAAA;AAAA,MAAA,wBAAA,IAAA;AAAA,YAAA,WAAA,wBAAA,EAAA;AAAA,YAAA,SAAA,wBAAA,CAAA;AAAA,aAAA,sBAAS,OAAA,cAAA,QAAA,CAAmB;IAAA,CAAA;AAChE,IAAA,iBAAA,GAAA,6BAAA;AACF,IAAA,uBAAA,EAAS;;;;;AArBb,IAAA,yBAAA,GAAA,OAAA,EAAA,EAAsD,GAAA,OAAA,EAAA,EAC3B,GAAA,IAAA;AACnB,IAAA,iBAAA,CAAA;AAAkB,IAAA,uBAAA;AACtB,IAAA,yBAAA,GAAA,QAAA,EAAA;AACE,IAAA,iBAAA,CAAA;AACF,IAAA,uBAAA,EAAO;AAET,IAAA,yBAAA,GAAA,OAAA,EAAA,EAAsB,GAAA,QAAA,EAAA;AACD,IAAA,iBAAA,CAAA;AAAgB,IAAA,uBAAA,EAAO;AAE5C,IAAA,qBAAA,GAAA,iDAAA,GAAA,GAAA,KAAA,EAAA;AACA,IAAA,yBAAA,IAAA,OAAA,EAAA,EAAwB,IAAA,MAAA;AAChB,IAAA,iBAAA,EAAA;AAAuC,IAAA,uBAAA;AAC7C,IAAA,qBAAA,IAAA,qDAAA,GAAA,GAAA,QAAA,EAAA;AACF,IAAA,uBAAA;AACA,IAAA,qBAAA,IAAA,oDAAA,GAAA,GAAA,OAAA,EAAA;AAQF,IAAA,uBAAA;;;;;AArBQ,IAAA,oBAAA,CAAA;AAAA,IAAA,4BAAA,SAAA,OAAA;AACgB,IAAA,oBAAA;AAAA,IAAA,qBAAA,SAAA,WAAA,kBAAA,cAAA;AAClB,IAAA,oBAAA;AAAA,IAAA,6BAAA,KAAA,SAAA,WAAA,UAAA,WAAA,GAAA;AAIiB,IAAA,oBAAA,CAAA;AAAA,IAAA,6BAAA,IAAA,SAAA,MAAA,GAAA;AAEjB,IAAA,oBAAA;AAAA,IAAA,qBAAA,QAAA,SAAA,WAAA;AAEI,IAAA,oBAAA,CAAA;AAAA,IAAA,6BAAA,WAAA,OAAA,WAAA,SAAA,SAAA,GAAA,EAAA;AACC,IAAA,oBAAA;AAAA,IAAA,qBAAA,QAAA,SAAA,OAAA;AAEkB,IAAA,oBAAA;AAAA,IAAA,qBAAA,QAAA,OAAA,QAAA,CAAA;;;;;AA/BjC,IAAA,yBAAA,GAAA,OAAA,EAAA,EAAqD,GAAA,OAAA,EAAA,EACzB,GAAA,IAAA;AACpB,IAAA,iBAAA,GAAA,oBAAA;AAAW,IAAA,uBAAA;AACf,IAAA,qBAAA,GAAA,gDAAA,GAAA,GAAA,UAAA,EAAA,EAIC,GAAA,6CAAA,GAAA,GAAA,OAAA,EAAA;AAMH,IAAA,uBAAA;AAEA,IAAA,yBAAA,GAAA,OAAA,EAAA;AACE,IAAA,qBAAA,GAAA,6CAAA,IAAA,GAAA,OAAA,EAAA;AAwBF,IAAA,uBAAA,EAAM;;;;AApCD,IAAA,oBAAA,CAAA;AAAA,IAAA,qBAAA,QAAA,OAAA,QAAA,CAAA;AAMG,IAAA,oBAAA;AAAA,IAAA,qBAAA,QAAA,CAAA,OAAA,QAAA,CAAA;AAMgB,IAAA,oBAAA,CAAA;AAAA,IAAA,qBAAA,WAAA,OAAA,QAAA,CAAA;;;;;AA0CtB,IAAA,yBAAA,GAAA,KAAA,EAAiC,GAAA,OAAA,EAAA,EACP,GAAA,OAAA;AACf,IAAA,iBAAA,GAAA,0BAAA;AAAqB,IAAA,uBAAA;AAC5B,IAAA,oBAAA,GAAA,SAAA,EAAA;AAQF,IAAA,uBAAA;AACA,IAAA,yBAAA,GAAA,OAAA,EAAA,EAAwB,GAAA,OAAA;AACf,IAAA,iBAAA,GAAA,aAAA;AAAW,IAAA,uBAAA;AAClB,IAAA,oBAAA,GAAA,YAAA,EAAA;AAMF,IAAA,uBAAA,EAAM;;;;;AASF,IAAA,yBAAA,GAAA,UAAA,EAAA;AACE,IAAA,iBAAA,CAAA;AACF,IAAA,uBAAA;;;;AAFyC,IAAA,qBAAA,SAAA,QAAA,EAAA;AACvC,IAAA,oBAAA;AAAA,IAAA,6BAAA,KAAA,QAAA,KAAA,GAAA;;;;;AANR,IAAA,yBAAA,GAAA,KAAA,EAAsC,GAAA,OAAA,EAAA,EACZ,GAAA,OAAA;AACf,IAAA,iBAAA,GAAA,wBAAA;AAAmB,IAAA,uBAAA;AAC1B,IAAA,yBAAA,GAAA,UAAA,EAAA,EAAiE,GAAA,UAAA,EAAA;AAC9C,IAAA,iBAAA,GAAA,kCAAA;AAA0B,IAAA,uBAAA;AAC3C,IAAA,qBAAA,GAAA,uDAAA,GAAA,GAAA,UAAA,EAAA;AAGF,IAAA,uBAAA,EAAS;AAEX,IAAA,yBAAA,GAAA,OAAA,EAAA,EAAwB,GAAA,OAAA;AACf,IAAA,iBAAA,IAAA,+BAAA;AAA0B,IAAA,uBAAA;AACjC,IAAA,oBAAA,IAAA,SAAA,EAAA;AAQF,IAAA,uBAAA;AACA,IAAA,yBAAA,IAAA,OAAA,EAAA,EAAwB,IAAA,OAAA;AACf,IAAA,iBAAA,IAAA,aAAA;AAAW,IAAA,uBAAA;AAClB,IAAA,oBAAA,IAAA,YAAA,EAAA;AAMF,IAAA,uBAAA;AACA,IAAA,yBAAA,IAAA,OAAA,EAAA,EAAwB,IAAA,OAAA;AAEpB,IAAA,oBAAA,IAAA,SAAA,EAAA;AACA,IAAA,iBAAA,IAAA,cAAA;AACF,IAAA,uBAAA,EAAQ,EACJ;;;;AA9BsB,IAAA,oBAAA,CAAA;AAAA,IAAA,qBAAA,WAAA,OAAA,WAAA,CAAA;;;;;AAkC9B,IAAA,yBAAA,GAAA,KAAA,EAA8B,GAAA,OAAA,EAAA,EACJ,GAAA,OAAA;AACf,IAAA,iBAAA,GAAA,oBAAA;AAAkB,IAAA,uBAAA;AACzB,IAAA,oBAAA,GAAA,SAAA,EAAA;AAQF,IAAA,uBAAA;AACA,IAAA,yBAAA,GAAA,OAAA,EAAA,EAAwB,GAAA,OAAA;AACf,IAAA,iBAAA,GAAA,aAAA;AAAW,IAAA,uBAAA;AAClB,IAAA,oBAAA,GAAA,SAAA,EAAA;AAOF,IAAA,uBAAA,EAAM;;;;;AASF,IAAA,yBAAA,GAAA,UAAA,EAAA;AACE,IAAA,iBAAA,CAAA;AACF,IAAA,uBAAA;;;;AAFyC,IAAA,qBAAA,SAAA,QAAA,EAAA;AACvC,IAAA,oBAAA;AAAA,IAAA,6BAAA,KAAA,QAAA,KAAA,GAAA;;;;;AANR,IAAA,yBAAA,GAAA,KAAA,EAA6B,GAAA,OAAA,EAAA,EACH,GAAA,OAAA;AACf,IAAA,iBAAA,GAAA,gBAAA;AAAW,IAAA,uBAAA;AAClB,IAAA,yBAAA,GAAA,UAAA,EAAA,EAAiE,GAAA,UAAA,EAAA;AAC9C,IAAA,iBAAA,GAAA,kCAAA;AAA0B,IAAA,uBAAA;AAC3C,IAAA,qBAAA,GAAA,uDAAA,GAAA,GAAA,UAAA,EAAA;AAGF,IAAA,uBAAA,EAAS;AAEX,IAAA,yBAAA,GAAA,OAAA,EAAA,EAAwB,GAAA,OAAA;AACf,IAAA,iBAAA,IAAA,mBAAA;AAAiB,IAAA,uBAAA;AACxB,IAAA,oBAAA,IAAA,SAAA,EAAA;AAQF,IAAA,uBAAA;AACA,IAAA,yBAAA,IAAA,OAAA,EAAA,EAAwB,IAAA,OAAA;AACf,IAAA,iBAAA,IAAA,gBAAA;AAAc,IAAA,uBAAA;AACrB,IAAA,oBAAA,IAAA,SAAA,EAAA;AAOF,IAAA,uBAAA,EAAM;;;;AAzBsB,IAAA,oBAAA,CAAA;AAAA,IAAA,qBAAA,WAAA,OAAA,WAAA,CAAA;;;;;AA6B9B,IAAA,yBAAA,GAAA,KAAA,EAA2B,GAAA,OAAA,EAAA,EACD,GAAA,OAAA;AACf,IAAA,iBAAA,GAAA,cAAA;AAAS,IAAA,uBAAA;AAChB,IAAA,oBAAA,GAAA,SAAA,EAAA;AAQF,IAAA,uBAAA;AACA,IAAA,yBAAA,GAAA,OAAA,EAAA,EAAwB,GAAA,OAAA;AACf,IAAA,iBAAA,GAAA,YAAA;AAAU,IAAA,uBAAA;AACjB,IAAA,oBAAA,GAAA,SAAA,EAAA;AAWF,IAAA,uBAAA;AACA,IAAA,yBAAA,GAAA,OAAA,EAAA,EAAwB,IAAA,OAAA;AACf,IAAA,iBAAA,IAAA,aAAA;AAAW,IAAA,uBAAA;AAClB,IAAA,oBAAA,IAAA,YAAA,EAAA;AAMF,IAAA,uBAAA;AACA,IAAA,yBAAA,IAAA,OAAA,EAAA,EAAwB,IAAA,OAAA;AACf,IAAA,iBAAA,IAAA,gBAAA;AAAc,IAAA,uBAAA;AACrB,IAAA,oBAAA,IAAA,SAAA,EAAA;AAOF,IAAA,uBAAA;AACA,IAAA,yBAAA,IAAA,OAAA,EAAA,EAAwB,IAAA,OAAA;AACf,IAAA,iBAAA,IAAA,aAAA;AAAW,IAAA,uBAAA;AAClB,IAAA,oBAAA,IAAA,SAAA,EAAA;AAMF,IAAA,uBAAA;AACA,IAAA,yBAAA,IAAA,OAAA,EAAA,EAAwB,IAAA,OAAA;AAEpB,IAAA,oBAAA,IAAA,SAAA,EAAA;AACA,IAAA,iBAAA,IAAA,SAAA;AACF,IAAA,uBAAA,EAAQ,EACJ;;;;;;AAjMhB,IAAA,yBAAA,GAAA,OAAA,EAAA;AAA+C,IAAA,qBAAA,SAAA,SAAA,6DAAA;AAAA,MAAA,wBAAA,IAAA;AAAA,YAAA,SAAA,wBAAA;AAAA,aAAA,sBAAS,OAAA,WAAA,CAAY;IAAA,CAAA;AAClE,IAAA,yBAAA,GAAA,OAAA,EAAA;AAA2B,IAAA,qBAAA,SAAA,SAAA,2DAAA,QAAA;AAAA,MAAA,wBAAA,IAAA;AAAA,aAAA,sBAAS,OAAA,gBAAA,CAAwB;IAAA,CAAA;AAC1D,IAAA,yBAAA,GAAA,OAAA,EAAA,EAA0B,GAAA,IAAA;AACpB,IAAA,iBAAA,CAAA;AAAkB,IAAA,uBAAA;AACtB,IAAA,yBAAA,GAAA,UAAA,EAAA;AAA0B,IAAA,qBAAA,SAAA,SAAA,gEAAA;AAAA,MAAA,wBAAA,IAAA;AAAA,YAAA,SAAA,wBAAA;AAAA,aAAA,sBAAS,OAAA,WAAA,CAAY;IAAA,CAAA;AAAE,IAAA,iBAAA,GAAA,QAAA;AAAC,IAAA,uBAAA,EAAS;AAG7D,IAAA,yBAAA,GAAA,QAAA,IAAA,CAAA;AAAqB,IAAA,qBAAA,YAAA,SAAA,iEAAA;AAAA,MAAA,wBAAA,IAAA;AAAA,YAAA,WAAA,sBAAA,CAAA;AAAA,YAAA,SAAA,wBAAA;AAAA,aAAA,sBAAY,OAAA,SAAA,QAAA,CAAc;IAAA,CAAA;AAE7C,IAAA,kCAAA,GAAA,EAAA;AAGE,IAAA,qBAAA,IAAA,8CAAA,GAAA,GAAA,OAAA,EAAA,EAAiC,IAAA,8CAAA,IAAA,GAAA,OAAA,EAAA,EAwBK,IAAA,8CAAA,GAAA,GAAA,OAAA,EAAA,EAuCR,IAAA,8CAAA,IAAA,GAAA,OAAA,EAAA,EAyBD,IAAA,8CAAA,IAAA,GAAA,OAAA,EAAA;;AAiG/B,IAAA,yBAAA,IAAA,OAAA,EAAA,EAA0B,IAAA,UAAA,EAAA;AACwB,IAAA,qBAAA,SAAA,SAAA,iEAAA;AAAA,MAAA,wBAAA,IAAA;AAAA,YAAA,SAAA,wBAAA;AAAA,aAAA,sBAAS,OAAA,WAAA,CAAY;IAAA,CAAA;AACnE,IAAA,iBAAA,IAAA,WAAA;AACF,IAAA,uBAAA;AACA,IAAA,yBAAA,IAAA,UAAA,EAAA;AACE,IAAA,iBAAA,EAAA;AACF,IAAA,uBAAA,EAAS,EACL,EACD,EACH;;;;;AA3ME,IAAA,oBAAA,CAAA;AAAA,IAAA,4BAAA,OAAA,WAAA,CAAA;AAMU,IAAA,oBAAA,CAAA;AAAA,IAAA,qBAAA,YAAA,OAAA,UAAA,CAAA;AAGN,IAAA,oBAAA;AAAA,IAAA,qBAAA,gBAAA,WAAA;AAwBA,IAAA,oBAAA;AAAA,IAAA,qBAAA,gBAAA,gBAAA;AAuCA,IAAA,oBAAA;AAAA,IAAA,qBAAA,gBAAA,QAAA;AAyBA,IAAA,oBAAA;AAAA,IAAA,qBAAA,gBAAA,OAAA;AAkCA,IAAA,oBAAA;AAAA,IAAA,qBAAA,gBAAA,KAAA;AAmEwC,IAAA,oBAAA,CAAA;AAAA,IAAA,qBAAA,YAAA,CAAA,SAAA,SAAA,OAAA,UAAA,CAAA;AAC5C,IAAA,oBAAA;AAAA,IAAA,6BAAA,KAAA,OAAA,WAAA,IAAA,aAAA,YAAA,GAAA;;;AAudR,IAAO,wBAAP,MAAO,uBAAqB;EA8BtB;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;EAnCV,aAAa,OAAoB,CAAA,CAAE;EACnC,iBAAiB,OAAwB,CAAA,CAAE;EAC3C,UAAU,OAAiB,CAAA,CAAE;EAC7B,SAAS,OAAgB,CAAA,CAAE;EAC3B,UAAU,OAAkB,CAAA,CAAE;;EAG9B,YAAY,OAAO,YAAY;EAC/B,YAAY,OAAO,KAAK;EACxB,YAAY,OAAO,EAAE;EACrB,aAAa,OAAO,EAAE;EACtB,aAAa,OAAO,KAAK;EACzB,YAAY,OAAO,KAAK;EACxB,eAAe,OAAY,IAAI;;EAG/B,UAAU,OAAO,KAAK;;EAGtB,OAAO;IACL,EAAE,IAAI,cAAc,OAAO,iBAAc,MAAM,YAAI;IACnD,EAAE,IAAI,mBAAmB,OAAO,sBAAmB,MAAM,YAAI;IAC7D,EAAE,IAAI,WAAW,OAAO,WAAW,MAAM,kBAAK;IAC9C,EAAE,IAAI,UAAU,OAAO,UAAU,MAAM,YAAI;IAC3C,EAAE,IAAI,OAAO,OAAO,YAAY,MAAM,YAAI;;EAG5C,YACU,kBACA,sBACA,eACA,cACA,gBACA,aACA,QACA,iBAAgC;AAPhC,SAAA,mBAAA;AACA,SAAA,uBAAA;AACA,SAAA,gBAAA;AACA,SAAA,eAAA;AACA,SAAA,iBAAA;AACA,SAAA,cAAA;AACA,SAAA,SAAA;AACA,SAAA,kBAAA;EACP;EAEH,WAAQ;AACN,SAAK,cAAa;AAClB,SAAK,YAAW;EAClB;;;;EAKQ,gBAAa;AACnB,UAAM,cAAc,KAAK,YAAY,eAAc;AACnD,SAAK,QAAQ,IAAI,aAAa,SAAS,OAAO;AAC9C,YAAQ,IAAI,kCAAwB,aAAa,MAAM,YAAY,KAAK,QAAO,CAAE;EACnF;;;;EAKA,cAAW;AACT,SAAK,eAAc;AACnB,SAAK,mBAAkB;AACvB,SAAK,YAAW;AAChB,SAAK,WAAU;AACf,SAAK,YAAW;EAClB;;;;EAKA,iBAAc;AACZ,SAAK,iBAAiB,OAAM,EAAG,UAAU;MACvC,MAAM,CAAC,SAAQ;AACb,gBAAQ,IAAI,iDAAoC,IAAI;AACpD,aAAK,WAAW,IAAI,MAAM,QAAQ,IAAI,IAAI,OAAO,CAAA,CAAE;AACnD,gBAAQ,IAAI,qCAA0B,KAAK,WAAU,EAAG,MAAM;MAChE;MACA,OAAO,CAAC,UAAS;AACf,gBAAQ,MAAM,gCAAwB,KAAK;AAC3C,aAAK,WAAW,IAAI,CAAA,CAAE;MACxB;KACD;EACH;;;;EAKA,qBAAkB;AAEhB,UAAM,cAAc,KAAK,YAAY,YAAW;AAChD,UAAM,gBAAgB,aAAa,SAAS,gBAAgB,YAAY,KAAK;AAE7E,YAAQ,IAAI,4CAAkC;AAC9C,YAAQ,IAAI,sCAA4B,WAAW;AACnD,YAAQ,IAAI,2CAAiC,aAAa;AAG1D,QAAI,aAAa,SAAS,eAAe;AACvC,cAAQ,IAAI,2CAAiC,YAAY,EAAE;IAG7D;AAEA,SAAK,qBAAqB,OAAO,aAAa,EAAE,UAAU;MACxD,MAAM,CAAC,SAAQ;AACb,gBAAQ,IAAI,sDAAyC,IAAI;AACzD,gBAAQ,IAAI,wCAA8B,aAAa;AAGvD,aAAK,QAAQ,QAAK;AAChB,kBAAQ,IAAI,eAAQ,GAAG,GAAG,KAAK,GAAG,aAAa,WAAW;QAC5D,CAAC;AAED,aAAK,eAAe,IAAI,MAAM,QAAQ,IAAI,IAAI,OAAO,CAAA,CAAE;AACvD,gBAAQ,IAAI,0CAA+B,KAAK,eAAc,EAAG,MAAM;MACzE;MACA,OAAO,CAAC,UAAS;AACf,gBAAQ,MAAM,qCAA6B,KAAK;AAChD,aAAK,eAAe,IAAI,CAAA,CAAE;MAC5B;KACD;EACH;;;;EAKA,cAAW;AACT,SAAK,cAAc,OAAM,EAAG,UAAU;MACpC,MAAM,CAAC,SAAQ;AACb,gBAAQ,IAAI,2CAAiC,IAAI;AACjD,aAAK,QAAQ,IAAI,MAAM,QAAQ,IAAI,IAAI,OAAO,CAAA,CAAE;AAChD,gBAAQ,IAAI,+BAAuB,KAAK,QAAO,EAAG,MAAM;MAC1D;MACA,OAAO,CAAC,UAAS;AACf,gBAAQ,MAAM,0BAAqB,KAAK;AACxC,aAAK,QAAQ,IAAI,CAAA,CAAE;MACrB;KACD;EACH;;;;EAKA,aAAU;AACR,SAAK,aAAa,OAAM,EAAG,UAAU;MACnC,MAAM,CAAC,SAAQ;AACb,gBAAQ,IAAI,0CAAgC,IAAI;AAChD,aAAK,OAAO,IAAI,MAAM,QAAQ,IAAI,IAAI,OAAO,CAAA,CAAE;AAC/C,gBAAQ,IAAI,8BAAsB,KAAK,OAAM,EAAG,MAAM;MACxD;MACA,OAAO,CAAC,UAAS;AACf,gBAAQ,MAAM,yBAAoB,KAAK;AACvC,aAAK,OAAO,IAAI,CAAA,CAAE;MACpB;KACD;EACH;;;;EAKA,cAAW;AACT,SAAK,eAAe,OAAM,EAAG,UAAU;MACrC,MAAM,CAAC,SAAQ;AACb,gBAAQ,IAAI,+BAAuB,IAAI;AACvC,aAAK,QAAQ,IAAI,MAAM,QAAQ,IAAI,IAAI,OAAO,CAAA,CAAE;MAClD;MACA,OAAO,CAAC,UAAS;AACf,gBAAQ,MAAM,2BAAsB,KAAK;AACzC,aAAK,QAAQ,IAAI,CAAA,CAAE;MACrB;KACD;EACH;;;;EAKA,aAAa,OAAa;AACxB,SAAK,UAAU,IAAI,KAAK;EAC1B;;;;EAKA,oBAAiB;AACf,SAAK,UAAU,IAAI,WAAW;AAC9B,SAAK,WAAW,IAAI,uBAAoB;AACxC,SAAK,WAAW,IAAI,KAAK;AACzB,SAAK,aAAa,IAAI,IAAI;AAC1B,SAAK,UAAU,IAAI,IAAI;EACzB;;;;EAKA,wBAAqB;AACnB,SAAK,UAAU,IAAI,gBAAgB;AACnC,SAAK,WAAW,IAAI,4BAAyB;AAC7C,SAAK,WAAW,IAAI,KAAK;AACzB,SAAK,aAAa,IAAI,IAAI;AAC1B,SAAK,UAAU,IAAI,IAAI;EACzB;;;;EAKA,iBAAc;AACZ,SAAK,UAAU,IAAI,QAAQ;AAC3B,SAAK,WAAW,IAAI,iBAAiB;AACrC,SAAK,WAAW,IAAI,KAAK;AACzB,SAAK,aAAa,IAAI,IAAI;AAC1B,SAAK,UAAU,IAAI,IAAI;EACzB;;;;EAKA,gBAAa;AACX,SAAK,UAAU,IAAI,OAAO;AAC1B,SAAK,WAAW,IAAI,gBAAgB;AACpC,SAAK,WAAW,IAAI,KAAK;AACzB,SAAK,aAAa,IAAI,IAAI;AAC1B,SAAK,UAAU,IAAI,IAAI;EACzB;;;;EAKA,kBAAe;AACb,SAAK,UAAU,IAAI,KAAK;AACxB,SAAK,WAAW,IAAI,kBAAkB;AACtC,SAAK,WAAW,IAAI,KAAK;AACzB,SAAK,aAAa,IAAI,IAAI;AAC1B,SAAK,UAAU,IAAI,IAAI;EACzB;;;;EAKA,cAAc,WAAoB;AAChC,SAAK,UAAU,IAAI,WAAW;AAC9B,SAAK,WAAW,IAAI,uBAAoB;AACxC,SAAK,WAAW,IAAI,IAAI;AACxB,SAAK,aAAa,IAAI,SAAS;AAC/B,SAAK,UAAU,IAAI,IAAI;EACzB;;;;EAKA,kBAAkB,eAA4B;AAC5C,SAAK,UAAU,IAAI,gBAAgB;AACnC,SAAK,WAAW,IAAI,4BAAyB;AAC7C,SAAK,WAAW,IAAI,IAAI;AACxB,SAAK,aAAa,IAAI,aAAa;AACnC,SAAK,UAAU,IAAI,IAAI;EACzB;;;;EAKA,WAAW,QAAc;AACvB,SAAK,UAAU,IAAI,QAAQ;AAC3B,SAAK,WAAW,IAAI,iBAAiB;AACrC,SAAK,WAAW,IAAI,IAAI;AACxB,SAAK,aAAa,IAAI,MAAM;AAC5B,SAAK,UAAU,IAAI,IAAI;EACzB;;;;EAKA,UAAU,OAAY;AACpB,SAAK,UAAU,IAAI,OAAO;AAC1B,SAAK,WAAW,IAAI,gBAAgB;AACpC,SAAK,WAAW,IAAI,IAAI;AACxB,SAAK,aAAa,IAAI,KAAK;AAC3B,SAAK,UAAU,IAAI,IAAI;EACzB;;;;EAKA,YAAY,MAAa;AACvB,SAAK,UAAU,IAAI,KAAK;AACxB,SAAK,WAAW,IAAI,mBAAmB;AACvC,SAAK,WAAW,IAAI,IAAI;AACxB,SAAK,aAAa,IAAI,IAAI;AAC1B,SAAK,UAAU,IAAI,IAAI;EACzB;;;;EAKA,aAAU;AACR,SAAK,UAAU,IAAI,KAAK;AACxB,SAAK,UAAU,IAAI,EAAE;AACrB,SAAK,WAAW,IAAI,EAAE;AACtB,SAAK,WAAW,IAAI,KAAK;AACzB,SAAK,aAAa,IAAI,IAAI;EAC5B;;;;EAKA,SAAS,MAAS;AAChB,QAAI,CAAC,KAAK;AAAO;AAEjB,SAAK,UAAU,IAAI,IAAI;AACvB,UAAM,WAAW,KAAK;AACtB,UAAM,YAAY,KAAK,UAAS;AAEhC,YAAQ,WAAW;MACjB,KAAK;AACH,aAAK,sBAAsB,QAAQ;AACnC;MACF,KAAK;AACH,aAAK,0BAA0B,QAAQ;AACvC;MACF,KAAK;AACH,aAAK,mBAAmB,QAAQ;AAChC;MACF,KAAK;AACH,aAAK,kBAAkB,QAAQ;AAC/B;MACF,KAAK;AACH,aAAK,oBAAoB,QAAQ;AACjC;IACJ;EACF;;;;EAKQ,sBAAsB,UAAa;AACzC,UAAM,gBAAiC;MACrC,KAAK,SAAS;MACd,aAAa,SAAS;;AAGxB,QAAI,KAAK,WAAU,GAAI;AACrB,YAAM,eAAe,KAAK,aAAY;AACtC,WAAK,iBAAiB,OAAO,aAAa,IAAI,aAAa,EAAE,UAAU;QACrE,MAAM,MAAK;AACT,eAAK,eAAc;AACnB,eAAK,WAAU;AACf,eAAK,UAAU,IAAI,KAAK;AACxB,gBAAM,4CAAmC;QAC3C;QACA,OAAO,CAAC,UAAS;AACf,kBAAQ,MAAM,uCAAiC,KAAK;AACpD,eAAK,UAAU,IAAI,KAAK;AACxB,gBAAM,kCAA+B;QACvC;OACD;IACH,OAAO;AACL,WAAK,iBAAiB,OAAO,aAAa,EAAE,UAAU;QACpD,MAAM,MAAK;AACT,eAAK,eAAc;AACnB,eAAK,WAAU;AACf,eAAK,UAAU,IAAI,KAAK;AACxB,gBAAM,yCAA6B;QACrC;QACA,OAAO,CAAC,UAAS;AACf,kBAAQ,MAAM,oCAA8B,KAAK;AACjD,eAAK,UAAU,IAAI,KAAK;AACxB,gBAAM,+BAA4B;QACpC;OACD;IACH;EACF;;;;EAKQ,0BAA0B,UAAa;AAC7C,UAAM,oBAAyC;MAC7C,KAAK,SAAS;MACd,aAAa,CAAC,SAAS;MACvB,aAAa,SAAS;MACtB,YAAY,SAAS,cAAc;;AAGrC,QAAI,KAAK,WAAU,GAAI;AACrB,YAAM,eAAe,KAAK,aAAY;AACtC,WAAK,qBAAqB,OAAO,aAAa,IAAI,iBAAiB,EAAE,UAAU;QAC7E,MAAM,MAAK;AACT,eAAK,mBAAkB;AACvB,eAAK,WAAU;AACf,eAAK,UAAU,IAAI,KAAK;AACxB,gBAAM,iDAAwC;QAChD;QACA,OAAO,CAAC,UAAS;AACf,kBAAQ,MAAM,4CAAsC,KAAK;AACzD,eAAK,UAAU,IAAI,KAAK;AACxB,gBAAM,kCAA+B;QACvC;OACD;IACH,OAAO;AACL,WAAK,qBAAqB,OAAO,iBAAiB,EAAE,UAAU;QAC5D,MAAM,MAAK;AACT,eAAK,mBAAkB;AACvB,eAAK,WAAU;AACf,eAAK,UAAU,IAAI,KAAK;AACxB,gBAAM,8CAAkC;QAC1C;QACA,OAAO,CAAC,UAAS;AACf,kBAAQ,MAAM,yCAAmC,KAAK;AACtD,eAAK,UAAU,IAAI,KAAK;AACxB,gBAAM,+BAA4B;QACpC;OACD;IACH;EACF;;;;EAKQ,mBAAmB,UAAa;AACtC,UAAM,aAA2B;MAC/B,MAAM,SAAS;MACf,MAAM,SAAS;;AAGjB,QAAI,KAAK,WAAU,GAAI;AACrB,YAAM,eAAe,KAAK,aAAY;AACtC,WAAK,cAAc,OAAO,aAAa,IAAI,UAAU,EAAE,UAAU;QAC/D,MAAM,MAAK;AACT,eAAK,YAAW;AAChB,eAAK,WAAU;AACf,eAAK,UAAU,IAAI,KAAK;AACxB,gBAAM,sCAAgC;QACxC;QACA,OAAO,CAAC,UAAS;AACf,kBAAQ,MAAM,iCAA8B,KAAK;AACjD,eAAK,UAAU,IAAI,KAAK;AACxB,gBAAM,kCAA+B;QACvC;OACD;IACH,OAAO;AACL,WAAK,cAAc,OAAO,UAAU,EAAE,UAAU;QAC9C,MAAM,MAAK;AACT,eAAK,YAAW;AAChB,eAAK,WAAU;AACf,eAAK,UAAU,IAAI,KAAK;AACxB,gBAAM,mCAA0B;QAClC;QACA,OAAO,CAAC,UAAS;AACf,kBAAQ,MAAM,8BAA2B,KAAK;AAC9C,eAAK,UAAU,IAAI,KAAK;AACxB,gBAAM,+BAA4B;QACpC;OACD;IACH;EACF;;;;EAKQ,kBAAkB,UAAa;AACrC,UAAM,YAAyB;MAC7B,KAAK,SAAS;MACd,aAAa,CAAC,SAAS;MACvB,UAAU,SAAS;;AAGrB,QAAI,KAAK,WAAU,GAAI;AACrB,YAAM,eAAe,KAAK,aAAY;AACtC,WAAK,aAAa,OAAO,aAAa,IAAI,SAAS,EAAE,UAAU;QAC7D,MAAM,MAAK;AACT,eAAK,WAAU;AACf,eAAK,WAAU;AACf,eAAK,UAAU,IAAI,KAAK;AACxB,gBAAM,qCAA+B;QACvC;QACA,OAAO,CAAC,UAAS;AACf,kBAAQ,MAAM,gCAA6B,KAAK;AAChD,eAAK,UAAU,IAAI,KAAK;AACxB,gBAAM,kCAA+B;QACvC;OACD;IACH,OAAO;AACL,WAAK,aAAa,OAAO,SAAS,EAAE,UAAU;QAC5C,MAAM,MAAK;AACT,eAAK,WAAU;AACf,eAAK,WAAU;AACf,eAAK,UAAU,IAAI,KAAK;AACxB,gBAAM,kCAAyB;QACjC;QACA,OAAO,CAAC,UAAS;AACf,kBAAQ,MAAM,6BAA0B,KAAK;AAC7C,eAAK,UAAU,IAAI,KAAK;AACxB,gBAAM,+BAA4B;QACpC;OACD;IACH;EACF;;;;EAKQ,oBAAoB,UAAa;AACvC,UAAM,cAA6B;MACjC,SAAS,SAAS;MAClB,MAAM,CAAC,SAAS;MAChB,aAAa,SAAS;MACtB,UAAU,SAAS,YAAY;MAC/B,WAAW,SAAS;MACpB,SAAS,SAAS;;AAGpB,QAAI,KAAK,WAAU,GAAI;AACrB,YAAM,eAAe,KAAK,aAAY;AACtC,WAAK,eAAe,OAAO,aAAa,IAAI,WAAW,EAAE,UAAU;QACjE,MAAM,MAAK;AACT,eAAK,YAAW;AAChB,eAAK,WAAU;AACf,eAAK,UAAU,IAAI,KAAK;AACxB,gBAAM,uCAAiC;QACzC;QACA,OAAO,CAAC,UAAS;AACf,kBAAQ,MAAM,mCAAgC,KAAK;AACnD,eAAK,UAAU,IAAI,KAAK;AACxB,gBAAM,kCAA+B;QACvC;OACD;IACH,OAAO;AACL,WAAK,eAAe,OAAO,WAAW,EAAE,UAAU;QAChD,MAAM,MAAK;AACT,eAAK,YAAW;AAChB,eAAK,WAAU;AACf,eAAK,UAAU,IAAI,KAAK;AACxB,gBAAM,oCAA2B;QACnC;QACA,OAAO,CAAC,UAAS;AACf,kBAAQ,MAAM,gCAA6B,KAAK;AAChD,eAAK,UAAU,IAAI,KAAK;AACxB,gBAAM,+BAA4B;QACpC;OACD;IACH;EACF;;;;EAKA,gBAAgB,WAAoB;AAClC,QAAI,QAAQ,6DAAoD,UAAU,GAAG,KAAK,GAAG;AACnF,WAAK,iBAAiB,OAAO,UAAU,EAAE,EAAE,UAAU;QACnD,MAAM,MAAK;AACT,eAAK,eAAc;AACnB,gBAAM,0CAAiC;QACzC;QACA,OAAO,CAAC,UAAS;AACf,kBAAQ,MAAM,oCAAiC,KAAK;AACpD,gBAAM,+BAA+B;QACvC;OACD;IACH;EACF;;;;EAKA,oBAAoB,eAA4B;AAC9C,QAAI,QAAQ,kEAAyD,cAAc,GAAG,KAAK,GAAG;AAC5F,WAAK,qBAAqB,OAAO,cAAc,EAAE,EAAE,UAAU;QAC3D,MAAM,MAAK;AACT,eAAK,mBAAkB;AACvB,gBAAM,+CAAsC;QAC9C;QACA,OAAO,CAAC,UAAS;AACf,kBAAQ,MAAM,yCAAsC,KAAK;AACzD,gBAAM,+BAA+B;QACvC;OACD;IACH;EACF;;;;EAKA,aAAa,QAAc;AACzB,QAAI,QAAQ,uDAAiD,OAAO,IAAI,KAAK,GAAG;AAC9E,WAAK,cAAc,OAAO,OAAO,EAAE,EAAE,UAAU;QAC7C,MAAM,MAAK;AACT,eAAK,YAAW;AAChB,gBAAM,oCAA8B;QACtC;QACA,OAAO,CAAC,UAAS;AACf,kBAAQ,MAAM,8BAA8B,KAAK;AACjD,gBAAM,+BAA+B;QACvC;OACD;IACH;EACF;;;;EAKA,YAAY,OAAY;AACtB,QAAI,QAAQ,sDAAgD,MAAM,GAAG,KAAK,GAAG;AAC3E,WAAK,aAAa,OAAO,MAAM,EAAE,EAAE,UAAU;QAC3C,MAAM,MAAK;AACT,eAAK,WAAU;AACf,gBAAM,mCAA6B;QACrC;QACA,OAAO,CAAC,UAAS;AACf,kBAAQ,MAAM,6BAA6B,KAAK;AAChD,gBAAM,+BAA+B;QACvC;OACD;IACH;EACF;;;;EAKA,cAAc,MAAa;AACzB,QAAI,QAAQ,yDAAmD,KAAK,OAAO,KAAK,GAAG;AACjF,WAAK,eAAe,OAAO,KAAK,EAAE,EAAE,UAAU;QAC5C,MAAM,MAAK;AACT,eAAK,YAAW;AAChB,gBAAM,qCAA+B;QACvC;QACA,OAAO,CAAC,UAAS;AACf,kBAAQ,MAAM,gCAAgC,KAAK;AACnD,gBAAM,+BAA+B;QACvC;OACD;IACH;EACF;;;;EAKA,sBAAmB;AACjB,YAAQ,IAAI,kEAAwD;AACpE,SAAK,OAAO,SAAS,CAAC,gCAAgC,CAAC;EACzD;;;;EAKA,0BAAuB;AACrB,YAAQ,IAAI,6FAAgF;AAC5F,SAAK,OAAO,SAAS,CAAC,gCAAgC,CAAC;EACzD;;;;EAKA,iBAAiB,UAAmC;AAClD,WAAO,KAAK,gBAAgB,iBAAiB,QAAQ;EACvD;;;;EAKA,iBAAiB,WAAoC;AACnD,WAAO,KAAK,gBAAgB,gBAAgB,SAAS;EACvD;;;;EAKA,aAAa,OAAU;AACrB,YAAQ,KAAK,iCAAkC,MAAM,OAAO,GAAG;AAC/D,UAAM,OAAO,MAAM;EACrB;;;;EAKA,WAAW,YAAkB;AAC3B,WAAO,IAAI,KAAK,UAAU,EAAE,mBAAmB,OAAO;EACxD;;qCAjqBW,wBAAqB,4BAAA,gBAAA,GAAA,4BAAA,oBAAA,GAAA,4BAAA,aAAA,GAAA,4BAAA,YAAA,GAAA,4BAAA,cAAA,GAAA,4BAAA,WAAA,GAAA,4BAAA,MAAA,GAAA,4BAAA,eAAA,CAAA;EAAA;yEAArB,wBAAqB,WAAA,CAAA,CAAA,kBAAA,CAAA,GAAA,OAAA,IAAA,MAAA,GAAA,QAAA,CAAA,CAAA,QAAA,QAAA,GAAA,CAAA,GAAA,wBAAA,GAAA,CAAA,GAAA,qBAAA,GAAA,CAAA,GAAA,oBAAA,GAAA,CAAA,GAAA,MAAA,GAAA,CAAA,SAAA,cAAA,GAAA,UAAA,SAAA,GAAA,SAAA,SAAA,GAAA,CAAA,GAAA,aAAA,GAAA,CAAA,SAAA,aAAA,GAAA,MAAA,GAAA,CAAA,SAAA,iBAAA,GAAA,SAAA,GAAA,MAAA,GAAA,CAAA,GAAA,cAAA,GAAA,OAAA,GAAA,CAAA,GAAA,WAAA,GAAA,CAAA,GAAA,cAAA,GAAA,CAAA,SAAA,mBAAA,GAAA,SAAA,GAAA,MAAA,GAAA,CAAA,SAAA,oBAAA,GAAA,MAAA,GAAA,CAAA,GAAA,YAAA,GAAA,CAAA,SAAA,aAAA,GAAA,SAAA,SAAA,GAAA,CAAA,GAAA,OAAA,eAAA,GAAA,OAAA,GAAA,CAAA,GAAA,kBAAA,GAAA,CAAA,GAAA,OAAA,uBAAA,GAAA,OAAA,GAAA,CAAA,GAAA,SAAA,YAAA,GAAA,CAAA,GAAA,WAAA,GAAA,CAAA,GAAA,aAAA,GAAA,CAAA,GAAA,OAAA,GAAA,CAAA,GAAA,MAAA,GAAA,CAAA,GAAA,YAAA,GAAA,CAAA,SAAA,gBAAA,GAAA,MAAA,GAAA,CAAA,GAAA,cAAA,GAAA,CAAA,GAAA,OAAA,UAAA,iBAAA,GAAA,OAAA,GAAA,CAAA,GAAA,OAAA,UAAA,cAAA,GAAA,OAAA,GAAA,CAAA,GAAA,SAAA,eAAA,GAAA,CAAA,GAAA,aAAA,GAAA,CAAA,GAAA,YAAA,GAAA,SAAA,OAAA,KAAA,GAAA,CAAA,GAAA,aAAA,GAAA,CAAA,GAAA,aAAA,GAAA,SAAA,OAAA,KAAA,GAAA,CAAA,SAAA,oBAAA,GAAA,MAAA,GAAA,CAAA,GAAA,kBAAA,GAAA,CAAA,GAAA,UAAA,GAAA,CAAA,GAAA,MAAA,GAAA,CAAA,GAAA,iBAAA,GAAA,OAAA,GAAA,CAAA,GAAA,iBAAA,GAAA,OAAA,GAAA,CAAA,GAAA,cAAA,GAAA,CAAA,GAAA,aAAA,GAAA,OAAA,GAAA,CAAA,GAAA,cAAA,GAAA,UAAA,GAAA,CAAA,GAAA,UAAA,GAAA,CAAA,GAAA,cAAA,GAAA,CAAA,GAAA,cAAA,GAAA,CAAA,QAAA,UAAA,GAAA,OAAA,iBAAA,GAAA,OAAA,GAAA,CAAA,QAAA,UAAA,GAAA,OAAA,eAAA,GAAA,UAAA,GAAA,CAAA,GAAA,YAAA,GAAA,CAAA,QAAA,QAAA,QAAA,OAAA,WAAA,IAAA,YAAA,IAAA,eAAA,uBAAA,GAAA,cAAA,GAAA,CAAA,QAAA,eAAA,WAAA,IAAA,eAAA,qCAAA,GAAA,cAAA,GAAA,CAAA,QAAA,eAAA,WAAA,IAAA,YAAA,IAAA,GAAA,cAAA,GAAA,CAAA,SAAA,EAAA,GAAA,CAAA,GAAA,SAAA,GAAA,SAAA,SAAA,GAAA,CAAA,QAAA,QAAA,QAAA,OAAA,WAAA,IAAA,YAAA,IAAA,eAAA,2BAAA,GAAA,cAAA,GAAA,CAAA,QAAA,eAAA,WAAA,IAAA,eAAA,0CAAA,GAAA,cAAA,GAAA,CAAA,QAAA,YAAA,QAAA,cAAA,WAAA,EAAA,GAAA,CAAA,GAAA,OAAA,GAAA,CAAA,QAAA,QAAA,QAAA,QAAA,WAAA,IAAA,YAAA,IAAA,eAAA,eAAA,GAAA,cAAA,GAAA,CAAA,QAAA,QAAA,QAAA,QAAA,WAAA,IAAA,eAAA,uBAAA,GAAA,cAAA,GAAA,CAAA,QAAA,QAAA,QAAA,OAAA,WAAA,IAAA,YAAA,IAAA,eAAA,qBAAA,GAAA,cAAA,GAAA,CAAA,QAAA,QAAA,QAAA,YAAA,WAAA,IAAA,eAAA,+BAAA,GAAA,cAAA,GAAA,CAAA,QAAA,QAAA,QAAA,WAAA,WAAA,IAAA,YAAA,IAAA,eAAA,oBAAA,GAAA,cAAA,GAAA,CAAA,QAAA,UAAA,QAAA,QAAA,WAAA,IAAA,YAAA,IAAA,OAAA,KAAA,OAAA,OAAA,QAAA,OAAA,eAAA,UAAA,GAAA,cAAA,GAAA,CAAA,QAAA,eAAA,WAAA,IAAA,eAAA,8BAAA,GAAA,cAAA,GAAA,CAAA,QAAA,QAAA,QAAA,aAAA,WAAA,IAAA,YAAA,IAAA,GAAA,cAAA,GAAA,CAAA,QAAA,QAAA,QAAA,WAAA,WAAA,IAAA,GAAA,cAAA,GAAA,CAAA,QAAA,YAAA,QAAA,YAAA,WAAA,IAAA,WAAA,EAAA,CAAA,GAAA,UAAA,SAAA,+BAAA,IAAA,KAAA;AAAA,QAAA,KAAA,GAAA;AAv6B9B,MAAA,yBAAA,GAAA,OAAA,CAAA,EAAoC,GAAA,OAAA,CAAA,EAED,GAAA,MAAA,CAAA,EACA,GAAA,MAAA;AACvB,MAAA,iBAAA,GAAA,iBAAA;AAAG,MAAA,uBAAA;AACT,MAAA,iBAAA,CAAA;AACF,MAAA,uBAAA;AACA,MAAA,yBAAA,GAAA,GAAA;AAAG,MAAA,iBAAA,CAAA;AAAoI,MAAA,uBAAA,EAAI;AAI7I,MAAA,yBAAA,GAAA,OAAA,CAAA;AACE,MAAA,qBAAA,GAAA,yCAAA,GAAA,GAAA,UAAA,CAAA;AAQF,MAAA,uBAAA;AAGA,MAAA,yBAAA,IAAA,OAAA,CAAA;AAGE,MAAA,qBAAA,IAAA,uCAAA,GAAA,GAAA,OAAA,CAAA,EAA4D,IAAA,uCAAA,GAAA,GAAA,OAAA,CAAA,EA8CK,IAAA,uCAAA,GAAA,GAAA,OAAA,CAAA,EA+CR,IAAA,uCAAA,GAAA,GAAA,OAAA,CAAA,EA+CD,IAAA,uCAAA,GAAA,GAAA,OAAA,CAAA;AA4F1D,MAAA,uBAAA,EAAM;AAIR,MAAA,qBAAA,IAAA,uCAAA,IAAA,GAAA,OAAA,CAAA;;;AAjQM,MAAA,oBAAA,CAAA;AAAA,MAAA,6BAAA,KAAA,IAAA,QAAA,IAAA,mCAAA,uCAAA,GAAA;AAEC,MAAA,oBAAA,CAAA;AAAA,MAAA,4BAAA,IAAA,QAAA,IAAA,2DAAA,mEAAA;AAMe,MAAA,oBAAA,CAAA;AAAA,MAAA,qBAAA,WAAA,IAAA,IAAA;AAaZ,MAAA,oBAAA,CAAA;AAAA,MAAA,qBAAA,QAAA,IAAA,UAAA,MAAA,YAAA;AA8CA,MAAA,oBAAA;AAAA,MAAA,qBAAA,QAAA,IAAA,UAAA,MAAA,iBAAA;AA+CA,MAAA,oBAAA;AAAA,MAAA,qBAAA,QAAA,IAAA,UAAA,MAAA,SAAA;AA+CA,MAAA,oBAAA;AAAA,MAAA,qBAAA,QAAA,IAAA,UAAA,MAAA,QAAA;AAkDA,MAAA,oBAAA;AAAA,MAAA,qBAAA,QAAA,IAAA,UAAA,MAAA,KAAA;AA8CkB,MAAA,oBAAA;AAAA,MAAA,qBAAA,QAAA,IAAA,UAAA,CAAA;;oBAxQpB,cAAY,SAAA,MAAA,UAAA,cAAE,aAAW,oBAAA,gBAAA,8BAAA,sBAAA,qBAAA,8BAAA,4BAAA,iBAAA,sBAAA,mBAAA,cAAA,cAAA,SAAA,MAAA,GAAA,QAAA,CAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;uDAAA,EAAA,CAAA;;;sEAy6BxB,uBAAqB,CAAA;UA56BjC;uBACW,oBAAkB,YAChB,MAAI,SACP,CAAC,cAAc,WAAW,GAAC,UAC1B;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;KAudT,QAAA,CAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;CAAA,EAAA,CAAA;;;;6EAidU,uBAAqB,EAAA,WAAA,yBAAA,UAAA,6DAAA,YAAA,IAAA,CAAA;AAAA,GAAA;", "names": []}
import {
  DashboardService
} from "./chunk-Y3FP3KQV.js";
import "./chunk-YIUF6N3Y.js";
import {
  AuthService
} from "./chunk-ZSXXNL7U.js";
import {
  RouterLink,
  RouterModule
} from "./chunk-6BVUYNW4.js";
import "./chunk-GFHHPDQ6.js";
import "./chunk-7JDDWGD3.js";
import {
  CommonModule,
  Component,
  DatePipe,
  NgClass,
  NgForOf,
  NgIf,
  setClassMetadata,
  ɵsetClassDebugInfo,
  ɵɵadvance,
  ɵɵdefineComponent,
  ɵɵdirectiveInject,
  ɵɵelement,
  ɵɵelementEnd,
  ɵɵelementStart,
  ɵɵnextContext,
  ɵɵpipe,
  ɵɵpipeBind2,
  ɵɵproperty,
  ɵɵstyleProp,
  ɵɵtemplate,
  ɵɵtext,
  ɵɵtextInterpolate,
  ɵɵtextInterpolate1,
  ɵɵtextInterpolate2
} from "./chunk-UBZQS7JS.js";

// src/app/components/dashboard/dashboard.component.ts
function DashboardComponent_span_7_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "span");
    \u0275\u0275text(1);
    \u0275\u0275elementEnd();
  }
  if (rf & 2) {
    const ctx_r0 = \u0275\u0275nextContext();
    \u0275\u0275advance();
    \u0275\u0275textInterpolate2(" Bonjour ", ctx_r0.fournisseurInfo.prenom, " ", ctx_r0.fournisseurInfo.nom, " ");
  }
}
function DashboardComponent_span_8_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "span");
    \u0275\u0275text(1);
    \u0275\u0275elementEnd();
  }
  if (rf & 2) {
    const ctx_r0 = \u0275\u0275nextContext();
    \u0275\u0275advance();
    \u0275\u0275textInterpolate2(" Bonjour ", ctx_r0.currentUser.prenom, " ", ctx_r0.currentUser.nom, " ");
  }
}
function DashboardComponent_span_9_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "span");
    \u0275\u0275text(1, " Bienvenue ");
    \u0275\u0275elementEnd();
  }
}
function DashboardComponent_span_11_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "span");
    \u0275\u0275text(1);
    \u0275\u0275elementEnd();
  }
  if (rf & 2) {
    const ctx_r0 = \u0275\u0275nextContext();
    \u0275\u0275advance();
    \u0275\u0275textInterpolate1(" ", ctx_r0.fournisseurInfo.raisonSociale, " - Voici un aper\xE7u de votre activit\xE9 aujourd'hui ");
  }
}
function DashboardComponent_span_12_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "span");
    \u0275\u0275text(1, " Voici un aper\xE7u de votre activit\xE9 aujourd'hui ");
    \u0275\u0275elementEnd();
  }
}
function DashboardComponent_span_26_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "span", 54);
    \u0275\u0275text(1, "\u{1F4C8}");
    \u0275\u0275elementEnd();
  }
}
function DashboardComponent_span_27_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "span", 54);
    \u0275\u0275text(1, "\u2796");
    \u0275\u0275elementEnd();
  }
}
function DashboardComponent_div_29_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "div", 55);
    \u0275\u0275text(1);
    \u0275\u0275elementEnd();
  }
  if (rf & 2) {
    const ctx_r0 = \u0275\u0275nextContext();
    \u0275\u0275advance();
    \u0275\u0275textInterpolate(ctx_r0.stats.totalProducts);
  }
}
function DashboardComponent_div_30_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "div", 56);
    \u0275\u0275element(1, "div", 57);
    \u0275\u0275elementEnd();
  }
}
function DashboardComponent_span_41_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "span", 54);
    \u0275\u0275text(1, "\u{1F4C8}");
    \u0275\u0275elementEnd();
  }
}
function DashboardComponent_span_42_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "span", 54);
    \u0275\u0275text(1, "\u2796");
    \u0275\u0275elementEnd();
  }
}
function DashboardComponent_div_44_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "div", 55);
    \u0275\u0275text(1);
    \u0275\u0275elementEnd();
  }
  if (rf & 2) {
    const ctx_r0 = \u0275\u0275nextContext();
    \u0275\u0275advance();
    \u0275\u0275textInterpolate(ctx_r0.stats.activeOrders);
  }
}
function DashboardComponent_div_45_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "div", 56);
    \u0275\u0275element(1, "div", 57);
    \u0275\u0275elementEnd();
  }
}
function DashboardComponent_span_56_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "span", 54);
    \u0275\u0275text(1, "\u{1F4B0}");
    \u0275\u0275elementEnd();
  }
}
function DashboardComponent_span_57_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "span", 54);
    \u0275\u0275text(1, "\u2796");
    \u0275\u0275elementEnd();
  }
}
function DashboardComponent_div_59_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "div", 55);
    \u0275\u0275text(1);
    \u0275\u0275elementEnd();
  }
  if (rf & 2) {
    const ctx_r0 = \u0275\u0275nextContext();
    \u0275\u0275advance();
    \u0275\u0275textInterpolate(ctx_r0.formatCurrency(ctx_r0.stats.monthlyRevenue));
  }
}
function DashboardComponent_div_60_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "div", 56);
    \u0275\u0275element(1, "div", 57);
    \u0275\u0275elementEnd();
  }
}
function DashboardComponent_span_71_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "span", 54);
    \u0275\u0275text(1, "\u{1F69A}");
    \u0275\u0275elementEnd();
  }
}
function DashboardComponent_span_72_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "span", 54);
    \u0275\u0275text(1, "\u2796");
    \u0275\u0275elementEnd();
  }
}
function DashboardComponent_div_74_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "div", 55);
    \u0275\u0275text(1);
    \u0275\u0275elementEnd();
  }
  if (rf & 2) {
    const ctx_r0 = \u0275\u0275nextContext();
    \u0275\u0275advance();
    \u0275\u0275textInterpolate(ctx_r0.stats.pendingDeliveries);
  }
}
function DashboardComponent_div_75_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "div", 56);
    \u0275\u0275element(1, "div", 57);
    \u0275\u0275elementEnd();
  }
}
function DashboardComponent_div_98_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "div", 58)(1, "div", 59);
    \u0275\u0275text(2, "\u{1F4CA}");
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(3, "h4");
    \u0275\u0275text(4, "Aucune donn\xE9e de ventes");
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(5, "p");
    \u0275\u0275text(6, "Les donn\xE9es appara\xEEtront apr\xE8s vos premi\xE8res commandes");
    \u0275\u0275elementEnd()();
  }
}
function DashboardComponent_div_99_div_2_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "div", 65)(1, "div", 66);
    \u0275\u0275text(2);
    \u0275\u0275elementEnd()();
  }
  if (rf & 2) {
    const data_r2 = ctx.$implicit;
    const ctx_r0 = \u0275\u0275nextContext(2);
    \u0275\u0275styleProp("height", data_r2.sales > 0 ? data_r2.sales / ctx_r0.getMaxSales() * 100 : 5, "%");
    \u0275\u0275advance(2);
    \u0275\u0275textInterpolate(ctx_r0.formatCurrency(data_r2.sales));
  }
}
function DashboardComponent_div_99_span_4_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "span");
    \u0275\u0275text(1);
    \u0275\u0275elementEnd();
  }
  if (rf & 2) {
    const data_r3 = ctx.$implicit;
    \u0275\u0275advance();
    \u0275\u0275textInterpolate(data_r3.month.substring(0, 3));
  }
}
function DashboardComponent_div_99_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "div", 60)(1, "div", 61);
    \u0275\u0275template(2, DashboardComponent_div_99_div_2_Template, 3, 3, "div", 62);
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(3, "div", 63);
    \u0275\u0275template(4, DashboardComponent_div_99_span_4_Template, 2, 1, "span", 64);
    \u0275\u0275elementEnd()();
  }
  if (rf & 2) {
    const ctx_r0 = \u0275\u0275nextContext();
    \u0275\u0275advance(2);
    \u0275\u0275property("ngForOf", ctx_r0.salesData);
    \u0275\u0275advance(2);
    \u0275\u0275property("ngForOf", ctx_r0.salesData);
  }
}
function DashboardComponent_div_109_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "div", 67);
    \u0275\u0275element(1, "div", 57);
    \u0275\u0275elementStart(2, "p");
    \u0275\u0275text(3, "Chargement des commandes...");
    \u0275\u0275elementEnd()();
  }
}
function DashboardComponent_div_110_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "div", 58)(1, "div", 59);
    \u0275\u0275text(2, "\u{1F4ED}");
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(3, "h4");
    \u0275\u0275text(4, "Aucune commande r\xE9cente");
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(5, "p");
    \u0275\u0275text(6, "Les nouvelles commandes appara\xEEtront ici");
    \u0275\u0275elementEnd()();
  }
}
function DashboardComponent_div_111_div_1_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "div", 70)(1, "div", 71)(2, "div", 72);
    \u0275\u0275text(3);
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(4, "div", 73);
    \u0275\u0275text(5);
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(6, "div", 74);
    \u0275\u0275text(7);
    \u0275\u0275pipe(8, "date");
    \u0275\u0275elementEnd()();
    \u0275\u0275elementStart(9, "div", 75)(10, "div", 76);
    \u0275\u0275text(11);
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(12, "span", 77);
    \u0275\u0275text(13);
    \u0275\u0275elementEnd()()();
  }
  if (rf & 2) {
    const order_r4 = ctx.$implicit;
    const ctx_r0 = \u0275\u0275nextContext(2);
    \u0275\u0275advance(3);
    \u0275\u0275textInterpolate(order_r4.reference);
    \u0275\u0275advance(2);
    \u0275\u0275textInterpolate(order_r4.client);
    \u0275\u0275advance(2);
    \u0275\u0275textInterpolate(\u0275\u0275pipeBind2(8, 6, order_r4.date, "dd/MM/yyyy"));
    \u0275\u0275advance(4);
    \u0275\u0275textInterpolate(ctx_r0.formatCurrency(order_r4.amount));
    \u0275\u0275advance();
    \u0275\u0275property("ngClass", "badge-" + ctx_r0.getStatusClass(order_r4.status));
    \u0275\u0275advance();
    \u0275\u0275textInterpolate1(" ", order_r4.status, " ");
  }
}
function DashboardComponent_div_111_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "div", 68);
    \u0275\u0275template(1, DashboardComponent_div_111_div_1_Template, 14, 9, "div", 69);
    \u0275\u0275elementEnd();
  }
  if (rf & 2) {
    const ctx_r0 = \u0275\u0275nextContext();
    \u0275\u0275advance();
    \u0275\u0275property("ngForOf", ctx_r0.recentOrders);
  }
}
function DashboardComponent_div_119_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "div", 67);
    \u0275\u0275element(1, "div", 57);
    \u0275\u0275elementStart(2, "p");
    \u0275\u0275text(3, "Chargement de l'activit\xE9...");
    \u0275\u0275elementEnd()();
  }
}
function DashboardComponent_div_120_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "div", 58)(1, "div", 59);
    \u0275\u0275text(2, "\u{1F4ED}");
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(3, "h4");
    \u0275\u0275text(4, "Aucune activit\xE9 r\xE9cente");
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(5, "p");
    \u0275\u0275text(6, "Vos derni\xE8res actions appara\xEEtront ici");
    \u0275\u0275elementEnd()();
  }
}
function DashboardComponent_div_121_div_1_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "div", 80)(1, "div", 81);
    \u0275\u0275text(2);
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(3, "div", 82)(4, "div", 83);
    \u0275\u0275text(5);
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(6, "div", 84);
    \u0275\u0275text(7);
    \u0275\u0275elementEnd()()();
  }
  if (rf & 2) {
    const activity_r5 = ctx.$implicit;
    \u0275\u0275advance(2);
    \u0275\u0275textInterpolate(activity_r5.icon);
    \u0275\u0275advance(3);
    \u0275\u0275textInterpolate(activity_r5.title);
    \u0275\u0275advance(2);
    \u0275\u0275textInterpolate(activity_r5.time);
  }
}
function DashboardComponent_div_121_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "div", 78);
    \u0275\u0275template(1, DashboardComponent_div_121_div_1_Template, 8, 3, "div", 79);
    \u0275\u0275elementEnd();
  }
  if (rf & 2) {
    const ctx_r0 = \u0275\u0275nextContext();
    \u0275\u0275advance();
    \u0275\u0275property("ngForOf", ctx_r0.recentActivity);
  }
}
var DashboardComponent = class _DashboardComponent {
  authService;
  dashboardService;
  currentUser = null;
  fournisseurInfo = null;
  // États de chargement
  isLoading = true;
  isStatsLoading = true;
  isOrdersLoading = true;
  isActivityLoading = true;
  // Statistiques du dashboard (données réelles)
  stats = {
    totalProducts: 0,
    activeOrders: 0,
    pendingDeliveries: 0,
    monthlyRevenue: 0
  };
  // Commandes récentes (données réelles)
  recentOrders = [];
  // Activité récente (données réelles)
  recentActivity = [];
  // Données pour le graphique des ventes (calculées dynamiquement)
  salesData = [];
  constructor(authService, dashboardService) {
    this.authService = authService;
    this.dashboardService = dashboardService;
  }
  ngOnInit() {
    this.authService.currentUser$.subscribe((user) => {
      this.currentUser = user;
    });
    this.loadDashboardData();
  }
  /**
   * Charger toutes les données du dashboard
   */
  loadDashboardData() {
    this.isLoading = true;
    console.log("\u{1F504} Chargement des donn\xE9es du dashboard...");
    const currentUser = this.authService.getCurrentUser();
    const supplierId = localStorage.getItem("supplierId");
    console.log("\u{1F464} Utilisateur connect\xE9:", currentUser);
    console.log("\u{1F3EA} ID Fournisseur (localStorage):", supplierId);
    this.dashboardService.getFournisseurInfo().subscribe({
      next: (fournisseur) => {
        this.fournisseurInfo = fournisseur;
        console.log("\u2705 Informations fournisseur charg\xE9es:", fournisseur);
      },
      error: (error) => {
        console.error("\u274C Erreur lors du chargement du fournisseur:", error);
      }
    });
    this.loadStats();
    this.loadRecentOrders();
    this.loadRecentActivity();
    this.calculateSalesData();
    this.isLoading = false;
  }
  /**
   * Charger les statistiques
   */
  loadStats() {
    this.isStatsLoading = true;
    console.log("\u{1F4CA} Chargement des statistiques...");
    this.dashboardService.getDashboardStats().subscribe({
      next: (stats) => {
        this.stats = stats;
        this.isStatsLoading = false;
        console.log("\u2705 Statistiques charg\xE9es:", stats);
      },
      error: (error) => {
        console.error("\u274C Erreur lors du chargement des statistiques:", error);
        this.isStatsLoading = false;
      }
    });
  }
  /**
   * Charger les commandes récentes
   */
  loadRecentOrders() {
    this.isOrdersLoading = true;
    this.dashboardService.getRecentOrders().subscribe({
      next: (orders) => {
        this.recentOrders = orders;
        this.isOrdersLoading = false;
        console.log("Commandes r\xE9centes charg\xE9es:", orders);
      },
      error: (error) => {
        console.error("Erreur lors du chargement des commandes:", error);
        this.isOrdersLoading = false;
      }
    });
  }
  /**
   * Charger l'activité récente
   */
  loadRecentActivity() {
    this.isActivityLoading = true;
    this.dashboardService.getRecentActivity().subscribe({
      next: (activity) => {
        this.recentActivity = activity;
        this.isActivityLoading = false;
        console.log("Activit\xE9 r\xE9cente charg\xE9e:", activity);
      },
      error: (error) => {
        console.error("Erreur lors du chargement de l'activit\xE9:", error);
        this.isActivityLoading = false;
      }
    });
  }
  getStatusClass(status) {
    switch (status) {
      case "Livr\xE9":
        return "status-delivered";
      case "En cours":
        return "status-processing";
      case "En pr\xE9paration":
        return "status-preparing";
      default:
        return "status-default";
    }
  }
  /**
   * Calculer les données de ventes pour le graphique
   */
  calculateSalesData() {
    this.dashboardService.getFournisseurOrders().subscribe({
      next: (orders) => {
        const monthNames = [
          "Janvier",
          "F\xE9vrier",
          "Mars",
          "Avril",
          "Mai",
          "Juin",
          "Juillet",
          "Ao\xFBt",
          "Septembre",
          "Octobre",
          "Novembre",
          "D\xE9cembre"
        ];
        const currentDate = /* @__PURE__ */ new Date();
        const salesByMonth = [];
        for (let i = 5; i >= 0; i--) {
          const monthDate = new Date(currentDate.getFullYear(), currentDate.getMonth() - i, 1);
          const monthName = monthNames[monthDate.getMonth()];
          const monthlySales = orders.filter((order) => {
            const orderDate = new Date(order.dateCommande);
            return orderDate.getMonth() === monthDate.getMonth() && orderDate.getFullYear() === monthDate.getFullYear();
          }).reduce((total, order) => total + order.montantTotal, 0);
          salesByMonth.push({
            month: monthName,
            sales: Math.round(monthlySales)
            // Arrondir pour l'affichage
          });
        }
        this.salesData = salesByMonth;
        console.log("\u{1F4CA} Donn\xE9es de ventes calcul\xE9es:", this.salesData);
      },
      error: (error) => {
        console.error("\u274C Erreur lors du calcul des donn\xE9es de ventes:", error);
        this.salesData = [];
      }
    });
  }
  /**
   * Obtenir la valeur maximale des ventes pour normaliser le graphique
   */
  getMaxSales() {
    if (this.salesData.length === 0)
      return 1;
    const maxSales = Math.max(...this.salesData.map((data) => data.sales));
    return maxSales > 0 ? maxSales : 1;
  }
  formatCurrency(amount) {
    return new Intl.NumberFormat("fr-TN", {
      style: "currency",
      currency: "TND"
    }).format(amount);
  }
  static \u0275fac = function DashboardComponent_Factory(__ngFactoryType__) {
    return new (__ngFactoryType__ || _DashboardComponent)(\u0275\u0275directiveInject(AuthService), \u0275\u0275directiveInject(DashboardService));
  };
  static \u0275cmp = /* @__PURE__ */ \u0275\u0275defineComponent({ type: _DashboardComponent, selectors: [["app-dashboard"]], decls: 162, vars: 33, consts: [[1, "dashboard-container"], [1, "dashboard-header"], [1, "header-content"], [1, "header-text"], [1, "dashboard-title"], [1, "title-icon"], [4, "ngIf"], [1, "dashboard-subtitle"], [1, "header-actions"], ["routerLink", "/dashboard/products", 1, "btn", "btn-primary", "btn-lg"], [1, "stats-section"], [1, "stats-grid"], [1, "stat-card", "stat-primary"], [1, "stat-header"], [1, "stat-icon-wrapper"], [1, "stat-icon"], [1, "stat-trend", 3, "ngClass"], ["class", "trend-icon", 4, "ngIf"], [1, "stat-content"], ["class", "stat-number", 4, "ngIf"], ["class", "stat-number loading", 4, "ngIf"], [1, "stat-label"], [1, "stat-description"], [1, "stat-card", "stat-success"], [1, "stat-card", "stat-warning"], [1, "stat-card", "stat-info"], [1, "dashboard-main"], [1, "dashboard-grid"], [1, "dashboard-card", "chart-card"], [1, "card-header"], [1, "card-title"], [1, "card-icon"], [1, "card-actions"], [1, "period-selector"], [1, "card-body"], [1, "chart-container"], ["class", "empty-state", 4, "ngIf"], ["class", "chart-placeholder", 4, "ngIf"], [1, "dashboard-card", "orders-card"], ["routerLink", "/dashboard/orders", 1, "card-link"], ["class", "loading-state", 4, "ngIf"], ["class", "orders-list", 4, "ngIf"], [1, "dashboard-card", "activity-card"], ["class", "activity-list", 4, "ngIf"], [1, "dashboard-card", "actions-card"], [1, "actions-grid"], ["routerLink", "/dashboard/products", 1, "action-button"], [1, "action-icon"], [1, "action-content"], [1, "action-title"], [1, "action-subtitle"], ["routerLink", "/dashboard/referentiels", 1, "action-button"], ["routerLink", "/dashboard/orders", 1, "action-button"], ["routerLink", "/dashboard/profile", 1, "action-button"], [1, "trend-icon"], [1, "stat-number"], [1, "stat-number", "loading"], [1, "spinner"], [1, "empty-state"], [1, "empty-icon"], [1, "chart-placeholder"], [1, "chart-bars"], ["class", "chart-bar", 3, "height", 4, "ngFor", "ngForOf"], [1, "chart-labels"], [4, "ngFor", "ngForOf"], [1, "chart-bar"], [1, "bar-tooltip"], [1, "loading-state"], [1, "orders-list"], ["class", "order-item", 4, "ngFor", "ngForOf"], [1, "order-item"], [1, "order-info"], [1, "order-id"], [1, "order-customer"], [1, "order-date"], [1, "order-details"], [1, "order-amount"], [1, "badge", 3, "ngClass"], [1, "activity-list"], ["class", "activity-item", 4, "ngFor", "ngForOf"], [1, "activity-item"], [1, "activity-icon"], [1, "activity-content"], [1, "activity-title"], [1, "activity-time"]], template: function DashboardComponent_Template(rf, ctx) {
    if (rf & 1) {
      \u0275\u0275elementStart(0, "div", 0)(1, "div", 1)(2, "div", 2)(3, "div", 3)(4, "h1", 4)(5, "span", 5);
      \u0275\u0275text(6, "\u{1F44B}");
      \u0275\u0275elementEnd();
      \u0275\u0275template(7, DashboardComponent_span_7_Template, 2, 2, "span", 6)(8, DashboardComponent_span_8_Template, 2, 2, "span", 6)(9, DashboardComponent_span_9_Template, 2, 0, "span", 6);
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(10, "p", 7);
      \u0275\u0275template(11, DashboardComponent_span_11_Template, 2, 1, "span", 6)(12, DashboardComponent_span_12_Template, 2, 0, "span", 6);
      \u0275\u0275elementEnd()();
      \u0275\u0275elementStart(13, "div", 8)(14, "button", 9)(15, "span");
      \u0275\u0275text(16, "\u2795");
      \u0275\u0275elementEnd();
      \u0275\u0275text(17, " Nouveau produit ");
      \u0275\u0275elementEnd()()()();
      \u0275\u0275elementStart(18, "div", 10)(19, "div", 11)(20, "div", 12)(21, "div", 13)(22, "div", 14)(23, "span", 15);
      \u0275\u0275text(24, "\u{1F4E6}");
      \u0275\u0275elementEnd()();
      \u0275\u0275elementStart(25, "div", 16);
      \u0275\u0275template(26, DashboardComponent_span_26_Template, 2, 0, "span", 17)(27, DashboardComponent_span_27_Template, 2, 0, "span", 17);
      \u0275\u0275elementEnd()();
      \u0275\u0275elementStart(28, "div", 18);
      \u0275\u0275template(29, DashboardComponent_div_29_Template, 2, 1, "div", 19)(30, DashboardComponent_div_30_Template, 2, 0, "div", 20);
      \u0275\u0275elementStart(31, "div", 21);
      \u0275\u0275text(32, "Produits actifs");
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(33, "div", 22);
      \u0275\u0275text(34, "Dans votre catalogue");
      \u0275\u0275elementEnd()()();
      \u0275\u0275elementStart(35, "div", 23)(36, "div", 13)(37, "div", 14)(38, "span", 15);
      \u0275\u0275text(39, "\u{1F4CB}");
      \u0275\u0275elementEnd()();
      \u0275\u0275elementStart(40, "div", 16);
      \u0275\u0275template(41, DashboardComponent_span_41_Template, 2, 0, "span", 17)(42, DashboardComponent_span_42_Template, 2, 0, "span", 17);
      \u0275\u0275elementEnd()();
      \u0275\u0275elementStart(43, "div", 18);
      \u0275\u0275template(44, DashboardComponent_div_44_Template, 2, 1, "div", 19)(45, DashboardComponent_div_45_Template, 2, 0, "div", 20);
      \u0275\u0275elementStart(46, "div", 21);
      \u0275\u0275text(47, "Commandes ce mois");
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(48, "div", 22);
      \u0275\u0275text(49, "En cours de traitement");
      \u0275\u0275elementEnd()()();
      \u0275\u0275elementStart(50, "div", 24)(51, "div", 13)(52, "div", 14)(53, "span", 15);
      \u0275\u0275text(54, "\u{1F4B0}");
      \u0275\u0275elementEnd()();
      \u0275\u0275elementStart(55, "div", 16);
      \u0275\u0275template(56, DashboardComponent_span_56_Template, 2, 0, "span", 17)(57, DashboardComponent_span_57_Template, 2, 0, "span", 17);
      \u0275\u0275elementEnd()();
      \u0275\u0275elementStart(58, "div", 18);
      \u0275\u0275template(59, DashboardComponent_div_59_Template, 2, 1, "div", 19)(60, DashboardComponent_div_60_Template, 2, 0, "div", 20);
      \u0275\u0275elementStart(61, "div", 21);
      \u0275\u0275text(62, "Chiffre d'affaires");
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(63, "div", 22);
      \u0275\u0275text(64, "Ce mois-ci");
      \u0275\u0275elementEnd()()();
      \u0275\u0275elementStart(65, "div", 25)(66, "div", 13)(67, "div", 14)(68, "span", 15);
      \u0275\u0275text(69, "\u{1F69A}");
      \u0275\u0275elementEnd()();
      \u0275\u0275elementStart(70, "div", 16);
      \u0275\u0275template(71, DashboardComponent_span_71_Template, 2, 0, "span", 17)(72, DashboardComponent_span_72_Template, 2, 0, "span", 17);
      \u0275\u0275elementEnd()();
      \u0275\u0275elementStart(73, "div", 18);
      \u0275\u0275template(74, DashboardComponent_div_74_Template, 2, 1, "div", 19)(75, DashboardComponent_div_75_Template, 2, 0, "div", 20);
      \u0275\u0275elementStart(76, "div", 21);
      \u0275\u0275text(77, "Livraisons");
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(78, "div", 22);
      \u0275\u0275text(79, "En cours");
      \u0275\u0275elementEnd()()()()();
      \u0275\u0275elementStart(80, "div", 26)(81, "div", 27)(82, "div", 28)(83, "div", 29)(84, "h3", 30)(85, "span", 31);
      \u0275\u0275text(86, "\u{1F4C8}");
      \u0275\u0275elementEnd();
      \u0275\u0275text(87, " \xC9volution des ventes ");
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(88, "div", 32)(89, "select", 33)(90, "option");
      \u0275\u0275text(91, "7 derniers jours");
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(92, "option");
      \u0275\u0275text(93, "30 derniers jours");
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(94, "option");
      \u0275\u0275text(95, "3 derniers mois");
      \u0275\u0275elementEnd()()()();
      \u0275\u0275elementStart(96, "div", 34)(97, "div", 35);
      \u0275\u0275template(98, DashboardComponent_div_98_Template, 7, 0, "div", 36)(99, DashboardComponent_div_99_Template, 5, 2, "div", 37);
      \u0275\u0275elementEnd()()();
      \u0275\u0275elementStart(100, "div", 38)(101, "div", 29)(102, "h3", 30)(103, "span", 31);
      \u0275\u0275text(104, "\u{1F4CB}");
      \u0275\u0275elementEnd();
      \u0275\u0275text(105, " Commandes r\xE9centes ");
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(106, "a", 39);
      \u0275\u0275text(107, "Voir tout");
      \u0275\u0275elementEnd()();
      \u0275\u0275elementStart(108, "div", 34);
      \u0275\u0275template(109, DashboardComponent_div_109_Template, 4, 0, "div", 40)(110, DashboardComponent_div_110_Template, 7, 0, "div", 36)(111, DashboardComponent_div_111_Template, 2, 1, "div", 41);
      \u0275\u0275elementEnd()();
      \u0275\u0275elementStart(112, "div", 42)(113, "div", 29)(114, "h3", 30)(115, "span", 31);
      \u0275\u0275text(116, "\u{1F514}");
      \u0275\u0275elementEnd();
      \u0275\u0275text(117, " Activit\xE9 r\xE9cente ");
      \u0275\u0275elementEnd()();
      \u0275\u0275elementStart(118, "div", 34);
      \u0275\u0275template(119, DashboardComponent_div_119_Template, 4, 0, "div", 40)(120, DashboardComponent_div_120_Template, 7, 0, "div", 36)(121, DashboardComponent_div_121_Template, 2, 1, "div", 43);
      \u0275\u0275elementEnd()();
      \u0275\u0275elementStart(122, "div", 44)(123, "div", 29)(124, "h3", 30)(125, "span", 31);
      \u0275\u0275text(126, "\u26A1");
      \u0275\u0275elementEnd();
      \u0275\u0275text(127, " Actions rapides ");
      \u0275\u0275elementEnd()();
      \u0275\u0275elementStart(128, "div", 34)(129, "div", 45)(130, "button", 46)(131, "div", 47);
      \u0275\u0275text(132, "\u{1F4E6}");
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(133, "div", 48)(134, "div", 49);
      \u0275\u0275text(135, "Produits");
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(136, "div", 50);
      \u0275\u0275text(137, "G\xE9rer le catalogue");
      \u0275\u0275elementEnd()()();
      \u0275\u0275elementStart(138, "button", 51)(139, "div", 47);
      \u0275\u0275text(140, "\u{1F5C2}\uFE0F");
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(141, "div", 48)(142, "div", 49);
      \u0275\u0275text(143, "R\xE9f\xE9rentiels");
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(144, "div", 50);
      \u0275\u0275text(145, "Cat\xE9gories & marques");
      \u0275\u0275elementEnd()()();
      \u0275\u0275elementStart(146, "button", 52)(147, "div", 47);
      \u0275\u0275text(148, "\u{1F4CB}");
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(149, "div", 48)(150, "div", 49);
      \u0275\u0275text(151, "Commandes");
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(152, "div", 50);
      \u0275\u0275text(153, "Suivi & gestion");
      \u0275\u0275elementEnd()()();
      \u0275\u0275elementStart(154, "button", 53)(155, "div", 47);
      \u0275\u0275text(156, "\u{1F464}");
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(157, "div", 48)(158, "div", 49);
      \u0275\u0275text(159, "Profil");
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(160, "div", 50);
      \u0275\u0275text(161, "Param\xE8tres compte");
      \u0275\u0275elementEnd()()()()()()()()();
    }
    if (rf & 2) {
      \u0275\u0275advance(7);
      \u0275\u0275property("ngIf", ctx.fournisseurInfo);
      \u0275\u0275advance();
      \u0275\u0275property("ngIf", !ctx.fournisseurInfo && ctx.currentUser);
      \u0275\u0275advance();
      \u0275\u0275property("ngIf", !ctx.fournisseurInfo && !ctx.currentUser);
      \u0275\u0275advance(2);
      \u0275\u0275property("ngIf", ctx.fournisseurInfo);
      \u0275\u0275advance();
      \u0275\u0275property("ngIf", !ctx.fournisseurInfo);
      \u0275\u0275advance(13);
      \u0275\u0275property("ngClass", ctx.stats.totalProducts > 0 ? "positive" : "neutral");
      \u0275\u0275advance();
      \u0275\u0275property("ngIf", ctx.stats.totalProducts > 0);
      \u0275\u0275advance();
      \u0275\u0275property("ngIf", ctx.stats.totalProducts === 0);
      \u0275\u0275advance(2);
      \u0275\u0275property("ngIf", !ctx.isStatsLoading);
      \u0275\u0275advance();
      \u0275\u0275property("ngIf", ctx.isStatsLoading);
      \u0275\u0275advance(10);
      \u0275\u0275property("ngClass", ctx.stats.activeOrders > 0 ? "positive" : "neutral");
      \u0275\u0275advance();
      \u0275\u0275property("ngIf", ctx.stats.activeOrders > 0);
      \u0275\u0275advance();
      \u0275\u0275property("ngIf", ctx.stats.activeOrders === 0);
      \u0275\u0275advance(2);
      \u0275\u0275property("ngIf", !ctx.isStatsLoading);
      \u0275\u0275advance();
      \u0275\u0275property("ngIf", ctx.isStatsLoading);
      \u0275\u0275advance(10);
      \u0275\u0275property("ngClass", ctx.stats.monthlyRevenue > 0 ? "positive" : "neutral");
      \u0275\u0275advance();
      \u0275\u0275property("ngIf", ctx.stats.monthlyRevenue > 0);
      \u0275\u0275advance();
      \u0275\u0275property("ngIf", ctx.stats.monthlyRevenue === 0);
      \u0275\u0275advance(2);
      \u0275\u0275property("ngIf", !ctx.isStatsLoading);
      \u0275\u0275advance();
      \u0275\u0275property("ngIf", ctx.isStatsLoading);
      \u0275\u0275advance(10);
      \u0275\u0275property("ngClass", ctx.stats.pendingDeliveries > 0 ? "positive" : "neutral");
      \u0275\u0275advance();
      \u0275\u0275property("ngIf", ctx.stats.pendingDeliveries > 0);
      \u0275\u0275advance();
      \u0275\u0275property("ngIf", ctx.stats.pendingDeliveries === 0);
      \u0275\u0275advance(2);
      \u0275\u0275property("ngIf", !ctx.isStatsLoading);
      \u0275\u0275advance();
      \u0275\u0275property("ngIf", ctx.isStatsLoading);
      \u0275\u0275advance(23);
      \u0275\u0275property("ngIf", ctx.salesData.length === 0);
      \u0275\u0275advance();
      \u0275\u0275property("ngIf", ctx.salesData.length > 0);
      \u0275\u0275advance(10);
      \u0275\u0275property("ngIf", ctx.isOrdersLoading);
      \u0275\u0275advance();
      \u0275\u0275property("ngIf", !ctx.isOrdersLoading && ctx.recentOrders.length === 0);
      \u0275\u0275advance();
      \u0275\u0275property("ngIf", !ctx.isOrdersLoading && ctx.recentOrders.length > 0);
      \u0275\u0275advance(8);
      \u0275\u0275property("ngIf", ctx.isActivityLoading);
      \u0275\u0275advance();
      \u0275\u0275property("ngIf", !ctx.isActivityLoading && ctx.recentActivity.length === 0);
      \u0275\u0275advance();
      \u0275\u0275property("ngIf", !ctx.isActivityLoading && ctx.recentActivity.length > 0);
    }
  }, dependencies: [CommonModule, NgClass, NgForOf, NgIf, DatePipe, RouterModule, RouterLink], styles: [`

.dashboard-container[_ngcontent-%COMP%] {
  padding: var(--spacing-8);
  max-width: 1400px;
  margin: 0 auto;
  background: transparent;
  min-height: 100vh;
}
.dashboard-header[_ngcontent-%COMP%] {
  background: var(--gradient-primary);
  border-radius: var(--border-radius-2xl);
  padding: var(--spacing-8);
  margin-bottom: var(--spacing-8);
  box-shadow: var(--shadow-blue-lg);
  position: relative;
  overflow: hidden;
}
.dashboard-header[_ngcontent-%COMP%]::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="50" cy="50" r="1" fill="rgba(255,255,255,0.1)"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
  opacity: 0.3;
}
.header-content[_ngcontent-%COMP%] {
  display: flex;
  justify-content: space-between;
  align-items: center;
  position: relative;
  z-index: 1;
}
.header-text[_ngcontent-%COMP%] {
  color: var(--white);
}
.dashboard-title[_ngcontent-%COMP%] {
  font-size: var(--font-size-4xl);
  font-weight: var(--font-weight-extrabold);
  margin: 0 0 var(--spacing-2) 0;
  display: flex;
  align-items: center;
  gap: var(--spacing-3);
}
.title-icon[_ngcontent-%COMP%] {
  font-size: var(--font-size-3xl);
  animation: _ngcontent-%COMP%_wave 2s ease-in-out infinite;
}
@keyframes _ngcontent-%COMP%_wave {
  0%, 100% {
    transform: rotate(0deg);
  }
  25% {
    transform: rotate(20deg);
  }
  75% {
    transform: rotate(-10deg);
  }
}
.dashboard-subtitle[_ngcontent-%COMP%] {
  font-size: var(--font-size-lg);
  opacity: 0.9;
  margin: 0;
  font-weight: var(--font-weight-normal);
}
.header-actions[_ngcontent-%COMP%] {
  display: flex;
  gap: var(--spacing-4);
}
.stats-section[_ngcontent-%COMP%] {
  margin-bottom: var(--spacing-8);
}
.stats-grid[_ngcontent-%COMP%] {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: var(--spacing-6);
}
.stat-card[_ngcontent-%COMP%] {
  background: var(--white);
  border-radius: var(--border-radius-xl);
  padding: var(--spacing-6);
  box-shadow: var(--shadow-base);
  transition: all var(--transition-base);
  position: relative;
  overflow: hidden;
}
.stat-card[_ngcontent-%COMP%]::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  transition: all var(--transition-base);
}
.stat-card.stat-primary[_ngcontent-%COMP%]::before {
  background: var(--gradient-primary);
}
.stat-card.stat-success[_ngcontent-%COMP%]::before {
  background:
    linear-gradient(
      90deg,
      var(--success-500),
      var(--success-400));
}
.stat-card.stat-warning[_ngcontent-%COMP%]::before {
  background:
    linear-gradient(
      90deg,
      var(--warning-500),
      var(--warning-400));
}
.stat-card.stat-info[_ngcontent-%COMP%]::before {
  background: var(--gradient-primary);
}
.stat-card[_ngcontent-%COMP%]:hover {
  transform: translateY(-4px);
  box-shadow: var(--shadow-lg);
}
.stat-header[_ngcontent-%COMP%] {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--spacing-4);
}
.stat-icon-wrapper[_ngcontent-%COMP%] {
  width: 48px;
  height: 48px;
  border-radius: var(--border-radius-xl);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: var(--font-size-xl);
}
.stat-primary[_ngcontent-%COMP%]   .stat-icon-wrapper[_ngcontent-%COMP%] {
  background: var(--primary-100);
}
.stat-success[_ngcontent-%COMP%]   .stat-icon-wrapper[_ngcontent-%COMP%] {
  background: var(--success-100);
}
.stat-warning[_ngcontent-%COMP%]   .stat-icon-wrapper[_ngcontent-%COMP%] {
  background: var(--warning-100);
}
.stat-info[_ngcontent-%COMP%]   .stat-icon-wrapper[_ngcontent-%COMP%] {
  background: var(--primary-100);
}
.stat-trend[_ngcontent-%COMP%] {
  display: flex;
  align-items: center;
  gap: var(--spacing-1);
  font-size: var(--font-size-xs);
  font-weight: var(--font-weight-medium);
  padding: var(--spacing-1) var(--spacing-2);
  border-radius: var(--border-radius-full);
}
.stat-trend.positive[_ngcontent-%COMP%] {
  background: var(--success-100);
  color: var(--success-700);
}
.stat-trend.neutral[_ngcontent-%COMP%] {
  background: var(--gray-100);
  color: var(--gray-600);
}
.stat-number[_ngcontent-%COMP%] {
  font-size: var(--font-size-3xl);
  font-weight: var(--font-weight-extrabold);
  color: var(--gray-900);
  margin-bottom: var(--spacing-1);
  line-height: 1;
}
.stat-number.loading[_ngcontent-%COMP%] {
  display: flex;
  align-items: center;
  gap: var(--spacing-2);
}
.stat-label[_ngcontent-%COMP%] {
  font-size: var(--font-size-base);
  font-weight: var(--font-weight-semibold);
  color: var(--gray-700);
  margin-bottom: var(--spacing-1);
}
.stat-description[_ngcontent-%COMP%] {
  font-size: var(--font-size-sm);
  color: var(--gray-500);
}
.dashboard-main[_ngcontent-%COMP%] {
  margin-bottom: var(--spacing-8);
}
.dashboard-grid[_ngcontent-%COMP%] {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
  gap: var(--spacing-6);
}
.dashboard-card[_ngcontent-%COMP%] {
  background: var(--white);
  border-radius: var(--border-radius-xl);
  box-shadow: var(--shadow-base);
  overflow: hidden;
  transition: all var(--transition-base);
  border: 1px solid var(--gray-200);
}
.dashboard-card[_ngcontent-%COMP%]:hover {
  box-shadow: var(--shadow-lg);
  transform: translateY(-4px);
  border-color: var(--primary-300);
}
.chart-card[_ngcontent-%COMP%] {
  background:
    linear-gradient(
      135deg,
      var(--white) 0%,
      #f8faff 100%);
  border: 1px solid #e1e7ff;
}
.orders-card[_ngcontent-%COMP%] {
  background:
    linear-gradient(
      135deg,
      var(--white) 0%,
      #f0f4ff 100%);
  border: 1px solid #d1d9ff;
}
.activity-card[_ngcontent-%COMP%] {
  background:
    linear-gradient(
      135deg,
      var(--white) 0%,
      #f0fff4 100%);
  border: 1px solid #d1fae5;
}
.actions-card[_ngcontent-%COMP%] {
  background:
    linear-gradient(
      135deg,
      var(--white) 0%,
      #fffbf0 100%);
  border: 1px solid #fed7aa;
}
.card-header[_ngcontent-%COMP%] {
  padding: var(--spacing-6);
  border-bottom: 1px solid var(--gray-200);
  background:
    linear-gradient(
      135deg,
      var(--gray-50) 0%,
      var(--white) 100%);
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.card-title[_ngcontent-%COMP%] {
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-bold);
  color: var(--gray-900);
  margin: 0;
  display: flex;
  align-items: center;
  gap: var(--spacing-2);
}
.card-icon[_ngcontent-%COMP%] {
  font-size: var(--font-size-xl);
}
.card-link[_ngcontent-%COMP%] {
  color: var(--primary-600);
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
  text-decoration: none;
  transition: var(--transition-fast);
}
.card-link[_ngcontent-%COMP%]:hover {
  color: var(--primary-700);
  text-decoration: underline;
}
.card-body[_ngcontent-%COMP%] {
  padding: var(--spacing-6);
  background: var(--white);
  border-radius: 0 0 var(--border-radius-xl) var(--border-radius-xl);
}
.period-selector[_ngcontent-%COMP%] {
  padding: var(--spacing-2) var(--spacing-3);
  border: 1px solid var(--gray-300);
  border-radius: var(--border-radius-lg);
  font-size: var(--font-size-sm);
  background: var(--white);
  color: var(--gray-700);
}
.loading-state[_ngcontent-%COMP%] {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: var(--spacing-8);
  color: var(--gray-500);
  gap: var(--spacing-3);
}
.empty-state[_ngcontent-%COMP%] {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: var(--spacing-8);
  color: var(--gray-500);
  text-align: center;
}
.empty-icon[_ngcontent-%COMP%] {
  font-size: var(--font-size-4xl);
  margin-bottom: var(--spacing-3);
}
.empty-state[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%] {
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-semibold);
  color: var(--gray-700);
  margin: 0 0 var(--spacing-2) 0;
}
.empty-state[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {
  font-size: var(--font-size-sm);
  color: var(--gray-500);
  margin: 0;
}
.chart-container[_ngcontent-%COMP%] {
  height: 300px;
  display: flex;
  align-items: center;
  justify-content: center;
  background:
    linear-gradient(
      135deg,
      #f8faff 0%,
      var(--white) 100%);
  border-radius: var(--border-radius-lg);
  margin: var(--spacing-2);
}
.chart-placeholder[_ngcontent-%COMP%] {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: flex-end;
  align-items: center;
  padding: var(--spacing-4);
}
.chart-bars[_ngcontent-%COMP%] {
  display: flex;
  align-items: flex-end;
  gap: var(--spacing-3);
  height: 200px;
  margin-bottom: var(--spacing-4);
}
.chart-bar[_ngcontent-%COMP%] {
  width: 40px;
  background: var(--gradient-primary);
  border-radius: var(--border-radius-md) var(--border-radius-md) 0 0;
  transition: all var(--transition-base);
  position: relative;
  cursor: pointer;
  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
}
.chart-bar[_ngcontent-%COMP%]:hover {
  background:
    linear-gradient(
      180deg,
      #5a67d8 0%,
      #667eea 100%);
  transform: scale(1.05);
  box-shadow: 0 6px 16px rgba(102, 126, 234, 0.4);
}
.bar-tooltip[_ngcontent-%COMP%] {
  position: absolute;
  top: -30px;
  left: 50%;
  transform: translateX(-50%);
  background: var(--white);
  color: var(--gray-800);
  padding: var(--spacing-1) var(--spacing-2);
  border-radius: var(--border-radius-md);
  font-size: var(--font-size-xs);
  opacity: 0;
  transition: var(--transition-fast);
  box-shadow: var(--shadow-lg);
  border: 1px solid var(--gray-200);
  font-weight: var(--font-weight-semibold);
}
.chart-bar[_ngcontent-%COMP%]:hover   .bar-tooltip[_ngcontent-%COMP%] {
  opacity: 1;
}
.chart-labels[_ngcontent-%COMP%] {
  display: flex;
  gap: var(--spacing-3);
}
.chart-labels[_ngcontent-%COMP%]   span[_ngcontent-%COMP%] {
  width: 40px;
  text-align: center;
  font-size: var(--font-size-xs);
  color: var(--gray-600);
  font-weight: var(--font-weight-medium);
}
.orders-list[_ngcontent-%COMP%] {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-4);
}
.order-item[_ngcontent-%COMP%] {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--spacing-4);
  background: var(--gray-50);
  border-radius: var(--border-radius-lg);
  transition: all var(--transition-fast);
  border-left: 4px solid var(--primary-500);
}
.order-item[_ngcontent-%COMP%]:hover {
  background: var(--primary-50);
  transform: translateX(4px);
}
.order-info[_ngcontent-%COMP%] {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-1);
}
.order-id[_ngcontent-%COMP%] {
  font-weight: var(--font-weight-bold);
  color: var(--gray-900);
  font-size: var(--font-size-sm);
}
.order-customer[_ngcontent-%COMP%] {
  color: var(--gray-600);
  font-size: var(--font-size-sm);
}
.order-date[_ngcontent-%COMP%] {
  color: var(--gray-500);
  font-size: var(--font-size-xs);
}
.order-details[_ngcontent-%COMP%] {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  gap: var(--spacing-2);
}
.order-amount[_ngcontent-%COMP%] {
  font-weight: var(--font-weight-bold);
  font-size: var(--font-size-lg);
  color: var(--primary-600);
}
.activity-list[_ngcontent-%COMP%] {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-4);
}
.activity-item[_ngcontent-%COMP%] {
  display: flex;
  align-items: center;
  gap: var(--spacing-3);
  padding: var(--spacing-3);
  border-radius: var(--border-radius-lg);
  transition: var(--transition-fast);
}
.activity-item[_ngcontent-%COMP%]:hover {
  background: var(--gray-50);
}
.activity-icon[_ngcontent-%COMP%] {
  font-size: var(--font-size-xl);
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: var(--primary-100);
  border-radius: var(--border-radius-full);
}
.activity-content[_ngcontent-%COMP%] {
  flex: 1;
}
.activity-title[_ngcontent-%COMP%] {
  font-weight: var(--font-weight-medium);
  color: var(--gray-900);
  font-size: var(--font-size-sm);
  margin-bottom: var(--spacing-1);
}
.activity-time[_ngcontent-%COMP%] {
  color: var(--gray-500);
  font-size: var(--font-size-xs);
}
.actions-grid[_ngcontent-%COMP%] {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: var(--spacing-4);
}
.action-button[_ngcontent-%COMP%] {
  display: flex;
  align-items: center;
  gap: var(--spacing-3);
  padding: var(--spacing-4);
  background: var(--white);
  border: 2px solid var(--gray-200);
  border-radius: var(--border-radius-xl);
  cursor: pointer;
  transition: all var(--transition-base);
  text-decoration: none;
  color: inherit;
}
.action-button[_ngcontent-%COMP%]:hover {
  border-color: var(--primary-500);
  background: var(--primary-50);
  transform: translateY(-2px);
  box-shadow: var(--shadow-md);
}
.action-icon[_ngcontent-%COMP%] {
  font-size: var(--font-size-2xl);
  width: 48px;
  height: 48px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: var(--primary-100);
  border-radius: var(--border-radius-xl);
  transition: var(--transition-base);
}
.action-button[_ngcontent-%COMP%]:hover   .action-icon[_ngcontent-%COMP%] {
  background: var(--primary-200);
  transform: scale(1.1);
}
.action-content[_ngcontent-%COMP%] {
  flex: 1;
}
.action-title[_ngcontent-%COMP%] {
  font-weight: var(--font-weight-semibold);
  color: var(--gray-900);
  font-size: var(--font-size-base);
  margin-bottom: var(--spacing-1);
}
.action-subtitle[_ngcontent-%COMP%] {
  color: var(--gray-600);
  font-size: var(--font-size-sm);
}
@media (max-width: 768px) {
  .dashboard-container[_ngcontent-%COMP%] {
    padding: var(--spacing-4);
  }
  .header-content[_ngcontent-%COMP%] {
    flex-direction: column;
    gap: var(--spacing-4);
    text-align: center;
  }
  .dashboard-title[_ngcontent-%COMP%] {
    font-size: var(--font-size-2xl);
  }
  .stats-grid[_ngcontent-%COMP%] {
    grid-template-columns: 1fr;
  }
  .dashboard-grid[_ngcontent-%COMP%] {
    grid-template-columns: 1fr;
  }
  .actions-grid[_ngcontent-%COMP%] {
    grid-template-columns: 1fr;
  }
  .chart-bars[_ngcontent-%COMP%] {
    gap: var(--spacing-2);
  }
  .chart-bar[_ngcontent-%COMP%] {
    width: 30px;
  }
  .chart-labels[_ngcontent-%COMP%]   span[_ngcontent-%COMP%] {
    width: 30px;
  }
}
/*# sourceMappingURL=dashboard.component.css.map */`] });
};
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && setClassMetadata(DashboardComponent, [{
    type: Component,
    args: [{ selector: "app-dashboard", standalone: true, imports: [CommonModule, RouterModule], template: `<div class="dashboard-container">\r
  <!-- Header moderne avec gradient -->\r
  <div class="dashboard-header">\r
    <div class="header-content">\r
      <div class="header-text">\r
        <h1 class="dashboard-title">\r
          <span class="title-icon">\u{1F44B}</span>\r
          <span *ngIf="fournisseurInfo">\r
            Bonjour {{ fournisseurInfo.prenom }} {{ fournisseurInfo.nom }}\r
          </span>\r
          <span *ngIf="!fournisseurInfo && currentUser">\r
            Bonjour {{ currentUser.prenom }} {{ currentUser.nom }}\r
          </span>\r
          <span *ngIf="!fournisseurInfo && !currentUser">\r
            Bienvenue\r
          </span>\r
        </h1>\r
        <p class="dashboard-subtitle">\r
          <span *ngIf="fournisseurInfo">\r
            {{ fournisseurInfo.raisonSociale }} - Voici un aper\xE7u de votre activit\xE9 aujourd'hui\r
          </span>\r
          <span *ngIf="!fournisseurInfo">\r
            Voici un aper\xE7u de votre activit\xE9 aujourd'hui\r
          </span>\r
        </p>\r
      </div>\r
      <div class="header-actions">\r
        <button class="btn btn-primary btn-lg" routerLink="/dashboard/products">\r
          <span>\u2795</span> Nouveau produit\r
        </button>\r
      </div>\r
    </div>\r
  </div>\r
\r
  <!-- Statistiques modernes -->\r
  <div class="stats-section">\r
    <div class="stats-grid">\r
      <div class="stat-card stat-primary">\r
        <div class="stat-header">\r
          <div class="stat-icon-wrapper">\r
            <span class="stat-icon">\u{1F4E6}</span>\r
          </div>\r
          <div class="stat-trend" [ngClass]="stats.totalProducts > 0 ? 'positive' : 'neutral'">\r
            <span class="trend-icon" *ngIf="stats.totalProducts > 0">\u{1F4C8}</span>\r
            <span class="trend-icon" *ngIf="stats.totalProducts === 0">\u2796</span>\r
          </div>\r
        </div>\r
        <div class="stat-content">\r
          <div class="stat-number" *ngIf="!isStatsLoading">{{ stats.totalProducts }}</div>\r
          <div class="stat-number loading" *ngIf="isStatsLoading">\r
            <div class="spinner"></div>\r
          </div>\r
          <div class="stat-label">Produits actifs</div>\r
          <div class="stat-description">Dans votre catalogue</div>\r
        </div>\r
      </div>\r
\r
      <div class="stat-card stat-success">\r
        <div class="stat-header">\r
          <div class="stat-icon-wrapper">\r
            <span class="stat-icon">\u{1F4CB}</span>\r
          </div>\r
          <div class="stat-trend" [ngClass]="stats.activeOrders > 0 ? 'positive' : 'neutral'">\r
            <span class="trend-icon" *ngIf="stats.activeOrders > 0">\u{1F4C8}</span>\r
            <span class="trend-icon" *ngIf="stats.activeOrders === 0">\u2796</span>\r
          </div>\r
        </div>\r
        <div class="stat-content">\r
          <div class="stat-number" *ngIf="!isStatsLoading">{{ stats.activeOrders }}</div>\r
          <div class="stat-number loading" *ngIf="isStatsLoading">\r
            <div class="spinner"></div>\r
          </div>\r
          <div class="stat-label">Commandes ce mois</div>\r
          <div class="stat-description">En cours de traitement</div>\r
        </div>\r
      </div>\r
\r
      <div class="stat-card stat-warning">\r
        <div class="stat-header">\r
          <div class="stat-icon-wrapper">\r
            <span class="stat-icon">\u{1F4B0}</span>\r
          </div>\r
          <div class="stat-trend" [ngClass]="stats.monthlyRevenue > 0 ? 'positive' : 'neutral'">\r
            <span class="trend-icon" *ngIf="stats.monthlyRevenue > 0">\u{1F4B0}</span>\r
            <span class="trend-icon" *ngIf="stats.monthlyRevenue === 0">\u2796</span>\r
          </div>\r
        </div>\r
        <div class="stat-content">\r
          <div class="stat-number" *ngIf="!isStatsLoading">{{ formatCurrency(stats.monthlyRevenue) }}</div>\r
          <div class="stat-number loading" *ngIf="isStatsLoading">\r
            <div class="spinner"></div>\r
          </div>\r
          <div class="stat-label">Chiffre d'affaires</div>\r
          <div class="stat-description">Ce mois-ci</div>\r
        </div>\r
      </div>\r
\r
      <div class="stat-card stat-info">\r
        <div class="stat-header">\r
          <div class="stat-icon-wrapper">\r
            <span class="stat-icon">\u{1F69A}</span>\r
          </div>\r
          <div class="stat-trend" [ngClass]="stats.pendingDeliveries > 0 ? 'positive' : 'neutral'">\r
            <span class="trend-icon" *ngIf="stats.pendingDeliveries > 0">\u{1F69A}</span>\r
            <span class="trend-icon" *ngIf="stats.pendingDeliveries === 0">\u2796</span>\r
          </div>\r
        </div>\r
        <div class="stat-content">\r
          <div class="stat-number" *ngIf="!isStatsLoading">{{ stats.pendingDeliveries }}</div>\r
          <div class="stat-number loading" *ngIf="isStatsLoading">\r
            <div class="spinner"></div>\r
          </div>\r
          <div class="stat-label">Livraisons</div>\r
          <div class="stat-description">En cours</div>\r
        </div>\r
      </div>\r
    </div>\r
  </div>\r
\r
  <!-- Contenu principal avec grille moderne -->\r
  <div class="dashboard-main">\r
    <div class="dashboard-grid">\r
\r
      <!-- Graphique des ventes -->\r
      <div class="dashboard-card chart-card">\r
        <div class="card-header">\r
          <h3 class="card-title">\r
            <span class="card-icon">\u{1F4C8}</span>\r
            \xC9volution des ventes\r
          </h3>\r
          <div class="card-actions">\r
            <select class="period-selector">\r
              <option>7 derniers jours</option>\r
              <option>30 derniers jours</option>\r
              <option>3 derniers mois</option>\r
            </select>\r
          </div>\r
        </div>\r
        <div class="card-body">\r
          <div class="chart-container">\r
            <!-- Aucune donn\xE9e de ventes -->\r
            <div *ngIf="salesData.length === 0" class="empty-state">\r
              <div class="empty-icon">\u{1F4CA}</div>\r
              <h4>Aucune donn\xE9e de ventes</h4>\r
              <p>Les donn\xE9es appara\xEEtront apr\xE8s vos premi\xE8res commandes</p>\r
            </div>\r
\r
            <!-- Graphique avec donn\xE9es -->\r
            <div *ngIf="salesData.length > 0" class="chart-placeholder">\r
              <div class="chart-bars">\r
                <div *ngFor="let data of salesData" class="chart-bar" [style.height.%]="data.sales > 0 ? (data.sales / getMaxSales() * 100) : 5">\r
                  <div class="bar-tooltip">{{ formatCurrency(data.sales) }}</div>\r
                </div>\r
              </div>\r
              <div class="chart-labels">\r
                <span *ngFor="let data of salesData">{{ data.month.substring(0, 3) }}</span>\r
              </div>\r
            </div>\r
          </div>\r
        </div>\r
      </div>\r
\r
      <!-- Commandes r\xE9centes -->\r
      <div class="dashboard-card orders-card">\r
        <div class="card-header">\r
          <h3 class="card-title">\r
            <span class="card-icon">\u{1F4CB}</span>\r
            Commandes r\xE9centes\r
          </h3>\r
          <a routerLink="/dashboard/orders" class="card-link">Voir tout</a>\r
        </div>\r
        <div class="card-body">\r
          <!-- \xC9tat de chargement -->\r
          <div *ngIf="isOrdersLoading" class="loading-state">\r
            <div class="spinner"></div>\r
            <p>Chargement des commandes...</p>\r
          </div>\r
\r
          <!-- Aucune commande -->\r
          <div *ngIf="!isOrdersLoading && recentOrders.length === 0" class="empty-state">\r
            <div class="empty-icon">\u{1F4ED}</div>\r
            <h4>Aucune commande r\xE9cente</h4>\r
            <p>Les nouvelles commandes appara\xEEtront ici</p>\r
          </div>\r
\r
          <!-- Liste des commandes -->\r
          <div *ngIf="!isOrdersLoading && recentOrders.length > 0" class="orders-list">\r
            <div *ngFor="let order of recentOrders" class="order-item">\r
              <div class="order-info">\r
                <div class="order-id">{{ order.reference }}</div>\r
                <div class="order-customer">{{ order.client }}</div>\r
                <div class="order-date">{{ order.date | date:'dd/MM/yyyy' }}</div>\r
              </div>\r
              <div class="order-details">\r
                <div class="order-amount">{{ formatCurrency(order.amount) }}</div>\r
                <span class="badge" [ngClass]="'badge-' + getStatusClass(order.status)">\r
                  {{ order.status }}\r
                </span>\r
              </div>\r
            </div>\r
          </div>\r
        </div>\r
      </div>\r
\r
      <!-- Activit\xE9 r\xE9cente -->\r
      <div class="dashboard-card activity-card">\r
        <div class="card-header">\r
          <h3 class="card-title">\r
            <span class="card-icon">\u{1F514}</span>\r
            Activit\xE9 r\xE9cente\r
          </h3>\r
        </div>\r
        <div class="card-body">\r
          <!-- \xC9tat de chargement -->\r
          <div *ngIf="isActivityLoading" class="loading-state">\r
            <div class="spinner"></div>\r
            <p>Chargement de l'activit\xE9...</p>\r
          </div>\r
\r
          <!-- Aucune activit\xE9 -->\r
          <div *ngIf="!isActivityLoading && recentActivity.length === 0" class="empty-state">\r
            <div class="empty-icon">\u{1F4ED}</div>\r
            <h4>Aucune activit\xE9 r\xE9cente</h4>\r
            <p>Vos derni\xE8res actions appara\xEEtront ici</p>\r
          </div>\r
\r
          <!-- Liste d'activit\xE9 -->\r
          <div *ngIf="!isActivityLoading && recentActivity.length > 0" class="activity-list">\r
            <div *ngFor="let activity of recentActivity" class="activity-item">\r
              <div class="activity-icon">{{ activity.icon }}</div>\r
              <div class="activity-content">\r
                <div class="activity-title">{{ activity.title }}</div>\r
                <div class="activity-time">{{ activity.time }}</div>\r
              </div>\r
            </div>\r
          </div>\r
        </div>\r
      </div>\r
\r
      <!-- Actions rapides -->\r
      <div class="dashboard-card actions-card">\r
        <div class="card-header">\r
          <h3 class="card-title">\r
            <span class="card-icon">\u26A1</span>\r
            Actions rapides\r
          </h3>\r
        </div>\r
        <div class="card-body">\r
          <div class="actions-grid">\r
            <button class="action-button" routerLink="/dashboard/products">\r
              <div class="action-icon">\u{1F4E6}</div>\r
              <div class="action-content">\r
                <div class="action-title">Produits</div>\r
                <div class="action-subtitle">G\xE9rer le catalogue</div>\r
              </div>\r
            </button>\r
\r
            <button class="action-button" routerLink="/dashboard/referentiels">\r
              <div class="action-icon">\u{1F5C2}\uFE0F</div>\r
              <div class="action-content">\r
                <div class="action-title">R\xE9f\xE9rentiels</div>\r
                <div class="action-subtitle">Cat\xE9gories & marques</div>\r
              </div>\r
            </button>\r
\r
            <button class="action-button" routerLink="/dashboard/orders">\r
              <div class="action-icon">\u{1F4CB}</div>\r
              <div class="action-content">\r
                <div class="action-title">Commandes</div>\r
                <div class="action-subtitle">Suivi & gestion</div>\r
              </div>\r
            </button>\r
\r
            <button class="action-button" routerLink="/dashboard/profile">\r
              <div class="action-icon">\u{1F464}</div>\r
              <div class="action-content">\r
                <div class="action-title">Profil</div>\r
                <div class="action-subtitle">Param\xE8tres compte</div>\r
              </div>\r
            </button>\r
          </div>\r
        </div>\r
      </div>\r
    </div>\r
  </div>\r
</div>`, styles: [`/* src/app/components/dashboard/dashboard.component.css */
.dashboard-container {
  padding: var(--spacing-8);
  max-width: 1400px;
  margin: 0 auto;
  background: transparent;
  min-height: 100vh;
}
.dashboard-header {
  background: var(--gradient-primary);
  border-radius: var(--border-radius-2xl);
  padding: var(--spacing-8);
  margin-bottom: var(--spacing-8);
  box-shadow: var(--shadow-blue-lg);
  position: relative;
  overflow: hidden;
}
.dashboard-header::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="50" cy="50" r="1" fill="rgba(255,255,255,0.1)"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
  opacity: 0.3;
}
.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  position: relative;
  z-index: 1;
}
.header-text {
  color: var(--white);
}
.dashboard-title {
  font-size: var(--font-size-4xl);
  font-weight: var(--font-weight-extrabold);
  margin: 0 0 var(--spacing-2) 0;
  display: flex;
  align-items: center;
  gap: var(--spacing-3);
}
.title-icon {
  font-size: var(--font-size-3xl);
  animation: wave 2s ease-in-out infinite;
}
@keyframes wave {
  0%, 100% {
    transform: rotate(0deg);
  }
  25% {
    transform: rotate(20deg);
  }
  75% {
    transform: rotate(-10deg);
  }
}
.dashboard-subtitle {
  font-size: var(--font-size-lg);
  opacity: 0.9;
  margin: 0;
  font-weight: var(--font-weight-normal);
}
.header-actions {
  display: flex;
  gap: var(--spacing-4);
}
.stats-section {
  margin-bottom: var(--spacing-8);
}
.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: var(--spacing-6);
}
.stat-card {
  background: var(--white);
  border-radius: var(--border-radius-xl);
  padding: var(--spacing-6);
  box-shadow: var(--shadow-base);
  transition: all var(--transition-base);
  position: relative;
  overflow: hidden;
}
.stat-card::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  transition: all var(--transition-base);
}
.stat-card.stat-primary::before {
  background: var(--gradient-primary);
}
.stat-card.stat-success::before {
  background:
    linear-gradient(
      90deg,
      var(--success-500),
      var(--success-400));
}
.stat-card.stat-warning::before {
  background:
    linear-gradient(
      90deg,
      var(--warning-500),
      var(--warning-400));
}
.stat-card.stat-info::before {
  background: var(--gradient-primary);
}
.stat-card:hover {
  transform: translateY(-4px);
  box-shadow: var(--shadow-lg);
}
.stat-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--spacing-4);
}
.stat-icon-wrapper {
  width: 48px;
  height: 48px;
  border-radius: var(--border-radius-xl);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: var(--font-size-xl);
}
.stat-primary .stat-icon-wrapper {
  background: var(--primary-100);
}
.stat-success .stat-icon-wrapper {
  background: var(--success-100);
}
.stat-warning .stat-icon-wrapper {
  background: var(--warning-100);
}
.stat-info .stat-icon-wrapper {
  background: var(--primary-100);
}
.stat-trend {
  display: flex;
  align-items: center;
  gap: var(--spacing-1);
  font-size: var(--font-size-xs);
  font-weight: var(--font-weight-medium);
  padding: var(--spacing-1) var(--spacing-2);
  border-radius: var(--border-radius-full);
}
.stat-trend.positive {
  background: var(--success-100);
  color: var(--success-700);
}
.stat-trend.neutral {
  background: var(--gray-100);
  color: var(--gray-600);
}
.stat-number {
  font-size: var(--font-size-3xl);
  font-weight: var(--font-weight-extrabold);
  color: var(--gray-900);
  margin-bottom: var(--spacing-1);
  line-height: 1;
}
.stat-number.loading {
  display: flex;
  align-items: center;
  gap: var(--spacing-2);
}
.stat-label {
  font-size: var(--font-size-base);
  font-weight: var(--font-weight-semibold);
  color: var(--gray-700);
  margin-bottom: var(--spacing-1);
}
.stat-description {
  font-size: var(--font-size-sm);
  color: var(--gray-500);
}
.dashboard-main {
  margin-bottom: var(--spacing-8);
}
.dashboard-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
  gap: var(--spacing-6);
}
.dashboard-card {
  background: var(--white);
  border-radius: var(--border-radius-xl);
  box-shadow: var(--shadow-base);
  overflow: hidden;
  transition: all var(--transition-base);
  border: 1px solid var(--gray-200);
}
.dashboard-card:hover {
  box-shadow: var(--shadow-lg);
  transform: translateY(-4px);
  border-color: var(--primary-300);
}
.chart-card {
  background:
    linear-gradient(
      135deg,
      var(--white) 0%,
      #f8faff 100%);
  border: 1px solid #e1e7ff;
}
.orders-card {
  background:
    linear-gradient(
      135deg,
      var(--white) 0%,
      #f0f4ff 100%);
  border: 1px solid #d1d9ff;
}
.activity-card {
  background:
    linear-gradient(
      135deg,
      var(--white) 0%,
      #f0fff4 100%);
  border: 1px solid #d1fae5;
}
.actions-card {
  background:
    linear-gradient(
      135deg,
      var(--white) 0%,
      #fffbf0 100%);
  border: 1px solid #fed7aa;
}
.card-header {
  padding: var(--spacing-6);
  border-bottom: 1px solid var(--gray-200);
  background:
    linear-gradient(
      135deg,
      var(--gray-50) 0%,
      var(--white) 100%);
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.card-title {
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-bold);
  color: var(--gray-900);
  margin: 0;
  display: flex;
  align-items: center;
  gap: var(--spacing-2);
}
.card-icon {
  font-size: var(--font-size-xl);
}
.card-link {
  color: var(--primary-600);
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
  text-decoration: none;
  transition: var(--transition-fast);
}
.card-link:hover {
  color: var(--primary-700);
  text-decoration: underline;
}
.card-body {
  padding: var(--spacing-6);
  background: var(--white);
  border-radius: 0 0 var(--border-radius-xl) var(--border-radius-xl);
}
.period-selector {
  padding: var(--spacing-2) var(--spacing-3);
  border: 1px solid var(--gray-300);
  border-radius: var(--border-radius-lg);
  font-size: var(--font-size-sm);
  background: var(--white);
  color: var(--gray-700);
}
.loading-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: var(--spacing-8);
  color: var(--gray-500);
  gap: var(--spacing-3);
}
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: var(--spacing-8);
  color: var(--gray-500);
  text-align: center;
}
.empty-icon {
  font-size: var(--font-size-4xl);
  margin-bottom: var(--spacing-3);
}
.empty-state h4 {
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-semibold);
  color: var(--gray-700);
  margin: 0 0 var(--spacing-2) 0;
}
.empty-state p {
  font-size: var(--font-size-sm);
  color: var(--gray-500);
  margin: 0;
}
.chart-container {
  height: 300px;
  display: flex;
  align-items: center;
  justify-content: center;
  background:
    linear-gradient(
      135deg,
      #f8faff 0%,
      var(--white) 100%);
  border-radius: var(--border-radius-lg);
  margin: var(--spacing-2);
}
.chart-placeholder {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: flex-end;
  align-items: center;
  padding: var(--spacing-4);
}
.chart-bars {
  display: flex;
  align-items: flex-end;
  gap: var(--spacing-3);
  height: 200px;
  margin-bottom: var(--spacing-4);
}
.chart-bar {
  width: 40px;
  background: var(--gradient-primary);
  border-radius: var(--border-radius-md) var(--border-radius-md) 0 0;
  transition: all var(--transition-base);
  position: relative;
  cursor: pointer;
  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
}
.chart-bar:hover {
  background:
    linear-gradient(
      180deg,
      #5a67d8 0%,
      #667eea 100%);
  transform: scale(1.05);
  box-shadow: 0 6px 16px rgba(102, 126, 234, 0.4);
}
.bar-tooltip {
  position: absolute;
  top: -30px;
  left: 50%;
  transform: translateX(-50%);
  background: var(--white);
  color: var(--gray-800);
  padding: var(--spacing-1) var(--spacing-2);
  border-radius: var(--border-radius-md);
  font-size: var(--font-size-xs);
  opacity: 0;
  transition: var(--transition-fast);
  box-shadow: var(--shadow-lg);
  border: 1px solid var(--gray-200);
  font-weight: var(--font-weight-semibold);
}
.chart-bar:hover .bar-tooltip {
  opacity: 1;
}
.chart-labels {
  display: flex;
  gap: var(--spacing-3);
}
.chart-labels span {
  width: 40px;
  text-align: center;
  font-size: var(--font-size-xs);
  color: var(--gray-600);
  font-weight: var(--font-weight-medium);
}
.orders-list {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-4);
}
.order-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--spacing-4);
  background: var(--gray-50);
  border-radius: var(--border-radius-lg);
  transition: all var(--transition-fast);
  border-left: 4px solid var(--primary-500);
}
.order-item:hover {
  background: var(--primary-50);
  transform: translateX(4px);
}
.order-info {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-1);
}
.order-id {
  font-weight: var(--font-weight-bold);
  color: var(--gray-900);
  font-size: var(--font-size-sm);
}
.order-customer {
  color: var(--gray-600);
  font-size: var(--font-size-sm);
}
.order-date {
  color: var(--gray-500);
  font-size: var(--font-size-xs);
}
.order-details {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  gap: var(--spacing-2);
}
.order-amount {
  font-weight: var(--font-weight-bold);
  font-size: var(--font-size-lg);
  color: var(--primary-600);
}
.activity-list {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-4);
}
.activity-item {
  display: flex;
  align-items: center;
  gap: var(--spacing-3);
  padding: var(--spacing-3);
  border-radius: var(--border-radius-lg);
  transition: var(--transition-fast);
}
.activity-item:hover {
  background: var(--gray-50);
}
.activity-icon {
  font-size: var(--font-size-xl);
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: var(--primary-100);
  border-radius: var(--border-radius-full);
}
.activity-content {
  flex: 1;
}
.activity-title {
  font-weight: var(--font-weight-medium);
  color: var(--gray-900);
  font-size: var(--font-size-sm);
  margin-bottom: var(--spacing-1);
}
.activity-time {
  color: var(--gray-500);
  font-size: var(--font-size-xs);
}
.actions-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: var(--spacing-4);
}
.action-button {
  display: flex;
  align-items: center;
  gap: var(--spacing-3);
  padding: var(--spacing-4);
  background: var(--white);
  border: 2px solid var(--gray-200);
  border-radius: var(--border-radius-xl);
  cursor: pointer;
  transition: all var(--transition-base);
  text-decoration: none;
  color: inherit;
}
.action-button:hover {
  border-color: var(--primary-500);
  background: var(--primary-50);
  transform: translateY(-2px);
  box-shadow: var(--shadow-md);
}
.action-icon {
  font-size: var(--font-size-2xl);
  width: 48px;
  height: 48px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: var(--primary-100);
  border-radius: var(--border-radius-xl);
  transition: var(--transition-base);
}
.action-button:hover .action-icon {
  background: var(--primary-200);
  transform: scale(1.1);
}
.action-content {
  flex: 1;
}
.action-title {
  font-weight: var(--font-weight-semibold);
  color: var(--gray-900);
  font-size: var(--font-size-base);
  margin-bottom: var(--spacing-1);
}
.action-subtitle {
  color: var(--gray-600);
  font-size: var(--font-size-sm);
}
@media (max-width: 768px) {
  .dashboard-container {
    padding: var(--spacing-4);
  }
  .header-content {
    flex-direction: column;
    gap: var(--spacing-4);
    text-align: center;
  }
  .dashboard-title {
    font-size: var(--font-size-2xl);
  }
  .stats-grid {
    grid-template-columns: 1fr;
  }
  .dashboard-grid {
    grid-template-columns: 1fr;
  }
  .actions-grid {
    grid-template-columns: 1fr;
  }
  .chart-bars {
    gap: var(--spacing-2);
  }
  .chart-bar {
    width: 30px;
  }
  .chart-labels span {
    width: 30px;
  }
}
/*# sourceMappingURL=dashboard.component.css.map */
`] }]
  }], () => [{ type: AuthService }, { type: DashboardService }], null);
})();
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && \u0275setClassDebugInfo(DashboardComponent, { className: "DashboardComponent", filePath: "src/app/components/dashboard/dashboard.component.ts", lineNumber: 15 });
})();
export {
  DashboardComponent
};
//# sourceMappingURL=chunk-2COJLMM2.js.map

import {
  environment
} from "./chunk-GFHHPDQ6.js";
import {
  HttpClient
} from "./chunk-7JDDWGD3.js";
import {
  BehaviorSubject,
  Injectable,
  catchError,
  delay,
  map,
  of,
  setClassMetadata,
  tap,
  throwError,
  ɵɵdefineInjectable,
  ɵɵinject
} from "./chunk-UBZQS7JS.js";

// src/app/models/admin.model.ts
var AdminRole;
(function(AdminRole2) {
  AdminRole2["ADMIN"] = "ADMIN";
  AdminRole2["SUPER_ADMIN"] = "SUPER_ADMIN";
  AdminRole2["MODERATOR"] = "MODERATOR";
  AdminRole2["ANALYST"] = "ANALYST";
})(AdminRole || (AdminRole = {}));
var PermissionAction;
(function(PermissionAction2) {
  PermissionAction2["CREATE"] = "CREATE";
  PermissionAction2["READ"] = "READ";
  PermissionAction2["UPDATE"] = "UPDATE";
  PermissionAction2["DELETE"] = "DELETE";
  PermissionAction2["APPROVE"] = "APPROVE";
  PermissionAction2["MODERATE"] = "MODERATE";
  PermissionAction2["EXPORT"] = "EXPORT";
  PermissionAction2["AUDIT"] = "AUDIT";
})(PermissionAction || (PermissionAction = {}));
var AuditSeverity;
(function(AuditSeverity2) {
  AuditSeverity2["LOW"] = "LOW";
  AuditSeverity2["MEDIUM"] = "MEDIUM";
  AuditSeverity2["HIGH"] = "HIGH";
  AuditSeverity2["CRITICAL"] = "CRITICAL";
})(AuditSeverity || (AuditSeverity = {}));
var AlertType;
(function(AlertType2) {
  AlertType2["STOCK_CRITICAL"] = "STOCK_CRITICAL";
  AlertType2["PAYMENT_OVERDUE"] = "PAYMENT_OVERDUE";
  AlertType2["SECURITY_BREACH"] = "SECURITY_BREACH";
  AlertType2["SYSTEM_ERROR"] = "SYSTEM_ERROR";
  AlertType2["USER_ACTIVITY"] = "USER_ACTIVITY";
  AlertType2["PERFORMANCE"] = "PERFORMANCE";
})(AlertType || (AlertType = {}));
var AlertSeverity;
(function(AlertSeverity2) {
  AlertSeverity2["INFO"] = "INFO";
  AlertSeverity2["WARNING"] = "WARNING";
  AlertSeverity2["ERROR"] = "ERROR";
  AlertSeverity2["CRITICAL"] = "CRITICAL";
})(AlertSeverity || (AlertSeverity = {}));
var PromotionType;
(function(PromotionType2) {
  PromotionType2["PERCENTAGE"] = "PERCENTAGE";
  PromotionType2["FIXED_AMOUNT"] = "FIXED_AMOUNT";
  PromotionType2["FREE_SHIPPING"] = "FREE_SHIPPING";
  PromotionType2["BUY_X_GET_Y"] = "BUY_X_GET_Y";
})(PromotionType || (PromotionType = {}));
var DiscountType;
(function(DiscountType2) {
  DiscountType2["PERCENTAGE"] = "PERCENTAGE";
  DiscountType2["FIXED"] = "FIXED";
})(DiscountType || (DiscountType = {}));
var BatchOperationType;
(function(BatchOperationType2) {
  BatchOperationType2["PRICE_UPDATE"] = "PRICE_UPDATE";
  BatchOperationType2["STOCK_UPDATE"] = "STOCK_UPDATE";
  BatchOperationType2["CATEGORY_ASSIGNMENT"] = "CATEGORY_ASSIGNMENT";
  BatchOperationType2["USER_IMPORT"] = "USER_IMPORT";
  BatchOperationType2["PRODUCT_EXPORT"] = "PRODUCT_EXPORT";
})(BatchOperationType || (BatchOperationType = {}));
var BatchStatus;
(function(BatchStatus2) {
  BatchStatus2["PENDING"] = "PENDING";
  BatchStatus2["RUNNING"] = "RUNNING";
  BatchStatus2["COMPLETED"] = "COMPLETED";
  BatchStatus2["FAILED"] = "FAILED";
  BatchStatus2["CANCELLED"] = "CANCELLED";
})(BatchStatus || (BatchStatus = {}));
var WorkflowType;
(function(WorkflowType2) {
  WorkflowType2["PRODUCT_APPROVAL"] = "PRODUCT_APPROVAL";
  WorkflowType2["SUPPLIER_ONBOARDING"] = "SUPPLIER_ONBOARDING";
  WorkflowType2["PRICE_CHANGE"] = "PRICE_CHANGE";
  WorkflowType2["CATEGORY_CREATION"] = "CATEGORY_CREATION";
})(WorkflowType || (WorkflowType = {}));
var WorkflowStatus;
(function(WorkflowStatus2) {
  WorkflowStatus2["PENDING"] = "PENDING";
  WorkflowStatus2["IN_PROGRESS"] = "IN_PROGRESS";
  WorkflowStatus2["APPROVED"] = "APPROVED";
  WorkflowStatus2["REJECTED"] = "REJECTED";
  WorkflowStatus2["CANCELLED"] = "CANCELLED";
})(WorkflowStatus || (WorkflowStatus = {}));

// src/app/config/admin.config.ts
var ADMIN_CONFIG = {
  username: "adminOptiLet",
  password: "adminOptiLet!2025",
  role: "ADMIN",
  permissions: [
    "MANAGE_USERS",
    "MANAGE_PRODUCTS",
    "MANAGE_ORDERS",
    "MANAGE_CATEGORIES",
    "MANAGE_PROMOTIONS",
    "VIEW_STATISTICS",
    "MANAGE_SETTINGS",
    "MODERATE_CONTENT",
    "MANAGE_REFUNDS",
    "SYSTEM_ADMIN"
  ]
};
var ADMIN_USER_INFO = {
  id: 1,
  username: ADMIN_CONFIG.username,
  email: "<EMAIL>",
  nom: "Administrateur",
  prenom: "OptiLet",
  role: ADMIN_CONFIG.role,
  permissions: ADMIN_CONFIG.permissions,
  isActive: true,
  lastLogin: /* @__PURE__ */ new Date(),
  createdAt: /* @__PURE__ */ new Date("2024-01-01"),
  updatedAt: /* @__PURE__ */ new Date(),
  profileImage: "https://via.placeholder.com/150x150?text=ADMIN",
  phoneNumber: "+33123456789",
  department: "Administration Syst\xE8me",
  twoFactorEnabled: false,
  sessionTimeout: 3600
};

// src/app/services/admin-auth.service.ts
var AdminAuthService = class _AdminAuthService {
  http;
  currentUserSubject = new BehaviorSubject(null);
  isAuthenticatedSubject = new BehaviorSubject(false);
  sessionTokenSubject = new BehaviorSubject(null);
  apiUrl = environment.apiUrl;
  currentUser$ = this.currentUserSubject.asObservable();
  isAuthenticated$ = this.isAuthenticatedSubject.asObservable();
  constructor(http) {
    this.http = http;
    this.checkStoredAuth();
  }
  /**
   * Forcer la déconnexion (pour debug)
   */
  forceLogout() {
    console.log("\u{1F512} D\xE9connexion forc\xE9e admin");
    this.clearAuthData();
    this.currentUserSubject.next(null);
    this.isAuthenticatedSubject.next(false);
    this.sessionTokenSubject.next(null);
  }
  /**
   * Connexion avec email et mot de passe
   */
  login(credentials) {
    console.log("\u{1F510} Tentative de connexion admin:", credentials.email);
    const loginData = {
      email: credentials.email,
      motDePasse: credentials.password
    };
    return this.http.post(`${this.apiUrl}/Auth/login`, loginData).pipe(map((response) => {
      console.log("\u2705 R\xE9ponse API login:", response);
      if (response.user && response.user.role === "Admin") {
        const adminUser = {
          id: response.user.id,
          email: response.user.email,
          nom: response.user.nom || "Admin",
          prenom: response.user.prenom || "OptiLet",
          role: AdminRole.SUPER_ADMIN,
          permissions: this.getAllPermissions(),
          isActive: true,
          lastLogin: /* @__PURE__ */ new Date(),
          createdAt: /* @__PURE__ */ new Date(),
          updatedAt: /* @__PURE__ */ new Date(),
          twoFactorEnabled: false,
          sessionTimeout: 8 * 60 * 60 * 1e3
          // 8 heures
        };
        const realToken = response.token;
        this.storeAuthData(adminUser, realToken, realToken, realToken);
        this.currentUserSubject.next(adminUser);
        this.isAuthenticatedSubject.next(true);
        return {
          success: true,
          requiresOTP: false,
          accessToken: realToken,
          refreshToken: realToken,
          user: adminUser,
          expiresIn: 8 * 60 * 60 * 1e3,
          message: "Connexion admin r\xE9ussie"
        };
      } else {
        throw new Error("Acc\xE8s refus\xE9 : droits administrateur requis");
      }
    }), catchError((error) => {
      console.error("\u274C Erreur connexion admin:", error);
      throw new Error(error.error?.Message || "Erreur de connexion");
    }));
  }
  /**
   * Vérification du code OTP
   */
  verifyOTP(otpRequest) {
    return of(null).pipe(delay(500), map(() => {
      if (otpRequest.otpCode !== "123456") {
        throw new Error("Code OTP invalide");
      }
      const user = this.findUserByEmail(otpRequest.email);
      if (!user) {
        throw new Error("Utilisateur non trouv\xE9");
      }
      return this.completeLogin(user, otpRequest.sessionToken);
    }));
  }
  /**
   * Finaliser la connexion
   */
  completeLogin(user, sessionToken) {
    const accessToken = this.generateAccessToken();
    const refreshToken = this.generateRefreshToken();
    user.lastLogin = /* @__PURE__ */ new Date();
    this.storeAuthData(user, accessToken, refreshToken, sessionToken);
    this.currentUserSubject.next(user);
    this.isAuthenticatedSubject.next(true);
    return {
      success: true,
      requiresOTP: false,
      accessToken,
      refreshToken,
      user,
      expiresIn: user.sessionTimeout,
      message: "Connexion r\xE9ussie"
    };
  }
  /**
   * Déconnexion
   */
  logout() {
    return of(true).pipe(delay(300), tap(() => {
      this.clearAuthData();
      this.currentUserSubject.next(null);
      this.isAuthenticatedSubject.next(false);
      this.sessionTokenSubject.next(null);
    }));
  }
  /**
   * Rafraîchir le token
   */
  refreshToken() {
    const refreshToken = localStorage.getItem("admin_refresh_token");
    if (!refreshToken) {
      return throwError(() => new Error("Aucun refresh token"));
    }
    return of(this.generateAccessToken()).pipe(delay(300), tap((newToken) => {
      localStorage.setItem("admin_access_token", newToken);
    }));
  }
  /**
   * Vérifier les permissions
   */
  hasPermission(resource, action) {
    const user = this.currentUserSubject.value;
    if (!user)
      return false;
    return user.permissions.some((p) => p.resource === resource && p.action === action);
  }
  /**
   * Vérifier le rôle
   */
  hasRole(role) {
    const user = this.currentUserSubject.value;
    return user?.role === role || false;
  }
  /**
   * Vérifier si super admin
   */
  isSuperAdmin() {
    return this.hasRole(AdminRole.SUPER_ADMIN);
  }
  /**
   * Obtenir l'utilisateur actuel
   */
  getCurrentUser() {
    return this.currentUserSubject.value;
  }
  /**
   * Vérifier l'authentification stockée
   */
  checkStoredAuth() {
    const token = localStorage.getItem("admin_access_token");
    const userStr = localStorage.getItem("admin_user");
    console.log("\u{1F50D} V\xE9rification auth stock\xE9e:", { hasToken: !!token, hasUser: !!userStr });
    if (token && userStr) {
      try {
        const user = JSON.parse(userStr);
        const tokenExpiry = localStorage.getItem("admin_token_expiry");
        const now = Date.now();
        if (tokenExpiry && now > parseInt(tokenExpiry)) {
          console.log("\u{1F512} Token admin expir\xE9, nettoyage...");
          this.clearAuthData();
          return;
        }
        console.log("\u2705 Utilisateur admin restaur\xE9:", user.email);
        this.currentUserSubject.next(user);
        this.isAuthenticatedSubject.next(true);
      } catch (error) {
        console.log("\u274C Erreur lors de la restauration auth admin:", error);
        this.clearAuthData();
      }
    } else {
      console.log("\u274C Pas d'authentification admin stock\xE9e");
      this.isAuthenticatedSubject.next(false);
    }
  }
  /**
   * Stocker les données d'authentification
   */
  storeAuthData(user, accessToken, refreshToken, sessionToken) {
    const expiryTime = Date.now() + 8 * 60 * 60 * 1e3;
    localStorage.setItem("admin_access_token", accessToken);
    localStorage.setItem("admin_refresh_token", refreshToken);
    localStorage.setItem("admin_session_token", sessionToken);
    localStorage.setItem("admin_user", JSON.stringify(user));
    localStorage.setItem("admin_token_expiry", expiryTime.toString());
  }
  /**
   * Effacer les données d'authentification
   */
  clearAuthData() {
    localStorage.removeItem("admin_access_token");
    localStorage.removeItem("admin_refresh_token");
    localStorage.removeItem("admin_session_token");
    localStorage.removeItem("admin_user");
    localStorage.removeItem("admin_token_expiry");
  }
  /**
   * Générer un token de session
   */
  generateSessionToken() {
    return "session_" + Math.random().toString(36).substr(2, 9) + Date.now().toString(36);
  }
  /**
   * Générer un access token
   */
  generateAccessToken() {
    return "access_" + Math.random().toString(36).substr(2, 9) + Date.now().toString(36);
  }
  /**
   * Générer un refresh token
   */
  generateRefreshToken() {
    return "refresh_" + Math.random().toString(36).substr(2, 9) + Date.now().toString(36);
  }
  /**
   * Obtenir toutes les permissions (Super Admin)
   */
  getAllPermissions() {
    return [
      { id: 1, name: "Cr\xE9er utilisateur", resource: "users", action: PermissionAction.CREATE, description: "Cr\xE9er de nouveaux utilisateurs" },
      { id: 2, name: "Lire utilisateur", resource: "users", action: PermissionAction.READ, description: "Consulter les utilisateurs" },
      { id: 3, name: "Modifier utilisateur", resource: "users", action: PermissionAction.UPDATE, description: "Modifier les utilisateurs" },
      { id: 4, name: "Supprimer utilisateur", resource: "users", action: PermissionAction.DELETE, description: "Supprimer les utilisateurs" },
      { id: 5, name: "Approuver produit", resource: "products", action: PermissionAction.APPROVE, description: "Approuver les produits" },
      { id: 6, name: "Lire produit", resource: "products", action: PermissionAction.READ, description: "Consulter les produits" },
      { id: 7, name: "Cr\xE9er produit", resource: "products", action: PermissionAction.CREATE, description: "Cr\xE9er des produits" },
      { id: 8, name: "Modifier produit", resource: "products", action: PermissionAction.UPDATE, description: "Modifier les produits" },
      { id: 9, name: "Supprimer produit", resource: "products", action: PermissionAction.DELETE, description: "Supprimer les produits" },
      { id: 10, name: "Mod\xE9rer avis", resource: "reviews", action: PermissionAction.MODERATE, description: "Mod\xE9rer les avis clients" },
      { id: 11, name: "Lire avis", resource: "reviews", action: PermissionAction.READ, description: "Consulter les avis clients" },
      { id: 12, name: "Cr\xE9er promotion", resource: "promotions", action: PermissionAction.CREATE, description: "Cr\xE9er des promotions" },
      { id: 13, name: "Approuver cat\xE9gorie", resource: "categories", action: PermissionAction.APPROVE, description: "Approuver les demandes de cat\xE9gories" },
      { id: 14, name: "Exporter donn\xE9es", resource: "data", action: PermissionAction.EXPORT, description: "Exporter les donn\xE9es" },
      { id: 15, name: "Consulter audit", resource: "audit", action: PermissionAction.AUDIT, description: "Consulter les logs d'audit" }
    ];
  }
  /**
   * Obtenir les permissions modérateur
   */
  getModeratorPermissions() {
    return [
      { id: 2, name: "Lire utilisateur", resource: "users", action: PermissionAction.READ, description: "Consulter les utilisateurs" },
      { id: 5, name: "Approuver produit", resource: "products", action: PermissionAction.APPROVE, description: "Approuver les produits" },
      { id: 6, name: "Lire produit", resource: "products", action: PermissionAction.READ, description: "Consulter les produits" },
      { id: 10, name: "Mod\xE9rer avis", resource: "reviews", action: PermissionAction.MODERATE, description: "Mod\xE9rer les avis clients" },
      { id: 11, name: "Lire avis", resource: "reviews", action: PermissionAction.READ, description: "Consulter les avis clients" },
      { id: 14, name: "Exporter donn\xE9es", resource: "data", action: PermissionAction.EXPORT, description: "Exporter les donn\xE9es" }
    ];
  }
  /**
   * Vérifier les credentials admin (username/password depuis la configuration)
   */
  validateAdminCredentials(email, password) {
    if ((email === ADMIN_CONFIG.username || email === ADMIN_USER_INFO.email) && password === ADMIN_CONFIG.password) {
      return {
        id: ADMIN_USER_INFO.id,
        email: ADMIN_USER_INFO.email,
        nom: ADMIN_USER_INFO.nom,
        prenom: ADMIN_USER_INFO.prenom,
        role: AdminRole.SUPER_ADMIN,
        permissions: this.getAllPermissions(),
        isActive: ADMIN_USER_INFO.isActive,
        lastLogin: /* @__PURE__ */ new Date(),
        createdAt: ADMIN_USER_INFO.createdAt,
        updatedAt: /* @__PURE__ */ new Date(),
        profileImage: ADMIN_USER_INFO.profileImage,
        phoneNumber: ADMIN_USER_INFO.phoneNumber,
        department: ADMIN_USER_INFO.department,
        twoFactorEnabled: ADMIN_USER_INFO.twoFactorEnabled,
        sessionTimeout: ADMIN_USER_INFO.sessionTimeout
      };
    }
    return void 0;
  }
  /**
   * Trouver un utilisateur par son email (pour compatibilité avec OTP)
   */
  findUserByEmail(email) {
    if (email === ADMIN_USER_INFO.email) {
      return {
        id: ADMIN_USER_INFO.id,
        email: ADMIN_USER_INFO.email,
        nom: ADMIN_USER_INFO.nom,
        prenom: ADMIN_USER_INFO.prenom,
        role: AdminRole.SUPER_ADMIN,
        permissions: this.getAllPermissions(),
        isActive: ADMIN_USER_INFO.isActive,
        lastLogin: /* @__PURE__ */ new Date(),
        createdAt: ADMIN_USER_INFO.createdAt,
        updatedAt: /* @__PURE__ */ new Date(),
        profileImage: ADMIN_USER_INFO.profileImage,
        phoneNumber: ADMIN_USER_INFO.phoneNumber,
        department: ADMIN_USER_INFO.department,
        twoFactorEnabled: ADMIN_USER_INFO.twoFactorEnabled,
        sessionTimeout: ADMIN_USER_INFO.sessionTimeout
      };
    }
    return void 0;
  }
  static \u0275fac = function AdminAuthService_Factory(__ngFactoryType__) {
    return new (__ngFactoryType__ || _AdminAuthService)(\u0275\u0275inject(HttpClient));
  };
  static \u0275prov = /* @__PURE__ */ \u0275\u0275defineInjectable({ token: _AdminAuthService, factory: _AdminAuthService.\u0275fac, providedIn: "root" });
};
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && setClassMetadata(AdminAuthService, [{
    type: Injectable,
    args: [{
      providedIn: "root"
    }]
  }], () => [{ type: HttpClient }], null);
})();

export {
  AdminRole,
  PermissionAction,
  AdminAuthService
};
//# sourceMappingURL=chunk-2RV3R4JN.js.map

{"version": 3, "sources": ["angular:styles/component:css;1a4e80d64094d2895a8bd5fbf72713863a39ed9a6ca08e95e5d2cfe6d16b11b8;C:/Users/<USER>/Desktop/STE/PFE/PFE/frontend-fournisseur/src/app/components/home/<USER>"], "sourcesContent": ["\n    .home-container {\n      min-height: 100vh;\n      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n      position: relative;\n      overflow-x: hidden;\n    }\n\n    .home-container::before {\n      content: '';\n      position: absolute;\n      top: 0;\n      left: 0;\n      right: 0;\n      bottom: 0;\n      background: url('data:image/svg+xml,<svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 100 100\"><defs><pattern id=\"grain\" width=\"100\" height=\"100\" patternUnits=\"userSpaceOnUse\"><circle cx=\"25\" cy=\"25\" r=\"1\" fill=\"rgba(255,255,255,0.1)\"/><circle cx=\"75\" cy=\"75\" r=\"1\" fill=\"rgba(255,255,255,0.1)\"/></pattern></defs><rect width=\"100\" height=\"100\" fill=\"url(%23grain)\"/></svg>');\n      pointer-events: none;\n    }\n\n    /* Hero Section */\n    .hero-section {\n      padding: 4rem 2rem;\n      min-height: 100vh;\n      display: flex;\n      align-items: center;\n      position: relative;\n    }\n\n    .hero-content {\n      max-width: 1200px;\n      margin: 0 auto;\n      display: grid;\n      grid-template-columns: 1fr 1fr;\n      gap: 4rem;\n      align-items: center;\n    }\n\n    .hero-title {\n      font-size: 3.5rem;\n      font-weight: 800;\n      line-height: 1.1;\n      margin: 0 0 1.5rem 0;\n      color: white;\n    }\n\n    .gradient-text {\n      background: linear-gradient(135deg, #ffffff 0%, #f0f9ff 100%);\n      -webkit-background-clip: text;\n      -webkit-text-fill-color: transparent;\n      background-clip: text;\n    }\n\n    .highlight {\n      background: linear-gradient(135deg, #fbbf24 0%, #f59e0b 100%);\n      -webkit-background-clip: text;\n      -webkit-text-fill-color: transparent;\n      background-clip: text;\n    }\n\n    .hero-description {\n      font-size: 1.25rem;\n      line-height: 1.6;\n      color: rgba(255, 255, 255, 0.9);\n      margin: 0 0 2rem 0;\n    }\n\n    .hero-actions {\n      display: flex;\n      gap: 1rem;\n      flex-wrap: wrap;\n    }\n\n    .btn {\n      padding: 1rem 2rem;\n      border-radius: 12px;\n      text-decoration: none;\n      font-weight: 600;\n      display: inline-flex;\n      align-items: center;\n      gap: 0.5rem;\n      transition: all 0.3s ease;\n      border: none;\n      cursor: pointer;\n    }\n\n    .btn-primary {\n      background: rgba(255, 255, 255, 0.2);\n      color: white;\n      backdrop-filter: blur(10px);\n      border: 1px solid rgba(255, 255, 255, 0.3);\n    }\n\n    .btn-primary:hover {\n      background: rgba(255, 255, 255, 0.3);\n      transform: translateY(-2px);\n    }\n\n    .btn-secondary {\n      background: transparent;\n      color: white;\n      border: 2px solid rgba(255, 255, 255, 0.3);\n    }\n\n    .btn-secondary:hover {\n      background: rgba(255, 255, 255, 0.1);\n      transform: translateY(-2px);\n    }\n\n    .btn-icon {\n      font-size: 1.25rem;\n    }\n\n    /* Floating Card */\n    .floating-card {\n      background: rgba(255, 255, 255, 0.1);\n      backdrop-filter: blur(20px);\n      border: 1px solid rgba(255, 255, 255, 0.2);\n      border-radius: 20px;\n      padding: 2rem;\n      animation: float 6s ease-in-out infinite;\n    }\n\n    @keyframes float {\n      0%, 100% { transform: translateY(0px); }\n      50% { transform: translateY(-20px); }\n    }\n\n    .card-header {\n      display: flex;\n      justify-content: flex-end;\n      margin-bottom: 1.5rem;\n    }\n\n    .card-dots {\n      display: flex;\n      gap: 0.5rem;\n    }\n\n    .card-dots span {\n      width: 12px;\n      height: 12px;\n      border-radius: 50%;\n      background: rgba(255, 255, 255, 0.3);\n    }\n\n    .card-content {\n      display: grid;\n      gap: 1.5rem;\n    }\n\n    .stat-item {\n      text-align: center;\n      color: white;\n    }\n\n    .stat-number {\n      display: block;\n      font-size: 2rem;\n      font-weight: bold;\n      margin-bottom: 0.5rem;\n    }\n\n    .stat-label {\n      font-size: 0.875rem;\n      opacity: 0.8;\n    }\n\n    /* Sections */\n    .features-section,\n    .tech-section {\n      padding: 4rem 2rem;\n      background: white;\n    }\n\n    .container {\n      max-width: 1200px;\n      margin: 0 auto;\n    }\n\n    .section-title {\n      text-align: center;\n      font-size: 2.5rem;\n      font-weight: 700;\n      margin: 0 0 3rem 0;\n      color: #1e293b;\n    }\n\n    .features-grid {\n      display: grid;\n      grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));\n      gap: 2rem;\n    }\n\n    .feature-card {\n      background: white;\n      border-radius: 16px;\n      padding: 2rem;\n      box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);\n      transition: all 0.3s ease;\n      border: 2px solid transparent;\n    }\n\n    .feature-card:hover {\n      transform: translateY(-4px);\n      box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1);\n      border-color: #3b82f6;\n    }\n\n    .feature-icon {\n      font-size: 3rem;\n      margin-bottom: 1rem;\n    }\n\n    .feature-card h3 {\n      margin: 0 0 1rem 0;\n      color: #1e293b;\n      font-size: 1.25rem;\n    }\n\n    .feature-card p {\n      margin: 0 0 1.5rem 0;\n      color: #64748b;\n      line-height: 1.6;\n    }\n\n    .feature-link {\n      color: #3b82f6;\n      text-decoration: none;\n      font-weight: 600;\n    }\n\n    .feature-link:hover {\n      text-decoration: underline;\n    }\n\n    .feature-link.disabled {\n      color: #94a3b8;\n      cursor: not-allowed;\n    }\n\n    .tech-grid {\n      display: grid;\n      grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));\n      gap: 1.5rem;\n    }\n\n    .tech-item {\n      display: flex;\n      flex-direction: column;\n      align-items: center;\n      gap: 0.5rem;\n      padding: 1.5rem;\n      background: #f8fafc;\n      border-radius: 12px;\n      transition: all 0.3s ease;\n    }\n\n    .tech-item:hover {\n      background: #e2e8f0;\n      transform: translateY(-2px);\n    }\n\n    .tech-icon {\n      font-size: 2rem;\n    }\n\n    /* Footer */\n    .footer {\n      background: #1e293b;\n      color: white;\n      padding: 3rem 2rem;\n    }\n\n    .footer-content {\n      display: flex;\n      justify-content: space-between;\n      align-items: center;\n      flex-wrap: wrap;\n      gap: 2rem;\n    }\n\n    .footer-text h3 {\n      margin: 0 0 0.5rem 0;\n      font-size: 1.25rem;\n    }\n\n    .footer-text p {\n      margin: 0;\n      opacity: 0.8;\n    }\n\n    .footer-links {\n      display: flex;\n      gap: 2rem;\n    }\n\n    .footer-link {\n      color: white;\n      text-decoration: none;\n      transition: opacity 0.3s ease;\n    }\n\n    .footer-link:hover {\n      opacity: 0.8;\n    }\n\n    /* Responsive */\n    @media (max-width: 768px) {\n      .hero-content {\n        grid-template-columns: 1fr;\n        text-align: center;\n      }\n      \n      .hero-title {\n        font-size: 2.5rem;\n      }\n      \n      .features-grid {\n        grid-template-columns: 1fr;\n      }\n      \n      .tech-grid {\n        grid-template-columns: repeat(2, 1fr);\n      }\n      \n      .footer-content {\n        flex-direction: column;\n        text-align: center;\n      }\n    }\n  "], "mappings": ";AACI,CAAC;AACC,cAAY;AACZ;AAAA,IAAY;AAAA,MAAgB,MAAhB;AAAA,MAAwB,QAAQ,EAAhC;AAAA,MAAoC,QAAQ;AACxD,YAAU;AACV,cAAY;AACd;AAEA,CAPC,cAOc;AACb,WAAS;AACT,YAAU;AACV,OAAK;AACL,QAAM;AACN,SAAO;AACP,UAAQ;AACR,cAAY;AACZ,kBAAgB;AAClB;AAGA,CAAC;AACC,WAAS,KAAK;AACd,cAAY;AACZ,WAAS;AACT,eAAa;AACb,YAAU;AACZ;AAEA,CAAC;AACC,aAAW;AACX,UAAQ,EAAE;AACV,WAAS;AACT,yBAAuB,IAAI;AAC3B,OAAK;AACL,eAAa;AACf;AAEA,CAAC;AACC,aAAW;AACX,eAAa;AACb,eAAa;AACb,UAAQ,EAAE,EAAE,OAAO;AACnB,SAAO;AACT;AAEA,CAAC;AACC;AAAA,IAAY;AAAA,MAAgB,MAAhB;AAAA,MAAwB,QAAQ,EAAhC;AAAA,MAAoC,QAAQ;AACxD,2BAAyB;AACzB,2BAAyB;AACzB,mBAAiB;AACnB;AAEA,CAAC;AACC;AAAA,IAAY;AAAA,MAAgB,MAAhB;AAAA,MAAwB,QAAQ,EAAhC;AAAA,MAAoC,QAAQ;AACxD,2BAAyB;AACzB,2BAAyB;AACzB,mBAAiB;AACnB;AAEA,CAAC;AACC,aAAW;AACX,eAAa;AACb,SAAO,KAAK,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE;AAC3B,UAAQ,EAAE,EAAE,KAAK;AACnB;AAEA,CAAC;AACC,WAAS;AACT,OAAK;AACL,aAAW;AACb;AAEA,CAAC;AACC,WAAS,KAAK;AACd,iBAAe;AACf,mBAAiB;AACjB,eAAa;AACb,WAAS;AACT,eAAa;AACb,OAAK;AACL,cAAY,IAAI,KAAK;AACrB,UAAQ;AACR,UAAQ;AACV;AAEA,CAAC;AACC,cAAY,KAAK,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE;AAChC,SAAO;AACP,2BAAiB,KAAK;AAAtB,mBAAiB,KAAK;AACtB,UAAQ,IAAI,MAAM,KAAK,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE;AACxC;AAEA,CAPC,WAOW;AACV,cAAY,KAAK,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE;AAChC,aAAW,WAAW;AACxB;AAEA,CAAC;AACC,cAAY;AACZ,SAAO;AACP,UAAQ,IAAI,MAAM,KAAK,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE;AACxC;AAEA,CANC,aAMa;AACZ,cAAY,KAAK,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE;AAChC,aAAW,WAAW;AACxB;AAEA,CAAC;AACC,aAAW;AACb;AAGA,CAAC;AACC,cAAY,KAAK,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE;AAChC,2BAAiB,KAAK;AAAtB,mBAAiB,KAAK;AACtB,UAAQ,IAAI,MAAM,KAAK,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE;AACtC,iBAAe;AACf,WAAS;AACT,aAAW,MAAM,GAAG,YAAY;AAClC;AAEA,WAHa;AAIX;AAAW,eAAW,WAAW;AAAM;AACvC;AAAM,eAAW,WAAW;AAAQ;AACtC;AAEA,CAAC;AACC,WAAS;AACT,mBAAiB;AACjB,iBAAe;AACjB;AAEA,CAAC;AACC,WAAS;AACT,OAAK;AACP;AAEA,CALC,UAKU;AACT,SAAO;AACP,UAAQ;AACR,iBAAe;AACf,cAAY,KAAK,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE;AAClC;AAEA,CAAC;AACC,WAAS;AACT,OAAK;AACP;AAEA,CAAC;AACC,cAAY;AACZ,SAAO;AACT;AAEA,CAAC;AACC,WAAS;AACT,aAAW;AACX,eAAa;AACb,iBAAe;AACjB;AAEA,CAAC;AACC,aAAW;AACX,WAAS;AACX;AAGA,CAAC;AACD,CAAC;AACC,WAAS,KAAK;AACd,cAAY;AACd;AAEA,CAAC;AACC,aAAW;AACX,UAAQ,EAAE;AACZ;AAEA,CAAC;AACC,cAAY;AACZ,aAAW;AACX,eAAa;AACb,UAAQ,EAAE,EAAE,KAAK;AACjB,SAAO;AACT;AAEA,CAAC;AACC,WAAS;AACT,yBAAuB,OAAO,QAAQ,EAAE,OAAO,KAAK,EAAE;AACtD,OAAK;AACP;AAEA,CAAC;AACC,cAAY;AACZ,iBAAe;AACf,WAAS;AACT,cAAY,EAAE,IAAI,IAAI,KAAK,KAAK,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE;AACzC,cAAY,IAAI,KAAK;AACrB,UAAQ,IAAI,MAAM;AACpB;AAEA,CATC,YASY;AACX,aAAW,WAAW;AACtB,cAAY,EAAE,KAAK,KAAK,KAAK,KAAK,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE;AAC3C,gBAAc;AAChB;AAEA,CAAC;AACC,aAAW;AACX,iBAAe;AACjB;AAEA,CApBC,aAoBa;AACZ,UAAQ,EAAE,EAAE,KAAK;AACjB,SAAO;AACP,aAAW;AACb;AAEA,CA1BC,aA0Ba;AACZ,UAAQ,EAAE,EAAE,OAAO;AACnB,SAAO;AACP,eAAa;AACf;AAEA,CAAC;AACC,SAAO;AACP,mBAAiB;AACjB,eAAa;AACf;AAEA,CANC,YAMY;AACX,mBAAiB;AACnB;AAEA,CAVC,YAUY,CAAC;AACZ,SAAO;AACP,UAAQ;AACV;AAEA,CAAC;AACC,WAAS;AACT,yBAAuB,OAAO,QAAQ,EAAE,OAAO,KAAK,EAAE;AACtD,OAAK;AACP;AAEA,CAAC;AACC,WAAS;AACT,kBAAgB;AAChB,eAAa;AACb,OAAK;AACL,WAAS;AACT,cAAY;AACZ,iBAAe;AACf,cAAY,IAAI,KAAK;AACvB;AAEA,CAXC,SAWS;AACR,cAAY;AACZ,aAAW,WAAW;AACxB;AAEA,CAAC;AACC,aAAW;AACb;AAGA,CAAC;AACC,cAAY;AACZ,SAAO;AACP,WAAS,KAAK;AAChB;AAEA,CAAC;AACC,WAAS;AACT,mBAAiB;AACjB,eAAa;AACb,aAAW;AACX,OAAK;AACP;AAEA,CAAC,YAAY;AACX,UAAQ,EAAE,EAAE,OAAO;AACnB,aAAW;AACb;AAEA,CALC,YAKY;AACX,UAAQ;AACR,WAAS;AACX;AAEA,CAAC;AACC,WAAS;AACT,OAAK;AACP;AAEA,CAAC;AACC,SAAO;AACP,mBAAiB;AACjB,cAAY,QAAQ,KAAK;AAC3B;AAEA,CANC,WAMW;AACV,WAAS;AACX;AAGA,OAAO,CAAC,SAAS,EAAE;AACjB,GAxRD;AAyRG,2BAAuB;AACvB,gBAAY;AACd;AAEA,GApRD;AAqRG,eAAW;AACb;AAEA,GAlID;AAmIG,2BAAuB;AACzB;AAEA,GAjFD;AAkFG,2BAAuB,OAAO,CAAC,EAAE;AACnC;AAEA,GApDD;AAqDG,oBAAgB;AAChB,gBAAY;AACd;AACF;", "names": []}
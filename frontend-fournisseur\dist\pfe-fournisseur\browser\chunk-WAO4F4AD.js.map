{"version": 3, "sources": ["src/app/services/admin-test.service.ts", "src/app/components/admin/admin-dashboard/admin-dashboard.component.ts", "src/app/components/admin/admin-dashboard/admin-dashboard.component.html"], "sourcesContent": ["import { Injectable } from '@angular/core';\nimport { HttpClient } from '@angular/common/http';\nimport { Observable, of } from 'rxjs';\nimport { tap, catchError } from 'rxjs/operators';\nimport { environment } from '../../environments/environment';\n\n@Injectable({\n  providedIn: 'root'\n})\nexport class AdminTestService {\n  private readonly API_URL = `${environment.apiUrl || 'https://localhost:7264/api'}/Admin`;\n\n  constructor(private http: HttpClient) {}\n\n  /**\n   * Test de connectivité avec le backend admin\n   */\n  testConnectivity(): Observable<any> {\n    console.log('🔗 Test de connectivité admin...');\n    \n    return this.http.get<any>(`${this.API_URL}/statistiques`)\n      .pipe(\n        tap(response => {\n          console.log('✅ Connectivité admin OK:', response);\n        }),\n        catchError(error => {\n          console.error('❌ Erreur de connectivité admin:', error);\n          return of({\n            error: true,\n            status: error.status,\n            message: error.message,\n            url: error.url\n          });\n        })\n      );\n  }\n\n  /**\n   * Test des endpoints utilisateurs\n   */\n  testUsersEndpoint(): Observable<any> {\n    console.log('👥 Test endpoint utilisateurs...');\n    \n    return this.http.get<any>(`${this.API_URL}/utilisateurs`)\n      .pipe(\n        tap(response => {\n          console.log('✅ Endpoint utilisateurs OK:', response);\n        }),\n        catchError(error => {\n          console.error('❌ Erreur endpoint utilisateurs:', error);\n          return of({\n            error: true,\n            status: error.status,\n            message: error.message,\n            url: error.url\n          });\n        })\n      );\n  }\n\n  /**\n   * Test de création d'une catégorie (pour vérifier les permissions)\n   */\n  testCreateCategory(): Observable<any> {\n    console.log('📁 Test création catégorie...');\n    \n    const testCategory = {\n      nom: 'Test Category',\n      description: 'Catégorie de test pour vérifier les permissions'\n    };\n    \n    return this.http.post<any>(`${this.API_URL}/categories`, testCategory)\n      .pipe(\n        tap(response => {\n          console.log('✅ Création catégorie OK:', response);\n        }),\n        catchError(error => {\n          console.error('❌ Erreur création catégorie:', error);\n          return of({\n            error: true,\n            status: error.status,\n            message: error.message,\n            url: error.url\n          });\n        })\n      );\n  }\n\n  /**\n   * Test complet de tous les endpoints\n   */\n  runFullTest(): Observable<any> {\n    console.log('🧪 Démarrage du test complet des endpoints admin...');\n    \n    const tests = [\n      { name: 'Statistiques', test: () => this.testConnectivity() },\n      { name: 'Utilisateurs', test: () => this.testUsersEndpoint() },\n      { name: 'Catégories', test: () => this.testCreateCategory() }\n    ];\n\n    const results: any[] = [];\n\n    return new Observable(observer => {\n      let completed = 0;\n\n      tests.forEach((testCase, index) => {\n        testCase.test().subscribe({\n          next: (result) => {\n            results[index] = {\n              name: testCase.name,\n              success: !result.error,\n              result: result\n            };\n            completed++;\n\n            if (completed === tests.length) {\n              observer.next({\n                summary: {\n                  total: tests.length,\n                  passed: results.filter(r => r.success).length,\n                  failed: results.filter(r => !r.success).length\n                },\n                details: results\n              });\n              observer.complete();\n            }\n          },\n          error: (error) => {\n            results[index] = {\n              name: testCase.name,\n              success: false,\n              error: error\n            };\n            completed++;\n\n            if (completed === tests.length) {\n              observer.next({\n                summary: {\n                  total: tests.length,\n                  passed: results.filter(r => r.success).length,\n                  failed: results.filter(r => !r.success).length\n                },\n                details: results\n              });\n              observer.complete();\n            }\n          }\n        });\n      });\n    });\n  }\n}\n", "import { Component, OnInit, signal, computed } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { RouterModule, Router } from '@angular/router';\nimport { AdminService, StatistiquesAdmin } from '../../../services/admin.service';\nimport { AdminTestService } from '../../../services/admin-test.service';\nimport { AdminAuthService } from '../../../services/admin-auth.service';\n\ninterface DashboardCard {\n  title: string;\n  value: number;\n  icon: string;\n  color: string;\n  description: string;\n  trend?: number; // Pourcentage de changement (positif ou négatif)\n}\n\n@Component({\n  selector: 'app-admin-dashboard',\n  standalone: true,\n  imports: [\n    CommonModule,\n    RouterModule\n  ],\n  templateUrl: './admin-dashboard.component.html',\n  styleUrls: ['./admin-dashboard.component.css']\n})\nexport class AdminDashboardComponent implements OnInit {\n  // Angular 19: Signals\n  activeSection = signal<'dashboard' | 'users' | 'categories' | 'orders' | 'products'>('dashboard');\n  statistiques = signal<StatistiquesAdmin | null>(null);\n  isLoading = signal(false);\n  error = signal('');\n\n  // Computed signals\n  dashboardCards = computed(() => {\n    const stats = this.statistiques();\n    if (!stats) return [];\n\n    return [\n      {\n        title: 'Utilisateurs',\n        value: stats.nombreUtilisateurs,\n        icon: 'icon-users',\n        color: 'blue',\n        description: `${stats.nombreFournisseurs} fournisseurs, ${stats.nombreClients} clients`,\n        trend: this.calculateTrend(stats.nombreUtilisateurs, 0)\n      },\n      {\n        title: 'Produits',\n        value: stats.nombreProduits || 0,\n        icon: 'icon-package',\n        color: 'green',\n        description: 'Produits disponibles',\n        trend: this.calculateTrend(stats.nombreProduits || 0, 0)\n      },\n      {\n        title: 'Commandes',\n        value: stats.nombreCommandes,\n        icon: 'icon-shopping-cart',\n        color: 'orange',\n        description: `${stats.nombreCommandesAnnulees || 0} annulées`,\n        trend: this.calculateTrend(stats.nombreCommandes, 0)\n      },\n      {\n        title: 'Ventes',\n        value: stats.nombreVentes || 0,\n        icon: 'icon-trending-up',\n        color: 'purple',\n        description: 'Total des ventes',\n        trend: this.calculateTrend(stats.nombreVentes || 0, 0)\n      }\n    ];\n  });\n\n  menuItems = [\n    {\n      id: 'dashboard',\n      label: 'Tableau de bord',\n      icon: 'icon-home',\n      description: 'Vue d\\'ensemble et statistiques'\n    },\n    {\n      id: 'users',\n      label: 'Utilisateurs',\n      icon: 'icon-users',\n      description: 'Gestion des comptes utilisateurs'\n    },\n    {\n      id: 'categories',\n      label: 'Catégories',\n      icon: 'icon-folder',\n      description: 'Gestion des catégories et sous-catégories'\n    },\n    {\n      id: 'orders',\n      label: 'Commandes',\n      icon: 'icon-shopping-cart',\n      description: 'Gestion des commandes'\n    },\n    {\n      id: 'products',\n      label: 'Produits',\n      icon: 'icon-package',\n      description: 'Validation et mise en avant des produits'\n    }\n  ];\n\n  constructor(\n    private adminService: AdminService,\n    private adminTestService: AdminTestService,\n    private adminAuthService: AdminAuthService,\n    private router: Router\n  ) {}\n\n  ngOnInit(): void {\n    this.loadStatistiques();\n  }\n\n  loadStatistiques(): void {\n    this.isLoading.set(true);\n    this.error.set('');\n\n    this.adminService.getStatistiques().subscribe({\n      next: (stats) => {\n        console.log('📊 Statistiques reçues:', stats);\n        this.statistiques.set(stats);\n        this.isLoading.set(false);\n      },\n      error: (error) => {\n        console.error('❌ Erreur lors du chargement des statistiques:', error);\n        this.error.set('Erreur lors du chargement des statistiques');\n        this.isLoading.set(false);\n      }\n    });\n  }\n\n  setActiveSection(section: string): void {\n    this.activeSection.set(section as 'dashboard' | 'users' | 'categories' | 'orders' | 'products');\n  }\n\n  formatPrice(price: number): string {\n    return new Intl.NumberFormat('fr-TN', {\n      style: 'currency',\n      currency: 'TND',\n      minimumFractionDigits: 0,\n      maximumFractionDigits: 0\n    }).format(price);\n  }\n\n  formatNumber(num: number): string {\n    return new Intl.NumberFormat('fr-FR').format(num);\n  }\n\n  refresh(): void {\n    this.loadStatistiques();\n  }\n\n  getCardClass(color: string): string {\n    return `stat-card stat-card-${color}`;\n  }\n\n  getMenuItemClass(itemId: string): string {\n    return this.activeSection() === itemId ? 'menu-item active' : 'menu-item';\n  }\n\n  testBackendConnection(): void {\n    console.log('🧪 Démarrage du test de connectivité backend...');\n\n    this.adminTestService.runFullTest().subscribe({\n      next: (results) => {\n        console.log('📊 Résultats des tests:', results);\n\n        const { summary, details } = results;\n        const message = `Tests terminés: ${summary.passed}/${summary.total} réussis\\n\\n` +\n          details.map((test: any) =>\n            `${test.success ? '✅' : '❌'} ${test.name}: ${test.success ? 'OK' : 'ERREUR'}`\n          ).join('\\n');\n\n        alert(message);\n      },\n      error: (error) => {\n        console.error('❌ Erreur lors des tests:', error);\n        alert('Erreur lors des tests de connectivité');\n      }\n    });\n  }\n\n\n\n  getCardIcon(iconClass: string): string {\n    const iconMap: { [key: string]: string } = {\n      'icon-users': '👥',\n      'icon-package': '📦',\n      'icon-shopping-cart': '🛒',\n      'icon-trending-up': '📈'\n    };\n    return iconMap[iconClass] || '📊';\n  }\n\n  getCurrentTime(): string {\n    return new Date().toLocaleTimeString('fr-FR', {\n      hour: '2-digit',\n      minute: '2-digit'\n    });\n  }\n\n  logout(): void {\n    this.adminAuthService.logout().subscribe({\n      next: () => {\n        this.router.navigate(['/adminOptiLet']);\n      },\n      error: (error) => {\n        console.error('Erreur lors de la déconnexion:', error);\n        // Forcer la redirection même en cas d'erreur\n        this.router.navigate(['/adminOptiLet']);\n      }\n    });\n  }\n\n  calculateTrend(current: number, previous: number): number {\n    if (previous === 0) return current > 0 ? 100 : 0;\n    return Math.round(((current - previous) / previous) * 100);\n  }\n\n  getTrendClass(trend: number): string {\n    const baseClass = 'trend-indicator';\n    if (trend > 0) return `${baseClass} positive`;\n    if (trend < 0) return `${baseClass} negative`;\n    return `${baseClass} neutral`;\n  }\n\n  formatTrend(trend: number): string {\n    if (trend === 0) return '0%';\n    const sign = trend > 0 ? '+' : '';\n    return `${sign}${trend}%`;\n  }\n}\n", "<div class=\"admin-dashboard\">\n  <!-- Header amé<PERSON> -->\n  <div class=\"dashboard-header\">\n    <div class=\"header-content\">\n      <div class=\"welcome-section\">\n        <div class=\"welcome-icon\">\n          <div class=\"icon-container\">\n            <svg width=\"28\" height=\"28\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" stroke-width=\"2\">\n              <path d=\"M3 9l9-7 9 7v11a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2z\"></path>\n              <polyline points=\"9,22 9,12 15,12 15,22\"></polyline>\n            </svg>\n          </div>\n        </div>\n        <div class=\"welcome-text\">\n          <h1 class=\"dashboard-title\">Tableau de bord administrateur</h1>\n          <p class=\"dashboard-subtitle\">Bienvenue dans l'espace d'administration OptiLet</p>\n          <div class=\"breadcrumb\">\n            <span class=\"breadcrumb-item\">Administration</span>\n            <span class=\"breadcrumb-separator\">›</span>\n            <span class=\"breadcrumb-item active\">Dashboard</span>\n          </div>\n        </div>\n      </div>\n      <div class=\"header-actions\">\n        <div class=\"time-info\">\n          <span class=\"current-time\">{{ getCurrentTime() }}</span>\n        </div>\n        <button class=\"refresh-btn\" (click)=\"loadStatistiques()\" [disabled]=\"isLoading()\">\n          <svg width=\"18\" height=\"18\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" stroke-width=\"2\">\n            <polyline points=\"23 4 23 10 17 10\"></polyline>\n            <path d=\"M20.49 9A9 9 0 0 0 5.64 5.64L1 10m22 4l-4.64 4.36A9 9 0 0 1 3.51 15\"></path>\n          </svg>\n          Actualiser\n        </button>\n        <button class=\"logout-btn\" (click)=\"logout()\">\n          <svg width=\"18\" height=\"18\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" stroke-width=\"2\">\n            <path d=\"M9 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h4\"></path>\n            <polyline points=\"16,17 21,12 16,7\"></polyline>\n            <line x1=\"21\" y1=\"12\" x2=\"9\" y2=\"12\"></line>\n          </svg>\n          Déconnexion\n        </button>\n      </div>\n    </div>\n  </div>\n\n  <!-- Loading -->\n  <div *ngIf=\"isLoading()\" class=\"loading-state\">\n    <div class=\"loading-spinner\"></div>\n    <p>Chargement des statistiques...</p>\n  </div>\n\n  <!-- Error -->\n  <div *ngIf=\"error() && !isLoading()\" class=\"error-state\">\n    <div class=\"error-icon\">⚠️</div>\n    <h3>Erreur de chargement</h3>\n    <p>{{ error() }}</p>\n    <button class=\"retry-btn\" (click)=\"loadStatistiques()\">Réessayer</button>\n  </div>\n\n  <!-- Dashboard Content -->\n  <div *ngIf=\"!isLoading() && !error()\" class=\"dashboard-content\">\n\n    <!-- Statistics Cards -->\n    <div class=\"stats-grid\">\n      <div *ngFor=\"let card of dashboardCards()\" class=\"stat-card\" [attr.data-color]=\"card.color\">\n        <div class=\"card-header\">\n          <div class=\"card-icon\">\n            <span [innerHTML]=\"getCardIcon(card.icon)\"></span>\n          </div>\n          <div class=\"card-trend\" *ngIf=\"card.trend\">\n            <span [class]=\"getTrendClass(card.trend)\">\n              {{ formatTrend(card.trend) }}\n            </span>\n          </div>\n        </div>\n        <div class=\"card-body\">\n          <div class=\"card-value\">{{ card.value | number }}</div>\n          <div class=\"card-title\">{{ card.title }}</div>\n          <div class=\"card-description\">{{ card.description }}</div>\n        </div>\n      </div>\n    </div>\n\n    <!-- Quick Actions -->\n    <div class=\"quick-actions\">\n      <h2 class=\"section-title\">Actions rapides</h2>\n      <div class=\"actions-grid\">\n        <button class=\"action-card\" routerLink=\"/admin/dashboard/users\">\n          <div class=\"action-icon\">👥</div>\n          <div class=\"action-content\">\n            <h3>Gestion des utilisateurs</h3>\n            <p>Gérer les comptes et permissions</p>\n          </div>\n          <div class=\"action-arrow\">→</div>\n        </button>\n\n        <button class=\"action-card\" routerLink=\"/admin/dashboard/products\">\n          <div class=\"action-icon\">📦</div>\n          <div class=\"action-content\">\n            <h3>Gestion des produits</h3>\n            <p>Valider et modérer les produits</p>\n          </div>\n          <div class=\"action-arrow\">→</div>\n        </button>\n\n        <button class=\"action-card\" routerLink=\"/admin/dashboard/demandes\">\n          <div class=\"action-icon\">📋</div>\n          <div class=\"action-content\">\n            <h3>Demandes en attente</h3>\n            <p>Traiter les demandes de catégories</p>\n          </div>\n          <div class=\"action-arrow\">→</div>\n        </button>\n\n        <button class=\"action-card\" routerLink=\"/admin/dashboard/avis-moderation\">\n          <div class=\"action-icon\">⭐</div>\n          <div class=\"action-content\">\n            <h3>Modération des avis</h3>\n            <p>Gérer les avis clients</p>\n          </div>\n          <div class=\"action-arrow\">→</div>\n        </button>\n      </div>\n    </div>\n\n  </div>\n</div>\n\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASM,IAAO,mBAAP,MAAO,kBAAgB;EAGP;EAFH,UAAU,GAAG,YAAY,UAAU,4BAA4B;EAEhF,YAAoB,MAAgB;AAAhB,SAAA,OAAA;EAAmB;;;;EAKvC,mBAAgB;AACd,YAAQ,IAAI,4CAAkC;AAE9C,WAAO,KAAK,KAAK,IAAS,GAAG,KAAK,OAAO,eAAe,EACrD,KACC,IAAI,cAAW;AACb,cAAQ,IAAI,oCAA4B,QAAQ;IAClD,CAAC,GACD,WAAW,WAAQ;AACjB,cAAQ,MAAM,2CAAmC,KAAK;AACtD,aAAO,GAAG;QACR,OAAO;QACP,QAAQ,MAAM;QACd,SAAS,MAAM;QACf,KAAK,MAAM;OACZ;IACH,CAAC,CAAC;EAER;;;;EAKA,oBAAiB;AACf,YAAQ,IAAI,yCAAkC;AAE9C,WAAO,KAAK,KAAK,IAAS,GAAG,KAAK,OAAO,eAAe,EACrD,KACC,IAAI,cAAW;AACb,cAAQ,IAAI,oCAA+B,QAAQ;IACrD,CAAC,GACD,WAAW,WAAQ;AACjB,cAAQ,MAAM,wCAAmC,KAAK;AACtD,aAAO,GAAG;QACR,OAAO;QACP,QAAQ,MAAM;QACd,SAAS,MAAM;QACf,KAAK,MAAM;OACZ;IACH,CAAC,CAAC;EAER;;;;EAKA,qBAAkB;AAChB,YAAQ,IAAI,4CAA+B;AAE3C,UAAM,eAAe;MACnB,KAAK;MACL,aAAa;;AAGf,WAAO,KAAK,KAAK,KAAU,GAAG,KAAK,OAAO,eAAe,YAAY,EAClE,KACC,IAAI,cAAW;AACb,cAAQ,IAAI,uCAA4B,QAAQ;IAClD,CAAC,GACD,WAAW,WAAQ;AACjB,cAAQ,MAAM,2CAAgC,KAAK;AACnD,aAAO,GAAG;QACR,OAAO;QACP,QAAQ,MAAM;QACd,SAAS,MAAM;QACf,KAAK,MAAM;OACZ;IACH,CAAC,CAAC;EAER;;;;EAKA,cAAW;AACT,YAAQ,IAAI,+DAAqD;AAEjE,UAAM,QAAQ;MACZ,EAAE,MAAM,gBAAgB,MAAM,MAAM,KAAK,iBAAgB,EAAE;MAC3D,EAAE,MAAM,gBAAgB,MAAM,MAAM,KAAK,kBAAiB,EAAE;MAC5D,EAAE,MAAM,iBAAc,MAAM,MAAM,KAAK,mBAAkB,EAAE;;AAG7D,UAAM,UAAiB,CAAA;AAEvB,WAAO,IAAI,WAAW,cAAW;AAC/B,UAAI,YAAY;AAEhB,YAAM,QAAQ,CAAC,UAAU,UAAS;AAChC,iBAAS,KAAI,EAAG,UAAU;UACxB,MAAM,CAAC,WAAU;AACf,oBAAQ,KAAK,IAAI;cACf,MAAM,SAAS;cACf,SAAS,CAAC,OAAO;cACjB;;AAEF;AAEA,gBAAI,cAAc,MAAM,QAAQ;AAC9B,uBAAS,KAAK;gBACZ,SAAS;kBACP,OAAO,MAAM;kBACb,QAAQ,QAAQ,OAAO,OAAK,EAAE,OAAO,EAAE;kBACvC,QAAQ,QAAQ,OAAO,OAAK,CAAC,EAAE,OAAO,EAAE;;gBAE1C,SAAS;eACV;AACD,uBAAS,SAAQ;YACnB;UACF;UACA,OAAO,CAAC,UAAS;AACf,oBAAQ,KAAK,IAAI;cACf,MAAM,SAAS;cACf,SAAS;cACT;;AAEF;AAEA,gBAAI,cAAc,MAAM,QAAQ;AAC9B,uBAAS,KAAK;gBACZ,SAAS;kBACP,OAAO,MAAM;kBACb,QAAQ,QAAQ,OAAO,OAAK,EAAE,OAAO,EAAE;kBACvC,QAAQ,QAAQ,OAAO,OAAK,CAAC,EAAE,OAAO,EAAE;;gBAE1C,SAAS;eACV;AACD,uBAAS,SAAQ;YACnB;UACF;SACD;MACH,CAAC;IACH,CAAC;EACH;;qCA7IW,mBAAgB,mBAAA,UAAA,CAAA;EAAA;4EAAhB,mBAAgB,SAAhB,kBAAgB,WAAA,YAFf,OAAM,CAAA;;;sEAEP,kBAAgB,CAAA;UAH5B;WAAW;MACV,YAAY;KACb;;;;;;;AEuCC,IAAA,yBAAA,GAAA,OAAA,EAAA;AACE,IAAA,oBAAA,GAAA,OAAA,EAAA;AACA,IAAA,yBAAA,GAAA,GAAA;AAAG,IAAA,iBAAA,GAAA,gCAAA;AAA8B,IAAA,uBAAA,EAAI;;;;;;AAIvC,IAAA,yBAAA,GAAA,OAAA,EAAA,EAAyD,GAAA,OAAA,EAAA;AAC/B,IAAA,iBAAA,GAAA,cAAA;AAAE,IAAA,uBAAA;AAC1B,IAAA,yBAAA,GAAA,IAAA;AAAI,IAAA,iBAAA,GAAA,sBAAA;AAAoB,IAAA,uBAAA;AACxB,IAAA,yBAAA,GAAA,GAAA;AAAG,IAAA,iBAAA,CAAA;AAAa,IAAA,uBAAA;AAChB,IAAA,yBAAA,GAAA,UAAA,EAAA;AAA0B,IAAA,qBAAA,SAAA,SAAA,kEAAA;AAAA,MAAA,wBAAA,GAAA;AAAA,YAAA,SAAA,wBAAA;AAAA,aAAA,sBAAS,OAAA,iBAAA,CAAkB;IAAA,CAAA;AAAE,IAAA,iBAAA,GAAA,cAAA;AAAS,IAAA,uBAAA,EAAS;;;;AADtE,IAAA,oBAAA,CAAA;AAAA,IAAA,4BAAA,OAAA,MAAA,CAAA;;;;;AAcG,IAAA,yBAAA,GAAA,OAAA,EAAA,EAA2C,GAAA,MAAA;AAEvC,IAAA,iBAAA,CAAA;AACF,IAAA,uBAAA,EAAO;;;;;AAFD,IAAA,oBAAA;AAAA,IAAA,qBAAA,OAAA,cAAA,QAAA,KAAA,CAAA;AACJ,IAAA,oBAAA;AAAA,IAAA,6BAAA,KAAA,OAAA,YAAA,QAAA,KAAA,GAAA,GAAA;;;;;AAPR,IAAA,yBAAA,GAAA,OAAA,EAAA,EAA4F,GAAA,OAAA,EAAA,EACjE,GAAA,OAAA,EAAA;AAErB,IAAA,oBAAA,GAAA,QAAA,EAAA;AACF,IAAA,uBAAA;AACA,IAAA,qBAAA,GAAA,qDAAA,GAAA,GAAA,OAAA,EAAA;AAKF,IAAA,uBAAA;AACA,IAAA,yBAAA,GAAA,OAAA,EAAA,EAAuB,GAAA,OAAA,EAAA;AACG,IAAA,iBAAA,CAAA;;AAAyB,IAAA,uBAAA;AACjD,IAAA,yBAAA,GAAA,OAAA,EAAA;AAAwB,IAAA,iBAAA,EAAA;AAAgB,IAAA,uBAAA;AACxC,IAAA,yBAAA,IAAA,OAAA,EAAA;AAA8B,IAAA,iBAAA,EAAA;AAAsB,IAAA,uBAAA,EAAM,EACtD;;;;;;AAZI,IAAA,oBAAA,CAAA;AAAA,IAAA,qBAAA,aAAA,OAAA,YAAA,QAAA,IAAA,GAAA,wBAAA;AAEiB,IAAA,oBAAA;AAAA,IAAA,qBAAA,QAAA,QAAA,KAAA;AAOD,IAAA,oBAAA,CAAA;AAAA,IAAA,4BAAA,sBAAA,GAAA,GAAA,QAAA,KAAA,CAAA;AACA,IAAA,oBAAA,CAAA;AAAA,IAAA,4BAAA,QAAA,KAAA;AACM,IAAA,oBAAA,CAAA;AAAA,IAAA,4BAAA,QAAA,WAAA;;;;;AAlBtC,IAAA,yBAAA,GAAA,OAAA,EAAA,EAAgE,GAAA,OAAA,EAAA;AAI5D,IAAA,qBAAA,GAAA,+CAAA,IAAA,GAAA,OAAA,EAAA;AAiBF,IAAA,uBAAA;AAGA,IAAA,yBAAA,GAAA,OAAA,EAAA,EAA2B,GAAA,MAAA,EAAA;AACC,IAAA,iBAAA,GAAA,iBAAA;AAAe,IAAA,uBAAA;AACzC,IAAA,yBAAA,GAAA,OAAA,EAAA,EAA0B,GAAA,UAAA,EAAA,EACwC,GAAA,OAAA,EAAA;AACrC,IAAA,iBAAA,GAAA,WAAA;AAAE,IAAA,uBAAA;AAC3B,IAAA,yBAAA,IAAA,OAAA,EAAA,EAA4B,IAAA,IAAA;AACtB,IAAA,iBAAA,IAAA,0BAAA;AAAwB,IAAA,uBAAA;AAC5B,IAAA,yBAAA,IAAA,GAAA;AAAG,IAAA,iBAAA,IAAA,qCAAA;AAAgC,IAAA,uBAAA,EAAI;AAEzC,IAAA,yBAAA,IAAA,OAAA,EAAA;AAA0B,IAAA,iBAAA,IAAA,QAAA;AAAC,IAAA,uBAAA,EAAM;AAGnC,IAAA,yBAAA,IAAA,UAAA,EAAA,EAAmE,IAAA,OAAA,EAAA;AACxC,IAAA,iBAAA,IAAA,WAAA;AAAE,IAAA,uBAAA;AAC3B,IAAA,yBAAA,IAAA,OAAA,EAAA,EAA4B,IAAA,IAAA;AACtB,IAAA,iBAAA,IAAA,sBAAA;AAAoB,IAAA,uBAAA;AACxB,IAAA,yBAAA,IAAA,GAAA;AAAG,IAAA,iBAAA,IAAA,oCAAA;AAA+B,IAAA,uBAAA,EAAI;AAExC,IAAA,yBAAA,IAAA,OAAA,EAAA;AAA0B,IAAA,iBAAA,IAAA,QAAA;AAAC,IAAA,uBAAA,EAAM;AAGnC,IAAA,yBAAA,IAAA,UAAA,EAAA,EAAmE,IAAA,OAAA,EAAA;AACxC,IAAA,iBAAA,IAAA,WAAA;AAAE,IAAA,uBAAA;AAC3B,IAAA,yBAAA,IAAA,OAAA,EAAA,EAA4B,IAAA,IAAA;AACtB,IAAA,iBAAA,IAAA,qBAAA;AAAmB,IAAA,uBAAA;AACvB,IAAA,yBAAA,IAAA,GAAA;AAAG,IAAA,iBAAA,IAAA,uCAAA;AAAkC,IAAA,uBAAA,EAAI;AAE3C,IAAA,yBAAA,IAAA,OAAA,EAAA;AAA0B,IAAA,iBAAA,IAAA,QAAA;AAAC,IAAA,uBAAA,EAAM;AAGnC,IAAA,yBAAA,IAAA,UAAA,EAAA,EAA0E,IAAA,OAAA,EAAA;AAC/C,IAAA,iBAAA,IAAA,QAAA;AAAC,IAAA,uBAAA;AAC1B,IAAA,yBAAA,IAAA,OAAA,EAAA,EAA4B,IAAA,IAAA;AACtB,IAAA,iBAAA,IAAA,wBAAA;AAAmB,IAAA,uBAAA;AACvB,IAAA,yBAAA,IAAA,GAAA;AAAG,IAAA,iBAAA,IAAA,2BAAA;AAAsB,IAAA,uBAAA,EAAI;AAE/B,IAAA,yBAAA,IAAA,OAAA,EAAA;AAA0B,IAAA,iBAAA,IAAA,QAAA;AAAC,IAAA,uBAAA,EAAM,EAC1B,EACL,EACF;;;;AA3DkB,IAAA,oBAAA,CAAA;AAAA,IAAA,qBAAA,WAAA,OAAA,eAAA,CAAA;;;ADvCtB,IAAO,0BAAP,MAAO,yBAAuB;EAkFxB;EACA;EACA;EACA;;EAnFV,gBAAgB,OAAqE,WAAW;EAChG,eAAe,OAAiC,IAAI;EACpD,YAAY,OAAO,KAAK;EACxB,QAAQ,OAAO,EAAE;;EAGjB,iBAAiB,SAAS,MAAK;AAC7B,UAAM,QAAQ,KAAK,aAAY;AAC/B,QAAI,CAAC;AAAO,aAAO,CAAA;AAEnB,WAAO;MACL;QACE,OAAO;QACP,OAAO,MAAM;QACb,MAAM;QACN,OAAO;QACP,aAAa,GAAG,MAAM,kBAAkB,kBAAkB,MAAM,aAAa;QAC7E,OAAO,KAAK,eAAe,MAAM,oBAAoB,CAAC;;MAExD;QACE,OAAO;QACP,OAAO,MAAM,kBAAkB;QAC/B,MAAM;QACN,OAAO;QACP,aAAa;QACb,OAAO,KAAK,eAAe,MAAM,kBAAkB,GAAG,CAAC;;MAEzD;QACE,OAAO;QACP,OAAO,MAAM;QACb,MAAM;QACN,OAAO;QACP,aAAa,GAAG,MAAM,2BAA2B,CAAC;QAClD,OAAO,KAAK,eAAe,MAAM,iBAAiB,CAAC;;MAErD;QACE,OAAO;QACP,OAAO,MAAM,gBAAgB;QAC7B,MAAM;QACN,OAAO;QACP,aAAa;QACb,OAAO,KAAK,eAAe,MAAM,gBAAgB,GAAG,CAAC;;;EAG3D,CAAC;EAED,YAAY;IACV;MACE,IAAI;MACJ,OAAO;MACP,MAAM;MACN,aAAa;;IAEf;MACE,IAAI;MACJ,OAAO;MACP,MAAM;MACN,aAAa;;IAEf;MACE,IAAI;MACJ,OAAO;MACP,MAAM;MACN,aAAa;;IAEf;MACE,IAAI;MACJ,OAAO;MACP,MAAM;MACN,aAAa;;IAEf;MACE,IAAI;MACJ,OAAO;MACP,MAAM;MACN,aAAa;;;EAIjB,YACU,cACA,kBACA,kBACA,QAAc;AAHd,SAAA,eAAA;AACA,SAAA,mBAAA;AACA,SAAA,mBAAA;AACA,SAAA,SAAA;EACP;EAEH,WAAQ;AACN,SAAK,iBAAgB;EACvB;EAEA,mBAAgB;AACd,SAAK,UAAU,IAAI,IAAI;AACvB,SAAK,MAAM,IAAI,EAAE;AAEjB,SAAK,aAAa,gBAAe,EAAG,UAAU;MAC5C,MAAM,CAAC,UAAS;AACd,gBAAQ,IAAI,qCAA2B,KAAK;AAC5C,aAAK,aAAa,IAAI,KAAK;AAC3B,aAAK,UAAU,IAAI,KAAK;MAC1B;MACA,OAAO,CAAC,UAAS;AACf,gBAAQ,MAAM,sDAAiD,KAAK;AACpE,aAAK,MAAM,IAAI,4CAA4C;AAC3D,aAAK,UAAU,IAAI,KAAK;MAC1B;KACD;EACH;EAEA,iBAAiB,SAAe;AAC9B,SAAK,cAAc,IAAI,OAAuE;EAChG;EAEA,YAAY,OAAa;AACvB,WAAO,IAAI,KAAK,aAAa,SAAS;MACpC,OAAO;MACP,UAAU;MACV,uBAAuB;MACvB,uBAAuB;KACxB,EAAE,OAAO,KAAK;EACjB;EAEA,aAAa,KAAW;AACtB,WAAO,IAAI,KAAK,aAAa,OAAO,EAAE,OAAO,GAAG;EAClD;EAEA,UAAO;AACL,SAAK,iBAAgB;EACvB;EAEA,aAAa,OAAa;AACxB,WAAO,uBAAuB,KAAK;EACrC;EAEA,iBAAiB,QAAc;AAC7B,WAAO,KAAK,cAAa,MAAO,SAAS,qBAAqB;EAChE;EAEA,wBAAqB;AACnB,YAAQ,IAAI,8DAAiD;AAE7D,SAAK,iBAAiB,YAAW,EAAG,UAAU;MAC5C,MAAM,CAAC,YAAW;AAChB,gBAAQ,IAAI,qCAA2B,OAAO;AAE9C,cAAM,EAAE,SAAS,QAAO,IAAK;AAC7B,cAAM,UAAU,sBAAmB,QAAQ,MAAM,IAAI,QAAQ,KAAK;;IAChE,QAAQ,IAAI,CAAC,SACX,GAAG,KAAK,UAAU,WAAM,QAAG,IAAI,KAAK,IAAI,KAAK,KAAK,UAAU,OAAO,QAAQ,EAAE,EAC7E,KAAK,IAAI;AAEb,cAAM,OAAO;MACf;MACA,OAAO,CAAC,UAAS;AACf,gBAAQ,MAAM,iCAA4B,KAAK;AAC/C,cAAM,0CAAuC;MAC/C;KACD;EACH;EAIA,YAAY,WAAiB;AAC3B,UAAM,UAAqC;MACzC,cAAc;MACd,gBAAgB;MAChB,sBAAsB;MACtB,oBAAoB;;AAEtB,WAAO,QAAQ,SAAS,KAAK;EAC/B;EAEA,iBAAc;AACZ,YAAO,oBAAI,KAAI,GAAG,mBAAmB,SAAS;MAC5C,MAAM;MACN,QAAQ;KACT;EACH;EAEA,SAAM;AACJ,SAAK,iBAAiB,OAAM,EAAG,UAAU;MACvC,MAAM,MAAK;AACT,aAAK,OAAO,SAAS,CAAC,eAAe,CAAC;MACxC;MACA,OAAO,CAAC,UAAS;AACf,gBAAQ,MAAM,qCAAkC,KAAK;AAErD,aAAK,OAAO,SAAS,CAAC,eAAe,CAAC;MACxC;KACD;EACH;EAEA,eAAe,SAAiB,UAAgB;AAC9C,QAAI,aAAa;AAAG,aAAO,UAAU,IAAI,MAAM;AAC/C,WAAO,KAAK,OAAQ,UAAU,YAAY,WAAY,GAAG;EAC3D;EAEA,cAAc,OAAa;AACzB,UAAM,YAAY;AAClB,QAAI,QAAQ;AAAG,aAAO,GAAG,SAAS;AAClC,QAAI,QAAQ;AAAG,aAAO,GAAG,SAAS;AAClC,WAAO,GAAG,SAAS;EACrB;EAEA,YAAY,OAAa;AACvB,QAAI,UAAU;AAAG,aAAO;AACxB,UAAM,OAAO,QAAQ,IAAI,MAAM;AAC/B,WAAO,GAAG,IAAI,GAAG,KAAK;EACxB;;qCAjNW,0BAAuB,4BAAA,YAAA,GAAA,4BAAA,gBAAA,GAAA,4BAAA,gBAAA,GAAA,4BAAA,MAAA,CAAA;EAAA;yEAAvB,0BAAuB,WAAA,CAAA,CAAA,qBAAA,CAAA,GAAA,OAAA,IAAA,MAAA,GAAA,QAAA,CAAA,CAAA,GAAA,iBAAA,GAAA,CAAA,GAAA,kBAAA,GAAA,CAAA,GAAA,gBAAA,GAAA,CAAA,GAAA,iBAAA,GAAA,CAAA,GAAA,cAAA,GAAA,CAAA,GAAA,gBAAA,GAAA,CAAA,SAAA,MAAA,UAAA,MAAA,WAAA,aAAA,QAAA,QAAA,UAAA,gBAAA,gBAAA,GAAA,GAAA,CAAA,KAAA,gDAAA,GAAA,CAAA,UAAA,uBAAA,GAAA,CAAA,GAAA,cAAA,GAAA,CAAA,GAAA,iBAAA,GAAA,CAAA,GAAA,oBAAA,GAAA,CAAA,GAAA,YAAA,GAAA,CAAA,GAAA,iBAAA,GAAA,CAAA,GAAA,sBAAA,GAAA,CAAA,GAAA,mBAAA,QAAA,GAAA,CAAA,GAAA,gBAAA,GAAA,CAAA,GAAA,WAAA,GAAA,CAAA,GAAA,cAAA,GAAA,CAAA,GAAA,eAAA,GAAA,SAAA,UAAA,GAAA,CAAA,SAAA,MAAA,UAAA,MAAA,WAAA,aAAA,QAAA,QAAA,UAAA,gBAAA,gBAAA,GAAA,GAAA,CAAA,UAAA,kBAAA,GAAA,CAAA,KAAA,qEAAA,GAAA,CAAA,GAAA,cAAA,GAAA,OAAA,GAAA,CAAA,KAAA,yCAAA,GAAA,CAAA,UAAA,kBAAA,GAAA,CAAA,MAAA,MAAA,MAAA,MAAA,MAAA,KAAA,MAAA,IAAA,GAAA,CAAA,SAAA,iBAAA,GAAA,MAAA,GAAA,CAAA,SAAA,eAAA,GAAA,MAAA,GAAA,CAAA,SAAA,qBAAA,GAAA,MAAA,GAAA,CAAA,GAAA,eAAA,GAAA,CAAA,GAAA,iBAAA,GAAA,CAAA,GAAA,aAAA,GAAA,CAAA,GAAA,YAAA,GAAA,CAAA,GAAA,aAAA,GAAA,OAAA,GAAA,CAAA,GAAA,mBAAA,GAAA,CAAA,GAAA,YAAA,GAAA,CAAA,SAAA,aAAA,GAAA,SAAA,SAAA,GAAA,CAAA,GAAA,eAAA,GAAA,CAAA,GAAA,eAAA,GAAA,CAAA,GAAA,cAAA,GAAA,CAAA,cAAA,0BAAA,GAAA,aAAA,GAAA,CAAA,GAAA,aAAA,GAAA,CAAA,GAAA,gBAAA,GAAA,CAAA,GAAA,cAAA,GAAA,CAAA,cAAA,6BAAA,GAAA,aAAA,GAAA,CAAA,cAAA,6BAAA,GAAA,aAAA,GAAA,CAAA,cAAA,oCAAA,GAAA,aAAA,GAAA,CAAA,GAAA,WAAA,GAAA,CAAA,GAAA,aAAA,GAAA,CAAA,GAAA,WAAA,GAAA,CAAA,GAAA,WAAA,GAAA,CAAA,SAAA,cAAA,GAAA,MAAA,GAAA,CAAA,GAAA,WAAA,GAAA,CAAA,GAAA,YAAA,GAAA,CAAA,GAAA,YAAA,GAAA,CAAA,GAAA,kBAAA,GAAA,CAAA,GAAA,YAAA,CAAA,GAAA,UAAA,SAAA,iCAAA,IAAA,KAAA;AAAA,QAAA,KAAA,GAAA;AC1BpC,MAAA,yBAAA,GAAA,OAAA,CAAA,EAA6B,GAAA,OAAA,CAAA,EAEG,GAAA,OAAA,CAAA,EACA,GAAA,OAAA,CAAA,EACG,GAAA,OAAA,CAAA,EACD,GAAA,OAAA,CAAA;;AAEtB,MAAA,yBAAA,GAAA,OAAA,CAAA;AACE,MAAA,oBAAA,GAAA,QAAA,CAAA,EAAgE,GAAA,YAAA,CAAA;AAElE,MAAA,uBAAA,EAAM,EACF;;AAER,MAAA,yBAAA,GAAA,OAAA,CAAA,EAA0B,IAAA,MAAA,EAAA;AACI,MAAA,iBAAA,IAAA,gCAAA;AAA8B,MAAA,uBAAA;AAC1D,MAAA,yBAAA,IAAA,KAAA,EAAA;AAA8B,MAAA,iBAAA,IAAA,kDAAA;AAAgD,MAAA,uBAAA;AAC9E,MAAA,yBAAA,IAAA,OAAA,EAAA,EAAwB,IAAA,QAAA,EAAA;AACQ,MAAA,iBAAA,IAAA,gBAAA;AAAc,MAAA,uBAAA;AAC5C,MAAA,yBAAA,IAAA,QAAA,EAAA;AAAmC,MAAA,iBAAA,IAAA,QAAA;AAAC,MAAA,uBAAA;AACpC,MAAA,yBAAA,IAAA,QAAA,EAAA;AAAqC,MAAA,iBAAA,IAAA,WAAA;AAAS,MAAA,uBAAA,EAAO,EACjD,EACF;AAER,MAAA,yBAAA,IAAA,OAAA,EAAA,EAA4B,IAAA,OAAA,EAAA,EACH,IAAA,QAAA,EAAA;AACM,MAAA,iBAAA,EAAA;AAAsB,MAAA,uBAAA,EAAO;AAE1D,MAAA,yBAAA,IAAA,UAAA,EAAA;AAA4B,MAAA,qBAAA,SAAA,SAAA,4DAAA;AAAA,eAAS,IAAA,iBAAA;MAAkB,CAAA;;AACrD,MAAA,yBAAA,IAAA,OAAA,EAAA;AACE,MAAA,oBAAA,IAAA,YAAA,EAAA,EAA+C,IAAA,QAAA,EAAA;AAEjD,MAAA,uBAAA;AACA,MAAA,iBAAA,IAAA,cAAA;AACF,MAAA,uBAAA;;AACA,MAAA,yBAAA,IAAA,UAAA,EAAA;AAA2B,MAAA,qBAAA,SAAA,SAAA,4DAAA;AAAA,eAAS,IAAA,OAAA;MAAQ,CAAA;;AAC1C,MAAA,yBAAA,IAAA,OAAA,EAAA;AACE,MAAA,oBAAA,IAAA,QAAA,EAAA,EAAyD,IAAA,YAAA,EAAA,EACV,IAAA,QAAA,EAAA;AAEjD,MAAA,uBAAA;AACA,MAAA,iBAAA,IAAA,kBAAA;AACF,MAAA,uBAAA,EAAS,EACL,EACF;AAIR,MAAA,qBAAA,IAAA,yCAAA,GAAA,GAAA,OAAA,EAAA,EAA+C,IAAA,yCAAA,GAAA,GAAA,OAAA,EAAA,EAMU,IAAA,yCAAA,IAAA,GAAA,OAAA,EAAA;AA0E3D,MAAA,uBAAA;;;AAtGqC,MAAA,oBAAA,EAAA;AAAA,MAAA,4BAAA,IAAA,eAAA,CAAA;AAE4B,MAAA,oBAAA;AAAA,MAAA,qBAAA,YAAA,IAAA,UAAA,CAAA;AAoBzD,MAAA,oBAAA,EAAA;AAAA,MAAA,qBAAA,QAAA,IAAA,UAAA,CAAA;AAMA,MAAA,oBAAA;AAAA,MAAA,qBAAA,QAAA,IAAA,MAAA,KAAA,CAAA,IAAA,UAAA,CAAA;AAQA,MAAA,oBAAA;AAAA,MAAA,qBAAA,QAAA,CAAA,IAAA,UAAA,KAAA,CAAA,IAAA,MAAA,CAAA;;oBDzCJ,cAAY,SAAA,MAAA,aACZ,cAAY,UAAA,GAAA,QAAA,CAAA,swXAAA,EAAA,CAAA;;;sEAKH,yBAAuB,CAAA;UAVnC;uBACW,uBAAqB,YACnB,MAAI,SACP;MACP;MACA;OACD,UAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;GAAA,QAAA,CAAA,2xUAAA,EAAA,CAAA;;;;6EAIU,yBAAuB,EAAA,WAAA,2BAAA,UAAA,yEAAA,YAAA,GAAA,CAAA;AAAA,GAAA;", "names": []}
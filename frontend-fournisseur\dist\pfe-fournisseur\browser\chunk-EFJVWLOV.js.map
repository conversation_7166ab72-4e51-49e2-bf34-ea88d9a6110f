{"version": 3, "sources": ["src/app/services/admin.service.ts"], "sourcesContent": ["import { Injectable } from '@angular/core';\nimport { HttpClient, HttpParams } from '@angular/common/http';\nimport { Observable, of } from 'rxjs';\nimport { tap, catchError, map } from 'rxjs/operators';\nimport { environment } from '../../environments/environment';\n\n// Interfaces pour les réponses API\nexport interface AdminResponse<T = any> {\n  success: boolean;\n  data?: T;\n  message?: string;\n}\n\nexport interface AdminListResponse<T = any> {\n  success: boolean;\n  data?: T[];\n  total?: number;\n  page?: number;\n  pageSize?: number;\n  message?: string;\n}\n\nexport interface UtilisateursPageResponse {\n  utilisateurs: UtilisateurAdmin[];\n  totalCount: number;\n  page: number;\n  pageSize: number;\n  totalPages: number;\n}\n\nexport interface ProduitAdmin {\n  id: number;\n  nom: string;\n  referenceOriginal: string;\n  referenceFournisseur?: string;\n  prixVenteTTC: number;\n  prixApresOutletTTC: number;\n  prixFinalTTC: number;\n  pourcentageRemiseTotale: number;\n  stock: number;\n  estStockCritique: boolean;\n  fournisseurNom: string;\n  categorieNom: string;\n  sousCategorieNom: string;\n  estValide: boolean;\n  estEnAvant: boolean;\n  dateCreation: string;\n  nombreVues: number;\n  nombreVentes: number;\n  noteMoyenne: number;\n  nombreAvis: number;\n  images: string[];\n  imagePrincipale?: string;\n  statutValidation: string;\n  statutStock: string;\n}\n\nexport interface ProduitsPageResponse {\n  produits: ProduitAdmin[];\n  totalCount: number;\n  page: number;\n  pageSize: number;\n  totalPages: number;\n}\n\nexport interface ProduitModerationRequest {\n  nouveauNom?: string;\n  nouvelleDescription?: string;\n  raison?: string;\n}\n\nexport interface StatistiquesAdmin {\n  nombreUtilisateurs: number;\n  nombreVentes: number;\n  nombreProduits: number;\n  nombreCommandesAnnulees: number;\n  nombreCommandes: number;\n  nombreFournisseurs: number;\n  nombreClients: number;\n}\n\nexport interface UtilisateurAdmin {\n  id: number;\n  nomComplet: string;\n  email: string;\n  role: string;\n  estActif: boolean;\n  dateInscription: Date;\n  derniereConnexion?: Date;\n}\n\n@Injectable({\n  providedIn: 'root'\n})\nexport class AdminService {\n  private readonly API_URL = `${environment.apiUrl || 'http://localhost:5014/api'}/Admin`;\n\n  constructor(private http: HttpClient) {}\n\n  // ========================================\n  // GESTION DES UTILISATEURS\n  // ========================================\n\n  /**\n   * GET /api/Admin/utilisateurs - Obtenir tous les utilisateurs avec pagination\n   */\n  getUtilisateurs(params?: {\n    page?: number;\n    pageSize?: number;\n    search?: string;\n    role?: string;\n    estActif?: boolean;\n  }): Observable<UtilisateursPageResponse> {\n    let httpParams = new HttpParams();\n\n    if (params) {\n      if (params.page) httpParams = httpParams.set('page', params.page.toString());\n      if (params.pageSize) httpParams = httpParams.set('pageSize', params.pageSize.toString());\n      if (params.search) httpParams = httpParams.set('search', params.search);\n      if (params.role) httpParams = httpParams.set('role', params.role);\n      if (params.estActif !== undefined) httpParams = httpParams.set('estActif', params.estActif.toString());\n    }\n\n    console.log('👥 Récupération des utilisateurs admin avec pagination:', params);\n\n    // Le backend retourne maintenant un objet avec pagination\n    return this.http.get<UtilisateursPageResponse>(`${this.API_URL}/utilisateurs`, { params: httpParams })\n      .pipe(\n        tap(response => console.log('✅ Utilisateurs admin récupérés avec pagination:', response)),\n        catchError(error => {\n          console.error('❌ Erreur récupération utilisateurs:', error);\n          return of({ utilisateurs: [], totalCount: 0, page: 1, pageSize: 10, totalPages: 0 }); // Retourner une réponse vide en cas d'erreur\n        })\n      );\n  }\n\n  /**\n   * PATCH /api/Admin/utilisateurs/{id}/toggle - Activer/Désactiver un utilisateur\n   */\n  toggleUtilisateur(id: number, motif?: string): Observable<any> {\n    console.log('🔄 Toggle utilisateur ID:', id);\n\n    const body = motif ? { motif } : {};\n\n    return this.http.patch<any>(`${this.API_URL}/utilisateurs/${id}/toggle`, body)\n      .pipe(\n        tap(response => console.log('✅ Utilisateur togglé:', response)),\n        catchError(error => {\n          console.error('❌ Erreur toggle utilisateur:', error);\n          throw error;\n        })\n      );\n  }\n\n  /**\n   * DELETE /api/Admin/utilisateurs/{id} - Supprimer un utilisateur\n   */\n  deleteUtilisateur(id: number): Observable<any> {\n    console.log('🗑️ Suppression utilisateur ID:', id);\n\n    return this.http.delete<any>(`${this.API_URL}/utilisateurs/${id}`)\n      .pipe(\n        tap(response => console.log('✅ Utilisateur supprimé:', response)),\n        catchError(error => {\n          console.error('❌ Erreur suppression utilisateur:', error);\n          if (error.status === 404) {\n            console.warn('⚠️ Utilisateur non trouvé (404)');\n          }\n          throw error;\n        })\n      );\n  }\n\n  // ========================================\n  // GESTION DES CATÉGORIES\n  // ========================================\n\n  /**\n   * POST /api/Admin/categories - Créer une catégorie\n   */\n  createCategorie(categorie: any): Observable<AdminResponse> {\n    console.log('➕ Création catégorie admin:', categorie);\n    \n    return this.http.post<AdminResponse>(`${this.API_URL}/categories`, categorie)\n      .pipe(\n        tap(response => console.log('✅ Catégorie créée:', response))\n      );\n  }\n\n  /**\n   * PUT /api/Admin/categories/{id} - Mettre à jour une catégorie\n   */\n  updateCategorie(id: number, categorie: any): Observable<AdminResponse> {\n    console.log('✏️ Mise à jour catégorie ID:', id);\n    \n    return this.http.put<AdminResponse>(`${this.API_URL}/categories/${id}`, categorie)\n      .pipe(\n        tap(response => console.log('✅ Catégorie mise à jour:', response))\n      );\n  }\n\n  /**\n   * DELETE /api/Admin/categories/{id} - Supprimer une catégorie\n   */\n  deleteCategorie(id: number): Observable<AdminResponse> {\n    console.log('🗑️ Suppression catégorie ID:', id);\n    \n    return this.http.delete<AdminResponse>(`${this.API_URL}/categories/${id}`)\n      .pipe(\n        tap(response => console.log('✅ Catégorie supprimée:', response))\n      );\n  }\n\n  /**\n   * PATCH /api/Admin/categories/{id}/valider - Valider une catégorie\n   */\n  validerCategorie(id: number, commentaire?: string): Observable<AdminResponse> {\n    console.log('✅ Validation catégorie ID:', id);\n    \n    const body = commentaire ? { commentaire } : {};\n    \n    return this.http.patch<AdminResponse>(`${this.API_URL}/categories/${id}/valider`, body)\n      .pipe(\n        tap(response => console.log('✅ Catégorie validée:', response))\n      );\n  }\n\n  /**\n   * PATCH /api/Admin/categories/{id}/refuser - Refuser une catégorie\n   */\n  refuserCategorie(id: number, motif: string): Observable<AdminResponse> {\n    console.log('❌ Refus catégorie ID:', id);\n    \n    return this.http.patch<AdminResponse>(`${this.API_URL}/categories/${id}/refuser`, { motif })\n      .pipe(\n        tap(response => console.log('✅ Catégorie refusée:', response))\n      );\n  }\n\n  // ========================================\n  // GESTION DES SOUS-CATÉGORIES\n  // ========================================\n\n  /**\n   * POST /api/Admin/souscategories - Créer une sous-catégorie\n   */\n  createSousCategorie(sousCategorie: any): Observable<AdminResponse> {\n    console.log('➕ Création sous-catégorie admin:', sousCategorie);\n    \n    return this.http.post<AdminResponse>(`${this.API_URL}/souscategories`, sousCategorie)\n      .pipe(\n        tap(response => console.log('✅ Sous-catégorie créée:', response))\n      );\n  }\n\n  /**\n   * PUT /api/Admin/souscategories/{id} - Mettre à jour une sous-catégorie\n   */\n  updateSousCategorie(id: number, sousCategorie: any): Observable<AdminResponse> {\n    console.log('✏️ Mise à jour sous-catégorie ID:', id);\n    \n    return this.http.put<AdminResponse>(`${this.API_URL}/souscategories/${id}`, sousCategorie)\n      .pipe(\n        tap(response => console.log('✅ Sous-catégorie mise à jour:', response))\n      );\n  }\n\n  /**\n   * DELETE /api/Admin/souscategories/{id} - Supprimer une sous-catégorie\n   */\n  deleteSousCategorie(id: number): Observable<AdminResponse> {\n    console.log('🗑️ Suppression sous-catégorie ID:', id);\n    \n    return this.http.delete<AdminResponse>(`${this.API_URL}/souscategories/${id}`)\n      .pipe(\n        tap(response => console.log('✅ Sous-catégorie supprimée:', response))\n      );\n  }\n\n  /**\n   * PATCH /api/Admin/souscategories/{id}/valider - Valider une sous-catégorie\n   */\n  validerSousCategorie(id: number, commentaire?: string): Observable<AdminResponse> {\n    console.log('✅ Validation sous-catégorie ID:', id);\n    \n    const body = commentaire ? { commentaire } : {};\n    \n    return this.http.patch<AdminResponse>(`${this.API_URL}/souscategories/${id}/valider`, body)\n      .pipe(\n        tap(response => console.log('✅ Sous-catégorie validée:', response))\n      );\n  }\n\n  /**\n   * PATCH /api/Admin/souscategories/{id}/refuser - Refuser une sous-catégorie\n   */\n  refuserSousCategorie(id: number, motif: string): Observable<AdminResponse> {\n    console.log('❌ Refus sous-catégorie ID:', id);\n    \n    return this.http.patch<AdminResponse>(`${this.API_URL}/souscategories/${id}/refuser`, { motif })\n      .pipe(\n        tap(response => console.log('✅ Sous-catégorie refusée:', response))\n      );\n  }\n\n  // ========================================\n  // GESTION DES COMMANDES\n  // ========================================\n\n  /**\n   * GET /api/Admin/commandes - Obtenir toutes les commandes\n   */\n  getCommandes(params?: {\n    page?: number;\n    pageSize?: number;\n    statut?: string;\n    dateDebut?: Date;\n    dateFin?: Date;\n  }): Observable<AdminListResponse> {\n    let httpParams = new HttpParams();\n    \n    if (params) {\n      if (params.page) httpParams = httpParams.set('page', params.page.toString());\n      if (params.pageSize) httpParams = httpParams.set('pageSize', params.pageSize.toString());\n      if (params.statut) httpParams = httpParams.set('statut', params.statut);\n      if (params.dateDebut) httpParams = httpParams.set('dateDebut', params.dateDebut.toISOString());\n      if (params.dateFin) httpParams = httpParams.set('dateFin', params.dateFin.toISOString());\n    }\n\n    console.log('📋 Récupération des commandes admin:', params);\n    \n    return this.http.get<AdminListResponse>(`${this.API_URL}/commandes`, { params: httpParams })\n      .pipe(\n        tap(response => console.log('✅ Commandes admin récupérées:', response))\n      );\n  }\n\n  /**\n   * PATCH /api/Admin/commandes/{id}/annuler - Annuler une commande\n   */\n  annulerCommande(id: number, motif: string): Observable<AdminResponse> {\n    console.log('❌ Annulation commande ID:', id);\n    \n    return this.http.patch<AdminResponse>(`${this.API_URL}/commandes/${id}/annuler`, { motif })\n      .pipe(\n        tap(response => console.log('✅ Commande annulée:', response))\n      );\n  }\n\n  // ========================================\n  // GESTION DES PRODUITS\n  // ========================================\n\n  /**\n   * GET /api/Admin/produits - Obtenir tous les produits avec pagination\n   */\n  getProduits(params?: {\n    page?: number;\n    pageSize?: number;\n    search?: string;\n    statut?: string;\n    stockCritique?: boolean;\n    fournisseur?: string;\n  }): Observable<ProduitsPageResponse> {\n    let httpParams = new HttpParams();\n\n    if (params) {\n      if (params.page) httpParams = httpParams.set('page', params.page.toString());\n      if (params.pageSize) httpParams = httpParams.set('pageSize', params.pageSize.toString());\n      if (params.search) httpParams = httpParams.set('search', params.search);\n      if (params.statut) httpParams = httpParams.set('statut', params.statut);\n      if (params.stockCritique !== undefined) httpParams = httpParams.set('stockCritique', params.stockCritique.toString());\n      if (params.fournisseur) httpParams = httpParams.set('fournisseur', params.fournisseur);\n    }\n\n    console.log('📦 Récupération des produits admin avec pagination:', params);\n\n    return this.http.get<ProduitsPageResponse>(`${this.API_URL}/produits`, { params: httpParams })\n      .pipe(\n        tap(response => console.log('✅ Produits admin récupérés avec pagination:', response)),\n        catchError(error => {\n          console.error('❌ Erreur récupération produits:', error);\n          return of({ produits: [], totalCount: 0, page: 1, pageSize: 10, totalPages: 0 });\n        })\n      );\n  }\n\n  /**\n   * PATCH /api/Admin/produits/{id}/valider - Valider un produit\n   */\n  validerProduit(id: number): Observable<boolean> {\n    console.log('✅ Validation produit admin:', id);\n\n    return this.http.patch<AdminResponse>(`${this.API_URL}/produits/${id}/valider`, {})\n      .pipe(\n        map(response => true), // Le backend retourne 200 OK en cas de succès\n        tap(success => console.log('✅ Produit validé:', success)),\n        catchError(error => {\n          console.error('❌ Erreur validation produit:', error);\n          return of(false);\n        })\n      );\n  }\n\n  /**\n   * PATCH /api/Admin/produits/{id}/refuser - Refuser un produit\n   */\n  refuserProduit(id: number, raison?: string): Observable<boolean> {\n    console.log('❌ Refus produit admin:', id, raison);\n\n    return this.http.patch<AdminResponse>(`${this.API_URL}/produits/${id}/refuser`, raison || '')\n      .pipe(\n        map(response => true),\n        tap(success => console.log('✅ Produit refusé:', success)),\n        catchError(error => {\n          console.error('❌ Erreur refus produit:', error);\n          return of(false);\n        })\n      );\n  }\n\n  /**\n   * PATCH /api/Admin/produits/{produitId}/enavant - Mettre un produit en avant\n   */\n  mettreEnAvantProduit(produitId: number, enAvant: boolean = true): Observable<AdminResponse> {\n    console.log('⭐ Mise en avant produit ID:', produitId);\n\n    return this.http.patch<AdminResponse>(`${this.API_URL}/produits/${produitId}/enavant`, { enAvant })\n      .pipe(\n        tap(response => console.log('✅ Produit mis en avant:', response))\n      );\n  }\n\n  /**\n   * PATCH /api/Admin/produits/{id}/retirer-avant - Retirer la mise en avant d'un produit\n   */\n  retirerMiseEnAvantProduit(id: number): Observable<boolean> {\n    console.log('⭐ Retrait mise en avant produit admin:', id);\n\n    return this.http.patch<AdminResponse>(`${this.API_URL}/produits/${id}/retirer-avant`, {})\n      .pipe(\n        map(response => true),\n        tap(success => console.log('✅ Mise en avant retirée:', success)),\n        catchError(error => {\n          console.error('❌ Erreur retrait mise en avant:', error);\n          return of(false);\n        })\n      );\n  }\n\n  /**\n   * PUT /api/Admin/produits/{id}/moderer - Modérer le contenu d'un produit\n   */\n  modererContenuProduit(id: number, moderation: ProduitModerationRequest): Observable<boolean> {\n    console.log('🔍 Modération contenu produit admin:', id, moderation);\n\n    return this.http.put<AdminResponse>(`${this.API_URL}/produits/${id}/moderer`, moderation)\n      .pipe(\n        map(response => true),\n        tap(success => console.log('✅ Contenu produit modéré:', success)),\n        catchError(error => {\n          console.error('❌ Erreur modération contenu:', error);\n          return of(false);\n        })\n      );\n  }\n\n  /**\n   * DELETE /api/Admin/produits/{id} - Supprimer un produit\n   */\n  supprimerProduit(id: number): Observable<boolean> {\n    console.log('🗑️ Suppression produit admin:', id);\n\n    return this.http.delete<AdminResponse>(`${this.API_URL}/produits/${id}`)\n      .pipe(\n        map(response => true),\n        tap(success => console.log('✅ Produit supprimé:', success)),\n        catchError(error => {\n          console.error('❌ Erreur suppression produit:', error);\n          return of(false);\n        })\n      );\n  }\n\n  /**\n   * GET /api/Admin/produits/stock-critique - Obtenir les produits en stock critique\n   */\n  getProduitsStockCritique(seuil: number = 10): Observable<ProduitAdmin[]> {\n    console.log('⚠️ Récupération produits stock critique, seuil:', seuil);\n\n    return this.http.get<ProduitAdmin[]>(`${this.API_URL}/produits/stock-critique?seuil=${seuil}`)\n      .pipe(\n        tap(response => console.log('✅ Produits stock critique récupérés:', response)),\n        catchError(error => {\n          console.error('❌ Erreur récupération stock critique:', error);\n          return of([]);\n        })\n      );\n  }\n\n  /**\n   * PATCH /api/Admin/produits/{id}/stock - Mettre à jour le stock d'un produit\n   */\n  updateStockProduit(id: number, nouveauStock: number): Observable<boolean> {\n    console.log('📦 Mise à jour stock produit admin:', id, nouveauStock);\n\n    return this.http.patch<AdminResponse>(`${this.API_URL}/produits/${id}/stock`, nouveauStock)\n      .pipe(\n        map(response => true),\n        tap(success => console.log('✅ Stock produit mis à jour:', success)),\n        catchError(error => {\n          console.error('❌ Erreur mise à jour stock:', error);\n          return of(false);\n        })\n      );\n  }\n\n  /**\n   * GET /api/Admin/produits/en-avant - Obtenir les produits mis en avant\n   */\n  getProduitsEnAvant(): Observable<ProduitAdmin[]> {\n    console.log('⭐ Récupération produits en avant');\n\n    return this.http.get<ProduitAdmin[]>(`${this.API_URL}/produits/en-avant`)\n      .pipe(\n        tap(response => console.log('✅ Produits en avant récupérés:', response)),\n        catchError(error => {\n          console.error('❌ Erreur récupération produits en avant:', error);\n          return of([]);\n        })\n      );\n  }\n\n  /**\n   * GET /api/Admin/statistiques - Obtenir les statistiques admin\n   */\n  getStatistiques(): Observable<StatistiquesAdmin> {\n    console.log('📊 Récupération des statistiques admin');\n\n    // Le backend retourne directement l'objet statistiques\n    return this.http.get<StatistiquesAdmin>(`${this.API_URL}/statistiques`)\n      .pipe(\n        tap(response => console.log('✅ Statistiques admin récupérées:', response)),\n        catchError(error => {\n          console.error('❌ Erreur récupération statistiques:', error);\n          // Retourner des statistiques par défaut en cas d'erreur\n          return of({\n            nombreUtilisateurs: 0,\n            nombreVentes: 0,\n            nombreProduits: 0,\n            nombreCommandesAnnulees: 0,\n            nombreCommandes: 0,\n            nombreFournisseurs: 0,\n            nombreClients: 0\n          });\n        })\n      );\n  }\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;;AA8FM,IAAO,eAAP,MAAO,cAAY;EAGH;EAFH,UAAU,GAAG,YAAY,UAAU,2BAA2B;EAE/E,YAAoB,MAAgB;AAAhB,SAAA,OAAA;EAAmB;;;;;;;EASvC,gBAAgB,QAMf;AACC,QAAI,aAAa,IAAI,WAAU;AAE/B,QAAI,QAAQ;AACV,UAAI,OAAO;AAAM,qBAAa,WAAW,IAAI,QAAQ,OAAO,KAAK,SAAQ,CAAE;AAC3E,UAAI,OAAO;AAAU,qBAAa,WAAW,IAAI,YAAY,OAAO,SAAS,SAAQ,CAAE;AACvF,UAAI,OAAO;AAAQ,qBAAa,WAAW,IAAI,UAAU,OAAO,MAAM;AACtE,UAAI,OAAO;AAAM,qBAAa,WAAW,IAAI,QAAQ,OAAO,IAAI;AAChE,UAAI,OAAO,aAAa;AAAW,qBAAa,WAAW,IAAI,YAAY,OAAO,SAAS,SAAQ,CAAE;IACvG;AAEA,YAAQ,IAAI,wEAA2D,MAAM;AAG7E,WAAO,KAAK,KAAK,IAA8B,GAAG,KAAK,OAAO,iBAAiB,EAAE,QAAQ,WAAU,CAAE,EAClG,KACC,IAAI,cAAY,QAAQ,IAAI,iEAAmD,QAAQ,CAAC,GACxF,WAAW,WAAQ;AACjB,cAAQ,MAAM,kDAAuC,KAAK;AAC1D,aAAO,GAAG,EAAE,cAAc,CAAA,GAAI,YAAY,GAAG,MAAM,GAAG,UAAU,IAAI,YAAY,EAAC,CAAE;IACrF,CAAC,CAAC;EAER;;;;EAKA,kBAAkB,IAAY,OAAc;AAC1C,YAAQ,IAAI,oCAA6B,EAAE;AAE3C,UAAM,OAAO,QAAQ,EAAE,MAAK,IAAK,CAAA;AAEjC,WAAO,KAAK,KAAK,MAAW,GAAG,KAAK,OAAO,iBAAiB,EAAE,WAAW,IAAI,EAC1E,KACC,IAAI,cAAY,QAAQ,IAAI,iCAAyB,QAAQ,CAAC,GAC9D,WAAW,WAAQ;AACjB,cAAQ,MAAM,qCAAgC,KAAK;AACnD,YAAM;IACR,CAAC,CAAC;EAER;;;;EAKA,kBAAkB,IAAU;AAC1B,YAAQ,IAAI,+CAAmC,EAAE;AAEjD,WAAO,KAAK,KAAK,OAAY,GAAG,KAAK,OAAO,iBAAiB,EAAE,EAAE,EAC9D,KACC,IAAI,cAAY,QAAQ,IAAI,mCAA2B,QAAQ,CAAC,GAChE,WAAW,WAAQ;AACjB,cAAQ,MAAM,0CAAqC,KAAK;AACxD,UAAI,MAAM,WAAW,KAAK;AACxB,gBAAQ,KAAK,8CAAiC;MAChD;AACA,YAAM;IACR,CAAC,CAAC;EAER;;;;;;;EASA,gBAAgB,WAAc;AAC5B,YAAQ,IAAI,0CAA+B,SAAS;AAEpD,WAAO,KAAK,KAAK,KAAoB,GAAG,KAAK,OAAO,eAAe,SAAS,EACzE,KACC,IAAI,cAAY,QAAQ,IAAI,oCAAsB,QAAQ,CAAC,CAAC;EAElE;;;;EAKA,gBAAgB,IAAY,WAAc;AACxC,YAAQ,IAAI,gDAAgC,EAAE;AAE9C,WAAO,KAAK,KAAK,IAAmB,GAAG,KAAK,OAAO,eAAe,EAAE,IAAI,SAAS,EAC9E,KACC,IAAI,cAAY,QAAQ,IAAI,uCAA4B,QAAQ,CAAC,CAAC;EAExE;;;;EAKA,gBAAgB,IAAU;AACxB,YAAQ,IAAI,gDAAiC,EAAE;AAE/C,WAAO,KAAK,KAAK,OAAsB,GAAG,KAAK,OAAO,eAAe,EAAE,EAAE,EACtE,KACC,IAAI,cAAY,QAAQ,IAAI,qCAA0B,QAAQ,CAAC,CAAC;EAEtE;;;;EAKA,iBAAiB,IAAY,aAAoB;AAC/C,YAAQ,IAAI,sCAA8B,EAAE;AAE5C,UAAM,OAAO,cAAc,EAAE,YAAW,IAAK,CAAA;AAE7C,WAAO,KAAK,KAAK,MAAqB,GAAG,KAAK,OAAO,eAAe,EAAE,YAAY,IAAI,EACnF,KACC,IAAI,cAAY,QAAQ,IAAI,mCAAwB,QAAQ,CAAC,CAAC;EAEpE;;;;EAKA,iBAAiB,IAAY,OAAa;AACxC,YAAQ,IAAI,iCAAyB,EAAE;AAEvC,WAAO,KAAK,KAAK,MAAqB,GAAG,KAAK,OAAO,eAAe,EAAE,YAAY,EAAE,MAAK,CAAE,EACxF,KACC,IAAI,cAAY,QAAQ,IAAI,mCAAwB,QAAQ,CAAC,CAAC;EAEpE;;;;;;;EASA,oBAAoB,eAAkB;AACpC,YAAQ,IAAI,+CAAoC,aAAa;AAE7D,WAAO,KAAK,KAAK,KAAoB,GAAG,KAAK,OAAO,mBAAmB,aAAa,EACjF,KACC,IAAI,cAAY,QAAQ,IAAI,yCAA2B,QAAQ,CAAC,CAAC;EAEvE;;;;EAKA,oBAAoB,IAAY,eAAkB;AAChD,YAAQ,IAAI,qDAAqC,EAAE;AAEnD,WAAO,KAAK,KAAK,IAAmB,GAAG,KAAK,OAAO,mBAAmB,EAAE,IAAI,aAAa,EACtF,KACC,IAAI,cAAY,QAAQ,IAAI,4CAAiC,QAAQ,CAAC,CAAC;EAE7E;;;;EAKA,oBAAoB,IAAU;AAC5B,YAAQ,IAAI,qDAAsC,EAAE;AAEpD,WAAO,KAAK,KAAK,OAAsB,GAAG,KAAK,OAAO,mBAAmB,EAAE,EAAE,EAC1E,KACC,IAAI,cAAY,QAAQ,IAAI,0CAA+B,QAAQ,CAAC,CAAC;EAE3E;;;;EAKA,qBAAqB,IAAY,aAAoB;AACnD,YAAQ,IAAI,2CAAmC,EAAE;AAEjD,UAAM,OAAO,cAAc,EAAE,YAAW,IAAK,CAAA;AAE7C,WAAO,KAAK,KAAK,MAAqB,GAAG,KAAK,OAAO,mBAAmB,EAAE,YAAY,IAAI,EACvF,KACC,IAAI,cAAY,QAAQ,IAAI,wCAA6B,QAAQ,CAAC,CAAC;EAEzE;;;;EAKA,qBAAqB,IAAY,OAAa;AAC5C,YAAQ,IAAI,sCAA8B,EAAE;AAE5C,WAAO,KAAK,KAAK,MAAqB,GAAG,KAAK,OAAO,mBAAmB,EAAE,YAAY,EAAE,MAAK,CAAE,EAC5F,KACC,IAAI,cAAY,QAAQ,IAAI,wCAA6B,QAAQ,CAAC,CAAC;EAEzE;;;;;;;EASA,aAAa,QAMZ;AACC,QAAI,aAAa,IAAI,WAAU;AAE/B,QAAI,QAAQ;AACV,UAAI,OAAO;AAAM,qBAAa,WAAW,IAAI,QAAQ,OAAO,KAAK,SAAQ,CAAE;AAC3E,UAAI,OAAO;AAAU,qBAAa,WAAW,IAAI,YAAY,OAAO,SAAS,SAAQ,CAAE;AACvF,UAAI,OAAO;AAAQ,qBAAa,WAAW,IAAI,UAAU,OAAO,MAAM;AACtE,UAAI,OAAO;AAAW,qBAAa,WAAW,IAAI,aAAa,OAAO,UAAU,YAAW,CAAE;AAC7F,UAAI,OAAO;AAAS,qBAAa,WAAW,IAAI,WAAW,OAAO,QAAQ,YAAW,CAAE;IACzF;AAEA,YAAQ,IAAI,qDAAwC,MAAM;AAE1D,WAAO,KAAK,KAAK,IAAuB,GAAG,KAAK,OAAO,cAAc,EAAE,QAAQ,WAAU,CAAE,EACxF,KACC,IAAI,cAAY,QAAQ,IAAI,+CAAiC,QAAQ,CAAC,CAAC;EAE7E;;;;EAKA,gBAAgB,IAAY,OAAa;AACvC,YAAQ,IAAI,kCAA6B,EAAE;AAE3C,WAAO,KAAK,KAAK,MAAqB,GAAG,KAAK,OAAO,cAAc,EAAE,YAAY,EAAE,MAAK,CAAE,EACvF,KACC,IAAI,cAAY,QAAQ,IAAI,+BAAuB,QAAQ,CAAC,CAAC;EAEnE;;;;;;;EASA,YAAY,QAOX;AACC,QAAI,aAAa,IAAI,WAAU;AAE/B,QAAI,QAAQ;AACV,UAAI,OAAO;AAAM,qBAAa,WAAW,IAAI,QAAQ,OAAO,KAAK,SAAQ,CAAE;AAC3E,UAAI,OAAO;AAAU,qBAAa,WAAW,IAAI,YAAY,OAAO,SAAS,SAAQ,CAAE;AACvF,UAAI,OAAO;AAAQ,qBAAa,WAAW,IAAI,UAAU,OAAO,MAAM;AACtE,UAAI,OAAO;AAAQ,qBAAa,WAAW,IAAI,UAAU,OAAO,MAAM;AACtE,UAAI,OAAO,kBAAkB;AAAW,qBAAa,WAAW,IAAI,iBAAiB,OAAO,cAAc,SAAQ,CAAE;AACpH,UAAI,OAAO;AAAa,qBAAa,WAAW,IAAI,eAAe,OAAO,WAAW;IACvF;AAEA,YAAQ,IAAI,oEAAuD,MAAM;AAEzE,WAAO,KAAK,KAAK,IAA0B,GAAG,KAAK,OAAO,aAAa,EAAE,QAAQ,WAAU,CAAE,EAC1F,KACC,IAAI,cAAY,QAAQ,IAAI,6DAA+C,QAAQ,CAAC,GACpF,WAAW,WAAQ;AACjB,cAAQ,MAAM,8CAAmC,KAAK;AACtD,aAAO,GAAG,EAAE,UAAU,CAAA,GAAI,YAAY,GAAG,MAAM,GAAG,UAAU,IAAI,YAAY,EAAC,CAAE;IACjF,CAAC,CAAC;EAER;;;;EAKA,eAAe,IAAU;AACvB,YAAQ,IAAI,oCAA+B,EAAE;AAE7C,WAAO,KAAK,KAAK,MAAqB,GAAG,KAAK,OAAO,aAAa,EAAE,YAAY,CAAA,CAAE,EAC/E;MACC,IAAI,cAAY,IAAI;;MACpB,IAAI,aAAW,QAAQ,IAAI,6BAAqB,OAAO,CAAC;MACxD,WAAW,WAAQ;AACjB,gBAAQ,MAAM,qCAAgC,KAAK;AACnD,eAAO,GAAG,KAAK;MACjB,CAAC;IAAC;EAER;;;;EAKA,eAAe,IAAY,QAAe;AACxC,YAAQ,IAAI,+BAA0B,IAAI,MAAM;AAEhD,WAAO,KAAK,KAAK,MAAqB,GAAG,KAAK,OAAO,aAAa,EAAE,YAAY,UAAU,EAAE,EACzF,KACC,IAAI,cAAY,IAAI,GACpB,IAAI,aAAW,QAAQ,IAAI,6BAAqB,OAAO,CAAC,GACxD,WAAW,WAAQ;AACjB,cAAQ,MAAM,gCAA2B,KAAK;AAC9C,aAAO,GAAG,KAAK;IACjB,CAAC,CAAC;EAER;;;;EAKA,qBAAqB,WAAmB,UAAmB,MAAI;AAC7D,YAAQ,IAAI,oCAA+B,SAAS;AAEpD,WAAO,KAAK,KAAK,MAAqB,GAAG,KAAK,OAAO,aAAa,SAAS,YAAY,EAAE,QAAO,CAAE,EAC/F,KACC,IAAI,cAAY,QAAQ,IAAI,gCAA2B,QAAQ,CAAC,CAAC;EAEvE;;;;EAKA,0BAA0B,IAAU;AAClC,YAAQ,IAAI,+CAA0C,EAAE;AAExD,WAAO,KAAK,KAAK,MAAqB,GAAG,KAAK,OAAO,aAAa,EAAE,kBAAkB,CAAA,CAAE,EACrF,KACC,IAAI,cAAY,IAAI,GACpB,IAAI,aAAW,QAAQ,IAAI,oCAA4B,OAAO,CAAC,GAC/D,WAAW,WAAQ;AACjB,cAAQ,MAAM,wCAAmC,KAAK;AACtD,aAAO,GAAG,KAAK;IACjB,CAAC,CAAC;EAER;;;;EAKA,sBAAsB,IAAY,YAAoC;AACpE,YAAQ,IAAI,kDAAwC,IAAI,UAAU;AAElE,WAAO,KAAK,KAAK,IAAmB,GAAG,KAAK,OAAO,aAAa,EAAE,YAAY,UAAU,EACrF,KACC,IAAI,cAAY,IAAI,GACpB,IAAI,aAAW,QAAQ,IAAI,wCAA6B,OAAO,CAAC,GAChE,WAAW,WAAQ;AACjB,cAAQ,MAAM,wCAAgC,KAAK;AACnD,aAAO,GAAG,KAAK;IACjB,CAAC,CAAC;EAER;;;;EAKA,iBAAiB,IAAU;AACzB,YAAQ,IAAI,8CAAkC,EAAE;AAEhD,WAAO,KAAK,KAAK,OAAsB,GAAG,KAAK,OAAO,aAAa,EAAE,EAAE,EACpE,KACC,IAAI,cAAY,IAAI,GACpB,IAAI,aAAW,QAAQ,IAAI,+BAAuB,OAAO,CAAC,GAC1D,WAAW,WAAQ;AACjB,cAAQ,MAAM,sCAAiC,KAAK;AACpD,aAAO,GAAG,KAAK;IACjB,CAAC,CAAC;EAER;;;;EAKA,yBAAyB,QAAgB,IAAE;AACzC,YAAQ,IAAI,mEAAmD,KAAK;AAEpE,WAAO,KAAK,KAAK,IAAoB,GAAG,KAAK,OAAO,kCAAkC,KAAK,EAAE,EAC1F,KACC,IAAI,cAAY,QAAQ,IAAI,sDAAwC,QAAQ,CAAC,GAC7E,WAAW,WAAQ;AACjB,cAAQ,MAAM,oDAAyC,KAAK;AAC5D,aAAO,GAAG,CAAA,CAAE;IACd,CAAC,CAAC;EAER;;;;EAKA,mBAAmB,IAAY,cAAoB;AACjD,YAAQ,IAAI,iDAAuC,IAAI,YAAY;AAEnE,WAAO,KAAK,KAAK,MAAqB,GAAG,KAAK,OAAO,aAAa,EAAE,UAAU,YAAY,EACvF,KACC,IAAI,cAAY,IAAI,GACpB,IAAI,aAAW,QAAQ,IAAI,uCAA+B,OAAO,CAAC,GAClE,WAAW,WAAQ;AACjB,cAAQ,MAAM,uCAA+B,KAAK;AAClD,aAAO,GAAG,KAAK;IACjB,CAAC,CAAC;EAER;;;;EAKA,qBAAkB;AAChB,YAAQ,IAAI,6CAAkC;AAE9C,WAAO,KAAK,KAAK,IAAoB,GAAG,KAAK,OAAO,oBAAoB,EACrE,KACC,IAAI,cAAY,QAAQ,IAAI,gDAAkC,QAAQ,CAAC,GACvE,WAAW,WAAQ;AACjB,cAAQ,MAAM,uDAA4C,KAAK;AAC/D,aAAO,GAAG,CAAA,CAAE;IACd,CAAC,CAAC;EAER;;;;EAKA,kBAAe;AACb,YAAQ,IAAI,qDAAwC;AAGpD,WAAO,KAAK,KAAK,IAAuB,GAAG,KAAK,OAAO,eAAe,EACnE,KACC,IAAI,cAAY,QAAQ,IAAI,kDAAoC,QAAQ,CAAC,GACzE,WAAW,WAAQ;AACjB,cAAQ,MAAM,kDAAuC,KAAK;AAE1D,aAAO,GAAG;QACR,oBAAoB;QACpB,cAAc;QACd,gBAAgB;QAChB,yBAAyB;QACzB,iBAAiB;QACjB,oBAAoB;QACpB,eAAe;OAChB;IACH,CAAC,CAAC;EAER;;qCA/cW,eAAY,mBAAA,UAAA,CAAA;EAAA;4EAAZ,eAAY,SAAZ,cAAY,WAAA,YAFX,OAAM,CAAA;;;sEAEP,cAAY,CAAA;UAHxB;WAAW;MACV,YAAY;KACb;;;", "names": []}
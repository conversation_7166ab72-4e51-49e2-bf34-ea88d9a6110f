{"version": 3, "sources": ["src/app/components/admin/order-management/order-management.component.ts", "src/app/components/admin/order-management/order-management.component.html"], "sourcesContent": ["import { Component, OnInit, signal, computed } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { FormsModule } from '@angular/forms';\nimport { AdminService } from '../../../services/admin.service';\n\n\n\ninterface CommandeAvecFournisseur {\n  id: number;\n  numeroCommande: string;\n  clientNom: string;\n  clientEmail: string;\n  montantTotal: number;\n  statut: string;\n  dateCommande: string;\n  nombreArticles: number;\n  commandesFournisseur: CommandeFournisseurDetail[];\n}\n\ninterface CommandeFournisseurDetail {\n  id: number;\n  reference: string;\n  fournisseurId: number;\n  fournisseurNom: string;\n  fournisseurEmail: string;\n  montantTotal: number;\n  statut: string;\n  dateCreation: string;\n  dateLivraison?: string;\n  numeroBonLivraison?: string;\n  fraisLivraison: number;\n  lignes: LigneCommandeFournisseurDetail[];\n}\n\ninterface LigneCommandeFournisseurDetail {\n  id: number;\n  produitId: number;\n  produitNom: string;\n  referenceProduit: string;\n  quantite: number;\n  prixUnitaire: number;\n  montantLigne: number;\n}\n\n@Component({\n  selector: 'app-order-management',\n  standalone: true,\n  imports: [CommonModule, FormsModule],\n  templateUrl: './order-management.component.html',\n  styleUrls: ['./order-management.component.css']\n})\nexport class OrderManagementComponent implements OnInit {\n  // Angular 19: Signals\n  commandesAvecFournisseur = signal<CommandeAvecFournisseur[]>([]);\n  isLoading = signal(false);\n  error = signal('');\n  searchTerm = signal('');\n  selectedStatus = signal('');\n  dateDebut = signal('');\n  dateFin = signal('');\n  currentPage = signal(1);\n  pageSize = signal(10);\n  totalItems = signal(0);\n  expandedOrderId = signal<number | null>(null);\n\n\n\n  // Computed signals pour les commandes filtrées\n  filteredCommandesAvecFournisseur = computed(() => {\n    const orders = this.commandesAvecFournisseur();\n    const search = this.searchTerm().toLowerCase();\n    const status = this.selectedStatus();\n    const dateDebut = this.dateDebut();\n    const dateFin = this.dateFin();\n\n    return orders.filter(order => {\n      const matchesSearch = !search || \n        order.numeroCommande.toLowerCase().includes(search) ||\n        order.clientNom.toLowerCase().includes(search) ||\n        order.clientEmail.toLowerCase().includes(search);\n\n      const matchesStatus = !status || order.statut === status;\n\n      let matchesDate = true;\n      if (dateDebut || dateFin) {\n        const orderDate = new Date(order.dateCommande);\n        if (dateDebut) {\n          const startDate = new Date(dateDebut);\n          matchesDate = matchesDate && orderDate >= startDate;\n        }\n        if (dateFin) {\n          const endDate = new Date(dateFin);\n          endDate.setHours(23, 59, 59, 999);\n          matchesDate = matchesDate && orderDate <= endDate;\n        }\n      }\n\n      return matchesSearch && matchesStatus && matchesDate;\n    });\n  });\n\n  statuts = ['EnAttente', 'Confirmee', 'EnPreparation', 'Expediee', 'Livree', 'Annulee'];\n\n  constructor(private adminService: AdminService) {}\n\n  ngOnInit(): void {\n    this.loadCommandes();\n  }\n\n  loadCommandes(): void {\n    this.isLoading.set(true);\n    this.error.set('');\n\n    // Charger les commandes avec les paramètres de pagination et filtres\n    const params = {\n      page: this.currentPage(),\n      pageSize: this.pageSize(),\n      statut: this.selectedStatus() || undefined,\n      dateDebut: this.dateDebut() ? new Date(this.dateDebut()) : undefined,\n      dateFin: this.dateFin() ? new Date(this.dateFin()) : undefined\n    };\n\n    this.adminService.getCommandes(params).subscribe({\n      next: (response: any) => {\n        if (response.success && response.data) {\n          // Convertir les commandes simples en format avec fournisseur pour compatibilité\n          const commandesAvecFournisseur = response.data.map((commande: any) => ({\n            ...commande,\n            fournisseurNom: commande.fournisseurNom || 'Non spécifié',\n            commandesFournisseur: commande.commandesFournisseur || []\n          }));\n          this.commandesAvecFournisseur.set(commandesAvecFournisseur);\n          this.totalItems.set(response.total || response.data.length);\n        } else {\n          this.commandesAvecFournisseur.set([]);\n          this.totalItems.set(0);\n        }\n        this.isLoading.set(false);\n      },\n      error: (error: any) => {\n        console.error('Erreur lors du chargement des commandes:', error);\n        this.error.set('Erreur lors du chargement des commandes');\n        this.isLoading.set(false);\n      }\n    });\n  }\n\n  // Méthodes simplifiées - plus besoin de basculer entre les vues\n  toggleDetailedView(): void {\n    // Ne fait plus rien car on reste toujours en vue détaillée\n  }\n\n  // Method called by template - alias for toggleDetailedView\n  toggleView(): void {\n    // Ne fait plus rien car on reste toujours en vue détaillée\n  }\n\n  onSearch(): void {\n    // Le signal se met à jour automatiquement\n  }\n\n  onFilterChange(): void {\n    // Le signal se met à jour automatiquement\n  }\n\n  onDateFilterChange(): void {\n    // Le signal se met à jour automatiquement\n  }\n\n  clearFilters(): void {\n    this.searchTerm.set('');\n    this.selectedStatus.set('');\n    this.dateDebut.set('');\n    this.dateFin.set('');\n  }\n\n  refresh(): void {\n    this.currentPage.set(1);\n    this.loadCommandes();\n  }\n\n  viewOrderDetails(commande: CommandeAvecFournisseur): void {\n    // Basculer l'expansion/contraction des détails de la commande\n    const currentExpanded = this.expandedOrderId();\n    this.expandedOrderId.set(currentExpanded === commande.id ? null : commande.id);\n  }\n\n  isOrderExpanded(orderId: number): boolean {\n    return this.expandedOrderId() === orderId;\n  }\n\n  getTotalCommandes(): number {\n    return this.filteredCommandesAvecFournisseur().length;\n  }\n\n  getTotalAmount(): string {\n    const total = this.filteredCommandesAvecFournisseur().reduce((sum, c) => sum + c.montantTotal, 0);\n    return this.formatPrice(total);\n  }\n\n  exportOrders(): void {\n    alert('Fonctionnalité d\\'export en cours de développement');\n  }\n\n  formatPrice(price: number): string {\n    return new Intl.NumberFormat('fr-TN', {\n      style: 'currency',\n      currency: 'TND'\n    }).format(price);\n  }\n\n  formatDate(date: string): string {\n    return new Date(date).toLocaleDateString('fr-FR', {\n      year: 'numeric',\n      month: 'short',\n      day: 'numeric',\n      hour: '2-digit',\n      minute: '2-digit'\n    });\n  }\n\n  getStatusText(statut: string): string {\n    switch (statut) {\n      case 'EnAttente': return 'En attente';\n      case 'Confirmee': return 'Confirmée';\n      case 'EnPreparation': return 'En préparation';\n      case 'Expediee': return 'Expédiée';\n      case 'Livree': return 'Livrée';\n      case 'Annulee': return 'Annulée';\n      default: return statut;\n    }\n  }\n\n  getStatusClass(statut: string): string {\n    switch (statut) {\n      case 'EnAttente': return 'status-pending';\n      case 'Confirmee': return 'status-confirmed';\n      case 'EnPreparation': return 'status-preparing';\n      case 'Expediee': return 'status-shipped';\n      case 'Livree': return 'status-delivered';\n      case 'Annulee': return 'status-cancelled';\n      default: return 'status-unknown';\n    }\n  }\n\n  getCommandeFournisseurStatusText(statut: string): string {\n    switch (statut) {\n      case 'EnAttente': return 'En attente';\n      case 'Confirmee': return 'Confirmée';\n      case 'EnPreparation': return 'En préparation';\n      case 'Expediee': return 'Expédiée';\n      case 'Livree': return 'Livrée';\n      case 'Annulee': return 'Annulée';\n      default: return statut;\n    }\n  }\n\n  getCommandeFournisseurStatusClass(statut: string): string {\n    switch (statut) {\n      case 'EnAttente': return 'status-pending';\n      case 'Confirmee': return 'status-confirmed';\n      case 'EnPreparation': return 'status-preparing';\n      case 'Expediee': return 'status-shipped';\n      case 'Livree': return 'status-delivered';\n      case 'Annulee': return 'status-cancelled';\n      default: return 'status-unknown';\n    }\n  }\n\n  canCancelOrder(statut: string): boolean {\n    return ['EnAttente', 'Confirmee'].includes(statut);\n  }\n\n  annulerCommande(commande: CommandeAvecFournisseur): void {\n    console.log('Annuler commande ID:', commande.id);\n  }\n\n  // Pagination methods\n  totalPages = computed(() => {\n    return Math.ceil(this.totalItems() / this.pageSize());\n  });\n\n  onPageChange(page: number): void {\n    if (page >= 1 && page <= this.totalPages()) {\n      this.currentPage.set(page);\n      this.loadCommandes();\n    }\n  }\n\n  // Math object for template\n  Math = Math;\n}\n", "<div class=\"order-management-container\">\n  <!-- Header -->\n  <div class=\"page-header\">\n    <div class=\"header-content\">\n      <h1 class=\"page-title\">\n        <i class=\"icon-shopping-cart\"></i>\n        Gestion des Commandes\n      </h1>\n      <div class=\"header-actions\">\n        <button class=\"btn btn-secondary\" (click)=\"clearFilters()\">\n          <i class=\"icon-filter-x\"></i>\n          Effacer filtres\n        </button>\n        <button class=\"btn btn-secondary\" (click)=\"refresh()\" [disabled]=\"isLoading()\">\n          <i class=\"icon-refresh\"></i>\n          Actualiser\n        </button>\n      </div>\n    </div>\n  </div>\n\n  <!-- Filtres et Recherche -->\n  <div class=\"filters-section\">\n    <div class=\"search-box\">\n      <i class=\"icon-search\"></i>\n      <input\n        type=\"text\"\n        placeholder=\"Rechercher par numéro, client...\"\n        [value]=\"searchTerm()\"\n        (input)=\"searchTerm.set($any($event.target).value)\"\n        class=\"search-input\">\n    </div>\n\n    <div class=\"filters\">\n      <select [value]=\"selectedStatus()\" (change)=\"selectedStatus.set($any($event.target).value)\" class=\"filter-select\">\n        <option value=\"\">Tous les statuts</option>\n        <option *ngFor=\"let statut of statuts\" [value]=\"statut\">{{ getStatusText(statut) }}</option>\n      </select>\n\n      <input\n        type=\"date\"\n        [value]=\"dateDebut()\"\n        (change)=\"dateDebut.set($any($event.target).value)\"\n        class=\"filter-date\"\n        placeholder=\"Date début\">\n\n      <input\n        type=\"date\"\n        [value]=\"dateFin()\"\n        (change)=\"dateFin.set($any($event.target).value)\"\n        class=\"filter-date\"\n        placeholder=\"Date fin\">\n    </div>\n  </div>\n\n  <!-- Message d'erreur -->\n  <div *ngIf=\"error()\" class=\"error-message\">\n    <i class=\"icon-alert\"></i>\n    {{ error() }}\n  </div>\n\n  <!-- Loading -->\n  <div *ngIf=\"isLoading()\" class=\"loading-container\">\n    <div class=\"loading-spinner\"></div>\n    <p>Chargement des commandes...</p>\n  </div>\n\n\n\n  <!-- Vue détaillée des commandes avec fournisseurs -->\n  <div *ngIf=\"!isLoading() && !error()\" class=\"detailed-orders-container\">\n    <div *ngFor=\"let commande of filteredCommandesAvecFournisseur()\" class=\"order-card\">\n      <!-- En-tête de la commande client -->\n      <div class=\"order-header\">\n        <div class=\"order-info\">\n          <h3 class=\"order-title\">{{ commande.numeroCommande }}</h3>\n          <div class=\"client-info\">\n            <strong>{{ commande.clientNom }}</strong> - {{ commande.clientEmail }}\n          </div>\n          <div class=\"order-meta\">\n            <span class=\"order-date\">{{ formatDate(commande.dateCommande) }}</span>\n            <span class=\"status-badge\" [ngClass]=\"getStatusClass(commande.statut)\">\n              {{ getStatusText(commande.statut) }}\n            </span>\n            <span class=\"order-amount\">{{ formatPrice(commande.montantTotal) }}</span>\n          </div>\n        </div>\n        <div class=\"order-actions\">\n          <button\n            class=\"btn btn-sm btn-outline\"\n            (click)=\"viewOrderDetails(commande)\"\n            [class.active]=\"isOrderExpanded(commande.id)\">\n            <i class=\"icon-chevron-down\" [class.rotated]=\"isOrderExpanded(commande.id)\"></i>\n            {{ isOrderExpanded(commande.id) ? 'Masquer' : 'Voir' }} détails\n          </button>\n        </div>\n      </div>\n\n      <!-- Commandes fournisseur associées (affichées seulement si expandée) -->\n      <div *ngIf=\"isOrderExpanded(commande.id)\" class=\"supplier-orders\">\n        <h4 class=\"supplier-orders-title\">\n          <i class=\"icon-truck\"></i>\n          Commandes Fournisseur ({{ commande.commandesFournisseur.length }})\n        </h4>\n\n        <div *ngIf=\"commande.commandesFournisseur.length === 0\" class=\"no-supplier-orders\">\n          <p>Aucune commande fournisseur associée</p>\n        </div>\n\n        <div *ngFor=\"let cmdFournisseur of commande.commandesFournisseur\" class=\"supplier-order-card\">\n          <div class=\"supplier-order-header\">\n            <div class=\"supplier-info\">\n              <strong>{{ cmdFournisseur.fournisseurNom }}</strong>\n              <span class=\"supplier-email\">{{ cmdFournisseur.fournisseurEmail }}</span>\n            </div>\n            <div class=\"supplier-order-meta\">\n              <span class=\"supplier-reference\">{{ cmdFournisseur.reference }}</span>\n              <span class=\"status-badge\" [ngClass]=\"getCommandeFournisseurStatusClass(cmdFournisseur.statut)\">\n                {{ getCommandeFournisseurStatusText(cmdFournisseur.statut) }}\n              </span>\n              <span class=\"supplier-amount\">{{ formatPrice(cmdFournisseur.montantTotal) }}</span>\n            </div>\n          </div>\n\n          <!-- Détails de livraison -->\n          <div *ngIf=\"cmdFournisseur.dateLivraison || cmdFournisseur.numeroBonLivraison\" class=\"delivery-info\">\n            <div *ngIf=\"cmdFournisseur.dateLivraison\" class=\"delivery-date\">\n              <i class=\"icon-calendar\"></i>\n              Livraison prévue: {{ formatDate(cmdFournisseur.dateLivraison) }}\n            </div>\n            <div *ngIf=\"cmdFournisseur.numeroBonLivraison\" class=\"delivery-number\">\n              <i class=\"icon-package\"></i>\n              Bon de livraison: {{ cmdFournisseur.numeroBonLivraison }}\n            </div>\n          </div>\n\n          <!-- Lignes de commande -->\n          <div class=\"order-lines\">\n            <div *ngFor=\"let ligne of cmdFournisseur.lignes\" class=\"order-line\">\n              <div class=\"product-info\">\n                <span class=\"product-name\">{{ ligne.produitNom }}</span>\n                <span class=\"product-ref\">{{ ligne.referenceProduit }}</span>\n              </div>\n              <div class=\"quantity-price\">\n                <span class=\"quantity\">{{ ligne.quantite }}x</span>\n                <span class=\"unit-price\">{{ formatPrice(ligne.prixUnitaire) }}</span>\n                <span class=\"line-total\">{{ formatPrice(ligne.montantLigne) }}</span>\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n\n    <!-- Message si aucune commande -->\n    <div *ngIf=\"commandesAvecFournisseur().length === 0\" class=\"no-data\">\n      <i class=\"icon-shopping-cart\"></i>\n      <h3>Aucune commande trouvée</h3>\n      <p>Aucune commande ne correspond aux critères de recherche.</p>\n    </div>\n  </div>\n\n  <!-- Pagination -->\n  <div *ngIf=\"totalPages() > 1\" class=\"pagination-container\">\n    <div class=\"pagination-info\">\n      Affichage de {{ (currentPage() - 1) * pageSize() + 1 }} à \n      {{ Math.min(currentPage() * pageSize(), totalItems()) }} sur {{ totalItems() }} commandes\n    </div>\n    \n    <div class=\"pagination\">\n      <button \n        class=\"btn-page\"\n        [disabled]=\"currentPage() === 1\"\n        (click)=\"onPageChange(currentPage() - 1)\">\n        <i class=\"icon-chevron-left\"></i>\n      </button>\n      \n      <span class=\"page-info\">\n        Page {{ currentPage() }} sur {{ totalPages() }}\n      </span>\n      \n      <button \n        class=\"btn-page\"\n        [disabled]=\"currentPage() === totalPages()\"\n        (click)=\"onPageChange(currentPage() + 1)\">\n        <i class=\"icon-chevron-right\"></i>\n      </button>\n    </div>\n  </div>\n\n  <!-- Statistiques rapides -->\n  <div class=\"stats-summary\">\n    <div class=\"stat-card\">\n      <div class=\"stat-value\">{{ getTotalCommandes() }}</div>\n      <div class=\"stat-label\">Commandes</div>\n    </div>\n</div>\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACoCQ,IAAA,yBAAA,GAAA,UAAA,EAAA;AAAwD,IAAA,iBAAA,CAAA;AAA2B,IAAA,uBAAA;;;;;AAA5C,IAAA,qBAAA,SAAA,SAAA;AAAiB,IAAA,oBAAA;AAAA,IAAA,4BAAA,OAAA,cAAA,SAAA,CAAA;;;;;AAoB9D,IAAA,yBAAA,GAAA,OAAA,EAAA;AACE,IAAA,oBAAA,GAAA,KAAA,EAAA;AACA,IAAA,iBAAA,CAAA;AACF,IAAA,uBAAA;;;;AADE,IAAA,oBAAA,CAAA;AAAA,IAAA,6BAAA,KAAA,OAAA,MAAA,GAAA,GAAA;;;;;AAIF,IAAA,yBAAA,GAAA,OAAA,EAAA;AACE,IAAA,oBAAA,GAAA,OAAA,EAAA;AACA,IAAA,yBAAA,GAAA,GAAA;AAAG,IAAA,iBAAA,GAAA,6BAAA;AAA2B,IAAA,uBAAA,EAAI;;;;;AAyC9B,IAAA,yBAAA,GAAA,OAAA,EAAA,EAAmF,GAAA,GAAA;AAC9E,IAAA,iBAAA,GAAA,yCAAA;AAAoC,IAAA,uBAAA,EAAI;;;;;AAoBzC,IAAA,yBAAA,GAAA,OAAA,EAAA;AACE,IAAA,oBAAA,GAAA,KAAA,EAAA;AACA,IAAA,iBAAA,CAAA;AACF,IAAA,uBAAA;;;;;AADE,IAAA,oBAAA,CAAA;AAAA,IAAA,6BAAA,0BAAA,OAAA,WAAA,kBAAA,aAAA,GAAA,GAAA;;;;;AAEF,IAAA,yBAAA,GAAA,OAAA,EAAA;AACE,IAAA,oBAAA,GAAA,KAAA,EAAA;AACA,IAAA,iBAAA,CAAA;AACF,IAAA,uBAAA;;;;AADE,IAAA,oBAAA,CAAA;AAAA,IAAA,6BAAA,uBAAA,kBAAA,oBAAA,GAAA;;;;;AAPJ,IAAA,yBAAA,GAAA,OAAA,EAAA;AACE,IAAA,qBAAA,GAAA,0EAAA,GAAA,GAAA,OAAA,EAAA,EAAgE,GAAA,0EAAA,GAAA,GAAA,OAAA,EAAA;AAQlE,IAAA,uBAAA;;;;AARQ,IAAA,oBAAA;AAAA,IAAA,qBAAA,QAAA,kBAAA,aAAA;AAIA,IAAA,oBAAA;AAAA,IAAA,qBAAA,QAAA,kBAAA,kBAAA;;;;;AAQN,IAAA,yBAAA,GAAA,OAAA,EAAA,EAAoE,GAAA,OAAA,EAAA,EACxC,GAAA,QAAA,EAAA;AACG,IAAA,iBAAA,CAAA;AAAsB,IAAA,uBAAA;AACjD,IAAA,yBAAA,GAAA,QAAA,EAAA;AAA0B,IAAA,iBAAA,CAAA;AAA4B,IAAA,uBAAA,EAAO;AAE/D,IAAA,yBAAA,GAAA,OAAA,EAAA,EAA4B,GAAA,QAAA,EAAA;AACH,IAAA,iBAAA,CAAA;AAAqB,IAAA,uBAAA;AAC5C,IAAA,yBAAA,GAAA,QAAA,EAAA;AAAyB,IAAA,iBAAA,EAAA;AAAqC,IAAA,uBAAA;AAC9D,IAAA,yBAAA,IAAA,QAAA,EAAA;AAAyB,IAAA,iBAAA,EAAA;AAAqC,IAAA,uBAAA,EAAO,EACjE;;;;;AAPuB,IAAA,oBAAA,CAAA;AAAA,IAAA,4BAAA,SAAA,UAAA;AACD,IAAA,oBAAA,CAAA;AAAA,IAAA,4BAAA,SAAA,gBAAA;AAGH,IAAA,oBAAA,CAAA;AAAA,IAAA,6BAAA,IAAA,SAAA,UAAA,GAAA;AACE,IAAA,oBAAA,CAAA;AAAA,IAAA,4BAAA,OAAA,YAAA,SAAA,YAAA,CAAA;AACA,IAAA,oBAAA,CAAA;AAAA,IAAA,4BAAA,OAAA,YAAA,SAAA,YAAA,CAAA;;;;;AArCjC,IAAA,yBAAA,GAAA,OAAA,EAAA,EAA8F,GAAA,OAAA,EAAA,EACzD,GAAA,OAAA,EAAA,EACN,GAAA,QAAA;AACjB,IAAA,iBAAA,CAAA;AAAmC,IAAA,uBAAA;AAC3C,IAAA,yBAAA,GAAA,QAAA,EAAA;AAA6B,IAAA,iBAAA,CAAA;AAAqC,IAAA,uBAAA,EAAO;AAE3E,IAAA,yBAAA,GAAA,OAAA,EAAA,EAAiC,GAAA,QAAA,EAAA;AACE,IAAA,iBAAA,CAAA;AAA8B,IAAA,uBAAA;AAC/D,IAAA,yBAAA,IAAA,QAAA,EAAA;AACE,IAAA,iBAAA,EAAA;AACF,IAAA,uBAAA;AACA,IAAA,yBAAA,IAAA,QAAA,EAAA;AAA8B,IAAA,iBAAA,EAAA;AAA8C,IAAA,uBAAA,EAAO,EAC/E;AAIR,IAAA,qBAAA,IAAA,oEAAA,GAAA,GAAA,OAAA,EAAA;AAYA,IAAA,yBAAA,IAAA,OAAA,EAAA;AACE,IAAA,qBAAA,IAAA,oEAAA,IAAA,GAAA,OAAA,EAAA;AAWF,IAAA,uBAAA,EAAM;;;;;AArCM,IAAA,oBAAA,CAAA;AAAA,IAAA,4BAAA,kBAAA,cAAA;AACqB,IAAA,oBAAA,CAAA;AAAA,IAAA,4BAAA,kBAAA,gBAAA;AAGI,IAAA,oBAAA,CAAA;AAAA,IAAA,4BAAA,kBAAA,SAAA;AACN,IAAA,oBAAA;AAAA,IAAA,qBAAA,WAAA,OAAA,kCAAA,kBAAA,MAAA,CAAA;AACzB,IAAA,oBAAA;AAAA,IAAA,6BAAA,KAAA,OAAA,iCAAA,kBAAA,MAAA,GAAA,GAAA;AAE4B,IAAA,oBAAA,CAAA;AAAA,IAAA,4BAAA,OAAA,YAAA,kBAAA,YAAA,CAAA;AAK5B,IAAA,oBAAA;AAAA,IAAA,qBAAA,QAAA,kBAAA,iBAAA,kBAAA,kBAAA;AAamB,IAAA,oBAAA,CAAA;AAAA,IAAA,qBAAA,WAAA,kBAAA,MAAA;;;;;AAvC7B,IAAA,yBAAA,GAAA,OAAA,EAAA,EAAkE,GAAA,MAAA,EAAA;AAE9D,IAAA,oBAAA,GAAA,KAAA,EAAA;AACA,IAAA,iBAAA,CAAA;AACF,IAAA,uBAAA;AAEA,IAAA,qBAAA,GAAA,6DAAA,GAAA,GAAA,OAAA,EAAA,EAAmF,GAAA,6DAAA,IAAA,GAAA,OAAA,EAAA;AA8CrF,IAAA,uBAAA;;;;AAjDI,IAAA,oBAAA,CAAA;AAAA,IAAA,6BAAA,4BAAA,YAAA,qBAAA,QAAA,IAAA;AAGI,IAAA,oBAAA;AAAA,IAAA,qBAAA,QAAA,YAAA,qBAAA,WAAA,CAAA;AAI0B,IAAA,oBAAA;AAAA,IAAA,qBAAA,WAAA,YAAA,oBAAA;;;;;;AAtCpC,IAAA,yBAAA,GAAA,OAAA,EAAA,EAAoF,GAAA,OAAA,EAAA,EAExD,GAAA,OAAA,EAAA,EACA,GAAA,MAAA,EAAA;AACE,IAAA,iBAAA,CAAA;AAA6B,IAAA,uBAAA;AACrD,IAAA,yBAAA,GAAA,OAAA,EAAA,EAAyB,GAAA,QAAA;AACf,IAAA,iBAAA,CAAA;AAAwB,IAAA,uBAAA;AAAU,IAAA,iBAAA,CAAA;AAC5C,IAAA,uBAAA;AACA,IAAA,yBAAA,GAAA,OAAA,EAAA,EAAwB,IAAA,QAAA,EAAA;AACG,IAAA,iBAAA,EAAA;AAAuC,IAAA,uBAAA;AAChE,IAAA,yBAAA,IAAA,QAAA,EAAA;AACE,IAAA,iBAAA,EAAA;AACF,IAAA,uBAAA;AACA,IAAA,yBAAA,IAAA,QAAA,EAAA;AAA2B,IAAA,iBAAA,EAAA;AAAwC,IAAA,uBAAA,EAAO,EACtE;AAER,IAAA,yBAAA,IAAA,OAAA,EAAA,EAA2B,IAAA,UAAA,EAAA;AAGvB,IAAA,qBAAA,SAAA,SAAA,0EAAA;AAAA,YAAA,cAAA,wBAAA,GAAA,EAAA;AAAA,YAAA,SAAA,wBAAA,CAAA;AAAA,aAAA,sBAAS,OAAA,iBAAA,WAAA,CAA0B;IAAA,CAAA;AAEnC,IAAA,oBAAA,IAAA,KAAA,EAAA;AACA,IAAA,iBAAA,EAAA;AACF,IAAA,uBAAA,EAAS,EACL;AAIR,IAAA,qBAAA,IAAA,uDAAA,GAAA,GAAA,OAAA,EAAA;AAqDF,IAAA,uBAAA;;;;;AA7E8B,IAAA,oBAAA,CAAA;AAAA,IAAA,4BAAA,YAAA,cAAA;AAEd,IAAA,oBAAA,CAAA;AAAA,IAAA,4BAAA,YAAA,SAAA;AAAkC,IAAA,oBAAA;AAAA,IAAA,6BAAA,OAAA,YAAA,aAAA,GAAA;AAGjB,IAAA,oBAAA,CAAA;AAAA,IAAA,4BAAA,OAAA,WAAA,YAAA,YAAA,CAAA;AACE,IAAA,oBAAA;AAAA,IAAA,qBAAA,WAAA,OAAA,eAAA,YAAA,MAAA,CAAA;AACzB,IAAA,oBAAA;AAAA,IAAA,6BAAA,KAAA,OAAA,cAAA,YAAA,MAAA,GAAA,GAAA;AAEyB,IAAA,oBAAA,CAAA;AAAA,IAAA,4BAAA,OAAA,YAAA,YAAA,YAAA,CAAA;AAO3B,IAAA,oBAAA,CAAA;AAAA,IAAA,sBAAA,UAAA,OAAA,gBAAA,YAAA,EAAA,CAAA;AAC6B,IAAA,oBAAA;AAAA,IAAA,sBAAA,WAAA,OAAA,gBAAA,YAAA,EAAA,CAAA;AAC7B,IAAA,oBAAA;AAAA,IAAA,6BAAA,KAAA,OAAA,gBAAA,YAAA,EAAA,IAAA,YAAA,QAAA,cAAA;AAMA,IAAA,oBAAA;AAAA,IAAA,qBAAA,QAAA,OAAA,gBAAA,YAAA,EAAA,CAAA;;;;;AAwDR,IAAA,yBAAA,GAAA,OAAA,EAAA;AACE,IAAA,oBAAA,GAAA,KAAA,CAAA;AACA,IAAA,yBAAA,GAAA,IAAA;AAAI,IAAA,iBAAA,GAAA,4BAAA;AAAuB,IAAA,uBAAA;AAC3B,IAAA,yBAAA,GAAA,GAAA;AAAG,IAAA,iBAAA,GAAA,6DAAA;AAAwD,IAAA,uBAAA,EAAI;;;;;AAxFnE,IAAA,yBAAA,GAAA,OAAA,EAAA;AACE,IAAA,qBAAA,GAAA,gDAAA,IAAA,IAAA,OAAA,EAAA,EAAoF,GAAA,gDAAA,GAAA,GAAA,OAAA,EAAA;AAyFtF,IAAA,uBAAA;;;;AAzF4B,IAAA,oBAAA;AAAA,IAAA,qBAAA,WAAA,OAAA,iCAAA,CAAA;AAoFpB,IAAA,oBAAA;AAAA,IAAA,qBAAA,QAAA,OAAA,yBAAA,EAAA,WAAA,CAAA;;;;;;AAQR,IAAA,yBAAA,GAAA,OAAA,EAAA,EAA2D,GAAA,OAAA,EAAA;AAEvD,IAAA,iBAAA,CAAA;AAEF,IAAA,uBAAA;AAEA,IAAA,yBAAA,GAAA,OAAA,EAAA,EAAwB,GAAA,UAAA,EAAA;AAIpB,IAAA,qBAAA,SAAA,SAAA,mEAAA;AAAA,MAAA,wBAAA,GAAA;AAAA,YAAA,SAAA,wBAAA;AAAA,aAAA,sBAAS,OAAA,aAAa,OAAA,YAAA,IAAgB,CAAC,CAAC;IAAA,CAAA;AACxC,IAAA,oBAAA,GAAA,KAAA,EAAA;AACF,IAAA,uBAAA;AAEA,IAAA,yBAAA,GAAA,QAAA,EAAA;AACE,IAAA,iBAAA,CAAA;AACF,IAAA,uBAAA;AAEA,IAAA,yBAAA,GAAA,UAAA,EAAA;AAGE,IAAA,qBAAA,SAAA,SAAA,mEAAA;AAAA,MAAA,wBAAA,GAAA;AAAA,YAAA,SAAA,wBAAA;AAAA,aAAA,sBAAS,OAAA,aAAa,OAAA,YAAA,IAAgB,CAAC,CAAC;IAAA,CAAA;AACxC,IAAA,oBAAA,GAAA,KAAA,EAAA;AACF,IAAA,uBAAA,EAAS,EACL;;;;AAtBJ,IAAA,oBAAA,CAAA;AAAA,IAAA,6BAAA,mBAAA,OAAA,YAAA,IAAA,KAAA,OAAA,SAAA,IAAA,GAAA,UAAA,OAAA,KAAA,IAAA,OAAA,YAAA,IAAA,OAAA,SAAA,GAAA,OAAA,WAAA,CAAA,GAAA,SAAA,OAAA,WAAA,GAAA,aAAA;AAOE,IAAA,oBAAA,CAAA;AAAA,IAAA,qBAAA,YAAA,OAAA,YAAA,MAAA,CAAA;AAMA,IAAA,oBAAA,CAAA;AAAA,IAAA,6BAAA,UAAA,OAAA,YAAA,GAAA,SAAA,OAAA,WAAA,GAAA,GAAA;AAKA,IAAA,oBAAA;AAAA,IAAA,qBAAA,YAAA,OAAA,YAAA,MAAA,OAAA,WAAA,CAAA;;;ADpIF,IAAO,2BAAP,MAAO,0BAAwB;EAoDf;;EAlDpB,2BAA2B,OAAkC,CAAA,CAAE;EAC/D,YAAY,OAAO,KAAK;EACxB,QAAQ,OAAO,EAAE;EACjB,aAAa,OAAO,EAAE;EACtB,iBAAiB,OAAO,EAAE;EAC1B,YAAY,OAAO,EAAE;EACrB,UAAU,OAAO,EAAE;EACnB,cAAc,OAAO,CAAC;EACtB,WAAW,OAAO,EAAE;EACpB,aAAa,OAAO,CAAC;EACrB,kBAAkB,OAAsB,IAAI;;EAK5C,mCAAmC,SAAS,MAAK;AAC/C,UAAM,SAAS,KAAK,yBAAwB;AAC5C,UAAM,SAAS,KAAK,WAAU,EAAG,YAAW;AAC5C,UAAM,SAAS,KAAK,eAAc;AAClC,UAAM,YAAY,KAAK,UAAS;AAChC,UAAM,UAAU,KAAK,QAAO;AAE5B,WAAO,OAAO,OAAO,WAAQ;AAC3B,YAAM,gBAAgB,CAAC,UACrB,MAAM,eAAe,YAAW,EAAG,SAAS,MAAM,KAClD,MAAM,UAAU,YAAW,EAAG,SAAS,MAAM,KAC7C,MAAM,YAAY,YAAW,EAAG,SAAS,MAAM;AAEjD,YAAM,gBAAgB,CAAC,UAAU,MAAM,WAAW;AAElD,UAAI,cAAc;AAClB,UAAI,aAAa,SAAS;AACxB,cAAM,YAAY,IAAI,KAAK,MAAM,YAAY;AAC7C,YAAI,WAAW;AACb,gBAAM,YAAY,IAAI,KAAK,SAAS;AACpC,wBAAc,eAAe,aAAa;QAC5C;AACA,YAAI,SAAS;AACX,gBAAM,UAAU,IAAI,KAAK,OAAO;AAChC,kBAAQ,SAAS,IAAI,IAAI,IAAI,GAAG;AAChC,wBAAc,eAAe,aAAa;QAC5C;MACF;AAEA,aAAO,iBAAiB,iBAAiB;IAC3C,CAAC;EACH,CAAC;EAED,UAAU,CAAC,aAAa,aAAa,iBAAiB,YAAY,UAAU,SAAS;EAErF,YAAoB,cAA0B;AAA1B,SAAA,eAAA;EAA6B;EAEjD,WAAQ;AACN,SAAK,cAAa;EACpB;EAEA,gBAAa;AACX,SAAK,UAAU,IAAI,IAAI;AACvB,SAAK,MAAM,IAAI,EAAE;AAGjB,UAAM,SAAS;MACb,MAAM,KAAK,YAAW;MACtB,UAAU,KAAK,SAAQ;MACvB,QAAQ,KAAK,eAAc,KAAM;MACjC,WAAW,KAAK,UAAS,IAAK,IAAI,KAAK,KAAK,UAAS,CAAE,IAAI;MAC3D,SAAS,KAAK,QAAO,IAAK,IAAI,KAAK,KAAK,QAAO,CAAE,IAAI;;AAGvD,SAAK,aAAa,aAAa,MAAM,EAAE,UAAU;MAC/C,MAAM,CAAC,aAAiB;AACtB,YAAI,SAAS,WAAW,SAAS,MAAM;AAErC,gBAAM,2BAA2B,SAAS,KAAK,IAAI,CAAC,aAAmB,iCAClE,WADkE;YAErE,gBAAgB,SAAS,kBAAkB;YAC3C,sBAAsB,SAAS,wBAAwB,CAAA;YACvD;AACF,eAAK,yBAAyB,IAAI,wBAAwB;AAC1D,eAAK,WAAW,IAAI,SAAS,SAAS,SAAS,KAAK,MAAM;QAC5D,OAAO;AACL,eAAK,yBAAyB,IAAI,CAAA,CAAE;AACpC,eAAK,WAAW,IAAI,CAAC;QACvB;AACA,aAAK,UAAU,IAAI,KAAK;MAC1B;MACA,OAAO,CAAC,UAAc;AACpB,gBAAQ,MAAM,4CAA4C,KAAK;AAC/D,aAAK,MAAM,IAAI,yCAAyC;AACxD,aAAK,UAAU,IAAI,KAAK;MAC1B;KACD;EACH;;EAGA,qBAAkB;EAElB;;EAGA,aAAU;EAEV;EAEA,WAAQ;EAER;EAEA,iBAAc;EAEd;EAEA,qBAAkB;EAElB;EAEA,eAAY;AACV,SAAK,WAAW,IAAI,EAAE;AACtB,SAAK,eAAe,IAAI,EAAE;AAC1B,SAAK,UAAU,IAAI,EAAE;AACrB,SAAK,QAAQ,IAAI,EAAE;EACrB;EAEA,UAAO;AACL,SAAK,YAAY,IAAI,CAAC;AACtB,SAAK,cAAa;EACpB;EAEA,iBAAiB,UAAiC;AAEhD,UAAM,kBAAkB,KAAK,gBAAe;AAC5C,SAAK,gBAAgB,IAAI,oBAAoB,SAAS,KAAK,OAAO,SAAS,EAAE;EAC/E;EAEA,gBAAgB,SAAe;AAC7B,WAAO,KAAK,gBAAe,MAAO;EACpC;EAEA,oBAAiB;AACf,WAAO,KAAK,iCAAgC,EAAG;EACjD;EAEA,iBAAc;AACZ,UAAM,QAAQ,KAAK,iCAAgC,EAAG,OAAO,CAAC,KAAK,MAAM,MAAM,EAAE,cAAc,CAAC;AAChG,WAAO,KAAK,YAAY,KAAK;EAC/B;EAEA,eAAY;AACV,UAAM,yDAAoD;EAC5D;EAEA,YAAY,OAAa;AACvB,WAAO,IAAI,KAAK,aAAa,SAAS;MACpC,OAAO;MACP,UAAU;KACX,EAAE,OAAO,KAAK;EACjB;EAEA,WAAW,MAAY;AACrB,WAAO,IAAI,KAAK,IAAI,EAAE,mBAAmB,SAAS;MAChD,MAAM;MACN,OAAO;MACP,KAAK;MACL,MAAM;MACN,QAAQ;KACT;EACH;EAEA,cAAc,QAAc;AAC1B,YAAQ,QAAQ;MACd,KAAK;AAAa,eAAO;MACzB,KAAK;AAAa,eAAO;MACzB,KAAK;AAAiB,eAAO;MAC7B,KAAK;AAAY,eAAO;MACxB,KAAK;AAAU,eAAO;MACtB,KAAK;AAAW,eAAO;MACvB;AAAS,eAAO;IAClB;EACF;EAEA,eAAe,QAAc;AAC3B,YAAQ,QAAQ;MACd,KAAK;AAAa,eAAO;MACzB,KAAK;AAAa,eAAO;MACzB,KAAK;AAAiB,eAAO;MAC7B,KAAK;AAAY,eAAO;MACxB,KAAK;AAAU,eAAO;MACtB,KAAK;AAAW,eAAO;MACvB;AAAS,eAAO;IAClB;EACF;EAEA,iCAAiC,QAAc;AAC7C,YAAQ,QAAQ;MACd,KAAK;AAAa,eAAO;MACzB,KAAK;AAAa,eAAO;MACzB,KAAK;AAAiB,eAAO;MAC7B,KAAK;AAAY,eAAO;MACxB,KAAK;AAAU,eAAO;MACtB,KAAK;AAAW,eAAO;MACvB;AAAS,eAAO;IAClB;EACF;EAEA,kCAAkC,QAAc;AAC9C,YAAQ,QAAQ;MACd,KAAK;AAAa,eAAO;MACzB,KAAK;AAAa,eAAO;MACzB,KAAK;AAAiB,eAAO;MAC7B,KAAK;AAAY,eAAO;MACxB,KAAK;AAAU,eAAO;MACtB,KAAK;AAAW,eAAO;MACvB;AAAS,eAAO;IAClB;EACF;EAEA,eAAe,QAAc;AAC3B,WAAO,CAAC,aAAa,WAAW,EAAE,SAAS,MAAM;EACnD;EAEA,gBAAgB,UAAiC;AAC/C,YAAQ,IAAI,wBAAwB,SAAS,EAAE;EACjD;;EAGA,aAAa,SAAS,MAAK;AACzB,WAAO,KAAK,KAAK,KAAK,WAAU,IAAK,KAAK,SAAQ,CAAE;EACtD,CAAC;EAED,aAAa,MAAY;AACvB,QAAI,QAAQ,KAAK,QAAQ,KAAK,WAAU,GAAI;AAC1C,WAAK,YAAY,IAAI,IAAI;AACzB,WAAK,cAAa;IACpB;EACF;;EAGA,OAAO;;qCA/OI,2BAAwB,4BAAA,YAAA,CAAA;EAAA;yEAAxB,2BAAwB,WAAA,CAAA,CAAA,sBAAA,CAAA,GAAA,OAAA,IAAA,MAAA,IAAA,QAAA,CAAA,CAAA,GAAA,4BAAA,GAAA,CAAA,GAAA,aAAA,GAAA,CAAA,GAAA,gBAAA,GAAA,CAAA,GAAA,YAAA,GAAA,CAAA,GAAA,oBAAA,GAAA,CAAA,GAAA,gBAAA,GAAA,CAAA,GAAA,OAAA,iBAAA,GAAA,OAAA,GAAA,CAAA,GAAA,eAAA,GAAA,CAAA,GAAA,OAAA,iBAAA,GAAA,SAAA,UAAA,GAAA,CAAA,GAAA,cAAA,GAAA,CAAA,GAAA,iBAAA,GAAA,CAAA,GAAA,YAAA,GAAA,CAAA,GAAA,aAAA,GAAA,CAAA,QAAA,QAAA,eAAA,uCAAA,GAAA,gBAAA,GAAA,SAAA,OAAA,GAAA,CAAA,GAAA,SAAA,GAAA,CAAA,GAAA,iBAAA,GAAA,UAAA,OAAA,GAAA,CAAA,SAAA,EAAA,GAAA,CAAA,GAAA,SAAA,GAAA,SAAA,SAAA,GAAA,CAAA,QAAA,QAAA,eAAA,iBAAA,GAAA,eAAA,GAAA,UAAA,OAAA,GAAA,CAAA,QAAA,QAAA,eAAA,YAAA,GAAA,eAAA,GAAA,UAAA,OAAA,GAAA,CAAA,SAAA,iBAAA,GAAA,MAAA,GAAA,CAAA,SAAA,qBAAA,GAAA,MAAA,GAAA,CAAA,SAAA,6BAAA,GAAA,MAAA,GAAA,CAAA,SAAA,wBAAA,GAAA,MAAA,GAAA,CAAA,GAAA,eAAA,GAAA,CAAA,GAAA,WAAA,GAAA,CAAA,GAAA,YAAA,GAAA,CAAA,GAAA,YAAA,GAAA,CAAA,GAAA,OAAA,GAAA,CAAA,GAAA,eAAA,GAAA,CAAA,GAAA,YAAA,GAAA,CAAA,GAAA,mBAAA,GAAA,CAAA,GAAA,iBAAA,GAAA,CAAA,GAAA,2BAAA,GAAA,CAAA,SAAA,cAAA,GAAA,SAAA,SAAA,GAAA,CAAA,SAAA,WAAA,GAAA,MAAA,GAAA,CAAA,GAAA,YAAA,GAAA,CAAA,GAAA,cAAA,GAAA,CAAA,GAAA,YAAA,GAAA,CAAA,GAAA,aAAA,GAAA,CAAA,GAAA,aAAA,GAAA,CAAA,GAAA,YAAA,GAAA,CAAA,GAAA,YAAA,GAAA,CAAA,GAAA,gBAAA,GAAA,SAAA,GAAA,CAAA,GAAA,cAAA,GAAA,CAAA,GAAA,eAAA,GAAA,CAAA,GAAA,OAAA,UAAA,eAAA,GAAA,OAAA,GAAA,CAAA,GAAA,mBAAA,GAAA,CAAA,SAAA,mBAAA,GAAA,MAAA,GAAA,CAAA,GAAA,iBAAA,GAAA,CAAA,GAAA,uBAAA,GAAA,CAAA,GAAA,YAAA,GAAA,CAAA,SAAA,sBAAA,GAAA,MAAA,GAAA,CAAA,SAAA,uBAAA,GAAA,SAAA,SAAA,GAAA,CAAA,GAAA,oBAAA,GAAA,CAAA,GAAA,qBAAA,GAAA,CAAA,GAAA,uBAAA,GAAA,CAAA,GAAA,eAAA,GAAA,CAAA,GAAA,gBAAA,GAAA,CAAA,GAAA,qBAAA,GAAA,CAAA,GAAA,oBAAA,GAAA,CAAA,GAAA,iBAAA,GAAA,CAAA,SAAA,iBAAA,GAAA,MAAA,GAAA,CAAA,GAAA,aAAA,GAAA,CAAA,SAAA,cAAA,GAAA,SAAA,SAAA,GAAA,CAAA,GAAA,eAAA,GAAA,CAAA,SAAA,iBAAA,GAAA,MAAA,GAAA,CAAA,SAAA,mBAAA,GAAA,MAAA,GAAA,CAAA,GAAA,eAAA,GAAA,CAAA,GAAA,eAAA,GAAA,CAAA,GAAA,iBAAA,GAAA,CAAA,GAAA,cAAA,GAAA,CAAA,GAAA,YAAA,GAAA,CAAA,GAAA,cAAA,GAAA,CAAA,GAAA,cAAA,GAAA,CAAA,GAAA,aAAA,GAAA,CAAA,GAAA,gBAAA,GAAA,CAAA,GAAA,UAAA,GAAA,CAAA,GAAA,YAAA,GAAA,CAAA,GAAA,YAAA,GAAA,CAAA,GAAA,SAAA,GAAA,CAAA,GAAA,sBAAA,GAAA,CAAA,GAAA,iBAAA,GAAA,CAAA,GAAA,YAAA,GAAA,CAAA,GAAA,YAAA,GAAA,SAAA,UAAA,GAAA,CAAA,GAAA,mBAAA,GAAA,CAAA,GAAA,WAAA,GAAA,CAAA,GAAA,oBAAA,CAAA,GAAA,UAAA,SAAA,kCAAA,IAAA,KAAA;AAAA,QAAA,KAAA,GAAA;ACnDrC,MAAA,yBAAA,GAAA,OAAA,CAAA,EAAwC,GAAA,OAAA,CAAA,EAEb,GAAA,OAAA,CAAA,EACK,GAAA,MAAA,CAAA;AAExB,MAAA,oBAAA,GAAA,KAAA,CAAA;AACA,MAAA,iBAAA,GAAA,yBAAA;AACF,MAAA,uBAAA;AACA,MAAA,yBAAA,GAAA,OAAA,CAAA,EAA4B,GAAA,UAAA,CAAA;AACQ,MAAA,qBAAA,SAAA,SAAA,4DAAA;AAAA,eAAS,IAAA,aAAA;MAAc,CAAA;AACvD,MAAA,oBAAA,GAAA,KAAA,CAAA;AACA,MAAA,iBAAA,GAAA,mBAAA;AACF,MAAA,uBAAA;AACA,MAAA,yBAAA,IAAA,UAAA,CAAA;AAAkC,MAAA,qBAAA,SAAA,SAAA,6DAAA;AAAA,eAAS,IAAA,QAAA;MAAS,CAAA;AAClD,MAAA,oBAAA,IAAA,KAAA,CAAA;AACA,MAAA,iBAAA,IAAA,cAAA;AACF,MAAA,uBAAA,EAAS,EACL,EACF;AAIR,MAAA,yBAAA,IAAA,OAAA,EAAA,EAA6B,IAAA,OAAA,EAAA;AAEzB,MAAA,oBAAA,IAAA,KAAA,EAAA;AACA,MAAA,yBAAA,IAAA,SAAA,EAAA;AAIE,MAAA,qBAAA,SAAA,SAAA,0DAAA,QAAA;AAAA,eAAS,IAAA,WAAA,IAAA,OAAA,OAAA,KAAA;MAAyC,CAAA;AAJpD,MAAA,uBAAA,EAKuB;AAGzB,MAAA,yBAAA,IAAA,OAAA,EAAA,EAAqB,IAAA,UAAA,EAAA;AACgB,MAAA,qBAAA,UAAA,SAAA,4DAAA,QAAA;AAAA,eAAU,IAAA,eAAA,IAAA,OAAA,OAAA,KAAA;MAA6C,CAAA;AACxF,MAAA,yBAAA,IAAA,UAAA,EAAA;AAAiB,MAAA,iBAAA,IAAA,kBAAA;AAAgB,MAAA,uBAAA;AACjC,MAAA,qBAAA,IAAA,6CAAA,GAAA,GAAA,UAAA,EAAA;AACF,MAAA,uBAAA;AAEA,MAAA,yBAAA,IAAA,SAAA,EAAA;AAGE,MAAA,qBAAA,UAAA,SAAA,2DAAA,QAAA;AAAA,eAAU,IAAA,UAAA,IAAA,OAAA,OAAA,KAAA;MAAwC,CAAA;AAHpD,MAAA,uBAAA;AAOA,MAAA,yBAAA,IAAA,SAAA,EAAA;AAGE,MAAA,qBAAA,UAAA,SAAA,2DAAA,QAAA;AAAA,eAAU,IAAA,QAAA,IAAA,OAAA,OAAA,KAAA;MAAsC,CAAA;AAHlD,MAAA,uBAAA,EAKyB,EACrB;AAIR,MAAA,qBAAA,IAAA,0CAAA,GAAA,GAAA,OAAA,EAAA,EAA2C,IAAA,0CAAA,GAAA,GAAA,OAAA,EAAA,EAMQ,IAAA,0CAAA,GAAA,GAAA,OAAA,EAAA,EAQqB,IAAA,0CAAA,IAAA,GAAA,OAAA,EAAA;AAyHxE,MAAA,yBAAA,IAAA,OAAA,EAAA,EAA2B,IAAA,OAAA,EAAA,EACF,IAAA,OAAA,EAAA;AACG,MAAA,iBAAA,EAAA;AAAyB,MAAA,uBAAA;AACjD,MAAA,yBAAA,IAAA,OAAA,EAAA;AAAwB,MAAA,iBAAA,IAAA,WAAA;AAAS,MAAA,uBAAA,EAAM,EACnC,EACJ;;;AAvLwD,MAAA,oBAAA,EAAA;AAAA,MAAA,qBAAA,YAAA,IAAA,UAAA,CAAA;AAetD,MAAA,oBAAA,CAAA;AAAA,MAAA,qBAAA,SAAA,IAAA,WAAA,CAAA;AAMM,MAAA,oBAAA,CAAA;AAAA,MAAA,qBAAA,SAAA,IAAA,eAAA,CAAA;AAEqB,MAAA,oBAAA,CAAA;AAAA,MAAA,qBAAA,WAAA,IAAA,OAAA;AAK3B,MAAA,oBAAA;AAAA,MAAA,qBAAA,SAAA,IAAA,UAAA,CAAA;AAOA,MAAA,oBAAA;AAAA,MAAA,qBAAA,SAAA,IAAA,QAAA,CAAA;AAQA,MAAA,oBAAA;AAAA,MAAA,qBAAA,QAAA,IAAA,MAAA,CAAA;AAMA,MAAA,oBAAA;AAAA,MAAA,qBAAA,QAAA,IAAA,UAAA,CAAA;AAQA,MAAA,oBAAA;AAAA,MAAA,qBAAA,QAAA,CAAA,IAAA,UAAA,KAAA,CAAA,IAAA,MAAA,CAAA;AA6FA,MAAA,oBAAA;AAAA,MAAA,qBAAA,QAAA,IAAA,WAAA,IAAA,CAAA;AA8BsB,MAAA,oBAAA,CAAA;AAAA,MAAA,4BAAA,IAAA,kBAAA,CAAA;;oBDlJlB,cAAY,SAAA,SAAA,MAAE,aAAW,gBAAA,4BAAA,GAAA,QAAA,CAAA,yyoBAAA,EAAA,CAAA;;;sEAIxB,0BAAwB,CAAA;UAPpC;uBACW,wBAAsB,YACpB,MAAI,SACP,CAAC,cAAc,WAAW,GAAC,UAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;GAAA,QAAA,CAAA,8iiBAAA,EAAA,CAAA;;;;6EAIzB,0BAAwB,EAAA,WAAA,4BAAA,UAAA,2EAAA,YAAA,GAAA,CAAA;AAAA,GAAA;", "names": []}
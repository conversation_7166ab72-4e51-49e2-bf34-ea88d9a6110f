{"version": 3, "sources": ["src/app/services/avis-moderation.service.ts"], "sourcesContent": ["import { Injectable } from '@angular/core';\nimport { HttpClient, HttpParams } from '@angular/common/http';\nimport { Observable } from 'rxjs';\nimport { environment } from '../../environments/environment';\n\nexport interface AvisModerationDto {\n  id: number;\n  note: number;\n  commentaire?: string;\n  datePublication: Date;\n  statut: StatutAvis;\n  statutLibelle: string;\n  commentaireSupprime: boolean;\n  dateModeration?: Date;\n  commentaireModeration?: string;\n  nomModerateur?: string;\n  \n  // Informations client\n  clientId: number;\n  clientNom: string;\n  clientPrenom: string;\n  clientEmail: string;\n  \n  // Informations produit\n  produitId: number;\n  produitNom: string;\n  produitReference: string;\n  \n  // Informations fournisseur\n  fournisseurId: number;\n  fournisseurNom: string;\n  fournisseurRaisonSociale: string;\n}\n\nexport interface ModererAvisDto {\n  statut: StatutAvis;\n  commentaireModeration?: string;\n}\n\nexport interface AvisFilterDto {\n  statut?: StatutAvis;\n  fournisseurId?: number;\n  produitId?: number;\n  dateDebut?: Date;\n  dateFin?: Date;\n  recherche?: string;\n  page?: number;\n  pageSize?: number;\n  sortBy?: string;\n  sortDesc?: boolean;\n}\n\nexport interface AvisStatsDto {\n  totalAvis: number;\n  avisPublies: number;\n  avisCommentaireSupprime: number;\n  avisSignales: number;\n  noteMoyenneGlobale: number;\n  avisParNote: { [key: number]: number };\n  avisRecents: AvisModerationDto[];\n}\n\nexport enum StatutAvis {\n  Publie = 1,\n  CommentaireSupprime = 2,\n  Signale = 3\n}\n\n@Injectable({\n  providedIn: 'root'\n})\nexport class AvisModerationService {\n  private apiUrl = `${environment.apiUrl}/AvisModeration`;\n\n  constructor(private http: HttpClient) { }\n\n  // Méthodes pour l'admin\n  getAvisForModeration(filter: AvisFilterDto = {}): Observable<AvisModerationDto[]> {\n    let params = new HttpParams();\n    \n    if (filter.statut !== undefined) params = params.set('statut', filter.statut.toString());\n    if (filter.fournisseurId) params = params.set('fournisseurId', filter.fournisseurId.toString());\n    if (filter.produitId) params = params.set('produitId', filter.produitId.toString());\n    if (filter.dateDebut) params = params.set('dateDebut', filter.dateDebut.toISOString());\n    if (filter.dateFin) params = params.set('dateFin', filter.dateFin.toISOString());\n    if (filter.recherche) params = params.set('recherche', filter.recherche);\n    if (filter.page) params = params.set('page', filter.page.toString());\n    if (filter.pageSize) params = params.set('pageSize', filter.pageSize.toString());\n    if (filter.sortBy) params = params.set('sortBy', filter.sortBy);\n    if (filter.sortDesc !== undefined) params = params.set('sortDesc', filter.sortDesc.toString());\n\n    return this.http.get<AvisModerationDto[]>(this.apiUrl, { params });\n  }\n\n  getAvisModeration(id: number): Observable<AvisModerationDto> {\n    return this.http.get<AvisModerationDto>(`${this.apiUrl}/${id}`);\n  }\n\n  modererAvis(id: number, dto: ModererAvisDto): Observable<AvisModerationDto> {\n    return this.http.put<AvisModerationDto>(`${this.apiUrl}/${id}/moderer`, dto);\n  }\n\n  getAvisStats(): Observable<AvisStatsDto> {\n    return this.http.get<AvisStatsDto>(`${this.apiUrl}/statistiques`);\n  }\n\n  // Méthodes pour les fournisseurs\n  getAvisFournisseur(filter: AvisFilterDto = {}): Observable<AvisModerationDto[]> {\n    let params = new HttpParams();\n\n    if (filter.statut !== undefined) params = params.set('statut', filter.statut.toString());\n    if (filter.produitId) params = params.set('produitId', filter.produitId.toString());\n    if (filter.dateDebut) params = params.set('dateDebut', filter.dateDebut.toISOString());\n    if (filter.dateFin) params = params.set('dateFin', filter.dateFin.toISOString());\n    if (filter.recherche) params = params.set('recherche', filter.recherche);\n    if (filter.page) params = params.set('page', filter.page.toString());\n    if (filter.pageSize) params = params.set('pageSize', filter.pageSize.toString());\n    if (filter.sortBy) params = params.set('sortBy', filter.sortBy);\n    if (filter.sortDesc !== undefined) params = params.set('sortDesc', filter.sortDesc.toString());\n\n    // Utiliser l'endpoint correct pour les fournisseurs\n    return this.http.get<AvisModerationDto[]>(`${this.apiUrl}/fournisseur`, { params });\n  }\n\n\n\n  repondreAvis(id: number, reponse: string): Observable<AvisModerationDto> {\n    return this.http.put<AvisModerationDto>(`${this.apiUrl}/${id}/repondre`, JSON.stringify(reponse), {\n      headers: { 'Content-Type': 'application/json' }\n    });\n  }\n\n  getAvisStatsFournisseur(): Observable<AvisStatsDto> {\n    // Utiliser l'endpoint correct pour les statistiques fournisseur\n    return this.http.get<AvisStatsDto>(`${this.apiUrl}/statistiques/fournisseur`);\n  }\n\n\n\n  // Méthode pour signaler un avis (fournisseur)\n  signalerAvis(id: number, raisonSignalement: string, detailsSignalement?: string): Observable<AvisModerationDto> {\n    const signalementData = {\n      raisonSignalement,\n      detailsSignalement: detailsSignalement || ''\n    };\n\n    console.log('🚨 Service - Envoi du signalement:', signalementData);\n\n    return this.http.put<AvisModerationDto>(`${this.apiUrl}/${id}/signaler`, signalementData);\n  }\n\n\n\n  // Nouvelles méthodes pour la suppression et restauration de commentaires\n  supprimerCommentaire(id: number, raisonSuppression: string): Observable<AvisModerationDto> {\n    return this.http.put<AvisModerationDto>(`${this.apiUrl}/${id}/supprimer-commentaire`, {\n      raisonSuppression\n    });\n  }\n\n  restaurerCommentaire(id: number): Observable<AvisModerationDto> {\n    return this.http.put<AvisModerationDto>(`${this.apiUrl}/${id}/restaurer-commentaire`, {});\n  }\n\n  // Méthode pour supprimer définitivement un avis (pour les données de test)\n  supprimerAvis(id: number): Observable<void> {\n    console.log(`🌐 Appel DELETE vers: ${this.apiUrl}/${id}`);\n    return this.http.delete<void>(`${this.apiUrl}/${id}`);\n  }\n\n  // Méthode pour tester la connectivité avec le backend\n  testerConnectivite(): Observable<any> {\n    return this.http.get<any>(`${this.apiUrl}/test`);\n  }\n\n  // Méthodes utilitaires\n  getStatutLibelle(statut: StatutAvis): string {\n    switch (statut) {\n      case StatutAvis.Publie: return 'Publié';\n      case StatutAvis.CommentaireSupprime: return 'Commentaire supprimé';\n      case StatutAvis.Signale: return 'Signalé';\n      default: return 'Non défini';\n    }\n  }\n\n  getStatutColor(statut: StatutAvis): string {\n    switch (statut) {\n      case StatutAvis.Publie: return 'success';\n      case StatutAvis.CommentaireSupprime: return 'warning';\n      case StatutAvis.Signale: return 'danger';\n      default: return 'secondary';\n    }\n  }\n\n  getStatutIcon(statut: StatutAvis): string {\n    switch (statut) {\n      case StatutAvis.Publie: return 'check-circle';\n      case StatutAvis.CommentaireSupprime: return 'exclamation-triangle';\n      case StatutAvis.Signale: return 'flag';\n      default: return 'question-circle';\n    }\n  }\n\n\n}\n"], "mappings": ";;;;;;;;;;;;;;;AA8DA,IAAY;CAAZ,SAAYA,aAAU;AACpB,EAAAA,YAAAA,YAAA,QAAA,IAAA,CAAA,IAAA;AACA,EAAAA,YAAAA,YAAA,qBAAA,IAAA,CAAA,IAAA;AACA,EAAAA,YAAAA,YAAA,SAAA,IAAA,CAAA,IAAA;AACF,GAJY,eAAA,aAAU,CAAA,EAAA;AAShB,IAAO,wBAAP,MAAO,uBAAqB;EAGZ;EAFZ,SAAS,GAAG,YAAY,MAAM;EAEtC,YAAoB,MAAgB;AAAhB,SAAA,OAAA;EAAoB;;EAGxC,qBAAqB,SAAwB,CAAA,GAAE;AAC7C,QAAI,SAAS,IAAI,WAAU;AAE3B,QAAI,OAAO,WAAW;AAAW,eAAS,OAAO,IAAI,UAAU,OAAO,OAAO,SAAQ,CAAE;AACvF,QAAI,OAAO;AAAe,eAAS,OAAO,IAAI,iBAAiB,OAAO,cAAc,SAAQ,CAAE;AAC9F,QAAI,OAAO;AAAW,eAAS,OAAO,IAAI,aAAa,OAAO,UAAU,SAAQ,CAAE;AAClF,QAAI,OAAO;AAAW,eAAS,OAAO,IAAI,aAAa,OAAO,UAAU,YAAW,CAAE;AACrF,QAAI,OAAO;AAAS,eAAS,OAAO,IAAI,WAAW,OAAO,QAAQ,YAAW,CAAE;AAC/E,QAAI,OAAO;AAAW,eAAS,OAAO,IAAI,aAAa,OAAO,SAAS;AACvE,QAAI,OAAO;AAAM,eAAS,OAAO,IAAI,QAAQ,OAAO,KAAK,SAAQ,CAAE;AACnE,QAAI,OAAO;AAAU,eAAS,OAAO,IAAI,YAAY,OAAO,SAAS,SAAQ,CAAE;AAC/E,QAAI,OAAO;AAAQ,eAAS,OAAO,IAAI,UAAU,OAAO,MAAM;AAC9D,QAAI,OAAO,aAAa;AAAW,eAAS,OAAO,IAAI,YAAY,OAAO,SAAS,SAAQ,CAAE;AAE7F,WAAO,KAAK,KAAK,IAAyB,KAAK,QAAQ,EAAE,OAAM,CAAE;EACnE;EAEA,kBAAkB,IAAU;AAC1B,WAAO,KAAK,KAAK,IAAuB,GAAG,KAAK,MAAM,IAAI,EAAE,EAAE;EAChE;EAEA,YAAY,IAAY,KAAmB;AACzC,WAAO,KAAK,KAAK,IAAuB,GAAG,KAAK,MAAM,IAAI,EAAE,YAAY,GAAG;EAC7E;EAEA,eAAY;AACV,WAAO,KAAK,KAAK,IAAkB,GAAG,KAAK,MAAM,eAAe;EAClE;;EAGA,mBAAmB,SAAwB,CAAA,GAAE;AAC3C,QAAI,SAAS,IAAI,WAAU;AAE3B,QAAI,OAAO,WAAW;AAAW,eAAS,OAAO,IAAI,UAAU,OAAO,OAAO,SAAQ,CAAE;AACvF,QAAI,OAAO;AAAW,eAAS,OAAO,IAAI,aAAa,OAAO,UAAU,SAAQ,CAAE;AAClF,QAAI,OAAO;AAAW,eAAS,OAAO,IAAI,aAAa,OAAO,UAAU,YAAW,CAAE;AACrF,QAAI,OAAO;AAAS,eAAS,OAAO,IAAI,WAAW,OAAO,QAAQ,YAAW,CAAE;AAC/E,QAAI,OAAO;AAAW,eAAS,OAAO,IAAI,aAAa,OAAO,SAAS;AACvE,QAAI,OAAO;AAAM,eAAS,OAAO,IAAI,QAAQ,OAAO,KAAK,SAAQ,CAAE;AACnE,QAAI,OAAO;AAAU,eAAS,OAAO,IAAI,YAAY,OAAO,SAAS,SAAQ,CAAE;AAC/E,QAAI,OAAO;AAAQ,eAAS,OAAO,IAAI,UAAU,OAAO,MAAM;AAC9D,QAAI,OAAO,aAAa;AAAW,eAAS,OAAO,IAAI,YAAY,OAAO,SAAS,SAAQ,CAAE;AAG7F,WAAO,KAAK,KAAK,IAAyB,GAAG,KAAK,MAAM,gBAAgB,EAAE,OAAM,CAAE;EACpF;EAIA,aAAa,IAAY,SAAe;AACtC,WAAO,KAAK,KAAK,IAAuB,GAAG,KAAK,MAAM,IAAI,EAAE,aAAa,KAAK,UAAU,OAAO,GAAG;MAChG,SAAS,EAAE,gBAAgB,mBAAkB;KAC9C;EACH;EAEA,0BAAuB;AAErB,WAAO,KAAK,KAAK,IAAkB,GAAG,KAAK,MAAM,2BAA2B;EAC9E;;EAKA,aAAa,IAAY,mBAA2B,oBAA2B;AAC7E,UAAM,kBAAkB;MACtB;MACA,oBAAoB,sBAAsB;;AAG5C,YAAQ,IAAI,6CAAsC,eAAe;AAEjE,WAAO,KAAK,KAAK,IAAuB,GAAG,KAAK,MAAM,IAAI,EAAE,aAAa,eAAe;EAC1F;;EAKA,qBAAqB,IAAY,mBAAyB;AACxD,WAAO,KAAK,KAAK,IAAuB,GAAG,KAAK,MAAM,IAAI,EAAE,0BAA0B;MACpF;KACD;EACH;EAEA,qBAAqB,IAAU;AAC7B,WAAO,KAAK,KAAK,IAAuB,GAAG,KAAK,MAAM,IAAI,EAAE,0BAA0B,CAAA,CAAE;EAC1F;;EAGA,cAAc,IAAU;AACtB,YAAQ,IAAI,gCAAyB,KAAK,MAAM,IAAI,EAAE,EAAE;AACxD,WAAO,KAAK,KAAK,OAAa,GAAG,KAAK,MAAM,IAAI,EAAE,EAAE;EACtD;;EAGA,qBAAkB;AAChB,WAAO,KAAK,KAAK,IAAS,GAAG,KAAK,MAAM,OAAO;EACjD;;EAGA,iBAAiB,QAAkB;AACjC,YAAQ,QAAQ;MACd,KAAK,WAAW;AAAQ,eAAO;MAC/B,KAAK,WAAW;AAAqB,eAAO;MAC5C,KAAK,WAAW;AAAS,eAAO;MAChC;AAAS,eAAO;IAClB;EACF;EAEA,eAAe,QAAkB;AAC/B,YAAQ,QAAQ;MACd,KAAK,WAAW;AAAQ,eAAO;MAC/B,KAAK,WAAW;AAAqB,eAAO;MAC5C,KAAK,WAAW;AAAS,eAAO;MAChC;AAAS,eAAO;IAClB;EACF;EAEA,cAAc,QAAkB;AAC9B,YAAQ,QAAQ;MACd,KAAK,WAAW;AAAQ,eAAO;MAC/B,KAAK,WAAW;AAAqB,eAAO;MAC5C,KAAK,WAAW;AAAS,eAAO;MAChC;AAAS,eAAO;IAClB;EACF;;qCAlIW,wBAAqB,mBAAA,UAAA,CAAA;EAAA;4EAArB,wBAAqB,SAArB,uBAAqB,WAAA,YAFpB,OAAM,CAAA;;;sEAEP,uBAAqB,CAAA;UAHjC;WAAW;MACV,YAAY;KACb;;;", "names": ["StatutAvis"]}
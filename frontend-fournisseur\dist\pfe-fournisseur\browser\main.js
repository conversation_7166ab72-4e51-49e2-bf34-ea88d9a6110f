import {
  AuthService
} from "./chunk-ZSXXNL7U.js";
import {
  AdminAuthService
} from "./chunk-2RV3R4JN.js";
import {
  DomRendererFactory2,
  Router,
  RouterOutlet,
  bootstrapApplication,
  provideRouter,
  withEnabledBlockingInitialNavigation,
  withInMemoryScrolling,
  withRouterConfig
} from "./chunk-6BVUYNW4.js";
import "./chunk-GFHHPDQ6.js";
import {
  provideHttpClient,
  withFetch,
  withInterceptors
} from "./chunk-7JDDWGD3.js";
import {
  ANIMATION_MODULE_TYPE,
  ChangeDetectionScheduler,
  CommonModule,
  Component,
  DOCUMENT,
  Injectable,
  InjectionToken,
  Injector,
  NgForOf,
  NgZone,
  RendererFactory2,
  RuntimeError,
  inject,
  makeEnvironmentProviders,
  map,
  performanceMarkFeature,
  provideZoneChangeDetection,
  setClassMetadata,
  take,
  ɵsetClassDebugInfo,
  ɵɵadvance,
  ɵɵdefineComponent,
  ɵɵdefineInjectable,
  ɵɵelement,
  ɵɵelementEnd,
  ɵɵelementStart,
  ɵɵgetCurrentView,
  ɵɵinject,
  ɵɵinvalidFactory,
  ɵɵlistener,
  ɵɵnextContext,
  ɵɵproperty,
  ɵɵresetView,
  ɵɵrestoreView,
  ɵɵsanitizeUrl,
  ɵɵstyleProp,
  ɵɵtemplate,
  ɵɵtext,
  ɵɵtextInterpolate,
  ɵɵtextInterpolate3
} from "./chunk-UBZQS7JS.js";

// node_modules/@angular/platform-browser/fesm2022/animations/async.mjs
var ANIMATION_PREFIX = "@";
var AsyncAnimationRendererFactory = class _AsyncAnimationRendererFactory {
  doc;
  delegate;
  zone;
  animationType;
  moduleImpl;
  _rendererFactoryPromise = null;
  scheduler = null;
  injector = inject(Injector);
  loadingSchedulerFn = inject(\u0275ASYNC_ANIMATION_LOADING_SCHEDULER_FN, {
    optional: true
  });
  _engine;
  /**
   *
   * @param moduleImpl allows to provide a mock implmentation (or will load the animation module)
   */
  constructor(doc, delegate, zone, animationType, moduleImpl) {
    this.doc = doc;
    this.delegate = delegate;
    this.zone = zone;
    this.animationType = animationType;
    this.moduleImpl = moduleImpl;
  }
  /** @docs-private */
  ngOnDestroy() {
    this._engine?.flush();
  }
  /**
   * @internal
   */
  loadImpl() {
    const loadFn = () => this.moduleImpl ?? import("./chunk-I6F7JBCW.js").then((m) => m);
    let moduleImplPromise;
    if (this.loadingSchedulerFn) {
      moduleImplPromise = this.loadingSchedulerFn(loadFn);
    } else {
      moduleImplPromise = loadFn();
    }
    return moduleImplPromise.catch((e) => {
      throw new RuntimeError(5300, (typeof ngDevMode === "undefined" || ngDevMode) && "Async loading for animations package was enabled, but loading failed. Angular falls back to using regular rendering. No animations will be displayed and their styles won't be applied.");
    }).then(({
      \u0275createEngine,
      \u0275AnimationRendererFactory
    }) => {
      this._engine = \u0275createEngine(this.animationType, this.doc);
      const rendererFactory = new \u0275AnimationRendererFactory(this.delegate, this._engine, this.zone);
      this.delegate = rendererFactory;
      return rendererFactory;
    });
  }
  /**
   * This method is delegating the renderer creation to the factories.
   * It uses default factory while the animation factory isn't loaded
   * and will rely on the animation factory once it is loaded.
   *
   * Calling this method will trigger as side effect the loading of the animation module
   * if the renderered component uses animations.
   */
  createRenderer(hostElement, rendererType) {
    const renderer = this.delegate.createRenderer(hostElement, rendererType);
    if (renderer.\u0275type === 0) {
      return renderer;
    }
    if (typeof renderer.throwOnSyntheticProps === "boolean") {
      renderer.throwOnSyntheticProps = false;
    }
    const dynamicRenderer = new DynamicDelegationRenderer(renderer);
    if (rendererType?.data?.["animation"] && !this._rendererFactoryPromise) {
      this._rendererFactoryPromise = this.loadImpl();
    }
    this._rendererFactoryPromise?.then((animationRendererFactory) => {
      const animationRenderer = animationRendererFactory.createRenderer(hostElement, rendererType);
      dynamicRenderer.use(animationRenderer);
      this.scheduler ??= this.injector.get(ChangeDetectionScheduler, null, {
        optional: true
      });
      this.scheduler?.notify(
        10
        /* NotificationSource.AsyncAnimationsLoaded */
      );
    }).catch((e) => {
      dynamicRenderer.use(renderer);
    });
    return dynamicRenderer;
  }
  begin() {
    this.delegate.begin?.();
  }
  end() {
    this.delegate.end?.();
  }
  whenRenderingDone() {
    return this.delegate.whenRenderingDone?.() ?? Promise.resolve();
  }
  /**
   * Used during HMR to clear any cached data about a component.
   * @param componentId ID of the component that is being replaced.
   */
  componentReplaced(componentId) {
    this._engine?.flush();
    this.delegate.componentReplaced?.(componentId);
  }
  static \u0275fac = function AsyncAnimationRendererFactory_Factory(__ngFactoryType__) {
    \u0275\u0275invalidFactory();
  };
  static \u0275prov = /* @__PURE__ */ \u0275\u0275defineInjectable({
    token: _AsyncAnimationRendererFactory,
    factory: _AsyncAnimationRendererFactory.\u0275fac
  });
};
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && setClassMetadata(AsyncAnimationRendererFactory, [{
    type: Injectable
  }], () => [{
    type: Document
  }, {
    type: RendererFactory2
  }, {
    type: NgZone
  }, {
    type: void 0
  }, {
    type: Promise
  }], null);
})();
var DynamicDelegationRenderer = class {
  delegate;
  // List of callbacks that need to be replayed on the animation renderer once its loaded
  replay = [];
  \u0275type = 1;
  constructor(delegate) {
    this.delegate = delegate;
  }
  use(impl) {
    this.delegate = impl;
    if (this.replay !== null) {
      for (const fn of this.replay) {
        fn(impl);
      }
      this.replay = null;
    }
  }
  get data() {
    return this.delegate.data;
  }
  destroy() {
    this.replay = null;
    this.delegate.destroy();
  }
  createElement(name, namespace) {
    return this.delegate.createElement(name, namespace);
  }
  createComment(value) {
    return this.delegate.createComment(value);
  }
  createText(value) {
    return this.delegate.createText(value);
  }
  get destroyNode() {
    return this.delegate.destroyNode;
  }
  appendChild(parent, newChild) {
    this.delegate.appendChild(parent, newChild);
  }
  insertBefore(parent, newChild, refChild, isMove) {
    this.delegate.insertBefore(parent, newChild, refChild, isMove);
  }
  removeChild(parent, oldChild, isHostElement) {
    this.delegate.removeChild(parent, oldChild, isHostElement);
  }
  selectRootElement(selectorOrNode, preserveContent) {
    return this.delegate.selectRootElement(selectorOrNode, preserveContent);
  }
  parentNode(node) {
    return this.delegate.parentNode(node);
  }
  nextSibling(node) {
    return this.delegate.nextSibling(node);
  }
  setAttribute(el, name, value, namespace) {
    this.delegate.setAttribute(el, name, value, namespace);
  }
  removeAttribute(el, name, namespace) {
    this.delegate.removeAttribute(el, name, namespace);
  }
  addClass(el, name) {
    this.delegate.addClass(el, name);
  }
  removeClass(el, name) {
    this.delegate.removeClass(el, name);
  }
  setStyle(el, style, value, flags) {
    this.delegate.setStyle(el, style, value, flags);
  }
  removeStyle(el, style, flags) {
    this.delegate.removeStyle(el, style, flags);
  }
  setProperty(el, name, value) {
    if (this.shouldReplay(name)) {
      this.replay.push((renderer) => renderer.setProperty(el, name, value));
    }
    this.delegate.setProperty(el, name, value);
  }
  setValue(node, value) {
    this.delegate.setValue(node, value);
  }
  listen(target, eventName, callback, options) {
    if (this.shouldReplay(eventName)) {
      this.replay.push((renderer) => renderer.listen(target, eventName, callback, options));
    }
    return this.delegate.listen(target, eventName, callback, options);
  }
  shouldReplay(propOrEventName) {
    return this.replay !== null && propOrEventName.startsWith(ANIMATION_PREFIX);
  }
};
var \u0275ASYNC_ANIMATION_LOADING_SCHEDULER_FN = new InjectionToken(ngDevMode ? "async_animation_loading_scheduler_fn" : "");
function provideAnimationsAsync(type = "animations") {
  performanceMarkFeature("NgAsyncAnimations");
  if (false) {
    type = "noop";
  }
  return makeEnvironmentProviders([{
    provide: RendererFactory2,
    useFactory: (doc, renderer, zone) => {
      return new AsyncAnimationRendererFactory(doc, renderer, zone, type);
    },
    deps: [DOCUMENT, DomRendererFactory2, NgZone]
  }, {
    provide: ANIMATION_MODULE_TYPE,
    useValue: type === "noop" ? "NoopAnimations" : "BrowserAnimations"
  }]);
}

// src/app/guards/fournisseur.guard.ts
var FournisseurGuard = class _FournisseurGuard {
  authService;
  router;
  constructor(authService, router) {
    this.authService = authService;
    this.router = router;
  }
  canActivate(route, state) {
    return this.authService.isAuthenticated$.pipe(take(1), map((isAuthenticated) => {
      if (isAuthenticated && this.authService.isFournisseur()) {
        return true;
      } else if (isAuthenticated && this.authService.isAdmin()) {
        this.router.navigate(["/admin"]);
        return false;
      } else {
        this.router.navigate(["/login"], {
          queryParams: { returnUrl: state.url }
        });
        return false;
      }
    }));
  }
  static \u0275fac = function FournisseurGuard_Factory(__ngFactoryType__) {
    return new (__ngFactoryType__ || _FournisseurGuard)(\u0275\u0275inject(AuthService), \u0275\u0275inject(Router));
  };
  static \u0275prov = /* @__PURE__ */ \u0275\u0275defineInjectable({ token: _FournisseurGuard, factory: _FournisseurGuard.\u0275fac, providedIn: "root" });
};
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && setClassMetadata(FournisseurGuard, [{
    type: Injectable,
    args: [{
      providedIn: "root"
    }]
  }], () => [{ type: AuthService }, { type: Router }], null);
})();

// src/app/test-images.component.ts
function TestImagesComponent_div_7_Template(rf, ctx) {
  if (rf & 1) {
    const _r1 = \u0275\u0275getCurrentView();
    \u0275\u0275elementStart(0, "div", 7)(1, "h3");
    \u0275\u0275text(2);
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(3, "img", 8);
    \u0275\u0275listener("load", function TestImagesComponent_div_7_Template_img_load_3_listener() {
      const image_r2 = \u0275\u0275restoreView(_r1).$implicit;
      const ctx_r2 = \u0275\u0275nextContext();
      return \u0275\u0275resetView(ctx_r2.onImageLoad(image_r2.name));
    })("error", function TestImagesComponent_div_7_Template_img_error_3_listener($event) {
      const image_r2 = \u0275\u0275restoreView(_r1).$implicit;
      const ctx_r2 = \u0275\u0275nextContext();
      return \u0275\u0275resetView(ctx_r2.onImageError(image_r2.name, $event));
    });
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(4, "p", 9);
    \u0275\u0275text(5);
    \u0275\u0275elementEnd()();
  }
  if (rf & 2) {
    const image_r2 = ctx.$implicit;
    \u0275\u0275advance(2);
    \u0275\u0275textInterpolate(image_r2.name);
    \u0275\u0275advance();
    \u0275\u0275property("src", image_r2.url, \u0275\u0275sanitizeUrl)("alt", image_r2.name);
    \u0275\u0275advance(2);
    \u0275\u0275textInterpolate(image_r2.url);
  }
}
function TestImagesComponent_div_16_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "div", 10);
    \u0275\u0275text(1);
    \u0275\u0275elementEnd();
  }
  if (rf & 2) {
    const result_r4 = ctx.$implicit;
    \u0275\u0275styleProp("color", result_r4.success ? "green" : "red");
    \u0275\u0275advance();
    \u0275\u0275textInterpolate3(" ", result_r4.success ? "\u2705" : "\u274C", " ", result_r4.name, ": ", result_r4.message, " ");
  }
}
var TestImagesComponent = class _TestImagesComponent {
  testImages = [
    { name: "Image 1", url: "/uploads/2d8a2c0a-6b33-486e-bc3f-de8b4fc4e917.jpg" },
    { name: "Image 2", url: "/uploads/3061901f-1db5-48b9-92d3-6dbd1aa85c21.jpg" },
    { name: "Image 3", url: "/uploads/3b318ca1-8e52-4965-a93e-86e40b2de5d3.jpg" },
    { name: "Image 4", url: "/uploads/4581739a-ed87-4799-a98b-1ced05354f90.jpg" },
    { name: "Image 5", url: "/uploads/4c82526b-b29c-4618-bdd1-25ea82c7251f.jpg" }
  ];
  testResults = [];
  onImageLoad(imageName) {
    console.log(`\u2705 Image charg\xE9e avec succ\xE8s: ${imageName}`);
    this.testResults.push({
      name: imageName,
      success: true,
      message: "Charg\xE9e avec succ\xE8s"
    });
  }
  onImageError(imageName, event) {
    console.error(`\u274C Erreur de chargement: ${imageName}`, event);
    this.testResults.push({
      name: imageName,
      success: false,
      message: `Erreur: ${event.target.src}`
    });
  }
  static \u0275fac = function TestImagesComponent_Factory(__ngFactoryType__) {
    return new (__ngFactoryType__ || _TestImagesComponent)();
  };
  static \u0275cmp = /* @__PURE__ */ \u0275\u0275defineComponent({ type: _TestImagesComponent, selectors: [["app-test-images"]], decls: 17, vars: 2, consts: [[2, "padding", "2rem"], [2, "margin", "2rem 0"], [2, "display", "grid", "grid-template-columns", "repeat(auto-fit, minmax(200px, 1fr))", "gap", "1rem"], ["style", "border: 1px solid #ccc; padding: 1rem; border-radius: 8px;", 4, "ngFor", "ngForOf"], ["src", "assets/logos/suppliers/default-supplier.svg", "alt", "Logo par d\xE9faut", 2, "width", "100px", "height", "100px", "border", "1px solid #ccc", "border-radius", "8px", 3, "load", "error"], [2, "background", "#f5f5f5", "padding", "1rem", "border-radius", "8px"], ["style", "margin: 0.5rem 0;", 3, "color", 4, "ngFor", "ngForOf"], [2, "border", "1px solid #ccc", "padding", "1rem", "border-radius", "8px"], [2, "width", "100%", "height", "150px", "object-fit", "cover", "border-radius", "4px", 3, "load", "error", "src", "alt"], [2, "font-size", "0.8rem", "color", "#666", "margin-top", "0.5rem"], [2, "margin", "0.5rem 0"]], template: function TestImagesComponent_Template(rf, ctx) {
    if (rf & 1) {
      \u0275\u0275elementStart(0, "div", 0)(1, "h1");
      \u0275\u0275text(2, "\u{1F5BC}\uFE0F Test d'affichage des images");
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(3, "div", 1)(4, "h2");
      \u0275\u0275text(5, "\u{1F4F8} Images de produits depuis le backend :");
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(6, "div", 2);
      \u0275\u0275template(7, TestImagesComponent_div_7_Template, 6, 4, "div", 3);
      \u0275\u0275elementEnd()();
      \u0275\u0275elementStart(8, "div", 1)(9, "h2");
      \u0275\u0275text(10, "\u{1F3EA} Logo fournisseur par d\xE9faut :");
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(11, "img", 4);
      \u0275\u0275listener("load", function TestImagesComponent_Template_img_load_11_listener() {
        return ctx.onImageLoad("Logo par d\xE9faut");
      })("error", function TestImagesComponent_Template_img_error_11_listener($event) {
        return ctx.onImageError("Logo par d\xE9faut", $event);
      });
      \u0275\u0275elementEnd()();
      \u0275\u0275elementStart(12, "div", 1)(13, "h2");
      \u0275\u0275text(14, "\u{1F4CA} R\xE9sultats des tests :");
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(15, "div", 5);
      \u0275\u0275template(16, TestImagesComponent_div_16_Template, 2, 5, "div", 6);
      \u0275\u0275elementEnd()()();
    }
    if (rf & 2) {
      \u0275\u0275advance(7);
      \u0275\u0275property("ngForOf", ctx.testImages);
      \u0275\u0275advance(9);
      \u0275\u0275property("ngForOf", ctx.testResults);
    }
  }, dependencies: [CommonModule, NgForOf], encapsulation: 2 });
};
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && setClassMetadata(TestImagesComponent, [{
    type: Component,
    args: [{
      selector: "app-test-images",
      standalone: true,
      imports: [CommonModule],
      template: `
    <div style="padding: 2rem;">
      <h1>\u{1F5BC}\uFE0F Test d'affichage des images</h1>
      
      <div style="margin: 2rem 0;">
        <h2>\u{1F4F8} Images de produits depuis le backend :</h2>
        <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 1rem;">
          <div *ngFor="let image of testImages" style="border: 1px solid #ccc; padding: 1rem; border-radius: 8px;">
            <h3>{{ image.name }}</h3>
            <img 
              [src]="image.url" 
              [alt]="image.name"
              style="width: 100%; height: 150px; object-fit: cover; border-radius: 4px;"
              (load)="onImageLoad(image.name)"
              (error)="onImageError(image.name, $event)">
            <p style="font-size: 0.8rem; color: #666; margin-top: 0.5rem;">{{ image.url }}</p>
          </div>
        </div>
      </div>

      <div style="margin: 2rem 0;">
        <h2>\u{1F3EA} Logo fournisseur par d\xE9faut :</h2>
        <img 
          src="assets/logos/suppliers/default-supplier.svg" 
          alt="Logo par d\xE9faut"
          style="width: 100px; height: 100px; border: 1px solid #ccc; border-radius: 8px;"
          (load)="onImageLoad('Logo par d\xE9faut')"
          (error)="onImageError('Logo par d\xE9faut', $event)">
      </div>

      <div style="margin: 2rem 0;">
        <h2>\u{1F4CA} R\xE9sultats des tests :</h2>
        <div style="background: #f5f5f5; padding: 1rem; border-radius: 8px;">
          <div *ngFor="let result of testResults" 
               [style.color]="result.success ? 'green' : 'red'"
               style="margin: 0.5rem 0;">
            {{ result.success ? '\u2705' : '\u274C' }} {{ result.name }}: {{ result.message }}
          </div>
        </div>
      </div>
    </div>
  `
    }]
  }], null, null);
})();
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && \u0275setClassDebugInfo(TestImagesComponent, { className: "TestImagesComponent", filePath: "src/app/test-images.component.ts", lineNumber: 51 });
})();

// src/app/app.routes.ts
var routes = [
  {
    path: "login",
    loadComponent: () => import("./chunk-4QOETRKB.js").then((m) => m.LoginComponent)
  },
  {
    path: "register",
    loadComponent: () => import("./chunk-MKVKK6BG.js").then((m) => m.RegisterComponent)
  },
  {
    path: "adminOptiLet",
    loadComponent: () => import("./chunk-CEJLOO4X.js").then((m) => m.AdminLoginComponent),
    title: "Connexion Administrateur - OptiLet"
  },
  {
    path: "test-images",
    component: TestImagesComponent,
    title: "Test Images"
  },
  {
    path: "admin",
    loadChildren: () => import("./chunk-723FXVA7.js").then((m) => m.adminRoutes)
  },
  // Composants Angular 19 - Temporairement commentés pour résoudre les erreurs
  // {
  //   path: 'dashboard-ng19',
  //   loadComponent: () => import('./components/dashboard/dashboard-ng19.component').then(m => m.DashboardNg19Component),
  //   title: 'Dashboard Angular 19',
  //   canActivate: [AuthGuard]
  // },
  // {
  //   path: 'login-ng19',
  //   loadComponent: () => import('./components/auth/login-ng19/login-ng19.component').then(m => m.LoginNg19Component),
  //   title: 'Connexion Angular 19'
  // },
  // {
  //   path: 'angular19-nav',
  //   loadComponent: () => import('./components/navigation/angular19-nav.component').then(m => m.Angular19NavComponent),
  //   title: 'Navigation Angular 19'
  // },
  // Dashboard du fournisseur avec layout et sidebar
  {
    path: "dashboard",
    loadComponent: () => import("./chunk-LZNEVSC5.js").then((m) => m.DashboardLayoutComponent),
    canActivate: [FournisseurGuard],
    children: [
      {
        path: "",
        redirectTo: "overview",
        pathMatch: "full"
      },
      {
        path: "overview",
        loadComponent: () => import("./chunk-2COJLMM2.js").then((m) => m.DashboardComponent)
      },
      {
        path: "products",
        loadComponent: () => import("./chunk-MRU7QAL2.js").then((m) => m.ProductsComponent),
        title: "Gestion des Produits"
      },
      {
        path: "orders",
        loadComponent: () => import("./chunk-3QGRCFWL.js").then((m) => m.OrdersComponent),
        title: "Gestion des Commandes"
      },
      {
        path: "referentiels",
        loadComponent: () => import("./chunk-XV7JT4TE.js").then((m) => m.ReferentielsComponent),
        title: "Gestion des R\xE9f\xE9rentiels"
      },
      {
        path: "profil",
        loadComponent: () => import("./chunk-3SNKANDK.js").then((m) => m.ProfileComponent),
        title: "Mon Profil"
      },
      {
        path: "notifications",
        loadComponent: () => import("./chunk-PFTXOXVW.js").then((m) => m.NotificationsComponent),
        title: "Mes Notifications"
      },
      {
        path: "avis",
        loadComponent: () => import("./chunk-JD3PSUQL.js").then((m) => m.AvisFournisseurComponent),
        title: "Mes Avis Clients"
      },
      {
        path: "demandes-categories",
        loadComponent: () => import("./chunk-3I2QQWFL.js").then((m) => m.DemandeCategorieComponent),
        title: "Demandes de Cat\xE9gories"
      }
    ]
  },
  {
    path: "",
    redirectTo: "/login",
    pathMatch: "full"
  },
  {
    path: "home",
    loadComponent: () => import("./chunk-D5EM4YND.js").then((m) => m.HomeComponent),
    title: "Accueil - Plateforme Fournisseur Angular 19"
  },
  {
    path: "**",
    redirectTo: "/login"
  }
];

// src/app/app.config.ts
var authInterceptor = (req, next) => {
  const authService = inject(AuthService);
  const adminAuthService = inject(AdminAuthService);
  const isAdminRequest = req.url.includes("/admin/") || req.url.includes("/Admin/") || req.url.includes("/DemandesCategories");
  let token = null;
  if (isAdminRequest) {
    token = localStorage.getItem("admin_access_token");
    console.log("\u{1F50D} Requ\xEAte Admin d\xE9tect\xE9e:", {
      url: req.url,
      hasAdminToken: !!token,
      tokenPreview: token ? token.substring(0, 20) + "..." : "Aucun"
    });
  } else {
    token = authService.getToken();
    console.log("\u{1F50D} Requ\xEAte normale:", {
      url: req.url,
      hasToken: !!token,
      tokenPreview: token ? token.substring(0, 20) + "..." : "Aucun"
    });
  }
  if (token) {
    const authReq = req.clone({
      headers: req.headers.set("Authorization", `Bearer ${token}`)
    });
    console.log("\u2705 Token ajout\xE9 \xE0 la requ\xEAte");
    return next(authReq);
  }
  console.warn("\u26A0\uFE0F Aucun token disponible pour:", req.url);
  return next(req);
};
var appConfig = {
  providers: [
    // Angular 19: Nouvelle détection de changement optimisée
    provideZoneChangeDetection({
      eventCoalescing: true,
      runCoalescing: true
    }),
    // Angular 19: Router amélioré avec nouvelles options
    provideRouter(routes, withEnabledBlockingInitialNavigation(), withInMemoryScrolling({
      scrollPositionRestoration: "top",
      anchorScrolling: "enabled"
    }), withRouterConfig({
      onSameUrlNavigation: "reload"
    })),
    // Angular 19: HTTP Client avec fetch API et intercepteur fonctionnel
    provideHttpClient(
      withFetch(),
      // Nouvelle API fetch native
      withInterceptors([authInterceptor])
      // Intercepteur fonctionnel pour l'authentification
    ),
    // Angular 19: Animations asynchrones pour de meilleures performances
    provideAnimationsAsync()
    // Les intercepteurs legacy sont remplacés par l'intercepteur fonctionnel authInterceptor
  ]
};

// src/app/app.component.ts
var AppComponent = class _AppComponent {
  title = "pfe-fournisseur";
  static \u0275fac = function AppComponent_Factory(__ngFactoryType__) {
    return new (__ngFactoryType__ || _AppComponent)();
  };
  static \u0275cmp = /* @__PURE__ */ \u0275\u0275defineComponent({ type: _AppComponent, selectors: [["app-root"]], decls: 1, vars: 0, template: function AppComponent_Template(rf, ctx) {
    if (rf & 1) {
      \u0275\u0275element(0, "router-outlet");
    }
  }, dependencies: [RouterOutlet], encapsulation: 2 });
};
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && setClassMetadata(AppComponent, [{
    type: Component,
    args: [{ selector: "app-root", imports: [RouterOutlet], template: "<router-outlet></router-outlet>\r\n" }]
  }], null, null);
})();
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && \u0275setClassDebugInfo(AppComponent, { className: "AppComponent", filePath: "src/app/app.component.ts", lineNumber: 10 });
})();

// src/main.ts
bootstrapApplication(AppComponent, appConfig).catch((err) => console.error(err));
/*! Bundled license information:

@angular/platform-browser/fesm2022/animations/async.mjs:
  (**
   * @license Angular v19.2.14
   * (c) 2010-2025 Google LLC. https://angular.io/
   * License: MIT
   *)
*/
//# sourceMappingURL=main.js.map

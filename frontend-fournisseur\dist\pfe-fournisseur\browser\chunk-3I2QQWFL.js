import {
  CategorieService
} from "./chunk-LBLEENAN.js";
import {
  DemandeService
} from "./chunk-WQ24PYVH.js";
import {
  DefaultValueAccessor,
  FormBuilder,
  FormControlName,
  FormGroupDirective,
  FormsModule,
  MaxLengthValidator,
  NgControlStatus,
  NgControlStatusGroup,
  NgSelectOption,
  ReactiveFormsModule,
  SelectControlValueAccessor,
  Validators,
  ɵNgNoValidate,
  ɵNgSelectMultipleOption
} from "./chunk-HQBVYEOO.js";
import "./chunk-GFHHPDQ6.js";
import "./chunk-7JDDWGD3.js";
import {
  CommonModule,
  Component,
  NgForOf,
  NgIf,
  setClassMetadata,
  ɵsetClassDebugInfo,
  ɵɵadvance,
  ɵɵclassProp,
  ɵɵdefineComponent,
  ɵɵdirectiveInject,
  ɵɵelement,
  ɵɵelementEnd,
  ɵɵelementStart,
  ɵɵgetCurrentView,
  ɵɵlistener,
  ɵɵnextContext,
  ɵɵproperty,
  ɵɵresetView,
  ɵɵrestoreView,
  ɵɵstyleProp,
  ɵɵtemplate,
  ɵɵtext,
  ɵɵtextInterpolate,
  ɵɵtextInterpolate1
} from "./chunk-UBZQS7JS.js";

// src/app/components/fournisseur/demande-categorie/demande-categorie.component.ts
function DemandeCategorieComponent_span_11_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "span", 8);
    \u0275\u0275text(1);
    \u0275\u0275elementEnd();
  }
  if (rf & 2) {
    const ctx_r0 = \u0275\u0275nextContext();
    \u0275\u0275advance();
    \u0275\u0275textInterpolate(ctx_r0.totalDemandes);
  }
}
function DemandeCategorieComponent_div_12_div_6_div_6_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "div", 26);
    \u0275\u0275text(1, " Le nom est requis (max 100 caract\xE8res) ");
    \u0275\u0275elementEnd();
  }
}
function DemandeCategorieComponent_div_12_div_6_span_18_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "span");
    \u0275\u0275text(1, "\u23F3 Envoi...");
    \u0275\u0275elementEnd();
  }
}
function DemandeCategorieComponent_div_12_div_6_span_19_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "span");
    \u0275\u0275text(1, "\u{1F4E4} Envoyer la demande");
    \u0275\u0275elementEnd();
  }
}
function DemandeCategorieComponent_div_12_div_6_Template(rf, ctx) {
  if (rf & 1) {
    const _r3 = \u0275\u0275getCurrentView();
    \u0275\u0275elementStart(0, "div", 13)(1, "form", 14);
    \u0275\u0275listener("ngSubmit", function DemandeCategorieComponent_div_12_div_6_Template_form_ngSubmit_1_listener() {
      \u0275\u0275restoreView(_r3);
      const ctx_r0 = \u0275\u0275nextContext(2);
      return \u0275\u0275resetView(ctx_r0.submitDemandeCategorie());
    });
    \u0275\u0275elementStart(2, "div", 15)(3, "label", 16);
    \u0275\u0275text(4, "Nom de la cat\xE9gorie *");
    \u0275\u0275elementEnd();
    \u0275\u0275element(5, "input", 17);
    \u0275\u0275template(6, DemandeCategorieComponent_div_12_div_6_div_6_Template, 2, 0, "div", 18);
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(7, "div", 15)(8, "label", 19);
    \u0275\u0275text(9, "Description");
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(10, "textarea", 20);
    \u0275\u0275text(11, "              ");
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(12, "div", 21);
    \u0275\u0275text(13);
    \u0275\u0275elementEnd()();
    \u0275\u0275elementStart(14, "div", 22)(15, "button", 23);
    \u0275\u0275listener("click", function DemandeCategorieComponent_div_12_div_6_Template_button_click_15_listener() {
      \u0275\u0275restoreView(_r3);
      const ctx_r0 = \u0275\u0275nextContext(2);
      return \u0275\u0275resetView(ctx_r0.resetCategorieForm());
    });
    \u0275\u0275text(16, " Annuler ");
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(17, "button", 24);
    \u0275\u0275template(18, DemandeCategorieComponent_div_12_div_6_span_18_Template, 2, 0, "span", 25)(19, DemandeCategorieComponent_div_12_div_6_span_19_Template, 2, 0, "span", 25);
    \u0275\u0275elementEnd()()()();
  }
  if (rf & 2) {
    let tmp_3_0;
    let tmp_4_0;
    const ctx_r0 = \u0275\u0275nextContext(2);
    \u0275\u0275advance();
    \u0275\u0275property("formGroup", ctx_r0.categorieForm);
    \u0275\u0275advance(5);
    \u0275\u0275property("ngIf", ((tmp_3_0 = ctx_r0.categorieForm.get("nom")) == null ? null : tmp_3_0.invalid) && ((tmp_3_0 = ctx_r0.categorieForm.get("nom")) == null ? null : tmp_3_0.touched));
    \u0275\u0275advance(7);
    \u0275\u0275textInterpolate1("", ((tmp_4_0 = ctx_r0.categorieForm.get("description")) == null ? null : tmp_4_0.value == null ? null : tmp_4_0.value.length) || 0, "/500");
    \u0275\u0275advance(4);
    \u0275\u0275property("disabled", ctx_r0.categorieForm.invalid || ctx_r0.isSubmitting);
    \u0275\u0275advance();
    \u0275\u0275property("ngIf", ctx_r0.isSubmitting);
    \u0275\u0275advance();
    \u0275\u0275property("ngIf", !ctx_r0.isSubmitting);
  }
}
function DemandeCategorieComponent_div_12_div_7_option_8_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "option", 35);
    \u0275\u0275text(1);
    \u0275\u0275elementEnd();
  }
  if (rf & 2) {
    const categorie_r5 = ctx.$implicit;
    \u0275\u0275property("value", categorie_r5.id);
    \u0275\u0275advance();
    \u0275\u0275textInterpolate1(" ", categorie_r5.nom, " ");
  }
}
function DemandeCategorieComponent_div_12_div_7_div_9_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "div", 26);
    \u0275\u0275text(1, " Veuillez s\xE9lectionner une cat\xE9gorie parent ");
    \u0275\u0275elementEnd();
  }
}
function DemandeCategorieComponent_div_12_div_7_div_14_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "div", 26);
    \u0275\u0275text(1, " Le nom est requis (max 100 caract\xE8res) ");
    \u0275\u0275elementEnd();
  }
}
function DemandeCategorieComponent_div_12_div_7_span_26_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "span");
    \u0275\u0275text(1, "\u23F3 Envoi...");
    \u0275\u0275elementEnd();
  }
}
function DemandeCategorieComponent_div_12_div_7_span_27_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "span");
    \u0275\u0275text(1, "\u{1F4E4} Envoyer la demande");
    \u0275\u0275elementEnd();
  }
}
function DemandeCategorieComponent_div_12_div_7_Template(rf, ctx) {
  if (rf & 1) {
    const _r4 = \u0275\u0275getCurrentView();
    \u0275\u0275elementStart(0, "div", 13)(1, "form", 14);
    \u0275\u0275listener("ngSubmit", function DemandeCategorieComponent_div_12_div_7_Template_form_ngSubmit_1_listener() {
      \u0275\u0275restoreView(_r4);
      const ctx_r0 = \u0275\u0275nextContext(2);
      return \u0275\u0275resetView(ctx_r0.submitDemandeSousCategorie());
    });
    \u0275\u0275elementStart(2, "div", 15)(3, "label", 27);
    \u0275\u0275text(4, "Cat\xE9gorie parent *");
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(5, "select", 28)(6, "option", 29);
    \u0275\u0275text(7, "S\xE9lectionnez une cat\xE9gorie");
    \u0275\u0275elementEnd();
    \u0275\u0275template(8, DemandeCategorieComponent_div_12_div_7_option_8_Template, 2, 2, "option", 30);
    \u0275\u0275elementEnd();
    \u0275\u0275template(9, DemandeCategorieComponent_div_12_div_7_div_9_Template, 2, 0, "div", 18);
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(10, "div", 15)(11, "label", 31);
    \u0275\u0275text(12, "Nom de la sous-cat\xE9gorie *");
    \u0275\u0275elementEnd();
    \u0275\u0275element(13, "input", 32);
    \u0275\u0275template(14, DemandeCategorieComponent_div_12_div_7_div_14_Template, 2, 0, "div", 18);
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(15, "div", 15)(16, "label", 33);
    \u0275\u0275text(17, "Description");
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(18, "textarea", 34);
    \u0275\u0275text(19, "              ");
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(20, "div", 21);
    \u0275\u0275text(21);
    \u0275\u0275elementEnd()();
    \u0275\u0275elementStart(22, "div", 22)(23, "button", 23);
    \u0275\u0275listener("click", function DemandeCategorieComponent_div_12_div_7_Template_button_click_23_listener() {
      \u0275\u0275restoreView(_r4);
      const ctx_r0 = \u0275\u0275nextContext(2);
      return \u0275\u0275resetView(ctx_r0.resetSousCategorieForm());
    });
    \u0275\u0275text(24, " Annuler ");
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(25, "button", 24);
    \u0275\u0275template(26, DemandeCategorieComponent_div_12_div_7_span_26_Template, 2, 0, "span", 25)(27, DemandeCategorieComponent_div_12_div_7_span_27_Template, 2, 0, "span", 25);
    \u0275\u0275elementEnd()()()();
  }
  if (rf & 2) {
    let tmp_4_0;
    let tmp_5_0;
    let tmp_6_0;
    const ctx_r0 = \u0275\u0275nextContext(2);
    \u0275\u0275advance();
    \u0275\u0275property("formGroup", ctx_r0.sousCategorieForm);
    \u0275\u0275advance(7);
    \u0275\u0275property("ngForOf", ctx_r0.categories);
    \u0275\u0275advance();
    \u0275\u0275property("ngIf", ((tmp_4_0 = ctx_r0.sousCategorieForm.get("categorieId")) == null ? null : tmp_4_0.invalid) && ((tmp_4_0 = ctx_r0.sousCategorieForm.get("categorieId")) == null ? null : tmp_4_0.touched));
    \u0275\u0275advance(5);
    \u0275\u0275property("ngIf", ((tmp_5_0 = ctx_r0.sousCategorieForm.get("nom")) == null ? null : tmp_5_0.invalid) && ((tmp_5_0 = ctx_r0.sousCategorieForm.get("nom")) == null ? null : tmp_5_0.touched));
    \u0275\u0275advance(7);
    \u0275\u0275textInterpolate1("", ((tmp_6_0 = ctx_r0.sousCategorieForm.get("description")) == null ? null : tmp_6_0.value == null ? null : tmp_6_0.value.length) || 0, "/500");
    \u0275\u0275advance(4);
    \u0275\u0275property("disabled", ctx_r0.sousCategorieForm.invalid || ctx_r0.isSubmitting);
    \u0275\u0275advance();
    \u0275\u0275property("ngIf", ctx_r0.isSubmitting);
    \u0275\u0275advance();
    \u0275\u0275property("ngIf", !ctx_r0.isSubmitting);
  }
}
function DemandeCategorieComponent_div_12_Template(rf, ctx) {
  if (rf & 1) {
    const _r2 = \u0275\u0275getCurrentView();
    \u0275\u0275elementStart(0, "div", 9)(1, "div", 10)(2, "button", 11);
    \u0275\u0275listener("click", function DemandeCategorieComponent_div_12_Template_button_click_2_listener() {
      \u0275\u0275restoreView(_r2);
      const ctx_r0 = \u0275\u0275nextContext();
      return \u0275\u0275resetView(ctx_r0.formType = "categorie");
    });
    \u0275\u0275text(3, " \u{1F3F7}\uFE0F Demander une Cat\xE9gorie ");
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(4, "button", 11);
    \u0275\u0275listener("click", function DemandeCategorieComponent_div_12_Template_button_click_4_listener() {
      \u0275\u0275restoreView(_r2);
      const ctx_r0 = \u0275\u0275nextContext();
      ctx_r0.formType = "sous-categorie";
      return \u0275\u0275resetView(ctx_r0.loadCategories());
    });
    \u0275\u0275text(5, " \u{1F3F7}\uFE0F Demander une Sous-cat\xE9gorie ");
    \u0275\u0275elementEnd()();
    \u0275\u0275template(6, DemandeCategorieComponent_div_12_div_6_Template, 20, 6, "div", 12)(7, DemandeCategorieComponent_div_12_div_7_Template, 28, 8, "div", 12);
    \u0275\u0275elementEnd();
  }
  if (rf & 2) {
    const ctx_r0 = \u0275\u0275nextContext();
    \u0275\u0275advance(2);
    \u0275\u0275classProp("active", ctx_r0.formType === "categorie");
    \u0275\u0275advance(2);
    \u0275\u0275classProp("active", ctx_r0.formType === "sous-categorie");
    \u0275\u0275advance(2);
    \u0275\u0275property("ngIf", ctx_r0.formType === "categorie");
    \u0275\u0275advance();
    \u0275\u0275property("ngIf", ctx_r0.formType === "sous-categorie");
  }
}
function DemandeCategorieComponent_div_13_div_4_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "div", 40)(1, "p");
    \u0275\u0275text(2, "Aucune demande de cat\xE9gorie");
    \u0275\u0275elementEnd()();
  }
}
function DemandeCategorieComponent_div_13_div_6_p_7_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "p")(1, "strong");
    \u0275\u0275text(2, "Description:");
    \u0275\u0275elementEnd();
    \u0275\u0275text(3);
    \u0275\u0275elementEnd();
  }
  if (rf & 2) {
    const demande_r6 = \u0275\u0275nextContext().$implicit;
    \u0275\u0275advance(3);
    \u0275\u0275textInterpolate1(" ", demande_r6.description, "");
  }
}
function DemandeCategorieComponent_div_13_div_6_div_12_p_5_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "p")(1, "strong");
    \u0275\u0275text(2, "Commentaire:");
    \u0275\u0275elementEnd();
    \u0275\u0275text(3);
    \u0275\u0275elementEnd();
  }
  if (rf & 2) {
    const demande_r6 = \u0275\u0275nextContext(2).$implicit;
    \u0275\u0275advance(3);
    \u0275\u0275textInterpolate1(" ", demande_r6.commentaireAdmin, "");
  }
}
function DemandeCategorieComponent_div_13_div_6_div_12_p_6_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "p")(1, "strong");
    \u0275\u0275text(2, "Cat\xE9gorie cr\xE9\xE9e:");
    \u0275\u0275elementEnd();
    \u0275\u0275text(3);
    \u0275\u0275elementEnd();
  }
  if (rf & 2) {
    const demande_r6 = \u0275\u0275nextContext(2).$implicit;
    \u0275\u0275advance(3);
    \u0275\u0275textInterpolate1(" ", demande_r6.nomCategorieCreee, " ");
  }
}
function DemandeCategorieComponent_div_13_div_6_div_12_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "div", 46)(1, "p")(2, "strong");
    \u0275\u0275text(3, "Trait\xE9 le:");
    \u0275\u0275elementEnd();
    \u0275\u0275text(4);
    \u0275\u0275elementEnd();
    \u0275\u0275template(5, DemandeCategorieComponent_div_13_div_6_div_12_p_5_Template, 4, 1, "p", 25)(6, DemandeCategorieComponent_div_13_div_6_div_12_p_6_Template, 4, 1, "p", 25);
    \u0275\u0275elementEnd();
  }
  if (rf & 2) {
    const demande_r6 = \u0275\u0275nextContext().$implicit;
    const ctx_r0 = \u0275\u0275nextContext(2);
    \u0275\u0275advance(4);
    \u0275\u0275textInterpolate1(" ", ctx_r0.formatDate(demande_r6.dateTraitement), "");
    \u0275\u0275advance();
    \u0275\u0275property("ngIf", demande_r6.commentaireAdmin);
    \u0275\u0275advance();
    \u0275\u0275property("ngIf", demande_r6.statut === 1 && demande_r6.nomCategorieCreee);
  }
}
function DemandeCategorieComponent_div_13_div_6_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "div", 41)(1, "div", 42)(2, "h4");
    \u0275\u0275text(3);
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(4, "span", 43);
    \u0275\u0275text(5);
    \u0275\u0275elementEnd()();
    \u0275\u0275elementStart(6, "div", 44);
    \u0275\u0275template(7, DemandeCategorieComponent_div_13_div_6_p_7_Template, 4, 1, "p", 25);
    \u0275\u0275elementStart(8, "p")(9, "strong");
    \u0275\u0275text(10, "Demand\xE9 le:");
    \u0275\u0275elementEnd();
    \u0275\u0275text(11);
    \u0275\u0275elementEnd();
    \u0275\u0275template(12, DemandeCategorieComponent_div_13_div_6_div_12_Template, 7, 3, "div", 45);
    \u0275\u0275elementEnd()();
  }
  if (rf & 2) {
    const demande_r6 = ctx.$implicit;
    const ctx_r0 = \u0275\u0275nextContext(2);
    \u0275\u0275advance(3);
    \u0275\u0275textInterpolate(demande_r6.nom);
    \u0275\u0275advance();
    \u0275\u0275styleProp("background-color", ctx_r0.getStatutColor(demande_r6.statut));
    \u0275\u0275advance();
    \u0275\u0275textInterpolate1(" ", ctx_r0.getStatutLabel(demande_r6.statut), " ");
    \u0275\u0275advance(2);
    \u0275\u0275property("ngIf", demande_r6.description);
    \u0275\u0275advance(4);
    \u0275\u0275textInterpolate1(" ", ctx_r0.formatDate(demande_r6.dateDemande), "");
    \u0275\u0275advance();
    \u0275\u0275property("ngIf", demande_r6.statut !== 0 && demande_r6.dateTraitement);
  }
}
function DemandeCategorieComponent_div_13_div_10_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "div", 40)(1, "p");
    \u0275\u0275text(2, "Aucune demande de sous-cat\xE9gorie");
    \u0275\u0275elementEnd()();
  }
}
function DemandeCategorieComponent_div_13_div_12_p_11_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "p")(1, "strong");
    \u0275\u0275text(2, "Description:");
    \u0275\u0275elementEnd();
    \u0275\u0275text(3);
    \u0275\u0275elementEnd();
  }
  if (rf & 2) {
    const demande_r7 = \u0275\u0275nextContext().$implicit;
    \u0275\u0275advance(3);
    \u0275\u0275textInterpolate1(" ", demande_r7.description, "");
  }
}
function DemandeCategorieComponent_div_13_div_12_div_16_p_5_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "p")(1, "strong");
    \u0275\u0275text(2, "Commentaire:");
    \u0275\u0275elementEnd();
    \u0275\u0275text(3);
    \u0275\u0275elementEnd();
  }
  if (rf & 2) {
    const demande_r7 = \u0275\u0275nextContext(2).$implicit;
    \u0275\u0275advance(3);
    \u0275\u0275textInterpolate1(" ", demande_r7.commentaireAdmin, "");
  }
}
function DemandeCategorieComponent_div_13_div_12_div_16_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "div", 46)(1, "p")(2, "strong");
    \u0275\u0275text(3, "Trait\xE9 le:");
    \u0275\u0275elementEnd();
    \u0275\u0275text(4);
    \u0275\u0275elementEnd();
    \u0275\u0275template(5, DemandeCategorieComponent_div_13_div_12_div_16_p_5_Template, 4, 1, "p", 25);
    \u0275\u0275elementEnd();
  }
  if (rf & 2) {
    const demande_r7 = \u0275\u0275nextContext().$implicit;
    const ctx_r0 = \u0275\u0275nextContext(2);
    \u0275\u0275advance(4);
    \u0275\u0275textInterpolate1(" ", ctx_r0.formatDate(demande_r7.dateTraitement), "");
    \u0275\u0275advance();
    \u0275\u0275property("ngIf", demande_r7.commentaireAdmin);
  }
}
function DemandeCategorieComponent_div_13_div_12_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "div", 41)(1, "div", 42)(2, "h4");
    \u0275\u0275text(3);
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(4, "span", 43);
    \u0275\u0275text(5);
    \u0275\u0275elementEnd()();
    \u0275\u0275elementStart(6, "div", 44)(7, "p")(8, "strong");
    \u0275\u0275text(9, "Cat\xE9gorie parent:");
    \u0275\u0275elementEnd();
    \u0275\u0275text(10);
    \u0275\u0275elementEnd();
    \u0275\u0275template(11, DemandeCategorieComponent_div_13_div_12_p_11_Template, 4, 1, "p", 25);
    \u0275\u0275elementStart(12, "p")(13, "strong");
    \u0275\u0275text(14, "Demand\xE9 le:");
    \u0275\u0275elementEnd();
    \u0275\u0275text(15);
    \u0275\u0275elementEnd();
    \u0275\u0275template(16, DemandeCategorieComponent_div_13_div_12_div_16_Template, 6, 2, "div", 45);
    \u0275\u0275elementEnd()();
  }
  if (rf & 2) {
    const demande_r7 = ctx.$implicit;
    const ctx_r0 = \u0275\u0275nextContext(2);
    \u0275\u0275advance(3);
    \u0275\u0275textInterpolate(demande_r7.nom);
    \u0275\u0275advance();
    \u0275\u0275styleProp("background-color", ctx_r0.getStatutColor(demande_r7.statut));
    \u0275\u0275advance();
    \u0275\u0275textInterpolate1(" ", ctx_r0.getStatutLabel(demande_r7.statut), " ");
    \u0275\u0275advance(5);
    \u0275\u0275textInterpolate1(" ", demande_r7.categorieNom, "");
    \u0275\u0275advance();
    \u0275\u0275property("ngIf", demande_r7.description);
    \u0275\u0275advance(4);
    \u0275\u0275textInterpolate1(" ", ctx_r0.formatDate(demande_r7.dateDemande), "");
    \u0275\u0275advance();
    \u0275\u0275property("ngIf", demande_r7.statut !== 0 && demande_r7.dateTraitement);
  }
}
function DemandeCategorieComponent_div_13_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "div", 9)(1, "div", 36)(2, "h3");
    \u0275\u0275text(3, "\u{1F3F7}\uFE0F Mes demandes de cat\xE9gories");
    \u0275\u0275elementEnd();
    \u0275\u0275template(4, DemandeCategorieComponent_div_13_div_4_Template, 3, 0, "div", 37);
    \u0275\u0275elementStart(5, "div", 38);
    \u0275\u0275template(6, DemandeCategorieComponent_div_13_div_6_Template, 13, 7, "div", 39);
    \u0275\u0275elementEnd()();
    \u0275\u0275elementStart(7, "div", 36)(8, "h3");
    \u0275\u0275text(9, "\u{1F3F7}\uFE0F Mes demandes de sous-cat\xE9gories");
    \u0275\u0275elementEnd();
    \u0275\u0275template(10, DemandeCategorieComponent_div_13_div_10_Template, 3, 0, "div", 37);
    \u0275\u0275elementStart(11, "div", 38);
    \u0275\u0275template(12, DemandeCategorieComponent_div_13_div_12_Template, 17, 8, "div", 39);
    \u0275\u0275elementEnd()()();
  }
  if (rf & 2) {
    const ctx_r0 = \u0275\u0275nextContext();
    \u0275\u0275advance(4);
    \u0275\u0275property("ngIf", ctx_r0.mesDemandesCategories.length === 0);
    \u0275\u0275advance(2);
    \u0275\u0275property("ngForOf", ctx_r0.mesDemandesCategories);
    \u0275\u0275advance(4);
    \u0275\u0275property("ngIf", ctx_r0.mesDemandesSousCategories.length === 0);
    \u0275\u0275advance(2);
    \u0275\u0275property("ngForOf", ctx_r0.mesDemandesSousCategories);
  }
}
function DemandeCategorieComponent_div_14_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "div", 47);
    \u0275\u0275text(1);
    \u0275\u0275elementEnd();
  }
  if (rf & 2) {
    const ctx_r0 = \u0275\u0275nextContext();
    \u0275\u0275advance();
    \u0275\u0275textInterpolate1(" ", ctx_r0.successMessage, " ");
  }
}
function DemandeCategorieComponent_div_15_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "div", 48);
    \u0275\u0275text(1);
    \u0275\u0275elementEnd();
  }
  if (rf & 2) {
    const ctx_r0 = \u0275\u0275nextContext();
    \u0275\u0275advance();
    \u0275\u0275textInterpolate1(" ", ctx_r0.errorMessage, " ");
  }
}
var DemandeCategorieComponent = class _DemandeCategorieComponent {
  fb;
  demandeService;
  categorieService;
  activeTab = "nouvelle-demande";
  formType = "categorie";
  categorieForm;
  sousCategorieForm;
  categories = [];
  mesDemandesCategories = [];
  mesDemandesSousCategories = [];
  isSubmitting = false;
  successMessage = "";
  errorMessage = "";
  constructor(fb, demandeService, categorieService) {
    this.fb = fb;
    this.demandeService = demandeService;
    this.categorieService = categorieService;
    this.categorieForm = this.fb.group({
      nom: ["", [Validators.required, Validators.maxLength(100)]],
      description: ["", [Validators.maxLength(500)]]
    });
    this.sousCategorieForm = this.fb.group({
      categorieId: ["", [Validators.required]],
      nom: ["", [Validators.required, Validators.maxLength(100)]],
      description: ["", [Validators.maxLength(500)]]
    });
  }
  ngOnInit() {
    console.log("\u{1F504} Initialisation du composant demande-categorie");
    this.loadMesDemandesCategories();
    this.loadMesDemandesSousCategories();
    this.loadCategories();
  }
  get totalDemandes() {
    return this.mesDemandesCategories.length + this.mesDemandesSousCategories.length;
  }
  loadCategories() {
    this.categorieService.getAll().subscribe({
      next: (categories) => {
        this.categories = categories;
      },
      error: (error) => {
        console.error("Erreur lors du chargement des cat\xE9gories:", error);
      }
    });
  }
  loadMesDemandesCategories() {
    console.log("\u{1F504} Chargement des demandes de cat\xE9gories...");
    this.demandeService.getMesDemandesCategories().subscribe({
      next: (demandes) => {
        console.log("\u2705 Demandes de cat\xE9gories re\xE7ues:", demandes);
        this.mesDemandesCategories = demandes;
        console.log("\u{1F4CA} Nombre de demandes de cat\xE9gories:", this.mesDemandesCategories.length);
      },
      error: (error) => {
        console.error("\u274C Erreur lors du chargement des demandes de cat\xE9gories:", error);
      }
    });
  }
  loadMesDemandesSousCategories() {
    console.log("\u{1F504} Chargement des demandes de sous-cat\xE9gories...");
    this.demandeService.getMesDemandesSousCategories().subscribe({
      next: (demandes) => {
        console.log("\u2705 Demandes de sous-cat\xE9gories re\xE7ues:", demandes);
        this.mesDemandesSousCategories = demandes;
        console.log("\u{1F4CA} Nombre de demandes de sous-cat\xE9gories:", this.mesDemandesSousCategories.length);
      },
      error: (error) => {
        console.error("\u274C Erreur lors du chargement des demandes de sous-cat\xE9gories:", error);
      }
    });
  }
  submitDemandeCategorie() {
    console.log("\u{1F50D} submitDemandeCategorie appel\xE9");
    console.log("\u{1F4DD} Formulaire valide:", this.categorieForm.valid);
    console.log("\u{1F4DD} Valeurs du formulaire:", this.categorieForm.value);
    this.categorieForm.markAllAsTouched();
    const nom = this.categorieForm.get("nom")?.value?.trim();
    const description = this.categorieForm.get("description")?.value?.trim();
    console.log("\u{1F4DD} Nom:", nom);
    console.log("\u{1F4DD} Description:", description);
    if (!nom) {
      console.log("\u274C Nom manquant");
      this.errorMessage = "Le nom de la cat\xE9gorie est requis.";
      return;
    }
    if (this.isSubmitting) {
      console.log("\u274C Soumission d\xE9j\xE0 en cours");
      return;
    }
    console.log("\u2705 Conditions remplies, envoi de la demande...");
    this.isSubmitting = true;
    this.clearMessages();
    const demande = {
      nom,
      description: description || ""
    };
    console.log("\u{1F4E4} Demande \xE0 envoyer:", demande);
    console.log("\u{1F680} Appel du service createDemandeCategorie...");
    this.demandeService.createDemandeCategorie(demande).subscribe({
      next: (response) => {
        console.log("\u2705 R\xE9ponse re\xE7ue:", response);
        this.successMessage = "Votre demande de cat\xE9gorie a \xE9t\xE9 envoy\xE9e avec succ\xE8s !";
        this.resetCategorieForm();
        this.isSubmitting = false;
        setTimeout(() => {
          window.location.reload();
        }, 2e3);
      },
      error: (error) => {
        console.log("\u274C Erreur re\xE7ue:", error);
        this.errorMessage = "Erreur lors de l'envoi de la demande. Veuillez r\xE9essayer.";
        this.isSubmitting = false;
      }
    });
  }
  submitDemandeSousCategorie() {
    if (this.sousCategorieForm.valid && !this.isSubmitting) {
      this.isSubmitting = true;
      this.clearMessages();
      const demande = {
        nom: this.sousCategorieForm.value.nom,
        description: this.sousCategorieForm.value.description || "",
        categorieId: parseInt(this.sousCategorieForm.value.categorieId)
      };
      this.demandeService.createDemandeSousCategorie(demande).subscribe({
        next: (response) => {
          this.successMessage = "Votre demande de sous-cat\xE9gorie a \xE9t\xE9 envoy\xE9e avec succ\xE8s !";
          this.resetSousCategorieForm();
          this.isSubmitting = false;
        },
        error: (error) => {
          this.errorMessage = "Erreur lors de l'envoi de la demande. Veuillez r\xE9essayer.";
          this.isSubmitting = false;
        }
      });
    }
  }
  resetCategorieForm() {
    this.categorieForm.reset();
    this.clearMessages();
  }
  resetSousCategorieForm() {
    this.sousCategorieForm.reset();
    this.clearMessages();
  }
  clearMessages() {
    this.successMessage = "";
    this.errorMessage = "";
  }
  getStatutLabel(statut) {
    return this.demandeService.getStatutLabel(statut);
  }
  getStatutColor(statut) {
    return this.demandeService.getStatutColor(statut);
  }
  formatDate(date) {
    return new Date(date).toLocaleDateString("fr-FR", {
      year: "numeric",
      month: "long",
      day: "numeric",
      hour: "2-digit",
      minute: "2-digit"
    });
  }
  static \u0275fac = function DemandeCategorieComponent_Factory(__ngFactoryType__) {
    return new (__ngFactoryType__ || _DemandeCategorieComponent)(\u0275\u0275directiveInject(FormBuilder), \u0275\u0275directiveInject(DemandeService), \u0275\u0275directiveInject(CategorieService));
  };
  static \u0275cmp = /* @__PURE__ */ \u0275\u0275defineComponent({ type: _DemandeCategorieComponent, selectors: [["app-demande-categorie"]], decls: 16, vars: 9, consts: [[1, "demande-categorie"], [1, "page-header"], [1, "tabs"], [1, "tab-btn", 3, "click"], ["class", "badge", 4, "ngIf"], ["class", "tab-content", 4, "ngIf"], ["class", "alert alert-success", 4, "ngIf"], ["class", "alert alert-error", 4, "ngIf"], [1, "badge"], [1, "tab-content"], [1, "form-tabs"], [1, "form-tab-btn", 3, "click"], ["class", "form-container", 4, "ngIf"], [1, "form-container"], [3, "ngSubmit", "formGroup"], [1, "form-group"], ["for", "nom"], ["type", "text", "id", "nom", "formControlName", "nom", "placeholder", "Ex: Lunettes de soleil premium", "maxlength", "100"], ["class", "error", 4, "ngIf"], ["for", "description"], ["id", "description", "formControlName", "description", "placeholder", "D\xE9crivez la cat\xE9gorie que vous souhaitez cr\xE9er...", "rows", "4", "maxlength", "500"], [1, "char-count"], [1, "form-actions"], ["type", "button", 1, "btn", "btn-secondary", 3, "click"], ["type", "submit", 1, "btn", "btn-primary", 3, "disabled"], [4, "ngIf"], [1, "error"], ["for", "categorieId"], ["id", "categorieId", "formControlName", "categorieId"], ["value", ""], [3, "value", 4, "ngFor", "ngForOf"], ["for", "nomSous"], ["type", "text", "id", "nomSous", "formControlName", "nom", "placeholder", "Ex: Lunettes polaris\xE9es", "maxlength", "100"], ["for", "descriptionSous"], ["id", "descriptionSous", "formControlName", "description", "placeholder", "D\xE9crivez la sous-cat\xE9gorie que vous souhaitez cr\xE9er...", "rows", "4", "maxlength", "500"], [3, "value"], [1, "demandes-section"], ["class", "empty-state", 4, "ngIf"], [1, "demandes-grid"], ["class", "demande-card", 4, "ngFor", "ngForOf"], [1, "empty-state"], [1, "demande-card"], [1, "demande-header"], [1, "statut-badge"], [1, "demande-content"], ["class", "traitement-info", 4, "ngIf"], [1, "traitement-info"], [1, "alert", "alert-success"], [1, "alert", "alert-error"]], template: function DemandeCategorieComponent_Template(rf, ctx) {
    if (rf & 1) {
      \u0275\u0275elementStart(0, "div", 0)(1, "div", 1)(2, "h1");
      \u0275\u0275text(3, "\u{1F4DD} Demandes de Cat\xE9gories et Sous-cat\xE9gories");
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(4, "p");
      \u0275\u0275text(5, "Demandez la cr\xE9ation de nouvelles cat\xE9gories ou sous-cat\xE9gories");
      \u0275\u0275elementEnd()();
      \u0275\u0275elementStart(6, "div", 2)(7, "button", 3);
      \u0275\u0275listener("click", function DemandeCategorieComponent_Template_button_click_7_listener() {
        return ctx.activeTab = "nouvelle-demande";
      });
      \u0275\u0275text(8, " \u2795 Nouvelle Demande ");
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(9, "button", 3);
      \u0275\u0275listener("click", function DemandeCategorieComponent_Template_button_click_9_listener() {
        ctx.activeTab = "mes-demandes";
        ctx.loadMesDemandesCategories();
        return ctx.loadMesDemandesSousCategories();
      });
      \u0275\u0275text(10, " \u{1F4CB} Mes Demandes ");
      \u0275\u0275template(11, DemandeCategorieComponent_span_11_Template, 2, 1, "span", 4);
      \u0275\u0275elementEnd()();
      \u0275\u0275template(12, DemandeCategorieComponent_div_12_Template, 8, 6, "div", 5)(13, DemandeCategorieComponent_div_13_Template, 13, 4, "div", 5)(14, DemandeCategorieComponent_div_14_Template, 2, 1, "div", 6)(15, DemandeCategorieComponent_div_15_Template, 2, 1, "div", 7);
      \u0275\u0275elementEnd();
    }
    if (rf & 2) {
      \u0275\u0275advance(7);
      \u0275\u0275classProp("active", ctx.activeTab === "nouvelle-demande");
      \u0275\u0275advance(2);
      \u0275\u0275classProp("active", ctx.activeTab === "mes-demandes");
      \u0275\u0275advance(2);
      \u0275\u0275property("ngIf", ctx.totalDemandes > 0);
      \u0275\u0275advance();
      \u0275\u0275property("ngIf", ctx.activeTab === "nouvelle-demande");
      \u0275\u0275advance();
      \u0275\u0275property("ngIf", ctx.activeTab === "mes-demandes");
      \u0275\u0275advance();
      \u0275\u0275property("ngIf", ctx.successMessage);
      \u0275\u0275advance();
      \u0275\u0275property("ngIf", ctx.errorMessage);
    }
  }, dependencies: [CommonModule, NgForOf, NgIf, FormsModule, \u0275NgNoValidate, NgSelectOption, \u0275NgSelectMultipleOption, DefaultValueAccessor, SelectControlValueAccessor, NgControlStatus, NgControlStatusGroup, MaxLengthValidator, ReactiveFormsModule, FormGroupDirective, FormControlName], styles: ["\n\n.demande-categorie[_ngcontent-%COMP%] {\n  padding: 2rem;\n  max-width: 1200px;\n  margin: 0 auto;\n}\n.page-header[_ngcontent-%COMP%] {\n  margin-bottom: 2rem;\n}\n.page-header[_ngcontent-%COMP%]   h1[_ngcontent-%COMP%] {\n  margin: 0 0 0.5rem 0;\n  color: #1e293b;\n}\n.page-header[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\n  margin: 0;\n  color: #64748b;\n}\n.tabs[_ngcontent-%COMP%] {\n  display: flex;\n  border-bottom: 2px solid #e2e8f0;\n  margin-bottom: 2rem;\n}\n.tab-btn[_ngcontent-%COMP%] {\n  padding: 1rem 2rem;\n  background: none;\n  border: none;\n  cursor: pointer;\n  font-size: 1rem;\n  color: #64748b;\n  border-bottom: 2px solid transparent;\n  transition: all 0.3s ease;\n  position: relative;\n}\n.tab-btn.active[_ngcontent-%COMP%] {\n  color: #3b82f6;\n  border-bottom-color: #3b82f6;\n}\n.badge[_ngcontent-%COMP%] {\n  background: #ef4444;\n  color: white;\n  border-radius: 50%;\n  padding: 0.25rem 0.5rem;\n  font-size: 0.75rem;\n  margin-left: 0.5rem;\n}\n.form-tabs[_ngcontent-%COMP%] {\n  display: flex;\n  gap: 1rem;\n  margin-bottom: 2rem;\n}\n.form-tab-btn[_ngcontent-%COMP%] {\n  padding: 0.75rem 1.5rem;\n  background: #f8fafc;\n  border: 1px solid #e2e8f0;\n  border-radius: 8px;\n  cursor: pointer;\n  transition: all 0.3s ease;\n}\n.form-tab-btn.active[_ngcontent-%COMP%] {\n  background: #3b82f6;\n  color: white;\n  border-color: #3b82f6;\n}\n.form-container[_ngcontent-%COMP%] {\n  background: white;\n  padding: 2rem;\n  border-radius: 12px;\n  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);\n  border: 1px solid #e2e8f0;\n}\n.form-group[_ngcontent-%COMP%] {\n  margin-bottom: 1.5rem;\n}\n.form-group[_ngcontent-%COMP%]   label[_ngcontent-%COMP%] {\n  display: block;\n  margin-bottom: 0.5rem;\n  font-weight: 500;\n  color: #374151;\n}\n.form-group[_ngcontent-%COMP%]   input[_ngcontent-%COMP%], \n.form-group[_ngcontent-%COMP%]   select[_ngcontent-%COMP%], \n.form-group[_ngcontent-%COMP%]   textarea[_ngcontent-%COMP%] {\n  width: 100%;\n  padding: 0.75rem;\n  border: 1px solid #d1d5db;\n  border-radius: 8px;\n  font-size: 1rem;\n  transition: border-color 0.3s ease;\n}\n.form-group[_ngcontent-%COMP%]   input[_ngcontent-%COMP%]:focus, \n.form-group[_ngcontent-%COMP%]   select[_ngcontent-%COMP%]:focus, \n.form-group[_ngcontent-%COMP%]   textarea[_ngcontent-%COMP%]:focus {\n  outline: none;\n  border-color: #3b82f6;\n  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);\n}\n.char-count[_ngcontent-%COMP%] {\n  text-align: right;\n  font-size: 0.875rem;\n  color: #6b7280;\n  margin-top: 0.25rem;\n}\n.error[_ngcontent-%COMP%] {\n  color: #ef4444;\n  font-size: 0.875rem;\n  margin-top: 0.25rem;\n}\n.form-actions[_ngcontent-%COMP%] {\n  display: flex;\n  gap: 1rem;\n  justify-content: flex-end;\n  margin-top: 2rem;\n}\n.btn[_ngcontent-%COMP%] {\n  padding: 0.75rem 1.5rem;\n  border: none;\n  border-radius: 8px;\n  cursor: pointer;\n  font-size: 1rem;\n  transition: all 0.3s ease;\n}\n.btn-primary[_ngcontent-%COMP%] {\n  background: #3b82f6;\n  color: white;\n}\n.btn-primary[_ngcontent-%COMP%]:hover:not(:disabled) {\n  background: #2563eb;\n}\n.btn-primary[_ngcontent-%COMP%]:disabled {\n  background: #9ca3af;\n  cursor: not-allowed;\n}\n.btn-secondary[_ngcontent-%COMP%] {\n  background: #6b7280;\n  color: white;\n}\n.btn-secondary[_ngcontent-%COMP%]:hover {\n  background: #4b5563;\n}\n.demandes-section[_ngcontent-%COMP%] {\n  margin-bottom: 3rem;\n}\n.demandes-section[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%] {\n  margin-bottom: 1rem;\n  color: #1e293b;\n}\n.empty-state[_ngcontent-%COMP%] {\n  text-align: center;\n  padding: 2rem;\n  color: #64748b;\n  background: #f8fafc;\n  border-radius: 8px;\n  margin-bottom: 2rem;\n}\n.demandes-grid[_ngcontent-%COMP%] {\n  display: grid;\n  grid-template-columns: repeat(auto-fill, minmax(400px, 1fr));\n  gap: 1.5rem;\n}\n.demande-card[_ngcontent-%COMP%] {\n  background: white;\n  border-radius: 12px;\n  padding: 1.5rem;\n  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);\n  border: 1px solid #e2e8f0;\n}\n.demande-header[_ngcontent-%COMP%] {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 1rem;\n}\n.demande-header[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%] {\n  margin: 0;\n  color: #1e293b;\n}\n.statut-badge[_ngcontent-%COMP%] {\n  padding: 0.25rem 0.75rem;\n  border-radius: 20px;\n  color: white;\n  font-size: 0.875rem;\n  font-weight: 500;\n}\n.demande-content[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\n  margin: 0.5rem 0;\n  color: #475569;\n}\n.traitement-info[_ngcontent-%COMP%] {\n  margin-top: 1rem;\n  padding-top: 1rem;\n  border-top: 1px solid #e2e8f0;\n  background: #f8fafc;\n  padding: 1rem;\n  border-radius: 8px;\n}\n.alert[_ngcontent-%COMP%] {\n  padding: 1rem;\n  border-radius: 8px;\n  margin-top: 1rem;\n}\n.alert-success[_ngcontent-%COMP%] {\n  background: #dcfce7;\n  color: #166534;\n  border: 1px solid #bbf7d0;\n}\n.alert-error[_ngcontent-%COMP%] {\n  background: #fef2f2;\n  color: #dc2626;\n  border: 1px solid #fecaca;\n}\n/*# sourceMappingURL=demande-categorie.component.css.map */"] });
};
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && setClassMetadata(DemandeCategorieComponent, [{
    type: Component,
    args: [{ selector: "app-demande-categorie", standalone: true, imports: [CommonModule, FormsModule, ReactiveFormsModule], template: `
    <div class="demande-categorie">
      <div class="page-header">
        <h1>\u{1F4DD} Demandes de Cat\xE9gories et Sous-cat\xE9gories</h1>
        <p>Demandez la cr\xE9ation de nouvelles cat\xE9gories ou sous-cat\xE9gories</p>
      </div>

      <div class="tabs">
        <button 
          class="tab-btn" 
          [class.active]="activeTab === 'nouvelle-demande'"
          (click)="activeTab = 'nouvelle-demande'">
          \u2795 Nouvelle Demande
        </button>
        <button 
          class="tab-btn" 
          [class.active]="activeTab === 'mes-demandes'"
          (click)="activeTab = 'mes-demandes'; loadMesDemandesCategories(); loadMesDemandesSousCategories()">
          \u{1F4CB} Mes Demandes
          <span *ngIf="totalDemandes > 0" class="badge">{{ totalDemandes }}</span>
        </button>
      </div>

      <!-- Nouvelle demande -->
      <div *ngIf="activeTab === 'nouvelle-demande'" class="tab-content">
        <div class="form-tabs">
          <button 
            class="form-tab-btn" 
            [class.active]="formType === 'categorie'"
            (click)="formType = 'categorie'">
            \u{1F3F7}\uFE0F Demander une Cat\xE9gorie
          </button>
          <button 
            class="form-tab-btn" 
            [class.active]="formType === 'sous-categorie'"
            (click)="formType = 'sous-categorie'; loadCategories()">
            \u{1F3F7}\uFE0F Demander une Sous-cat\xE9gorie
          </button>
        </div>

        <!-- Formulaire cat\xE9gorie -->
        <div *ngIf="formType === 'categorie'" class="form-container">
          <form [formGroup]="categorieForm" (ngSubmit)="submitDemandeCategorie()">
            <div class="form-group">
              <label for="nom">Nom de la cat\xE9gorie *</label>
              <input 
                type="text" 
                id="nom" 
                formControlName="nom" 
                placeholder="Ex: Lunettes de soleil premium"
                maxlength="100">
              <div *ngIf="categorieForm.get('nom')?.invalid && categorieForm.get('nom')?.touched" class="error">
                Le nom est requis (max 100 caract\xE8res)
              </div>
            </div>

            <div class="form-group">
              <label for="description">Description</label>
              <textarea 
                id="description" 
                formControlName="description" 
                placeholder="D\xE9crivez la cat\xE9gorie que vous souhaitez cr\xE9er..."
                rows="4"
                maxlength="500">
              </textarea>
              <div class="char-count">{{ categorieForm.get('description')?.value?.length || 0 }}/500</div>
            </div>

            <div class="form-actions">
              <button type="button" class="btn btn-secondary" (click)="resetCategorieForm()">
                Annuler
              </button>
              <button type="submit" class="btn btn-primary" [disabled]="categorieForm.invalid || isSubmitting">
                <span *ngIf="isSubmitting">\u23F3 Envoi...</span>
                <span *ngIf="!isSubmitting">\u{1F4E4} Envoyer la demande</span>
              </button>
            </div>
          </form>
        </div>

        <!-- Formulaire sous-cat\xE9gorie -->
        <div *ngIf="formType === 'sous-categorie'" class="form-container">
          <form [formGroup]="sousCategorieForm" (ngSubmit)="submitDemandeSousCategorie()">
            <div class="form-group">
              <label for="categorieId">Cat\xE9gorie parent *</label>
              <select id="categorieId" formControlName="categorieId">
                <option value="">S\xE9lectionnez une cat\xE9gorie</option>
                <option *ngFor="let categorie of categories" [value]="categorie.id">
                  {{ categorie.nom }}
                </option>
              </select>
              <div *ngIf="sousCategorieForm.get('categorieId')?.invalid && sousCategorieForm.get('categorieId')?.touched" class="error">
                Veuillez s\xE9lectionner une cat\xE9gorie parent
              </div>
            </div>

            <div class="form-group">
              <label for="nomSous">Nom de la sous-cat\xE9gorie *</label>
              <input 
                type="text" 
                id="nomSous" 
                formControlName="nom" 
                placeholder="Ex: Lunettes polaris\xE9es"
                maxlength="100">
              <div *ngIf="sousCategorieForm.get('nom')?.invalid && sousCategorieForm.get('nom')?.touched" class="error">
                Le nom est requis (max 100 caract\xE8res)
              </div>
            </div>

            <div class="form-group">
              <label for="descriptionSous">Description</label>
              <textarea 
                id="descriptionSous" 
                formControlName="description" 
                placeholder="D\xE9crivez la sous-cat\xE9gorie que vous souhaitez cr\xE9er..."
                rows="4"
                maxlength="500">
              </textarea>
              <div class="char-count">{{ sousCategorieForm.get('description')?.value?.length || 0 }}/500</div>
            </div>

            <div class="form-actions">
              <button type="button" class="btn btn-secondary" (click)="resetSousCategorieForm()">
                Annuler
              </button>
              <button type="submit" class="btn btn-primary" [disabled]="sousCategorieForm.invalid || isSubmitting">
                <span *ngIf="isSubmitting">\u23F3 Envoi...</span>
                <span *ngIf="!isSubmitting">\u{1F4E4} Envoyer la demande</span>
              </button>
            </div>
          </form>
        </div>
      </div>

      <!-- Mes demandes -->
      <div *ngIf="activeTab === 'mes-demandes'" class="tab-content">
        <div class="demandes-section">
          <h3>\u{1F3F7}\uFE0F Mes demandes de cat\xE9gories</h3>
          <div *ngIf="mesDemandesCategories.length === 0" class="empty-state">
            <p>Aucune demande de cat\xE9gorie</p>
          </div>
          <div class="demandes-grid">
            <div *ngFor="let demande of mesDemandesCategories" class="demande-card">
              <div class="demande-header">
                <h4>{{ demande.nom }}</h4>
                <span class="statut-badge" [style.background-color]="getStatutColor(demande.statut)">
                  {{ getStatutLabel(demande.statut) }}
                </span>
              </div>
              <div class="demande-content">
                <p *ngIf="demande.description"><strong>Description:</strong> {{ demande.description }}</p>
                <p><strong>Demand\xE9 le:</strong> {{ formatDate(demande.dateDemande) }}</p>
                <div *ngIf="demande.statut !== 0 && demande.dateTraitement" class="traitement-info">
                  <p><strong>Trait\xE9 le:</strong> {{ formatDate(demande.dateTraitement) }}</p>
                  <p *ngIf="demande.commentaireAdmin"><strong>Commentaire:</strong> {{ demande.commentaireAdmin }}</p>
                  <p *ngIf="demande.statut === 1 && demande.nomCategorieCreee">
                    <strong>Cat\xE9gorie cr\xE9\xE9e:</strong> {{ demande.nomCategorieCreee }}
                  </p>
                </div>
              </div>
            </div>
          </div>
        </div>

        <div class="demandes-section">
          <h3>\u{1F3F7}\uFE0F Mes demandes de sous-cat\xE9gories</h3>
          <div *ngIf="mesDemandesSousCategories.length === 0" class="empty-state">
            <p>Aucune demande de sous-cat\xE9gorie</p>
          </div>
          <div class="demandes-grid">
            <div *ngFor="let demande of mesDemandesSousCategories" class="demande-card">
              <div class="demande-header">
                <h4>{{ demande.nom }}</h4>
                <span class="statut-badge" [style.background-color]="getStatutColor(demande.statut)">
                  {{ getStatutLabel(demande.statut) }}
                </span>
              </div>
              <div class="demande-content">
                <p><strong>Cat\xE9gorie parent:</strong> {{ demande.categorieNom }}</p>
                <p *ngIf="demande.description"><strong>Description:</strong> {{ demande.description }}</p>
                <p><strong>Demand\xE9 le:</strong> {{ formatDate(demande.dateDemande) }}</p>
                <div *ngIf="demande.statut !== 0 && demande.dateTraitement" class="traitement-info">
                  <p><strong>Trait\xE9 le:</strong> {{ formatDate(demande.dateTraitement) }}</p>
                  <p *ngIf="demande.commentaireAdmin"><strong>Commentaire:</strong> {{ demande.commentaireAdmin }}</p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Messages de succ\xE8s/erreur -->
      <div *ngIf="successMessage" class="alert alert-success">
        {{ successMessage }}
      </div>
      <div *ngIf="errorMessage" class="alert alert-error">
        {{ errorMessage }}
      </div>
    </div>
  `, styles: ["/* angular:styles/component:css;5434fe34c30caa7f4de433b4370c725c63c41934f20e026d0e4f129fd459f42a;C:/Users/<USER>/Desktop/STE/PFE/PFE/frontend-fournisseur/src/app/components/fournisseur/demande-categorie/demande-categorie.component.ts */\n.demande-categorie {\n  padding: 2rem;\n  max-width: 1200px;\n  margin: 0 auto;\n}\n.page-header {\n  margin-bottom: 2rem;\n}\n.page-header h1 {\n  margin: 0 0 0.5rem 0;\n  color: #1e293b;\n}\n.page-header p {\n  margin: 0;\n  color: #64748b;\n}\n.tabs {\n  display: flex;\n  border-bottom: 2px solid #e2e8f0;\n  margin-bottom: 2rem;\n}\n.tab-btn {\n  padding: 1rem 2rem;\n  background: none;\n  border: none;\n  cursor: pointer;\n  font-size: 1rem;\n  color: #64748b;\n  border-bottom: 2px solid transparent;\n  transition: all 0.3s ease;\n  position: relative;\n}\n.tab-btn.active {\n  color: #3b82f6;\n  border-bottom-color: #3b82f6;\n}\n.badge {\n  background: #ef4444;\n  color: white;\n  border-radius: 50%;\n  padding: 0.25rem 0.5rem;\n  font-size: 0.75rem;\n  margin-left: 0.5rem;\n}\n.form-tabs {\n  display: flex;\n  gap: 1rem;\n  margin-bottom: 2rem;\n}\n.form-tab-btn {\n  padding: 0.75rem 1.5rem;\n  background: #f8fafc;\n  border: 1px solid #e2e8f0;\n  border-radius: 8px;\n  cursor: pointer;\n  transition: all 0.3s ease;\n}\n.form-tab-btn.active {\n  background: #3b82f6;\n  color: white;\n  border-color: #3b82f6;\n}\n.form-container {\n  background: white;\n  padding: 2rem;\n  border-radius: 12px;\n  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);\n  border: 1px solid #e2e8f0;\n}\n.form-group {\n  margin-bottom: 1.5rem;\n}\n.form-group label {\n  display: block;\n  margin-bottom: 0.5rem;\n  font-weight: 500;\n  color: #374151;\n}\n.form-group input,\n.form-group select,\n.form-group textarea {\n  width: 100%;\n  padding: 0.75rem;\n  border: 1px solid #d1d5db;\n  border-radius: 8px;\n  font-size: 1rem;\n  transition: border-color 0.3s ease;\n}\n.form-group input:focus,\n.form-group select:focus,\n.form-group textarea:focus {\n  outline: none;\n  border-color: #3b82f6;\n  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);\n}\n.char-count {\n  text-align: right;\n  font-size: 0.875rem;\n  color: #6b7280;\n  margin-top: 0.25rem;\n}\n.error {\n  color: #ef4444;\n  font-size: 0.875rem;\n  margin-top: 0.25rem;\n}\n.form-actions {\n  display: flex;\n  gap: 1rem;\n  justify-content: flex-end;\n  margin-top: 2rem;\n}\n.btn {\n  padding: 0.75rem 1.5rem;\n  border: none;\n  border-radius: 8px;\n  cursor: pointer;\n  font-size: 1rem;\n  transition: all 0.3s ease;\n}\n.btn-primary {\n  background: #3b82f6;\n  color: white;\n}\n.btn-primary:hover:not(:disabled) {\n  background: #2563eb;\n}\n.btn-primary:disabled {\n  background: #9ca3af;\n  cursor: not-allowed;\n}\n.btn-secondary {\n  background: #6b7280;\n  color: white;\n}\n.btn-secondary:hover {\n  background: #4b5563;\n}\n.demandes-section {\n  margin-bottom: 3rem;\n}\n.demandes-section h3 {\n  margin-bottom: 1rem;\n  color: #1e293b;\n}\n.empty-state {\n  text-align: center;\n  padding: 2rem;\n  color: #64748b;\n  background: #f8fafc;\n  border-radius: 8px;\n  margin-bottom: 2rem;\n}\n.demandes-grid {\n  display: grid;\n  grid-template-columns: repeat(auto-fill, minmax(400px, 1fr));\n  gap: 1.5rem;\n}\n.demande-card {\n  background: white;\n  border-radius: 12px;\n  padding: 1.5rem;\n  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);\n  border: 1px solid #e2e8f0;\n}\n.demande-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 1rem;\n}\n.demande-header h4 {\n  margin: 0;\n  color: #1e293b;\n}\n.statut-badge {\n  padding: 0.25rem 0.75rem;\n  border-radius: 20px;\n  color: white;\n  font-size: 0.875rem;\n  font-weight: 500;\n}\n.demande-content p {\n  margin: 0.5rem 0;\n  color: #475569;\n}\n.traitement-info {\n  margin-top: 1rem;\n  padding-top: 1rem;\n  border-top: 1px solid #e2e8f0;\n  background: #f8fafc;\n  padding: 1rem;\n  border-radius: 8px;\n}\n.alert {\n  padding: 1rem;\n  border-radius: 8px;\n  margin-top: 1rem;\n}\n.alert-success {\n  background: #dcfce7;\n  color: #166534;\n  border: 1px solid #bbf7d0;\n}\n.alert-error {\n  background: #fef2f2;\n  color: #dc2626;\n  border: 1px solid #fecaca;\n}\n/*# sourceMappingURL=demande-categorie.component.css.map */\n"] }]
  }], () => [{ type: FormBuilder }, { type: DemandeService }, { type: CategorieService }], null);
})();
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && \u0275setClassDebugInfo(DemandeCategorieComponent, { className: "DemandeCategorieComponent", filePath: "src/app/components/fournisseur/demande-categorie/demande-categorie.component.ts", lineNumber: 461 });
})();
export {
  DemandeCategorieComponent
};
//# sourceMappingURL=chunk-3I2QQWFL.js.map

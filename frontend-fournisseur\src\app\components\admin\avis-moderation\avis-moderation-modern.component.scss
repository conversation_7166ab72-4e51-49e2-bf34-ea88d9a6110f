// Styles modernes pour la modération des avis
@use 'sass:color';
@import '../../../../styles.scss';

// Variables de couleurs
$gradient-primary: linear-gradient(135deg, #{$primary-color}, #{color.adjust($primary-color, $lightness: 10%)});
$shadow-light: 0 2px 10px rgba(0, 0, 0, 0.08);
$shadow-medium: 0 4px 20px rgba(0, 0, 0, 0.12);
$shadow-heavy: 0 8px 30px rgba(0, 0, 0, 0.15);
$border-radius: 12px;
$transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);

// Container principal
.container-fluid {
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  min-height: 100vh;
  padding: 2rem;

  @media (max-width: 768px) {
    padding: 1rem;
  }
}

// En-tête moderne
.page-header {
  background: $gradient-primary;
  border-radius: $border-radius;
  padding: 2rem;
  margin-bottom: 2rem;
  box-shadow: $shadow-medium;
  color: white;

  .header-content {
    display: flex;
    justify-content: space-between;
    align-items: center;

    @media (max-width: 768px) {
      flex-direction: column;
      gap: 1rem;
      text-align: center;
    }
  }

  .header-title {
    display: flex;
    align-items: center;
    gap: 1rem;

    .title-icon {
      background: rgba(255, 255, 255, 0.2);
      border-radius: 50%;
      width: 60px;
      height: 60px;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 1.5rem;
    }

    .title-text {
      .page-title {
        font-size: 2.5rem;
        font-weight: 700;
        margin: 0;
        text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);

        @media (max-width: 768px) {
          font-size: 2rem;
        }
      }

      .page-subtitle {
        font-size: 1.1rem;
        opacity: 0.9;
        margin: 0.5rem 0 0 0;
      }
    }
  }

  .header-actions {
    .btn {
      background: rgba(255, 255, 255, 0.2);
      border: 2px solid rgba(255, 255, 255, 0.3);
      color: white;
      border-radius: 8px;
      padding: 0.75rem 1.5rem;
      font-weight: 600;
      transition: $transition;

      &:hover {
        background: rgba(255, 255, 255, 0.3);
        border-color: rgba(255, 255, 255, 0.5);
        transform: translateY(-2px);
      }
    }
  }
}

// Statistiques en ligne
.stats-container {
  display: flex;
  gap: 1rem;
  margin-bottom: 2rem;
  overflow-x: auto;
  padding-bottom: 0.5rem;

  @media (max-width: 768px) {
    flex-direction: column;
    gap: 0.75rem;
  }
}

.stats-card {
  background: white;
  border-radius: $border-radius;
  padding: 1.5rem;
  box-shadow: $shadow-light;
  transition: $transition;
  flex: 1;
  min-width: 200px;
  display: flex;
  align-items: center;
  gap: 1rem;
  border-left: 4px solid;

  &:hover {
    transform: translateY(-3px);
    box-shadow: $shadow-medium;
  }

  .stats-icon {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    color: white;
  }

  .stats-content {
    flex: 1;

    .stats-number {
      font-size: 2rem;
      font-weight: 700;
      line-height: 1;
      margin-bottom: 0.25rem;
    }

    .stats-label {
      font-size: 0.9rem;
      color: #6c757d;
      font-weight: 500;
    }
  }

  &.stats-primary {
    border-left-color: $primary-color;
    .stats-icon {
      background: $primary-color;
    }
    .stats-number {
      color: $primary-color;
    }
  }

  &.stats-success {
    border-left-color: #28a745;
    .stats-icon {
      background: #28a745;
    }
    .stats-number {
      color: #28a745;
    }
  }

  &.stats-warning {
    border-left-color: #ffc107;
    .stats-icon {
      background: #ffc107;
    }
    .stats-number {
      color: #ffc107;
    }
  }

  &.stats-danger {
    border-left-color: #dc3545;
    .stats-icon {
      background: #dc3545;
    }
    .stats-number {
      color: #dc3545;
    }
  }
}

// Filtres
.card {
  border: none;
  border-radius: $border-radius;
  box-shadow: $shadow-light;
  transition: $transition;

  &:hover {
    box-shadow: $shadow-medium;
  }

  .card-header {
    background: linear-gradient(135deg, #f8f9fa, #e9ecef);
    border-bottom: 1px solid #dee2e6;
    border-radius: $border-radius $border-radius 0 0;
    padding: 1.5rem;

    h5 {
      color: $primary-color;
      font-weight: 600;
      margin: 0;

      .bi {
        margin-right: 0.5rem;
      }
    }
  }

  .card-body {
    padding: 1.5rem;
  }
}

// Cartes d'avis modernes
.avis-card {
  background: white;
  border-radius: $border-radius;
  box-shadow: $shadow-light;
  margin-bottom: 1.5rem;
  overflow: hidden;
  transition: $transition;
  border: 1px solid rgba(0, 0, 0, 0.05);

  &:hover {
    transform: translateY(-3px);
    box-shadow: $shadow-medium;
  }

  .avis-card-content {
    display: flex;
    min-height: 200px;

    @media (max-width: 768px) {
      flex-direction: column;
    }
  }

  .avis-main-content {
    flex: 1;
    padding: 1.5rem;
  }

  .avis-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 1.5rem;

    @media (max-width: 768px) {
      flex-direction: column;
      gap: 1rem;
    }

    .avis-info {
      flex: 1;

      .product-name {
        font-size: 1.25rem;
        font-weight: 600;
        color: $primary-color;
        margin-bottom: 0.5rem;
      }

      .client-info {
        color: #6c757d;
        font-size: 0.9rem;
        display: flex;
        align-items: center;
        gap: 0.5rem;

        .date-separator {
          margin: 0 0.25rem;
        }

        .bi {
          color: $primary-color;
        }
      }
    }

    .avis-rating-status {
      text-align: right;

      @media (max-width: 768px) {
        text-align: left;
      }

      .rating-display {
        margin-bottom: 0.75rem;

        .stars {
          display: flex;
          gap: 0.125rem;
          margin-bottom: 0.5rem;

          .star {
            font-size: 1.1rem;
            color: #ddd;
            transition: $transition;

            &.filled {
              color: #ffc107;
            }
          }
        }

        .rating-number {
          font-weight: 600;
          color: #495057;
          font-size: 0.9rem;
        }
      }

      .status-badge {
        display: inline-flex;
        align-items: center;
        padding: 0.375rem 0.75rem;
        border-radius: 20px;
        font-size: 0.75rem;
        font-weight: 600;
        text-transform: uppercase;
        letter-spacing: 0.5px;

        .bi {
          font-size: 0.6rem;
        }

        &.badge-success {
          background: linear-gradient(135deg, #d4edda, #c3e6cb);
          color: #155724;
        }

        &.badge-warning {
          background: linear-gradient(135deg, #fff3cd, #ffeaa7);
          color: #856404;
        }

        &.badge-danger {
          background: linear-gradient(135deg, #f8d7da, #f5c6cb);
          color: #721c24;
        }

        &.badge-secondary {
          background: linear-gradient(135deg, #e2e3e5, #d6d8db);
          color: #383d41;
        }
      }
    }
  }

  .avis-comment {
    background: #f8f9fa;
    border-radius: 8px;
    padding: 1rem;
    margin-bottom: 1rem;
    border-left: 3px solid $primary-color;

    .comment-content {
      display: flex;
      align-items: flex-start;
      gap: 0.5rem;
      font-style: italic;
      line-height: 1.6;

      .bi {
        color: $primary-color;
        margin-top: 0.125rem;
        flex-shrink: 0;
      }
    }

    .comment-deleted {
      display: flex;
      align-items: center;
      gap: 0.5rem;
      color: #6c757d;
      font-style: italic;

      .bi {
        color: #dc3545;
      }
    }
  }

  .avis-actions {
    background: #f8f9fa;
    padding: 1.5rem;
    border-left: 1px solid #dee2e6;
    display: flex;
    flex-direction: column;
    gap: 0.75rem;
    justify-content: center;
    min-width: 200px;

    @media (max-width: 768px) {
      border-left: none;
      border-top: 1px solid #dee2e6;
      flex-direction: row;
      min-width: auto;
    }

    .action-btn {
      display: flex;
      align-items: center;
      gap: 0.5rem;
      padding: 0.75rem 1rem;
      border-radius: 8px;
      border: 2px solid;
      background: transparent;
      font-weight: 600;
      transition: $transition;
      cursor: pointer;
      text-decoration: none;
      justify-content: center;

      &:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
      }

      &.danger-btn {
        border-color: #dc3545;
        color: #dc3545;

        &:hover {
          background: #dc3545;
          color: white;
        }
      }

      &.info-btn {
        border-color: #17a2b8;
        color: #17a2b8;

        &:hover {
          background: #17a2b8;
          color: white;
        }
      }

      .bi {
        font-size: 1rem;
      }
    }
  }
}

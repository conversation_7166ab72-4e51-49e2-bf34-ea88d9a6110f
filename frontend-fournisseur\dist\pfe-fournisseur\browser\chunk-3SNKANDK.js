import {
  DashboardService
} from "./chunk-Y3FP3KQV.js";
import "./chunk-YIUF6N3Y.js";
import {
  ImageUrlService
} from "./chunk-CZWG52C5.js";
import {
  AuthService
} from "./chunk-ZSXXNL7U.js";
import {
  CheckboxControlValueAccessor,
  DefaultValueAccessor,
  FormArrayName,
  FormBuilder,
  FormControlName,
  FormGroupDirective,
  FormGroupName,
  MaxLengthValidator,
  MinValidator,
  NgControlStatus,
  NgControlStatusGroup,
  NumberValueAccessor,
  ReactiveFormsModule,
  Validators,
  ɵNgNoValidate
} from "./chunk-HQBVYEOO.js";
import {
  environment
} from "./chunk-GFHHPDQ6.js";
import {
  HttpClient,
  HttpParams
} from "./chunk-7JDDWGD3.js";
import {
  CommonModule,
  Component,
  Injectable,
  NgClass,
  NgForOf,
  NgIf,
  Observable,
  setClassMetadata,
  signal,
  tap,
  ɵsetClassDebugInfo,
  ɵɵadvance,
  ɵɵclassProp,
  ɵɵdefineComponent,
  ɵɵdefineInjectable,
  ɵɵdirectiveInject,
  ɵɵelement,
  ɵɵelementEnd,
  ɵɵelementStart,
  ɵɵgetCurrentView,
  ɵɵinject,
  ɵɵlistener,
  ɵɵnextContext,
  ɵɵproperty,
  ɵɵpropertyInterpolate1,
  ɵɵresetView,
  ɵɵrestoreView,
  ɵɵsanitizeUrl,
  ɵɵtemplate,
  ɵɵtext,
  ɵɵtextInterpolate,
  ɵɵtextInterpolate1,
  ɵɵtextInterpolate4
} from "./chunk-UBZQS7JS.js";

// src/app/services/fournisseur.service.ts
var FournisseurService = class _FournisseurService {
  http;
  API_URL = `${environment.apiUrl || "http://localhost:5014/api"}/Fournisseurs`;
  constructor(http) {
    this.http = http;
  }
  /**
   * GET /api/Fournisseurs - Obtenir tous les fournisseurs avec pagination et filtres
   */
  getAll(params) {
    let httpParams = new HttpParams();
    if (params) {
      if (params.page)
        httpParams = httpParams.set("page", params.page.toString());
      if (params.pageSize)
        httpParams = httpParams.set("pageSize", params.pageSize.toString());
      if (params.search)
        httpParams = httpParams.set("search", params.search);
      if (params.estActif !== void 0)
        httpParams = httpParams.set("estActif", params.estActif.toString());
      if (params.sortBy)
        httpParams = httpParams.set("sortBy", params.sortBy);
      if (params.sortOrder)
        httpParams = httpParams.set("sortOrder", params.sortOrder);
    }
    console.log("\u{1F4CB} R\xE9cup\xE9ration des fournisseurs avec param\xE8tres:", params);
    return this.http.get(this.API_URL, { params: httpParams }).pipe(tap((response) => console.log("\u2705 Fournisseurs r\xE9cup\xE9r\xE9s:", response)));
  }
  /**
   * GET /api/Fournisseurs/{id} - Obtenir un fournisseur par ID
   */
  getById(id) {
    console.log("\u{1F50D} R\xE9cup\xE9ration du fournisseur ID:", id);
    return this.http.get(`${this.API_URL}/${id}`).pipe(tap((response) => console.log("\u2705 Fournisseur r\xE9cup\xE9r\xE9:", response)));
  }
  /**
   * POST /api/Fournisseurs - Créer un nouveau fournisseur
   */
  create(fournisseur) {
    console.log("\u2795 Cr\xE9ation d'un nouveau fournisseur:", fournisseur);
    return this.http.post(this.API_URL, fournisseur).pipe(tap((response) => console.log("\u2705 Fournisseur cr\xE9\xE9:", response)));
  }
  /**
   * PUT /api/Fournisseurs/{id} - Mettre à jour un fournisseur
   */
  update(id, fournisseur) {
    console.log("\u270F\uFE0F Mise \xE0 jour du fournisseur ID:", id, fournisseur);
    return this.http.put(`${this.API_URL}/${id}`, fournisseur).pipe(tap((response) => console.log("\u2705 Fournisseur mis \xE0 jour:", response)));
  }
  /**
   * DELETE /api/Fournisseurs/{id} - Supprimer un fournisseur
   */
  delete(id) {
    console.log("\u{1F5D1}\uFE0F Suppression du fournisseur ID:", id);
    return this.http.delete(`${this.API_URL}/${id}`).pipe(tap((response) => console.log("\u2705 Fournisseur supprim\xE9:", response)));
  }
  /**
   * PATCH /api/Fournisseurs/{id}/toggle-status - Activer/Désactiver un fournisseur
   */
  toggleStatus(id, request) {
    console.log("\u{1F504} Changement de statut du fournisseur ID:", id, request);
    return this.http.patch(`${this.API_URL}/${id}/toggle-status`, request).pipe(tap((response) => console.log("\u2705 Statut du fournisseur modifi\xE9:", response)));
  }
  /**
   * GET /api/Fournisseurs/{id}/produits - Obtenir les produits d'un fournisseur
   */
  getProduits(id, params) {
    let httpParams = new HttpParams();
    if (params) {
      if (params.page)
        httpParams = httpParams.set("page", params.page.toString());
      if (params.pageSize)
        httpParams = httpParams.set("pageSize", params.pageSize.toString());
      if (params.search)
        httpParams = httpParams.set("search", params.search);
      if (params.category)
        httpParams = httpParams.set("category", params.category);
      if (params.estActif !== void 0)
        httpParams = httpParams.set("estActif", params.estActif.toString());
    }
    console.log("\u{1F4E6} R\xE9cup\xE9ration des produits du fournisseur ID:", id, params);
    return this.http.get(`${this.API_URL}/${id}/produits`, { params: httpParams }).pipe(tap((response) => console.log("\u2705 Produits du fournisseur r\xE9cup\xE9r\xE9s:", response)));
  }
  /**
   * GET /api/Fournisseurs/valider-rib - Valider un RIB
   */
  validerRib(rib, codeBanque) {
    const params = new HttpParams().set("rib", rib).set("codeBanque", codeBanque);
    console.log("\u{1F3E6} Validation du RIB:", { rib, codeBanque });
    return this.http.get(`${this.API_URL}/valider-rib`, { params }).pipe(tap((response) => console.log("\u2705 Validation RIB:", response)));
  }
  /**
   * GET /api/Fournisseurs/exists/{id} - Vérifier si un fournisseur existe
   */
  exists(id) {
    console.log("\u{1F50D} V\xE9rification de l'existence du fournisseur ID:", id);
    return this.http.get(`${this.API_URL}/exists/${id}`).pipe(tap((response) => console.log("\u2705 V\xE9rification existence:", response)));
  }
  /**
   * PATCH /api/Fournisseurs/{id}/commission - Mettre à jour la commission d'un fournisseur
   */
  updateCommission(id, request) {
    console.log("\u{1F4B0} Mise \xE0 jour de la commission du fournisseur ID:", id, request);
    return this.http.patch(`${this.API_URL}/${id}/commission`, request).pipe(tap((response) => console.log("\u2705 Commission mise \xE0 jour:", response)));
  }
  /**
   * Obtenir le fournisseur connecté (basé sur le token)
   */
  getCurrentFournisseur() {
    console.log("\u{1F464} R\xE9cup\xE9ration du fournisseur connect\xE9");
    return this.http.get(`${this.API_URL}/current`).pipe(tap((response) => console.log("\u2705 Fournisseur connect\xE9 r\xE9cup\xE9r\xE9:", response)));
  }
  /**
   * Valider les données RIB avec retour détaillé
   */
  validateRibDetails(rib, codeBanque) {
    return new Observable((observer) => {
      this.validerRib(rib, codeBanque).subscribe({
        next: (response) => {
          observer.next({
            rib,
            codeBanque,
            isValid: response.isValid
          });
          observer.complete();
        },
        error: (error) => observer.error(error)
      });
    });
  }
  static \u0275fac = function FournisseurService_Factory(__ngFactoryType__) {
    return new (__ngFactoryType__ || _FournisseurService)(\u0275\u0275inject(HttpClient));
  };
  static \u0275prov = /* @__PURE__ */ \u0275\u0275defineInjectable({ token: _FournisseurService, factory: _FournisseurService.\u0275fac, providedIn: "root" });
};
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && setClassMetadata(FournisseurService, [{
    type: Injectable,
    args: [{
      providedIn: "root"
    }]
  }], () => [{ type: HttpClient }], null);
})();

// src/app/components/profile/profile.component.ts
function ProfileComponent_div_4_Template(rf, ctx) {
  if (rf & 1) {
    const _r1 = \u0275\u0275getCurrentView();
    \u0275\u0275elementStart(0, "div", 15)(1, "img", 16);
    \u0275\u0275listener("error", function ProfileComponent_div_4_Template_img_error_1_listener($event) {
      \u0275\u0275restoreView(_r1);
      const ctx_r1 = \u0275\u0275nextContext();
      return \u0275\u0275resetView(ctx_r1.onLogoError($event));
    });
    \u0275\u0275elementEnd()();
  }
  if (rf & 2) {
    let tmp_1_0;
    let tmp_2_0;
    const ctx_r1 = \u0275\u0275nextContext();
    \u0275\u0275advance();
    \u0275\u0275property("src", ctx_r1.imageUrlService.getFournisseurLogoUrl((tmp_1_0 = ctx_r1.fournisseurInfo()) == null ? null : tmp_1_0.logoFile), \u0275\u0275sanitizeUrl)("alt", "Logo de " + ((tmp_2_0 = ctx_r1.fournisseurInfo()) == null ? null : tmp_2_0.raisonSociale));
  }
}
function ProfileComponent_div_13_button_1_Template(rf, ctx) {
  if (rf & 1) {
    const _r3 = \u0275\u0275getCurrentView();
    \u0275\u0275elementStart(0, "button", 19);
    \u0275\u0275listener("click", function ProfileComponent_div_13_button_1_Template_button_click_0_listener() {
      \u0275\u0275restoreView(_r3);
      const ctx_r1 = \u0275\u0275nextContext(2);
      return \u0275\u0275resetView(ctx_r1.enableEdit());
    });
    \u0275\u0275elementStart(1, "span");
    \u0275\u0275text(2, "\u270F\uFE0F");
    \u0275\u0275elementEnd();
    \u0275\u0275text(3, " Modifier ");
    \u0275\u0275elementEnd();
  }
}
function ProfileComponent_div_13_div_2_span_4_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275element(0, "span", 24);
  }
}
function ProfileComponent_div_13_div_2_Template(rf, ctx) {
  if (rf & 1) {
    const _r4 = \u0275\u0275getCurrentView();
    \u0275\u0275elementStart(0, "div", 20)(1, "button", 21);
    \u0275\u0275listener("click", function ProfileComponent_div_13_div_2_Template_button_click_1_listener() {
      \u0275\u0275restoreView(_r4);
      const ctx_r1 = \u0275\u0275nextContext(2);
      return \u0275\u0275resetView(ctx_r1.cancelEdit());
    });
    \u0275\u0275text(2, " Annuler ");
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(3, "button", 22);
    \u0275\u0275listener("click", function ProfileComponent_div_13_div_2_Template_button_click_3_listener() {
      \u0275\u0275restoreView(_r4);
      const ctx_r1 = \u0275\u0275nextContext(2);
      return \u0275\u0275resetView(ctx_r1.saveProfile());
    });
    \u0275\u0275template(4, ProfileComponent_div_13_div_2_span_4_Template, 1, 0, "span", 23);
    \u0275\u0275text(5);
    \u0275\u0275elementEnd()();
  }
  if (rf & 2) {
    const ctx_r1 = \u0275\u0275nextContext(2);
    \u0275\u0275advance();
    \u0275\u0275property("disabled", ctx_r1.isSaving());
    \u0275\u0275advance(2);
    \u0275\u0275property("disabled", ctx_r1.isSaving() || ctx_r1.profileForm.invalid);
    \u0275\u0275advance();
    \u0275\u0275property("ngIf", ctx_r1.isSaving());
    \u0275\u0275advance();
    \u0275\u0275textInterpolate1(" ", ctx_r1.isSaving() ? "Sauvegarde..." : "Sauvegarder", " ");
  }
}
function ProfileComponent_div_13_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "div");
    \u0275\u0275template(1, ProfileComponent_div_13_button_1_Template, 4, 0, "button", 17)(2, ProfileComponent_div_13_div_2_Template, 6, 4, "div", 18);
    \u0275\u0275elementEnd();
  }
  if (rf & 2) {
    const ctx_r1 = \u0275\u0275nextContext();
    \u0275\u0275advance();
    \u0275\u0275property("ngIf", !ctx_r1.isEditing());
    \u0275\u0275advance();
    \u0275\u0275property("ngIf", ctx_r1.isEditing());
  }
}
function ProfileComponent_div_14_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "div", 25)(1, "span", 26);
    \u0275\u0275text(2, "\u274C");
    \u0275\u0275elementEnd();
    \u0275\u0275text(3);
    \u0275\u0275elementEnd();
  }
  if (rf & 2) {
    const ctx_r1 = \u0275\u0275nextContext();
    \u0275\u0275advance(3);
    \u0275\u0275textInterpolate1(" ", ctx_r1.error(), " ");
  }
}
function ProfileComponent_div_15_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "div", 27)(1, "span", 26);
    \u0275\u0275text(2, "\u2705");
    \u0275\u0275elementEnd();
    \u0275\u0275text(3);
    \u0275\u0275elementEnd();
  }
  if (rf & 2) {
    const ctx_r1 = \u0275\u0275nextContext();
    \u0275\u0275advance(3);
    \u0275\u0275textInterpolate1(" ", ctx_r1.successMessage(), " ");
  }
}
function ProfileComponent_div_16_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "div", 28)(1, "div", 29);
    \u0275\u0275element(2, "div", 30);
    \u0275\u0275elementStart(3, "p");
    \u0275\u0275text(4, "Chargement de votre profil...");
    \u0275\u0275elementEnd()()();
  }
}
function ProfileComponent_div_17_div_12_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "div", 73);
    \u0275\u0275text(1);
    \u0275\u0275elementEnd();
  }
  if (rf & 2) {
    const ctx_r1 = \u0275\u0275nextContext(2);
    \u0275\u0275advance();
    \u0275\u0275textInterpolate1(" ", ctx_r1.getFieldError("prenom"), " ");
  }
}
function ProfileComponent_div_17_div_17_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "div", 73);
    \u0275\u0275text(1);
    \u0275\u0275elementEnd();
  }
  if (rf & 2) {
    const ctx_r1 = \u0275\u0275nextContext(2);
    \u0275\u0275advance();
    \u0275\u0275textInterpolate1(" ", ctx_r1.getFieldError("nom"), " ");
  }
}
function ProfileComponent_div_17_div_23_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "div", 73);
    \u0275\u0275text(1);
    \u0275\u0275elementEnd();
  }
  if (rf & 2) {
    const ctx_r1 = \u0275\u0275nextContext(2);
    \u0275\u0275advance();
    \u0275\u0275textInterpolate1(" ", ctx_r1.getFieldError("email"), " ");
  }
}
function ProfileComponent_div_17_div_28_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "div", 73);
    \u0275\u0275text(1);
    \u0275\u0275elementEnd();
  }
  if (rf & 2) {
    const ctx_r1 = \u0275\u0275nextContext(2);
    \u0275\u0275advance();
    \u0275\u0275textInterpolate1(" ", ctx_r1.getFieldError("phoneNumber"), " ");
  }
}
function ProfileComponent_div_17_div_43_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "div", 73);
    \u0275\u0275text(1);
    \u0275\u0275elementEnd();
  }
  if (rf & 2) {
    const ctx_r1 = \u0275\u0275nextContext(2);
    \u0275\u0275advance();
    \u0275\u0275textInterpolate1(" ", ctx_r1.getFieldError("raisonSociale"), " ");
  }
}
function ProfileComponent_div_17_div_48_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "div", 73);
    \u0275\u0275text(1);
    \u0275\u0275elementEnd();
  }
  if (rf & 2) {
    const ctx_r1 = \u0275\u0275nextContext(2);
    \u0275\u0275advance();
    \u0275\u0275textInterpolate1(" ", ctx_r1.getFieldError("matriculeFiscale"), " ");
  }
}
function ProfileComponent_div_17_div_91_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "div", 73);
    \u0275\u0275text(1);
    \u0275\u0275elementEnd();
  }
  if (rf & 2) {
    const ctx_r1 = \u0275\u0275nextContext(2);
    \u0275\u0275advance();
    \u0275\u0275textInterpolate1(" ", ctx_r1.getFieldError("delaiPreparationJours"), " ");
  }
}
function ProfileComponent_div_17_div_99_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "div", 73);
    \u0275\u0275text(1);
    \u0275\u0275elementEnd();
  }
  if (rf & 2) {
    const ctx_r1 = \u0275\u0275nextContext(2);
    \u0275\u0275advance();
    \u0275\u0275textInterpolate1(" ", ctx_r1.getFieldError("fraisLivraisonBase"), " ");
  }
}
function ProfileComponent_div_17_button_105_Template(rf, ctx) {
  if (rf & 1) {
    const _r5 = \u0275\u0275getCurrentView();
    \u0275\u0275elementStart(0, "button", 74);
    \u0275\u0275listener("click", function ProfileComponent_div_17_button_105_Template_button_click_0_listener() {
      \u0275\u0275restoreView(_r5);
      const ctx_r1 = \u0275\u0275nextContext(2);
      return \u0275\u0275resetView(ctx_r1.addAdresse());
    });
    \u0275\u0275elementStart(1, "span");
    \u0275\u0275text(2, "\u2795");
    \u0275\u0275elementEnd();
    \u0275\u0275text(3, " Ajouter une adresse ");
    \u0275\u0275elementEnd();
  }
}
function ProfileComponent_div_17_div_107_div_1_span_2_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "span", 84);
    \u0275\u0275text(1);
    \u0275\u0275elementEnd();
  }
  if (rf & 2) {
    let tmp_6_0;
    const adresseCtrl_r6 = \u0275\u0275nextContext(2).$implicit;
    \u0275\u0275advance();
    \u0275\u0275textInterpolate4(" ", (tmp_6_0 = adresseCtrl_r6.get("rue")) == null ? null : tmp_6_0.value, ", ", (tmp_6_0 = adresseCtrl_r6.get("ville")) == null ? null : tmp_6_0.value, ", ", (tmp_6_0 = adresseCtrl_r6.get("codePostal")) == null ? null : tmp_6_0.value, ", ", (tmp_6_0 = adresseCtrl_r6.get("pays")) == null ? null : tmp_6_0.value, " ");
  }
}
function ProfileComponent_div_17_div_107_div_1_span_3_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "span", 85);
    \u0275\u0275text(1, " Adresse vide ");
    \u0275\u0275elementEnd();
  }
}
function ProfileComponent_div_17_div_107_div_1_span_4_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "span", 86);
    \u0275\u0275text(1, " Principale ");
    \u0275\u0275elementEnd();
  }
}
function ProfileComponent_div_17_div_107_div_1_div_5_Template(rf, ctx) {
  if (rf & 1) {
    const _r7 = \u0275\u0275getCurrentView();
    \u0275\u0275elementStart(0, "div", 87)(1, "button", 88);
    \u0275\u0275listener("click", function ProfileComponent_div_17_div_107_div_1_div_5_Template_button_click_1_listener() {
      \u0275\u0275restoreView(_r7);
      const i_r8 = \u0275\u0275nextContext(2).index;
      const ctx_r1 = \u0275\u0275nextContext(2);
      return \u0275\u0275resetView(ctx_r1.toggleEditAddress(i_r8));
    });
    \u0275\u0275text(2, " \u270F\uFE0F Modifier ");
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(3, "button", 89);
    \u0275\u0275listener("click", function ProfileComponent_div_17_div_107_div_1_div_5_Template_button_click_3_listener() {
      \u0275\u0275restoreView(_r7);
      const i_r8 = \u0275\u0275nextContext(2).index;
      const ctx_r1 = \u0275\u0275nextContext(2);
      return \u0275\u0275resetView(ctx_r1.removeAdresse(i_r8));
    });
    \u0275\u0275text(4, " \u{1F5D1}\uFE0F Supprimer ");
    \u0275\u0275elementEnd()();
  }
}
function ProfileComponent_div_17_div_107_div_1_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "div", 78)(1, "div", 79);
    \u0275\u0275template(2, ProfileComponent_div_17_div_107_div_1_span_2_Template, 2, 4, "span", 80)(3, ProfileComponent_div_17_div_107_div_1_span_3_Template, 2, 0, "span", 81)(4, ProfileComponent_div_17_div_107_div_1_span_4_Template, 2, 0, "span", 82);
    \u0275\u0275elementEnd();
    \u0275\u0275template(5, ProfileComponent_div_17_div_107_div_1_div_5_Template, 5, 0, "div", 83);
    \u0275\u0275elementEnd();
  }
  if (rf & 2) {
    let tmp_5_0;
    let tmp_6_0;
    let tmp_7_0;
    const adresseCtrl_r6 = \u0275\u0275nextContext().$implicit;
    const ctx_r1 = \u0275\u0275nextContext(2);
    \u0275\u0275advance(2);
    \u0275\u0275property("ngIf", (tmp_5_0 = adresseCtrl_r6.get("rue")) == null ? null : tmp_5_0.value);
    \u0275\u0275advance();
    \u0275\u0275property("ngIf", !((tmp_6_0 = adresseCtrl_r6.get("rue")) == null ? null : tmp_6_0.value));
    \u0275\u0275advance();
    \u0275\u0275property("ngIf", (tmp_7_0 = adresseCtrl_r6.get("estPrincipale")) == null ? null : tmp_7_0.value);
    \u0275\u0275advance();
    \u0275\u0275property("ngIf", ctx_r1.isEditing());
  }
}
function ProfileComponent_div_17_div_107_div_2_Template(rf, ctx) {
  if (rf & 1) {
    const _r9 = \u0275\u0275getCurrentView();
    \u0275\u0275elementStart(0, "div", 90)(1, "div", 91)(2, "div", 92)(3, "label", 93);
    \u0275\u0275text(4, "Rue *");
    \u0275\u0275elementEnd();
    \u0275\u0275element(5, "input", 94);
    \u0275\u0275elementEnd()();
    \u0275\u0275elementStart(6, "div", 91)(7, "div", 95)(8, "label", 93);
    \u0275\u0275text(9, "Ville *");
    \u0275\u0275elementEnd();
    \u0275\u0275element(10, "input", 96);
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(11, "div", 95)(12, "label", 93);
    \u0275\u0275text(13, "Code Postal *");
    \u0275\u0275elementEnd();
    \u0275\u0275element(14, "input", 97);
    \u0275\u0275elementEnd()();
    \u0275\u0275elementStart(15, "div", 91)(16, "div", 95)(17, "label", 93);
    \u0275\u0275text(18, "Pays *");
    \u0275\u0275elementEnd();
    \u0275\u0275element(19, "input", 98);
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(20, "div", 95)(21, "div", 99)(22, "input", 100);
    \u0275\u0275listener("change", function ProfileComponent_div_17_div_107_div_2_Template_input_change_22_listener() {
      \u0275\u0275restoreView(_r9);
      const i_r8 = \u0275\u0275nextContext().index;
      const ctx_r1 = \u0275\u0275nextContext(2);
      return \u0275\u0275resetView(ctx_r1.setAsMain(i_r8));
    });
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(23, "label", 101);
    \u0275\u0275text(24, " Adresse principale ");
    \u0275\u0275elementEnd()()()();
    \u0275\u0275elementStart(25, "div", 87)(26, "button", 102);
    \u0275\u0275listener("click", function ProfileComponent_div_17_div_107_div_2_Template_button_click_26_listener() {
      \u0275\u0275restoreView(_r9);
      const i_r8 = \u0275\u0275nextContext().index;
      const ctx_r1 = \u0275\u0275nextContext(2);
      return \u0275\u0275resetView(ctx_r1.toggleEditAddress(i_r8));
    });
    \u0275\u0275text(27, " \u2705 Valider ");
    \u0275\u0275elementEnd()()();
  }
  if (rf & 2) {
    const i_r8 = \u0275\u0275nextContext().index;
    \u0275\u0275advance(22);
    \u0275\u0275propertyInterpolate1("id", "principal-", i_r8, "");
    \u0275\u0275advance();
    \u0275\u0275propertyInterpolate1("for", "principal-", i_r8, "");
  }
}
function ProfileComponent_div_17_div_107_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "div", 75);
    \u0275\u0275template(1, ProfileComponent_div_17_div_107_div_1_Template, 6, 4, "div", 76)(2, ProfileComponent_div_17_div_107_div_2_Template, 28, 4, "div", 77);
    \u0275\u0275elementEnd();
  }
  if (rf & 2) {
    const i_r8 = ctx.index;
    const ctx_r1 = \u0275\u0275nextContext(2);
    \u0275\u0275property("formGroupName", i_r8);
    \u0275\u0275advance();
    \u0275\u0275property("ngIf", !ctx_r1.isEditingAddress(i_r8));
    \u0275\u0275advance();
    \u0275\u0275property("ngIf", ctx_r1.isEditingAddress(i_r8));
  }
}
function ProfileComponent_div_17_div_108_button_3_Template(rf, ctx) {
  if (rf & 1) {
    const _r10 = \u0275\u0275getCurrentView();
    \u0275\u0275elementStart(0, "button", 106);
    \u0275\u0275listener("click", function ProfileComponent_div_17_div_108_button_3_Template_button_click_0_listener() {
      \u0275\u0275restoreView(_r10);
      const ctx_r1 = \u0275\u0275nextContext(3);
      return \u0275\u0275resetView(ctx_r1.addAdresse());
    });
    \u0275\u0275elementStart(1, "span");
    \u0275\u0275text(2, "\u2795");
    \u0275\u0275elementEnd();
    \u0275\u0275text(3, " Ajouter votre premi\xE8re adresse ");
    \u0275\u0275elementEnd();
  }
}
function ProfileComponent_div_17_div_108_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "div", 103)(1, "p", 104);
    \u0275\u0275text(2, "Aucune adresse enregistr\xE9e");
    \u0275\u0275elementEnd();
    \u0275\u0275template(3, ProfileComponent_div_17_div_108_button_3_Template, 4, 0, "button", 105);
    \u0275\u0275elementEnd();
  }
  if (rf & 2) {
    const ctx_r1 = \u0275\u0275nextContext(2);
    \u0275\u0275advance(3);
    \u0275\u0275property("ngIf", ctx_r1.isEditing());
  }
}
function ProfileComponent_div_17_div_109_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "div", 33)(1, "h3", 34)(2, "span", 35);
    \u0275\u0275text(3, "\u2699\uFE0F");
    \u0275\u0275elementEnd();
    \u0275\u0275text(4, " Informations syst\xE8me ");
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(5, "div", 107)(6, "div", 108)(7, "label");
    \u0275\u0275text(8, "Statut du compte");
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(9, "span", 109);
    \u0275\u0275text(10);
    \u0275\u0275elementEnd()();
    \u0275\u0275elementStart(11, "div", 108)(12, "label");
    \u0275\u0275text(13, "Date d'inscription");
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(14, "span");
    \u0275\u0275text(15);
    \u0275\u0275elementEnd()();
    \u0275\u0275elementStart(16, "div", 108)(17, "label");
    \u0275\u0275text(18, "Derni\xE8re connexion");
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(19, "span");
    \u0275\u0275text(20);
    \u0275\u0275elementEnd()()()();
  }
  if (rf & 2) {
    const ctx_r1 = \u0275\u0275nextContext(2);
    \u0275\u0275advance(9);
    \u0275\u0275property("ngClass", ctx_r1.getStatusClass(ctx_r1.fournisseurInfo().estActif));
    \u0275\u0275advance();
    \u0275\u0275textInterpolate1(" ", ctx_r1.getStatusText(ctx_r1.fournisseurInfo().estActif), " ");
    \u0275\u0275advance(5);
    \u0275\u0275textInterpolate(ctx_r1.formatDate(ctx_r1.fournisseurInfo().dateInscription));
    \u0275\u0275advance(5);
    \u0275\u0275textInterpolate(ctx_r1.fournisseurInfo().derniereConnexion ? ctx_r1.formatDate(ctx_r1.fournisseurInfo().derniereConnexion) : "Jamais");
  }
}
function ProfileComponent_div_17_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "div", 31)(1, "form", 32)(2, "div", 33)(3, "h3", 34)(4, "span", 35);
    \u0275\u0275text(5, "\u{1F468}\u200D\u{1F4BC}");
    \u0275\u0275elementEnd();
    \u0275\u0275text(6, " Informations personnelles ");
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(7, "div", 36)(8, "div", 37)(9, "label", 38);
    \u0275\u0275text(10, "Pr\xE9nom *");
    \u0275\u0275elementEnd();
    \u0275\u0275element(11, "input", 39);
    \u0275\u0275template(12, ProfileComponent_div_17_div_12_Template, 2, 1, "div", 40);
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(13, "div", 37)(14, "label", 41);
    \u0275\u0275text(15, "Nom *");
    \u0275\u0275elementEnd();
    \u0275\u0275element(16, "input", 42);
    \u0275\u0275template(17, ProfileComponent_div_17_div_17_Template, 2, 1, "div", 40);
    \u0275\u0275elementEnd()();
    \u0275\u0275elementStart(18, "div", 36)(19, "div", 37)(20, "label", 43);
    \u0275\u0275text(21, "Email *");
    \u0275\u0275elementEnd();
    \u0275\u0275element(22, "input", 44);
    \u0275\u0275template(23, ProfileComponent_div_17_div_23_Template, 2, 1, "div", 40);
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(24, "div", 37)(25, "label", 45);
    \u0275\u0275text(26, "T\xE9l\xE9phone *");
    \u0275\u0275elementEnd();
    \u0275\u0275element(27, "input", 46);
    \u0275\u0275template(28, ProfileComponent_div_17_div_28_Template, 2, 1, "div", 40);
    \u0275\u0275elementEnd()();
    \u0275\u0275elementStart(29, "div", 37)(30, "label", 47);
    \u0275\u0275text(31, "Date de naissance");
    \u0275\u0275elementEnd();
    \u0275\u0275element(32, "input", 48);
    \u0275\u0275elementEnd()();
    \u0275\u0275elementStart(33, "div", 33)(34, "h3", 34)(35, "span", 35);
    \u0275\u0275text(36, "\u{1F3E2}");
    \u0275\u0275elementEnd();
    \u0275\u0275text(37, " Informations entreprise ");
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(38, "div", 36)(39, "div", 37)(40, "label", 49);
    \u0275\u0275text(41, "Raison sociale *");
    \u0275\u0275elementEnd();
    \u0275\u0275element(42, "input", 50);
    \u0275\u0275template(43, ProfileComponent_div_17_div_43_Template, 2, 1, "div", 40);
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(44, "div", 37)(45, "label", 51);
    \u0275\u0275text(46, "Matricule fiscal *");
    \u0275\u0275elementEnd();
    \u0275\u0275element(47, "input", 52);
    \u0275\u0275template(48, ProfileComponent_div_17_div_48_Template, 2, 1, "div", 40);
    \u0275\u0275elementEnd()();
    \u0275\u0275elementStart(49, "div", 37)(50, "label", 53);
    \u0275\u0275text(51, "Description");
    \u0275\u0275elementEnd();
    \u0275\u0275element(52, "textarea", 54);
    \u0275\u0275elementEnd()();
    \u0275\u0275elementStart(53, "div", 33)(54, "h3", 34)(55, "span", 35);
    \u0275\u0275text(56, "\u{1F3E6}");
    \u0275\u0275elementEnd();
    \u0275\u0275text(57, " Informations bancaires ");
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(58, "div", 36)(59, "div", 37)(60, "label", 55);
    \u0275\u0275text(61, "RIB");
    \u0275\u0275elementEnd();
    \u0275\u0275element(62, "input", 56);
    \u0275\u0275elementStart(63, "small", 57);
    \u0275\u0275text(64, "Contactez l'administration pour modifier vos informations bancaires");
    \u0275\u0275elementEnd()();
    \u0275\u0275elementStart(65, "div", 37)(66, "label", 58);
    \u0275\u0275text(67, "Code banque");
    \u0275\u0275elementEnd();
    \u0275\u0275element(68, "input", 59);
    \u0275\u0275elementEnd()()();
    \u0275\u0275elementStart(69, "div", 33)(70, "h3", 34)(71, "span", 35);
    \u0275\u0275text(72, "\u{1F4BC}");
    \u0275\u0275elementEnd();
    \u0275\u0275text(73, " Param\xE8tres commerciaux ");
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(74, "div", 36)(75, "div", 37)(76, "label", 60);
    \u0275\u0275text(77, "Commission");
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(78, "div", 61);
    \u0275\u0275element(79, "input", 62);
    \u0275\u0275elementStart(80, "span", 63);
    \u0275\u0275text(81, "%");
    \u0275\u0275elementEnd()();
    \u0275\u0275elementStart(82, "small", 57);
    \u0275\u0275text(83, "Taux de commission d\xE9fini par l'administration");
    \u0275\u0275elementEnd()();
    \u0275\u0275elementStart(84, "div", 37)(85, "label", 64);
    \u0275\u0275text(86, "D\xE9lai de pr\xE9paration *");
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(87, "div", 61);
    \u0275\u0275element(88, "input", 65);
    \u0275\u0275elementStart(89, "span", 63);
    \u0275\u0275text(90, "jours");
    \u0275\u0275elementEnd()();
    \u0275\u0275template(91, ProfileComponent_div_17_div_91_Template, 2, 1, "div", 40);
    \u0275\u0275elementEnd()();
    \u0275\u0275elementStart(92, "div", 37)(93, "label", 66);
    \u0275\u0275text(94, "Frais de livraison de base *");
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(95, "div", 61);
    \u0275\u0275element(96, "input", 67);
    \u0275\u0275elementStart(97, "span", 63);
    \u0275\u0275text(98, "DT");
    \u0275\u0275elementEnd()();
    \u0275\u0275template(99, ProfileComponent_div_17_div_99_Template, 2, 1, "div", 40);
    \u0275\u0275elementEnd()();
    \u0275\u0275elementStart(100, "div", 33)(101, "h3", 34)(102, "span", 35);
    \u0275\u0275text(103, "\u{1F4CD}");
    \u0275\u0275elementEnd();
    \u0275\u0275text(104, " Adresses ");
    \u0275\u0275template(105, ProfileComponent_div_17_button_105_Template, 4, 0, "button", 68);
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(106, "div", 69);
    \u0275\u0275template(107, ProfileComponent_div_17_div_107_Template, 3, 3, "div", 70)(108, ProfileComponent_div_17_div_108_Template, 4, 1, "div", 71);
    \u0275\u0275elementEnd()();
    \u0275\u0275template(109, ProfileComponent_div_17_div_109_Template, 21, 4, "div", 72);
    \u0275\u0275elementEnd()();
  }
  if (rf & 2) {
    const ctx_r1 = \u0275\u0275nextContext();
    \u0275\u0275advance();
    \u0275\u0275property("formGroup", ctx_r1.profileForm);
    \u0275\u0275advance(10);
    \u0275\u0275classProp("is-invalid", ctx_r1.hasFieldError("prenom"));
    \u0275\u0275property("readonly", !ctx_r1.isEditing());
    \u0275\u0275advance();
    \u0275\u0275property("ngIf", ctx_r1.hasFieldError("prenom"));
    \u0275\u0275advance(4);
    \u0275\u0275classProp("is-invalid", ctx_r1.hasFieldError("nom"));
    \u0275\u0275property("readonly", !ctx_r1.isEditing());
    \u0275\u0275advance();
    \u0275\u0275property("ngIf", ctx_r1.hasFieldError("nom"));
    \u0275\u0275advance(5);
    \u0275\u0275classProp("is-invalid", ctx_r1.hasFieldError("email"));
    \u0275\u0275property("readonly", !ctx_r1.isEditing());
    \u0275\u0275advance();
    \u0275\u0275property("ngIf", ctx_r1.hasFieldError("email"));
    \u0275\u0275advance(4);
    \u0275\u0275classProp("is-invalid", ctx_r1.hasFieldError("phoneNumber"));
    \u0275\u0275property("readonly", !ctx_r1.isEditing());
    \u0275\u0275advance();
    \u0275\u0275property("ngIf", ctx_r1.hasFieldError("phoneNumber"));
    \u0275\u0275advance(4);
    \u0275\u0275property("readonly", !ctx_r1.isEditing());
    \u0275\u0275advance(10);
    \u0275\u0275classProp("is-invalid", ctx_r1.hasFieldError("raisonSociale"));
    \u0275\u0275property("readonly", !ctx_r1.isEditing());
    \u0275\u0275advance();
    \u0275\u0275property("ngIf", ctx_r1.hasFieldError("raisonSociale"));
    \u0275\u0275advance(4);
    \u0275\u0275classProp("is-invalid", ctx_r1.hasFieldError("matriculeFiscale"));
    \u0275\u0275property("readonly", !ctx_r1.isEditing());
    \u0275\u0275advance();
    \u0275\u0275property("ngIf", ctx_r1.hasFieldError("matriculeFiscale"));
    \u0275\u0275advance(4);
    \u0275\u0275property("readonly", !ctx_r1.isEditing());
    \u0275\u0275advance(36);
    \u0275\u0275classProp("is-invalid", ctx_r1.hasFieldError("delaiPreparationJours"));
    \u0275\u0275property("readonly", !ctx_r1.isEditing());
    \u0275\u0275advance(3);
    \u0275\u0275property("ngIf", ctx_r1.hasFieldError("delaiPreparationJours"));
    \u0275\u0275advance(5);
    \u0275\u0275classProp("is-invalid", ctx_r1.hasFieldError("fraisLivraisonBase"));
    \u0275\u0275property("readonly", !ctx_r1.isEditing());
    \u0275\u0275advance(3);
    \u0275\u0275property("ngIf", ctx_r1.hasFieldError("fraisLivraisonBase"));
    \u0275\u0275advance(6);
    \u0275\u0275property("ngIf", ctx_r1.isEditing());
    \u0275\u0275advance(2);
    \u0275\u0275property("ngForOf", ctx_r1.adresses.controls);
    \u0275\u0275advance();
    \u0275\u0275property("ngIf", ctx_r1.adresses.length === 0);
    \u0275\u0275advance();
    \u0275\u0275property("ngIf", ctx_r1.fournisseurInfo());
  }
}
var ProfileComponent = class _ProfileComponent {
  authService;
  dashboardService;
  fb;
  fournisseurService;
  imageUrlService;
  // Signaux pour la gestion d'état
  fournisseurInfo = signal(null);
  isLoading = signal(true);
  isEditing = signal(false);
  isSaving = signal(false);
  error = signal(null);
  successMessage = signal(null);
  // Gestion des adresses
  showOtherAddresses = false;
  editingAddresses = /* @__PURE__ */ new Set();
  // Formulaire
  profileForm;
  constructor(authService, dashboardService, fb, fournisseurService, imageUrlService) {
    this.authService = authService;
    this.dashboardService = dashboardService;
    this.fb = fb;
    this.fournisseurService = fournisseurService;
    this.imageUrlService = imageUrlService;
    this.profileForm = this.createForm();
  }
  ngOnInit() {
    console.log("\u{1F50D} Contenu du localStorage:");
    console.log("- token:", !!localStorage.getItem("token"));
    console.log("- user:", !!localStorage.getItem("user"));
    console.log("- fournisseur_profile:", !!localStorage.getItem("fournisseur_profile"));
    console.log("- supplierId:", localStorage.getItem("supplierId"));
    this.loadProfileData();
  }
  /**
   * Getter pour le FormArray des adresses
   */
  get adresses() {
    return this.profileForm.get("adresses");
  }
  /**
   * Créer le formulaire de profil
   */
  createForm() {
    return this.fb.group({
      // Informations personnelles
      nom: ["", [Validators.required, Validators.minLength(2)]],
      prenom: ["", [Validators.required, Validators.minLength(2)]],
      email: ["", [Validators.required, Validators.email]],
      phoneNumber: ["", [Validators.required, Validators.pattern(/^[0-9+\-\s()]+$/)]],
      dateNaissance: [""],
      // Informations entreprise
      raisonSociale: ["", [Validators.required, Validators.minLength(2)]],
      matriculeFiscale: ["", [Validators.required]],
      description: [""],
      // Informations bancaires (lecture seule)
      ribMasque: [{ value: "", disabled: true }],
      codeBanque: [{ value: "", disabled: true }],
      // Paramètres commerciaux
      commission: [{ value: 0, disabled: true }],
      delaiPreparationJours: ["", [Validators.required, Validators.min(1)]],
      fraisLivraisonBase: ["", [Validators.required, Validators.min(0)]],
      // Adresses
      adresses: this.fb.array([])
    });
  }
  /**
   * Charger les données du profil depuis localStorage et backend
   */
  loadProfileData() {
    this.isLoading.set(true);
    this.error.set(null);
    console.log("\u{1F504} Chargement des donn\xE9es du profil...");
    const currentUser = this.authService.getCurrentUser();
    if (currentUser && currentUser.id) {
      console.log("\u{1F310} R\xE9cup\xE9ration des donn\xE9es depuis l'API pour l'utilisateur ID:", currentUser.id);
      this.fournisseurService.getById(currentUser.id).subscribe({
        next: (response) => {
          console.log("\u2705 Donn\xE9es du fournisseur re\xE7ues de l'API:", response);
          this.handleApiResponse(response);
        },
        error: (error) => {
          console.error("\u274C Erreur lors de la r\xE9cup\xE9ration depuis l'API:", error);
          this.loadFromLocalStorage();
        }
      });
    } else {
      console.warn("\u26A0\uFE0F Utilisateur non connect\xE9, fallback vers localStorage");
      this.loadFromLocalStorage();
    }
  }
  /**
   * Traiter la réponse de l'API
   */
  handleApiResponse(response) {
    let fournisseur;
    if (response && response.data) {
      const apiData = response.data;
      fournisseur = this.mapApiDataToFournisseur(apiData);
    } else if (response) {
      fournisseur = this.mapApiDataToFournisseur(response);
    } else {
      console.error("\u274C R\xE9ponse API vide");
      this.loadFromLocalStorage();
      return;
    }
    console.log("\u2705 Profil cr\xE9\xE9 depuis l'API:", fournisseur);
    this.fournisseurInfo.set(fournisseur);
    this.populateForm(fournisseur);
    localStorage.setItem("fournisseur_profile", JSON.stringify(fournisseur));
    this.isLoading.set(false);
  }
  /**
   * Mapper les données API vers l'interface Fournisseur
   */
  mapApiDataToFournisseur(apiData) {
    return {
      id: apiData.id,
      email: apiData.email,
      nom: apiData.nom,
      prenom: apiData.prenom,
      phoneNumber: apiData.phoneNumber,
      role: apiData.role,
      dateNaissance: apiData.dateNaissance,
      dateInscription: apiData.dateInscription,
      derniereConnexion: apiData.derniereConnexion,
      estActif: apiData.estActif,
      matriculeFiscale: apiData.matriculeFiscale,
      raisonSociale: apiData.raisonSociale,
      description: apiData.description || "",
      ribMasque: apiData.ribMasque || apiData.rib || "",
      codeBanque: apiData.codeBanque,
      commission: apiData.commission,
      delaiPreparationJours: apiData.delaiPreparationJours,
      fraisLivraisonBase: apiData.fraisLivraisonBase,
      logoFile: apiData.logoFile || "",
      adresses: apiData.adresses || []
    };
  }
  /**
   * Charger depuis localStorage en fallback
   */
  loadFromLocalStorage() {
    const storedProfile = localStorage.getItem("fournisseur_profile");
    const storedUser = localStorage.getItem("user");
    const currentUser = this.authService.getCurrentUser();
    if (storedProfile) {
      try {
        const profileData = JSON.parse(storedProfile);
        console.log("\u{1F3AF} Donn\xE9es compl\xE8tes du profil depuis localStorage:", profileData);
        const fournisseur = {
          id: profileData.id,
          email: profileData.email,
          nom: profileData.nom,
          prenom: profileData.prenom,
          phoneNumber: profileData.phoneNumber,
          role: profileData.role,
          dateNaissance: profileData.dateNaissance,
          dateInscription: profileData.dateInscription,
          derniereConnexion: profileData.derniereConnexion,
          estActif: profileData.estActif,
          matriculeFiscale: profileData.matriculeFiscale,
          raisonSociale: profileData.raisonSociale,
          description: profileData.description,
          ribMasque: profileData.ribMasque,
          codeBanque: profileData.codeBanque,
          commission: profileData.commission,
          delaiPreparationJours: profileData.delaiPreparationJours,
          fraisLivraisonBase: profileData.fraisLivraisonBase,
          logoFile: profileData.logoFile,
          adresses: profileData.adresses
        };
        console.log("\u2705 Profil cr\xE9\xE9 depuis donn\xE9es compl\xE8tes:", fournisseur);
        this.fournisseurInfo.set(fournisseur);
        this.populateForm(fournisseur);
        this.isLoading.set(false);
        return;
      } catch (error) {
        console.warn("\u26A0\uFE0F Erreur parsing fournisseur_profile:", error);
      }
    }
    if (storedUser) {
      try {
        const userData = JSON.parse(storedUser);
        console.log("\u{1F4E6} Fallback: Donn\xE9es utilisateur depuis localStorage:", userData);
        const fournisseurFromStorage = {
          id: userData.id || currentUser?.id || 0,
          email: userData.email || currentUser?.email || "",
          nom: userData.nom || currentUser?.nom || "",
          prenom: userData.prenom || currentUser?.prenom || "",
          phoneNumber: userData.phoneNumber || userData.telephone || "",
          role: userData.role || "Fournisseur",
          dateNaissance: userData.dateNaissance || "",
          dateInscription: userData.dateInscription || (/* @__PURE__ */ new Date()).toISOString(),
          derniereConnexion: userData.derniereConnexion || null,
          estActif: userData.estActif !== void 0 ? userData.estActif : true,
          matriculeFiscale: userData.matriculeFiscale || "",
          raisonSociale: userData.raisonSociale || "",
          description: userData.description || "",
          ribMasque: userData.ribMasque || userData.rib || "",
          codeBanque: userData.codeBanque || "",
          commission: userData.commission || 0,
          delaiPreparationJours: userData.delaiPreparationJours || 3,
          fraisLivraisonBase: userData.fraisLivraisonBase || 5,
          logoFile: userData.logoFile || "",
          adresses: userData.adresses || []
        };
        console.log("\u2705 Profil cr\xE9\xE9 depuis localStorage:", fournisseurFromStorage);
        this.fournisseurInfo.set(fournisseurFromStorage);
        this.populateForm(fournisseurFromStorage);
        this.isLoading.set(false);
        return;
      } catch (error) {
        console.warn("\u26A0\uFE0F Erreur parsing localStorage user:", error);
      }
    }
    console.log("\u{1F504} Fallback: tentative via DashboardService...");
    this.dashboardService.getFournisseurInfo().subscribe({
      next: (fournisseur) => {
        if (fournisseur) {
          console.log("\u2705 Donn\xE9es du profil charg\xE9es via DashboardService:", fournisseur);
          this.fournisseurInfo.set(fournisseur);
          this.populateForm(fournisseur);
        } else {
          this.createMinimalProfile();
        }
        this.isLoading.set(false);
      },
      error: (error) => {
        console.error("\u274C Erreur lors du chargement du profil via DashboardService:", error);
        this.createMinimalProfile();
        this.isLoading.set(false);
      }
    });
  }
  /**
   * Créer un profil minimal depuis les données disponibles
   */
  createMinimalProfile() {
    const currentUser = this.authService.getCurrentUser();
    if (currentUser) {
      const minimalProfile = {
        id: currentUser.id,
        email: currentUser.email,
        nom: currentUser.nom,
        prenom: currentUser.prenom,
        phoneNumber: currentUser.phoneNumber || currentUser.telephone || "",
        role: currentUser.role || "Fournisseur",
        dateNaissance: "",
        dateInscription: (/* @__PURE__ */ new Date()).toISOString(),
        derniereConnexion: null,
        estActif: true,
        matriculeFiscale: "",
        raisonSociale: "",
        description: "",
        ribMasque: "",
        codeBanque: "",
        commission: 0,
        delaiPreparationJours: 3,
        fraisLivraisonBase: 5,
        logoFile: "",
        adresses: []
      };
      console.log("\u2705 Profil minimal cr\xE9\xE9:", minimalProfile);
      this.fournisseurInfo.set(minimalProfile);
      this.populateForm(minimalProfile);
    } else {
      this.error.set("Aucune donn\xE9e utilisateur disponible. Veuillez vous reconnecter.");
    }
  }
  /**
   * Remplir le formulaire avec les données du fournisseur
   */
  populateForm(fournisseur) {
    console.log("\u{1F4DD} Remplissage du formulaire avec:", fournisseur);
    this.profileForm.patchValue({
      nom: fournisseur.nom || "",
      prenom: fournisseur.prenom || "",
      email: fournisseur.email || "",
      phoneNumber: fournisseur.phoneNumber || "",
      dateNaissance: fournisseur.dateNaissance ? fournisseur.dateNaissance.split("T")[0] : "",
      raisonSociale: fournisseur.raisonSociale || "",
      matriculeFiscale: fournisseur.matriculeFiscale || "",
      description: fournisseur.description || "",
      ribMasque: fournisseur.ribMasque || "Non renseign\xE9",
      codeBanque: fournisseur.codeBanque || "Non renseign\xE9",
      commission: fournisseur.commission || 0,
      delaiPreparationJours: fournisseur.delaiPreparationJours || 3,
      fraisLivraisonBase: fournisseur.fraisLivraisonBase || 5
    });
    this.initAdresses(fournisseur.adresses || []);
    console.log("\u2705 Formulaire rempli avec les valeurs:", this.profileForm.value);
  }
  /**
   * Initialiser les adresses dans le FormArray
   */
  initAdresses(adresses) {
    const adressesArray = this.adresses;
    adressesArray.clear();
    adresses.forEach((adresse) => {
      adressesArray.push(this.createAdresseFormGroup(adresse));
    });
  }
  /**
   * Créer un FormGroup pour une adresse
   */
  createAdresseFormGroup(adresse) {
    return this.fb.group({
      id: [adresse?.id || 0],
      rue: [adresse?.rue || "", Validators.required],
      ville: [adresse?.ville || "", Validators.required],
      codePostal: [adresse?.codePostal || "", [Validators.required, Validators.pattern(/^[0-9]{4}$/)]],
      pays: [adresse?.pays || "Tunisie", Validators.required],
      estPrincipale: [adresse?.estPrincipale || false]
    });
  }
  /**
   * Ajouter une nouvelle adresse
   */
  addAdresse() {
    const newIndex = this.adresses.length;
    this.adresses.push(this.createAdresseFormGroup());
    this.editingAddresses.add(newIndex);
  }
  /**
   * Supprimer une adresse
   */
  removeAdresse(index) {
    this.adresses.removeAt(index);
    this.editingAddresses.delete(index);
    const newEditingAddresses = /* @__PURE__ */ new Set();
    this.editingAddresses.forEach((editIndex) => {
      if (editIndex > index) {
        newEditingAddresses.add(editIndex - 1);
      } else if (editIndex < index) {
        newEditingAddresses.add(editIndex);
      }
    });
    this.editingAddresses = newEditingAddresses;
  }
  /**
   * Basculer le mode édition d'une adresse
   */
  toggleEditAddress(index) {
    if (this.editingAddresses.has(index)) {
      this.editingAddresses.delete(index);
    } else {
      this.editingAddresses.add(index);
    }
  }
  /**
   * Vérifier si une adresse est en mode édition
   */
  isEditingAddress(index) {
    return this.editingAddresses.has(index);
  }
  /**
   * Définir une adresse comme principale
   */
  setAsMain(index) {
    for (let i = 0; i < this.adresses.length; i++) {
      this.adresses.at(i).get("estPrincipale")?.setValue(i === index);
    }
  }
  /**
   * Basculer l'affichage des autres adresses
   */
  toggleOtherAddresses() {
    this.showOtherAddresses = !this.showOtherAddresses;
  }
  /**
   * Activer le mode édition
   */
  enableEdit() {
    this.isEditing.set(true);
    this.successMessage.set(null);
    this.error.set(null);
  }
  /**
   * Annuler l'édition
   */
  cancelEdit() {
    this.isEditing.set(false);
    const fournisseur = this.fournisseurInfo();
    if (fournisseur) {
      this.populateForm(fournisseur);
    }
    this.error.set(null);
  }
  /**
   * Sauvegarder les modifications
   */
  saveProfile() {
    if (this.profileForm.valid) {
      this.isSaving.set(true);
      this.error.set(null);
      const formData = this.profileForm.value;
      console.log("\u{1F4BE} Sauvegarde du profil:", formData);
      setTimeout(() => {
        this.isSaving.set(false);
        this.isEditing.set(false);
        this.successMessage.set("Profil mis \xE0 jour avec succ\xE8s !");
        setTimeout(() => {
          this.successMessage.set(null);
        }, 3e3);
      }, 1e3);
    } else {
      this.error.set("Veuillez corriger les erreurs dans le formulaire");
      this.markFormGroupTouched();
    }
  }
  /**
   * Marquer tous les champs comme touchés pour afficher les erreurs
   */
  markFormGroupTouched() {
    Object.keys(this.profileForm.controls).forEach((key) => {
      const control = this.profileForm.get(key);
      control?.markAsTouched();
    });
  }
  /**
   * Vérifier si un champ a une erreur
   */
  hasFieldError(fieldName) {
    const field = this.profileForm.get(fieldName);
    return !!(field && field.invalid && field.touched);
  }
  /**
   * Obtenir le message d'erreur pour un champ
   */
  getFieldError(fieldName) {
    const field = this.profileForm.get(fieldName);
    if (field && field.errors && field.touched) {
      if (field.errors["required"])
        return "Ce champ est obligatoire";
      if (field.errors["email"])
        return "Email invalide";
      if (field.errors["minlength"])
        return `Minimum ${field.errors["minlength"].requiredLength} caract\xE8res`;
      if (field.errors["pattern"])
        return "Format invalide";
      if (field.errors["min"])
        return `Valeur minimum: ${field.errors["min"].min}`;
    }
    return "";
  }
  /**
   * Formater la date d'inscription
   */
  formatDate(dateString) {
    if (!dateString)
      return "Non renseign\xE9";
    return new Date(dateString).toLocaleDateString("fr-FR");
  }
  /**
   * Formater le statut
   */
  getStatusText(estActif) {
    return estActif ? "Actif" : "Inactif";
  }
  /**
   * Obtenir la classe CSS pour le statut
   */
  getStatusClass(estActif) {
    return estActif ? "status-active" : "status-inactive";
  }
  /**
   * Formater la commission
   */
  formatCommission(commission) {
    return `${commission}%`;
  }
  /**
   * Formater les frais de livraison
   */
  formatCurrency(amount) {
    return new Intl.NumberFormat("fr-FR", {
      style: "currency",
      currency: "EUR"
    }).format(amount);
  }
  /**
   * Rafraîchir les données du profil depuis l'API
   */
  refreshProfile() {
    console.log("\u{1F504} Rafra\xEEchissement du profil depuis l'API...");
    this.isLoading.set(true);
    this.error.set(null);
    const currentUser = this.authService.getCurrentUser();
    if (currentUser && currentUser.id) {
      this.fournisseurService.getById(currentUser.id).subscribe({
        next: (response) => {
          console.log("\u2705 Profil rafra\xEEchi depuis l'API:", response);
          this.handleApiResponse(response);
        },
        error: (error) => {
          console.error("\u274C Erreur lors du rafra\xEEchissement:", error);
          this.error.set("Erreur lors du rafra\xEEchissement du profil");
          this.isLoading.set(false);
        }
      });
    } else {
      this.error.set("Utilisateur non connect\xE9");
      this.isLoading.set(false);
    }
  }
  /**
   * Debug: afficher toutes les données disponibles
   */
  debugProfile() {
    console.log("\u{1F41B} DEBUG PROFIL COMPLET:");
    console.log("\u{1F4E6} localStorage:");
    console.log("  - token:", !!localStorage.getItem("token"));
    console.log("  - user:", localStorage.getItem("user"));
    console.log("  - fournisseur_profile:", localStorage.getItem("fournisseur_profile"));
    console.log("  - supplierId:", localStorage.getItem("supplierId"));
    console.log("\u{1F464} AuthService:");
    console.log("  - currentUser:", this.authService.getCurrentUser());
    console.log("  - isAuthenticated:", this.authService.isAuthenticated());
    console.log("\u{1F3EA} Composant:");
    console.log("  - fournisseurInfo:", this.fournisseurInfo());
    console.log("  - form values:", this.profileForm.value);
    console.log("  - isLoading:", this.isLoading());
    console.log("  - error:", this.error());
    console.log("\u{1F4CA} Statistiques localStorage:");
    console.log("  - Nombre de cl\xE9s:", Object.keys(localStorage).length);
    console.log("  - Toutes les cl\xE9s:", Object.keys(localStorage));
  }
  /**
   * Forcer la création du profil depuis localStorage uniquement
   */
  loadFromLocalStorageOnly() {
    console.log("\u{1F504} Chargement forc\xE9 depuis localStorage uniquement...");
    this.isLoading.set(true);
    this.error.set(null);
    const storedProfile = localStorage.getItem("fournisseur_profile");
    if (storedProfile) {
      try {
        const profileData = JSON.parse(storedProfile);
        console.log("\u{1F3AF} Donn\xE9es compl\xE8tes trouv\xE9es:", profileData);
        const fournisseur = {
          id: profileData.id || 0,
          email: profileData.email || "",
          nom: profileData.nom || "",
          prenom: profileData.prenom || "",
          phoneNumber: profileData.phoneNumber || "",
          role: profileData.role || "Fournisseur",
          dateNaissance: profileData.dateNaissance || "",
          dateInscription: profileData.dateInscription || (/* @__PURE__ */ new Date()).toISOString(),
          derniereConnexion: profileData.derniereConnexion || null,
          estActif: profileData.estActif !== void 0 ? profileData.estActif : true,
          matriculeFiscale: profileData.matriculeFiscale || "",
          raisonSociale: profileData.raisonSociale || "",
          description: profileData.description || "",
          ribMasque: profileData.ribMasque || "Non renseign\xE9",
          codeBanque: profileData.codeBanque || "Non renseign\xE9",
          commission: profileData.commission || 0,
          delaiPreparationJours: profileData.delaiPreparationJours || 3,
          fraisLivraisonBase: profileData.fraisLivraisonBase || 5,
          logoFile: profileData.logoFile || "",
          adresses: profileData.adresses || []
        };
        console.log("\u2705 Profil cr\xE9\xE9 depuis donn\xE9es compl\xE8tes:", fournisseur);
        this.fournisseurInfo.set(fournisseur);
        this.populateForm(fournisseur);
        this.isLoading.set(false);
        return;
      } catch (error) {
        console.error("\u274C Erreur parsing fournisseur_profile:", error);
      }
    }
    const storedUser = localStorage.getItem("user");
    if (storedUser) {
      try {
        const userData = JSON.parse(storedUser);
        console.log("\u{1F4E6} Donn\xE9es localStorage:", userData);
        const fournisseur = {
          id: userData.id || 0,
          email: userData.email || "",
          nom: userData.nom || "",
          prenom: userData.prenom || "",
          phoneNumber: userData.phoneNumber || userData.telephone || "",
          role: userData.role || "Fournisseur",
          dateNaissance: userData.dateNaissance || "",
          dateInscription: userData.dateInscription || (/* @__PURE__ */ new Date()).toISOString(),
          derniereConnexion: userData.derniereConnexion || null,
          estActif: userData.estActif !== void 0 ? userData.estActif : true,
          matriculeFiscale: userData.matriculeFiscale || "",
          raisonSociale: userData.raisonSociale || "",
          description: userData.description || "",
          ribMasque: userData.ribMasque || userData.rib || "Non renseign\xE9",
          codeBanque: userData.codeBanque || "Non renseign\xE9",
          commission: userData.commission || 0,
          delaiPreparationJours: userData.delaiPreparationJours || 3,
          fraisLivraisonBase: userData.fraisLivraisonBase || 5,
          logoFile: userData.logoFile || "",
          adresses: userData.adresses || []
        };
        console.log("\u2705 Profil cr\xE9\xE9 depuis localStorage:", fournisseur);
        this.fournisseurInfo.set(fournisseur);
        this.populateForm(fournisseur);
        this.isLoading.set(false);
      } catch (error) {
        console.error("\u274C Erreur parsing localStorage:", error);
        this.error.set("Erreur lors de la lecture des donn\xE9es localStorage");
        this.isLoading.set(false);
      }
    } else {
      this.error.set("Aucune donn\xE9e utilisateur trouv\xE9e dans localStorage");
      this.isLoading.set(false);
    }
  }
  /**
   * Gérer les erreurs de chargement du logo
   */
  onLogoError(event) {
    event.target.src = this.imageUrlService.getPlaceholderUrl();
  }
  static \u0275fac = function ProfileComponent_Factory(__ngFactoryType__) {
    return new (__ngFactoryType__ || _ProfileComponent)(\u0275\u0275directiveInject(AuthService), \u0275\u0275directiveInject(DashboardService), \u0275\u0275directiveInject(FormBuilder), \u0275\u0275directiveInject(FournisseurService), \u0275\u0275directiveInject(ImageUrlService));
  };
  static \u0275cmp = /* @__PURE__ */ \u0275\u0275defineComponent({ type: _ProfileComponent, selectors: [["app-profile"]], decls: 18, vars: 6, consts: [[1, "profile-container"], [1, "profile-header"], [1, "header-content"], [1, "header-left"], ["class", "profile-logo", 4, "ngIf"], [1, "header-text"], [1, "profile-title"], [1, "title-icon"], [1, "profile-subtitle"], [1, "header-actions"], [4, "ngIf"], ["class", "alert alert-error", 4, "ngIf"], ["class", "alert alert-success", 4, "ngIf"], ["class", "loading-container", 4, "ngIf"], ["class", "profile-content", 4, "ngIf"], [1, "profile-logo"], ["loading", "lazy", 1, "logo-image", 3, "error", "src", "alt"], ["class", "btn btn-primary", "type", "button", 3, "click", 4, "ngIf"], ["class", "edit-actions", 4, "ngIf"], ["type", "button", 1, "btn", "btn-primary", 3, "click"], [1, "edit-actions"], ["type", "button", 1, "btn", "btn-secondary", 3, "click", "disabled"], ["type", "button", 1, "btn", "btn-primary", 3, "click", "disabled"], ["class", "spinner", 4, "ngIf"], [1, "spinner"], [1, "alert", "alert-error"], [1, "alert-icon"], [1, "alert", "alert-success"], [1, "loading-container"], [1, "loading-content"], [1, "spinner", "large"], [1, "profile-content"], [1, "profile-form", 3, "formGroup"], [1, "form-section"], [1, "section-title"], [1, "section-icon"], [1, "form-row"], [1, "form-group"], ["for", "prenom"], ["type", "text", "id", "prenom", "formControlName", "prenom", 1, "form-control", 3, "readonly"], ["class", "invalid-feedback", 4, "ngIf"], ["for", "nom"], ["type", "text", "id", "nom", "formControlName", "nom", 1, "form-control", 3, "readonly"], ["for", "email"], ["type", "email", "id", "email", "formControlName", "email", 1, "form-control", 3, "readonly"], ["for", "phoneNumber"], ["type", "tel", "id", "phoneNumber", "formControlName", "phoneNumber", 1, "form-control", 3, "readonly"], ["for", "dateNaissance"], ["type", "date", "id", "dateNaissance", "formControlName", "dateNaissance", 1, "form-control", 3, "readonly"], ["for", "raisonSociale"], ["type", "text", "id", "raisonSociale", "formControlName", "raisonSociale", 1, "form-control", 3, "readonly"], ["for", "matriculeFiscale"], ["type", "text", "id", "matriculeFiscale", "formControlName", "matriculeFiscale", 1, "form-control", 3, "readonly"], ["for", "description"], ["id", "description", "formControlName", "description", "rows", "4", "placeholder", "D\xE9crivez votre entreprise et vos activit\xE9s...", 1, "form-control", 3, "readonly"], ["for", "ribMasque"], ["type", "text", "id", "ribMasque", "formControlName", "ribMasque", "readonly", "", 1, "form-control"], [1, "form-hint"], ["for", "codeBanque"], ["type", "text", "id", "codeBanque", "formControlName", "codeBanque", "readonly", "", 1, "form-control"], ["for", "commission"], [1, "input-group"], ["type", "number", "id", "commission", "formControlName", "commission", "readonly", "", 1, "form-control"], [1, "input-suffix"], ["for", "delaiPreparationJours"], ["type", "number", "id", "delaiPreparationJours", "formControlName", "delaiPreparationJours", "min", "1", 1, "form-control", 3, "readonly"], ["for", "fraisLivraisonBase"], ["type", "number", "id", "fraisLivraisonBase", "formControlName", "fraisLivraisonBase", "min", "0", "step", "0.01", 1, "form-control", 3, "readonly"], ["type", "button", "class", "btn btn-sm btn-outline-primary ms-auto", 3, "click", 4, "ngIf"], ["formArrayName", "adresses"], ["class", "address-item", 3, "formGroupName", 4, "ngFor", "ngForOf"], ["class", "no-addresses", 4, "ngIf"], ["class", "form-section", 4, "ngIf"], [1, "invalid-feedback"], ["type", "button", 1, "btn", "btn-sm", "btn-outline-primary", "ms-auto", 3, "click"], [1, "address-item", 3, "formGroupName"], ["class", "address-display", 4, "ngIf"], ["class", "address-edit", 4, "ngIf"], [1, "address-display"], [1, "address-content"], ["class", "address-text", 4, "ngIf"], ["class", "no-address", 4, "ngIf"], ["class", "badge badge-primary ms-2", 4, "ngIf"], ["class", "address-actions", 4, "ngIf"], [1, "address-text"], [1, "no-address"], [1, "badge", "badge-primary", "ms-2"], [1, "address-actions"], ["type", "button", 1, "btn", "btn-sm", "btn-outline-secondary", 3, "click"], ["type", "button", 1, "btn", "btn-sm", "btn-outline-danger", 3, "click"], [1, "address-edit"], [1, "row"], [1, "col-md-12", "mb-3"], [1, "form-label"], ["type", "text", "formControlName", "rue", "placeholder", "Num\xE9ro et nom de rue", 1, "form-control"], [1, "col-md-6", "mb-3"], ["type", "text", "formControlName", "ville", "placeholder", "Ville", 1, "form-control"], ["type", "text", "formControlName", "codePostal", "placeholder", "1234", "maxlength", "4", 1, "form-control"], ["type", "text", "formControlName", "pays", "value", "Tunisie", "readonly", "", 1, "form-control"], [1, "form-check"], ["type", "checkbox", "formControlName", "estPrincipale", 1, "form-check-input", 3, "change", "id"], [1, "form-check-label", 3, "for"], ["type", "button", 1, "btn", "btn-sm", "btn-success", 3, "click"], [1, "no-addresses"], [1, "text-muted"], ["type", "button", "class", "btn btn-outline-primary", 3, "click", 4, "ngIf"], ["type", "button", 1, "btn", "btn-outline-primary", 3, "click"], [1, "info-grid"], [1, "info-item"], [1, "badge", 3, "ngClass"]], template: function ProfileComponent_Template(rf, ctx) {
    if (rf & 1) {
      \u0275\u0275elementStart(0, "div", 0)(1, "div", 1)(2, "div", 2)(3, "div", 3);
      \u0275\u0275template(4, ProfileComponent_div_4_Template, 2, 2, "div", 4);
      \u0275\u0275elementStart(5, "div", 5)(6, "h1", 6)(7, "span", 7);
      \u0275\u0275text(8, "\u{1F464}");
      \u0275\u0275elementEnd();
      \u0275\u0275text(9, " Mon Profil ");
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(10, "p", 8);
      \u0275\u0275text(11, "G\xE9rez vos informations personnelles et professionnelles");
      \u0275\u0275elementEnd()()();
      \u0275\u0275elementStart(12, "div", 9);
      \u0275\u0275template(13, ProfileComponent_div_13_Template, 3, 2, "div", 10);
      \u0275\u0275elementEnd()()();
      \u0275\u0275template(14, ProfileComponent_div_14_Template, 4, 1, "div", 11)(15, ProfileComponent_div_15_Template, 4, 1, "div", 12)(16, ProfileComponent_div_16_Template, 5, 0, "div", 13)(17, ProfileComponent_div_17_Template, 110, 39, "div", 14);
      \u0275\u0275elementEnd();
    }
    if (rf & 2) {
      \u0275\u0275advance(4);
      \u0275\u0275property("ngIf", ctx.fournisseurInfo());
      \u0275\u0275advance(9);
      \u0275\u0275property("ngIf", !ctx.isLoading() && ctx.fournisseurInfo());
      \u0275\u0275advance();
      \u0275\u0275property("ngIf", ctx.error());
      \u0275\u0275advance();
      \u0275\u0275property("ngIf", ctx.successMessage());
      \u0275\u0275advance();
      \u0275\u0275property("ngIf", ctx.isLoading());
      \u0275\u0275advance();
      \u0275\u0275property("ngIf", !ctx.isLoading() && ctx.fournisseurInfo());
    }
  }, dependencies: [CommonModule, NgClass, NgForOf, NgIf, ReactiveFormsModule, \u0275NgNoValidate, DefaultValueAccessor, NumberValueAccessor, CheckboxControlValueAccessor, NgControlStatus, NgControlStatusGroup, MaxLengthValidator, MinValidator, FormGroupDirective, FormControlName, FormGroupName, FormArrayName], styles: ['\n\n.profile-container[_ngcontent-%COMP%] {\n  max-width: 1000px;\n  margin: 0 auto;\n  padding: 2rem;\n  background:\n    linear-gradient(\n      135deg,\n      rgba(255, 255, 255, 0.98) 0%,\n      rgba(248, 250, 252, 0.95) 100%);\n  min-height: 100vh;\n}\n.profile-header[_ngcontent-%COMP%] {\n  background:\n    linear-gradient(\n      135deg,\n      rgba(59, 130, 246, 0.08) 0%,\n      rgba(99, 102, 241, 0.05) 100%);\n  border: 1px solid rgba(59, 130, 246, 0.2);\n  border-radius: 24px;\n  padding: 2rem;\n  margin-bottom: 2rem;\n  position: relative;\n  overflow: hidden;\n}\n.profile-header[_ngcontent-%COMP%]::before {\n  content: "";\n  position: absolute;\n  top: 0;\n  left: 0;\n  right: 0;\n  height: 4px;\n  background:\n    linear-gradient(\n      90deg,\n      #3b82f6,\n      #6366f1,\n      #8b5cf6);\n}\n.header-content[_ngcontent-%COMP%] {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  gap: 2rem;\n}\n.header-left[_ngcontent-%COMP%] {\n  display: flex;\n  align-items: center;\n  gap: 1.5rem;\n}\n.profile-logo[_ngcontent-%COMP%] {\n  flex-shrink: 0;\n}\n.logo-image[_ngcontent-%COMP%] {\n  width: 80px;\n  height: 80px;\n  border-radius: 16px;\n  object-fit: cover;\n  border: 3px solid rgba(59, 130, 246, 0.2);\n  box-shadow: 0 8px 25px rgba(59, 130, 246, 0.15);\n  transition: all 0.3s ease;\n}\n.logo-image[_ngcontent-%COMP%]:hover {\n  transform: translateY(-2px);\n  box-shadow: 0 12px 35px rgba(59, 130, 246, 0.25);\n  border-color: rgba(59, 130, 246, 0.4);\n}\n.profile-title[_ngcontent-%COMP%] {\n  margin: 0;\n  font-size: 2rem;\n  font-weight: 800;\n  background:\n    linear-gradient(\n      135deg,\n      #3b82f6 0%,\n      #6366f1 50%,\n      #8b5cf6 100%);\n  -webkit-background-clip: text;\n  -webkit-text-fill-color: transparent;\n  background-clip: text;\n  display: flex;\n  align-items: center;\n  gap: 1rem;\n}\n.title-icon[_ngcontent-%COMP%] {\n  font-size: 2.5rem;\n  filter: drop-shadow(0 2px 4px rgba(59, 130, 246, 0.3));\n}\n.profile-subtitle[_ngcontent-%COMP%] {\n  margin: 0.5rem 0 0 0;\n  color: #64748b;\n  font-size: 1.1rem;\n}\n.header-actions[_ngcontent-%COMP%] {\n  display: flex;\n  gap: 1rem;\n}\n.edit-actions[_ngcontent-%COMP%] {\n  display: flex;\n  gap: 1rem;\n}\n.profile-content[_ngcontent-%COMP%] {\n  animation: _ngcontent-%COMP%_fadeInUp 0.6s ease forwards;\n}\n.profile-form[_ngcontent-%COMP%] {\n  display: flex;\n  flex-direction: column;\n  gap: 2.5rem;\n}\n.form-section[_ngcontent-%COMP%] {\n  background:\n    linear-gradient(\n      135deg,\n      rgba(248, 250, 252, 0.8) 0%,\n      rgba(255, 255, 255, 0.6) 100%);\n  border: 1px solid rgba(59, 130, 246, 0.1);\n  border-radius: 20px;\n  padding: 2rem;\n  position: relative;\n  transition: all 0.3s ease;\n}\n.form-section[_ngcontent-%COMP%]:hover {\n  transform: translateY(-2px);\n  box-shadow: 0 8px 16px rgba(59, 130, 246, 0.1);\n  border-color: rgba(59, 130, 246, 0.2);\n}\n.section-title[_ngcontent-%COMP%] {\n  margin: 0 0 1.5rem 0;\n  font-size: 1.25rem;\n  font-weight: 700;\n  color: #374151;\n  display: flex;\n  align-items: center;\n  gap: 0.75rem;\n}\n.section-icon[_ngcontent-%COMP%] {\n  font-size: 1.5rem;\n  filter: drop-shadow(0 1px 2px rgba(59, 130, 246, 0.3));\n}\n.form-row[_ngcontent-%COMP%] {\n  display: grid;\n  grid-template-columns: 1fr 1fr;\n  gap: 2rem;\n  margin-bottom: 1.5rem;\n}\n.form-group[_ngcontent-%COMP%] {\n  display: flex;\n  flex-direction: column;\n  gap: 0.75rem;\n}\n.form-group[_ngcontent-%COMP%]   label[_ngcontent-%COMP%] {\n  font-size: 0.95rem;\n  font-weight: 600;\n  color: #374151;\n  position: relative;\n  padding-left: 1rem;\n}\n.form-group[_ngcontent-%COMP%]   label[_ngcontent-%COMP%]::before {\n  content: "";\n  position: absolute;\n  left: 0;\n  top: 50%;\n  transform: translateY(-50%);\n  width: 4px;\n  height: 4px;\n  background:\n    linear-gradient(\n      135deg,\n      #3b82f6,\n      #6366f1);\n  border-radius: 50%;\n  opacity: 0.7;\n}\n.form-control[_ngcontent-%COMP%] {\n  padding: 1rem 1.25rem;\n  border: 2px solid rgba(203, 213, 225, 0.6);\n  border-radius: 12px;\n  font-size: 0.95rem;\n  font-weight: 500;\n  background:\n    linear-gradient(\n      135deg,\n      rgba(248, 250, 252, 0.9) 0%,\n      rgba(255, 255, 255, 0.8) 100%);\n  transition: all 0.3s ease;\n  box-shadow: 0 2px 4px rgba(59, 130, 246, 0.05);\n}\n.form-control[_ngcontent-%COMP%]:focus {\n  outline: none;\n  border-color: #3b82f6;\n  background:\n    linear-gradient(\n      135deg,\n      rgba(255, 255, 255, 0.98) 0%,\n      rgba(248, 250, 252, 0.95) 100%);\n  box-shadow: 0 0 0 4px rgba(59, 130, 246, 0.15), 0 8px 16px rgba(59, 130, 246, 0.1);\n  transform: translateY(-1px);\n}\n.form-control[readonly][_ngcontent-%COMP%] {\n  background: rgba(248, 250, 252, 0.6);\n  color: #64748b;\n  cursor: not-allowed;\n}\n.form-control.is-invalid[_ngcontent-%COMP%] {\n  border-color: #ef4444;\n  background: rgba(254, 242, 242, 0.8);\n}\n.invalid-feedback[_ngcontent-%COMP%] {\n  color: #ef4444;\n  font-size: 0.875rem;\n  font-weight: 500;\n  display: flex;\n  align-items: center;\n  gap: 0.5rem;\n}\n.invalid-feedback[_ngcontent-%COMP%]::before {\n  content: "\\26a0\\fe0f";\n  font-size: 0.875rem;\n}\n.form-hint[_ngcontent-%COMP%] {\n  color: #64748b;\n  font-size: 0.8125rem;\n  font-style: italic;\n  margin-top: 0.25rem;\n}\n.input-group[_ngcontent-%COMP%] {\n  position: relative;\n  display: flex;\n  align-items: center;\n}\n.input-suffix[_ngcontent-%COMP%] {\n  position: absolute;\n  right: 1rem;\n  color: #64748b;\n  font-size: 0.95rem;\n  font-weight: 600;\n  pointer-events: none;\n}\n.info-grid[_ngcontent-%COMP%] {\n  display: grid;\n  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));\n  gap: 1.5rem;\n}\n.info-item[_ngcontent-%COMP%] {\n  display: flex;\n  flex-direction: column;\n  gap: 0.5rem;\n}\n.info-item[_ngcontent-%COMP%]   label[_ngcontent-%COMP%] {\n  font-size: 0.875rem;\n  font-weight: 600;\n  color: #64748b;\n  text-transform: uppercase;\n  letter-spacing: 0.05em;\n}\n.info-item[_ngcontent-%COMP%]   span[_ngcontent-%COMP%] {\n  font-size: 0.95rem;\n  font-weight: 500;\n  color: #374151;\n}\n.badge[_ngcontent-%COMP%] {\n  display: inline-flex;\n  align-items: center;\n  padding: 0.5rem 1rem;\n  border-radius: 20px;\n  font-size: 0.875rem;\n  font-weight: 600;\n  text-transform: uppercase;\n  letter-spacing: 0.025em;\n}\n.badge.status-active[_ngcontent-%COMP%] {\n  background:\n    linear-gradient(\n      135deg,\n      #10b981,\n      #059669);\n  color: white;\n  box-shadow: 0 2px 8px rgba(16, 185, 129, 0.3);\n}\n.badge.status-inactive[_ngcontent-%COMP%] {\n  background:\n    linear-gradient(\n      135deg,\n      #ef4444,\n      #dc2626);\n  color: white;\n  box-shadow: 0 2px 8px rgba(239, 68, 68, 0.3);\n}\n.badge.badge-role[_ngcontent-%COMP%] {\n  background:\n    linear-gradient(\n      135deg,\n      #8b5cf6,\n      #7c3aed);\n  color: white;\n  box-shadow: 0 2px 8px rgba(139, 92, 246, 0.3);\n}\n.btn[_ngcontent-%COMP%] {\n  padding: 0.875rem 1.5rem;\n  border: none;\n  border-radius: 12px;\n  font-size: 0.95rem;\n  font-weight: 600;\n  cursor: pointer;\n  transition: all 0.3s ease;\n  display: flex;\n  align-items: center;\n  gap: 0.5rem;\n  text-decoration: none;\n  position: relative;\n  overflow: hidden;\n}\n.btn[_ngcontent-%COMP%]::before {\n  content: "";\n  position: absolute;\n  top: 0;\n  left: -100%;\n  width: 100%;\n  height: 100%;\n  background:\n    linear-gradient(\n      90deg,\n      transparent,\n      rgba(255, 255, 255, 0.3),\n      transparent);\n  transition: left 0.6s ease;\n}\n.btn[_ngcontent-%COMP%]:hover:not(:disabled)::before {\n  left: 100%;\n}\n.btn-primary[_ngcontent-%COMP%] {\n  background:\n    linear-gradient(\n      135deg,\n      #3b82f6 0%,\n      #6366f1 100%);\n  color: white;\n  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);\n}\n.btn-primary[_ngcontent-%COMP%]:hover:not(:disabled) {\n  background:\n    linear-gradient(\n      135deg,\n      #2563eb 0%,\n      #4f46e5 100%);\n  transform: translateY(-2px);\n  box-shadow: 0 8px 20px rgba(59, 130, 246, 0.4);\n}\n.btn-secondary[_ngcontent-%COMP%] {\n  background:\n    linear-gradient(\n      135deg,\n      #6b7280 0%,\n      #4b5563 100%);\n  color: white;\n  box-shadow: 0 4px 12px rgba(107, 114, 128, 0.3);\n}\n.btn-secondary[_ngcontent-%COMP%]:hover:not(:disabled) {\n  background:\n    linear-gradient(\n      135deg,\n      #4b5563 0%,\n      #374151 100%);\n  transform: translateY(-2px);\n  box-shadow: 0 8px 20px rgba(107, 114, 128, 0.4);\n}\n.btn[_ngcontent-%COMP%]:disabled {\n  opacity: 0.6;\n  cursor: not-allowed;\n  transform: none;\n}\n.btn-sm[_ngcontent-%COMP%] {\n  padding: 0.5rem 1rem;\n  font-size: 0.875rem;\n}\n.spinner[_ngcontent-%COMP%] {\n  width: 16px;\n  height: 16px;\n  border: 2px solid rgba(255, 255, 255, 0.3);\n  border-top: 2px solid white;\n  border-radius: 50%;\n  animation: _ngcontent-%COMP%_spin 1s linear infinite;\n}\n.spinner.large[_ngcontent-%COMP%] {\n  width: 40px;\n  height: 40px;\n  border-width: 4px;\n  border-color: rgba(59, 130, 246, 0.3);\n  border-top-color: #3b82f6;\n}\n@keyframes _ngcontent-%COMP%_spin {\n  0% {\n    transform: rotate(0deg);\n  }\n  100% {\n    transform: rotate(360deg);\n  }\n}\n.alert[_ngcontent-%COMP%] {\n  padding: 1rem 1.5rem;\n  border-radius: 12px;\n  margin-bottom: 1.5rem;\n  display: flex;\n  align-items: center;\n  gap: 0.75rem;\n  font-weight: 500;\n}\n.alert-error[_ngcontent-%COMP%] {\n  background:\n    linear-gradient(\n      135deg,\n      rgba(254, 242, 242, 0.9),\n      rgba(252, 231, 243, 0.8));\n  border: 1px solid rgba(239, 68, 68, 0.3);\n  color: #dc2626;\n}\n.alert-success[_ngcontent-%COMP%] {\n  background:\n    linear-gradient(\n      135deg,\n      rgba(236, 253, 245, 0.9),\n      rgba(240, 253, 244, 0.8));\n  border: 1px solid rgba(34, 197, 94, 0.3);\n  color: #059669;\n}\n@media (max-width: 768px) {\n  .profile-container[_ngcontent-%COMP%] {\n    padding: 1rem;\n  }\n  .header-content[_ngcontent-%COMP%] {\n    flex-direction: column;\n    align-items: flex-start;\n    gap: 1rem;\n  }\n  .profile-title[_ngcontent-%COMP%] {\n    font-size: 1.5rem;\n  }\n  .form-row[_ngcontent-%COMP%] {\n    grid-template-columns: 1fr;\n    gap: 1rem;\n  }\n  .form-section[_ngcontent-%COMP%] {\n    padding: 1.5rem;\n  }\n  .edit-actions[_ngcontent-%COMP%] {\n    flex-direction: column;\n    width: 100%;\n  }\n  .info-grid[_ngcontent-%COMP%] {\n    grid-template-columns: 1fr;\n  }\n}\n@keyframes _ngcontent-%COMP%_fadeInUp {\n  0% {\n    opacity: 0;\n    transform: translateY(20px);\n  }\n  100% {\n    opacity: 1;\n    transform: translateY(0);\n  }\n}\n.loading-container[_ngcontent-%COMP%] {\n  display: flex;\n  justify-content: center;\n  align-items: center;\n  min-height: 400px;\n}\n.loading-content[_ngcontent-%COMP%] {\n  text-align: center;\n  color: #64748b;\n}\n.loading-content[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\n  margin-top: 1rem;\n  font-size: 1.1rem;\n}\n.address-item[_ngcontent-%COMP%] {\n  background: rgba(255, 255, 255, 0.8);\n  border: 1px solid rgba(226, 232, 240, 0.8);\n  border-radius: 16px;\n  padding: 1.5rem;\n  margin-bottom: 1rem;\n  transition: all 0.3s ease;\n}\n.address-item[_ngcontent-%COMP%]:hover {\n  border-color: rgba(59, 130, 246, 0.3);\n  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.1);\n}\n.address-display[_ngcontent-%COMP%] {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  gap: 1rem;\n}\n.address-content[_ngcontent-%COMP%] {\n  flex: 1;\n  display: flex;\n  align-items: center;\n  gap: 0.5rem;\n}\n.address-text[_ngcontent-%COMP%] {\n  color: #374151;\n  font-weight: 500;\n}\n.no-address[_ngcontent-%COMP%] {\n  color: #9ca3af;\n  font-style: italic;\n}\n.address-actions[_ngcontent-%COMP%] {\n  display: flex;\n  gap: 0.5rem;\n  align-items: center;\n}\n.address-edit[_ngcontent-%COMP%] {\n  background: rgba(249, 250, 251, 0.8);\n  border-radius: 12px;\n  padding: 1rem;\n  margin-top: 1rem;\n}\n.no-addresses[_ngcontent-%COMP%] {\n  text-align: center;\n  padding: 2rem;\n  background: rgba(249, 250, 251, 0.5);\n  border: 2px dashed rgba(209, 213, 219, 0.8);\n  border-radius: 16px;\n  margin-top: 1rem;\n}\n.badge-primary[_ngcontent-%COMP%] {\n  background:\n    linear-gradient(\n      135deg,\n      #3b82f6,\n      #6366f1);\n  color: white;\n  padding: 0.25rem 0.75rem;\n  border-radius: 12px;\n  font-size: 0.75rem;\n  font-weight: 600;\n}\n@media (max-width: 768px) {\n  .address-display[_ngcontent-%COMP%] {\n    flex-direction: column;\n    align-items: flex-start;\n    gap: 1rem;\n  }\n  .address-actions[_ngcontent-%COMP%] {\n    width: 100%;\n    justify-content: flex-end;\n  }\n  .address-edit[_ngcontent-%COMP%]   .row[_ngcontent-%COMP%] {\n    margin: 0;\n  }\n  .address-edit[_ngcontent-%COMP%]   .col-md-6[_ngcontent-%COMP%], \n   .address-edit[_ngcontent-%COMP%]   .col-md-12[_ngcontent-%COMP%] {\n    padding: 0 0.5rem;\n  }\n}\n/*# sourceMappingURL=profile.component.css.map */'] });
};
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && setClassMetadata(ProfileComponent, [{
    type: Component,
    args: [{ selector: "app-profile", standalone: true, imports: [CommonModule, ReactiveFormsModule], template: `<div class="profile-container">
  <!-- Header -->
  <div class="profile-header">
    <div class="header-content">
      <div class="header-left">
        <!-- Logo du fournisseur -->
        <div class="profile-logo" *ngIf="fournisseurInfo()">
          <img
            [src]="imageUrlService.getFournisseurLogoUrl(fournisseurInfo()?.logoFile)"
            [alt]="'Logo de ' + fournisseurInfo()?.raisonSociale"
            class="logo-image"
            (error)="onLogoError($event)"
            loading="lazy">
        </div>
        <div class="header-text">
          <h1 class="profile-title">
            <span class="title-icon">\u{1F464}</span>
            Mon Profil
          </h1>
          <p class="profile-subtitle">G\xE9rez vos informations personnelles et professionnelles</p>
        </div>
      </div>
      <div class="header-actions">
        <div *ngIf="!isLoading() && fournisseurInfo()">
          <button
            *ngIf="!isEditing()"
            class="btn btn-primary"
            (click)="enableEdit()"
            type="button">
            <span>\u270F\uFE0F</span> Modifier
          </button>
          <div *ngIf="isEditing()" class="edit-actions">
            <button
              class="btn btn-secondary"
              (click)="cancelEdit()"
              type="button"
              [disabled]="isSaving()">
              Annuler
            </button>
            <button
              class="btn btn-primary"
              (click)="saveProfile()"
              type="button"
              [disabled]="isSaving() || profileForm.invalid">
              <span *ngIf="isSaving()" class="spinner"></span>
              {{ isSaving() ? 'Sauvegarde...' : 'Sauvegarder' }}
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Messages -->
  <div *ngIf="error()" class="alert alert-error">
    <span class="alert-icon">\u274C</span>
    {{ error() }}
  </div>

  <div *ngIf="successMessage()" class="alert alert-success">
    <span class="alert-icon">\u2705</span>
    {{ successMessage() }}
  </div>

  <!-- Loading State -->
  <div *ngIf="isLoading()" class="loading-container">
    <div class="loading-content">
      <div class="spinner large"></div>
      <p>Chargement de votre profil...</p>
    </div>
  </div>

  <!-- Profile Content -->
  <div *ngIf="!isLoading() && fournisseurInfo()" class="profile-content">
    <form [formGroup]="profileForm" class="profile-form">
      
      <!-- Informations personnelles -->
      <div class="form-section">
        <h3 class="section-title">
          <span class="section-icon">\u{1F468}\u200D\u{1F4BC}</span>
          Informations personnelles
        </h3>
        
        <div class="form-row">
          <div class="form-group">
            <label for="prenom">Pr\xE9nom *</label>
            <input
              type="text"
              id="prenom"
              formControlName="prenom"
              class="form-control"
              [class.is-invalid]="hasFieldError('prenom')"
              [readonly]="!isEditing()"
            />
            <div class="invalid-feedback" *ngIf="hasFieldError('prenom')">
              {{ getFieldError('prenom') }}
            </div>
          </div>

          <div class="form-group">
            <label for="nom">Nom *</label>
            <input
              type="text"
              id="nom"
              formControlName="nom"
              class="form-control"
              [class.is-invalid]="hasFieldError('nom')"
              [readonly]="!isEditing()"
            />
            <div class="invalid-feedback" *ngIf="hasFieldError('nom')">
              {{ getFieldError('nom') }}
            </div>
          </div>
        </div>

        <div class="form-row">
          <div class="form-group">
            <label for="email">Email *</label>
            <input
              type="email"
              id="email"
              formControlName="email"
              class="form-control"
              [class.is-invalid]="hasFieldError('email')"
              [readonly]="!isEditing()"
            />
            <div class="invalid-feedback" *ngIf="hasFieldError('email')">
              {{ getFieldError('email') }}
            </div>
          </div>

          <div class="form-group">
            <label for="phoneNumber">T\xE9l\xE9phone *</label>
            <input
              type="tel"
              id="phoneNumber"
              formControlName="phoneNumber"
              class="form-control"
              [class.is-invalid]="hasFieldError('phoneNumber')"
              [readonly]="!isEditing()"
            />
            <div class="invalid-feedback" *ngIf="hasFieldError('phoneNumber')">
              {{ getFieldError('phoneNumber') }}
            </div>
          </div>
        </div>

        <div class="form-group">
          <label for="dateNaissance">Date de naissance</label>
          <input
            type="date"
            id="dateNaissance"
            formControlName="dateNaissance"
            class="form-control"
            [readonly]="!isEditing()"
          />
        </div>
      </div>

      <!-- Informations entreprise -->
      <div class="form-section">
        <h3 class="section-title">
          <span class="section-icon">\u{1F3E2}</span>
          Informations entreprise
        </h3>
        
        <div class="form-row">
          <div class="form-group">
            <label for="raisonSociale">Raison sociale *</label>
            <input
              type="text"
              id="raisonSociale"
              formControlName="raisonSociale"
              class="form-control"
              [class.is-invalid]="hasFieldError('raisonSociale')"
              [readonly]="!isEditing()"
            />
            <div class="invalid-feedback" *ngIf="hasFieldError('raisonSociale')">
              {{ getFieldError('raisonSociale') }}
            </div>
          </div>

          <div class="form-group">
            <label for="matriculeFiscale">Matricule fiscal *</label>
            <input
              type="text"
              id="matriculeFiscale"
              formControlName="matriculeFiscale"
              class="form-control"
              [class.is-invalid]="hasFieldError('matriculeFiscale')"
              [readonly]="!isEditing()"
            />
            <div class="invalid-feedback" *ngIf="hasFieldError('matriculeFiscale')">
              {{ getFieldError('matriculeFiscale') }}
            </div>
          </div>
        </div>

        <div class="form-group">
          <label for="description">Description</label>
          <textarea
            id="description"
            formControlName="description"
            class="form-control"
            rows="4"
            [readonly]="!isEditing()"
            placeholder="D\xE9crivez votre entreprise et vos activit\xE9s..."
          ></textarea>
        </div>
      </div>

      <!-- Informations bancaires -->
      <div class="form-section">
        <h3 class="section-title">
          <span class="section-icon">\u{1F3E6}</span>
          Informations bancaires
        </h3>
        
        <div class="form-row">
          <div class="form-group">
            <label for="ribMasque">RIB</label>
            <input
              type="text"
              id="ribMasque"
              formControlName="ribMasque"
              class="form-control"
              readonly
            />
            <small class="form-hint">Contactez l'administration pour modifier vos informations bancaires</small>
          </div>

          <div class="form-group">
            <label for="codeBanque">Code banque</label>
            <input
              type="text"
              id="codeBanque"
              formControlName="codeBanque"
              class="form-control"
              readonly
            />
          </div>
        </div>
      </div>

      <!-- Param\xE8tres commerciaux -->
      <div class="form-section">
        <h3 class="section-title">
          <span class="section-icon">\u{1F4BC}</span>
          Param\xE8tres commerciaux
        </h3>
        
        <div class="form-row">
          <div class="form-group">
            <label for="commission">Commission</label>
            <div class="input-group">
              <input
                type="number"
                id="commission"
                formControlName="commission"
                class="form-control"
                readonly
              />
              <span class="input-suffix">%</span>
            </div>
            <small class="form-hint">Taux de commission d\xE9fini par l'administration</small>
          </div>

          <div class="form-group">
            <label for="delaiPreparationJours">D\xE9lai de pr\xE9paration *</label>
            <div class="input-group">
              <input
                type="number"
                id="delaiPreparationJours"
                formControlName="delaiPreparationJours"
                class="form-control"
                [class.is-invalid]="hasFieldError('delaiPreparationJours')"
                [readonly]="!isEditing()"
                min="1"
              />
              <span class="input-suffix">jours</span>
            </div>
            <div class="invalid-feedback" *ngIf="hasFieldError('delaiPreparationJours')">
              {{ getFieldError('delaiPreparationJours') }}
            </div>
          </div>
        </div>

        <div class="form-group">
          <label for="fraisLivraisonBase">Frais de livraison de base *</label>
          <div class="input-group">
            <input
              type="number"
              id="fraisLivraisonBase"
              formControlName="fraisLivraisonBase"
              class="form-control"
              [class.is-invalid]="hasFieldError('fraisLivraisonBase')"
              [readonly]="!isEditing()"
              min="0"
              step="0.01"
            />
            <span class="input-suffix">DT</span>
          </div>
          <div class="invalid-feedback" *ngIf="hasFieldError('fraisLivraisonBase')">
            {{ getFieldError('fraisLivraisonBase') }}
          </div>
        </div>
      </div>

      <!-- Section Adresses -->
      <div class="form-section">
        <h3 class="section-title">
          <span class="section-icon">\u{1F4CD}</span>
          Adresses
          <button
            type="button"
            class="btn btn-sm btn-outline-primary ms-auto"
            (click)="addAdresse()"
            *ngIf="isEditing()"
          >
            <span>\u2795</span> Ajouter une adresse
          </button>
        </h3>

        <div formArrayName="adresses">
          <div *ngFor="let adresseCtrl of adresses.controls; let i = index"
               [formGroupName]="i"
               class="address-item">

            <!-- Mode affichage -->
            <div *ngIf="!isEditingAddress(i)" class="address-display">
              <div class="address-content">
                <span *ngIf="adresseCtrl.get('rue')?.value" class="address-text">
                  {{ adresseCtrl.get('rue')?.value }},
                  {{ adresseCtrl.get('ville')?.value }},
                  {{ adresseCtrl.get('codePostal')?.value }},
                  {{ adresseCtrl.get('pays')?.value }}
                </span>
                <span *ngIf="!adresseCtrl.get('rue')?.value" class="no-address">
                  Adresse vide
                </span>
                <span *ngIf="adresseCtrl.get('estPrincipale')?.value" class="badge badge-primary ms-2">
                  Principale
                </span>
              </div>
              <div class="address-actions" *ngIf="isEditing()">
                <button
                  type="button"
                  class="btn btn-sm btn-outline-secondary"
                  (click)="toggleEditAddress(i)"
                >
                  \u270F\uFE0F Modifier
                </button>
                <button
                  type="button"
                  class="btn btn-sm btn-outline-danger"
                  (click)="removeAdresse(i)"
                >
                  \u{1F5D1}\uFE0F Supprimer
                </button>
              </div>
            </div>

            <!-- Mode \xE9dition -->
            <div *ngIf="isEditingAddress(i)" class="address-edit">
              <div class="row">
                <div class="col-md-12 mb-3">
                  <label class="form-label">Rue *</label>
                  <input
                    type="text"
                    formControlName="rue"
                    class="form-control"
                    placeholder="Num\xE9ro et nom de rue"
                  />
                </div>
              </div>

              <div class="row">
                <div class="col-md-6 mb-3">
                  <label class="form-label">Ville *</label>
                  <input
                    type="text"
                    formControlName="ville"
                    class="form-control"
                    placeholder="Ville"
                  />
                </div>
                <div class="col-md-6 mb-3">
                  <label class="form-label">Code Postal *</label>
                  <input
                    type="text"
                    formControlName="codePostal"
                    class="form-control"
                    placeholder="1234"
                    maxlength="4"
                  />
                </div>
              </div>

              <div class="row">
                <div class="col-md-6 mb-3">
                  <label class="form-label">Pays *</label>
                  <input
                    type="text"
                    formControlName="pays"
                    class="form-control"
                    value="Tunisie"
                    readonly
                  />
                </div>
                <div class="col-md-6 mb-3">
                  <div class="form-check">
                    <input
                      type="checkbox"
                      formControlName="estPrincipale"
                      (change)="setAsMain(i)"
                      class="form-check-input"
                      id="principal-{{i}}"
                    />
                    <label for="principal-{{i}}" class="form-check-label">
                      Adresse principale
                    </label>
                  </div>
                </div>
              </div>

              <div class="address-actions">
                <button
                  type="button"
                  class="btn btn-sm btn-success"
                  (click)="toggleEditAddress(i)"
                >
                  \u2705 Valider
                </button>
              </div>
            </div>
          </div>

          <div *ngIf="adresses.length === 0" class="no-addresses">
            <p class="text-muted">Aucune adresse enregistr\xE9e</p>
            <button
              type="button"
              class="btn btn-outline-primary"
              (click)="addAdresse()"
              *ngIf="isEditing()"
            >
              <span>\u2795</span> Ajouter votre premi\xE8re adresse
            </button>
          </div>
        </div>
      </div>

      <!-- Informations syst\xE8me -->
      <div class="form-section" *ngIf="fournisseurInfo()">
        <h3 class="section-title">
          <span class="section-icon">\u2699\uFE0F</span>
          Informations syst\xE8me
        </h3>
        
        <div class="info-grid">
          <div class="info-item">
            <label>Statut du compte</label>
            <span class="badge" [ngClass]="getStatusClass(fournisseurInfo()!.estActif)">
              {{ getStatusText(fournisseurInfo()!.estActif) }}
            </span>
          </div>

          <div class="info-item">
            <label>Date d'inscription</label>
            <span>{{ formatDate(fournisseurInfo()!.dateInscription) }}</span>
          </div>

          <div class="info-item">
            <label>Derni\xE8re connexion</label>
            <span>{{ fournisseurInfo()!.derniereConnexion ? formatDate(fournisseurInfo()!.derniereConnexion!) : 'Jamais' }}</span>
          </div>          
        </div>
      </div>
    </form>
  </div>
</div>
`, styles: ['/* src/app/components/profile/profile.component.css */\n.profile-container {\n  max-width: 1000px;\n  margin: 0 auto;\n  padding: 2rem;\n  background:\n    linear-gradient(\n      135deg,\n      rgba(255, 255, 255, 0.98) 0%,\n      rgba(248, 250, 252, 0.95) 100%);\n  min-height: 100vh;\n}\n.profile-header {\n  background:\n    linear-gradient(\n      135deg,\n      rgba(59, 130, 246, 0.08) 0%,\n      rgba(99, 102, 241, 0.05) 100%);\n  border: 1px solid rgba(59, 130, 246, 0.2);\n  border-radius: 24px;\n  padding: 2rem;\n  margin-bottom: 2rem;\n  position: relative;\n  overflow: hidden;\n}\n.profile-header::before {\n  content: "";\n  position: absolute;\n  top: 0;\n  left: 0;\n  right: 0;\n  height: 4px;\n  background:\n    linear-gradient(\n      90deg,\n      #3b82f6,\n      #6366f1,\n      #8b5cf6);\n}\n.header-content {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  gap: 2rem;\n}\n.header-left {\n  display: flex;\n  align-items: center;\n  gap: 1.5rem;\n}\n.profile-logo {\n  flex-shrink: 0;\n}\n.logo-image {\n  width: 80px;\n  height: 80px;\n  border-radius: 16px;\n  object-fit: cover;\n  border: 3px solid rgba(59, 130, 246, 0.2);\n  box-shadow: 0 8px 25px rgba(59, 130, 246, 0.15);\n  transition: all 0.3s ease;\n}\n.logo-image:hover {\n  transform: translateY(-2px);\n  box-shadow: 0 12px 35px rgba(59, 130, 246, 0.25);\n  border-color: rgba(59, 130, 246, 0.4);\n}\n.profile-title {\n  margin: 0;\n  font-size: 2rem;\n  font-weight: 800;\n  background:\n    linear-gradient(\n      135deg,\n      #3b82f6 0%,\n      #6366f1 50%,\n      #8b5cf6 100%);\n  -webkit-background-clip: text;\n  -webkit-text-fill-color: transparent;\n  background-clip: text;\n  display: flex;\n  align-items: center;\n  gap: 1rem;\n}\n.title-icon {\n  font-size: 2.5rem;\n  filter: drop-shadow(0 2px 4px rgba(59, 130, 246, 0.3));\n}\n.profile-subtitle {\n  margin: 0.5rem 0 0 0;\n  color: #64748b;\n  font-size: 1.1rem;\n}\n.header-actions {\n  display: flex;\n  gap: 1rem;\n}\n.edit-actions {\n  display: flex;\n  gap: 1rem;\n}\n.profile-content {\n  animation: fadeInUp 0.6s ease forwards;\n}\n.profile-form {\n  display: flex;\n  flex-direction: column;\n  gap: 2.5rem;\n}\n.form-section {\n  background:\n    linear-gradient(\n      135deg,\n      rgba(248, 250, 252, 0.8) 0%,\n      rgba(255, 255, 255, 0.6) 100%);\n  border: 1px solid rgba(59, 130, 246, 0.1);\n  border-radius: 20px;\n  padding: 2rem;\n  position: relative;\n  transition: all 0.3s ease;\n}\n.form-section:hover {\n  transform: translateY(-2px);\n  box-shadow: 0 8px 16px rgba(59, 130, 246, 0.1);\n  border-color: rgba(59, 130, 246, 0.2);\n}\n.section-title {\n  margin: 0 0 1.5rem 0;\n  font-size: 1.25rem;\n  font-weight: 700;\n  color: #374151;\n  display: flex;\n  align-items: center;\n  gap: 0.75rem;\n}\n.section-icon {\n  font-size: 1.5rem;\n  filter: drop-shadow(0 1px 2px rgba(59, 130, 246, 0.3));\n}\n.form-row {\n  display: grid;\n  grid-template-columns: 1fr 1fr;\n  gap: 2rem;\n  margin-bottom: 1.5rem;\n}\n.form-group {\n  display: flex;\n  flex-direction: column;\n  gap: 0.75rem;\n}\n.form-group label {\n  font-size: 0.95rem;\n  font-weight: 600;\n  color: #374151;\n  position: relative;\n  padding-left: 1rem;\n}\n.form-group label::before {\n  content: "";\n  position: absolute;\n  left: 0;\n  top: 50%;\n  transform: translateY(-50%);\n  width: 4px;\n  height: 4px;\n  background:\n    linear-gradient(\n      135deg,\n      #3b82f6,\n      #6366f1);\n  border-radius: 50%;\n  opacity: 0.7;\n}\n.form-control {\n  padding: 1rem 1.25rem;\n  border: 2px solid rgba(203, 213, 225, 0.6);\n  border-radius: 12px;\n  font-size: 0.95rem;\n  font-weight: 500;\n  background:\n    linear-gradient(\n      135deg,\n      rgba(248, 250, 252, 0.9) 0%,\n      rgba(255, 255, 255, 0.8) 100%);\n  transition: all 0.3s ease;\n  box-shadow: 0 2px 4px rgba(59, 130, 246, 0.05);\n}\n.form-control:focus {\n  outline: none;\n  border-color: #3b82f6;\n  background:\n    linear-gradient(\n      135deg,\n      rgba(255, 255, 255, 0.98) 0%,\n      rgba(248, 250, 252, 0.95) 100%);\n  box-shadow: 0 0 0 4px rgba(59, 130, 246, 0.15), 0 8px 16px rgba(59, 130, 246, 0.1);\n  transform: translateY(-1px);\n}\n.form-control[readonly] {\n  background: rgba(248, 250, 252, 0.6);\n  color: #64748b;\n  cursor: not-allowed;\n}\n.form-control.is-invalid {\n  border-color: #ef4444;\n  background: rgba(254, 242, 242, 0.8);\n}\n.invalid-feedback {\n  color: #ef4444;\n  font-size: 0.875rem;\n  font-weight: 500;\n  display: flex;\n  align-items: center;\n  gap: 0.5rem;\n}\n.invalid-feedback::before {\n  content: "\\26a0\\fe0f";\n  font-size: 0.875rem;\n}\n.form-hint {\n  color: #64748b;\n  font-size: 0.8125rem;\n  font-style: italic;\n  margin-top: 0.25rem;\n}\n.input-group {\n  position: relative;\n  display: flex;\n  align-items: center;\n}\n.input-suffix {\n  position: absolute;\n  right: 1rem;\n  color: #64748b;\n  font-size: 0.95rem;\n  font-weight: 600;\n  pointer-events: none;\n}\n.info-grid {\n  display: grid;\n  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));\n  gap: 1.5rem;\n}\n.info-item {\n  display: flex;\n  flex-direction: column;\n  gap: 0.5rem;\n}\n.info-item label {\n  font-size: 0.875rem;\n  font-weight: 600;\n  color: #64748b;\n  text-transform: uppercase;\n  letter-spacing: 0.05em;\n}\n.info-item span {\n  font-size: 0.95rem;\n  font-weight: 500;\n  color: #374151;\n}\n.badge {\n  display: inline-flex;\n  align-items: center;\n  padding: 0.5rem 1rem;\n  border-radius: 20px;\n  font-size: 0.875rem;\n  font-weight: 600;\n  text-transform: uppercase;\n  letter-spacing: 0.025em;\n}\n.badge.status-active {\n  background:\n    linear-gradient(\n      135deg,\n      #10b981,\n      #059669);\n  color: white;\n  box-shadow: 0 2px 8px rgba(16, 185, 129, 0.3);\n}\n.badge.status-inactive {\n  background:\n    linear-gradient(\n      135deg,\n      #ef4444,\n      #dc2626);\n  color: white;\n  box-shadow: 0 2px 8px rgba(239, 68, 68, 0.3);\n}\n.badge.badge-role {\n  background:\n    linear-gradient(\n      135deg,\n      #8b5cf6,\n      #7c3aed);\n  color: white;\n  box-shadow: 0 2px 8px rgba(139, 92, 246, 0.3);\n}\n.btn {\n  padding: 0.875rem 1.5rem;\n  border: none;\n  border-radius: 12px;\n  font-size: 0.95rem;\n  font-weight: 600;\n  cursor: pointer;\n  transition: all 0.3s ease;\n  display: flex;\n  align-items: center;\n  gap: 0.5rem;\n  text-decoration: none;\n  position: relative;\n  overflow: hidden;\n}\n.btn::before {\n  content: "";\n  position: absolute;\n  top: 0;\n  left: -100%;\n  width: 100%;\n  height: 100%;\n  background:\n    linear-gradient(\n      90deg,\n      transparent,\n      rgba(255, 255, 255, 0.3),\n      transparent);\n  transition: left 0.6s ease;\n}\n.btn:hover:not(:disabled)::before {\n  left: 100%;\n}\n.btn-primary {\n  background:\n    linear-gradient(\n      135deg,\n      #3b82f6 0%,\n      #6366f1 100%);\n  color: white;\n  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);\n}\n.btn-primary:hover:not(:disabled) {\n  background:\n    linear-gradient(\n      135deg,\n      #2563eb 0%,\n      #4f46e5 100%);\n  transform: translateY(-2px);\n  box-shadow: 0 8px 20px rgba(59, 130, 246, 0.4);\n}\n.btn-secondary {\n  background:\n    linear-gradient(\n      135deg,\n      #6b7280 0%,\n      #4b5563 100%);\n  color: white;\n  box-shadow: 0 4px 12px rgba(107, 114, 128, 0.3);\n}\n.btn-secondary:hover:not(:disabled) {\n  background:\n    linear-gradient(\n      135deg,\n      #4b5563 0%,\n      #374151 100%);\n  transform: translateY(-2px);\n  box-shadow: 0 8px 20px rgba(107, 114, 128, 0.4);\n}\n.btn:disabled {\n  opacity: 0.6;\n  cursor: not-allowed;\n  transform: none;\n}\n.btn-sm {\n  padding: 0.5rem 1rem;\n  font-size: 0.875rem;\n}\n.spinner {\n  width: 16px;\n  height: 16px;\n  border: 2px solid rgba(255, 255, 255, 0.3);\n  border-top: 2px solid white;\n  border-radius: 50%;\n  animation: spin 1s linear infinite;\n}\n.spinner.large {\n  width: 40px;\n  height: 40px;\n  border-width: 4px;\n  border-color: rgba(59, 130, 246, 0.3);\n  border-top-color: #3b82f6;\n}\n@keyframes spin {\n  0% {\n    transform: rotate(0deg);\n  }\n  100% {\n    transform: rotate(360deg);\n  }\n}\n.alert {\n  padding: 1rem 1.5rem;\n  border-radius: 12px;\n  margin-bottom: 1.5rem;\n  display: flex;\n  align-items: center;\n  gap: 0.75rem;\n  font-weight: 500;\n}\n.alert-error {\n  background:\n    linear-gradient(\n      135deg,\n      rgba(254, 242, 242, 0.9),\n      rgba(252, 231, 243, 0.8));\n  border: 1px solid rgba(239, 68, 68, 0.3);\n  color: #dc2626;\n}\n.alert-success {\n  background:\n    linear-gradient(\n      135deg,\n      rgba(236, 253, 245, 0.9),\n      rgba(240, 253, 244, 0.8));\n  border: 1px solid rgba(34, 197, 94, 0.3);\n  color: #059669;\n}\n@media (max-width: 768px) {\n  .profile-container {\n    padding: 1rem;\n  }\n  .header-content {\n    flex-direction: column;\n    align-items: flex-start;\n    gap: 1rem;\n  }\n  .profile-title {\n    font-size: 1.5rem;\n  }\n  .form-row {\n    grid-template-columns: 1fr;\n    gap: 1rem;\n  }\n  .form-section {\n    padding: 1.5rem;\n  }\n  .edit-actions {\n    flex-direction: column;\n    width: 100%;\n  }\n  .info-grid {\n    grid-template-columns: 1fr;\n  }\n}\n@keyframes fadeInUp {\n  0% {\n    opacity: 0;\n    transform: translateY(20px);\n  }\n  100% {\n    opacity: 1;\n    transform: translateY(0);\n  }\n}\n.loading-container {\n  display: flex;\n  justify-content: center;\n  align-items: center;\n  min-height: 400px;\n}\n.loading-content {\n  text-align: center;\n  color: #64748b;\n}\n.loading-content p {\n  margin-top: 1rem;\n  font-size: 1.1rem;\n}\n.address-item {\n  background: rgba(255, 255, 255, 0.8);\n  border: 1px solid rgba(226, 232, 240, 0.8);\n  border-radius: 16px;\n  padding: 1.5rem;\n  margin-bottom: 1rem;\n  transition: all 0.3s ease;\n}\n.address-item:hover {\n  border-color: rgba(59, 130, 246, 0.3);\n  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.1);\n}\n.address-display {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  gap: 1rem;\n}\n.address-content {\n  flex: 1;\n  display: flex;\n  align-items: center;\n  gap: 0.5rem;\n}\n.address-text {\n  color: #374151;\n  font-weight: 500;\n}\n.no-address {\n  color: #9ca3af;\n  font-style: italic;\n}\n.address-actions {\n  display: flex;\n  gap: 0.5rem;\n  align-items: center;\n}\n.address-edit {\n  background: rgba(249, 250, 251, 0.8);\n  border-radius: 12px;\n  padding: 1rem;\n  margin-top: 1rem;\n}\n.no-addresses {\n  text-align: center;\n  padding: 2rem;\n  background: rgba(249, 250, 251, 0.5);\n  border: 2px dashed rgba(209, 213, 219, 0.8);\n  border-radius: 16px;\n  margin-top: 1rem;\n}\n.badge-primary {\n  background:\n    linear-gradient(\n      135deg,\n      #3b82f6,\n      #6366f1);\n  color: white;\n  padding: 0.25rem 0.75rem;\n  border-radius: 12px;\n  font-size: 0.75rem;\n  font-weight: 600;\n}\n@media (max-width: 768px) {\n  .address-display {\n    flex-direction: column;\n    align-items: flex-start;\n    gap: 1rem;\n  }\n  .address-actions {\n    width: 100%;\n    justify-content: flex-end;\n  }\n  .address-edit .row {\n    margin: 0;\n  }\n  .address-edit .col-md-6,\n  .address-edit .col-md-12 {\n    padding: 0 0.5rem;\n  }\n}\n/*# sourceMappingURL=profile.component.css.map */\n'] }]
  }], () => [{ type: AuthService }, { type: DashboardService }, { type: FormBuilder }, { type: FournisseurService }, { type: ImageUrlService }], null);
})();
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && \u0275setClassDebugInfo(ProfileComponent, { className: "ProfileComponent", filePath: "src/app/components/profile/profile.component.ts", lineNumber: 17 });
})();
export {
  ProfileComponent
};
//# sourceMappingURL=chunk-3SNKANDK.js.map

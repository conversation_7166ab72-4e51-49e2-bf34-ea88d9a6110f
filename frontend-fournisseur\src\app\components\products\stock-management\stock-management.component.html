<div class="stock-management-page">
  <!-- Header -->
  <div class="page-header">
    <h1>Gestion du Stock</h1>
    <div class="header-stats">
      <div class="stat-item">
        <span class="stat-value">{{ getGlobalStats().totalProducts }}</span>
        <span class="stat-label">Produits</span>
      </div>
      <div class="stat-item warning">
        <span class="stat-value">{{ getGlobalStats().lowStockProducts }}</span>
        <span class="stat-label">Stock faible</span>
      </div>
      <div class="stat-item danger">
        <span class="stat-value">{{ getGlobalStats().emptyStockProducts }}</span>
        <span class="stat-label">Ruptures</span>
      </div>
      <div class="stat-item success">
        <span class="stat-value">{{ formatPrice(getGlobalStats().totalStockValue) }}</span>
        <span class="stat-label">Valeur stock</span>
      </div>
    </div>
  </div>

  <!-- Alertes de stock -->
  <div class="alerts-section" *ngIf="stockAlerts.length > 0">
    <h2>🚨 Alertes de Stock</h2>
    <div class="alerts-grid">
      <div *ngFor="let alert of stockAlerts" class="alert-card" [class]="getAlertClass(alert)">
        <div class="alert-product">
          <img [src]="getProductImageUrl(alert.product)" [alt]="alert.product.nom" />
          <div class="alert-info">
            <h3>{{ alert.product.nom }}</h3>
            <p>{{ alert.message }}</p>
          </div>
        </div>
        <button class="btn btn-sm btn-primary" (click)="openStockModal(alert.product)">
          📦 Gérer
        </button>
      </div>
    </div>
  </div>

  <!-- Filtres et recherche -->
  <div class="filters-section">
    <div class="search-bar">
      <input
        type="text"
        [(ngModel)]="searchQuery"
        placeholder="Rechercher un produit..."
        class="search-input"
      />
    </div>
    
    <div class="filters">
      <select [(ngModel)]="selectedFilter" class="filter-select">
        <option value="all">Tous les produits</option>
        <option value="active">Produits actifs</option>
        <option value="low">Stock faible</option>
        <option value="empty">Rupture de stock</option>
      </select>
    </div>
  </div>

  <!-- Loading -->
  <div *ngIf="isLoading" class="loading">
    <div class="spinner"></div>
    <p>Chargement des produits...</p>
  </div>

  <!-- Error -->
  <div *ngIf="error" class="error-message">
    {{ error }}
  </div>

  <!-- Liste des produits -->
  <div *ngIf="!isLoading && !error" class="products-section">
    <h2>📦 Gestion du Stock par Produit</h2>
    
    <div class="products-table">
      <div class="table-header">
        <div class="col-product">Produit</div>
        <div class="col-stock">Stock actuel</div>
        <div class="col-alert">Seuil d'alerte</div>
        <div class="col-value">Valeur stock</div>
        <div class="col-status">Statut</div>
        <div class="col-actions">Actions</div>
      </div>
      
      <div *ngFor="let product of getFilteredProducts()" class="table-row">
        <div class="col-product">
          <div class="product-info">
            <img [src]="getProductImageUrl(product)" [alt]="product.nom" />
            <div class="product-details">
              <h3>{{ product.nom }}</h3>
              <p>{{ getProductCategory(product) }}</p>
            </div>
          </div>
        </div>

        <div class="col-stock">
          <div class="stock-display" [class]="getStockLevelClass(product)">
            <span class="stock-number">{{ product.stock }}</span>
            <span class="stock-unit">unités</span>
          </div>
        </div>

        <div class="col-alert">
          <span class="alert-threshold">5</span>
        </div>
        
        <div class="col-value">
          <strong>{{ formatPrice(getStockValue(product)) }}</strong>
        </div>
        
        <div class="col-status">
          <span class="status-badge" [class]="getStockLevelClass(product)">
            {{ getStockLevelText(product) }}
          </span>
        </div>
        
        <div class="col-actions">
          <button class="btn btn-sm btn-primary" (click)="openStockModal(product)">
            📦 Gérer Stock
          </button>
        </div>
      </div>
    </div>

    <!-- Message si aucun produit -->
    <div *ngIf="getFilteredProducts().length === 0" class="no-products">
      <div class="no-products-icon">📦</div>
      <h3>Aucun produit trouvé</h3>
      <p>Aucun produit ne correspond à vos critères de recherche.</p>
    </div>
  </div>

  <!-- Historique des mouvements -->
  <div class="movements-section">
    <h2>📋 Historique des Mouvements</h2>

    <div class="movements-table movements-history">
      <div class="table-header">
        <div class="col-date">Date</div>
        <div class="col-product">Produit</div>
        <div class="col-type">Type</div>
        <div class="col-quantity">Quantité</div>
        <div class="col-reason">Raison</div>
        <div class="col-user">Utilisateur</div>
      </div>

      <div *ngFor="let movement of stockMovements.slice(0, 10)" class="table-row">
        <div class="col-date movement-date">
          {{ formatDate(movement.date) }}
        </div>

        <div class="col-product">
          {{ movement.productName }}
        </div>

        <div class="col-type">
          <div class="movement-type" [ngClass]="getMovementClass(movement.type)">
            <span class="movement-icon">{{ getMovementIcon(movement.type) }}</span>
            {{ movement.type === 'in' ? 'Entrée' : 'Sortie' }}
          </div>
        </div>

        <div class="col-quantity">
          <span class="movement-quantity" [ngClass]="getMovementClass(movement.type)">
            {{ movement.type === 'in' ? '+' : '-' }}{{ movement.quantity }}
          </span>
        </div>

        <div class="col-reason">
          {{ movement.reason }}
        </div>

        <div class="col-user movement-user">
          {{ movement.user }}
        </div>
      </div>

      <!-- Message si aucun mouvement -->
      <div *ngIf="stockMovements.length === 0" class="no-movements">
        <div class="no-movements-icon">📋</div>
        <h3>Aucun mouvement enregistré</h3>
        <p>Les mouvements de stock apparaîtront ici après vos premières modifications.</p>
      </div>
    </div>
  </div>

  <!-- Modal de gestion du stock -->
  <div class="modal-overlay" *ngIf="showStockModal" (click)="closeStockModal()">
    <div class="modal-content" (click)="$event.stopPropagation()">
      <div class="modal-header">
        <h2>Gestion du Stock</h2>
        <button type="button" class="btn-close" (click)="closeStockModal()">✕</button>
      </div>
      
      <div class="modal-body" *ngIf="selectedProduct">
        <div class="product-summary">
          <img [src]="getProductImageUrl(selectedProduct)" [alt]="selectedProduct.nom" />
          <div class="product-info">
            <h3>{{ selectedProduct.nom }}</h3>
            <p>Stock actuel : <strong>{{ selectedProduct.stock }} unités</strong></p>
            <p>Valeur : <strong>{{ formatPrice(getStockValue(selectedProduct)) }}</strong></p>
          </div>
        </div>
        
        <form class="stock-form">
          <div class="form-group">
            <label>Type d'opération</label>
            <div class="radio-group">
              <label class="radio-label">
                <input type="radio" [(ngModel)]="stockOperation.type" value="in" name="operationType" />
                <span class="radio-text">📥 Entrée de stock</span>
              </label>
              <label class="radio-label">
                <input type="radio" [(ngModel)]="stockOperation.type" value="out" name="operationType" />
                <span class="radio-text">📤 Sortie de stock</span>
              </label>
            </div>
          </div>
          
          <div class="form-group">
            <label for="quantity">Quantité</label>
            <input
              type="number"
              id="quantity"
              [(ngModel)]="stockOperation.quantity"
              class="form-control"
              min="1"
              placeholder="0"
            />
          </div>
          
          <div class="form-group">
            <label for="reason">Raison</label>
            <select
              id="reason"
              [(ngModel)]="stockOperation.reason"
              class="form-control"
            >
              <option value="">Sélectionner une raison</option>
              <option *ngFor="let reason of stockReasons[stockOperation.type]" [value]="reason">
                {{ reason }}
              </option>
            </select>
          </div>
          
          <div class="stock-preview" *ngIf="stockOperation.quantity > 0">
            <div class="preview-info">
              <span>Nouveau stock : </span>
              <strong [class.text-success]="stockOperation.type === 'in'" [class.text-danger]="stockOperation.type === 'out'">
                {{ stockOperation.type === 'in' 
                  ? selectedProduct.stock + stockOperation.quantity 
                  : selectedProduct.stock - stockOperation.quantity }} unités
              </strong>
            </div>
          </div>
        </form>
      </div>
      
      <div class="modal-actions">
        <button type="button" class="btn btn-secondary" (click)="closeStockModal()">
          Annuler
        </button>
        <button 
          type="button" 
          class="btn btn-primary" 
          (click)="applyStockOperation()"
          [disabled]="stockOperation.quantity <= 0 || !stockOperation.reason"
        >
          Appliquer
        </button>
      </div>
    </div>
  </div>
</div>

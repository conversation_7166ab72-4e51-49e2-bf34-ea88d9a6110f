{"version": 3, "sources": ["src/app/services/categorie.service.ts"], "sourcesContent": ["import { Injectable } from '@angular/core';\nimport { HttpClient } from '@angular/common/http';\nimport { Observable } from 'rxjs';\nimport { tap } from 'rxjs/operators';\nimport {\n  Categorie,\n  CategorieCreate,\n  CategorieUpdate,\n  CategorieResponse,\n  CategorieListResponse,\n  CategorieDropdown,\n  CategorieAdmin\n} from '../models';\nimport { environment } from '../../environments/environment';\n\n@Injectable({\n  providedIn: 'root'\n})\nexport class CategorieService {\n  private readonly API_URL = `${environment.apiUrl || 'https://localhost:7264/api'}/Categories`;\n\n  constructor(private http: HttpClient) {}\n\n  /**\n   * GET /api/Categories - Obtenir toutes les catégories\n   */\n  getAll(): Observable<Categorie[]> {\n    console.log('📦 Récupération des catégories');\n    return this.http.get<Categorie[]>(`${this.API_URL}/enriched`)\n      .pipe(\n        tap(response => console.log('✅ Catégories récupérées:', response))\n      );\n  }\n\n  /**\n   * GET /api/Categories/admin - Obtenir toutes les catégories pour les admins (y compris non validées)\n   */\n  getAllForAdmin(): Observable<CategorieAdmin[]> {\n    console.log('📦 Récupération des catégories pour admin');\n    return this.http.get<CategorieAdmin[]>(`${this.API_URL}/admin`)\n      .pipe(\n        tap(response => console.log('✅ Catégories admin récupérées:', response))\n      );\n  }\n\n  /**\n   * GET /api/Categories/{id} - Obtenir une catégorie par ID\n   */\n  getById(id: number): Observable<Categorie> {\n    console.log('🔍 Récupération de la catégorie ID:', id);\n    return this.http.get<Categorie>(`${this.API_URL}/${id}`)\n      .pipe(\n        tap(response => console.log('✅ Catégorie récupérée:', response))\n      );\n  }\n\n  /**\n   * POST /api/Categories - Créer une nouvelle catégorie\n   */\n  create(categorie: CategorieCreate): Observable<Categorie> {\n    console.log('➕ Création d\\'une nouvelle catégorie:', categorie);\n    return this.http.post<Categorie>(this.API_URL, categorie)\n      .pipe(\n        tap(response => console.log('✅ Catégorie créée:', response))\n      );\n  }\n\n  /**\n   * PUT /api/Categories/{id} - Mettre à jour une catégorie\n   */\n  update(id: number, categorie: CategorieUpdate): Observable<Categorie> {\n    console.log('✏️ Mise à jour de la catégorie ID:', id, categorie);\n    return this.http.put<Categorie>(`${this.API_URL}/${id}`, categorie)\n      .pipe(\n        tap(response => console.log('✅ Catégorie mise à jour:', response))\n      );\n  }\n\n  /**\n   * DELETE /api/Categories/{id} - Supprimer une catégorie\n   */\n  delete(id: number): Observable<void> {\n    console.log('🗑️ Suppression de la catégorie ID:', id);\n    return this.http.delete<void>(`${this.API_URL}/${id}`)\n      .pipe(\n        tap(() => console.log('✅ Catégorie supprimée:', id))\n      );\n  }\n\n  /**\n   * GET /api/Categories/{id}/sous-categories - Obtenir les sous-catégories d'une catégorie\n   */\n  getSousCategories(id: number): Observable<any[]> {\n    console.log('📂 Récupération des sous-catégories pour la catégorie:', id);\n    return this.http.get<any[]>(`${this.API_URL}/${id}/sous-categories`)\n      .pipe(\n        tap(response => console.log('✅ Sous-catégories récupérées:', response))\n      );\n  }\n\n  /**\n   * GET /api/Categories/{id}/produits-count - Obtenir le nombre de produits d'une catégorie\n   */\n  getProduitsCount(id: number): Observable<number> {\n    console.log('🔢 Récupération du nombre de produits pour la catégorie:', id);\n    return this.http.get<number>(`${this.API_URL}/${id}/produits-count`)\n      .pipe(\n        tap(response => console.log('✅ Nombre de produits récupéré:', response))\n      );\n  }\n\n  /**\n   * GET /api/Categories/dropdown - Obtenir les catégories pour dropdown\n   */\n  getDropdown(): Observable<CategorieDropdown[]> {\n    console.log('📋 Récupération des catégories pour dropdown');\n    return this.http.get<CategorieDropdown[]>(`${this.API_URL}/dropdown`)\n      .pipe(\n        tap(response => console.log('✅ Dropdown catégories récupéré:', response))\n      );\n  }\n\n  /**\n   * PATCH /api/Categories/{id}/toggle-visibility - Basculer la visibilité d'une catégorie\n   */\n  toggleVisibility(id: number): Observable<Categorie> {\n    console.log('👁️ Basculer la visibilité de la catégorie:', id);\n    return this.http.patch<Categorie>(`${this.API_URL}/${id}/toggle-visibility`, {})\n      .pipe(\n        tap(response => console.log('✅ Visibilité basculée:', response))\n      );\n  }\n}\n"], "mappings": ";;;;;;;;;;;;;;;AAkBM,IAAO,mBAAP,MAAO,kBAAgB;EAGP;EAFH,UAAU,GAAG,YAAY,UAAU,4BAA4B;EAEhF,YAAoB,MAAgB;AAAhB,SAAA,OAAA;EAAmB;;;;EAKvC,SAAM;AACJ,YAAQ,IAAI,gDAAgC;AAC5C,WAAO,KAAK,KAAK,IAAiB,GAAG,KAAK,OAAO,WAAW,EACzD,KACC,IAAI,cAAY,QAAQ,IAAI,6CAA4B,QAAQ,CAAC,CAAC;EAExE;;;;EAKA,iBAAc;AACZ,YAAQ,IAAI,2DAA2C;AACvD,WAAO,KAAK,KAAK,IAAsB,GAAG,KAAK,OAAO,QAAQ,EAC3D,KACC,IAAI,cAAY,QAAQ,IAAI,mDAAkC,QAAQ,CAAC,CAAC;EAE9E;;;;EAKA,QAAQ,IAAU;AAChB,YAAQ,IAAI,uDAAuC,EAAE;AACrD,WAAO,KAAK,KAAK,IAAe,GAAG,KAAK,OAAO,IAAI,EAAE,EAAE,EACpD,KACC,IAAI,cAAY,QAAQ,IAAI,2CAA0B,QAAQ,CAAC,CAAC;EAEtE;;;;EAKA,OAAO,WAA0B;AAC/B,YAAQ,IAAI,mDAAyC,SAAS;AAC9D,WAAO,KAAK,KAAK,KAAgB,KAAK,SAAS,SAAS,EACrD,KACC,IAAI,cAAY,QAAQ,IAAI,oCAAsB,QAAQ,CAAC,CAAC;EAElE;;;;EAKA,OAAO,IAAY,WAA0B;AAC3C,YAAQ,IAAI,sDAAsC,IAAI,SAAS;AAC/D,WAAO,KAAK,KAAK,IAAe,GAAG,KAAK,OAAO,IAAI,EAAE,IAAI,SAAS,EAC/D,KACC,IAAI,cAAY,QAAQ,IAAI,uCAA4B,QAAQ,CAAC,CAAC;EAExE;;;;EAKA,OAAO,IAAU;AACf,YAAQ,IAAI,sDAAuC,EAAE;AACrD,WAAO,KAAK,KAAK,OAAa,GAAG,KAAK,OAAO,IAAI,EAAE,EAAE,EAClD,KACC,IAAI,MAAM,QAAQ,IAAI,qCAA0B,EAAE,CAAC,CAAC;EAE1D;;;;EAKA,kBAAkB,IAAU;AAC1B,YAAQ,IAAI,6EAA0D,EAAE;AACxE,WAAO,KAAK,KAAK,IAAW,GAAG,KAAK,OAAO,IAAI,EAAE,kBAAkB,EAChE,KACC,IAAI,cAAY,QAAQ,IAAI,kDAAiC,QAAQ,CAAC,CAAC;EAE7E;;;;EAKA,iBAAiB,IAAU;AACzB,YAAQ,IAAI,4EAA4D,EAAE;AAC1E,WAAO,KAAK,KAAK,IAAY,GAAG,KAAK,OAAO,IAAI,EAAE,iBAAiB,EAChE,KACC,IAAI,cAAY,QAAQ,IAAI,gDAAkC,QAAQ,CAAC,CAAC;EAE9E;;;;EAKA,cAAW;AACT,YAAQ,IAAI,8DAA8C;AAC1D,WAAO,KAAK,KAAK,IAAyB,GAAG,KAAK,OAAO,WAAW,EACjE,KACC,IAAI,cAAY,QAAQ,IAAI,oDAAmC,QAAQ,CAAC,CAAC;EAE/E;;;;EAKA,iBAAiB,IAAU;AACzB,YAAQ,IAAI,iEAA+C,EAAE;AAC7D,WAAO,KAAK,KAAK,MAAiB,GAAG,KAAK,OAAO,IAAI,EAAE,sBAAsB,CAAA,CAAE,EAC5E,KACC,IAAI,cAAY,QAAQ,IAAI,qCAA0B,QAAQ,CAAC,CAAC;EAEtE;;qCAjHW,mBAAgB,mBAAA,UAAA,CAAA;EAAA;4EAAhB,mBAAgB,SAAhB,kBAAgB,WAAA,YAFf,OAAM,CAAA;;;sEAEP,kBAAgB,CAAA;UAH5B;WAAW;MACV,YAAY;KACb;;;", "names": []}
// Script pour créer un compte fournisseur de test
const API_URL = 'http://localhost:5014/api';

async function createTestFournisseur() {
    try {
        // Créer un FormData pour l'inscription fournisseur
        const formData = new FormData();
        
        // Données utilisateur de base
        formData.append('Email', '<EMAIL>');
        formData.append('Password', 'TestFournisseur123!');
        formData.append('Nom', 'TestFournisseur');
        formData.append('Prenom', 'Société');
        formData.append('PhoneNumber', '12345678');
        formData.append('DateNaissance', '1990-01-01');
        
        // Données spécifiques fournisseur
        formData.append('MatriculeFiscale', '12345678');
        formData.append('RaisonSociale', 'Test Fournisseur SARL');
        formData.append('Description', 'Fournisseur de test pour les avis');
        formData.append('RIB', '12345678901234567890');
        formData.append('CodeBanque', '123');
        formData.append('Commission', '0.75');
        formData.append('DelaiPreparationJours', '2');
        formData.append('FraisLivraisonBase', '9.99');
        
        // Adresse
        formData.append('Rue', '123 Rue de Test');
        formData.append('Ville', 'Tunis');
        formData.append('CodePostal', '1000');
        formData.append('Pays', 'Tunisie');
        
        // Logo (fichier vide pour le test)
        const logoBlob = new Blob([''], { type: 'image/png' });
        formData.append('LogoFile', logoBlob, 'test-logo.png');
        
        console.log('🚀 Création du compte fournisseur de test...');
        
        const response = await fetch(`${API_URL}/Auth/register/fournisseur`, {
            method: 'POST',
            body: formData
        });
        
        if (response.ok) {
            const result = await response.json();
            console.log('✅ Compte fournisseur créé avec succès:', result);
            
            // Maintenant, connectons-nous avec ce compte
            await loginTestFournisseur();
        } else {
            const error = await response.text();
            console.error('❌ Erreur lors de la création:', error);
        }
        
    } catch (error) {
        console.error('❌ Erreur:', error);
    }
}

async function loginTestFournisseur() {
    try {
        console.log('🔐 Connexion avec le compte fournisseur de test...');
        
        const loginData = {
            email: '<EMAIL>',
            password: 'TestFournisseur123!'
        };
        
        const response = await fetch(`${API_URL}/Auth/login`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(loginData)
        });
        
        if (response.ok) {
            const result = await response.json();
            console.log('✅ Connexion réussie:', result);
            console.log('🎫 Token:', result.token);
            console.log('👤 Utilisateur:', result.utilisateur);
            
            // Stocker le token dans localStorage pour le frontend
            localStorage.setItem('auth_token', result.token);
            localStorage.setItem('current_user', JSON.stringify(result.utilisateur));
            
            console.log('💾 Token stocké dans localStorage');
            console.log('🔄 Rechargez la page pour voir les avis !');
            
        } else {
            const error = await response.text();
            console.error('❌ Erreur lors de la connexion:', error);
        }
        
    } catch (error) {
        console.error('❌ Erreur:', error);
    }
}

// Exécuter le script
createTestFournisseur();

{"version": 3, "sources": ["src/app/components/admin/user-management/user-management.component.css"], "sourcesContent": [".user-management-container {\n  padding: 2rem;\n  max-width: 1400px;\n  margin: 0 auto;\n}\n\n/* Header */\n.page-header {\n  margin-bottom: 2rem;\n}\n\n.header-content {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  flex-wrap: wrap;\n  gap: 1rem;\n}\n\n.page-title {\n  display: flex;\n  align-items: center;\n  gap: 0.75rem;\n  font-size: 2rem;\n  font-weight: 600;\n  color: #1a202c;\n  margin: 0;\n}\n\n.header-actions {\n  display: flex;\n  gap: 1rem;\n}\n\n/* Filtres */\n.filters-section {\n  background: white;\n  border-radius: 12px;\n  padding: 1.5rem;\n  margin-bottom: 2rem;\n  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);\n  display: flex;\n  gap: 1rem;\n  flex-wrap: wrap;\n  align-items: center;\n}\n\n.search-box {\n  position: relative;\n  flex: 1;\n  min-width: 300px;\n}\n\n.search-box i {\n  position: absolute;\n  left: 1rem;\n  top: 50%;\n  transform: translateY(-50%);\n  color: #64748b;\n}\n\n.search-input {\n  width: 100%;\n  padding: 0.75rem 1rem 0.75rem 2.5rem;\n  border: 2px solid #e2e8f0;\n  border-radius: 8px;\n  font-size: 1rem;\n  transition: border-color 0.2s;\n}\n\n.search-input:focus {\n  outline: none;\n  border-color: #3b82f6;\n}\n\n.filters {\n  display: flex;\n  gap: 1rem;\n}\n\n.filter-select {\n  padding: 0.75rem 1rem;\n  border: 2px solid #e2e8f0;\n  border-radius: 8px;\n  font-size: 1rem;\n  background: white;\n  min-width: 150px;\n}\n\n/* Messages */\n.error-message {\n  background: #fef2f2;\n  border: 1px solid #fecaca;\n  color: #dc2626;\n  padding: 1rem;\n  border-radius: 8px;\n  margin-bottom: 2rem;\n  display: flex;\n  align-items: center;\n  gap: 0.5rem;\n}\n\n.loading-container {\n  text-align: center;\n  padding: 3rem;\n}\n\n.loading-spinner {\n  width: 40px;\n  height: 40px;\n  border: 4px solid #e2e8f0;\n  border-top: 4px solid #3b82f6;\n  border-radius: 50%;\n  animation: spin 1s linear infinite;\n  margin: 0 auto 1rem;\n}\n\n@keyframes spin {\n  0% { transform: rotate(0deg); }\n  100% { transform: rotate(360deg); }\n}\n\n/* Tableau */\n.table-container {\n  background: white;\n  border-radius: 12px;\n  overflow: hidden;\n  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);\n  margin-bottom: 2rem;\n}\n\n.users-table {\n  width: 100%;\n  border-collapse: collapse;\n}\n\n.users-table th {\n  background: #f8fafc;\n  padding: 1rem;\n  text-align: left;\n  font-weight: 600;\n  color: #374151;\n  border-bottom: 1px solid #e5e7eb;\n}\n\n.user-row {\n  border-bottom: 1px solid #f3f4f6;\n  transition: background-color 0.2s;\n}\n\n.user-row:hover {\n  background: #f9fafb;\n}\n\n.users-table td {\n  padding: 1rem;\n  vertical-align: middle;\n}\n\n/* Cellules spécifiques */\n.user-info {\n  display: flex;\n  align-items: center;\n  gap: 1rem;\n}\n\n.user-avatar {\n  width: 40px;\n  height: 40px;\n  border-radius: 50%;\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  color: white;\n  font-weight: 600;\n  font-size: 0.875rem;\n}\n\n.user-details {\n  display: flex;\n  flex-direction: column;\n}\n\n.user-name {\n  font-weight: 600;\n  color: #1a202c;\n}\n\n.user-id {\n  font-size: 0.875rem;\n  color: #64748b;\n}\n\n.user-email {\n  color: #4f46e5;\n  font-family: monospace;\n}\n\n/* Badges */\n.role-badge, .status-badge {\n  padding: 0.25rem 0.75rem;\n  border-radius: 9999px;\n  font-size: 0.875rem;\n  font-weight: 500;\n}\n\n.role-admin {\n  background: #fef3c7;\n  color: #d97706;\n}\n\n.role-fournisseur {\n  background: #dbeafe;\n  color: #2563eb;\n}\n\n.role-client {\n  background: #d1fae5;\n  color: #059669;\n}\n\n.role-default {\n  background: #f3f4f6;\n  color: #6b7280;\n}\n\n.status-active {\n  background: #d1fae5;\n  color: #059669;\n}\n\n.status-inactive {\n  background: #fee2e2;\n  color: #dc2626;\n}\n\n.date-cell {\n  color: #64748b;\n  font-size: 0.875rem;\n}\n\n.no-connection {\n  color: #9ca3af;\n  font-style: italic;\n}\n\n/* Actions */\n.actions-cell {\n  width: 120px;\n}\n\n.action-buttons {\n  display: flex;\n  gap: 0.5rem;\n}\n\n.btn-action {\n  width: 36px;\n  height: 36px;\n  border: none;\n  border-radius: 6px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  cursor: pointer;\n  transition: all 0.2s;\n}\n\n.btn-activate {\n  background: #d1fae5;\n  color: #059669;\n}\n\n.btn-activate:hover {\n  background: #a7f3d0;\n}\n\n.btn-deactivate {\n  background: #fef3c7;\n  color: #d97706;\n}\n\n.btn-deactivate:hover {\n  background: #fde68a;\n}\n\n.btn-delete {\n  background: #fee2e2;\n  color: #dc2626;\n}\n\n.btn-delete:hover {\n  background: #fecaca;\n}\n\n/* Boutons généraux */\n.btn {\n  padding: 0.75rem 1.5rem;\n  border: none;\n  border-radius: 8px;\n  font-weight: 500;\n  cursor: pointer;\n  transition: all 0.2s;\n  display: flex;\n  align-items: center;\n  gap: 0.5rem;\n}\n\n.btn-primary {\n  background: #3b82f6;\n  color: white;\n}\n\n.btn-primary:hover {\n  background: #2563eb;\n}\n\n.btn-secondary {\n  background: #f3f4f6;\n  color: #374151;\n}\n\n.btn-secondary:hover {\n  background: #e5e7eb;\n}\n\n.btn:disabled {\n  opacity: 0.5;\n  cursor: not-allowed;\n}\n\n/* No data */\n.no-data {\n  text-align: center;\n  padding: 3rem;\n  color: #64748b;\n}\n\n.no-data i {\n  font-size: 3rem;\n  margin-bottom: 1rem;\n  opacity: 0.5;\n}\n\n.no-data h3 {\n  margin: 0 0 0.5rem 0;\n  color: #374151;\n}\n\n/* Pagination */\n.pagination-container {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  padding: 1rem;\n  background: white;\n  border-radius: 8px;\n  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);\n}\n\n.pagination-info {\n  color: #64748b;\n  font-size: 0.875rem;\n}\n\n.pagination {\n  display: flex;\n  align-items: center;\n  gap: 1rem;\n}\n\n.btn-page {\n  width: 36px;\n  height: 36px;\n  border: 1px solid #e5e7eb;\n  background: white;\n  border-radius: 6px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  cursor: pointer;\n  transition: all 0.2s;\n}\n\n.btn-page:hover:not(:disabled) {\n  background: #f3f4f6;\n}\n\n.btn-page:disabled {\n  opacity: 0.5;\n  cursor: not-allowed;\n}\n\n.page-info {\n  font-weight: 500;\n  color: #374151;\n}\n\n/* Icons */\n.icon-users::before { content: \"👥\"; }\n.icon-refresh::before { content: \"🔄\"; }\n.icon-download::before { content: \"📥\"; }\n.icon-search::before { content: \"🔍\"; }\n.icon-alert::before { content: \"⚠️\"; }\n.icon-play::before { content: \"▶️\"; }\n.icon-pause::before { content: \"⏸️\"; }\n.icon-trash::before { content: \"🗑️\"; }\n.icon-chevron-left::before { content: \"◀️\"; }\n.icon-chevron-right::before { content: \"▶️\"; }\n.icon-filter-x::before { content: \"🚫\"; }\n.icon-test::before { content: \"🧪\"; }\n\n/* Responsive */\n@media (max-width: 768px) {\n  .user-management-container {\n    padding: 1rem;\n  }\n\n  .header-content {\n    flex-direction: column;\n    align-items: stretch;\n  }\n\n  .filters-section {\n    flex-direction: column;\n  }\n\n  .search-box {\n    min-width: auto;\n  }\n\n  .users-table {\n    font-size: 0.875rem;\n  }\n\n  .users-table th,\n  .users-table td {\n    padding: 0.75rem 0.5rem;\n  }\n\n  .pagination-container {\n    flex-direction: column;\n    gap: 1rem;\n  }\n}\n"], "mappings": ";AAAA,CAAC;AACC,WAAS;AACT,aAAW;AACX,UAAQ,EAAE;AACZ;AAGA,CAAC;AACC,iBAAe;AACjB;AAEA,CAAC;AACC,WAAS;AACT,mBAAiB;AACjB,eAAa;AACb,aAAW;AACX,OAAK;AACP;AAEA,CAAC;AACC,WAAS;AACT,eAAa;AACb,OAAK;AACL,aAAW;AACX,eAAa;AACb,SAAO;AACP,UAAQ;AACV;AAEA,CAAC;AACC,WAAS;AACT,OAAK;AACP;AAGA,CAAC;AACC,cAAY;AACZ,iBAAe;AACf,WAAS;AACT,iBAAe;AACf,cAAY,EAAE,IAAI,IAAI,KAAK,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE;AACpC,WAAS;AACT,OAAK;AACL,aAAW;AACX,eAAa;AACf;AAEA,CAAC;AACC,YAAU;AACV,QAAM;AACN,aAAW;AACb;AAEA,CANC,WAMW;AACV,YAAU;AACV,QAAM;AACN,OAAK;AACL,aAAW,WAAW;AACtB,SAAO;AACT;AAEA,CAAC;AACC,SAAO;AACP,WAAS,QAAQ,KAAK,QAAQ;AAC9B,UAAQ,IAAI,MAAM;AAClB,iBAAe;AACf,aAAW;AACX,cAAY,aAAa;AAC3B;AAEA,CATC,YASY;AACX,WAAS;AACT,gBAAc;AAChB;AAEA,CAAC;AACC,WAAS;AACT,OAAK;AACP;AAEA,CAAC;AACC,WAAS,QAAQ;AACjB,UAAQ,IAAI,MAAM;AAClB,iBAAe;AACf,aAAW;AACX,cAAY;AACZ,aAAW;AACb;AAGA,CAAC;AACC,cAAY;AACZ,UAAQ,IAAI,MAAM;AAClB,SAAO;AACP,WAAS;AACT,iBAAe;AACf,iBAAe;AACf,WAAS;AACT,eAAa;AACb,OAAK;AACP;AAEA,CAAC;AACC,cAAY;AACZ,WAAS;AACX;AAEA,CAAC;AACC,SAAO;AACP,UAAQ;AACR,UAAQ,IAAI,MAAM;AAClB,cAAY,IAAI,MAAM;AACtB,iBAAe;AACf,aAAW,KAAK,GAAG,OAAO;AAC1B,UAAQ,EAAE,KAAK;AACjB;AAEA,WAJa;AAKX;AAAK,eAAW,OAAO;AAAO;AAC9B;AAAO,eAAW,OAAO;AAAS;AACpC;AAGA,CAAC;AACC,cAAY;AACZ,iBAAe;AACf,YAAU;AACV,cAAY,EAAE,IAAI,IAAI,KAAK,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE;AACpC,iBAAe;AACjB;AAEA,CAAC;AACC,SAAO;AACP,mBAAiB;AACnB;AAEA,CALC,YAKY;AACX,cAAY;AACZ,WAAS;AACT,cAAY;AACZ,eAAa;AACb,SAAO;AACP,iBAAe,IAAI,MAAM;AAC3B;AAEA,CAAC;AACC,iBAAe,IAAI,MAAM;AACzB,cAAY,iBAAiB;AAC/B;AAEA,CALC,QAKQ;AACP,cAAY;AACd;AAEA,CAvBC,YAuBY;AACX,WAAS;AACT,kBAAgB;AAClB;AAGA,CAAC;AACC,WAAS;AACT,eAAa;AACb,OAAK;AACP;AAEA,CAAC;AACC,SAAO;AACP,UAAQ;AACR,iBAAe;AACf;AAAA,IAAY;AAAA,MAAgB,MAAhB;AAAA,MAAwB,QAAQ,EAAhC;AAAA,MAAoC,QAAQ;AACxD,WAAS;AACT,eAAa;AACb,mBAAiB;AACjB,SAAO;AACP,eAAa;AACb,aAAW;AACb;AAEA,CAAC;AACC,WAAS;AACT,kBAAgB;AAClB;AAEA,CAAC;AACC,eAAa;AACb,SAAO;AACT;AAEA,CAAC;AACC,aAAW;AACX,SAAO;AACT;AAEA,CAAC;AACC,SAAO;AACP,eAAa;AACf;AAGA,CAAC;AAAY,CAAC;AACZ,WAAS,QAAQ;AACjB,iBAAe;AACf,aAAW;AACX,eAAa;AACf;AAEA,CAAC;AACC,cAAY;AACZ,SAAO;AACT;AAEA,CAAC;AACC,cAAY;AACZ,SAAO;AACT;AAEA,CAAC;AACC,cAAY;AACZ,SAAO;AACT;AAEA,CAAC;AACC,cAAY;AACZ,SAAO;AACT;AAEA,CAAC;AACC,cAAY;AACZ,SAAO;AACT;AAEA,CAAC;AACC,cAAY;AACZ,SAAO;AACT;AAEA,CAAC;AACC,SAAO;AACP,aAAW;AACb;AAEA,CAAC;AACC,SAAO;AACP,cAAY;AACd;AAGA,CAAC;AACC,SAAO;AACT;AAEA,CAAC;AACC,WAAS;AACT,OAAK;AACP;AAEA,CAAC;AACC,SAAO;AACP,UAAQ;AACR,UAAQ;AACR,iBAAe;AACf,WAAS;AACT,eAAa;AACb,mBAAiB;AACjB,UAAQ;AACR,cAAY,IAAI;AAClB;AAEA,CAAC;AACC,cAAY;AACZ,SAAO;AACT;AAEA,CALC,YAKY;AACX,cAAY;AACd;AAEA,CAAC;AACC,cAAY;AACZ,SAAO;AACT;AAEA,CALC,cAKc;AACb,cAAY;AACd;AAEA,CAAC;AACC,cAAY;AACZ,SAAO;AACT;AAEA,CALC,UAKU;AACT,cAAY;AACd;AAGA,CAAC;AACC,WAAS,QAAQ;AACjB,UAAQ;AACR,iBAAe;AACf,eAAa;AACb,UAAQ;AACR,cAAY,IAAI;AAChB,WAAS;AACT,eAAa;AACb,OAAK;AACP;AAEA,CAAC;AACC,cAAY;AACZ,SAAO;AACT;AAEA,CALC,WAKW;AACV,cAAY;AACd;AAEA,CAAC;AACC,cAAY;AACZ,SAAO;AACT;AAEA,CALC,aAKa;AACZ,cAAY;AACd;AAEA,CA9BC,GA8BG;AACF,WAAS;AACT,UAAQ;AACV;AAGA,CAAC;AACC,cAAY;AACZ,WAAS;AACT,SAAO;AACT;AAEA,CANC,QAMQ;AACP,aAAW;AACX,iBAAe;AACf,WAAS;AACX;AAEA,CAZC,QAYQ;AACP,UAAQ,EAAE,EAAE,OAAO;AACnB,SAAO;AACT;AAGA,CAAC;AACC,WAAS;AACT,mBAAiB;AACjB,eAAa;AACb,WAAS;AACT,cAAY;AACZ,iBAAe;AACf,cAAY,EAAE,IAAI,IAAI,KAAK,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE;AACtC;AAEA,CAAC;AACC,SAAO;AACP,aAAW;AACb;AAEA,CAAC;AACC,WAAS;AACT,eAAa;AACb,OAAK;AACP;AAEA,CAAC;AACC,SAAO;AACP,UAAQ;AACR,UAAQ,IAAI,MAAM;AAClB,cAAY;AACZ,iBAAe;AACf,WAAS;AACT,eAAa;AACb,mBAAiB;AACjB,UAAQ;AACR,cAAY,IAAI;AAClB;AAEA,CAbC,QAaQ,MAAM,KAAK;AAClB,cAAY;AACd;AAEA,CAjBC,QAiBQ;AACP,WAAS;AACT,UAAQ;AACV;AAEA,CAAC;AACC,eAAa;AACb,SAAO;AACT;AAGA,CAAC,UAAU;AAAW,WAAS;AAAM;AACrC,CAAC,YAAY;AAAW,WAAS;AAAM;AACvC,CAAC,aAAa;AAAW,WAAS;AAAM;AACxC,CAAC,WAAW;AAAW,WAAS;AAAM;AACtC,CAAC,UAAU;AAAW,WAAS;AAAM;AACrC,CAAC,SAAS;AAAW,WAAS;AAAM;AACpC,CAAC,UAAU;AAAW,WAAS;AAAM;AACrC,CAAC,UAAU;AAAW,WAAS;AAAO;AACtC,CAAC,iBAAiB;AAAW,WAAS;AAAM;AAC5C,CAAC,kBAAkB;AAAW,WAAS;AAAM;AAC7C,CAAC,aAAa;AAAW,WAAS;AAAM;AACxC,CAAC,SAAS;AAAW,WAAS;AAAM;AAGpC,OAAO,CAAC,SAAS,EAAE;AACjB,GA/ZD;AAgaG,aAAS;AACX;AAEA,GAxZD;AAyZG,oBAAgB;AAChB,iBAAa;AACf;AAEA,GArYD;AAsYG,oBAAgB;AAClB;AAEA,GA7XD;AA8XG,eAAW;AACb;AAEA,GA7SD;AA8SG,eAAW;AACb;AAEA,GAjTD,YAiTc;AAAA,EACb,CAlTD,YAkTc;AACX,aAAS,QAAQ;AACnB;AAEA,GA1FD;AA2FG,oBAAgB;AAChB,SAAK;AACP;AACF;", "names": []}
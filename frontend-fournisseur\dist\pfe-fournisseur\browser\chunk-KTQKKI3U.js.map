{"version": 3, "sources": ["src/app/services/marque.service.ts", "src/app/services/forme.service.ts", "src/app/services/taux-tva.service.ts"], "sourcesContent": ["import { Injectable } from '@angular/core';\nimport { HttpClient } from '@angular/common/http';\nimport { Observable } from 'rxjs';\nimport { tap } from 'rxjs/operators';\nimport {\n  Marque,\n  MarqueCreate,\n  MarqueUpdate,\n  MarqueDropdown\n} from '../models';\nimport { environment } from '../../environments/environment';\n\n@Injectable({\n  providedIn: 'root'\n})\nexport class MarqueService {\n  private readonly API_URL = `${environment.apiUrl || 'https://localhost:7264/api'}/Marques`;\n\n  constructor(private http: HttpClient) {}\n\n  /**\n   * GET /api/Marques - Obtenir toutes les marques\n   */\n  getAll(): Observable<Marque[]> {\n    console.log('📦 Récupération des marques');\n    return this.http.get<Marque[]>(`${this.API_URL}/enriched`)\n      .pipe(\n        tap(response => console.log('✅ Marques récupérées:', response))\n      );\n  }\n\n  /**\n   * GET /api/Marques/{id} - Obtenir une marque par ID\n   */\n  getById(id: number): Observable<Marque> {\n    console.log('🔍 Récupération de la marque ID:', id);\n    return this.http.get<Marque>(`${this.API_URL}/${id}`)\n      .pipe(\n        tap(response => console.log('✅ Marque récupérée:', response))\n      );\n  }\n\n  /**\n   * POST /api/Marques - Créer une nouvelle marque\n   */\n  create(marque: MarqueCreate): Observable<Marque> {\n    console.log('➕ Création d\\'une nouvelle marque:', marque);\n    return this.http.post<Marque>(this.API_URL, marque)\n      .pipe(\n        tap(response => console.log('✅ Marque créée:', response))\n      );\n  }\n\n  /**\n   * PUT /api/Marques/{id} - Mettre à jour une marque\n   */\n  update(id: number, marque: MarqueUpdate): Observable<Marque> {\n    console.log('✏️ Mise à jour de la marque ID:', id, marque);\n    return this.http.put<Marque>(`${this.API_URL}/${id}`, marque)\n      .pipe(\n        tap(response => console.log('✅ Marque mise à jour:', response))\n      );\n  }\n\n  /**\n   * DELETE /api/Marques/{id} - Supprimer une marque\n   */\n  delete(id: number): Observable<void> {\n    console.log('🗑️ Suppression de la marque ID:', id);\n    return this.http.delete<void>(`${this.API_URL}/${id}`)\n      .pipe(\n        tap(() => console.log('✅ Marque supprimée:', id))\n      );\n  }\n\n  /**\n   * GET /api/Marques/dropdown - Obtenir les marques pour dropdown\n   */\n  getDropdown(): Observable<MarqueDropdown[]> {\n    console.log('📋 Récupération des marques pour dropdown');\n    return this.http.get<MarqueDropdown[]>(`${this.API_URL}/dropdown`)\n      .pipe(\n        tap(response => console.log('✅ Dropdown marques récupéré:', response))\n      );\n  }\n}\n", "import { Injectable } from '@angular/core';\nimport { HttpClient } from '@angular/common/http';\nimport { Observable } from 'rxjs';\nimport { tap } from 'rxjs/operators';\nimport {\n  Forme,\n  FormeCreate,\n  FormeUpdate,\n  FormeDropdown\n} from '../models';\nimport { environment } from '../../environments/environment';\n\n@Injectable({\n  providedIn: 'root'\n})\nexport class FormeService {\n  private readonly API_URL = `${environment.apiUrl || 'https://localhost:7264/api'}/Formes`;\n\n  constructor(private http: HttpClient) {}\n\n  /**\n   * GET /api/Formes - Obtenir toutes les formes\n   */\n  getAll(): Observable<Forme[]> {\n    console.log('📦 Récupération des formes');\n    return this.http.get<Forme[]>(`${this.API_URL}/enriched`)\n      .pipe(\n        tap(response => console.log('✅ Formes récupérées:', response))\n      );\n  }\n\n  /**\n   * GET /api/Formes/{id} - Obtenir une forme par ID\n   */\n  getById(id: number): Observable<Forme> {\n    console.log('🔍 Récupération de la forme ID:', id);\n    return this.http.get<Forme>(`${this.API_URL}/${id}`)\n      .pipe(\n        tap(response => console.log('✅ Forme récupérée:', response))\n      );\n  }\n\n  /**\n   * POST /api/Formes - Créer une nouvelle forme\n   */\n  create(forme: FormeCreate): Observable<Forme> {\n    console.log('➕ Création d\\'une nouvelle forme:', forme);\n    return this.http.post<Forme>(this.API_URL, forme)\n      .pipe(\n        tap(response => console.log('✅ Forme créée:', response))\n      );\n  }\n\n  /**\n   * PUT /api/Formes/{id} - Mettre à jour une forme\n   */\n  update(id: number, forme: FormeUpdate): Observable<Forme> {\n    console.log('✏️ Mise à jour de la forme ID:', id, forme);\n    return this.http.put<Forme>(`${this.API_URL}/${id}`, forme)\n      .pipe(\n        tap(response => console.log('✅ Forme mise à jour:', response))\n      );\n  }\n\n  /**\n   * DELETE /api/Formes/{id} - Supprimer une forme\n   */\n  delete(id: number): Observable<void> {\n    console.log('🗑️ Suppression de la forme ID:', id);\n    return this.http.delete<void>(`${this.API_URL}/${id}`)\n      .pipe(\n        tap(() => console.log('✅ Forme supprimée:', id))\n      );\n  }\n\n  /**\n   * GET /api/Formes/by-categorie/{categorieId} - Obtenir les formes d'une catégorie\n   */\n  getByCategorie(categorieId: number): Observable<Forme[]> {\n    console.log('📂 Récupération des formes pour la catégorie:', categorieId);\n    return this.http.get<Forme[]>(`${this.API_URL}/by-categorie/${categorieId}`)\n      .pipe(\n        tap(response => console.log('✅ Formes par catégorie récupérées:', response))\n      );\n  }\n\n  /**\n   * GET /api/Formes/dropdown - Obtenir les formes pour dropdown\n   */\n  getDropdown(): Observable<FormeDropdown[]> {\n    console.log('📋 Récupération des formes pour dropdown');\n    return this.http.get<FormeDropdown[]>(`${this.API_URL}/dropdown`)\n      .pipe(\n        tap(response => console.log('✅ Dropdown formes récupéré:', response))\n      );\n  }\n}\n", "import { Injectable } from '@angular/core';\nimport { HttpClient } from '@angular/common/http';\nimport { Observable } from 'rxjs';\nimport { tap } from 'rxjs/operators';\nimport {\n  TauxTVA,\n  TauxTVACreate,\n  TauxTVAUpdate,\n  TauxTVADropdown,\n  CalculTTCRequest,\n  CalculHTRequest,\n  CalculResponse\n} from '../models';\nimport { environment } from '../../environments/environment';\n\n@Injectable({\n  providedIn: 'root'\n})\nexport class TauxTVAService {\n  private readonly API_URL = `${environment.apiUrl}/tva`;\n\n  constructor(private http: HttpClient) {\n    console.log('🔧 TauxTVAService initialisé avec URL:', this.API_URL);\n  }\n\n  /**\n   * GET /api/tva - Obtenir tous les taux TVA\n   */\n  getAll(): Observable<TauxTVA[]> {\n    console.log('📦 Récupération des taux TVA');\n    return this.http.get<TauxTVA[]>(this.API_URL)\n      .pipe(\n        tap(response => console.log('✅ Taux TVA récupérés:', response))\n      );\n  }\n\n  /**\n   * GET /api/tva/{id} - Obtenir un taux TVA par ID\n   */\n  getById(id: number): Observable<TauxTVA> {\n    console.log('🔍 Récupération du taux TVA ID:', id);\n    return this.http.get<TauxTVA>(`${this.API_URL}/${id}`)\n      .pipe(\n        tap(response => console.log('✅ Taux TVA récupéré:', response))\n      );\n  }\n\n  /**\n   * POST /api/tva - Créer un nouveau taux TVA\n   */\n  create(tauxTVA: TauxTVACreate): Observable<TauxTVA> {\n    console.log('➕ Création d\\'un nouveau taux TVA:', tauxTVA);\n    return this.http.post<TauxTVA>(this.API_URL, tauxTVA)\n      .pipe(\n        tap(response => console.log('✅ Taux TVA créé:', response))\n      );\n  }\n\n  /**\n   * PUT /api/tva/{id} - Mettre à jour un taux TVA\n   */\n  update(id: number, tauxTVA: TauxTVAUpdate): Observable<TauxTVA> {\n    console.log('✏️ Mise à jour du taux TVA ID:', id, tauxTVA);\n    return this.http.put<TauxTVA>(`${this.API_URL}/${id}`, tauxTVA)\n      .pipe(\n        tap(response => console.log('✅ Taux TVA mis à jour:', response))\n      );\n  }\n\n  /**\n   * DELETE /api/tva/{id} - Supprimer un taux TVA\n   */\n  delete(id: number): Observable<void> {\n    console.log('🗑️ Suppression du taux TVA ID:', id);\n    return this.http.delete<void>(`${this.API_URL}/${id}`)\n      .pipe(\n        tap(() => console.log('✅ Taux TVA supprimé:', id))\n      );\n  }\n\n  /**\n   * GET /api/tva/actuel - Obtenir le taux TVA actuel\n   */\n  getActuel(): Observable<TauxTVA> {\n    console.log('📅 Récupération du taux TVA actuel');\n    return this.http.get<TauxTVA>(`${this.API_URL}/actuel`)\n      .pipe(\n        tap(response => console.log('✅ Taux TVA actuel récupéré:', response))\n      );\n  }\n\n  /**\n   * GET /api/tva/dropdown - Obtenir les taux TVA pour dropdown\n   */\n  getDropdown(): Observable<TauxTVADropdown[]> {\n    console.log('📋 Récupération des taux TVA pour dropdown');\n    return this.http.get<TauxTVADropdown[]>(`${this.API_URL}/dropdown`)\n      .pipe(\n        tap(response => console.log('✅ Dropdown taux TVA récupéré:', response))\n      );\n  }\n\n  /**\n   * GET /api/tva/by-categorie/{categorieId} - Obtenir les taux TVA d'une catégorie\n   */\n  getByCategorie(categorieId: number): Observable<TauxTVA[]> {\n    console.log('📂 Récupération des taux TVA pour la catégorie:', categorieId);\n    return this.http.get<TauxTVA[]>(`${this.API_URL}/by-categorie/${categorieId}`)\n      .pipe(\n        tap(response => console.log('✅ Taux TVA par catégorie récupérés:', response))\n      );\n  }\n\n  /**\n   * GET /api/tva/dropdown/{categorieId} - Obtenir les taux TVA pour dropdown par catégorie\n   */\n  getDropdownByCategorie(categorieId: number): Observable<TauxTVADropdown[]> {\n    console.log('📋 Récupération des taux TVA dropdown pour la catégorie:', categorieId);\n    return this.http.get<TauxTVADropdown[]>(`${this.API_URL}/dropdown/${categorieId}`)\n      .pipe(\n        tap(response => console.log('✅ Dropdown taux TVA par catégorie récupéré:', response))\n      );\n  }\n\n  /**\n   * POST /api/tva/calculer-ttc - Calculer le montant TTC\n   */\n  calculerTTC(request: CalculTTCRequest): Observable<CalculResponse> {\n    console.log('🧮 Calcul TTC:', request);\n    return this.http.post<CalculResponse>(`${this.API_URL}/calculer-ttc`, request)\n      .pipe(\n        tap(response => console.log('✅ Calcul TTC effectué:', response))\n      );\n  }\n\n  /**\n   * POST /api/tva/calculer-ht - Calculer le montant HT\n   */\n  calculerHT(request: CalculHTRequest): Observable<CalculResponse> {\n    console.log('🧮 Calcul HT:', request);\n    return this.http.post<CalculResponse>(`${this.API_URL}/calculer-ht`, request)\n      .pipe(\n        tap(response => console.log('✅ Calcul HT effectué:', response))\n      );\n  }\n}\n"], "mappings": ";;;;;;;;;;;;;;;AAeM,IAAO,gBAAP,MAAO,eAAa;EAGJ;EAFH,UAAU,GAAG,YAAY,UAAU,4BAA4B;EAEhF,YAAoB,MAAgB;AAAhB,SAAA,OAAA;EAAmB;;;;EAKvC,SAAM;AACJ,YAAQ,IAAI,0CAA6B;AACzC,WAAO,KAAK,KAAK,IAAc,GAAG,KAAK,OAAO,WAAW,EACtD,KACC,IAAI,cAAY,QAAQ,IAAI,uCAAyB,QAAQ,CAAC,CAAC;EAErE;;;;EAKA,QAAQ,IAAU;AAChB,YAAQ,IAAI,iDAAoC,EAAE;AAClD,WAAO,KAAK,KAAK,IAAY,GAAG,KAAK,OAAO,IAAI,EAAE,EAAE,EACjD,KACC,IAAI,cAAY,QAAQ,IAAI,qCAAuB,QAAQ,CAAC,CAAC;EAEnE;;;;EAKA,OAAO,QAAoB;AACzB,YAAQ,IAAI,6CAAsC,MAAM;AACxD,WAAO,KAAK,KAAK,KAAa,KAAK,SAAS,MAAM,EAC/C,KACC,IAAI,cAAY,QAAQ,IAAI,8BAAmB,QAAQ,CAAC,CAAC;EAE/D;;;;EAKA,OAAO,IAAY,QAAoB;AACrC,YAAQ,IAAI,gDAAmC,IAAI,MAAM;AACzD,WAAO,KAAK,KAAK,IAAY,GAAG,KAAK,OAAO,IAAI,EAAE,IAAI,MAAM,EACzD,KACC,IAAI,cAAY,QAAQ,IAAI,iCAAyB,QAAQ,CAAC,CAAC;EAErE;;;;EAKA,OAAO,IAAU;AACf,YAAQ,IAAI,gDAAoC,EAAE;AAClD,WAAO,KAAK,KAAK,OAAa,GAAG,KAAK,OAAO,IAAI,EAAE,EAAE,EAClD,KACC,IAAI,MAAM,QAAQ,IAAI,+BAAuB,EAAE,CAAC,CAAC;EAEvD;;;;EAKA,cAAW;AACT,YAAQ,IAAI,wDAA2C;AACvD,WAAO,KAAK,KAAK,IAAsB,GAAG,KAAK,OAAO,WAAW,EAC9D,KACC,IAAI,cAAY,QAAQ,IAAI,8CAAgC,QAAQ,CAAC,CAAC;EAE5E;;qCArEW,gBAAa,mBAAA,UAAA,CAAA;EAAA;4EAAb,gBAAa,SAAb,eAAa,WAAA,YAFZ,OAAM,CAAA;;;sEAEP,eAAa,CAAA;UAHzB;WAAW;MACV,YAAY;KACb;;;;;ACCK,IAAO,eAAP,MAAO,cAAY;EAGH;EAFH,UAAU,GAAG,YAAY,UAAU,4BAA4B;EAEhF,YAAoB,MAAgB;AAAhB,SAAA,OAAA;EAAmB;;;;EAKvC,SAAM;AACJ,YAAQ,IAAI,yCAA4B;AACxC,WAAO,KAAK,KAAK,IAAa,GAAG,KAAK,OAAO,WAAW,EACrD,KACC,IAAI,cAAY,QAAQ,IAAI,sCAAwB,QAAQ,CAAC,CAAC;EAEpE;;;;EAKA,QAAQ,IAAU;AAChB,YAAQ,IAAI,gDAAmC,EAAE;AACjD,WAAO,KAAK,KAAK,IAAW,GAAG,KAAK,OAAO,IAAI,EAAE,EAAE,EAChD,KACC,IAAI,cAAY,QAAQ,IAAI,oCAAsB,QAAQ,CAAC,CAAC;EAElE;;;;EAKA,OAAO,OAAkB;AACvB,YAAQ,IAAI,4CAAqC,KAAK;AACtD,WAAO,KAAK,KAAK,KAAY,KAAK,SAAS,KAAK,EAC7C,KACC,IAAI,cAAY,QAAQ,IAAI,6BAAkB,QAAQ,CAAC,CAAC;EAE9D;;;;EAKA,OAAO,IAAY,OAAkB;AACnC,YAAQ,IAAI,+CAAkC,IAAI,KAAK;AACvD,WAAO,KAAK,KAAK,IAAW,GAAG,KAAK,OAAO,IAAI,EAAE,IAAI,KAAK,EACvD,KACC,IAAI,cAAY,QAAQ,IAAI,gCAAwB,QAAQ,CAAC,CAAC;EAEpE;;;;EAKA,OAAO,IAAU;AACf,YAAQ,IAAI,+CAAmC,EAAE;AACjD,WAAO,KAAK,KAAK,OAAa,GAAG,KAAK,OAAO,IAAI,EAAE,EAAE,EAClD,KACC,IAAI,MAAM,QAAQ,IAAI,8BAAsB,EAAE,CAAC,CAAC;EAEtD;;;;EAKA,eAAe,aAAmB;AAChC,YAAQ,IAAI,iEAAiD,WAAW;AACxE,WAAO,KAAK,KAAK,IAAa,GAAG,KAAK,OAAO,iBAAiB,WAAW,EAAE,EACxE,KACC,IAAI,cAAY,QAAQ,IAAI,uDAAsC,QAAQ,CAAC,CAAC;EAElF;;;;EAKA,cAAW;AACT,YAAQ,IAAI,uDAA0C;AACtD,WAAO,KAAK,KAAK,IAAqB,GAAG,KAAK,OAAO,WAAW,EAC7D,KACC,IAAI,cAAY,QAAQ,IAAI,6CAA+B,QAAQ,CAAC,CAAC;EAE3E;;qCAhFW,eAAY,mBAAA,UAAA,CAAA;EAAA;4EAAZ,eAAY,SAAZ,cAAY,WAAA,YAFX,OAAM,CAAA;;;sEAEP,cAAY,CAAA;UAHxB;WAAW;MACV,YAAY;KACb;;;;;ACIK,IAAO,iBAAP,MAAO,gBAAc;EAGL;EAFH,UAAU,GAAG,YAAY,MAAM;EAEhD,YAAoB,MAAgB;AAAhB,SAAA,OAAA;AAClB,YAAQ,IAAI,oDAA0C,KAAK,OAAO;EACpE;;;;EAKA,SAAM;AACJ,YAAQ,IAAI,2CAA8B;AAC1C,WAAO,KAAK,KAAK,IAAe,KAAK,OAAO,EACzC,KACC,IAAI,cAAY,QAAQ,IAAI,uCAAyB,QAAQ,CAAC,CAAC;EAErE;;;;EAKA,QAAQ,IAAU;AAChB,YAAQ,IAAI,gDAAmC,EAAE;AACjD,WAAO,KAAK,KAAK,IAAa,GAAG,KAAK,OAAO,IAAI,EAAE,EAAE,EAClD,KACC,IAAI,cAAY,QAAQ,IAAI,sCAAwB,QAAQ,CAAC,CAAC;EAEpE;;;;EAKA,OAAO,SAAsB;AAC3B,YAAQ,IAAI,6CAAsC,OAAO;AACzD,WAAO,KAAK,KAAK,KAAc,KAAK,SAAS,OAAO,EACjD,KACC,IAAI,cAAY,QAAQ,IAAI,+BAAoB,QAAQ,CAAC,CAAC;EAEhE;;;;EAKA,OAAO,IAAY,SAAsB;AACvC,YAAQ,IAAI,+CAAkC,IAAI,OAAO;AACzD,WAAO,KAAK,KAAK,IAAa,GAAG,KAAK,OAAO,IAAI,EAAE,IAAI,OAAO,EAC3D,KACC,IAAI,cAAY,QAAQ,IAAI,kCAA0B,QAAQ,CAAC,CAAC;EAEtE;;;;EAKA,OAAO,IAAU;AACf,YAAQ,IAAI,+CAAmC,EAAE;AACjD,WAAO,KAAK,KAAK,OAAa,GAAG,KAAK,OAAO,IAAI,EAAE,EAAE,EAClD,KACC,IAAI,MAAM,QAAQ,IAAI,gCAAwB,EAAE,CAAC,CAAC;EAExD;;;;EAKA,YAAS;AACP,YAAQ,IAAI,iDAAoC;AAChD,WAAO,KAAK,KAAK,IAAa,GAAG,KAAK,OAAO,SAAS,EACnD,KACC,IAAI,cAAY,QAAQ,IAAI,6CAA+B,QAAQ,CAAC,CAAC;EAE3E;;;;EAKA,cAAW;AACT,YAAQ,IAAI,yDAA4C;AACxD,WAAO,KAAK,KAAK,IAAuB,GAAG,KAAK,OAAO,WAAW,EAC/D,KACC,IAAI,cAAY,QAAQ,IAAI,+CAAiC,QAAQ,CAAC,CAAC;EAE7E;;;;EAKA,eAAe,aAAmB;AAChC,YAAQ,IAAI,mEAAmD,WAAW;AAC1E,WAAO,KAAK,KAAK,IAAe,GAAG,KAAK,OAAO,iBAAiB,WAAW,EAAE,EAC1E,KACC,IAAI,cAAY,QAAQ,IAAI,wDAAuC,QAAQ,CAAC,CAAC;EAEnF;;;;EAKA,uBAAuB,aAAmB;AACxC,YAAQ,IAAI,4EAA4D,WAAW;AACnF,WAAO,KAAK,KAAK,IAAuB,GAAG,KAAK,OAAO,aAAa,WAAW,EAAE,EAC9E,KACC,IAAI,cAAY,QAAQ,IAAI,gEAA+C,QAAQ,CAAC,CAAC;EAE3F;;;;EAKA,YAAY,SAAyB;AACnC,YAAQ,IAAI,yBAAkB,OAAO;AACrC,WAAO,KAAK,KAAK,KAAqB,GAAG,KAAK,OAAO,iBAAiB,OAAO,EAC1E,KACC,IAAI,cAAY,QAAQ,IAAI,kCAA0B,QAAQ,CAAC,CAAC;EAEtE;;;;EAKA,WAAW,SAAwB;AACjC,YAAQ,IAAI,wBAAiB,OAAO;AACpC,WAAO,KAAK,KAAK,KAAqB,GAAG,KAAK,OAAO,gBAAgB,OAAO,EACzE,KACC,IAAI,cAAY,QAAQ,IAAI,iCAAyB,QAAQ,CAAC,CAAC;EAErE;;qCA9HW,iBAAc,mBAAA,UAAA,CAAA;EAAA;4EAAd,iBAAc,SAAd,gBAAc,WAAA,YAFb,OAAM,CAAA;;;sEAEP,gBAAc,CAAA;UAH1B;WAAW;MACV,YAAY;KACb;;;", "names": []}
import { ApplicationConfig, provideZoneChangeDetection, inject } from '@angular/core';
import { provideRouter, withEnabledBlockingInitialNavigation, withInMemoryScrolling, withRouterConfig } from '@angular/router';
import { provideHttpClient, withInterceptors, withF<PERSON>ch, HTTP_INTERCEPTORS, HttpInterceptorFn } from '@angular/common/http';
import { provideAnimationsAsync } from '@angular/platform-browser/animations/async';

import { routes } from './app.routes';
import { HttpInterceptorService } from './services';
import { HttpErrorInterceptor } from './interceptors/http-error.interceptor';
import { CorsInterceptor } from './interceptors/cors.interceptor';
import { AuthService } from './services/auth.service';

// Intercepteur fonctionnel pour l'authentification (Angular 19)
const authInterceptor: HttpInterceptorFn = (req, next) => {
  const authService = inject(AuthService);
  const token = authService.getToken();

  console.log('🔍 Intercepteur fonctionnel - Requête:', {
    url: req.url,
    method: req.method,
    hasToken: !!token,
    tokenPreview: token ? token.substring(0, 20) + '...' : 'Aucun'
  });

  if (token) {
    const authReq = req.clone({
      headers: req.headers.set('Authorization', `Bearer ${token}`)
    });
    console.log('✅ Token ajouté à la requête');
    return next(authReq);
  }

  console.warn('⚠️ Aucun token disponible');
  return next(req);
};

export const appConfig: ApplicationConfig = {
  providers: [
    // Angular 19: Nouvelle détection de changement optimisée
    provideZoneChangeDetection({
      eventCoalescing: true,
      runCoalescing: true
    }),

    // Angular 19: Router amélioré avec nouvelles options
    provideRouter(
      routes,
      withEnabledBlockingInitialNavigation(),
      withInMemoryScrolling({
        scrollPositionRestoration: 'top',
        anchorScrolling: 'enabled'
      }),
      withRouterConfig({
        onSameUrlNavigation: 'reload'
      })
    ),

    // Angular 19: HTTP Client avec fetch API et intercepteur fonctionnel
    provideHttpClient(
      withFetch(), // Nouvelle API fetch native
      withInterceptors([authInterceptor]) // Intercepteur fonctionnel pour l'authentification
    ),

    // Angular 19: Animations asynchrones pour de meilleures performances
    provideAnimationsAsync(),

    // Les intercepteurs legacy sont remplacés par l'intercepteur fonctionnel authInterceptor
  ]
};

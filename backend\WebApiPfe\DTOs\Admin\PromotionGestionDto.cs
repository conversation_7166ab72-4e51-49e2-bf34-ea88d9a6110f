﻿using System.ComponentModel.DataAnnotations;
using WebApiPfe.Models.Entity;
using WebApiPfe.Models.Enum;
namespace WebApiPfe.DTOs.Admin
{
    public class PromotionGestionDto
    {
        public int Id { get; set; }

        [Required]
        [StringLength(100)]
        public string Code { get; set; } = string.Empty;

        [Required]
        [StringLength(200)]
        public string Nom { get; set; } = string.Empty;

        [StringLength(1000)]
        public string? Description { get; set; }

        public TypePromotionGestion Type { get; set; }

        [Range(0, 100)]
        public decimal Valeur { get; set; }

        public DateTime DateDebut { get; set; }

        public DateTime DateFin { get; set; }

        public bool EstActive { get; set; }

        public int? UtilisationsMax { get; set; }

        public int UtilisationsActuelles { get; set; }

        public decimal? MontantMinimum { get; set; }

        public DateTime DateCreation { get; set; }

        // Informations fournisseur
        public int? FournisseurId { get; set; }
        public string? FournisseurNom { get; set; }
        public string? FournisseurRaisonSociale { get; set; }

        // Informations produit (pour promotions spécifiques)
        public int? ProduitId { get; set; }
        public string? ProduitNom { get; set; }

        // Statistiques
        public decimal MontantTotalEconomise { get; set; }
        public int NombreCommandesImpactees { get; set; }
    }

    public class PromotionCreateDto
    {
        [Required]
        [StringLength(100)]
        public string Code { get; set; } = string.Empty;

        [Required]
        [StringLength(200)]
        public string Nom { get; set; } = string.Empty;

        [StringLength(1000)]
        public string? Description { get; set; }

        [Required]
        public TypePromotionGestion Type { get; set; }

        [Required]
        [Range(0, 100)]
        public decimal Valeur { get; set; }

        [Required]
        public DateTime DateDebut { get; set; }

        [Required]
        public DateTime DateFin { get; set; }

        public int? UtilisationsMax { get; set; }

        public decimal? MontantMinimum { get; set; }

        public int? ProduitId { get; set; }

        // Pour les promotions fournisseur
        public int? FournisseurId { get; set; }
    }

    public class PromotionUpdateDto
    {
        [Required]
        [StringLength(200)]
        public string Nom { get; set; } = string.Empty;

        [StringLength(1000)]
        public string? Description { get; set; }

        [Required]
        [Range(0, 100)]
        public decimal Valeur { get; set; }

        [Required]
        public DateTime DateDebut { get; set; }

        [Required]
        public DateTime DateFin { get; set; }

        public bool EstActive { get; set; }

        public int? UtilisationsMax { get; set; }

        public decimal? MontantMinimum { get; set; }
    }

    public class PromotionFilterDto
    {
        public TypePromotionGestion? Type { get; set; }
        public bool? EstActive { get; set; }
        public int? FournisseurId { get; set; }
        public DateTime? DateDebut { get; set; }
        public DateTime? DateFin { get; set; }
        public string? Recherche { get; set; }
        public int Page { get; set; } = 1;
        public int PageSize { get; set; } = 10;
        public string SortBy { get; set; } = "DateCreation";
        public bool SortDesc { get; set; } = true;
    }

    public class PromotionStatsDto
    {
        public int TotalPromotions { get; set; }
        public int PromotionsActives { get; set; }
        public int PromotionsExpirees { get; set; }
        public int PromotionsEnAttente { get; set; }

        public decimal MontantTotalEconomise { get; set; }
        public int TotalUtilisations { get; set; }
        public decimal TauxUtilisationMoyen { get; set; }

        public Dictionary<TypePromotionGestion, int> PromotionsParType { get; set; } = new Dictionary<TypePromotionGestion, int>();
        public List<PromotionGestionDto> PromotionsPopulaires { get; set; } = new List<PromotionGestionDto>();
        public List<PromotionGestionDto> PromotionsRecentes { get; set; } = new List<PromotionGestionDto>();
    }

    public class ValidatePromotionDto
    {
        [Required]
        [StringLength(100)]
        public string Code { get; set; } = string.Empty;

        public decimal MontantCommande { get; set; }

        public int? ProduitId { get; set; }

        public int? ClientId { get; set; }
    }

    public class PromotionValidationResultDto
    {
        public bool EstValide { get; set; }
        public string? MessageErreur { get; set; }
        public decimal MontantReduction { get; set; }
        public PromotionGestionDto? Promotion { get; set; }
    }
}

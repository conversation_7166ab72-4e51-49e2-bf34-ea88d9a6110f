import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { AvisModerationService, AvisModerationDto, AvisFilterDto, StatutAvis, AvisStatsDto } from '../../../services/avis-moderation.service';

@Component({
  selector: 'app-avis-fournisseur',
  standalone: true,
  imports: [CommonModule, FormsModule],
  templateUrl: './avis-fournisseur.component.html',
  styleUrls: ['./avis-fournisseur.component.scss']
})
export class AvisFournisseurComponent implements OnInit {
  avis: AvisModerationDto[] = [];
  stats: AvisStatsDto | null = null;
  loading = false;
  error: string | null = null;

  // Filtres
  filter: AvisFilterDto = {
    page: 1,
    pageSize: 10,
    sortBy: 'datePublication',
    sortDesc: true
  };

  // Énumérations pour le template
  StatutAvis = StatutAvis;

  // Réponse aux avis
  selectedAvis: AvisModerationDto | null = null;
  reponseForm = {
    reponse: ''
  };

  // Signalement d'avis
  showSignalementModal = false;
  signalementForm = {
    raisonSignalement: ''
  };
  selectedAvisForSignalement: AvisModerationDto | null = null;

  constructor(private avisModerationService: AvisModerationService) { }

  ngOnInit(): void {
    this.loadAvis();
    this.loadStats();
  }

  loadAvis(): void {
    this.loading = true;
    this.error = null;

    this.avisModerationService.getAvisFournisseur(this.filter).subscribe({
      next: (data) => {
        this.avis = data;
        this.loading = false;
      },
      error: (error) => {
        console.error('Erreur lors du chargement des avis:', error);
        this.error = 'Erreur lors du chargement des avis';
        this.loading = false;
      }
    });
  }

  loadStats(): void {
    this.avisModerationService.getAvisStatsFournisseur().subscribe({
      next: (stats) => {
        this.stats = stats;
      },
      error: (error) => {
        console.error('Erreur lors du chargement des statistiques:', error);
      }
    });
  }

  onFilterChange(): void {
    this.filter.page = 1; // Reset à la première page
    this.loadAvis();
  }

  onSortChange(sortBy: string): void {
    if (this.filter.sortBy === sortBy) {
      this.filter.sortDesc = !this.filter.sortDesc;
    } else {
      this.filter.sortBy = sortBy;
      this.filter.sortDesc = true;
    }
    this.loadAvis();
  }

  openReponseModal(avis: AvisModerationDto): void {
    this.selectedAvis = avis;
    this.reponseForm = {
      reponse: avis.commentaireModeration || ''
    };
  }

  closeReponseModal(): void {
    this.selectedAvis = null;
    this.reponseForm = {
      reponse: ''
    };
  }

  repondreAvis(avis?: AvisModerationDto): void {
    if (avis) {
      this.openReponseModal(avis);
      return;
    }

    if (!this.selectedAvis || !this.reponseForm.reponse.trim()) return;

    this.avisModerationService.repondreAvis(this.selectedAvis.id, this.reponseForm.reponse).subscribe({
      next: (updatedAvis) => {
        // Mettre à jour l'avis dans la liste
        const index = this.avis.findIndex(a => a.id === updatedAvis.id);
        if (index !== -1) {
          this.avis[index] = updatedAvis;
        }
        this.closeReponseModal();
        this.loadStats(); // Recharger les stats
      },
      error: (error) => {
        console.error('Erreur lors de la réponse:', error);
        this.error = 'Erreur lors de l\'envoi de la réponse';
      }
    });
  }

  getStatutLibelle(statut: StatutAvis): string {
    return this.avisModerationService.getStatutLibelle(statut);
  }

  getStatutColor(statut: StatutAvis): string {
    return this.avisModerationService.getStatutColor(statut);
  }

  getStatutIcon(statut: StatutAvis): string {
    return this.avisModerationService.getStatutIcon(statut);
  }

  getStars(note: number): string[] {
    const stars = [];
    for (let i = 1; i <= 5; i++) {
      stars.push(i <= note ? 'star-fill' : 'star');
    }
    return stars;
  }

  resetFilters(): void {
    this.filter = {
      page: 1,
      pageSize: 10,
      sortBy: 'datePublication',
      sortDesc: true
    };
    this.loadAvis();
  }

  exportAvis(): void {
    // TODO: Implémenter l'export des avis
    console.log('Export des avis à implémenter');
  }

  // Méthodes pour le signalement d'avis
  openSignalementModal(avis: AvisModerationDto): void {
    this.selectedAvisForSignalement = avis;
    this.signalementForm.raisonSignalement = '';
    this.showSignalementModal = true;
  }

  closeSignalementModal(): void {
    this.showSignalementModal = false;
    this.selectedAvisForSignalement = null;
    this.signalementForm.raisonSignalement = '';
  }

  signalerAvis(avis?: AvisModerationDto): void {
    if (avis) {
      this.openSignalementModal(avis);
      return;
    }

    if (!this.selectedAvisForSignalement || !this.signalementForm.raisonSignalement.trim()) {
      return;
    }

    this.loading = true;
    this.avisModerationService.signalerAvis(
      this.selectedAvisForSignalement.id,
      this.signalementForm.raisonSignalement.trim()
    ).subscribe({
      next: (avisUpdated) => {
        // Mettre à jour l'avis dans la liste
        const index = this.avis.findIndex(a => a.id === avisUpdated.id);
        if (index !== -1) {
          this.avis[index] = avisUpdated;
        }

        this.closeSignalementModal();
        this.loading = false;

        // Afficher un message de succès
        console.log('Avis signalé avec succès. Notification envoyée à l\'administrateur.');
      },
      error: (error) => {
        console.error('Erreur lors du signalement de l\'avis:', error);
        this.error = 'Erreur lors du signalement de l\'avis';
        this.loading = false;
      }
    });
  }

  // Pagination
  previousPage(): void {
    if (this.filter.page && this.filter.page > 1) {
      this.filter.page--;
      this.loadAvis();
    }
  }

  nextPage(): void {
    if (this.filter.page) {
      this.filter.page++;
      this.loadAvis();
    }
  }

  // Méthodes pour les statistiques
  getStatsArray(): { note: number, count: number, percentage: number }[] {
    if (!this.stats) return [];
    
    const total = this.stats.totalAvis;
    return [5, 4, 3, 2, 1].map(note => ({
      note,
      count: this.stats!.avisParNote[note] || 0,
      percentage: total > 0 ? ((this.stats!.avisParNote[note] || 0) / total) * 100 : 0
    }));
  }

  getProgressBarWidth(percentage: number): string {
    return `${Math.max(percentage, 2)}%`; // Minimum 2% pour la visibilité
  }

  // Nouvelles méthodes pour le nouveau design
  getStatutClass(statut: StatutAvis): string {
    switch (statut) {
      case StatutAvis.Publie: return 'success';
      case StatutAvis.Signale: return 'warning';
      case StatutAvis.CommentaireSupprime: return 'danger';
      default: return 'secondary';
    }
  }

  voirDetails(avis: AvisModerationDto): void {
    this.selectedAvis = avis;
  }
}

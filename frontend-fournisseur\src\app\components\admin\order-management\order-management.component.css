.order-management-container {
  padding: 2rem;
  max-width: 1400px;
  margin: 0 auto;
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
  min-height: 100vh;
}

/* Header */
.page-header {
  margin-bottom: 2rem;
  background: white;
  border-radius: 16px;
  padding: 2rem;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
  gap: 1rem;
}

.page-title {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  font-size: 2.25rem;
  font-weight: 700;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  margin: 0;
}

.header-actions {
  display: flex;
  gap: 1rem;
  flex-wrap: wrap;
}

/* Filtres */
.filters-section {
  background: white;
  border-radius: 16px;
  padding: 2rem;
  margin-bottom: 2rem;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  border: 1px solid rgba(255, 255, 255, 0.2);
  display: flex;
  gap: 1.5rem;
  flex-wrap: wrap;
  align-items: center;
  backdrop-filter: blur(10px);
}

.search-box {
  position: relative;
  flex: 1;
  min-width: 300px;
}

.search-box i {
  position: absolute;
  left: 1rem;
  top: 50%;
  transform: translateY(-50%);
  color: #64748b;
}

.search-input {
  width: 100%;
  padding: 0.75rem 1rem 0.75rem 2.5rem;
  border: 2px solid #e2e8f0;
  border-radius: 8px;
  font-size: 1rem;
  transition: border-color 0.2s;
}

.search-input:focus {
  outline: none;
  border-color: #3b82f6;
}

.filters {
  display: flex;
  gap: 1rem;
  flex-wrap: wrap;
}

.filter-select,
.filter-date {
  padding: 0.75rem 1rem;
  border: 2px solid #e2e8f0;
  border-radius: 8px;
  font-size: 1rem;
  background: white;
  min-width: 150px;
}

/* Table */
.table-container {
  background: white;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  margin-bottom: 2rem;
}

.orders-table {
  width: 100%;
  border-collapse: collapse;
  table-layout: fixed;
}

.orders-table th {
  background: #f8fafc;
  padding: 1rem;
  text-align: left;
  font-weight: 600;
  color: #374151;
  border-bottom: 1px solid #e5e7eb;
}

/* Largeurs spécifiques des colonnes */
.orders-table th:nth-child(1) { width: 15%; } /* Numéro */
.orders-table th:nth-child(2) { width: 25%; } /* Client */
.orders-table th:nth-child(3) { width: 20%; } /* Fournisseur(s) */
.orders-table th:nth-child(4) { width: 15%; } /* Montant */
.orders-table th:nth-child(5) { width: 12%; } /* Statut */
.orders-table th:nth-child(6) { width: 13%; } /* Date */
.orders-table th:nth-child(7) { width: 10%; } /* Actions */

.order-row {
  border-bottom: 1px solid #f3f4f6;
  transition: background-color 0.2s;
}

.order-row:hover {
  background: #f9fafb;
}

.orders-table td {
  padding: 1rem;
  vertical-align: middle;
  word-wrap: break-word;
  overflow: hidden;
}

/* Cellules spécifiques */
.order-number {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

.number-main {
  font-weight: 600;
  color: #1a202c;
  font-family: monospace;
}

.order-id {
  font-size: 0.875rem;
  color: #64748b;
}

.client-info {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

.client-name {
  font-weight: 600;
  color: #1a202c;
  font-size: 0.95rem;
}

.client-email {
  font-size: 0.8rem;
  color: #4f46e5;
  font-family: monospace;
}

.supplier-name {
  color: #059669;
  font-weight: 500;
  font-size: 0.9rem;
  line-height: 1.4;
  max-height: 2.8rem;
  overflow: hidden;
  text-overflow: ellipsis;
}



.amount {
  font-weight: 700;
  color: #059669;
  font-size: 1.125rem;
}

.date-cell {
  color: #64748b;
  font-size: 0.875rem;
}

/* Status badges */
.status-badge {
  padding: 0.5rem 1rem;
  border-radius: 20px;
  font-size: 0.8rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  position: relative;
  overflow: hidden;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.status-pending {
  background: linear-gradient(135deg, #fef3c7 0%, #fde68a 100%);
  color: #92400e;
  box-shadow: 0 2px 8px rgba(251, 191, 36, 0.3);
}

.status-confirmed {
  background: linear-gradient(135deg, #dbeafe 0%, #bfdbfe 100%);
  color: #1e40af;
  box-shadow: 0 2px 8px rgba(59, 130, 246, 0.3);
}

.status-preparing {
  background: linear-gradient(135deg, #e0e7ff 0%, #c7d2fe 100%);
  color: #4338ca;
  box-shadow: 0 2px 8px rgba(99, 102, 241, 0.3);
}

.status-shipped {
  background: linear-gradient(135deg, #f3e8ff 0%, #e9d5ff 100%);
  color: #7c2d12;
  box-shadow: 0 2px 8px rgba(147, 51, 234, 0.3);
}

.status-delivered {
  background: linear-gradient(135deg, #d1fae5 0%, #a7f3d0 100%);
  color: #065f46;
  box-shadow: 0 2px 8px rgba(5, 150, 105, 0.3);
}

.status-cancelled {
  background: linear-gradient(135deg, #fee2e2 0%, #fecaca 100%);
  color: #991b1b;
  box-shadow: 0 2px 8px rgba(220, 38, 38, 0.3);
}

.status-unknown {
  background: linear-gradient(135deg, #f3f4f6 0%, #e5e7eb 100%);
  color: #4b5563;
  box-shadow: 0 2px 8px rgba(107, 114, 128, 0.3);
}

/* Actions */
.actions-cell {
  width: 100px;
}

.action-buttons {
  display: flex;
  gap: 0.5rem;
}

.btn-action {
  width: 32px;
  height: 32px;
  border: none;
  border-radius: 6px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.2s;
}

.btn-view {
  background: #dbeafe;
  color: #2563eb;
}

.btn-view:hover {
  background: #bfdbfe;
}

.btn-cancel {
  background: #fee2e2;
  color: #dc2626;
}

.btn-cancel:hover {
  background: #fecaca;
}

/* Buttons */
.btn {
  padding: 0.875rem 1.75rem;
  border: none;
  border-radius: 12px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  position: relative;
  overflow: hidden;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  font-size: 0.875rem;
}

.btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s;
}

.btn:hover::before {
  left: 100%;
}

.btn-primary {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  box-shadow: 0 4px 15px rgba(102, 126, 234, 0.4);
}

.btn-primary:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(102, 126, 234, 0.6);
}

.btn-secondary {
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
  color: #4a5568;
  border: 1px solid rgba(226, 232, 240, 0.8);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.btn-secondary:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.15);
  background: linear-gradient(135deg, #e2e8f0 0%, #cbd5e0 100%);
}

.btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

/* Pagination */
.pagination-container {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1.5rem;
  background: white;
  border-radius: 16px;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  border: 1px solid rgba(255, 255, 255, 0.2);
  backdrop-filter: blur(10px);
  margin-bottom: 2rem;
}

.pagination-info {
  color: #64748b;
  font-size: 0.875rem;
}

.pagination {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.btn-page {
  width: 36px;
  height: 36px;
  border: 1px solid #e5e7eb;
  background: white;
  border-radius: 6px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.2s;
}

.btn-page:hover:not(:disabled) {
  background: #f3f4f6;
}

.btn-page:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.page-info {
  font-weight: 500;
  color: #374151;
}

/* Stats Summary */
.stats-summary {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1rem;
  margin-top: 2rem;
}

.stat-card {
  background: white;
  border-radius: 12px;
  padding: 1.5rem;
  text-align: center;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.stat-value {
  font-size: 1.5rem;
  font-weight: 700;
  color: #1a202c;
  margin-bottom: 0.5rem;
}

.stat-label {
  color: #64748b;
  font-size: 0.875rem;
}

/* Messages */
.error-message {
  background: #fef2f2;
  border: 1px solid #fecaca;
  color: #dc2626;
  padding: 1rem;
  border-radius: 8px;
  margin-bottom: 2rem;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.loading-container {
  text-align: center;
  padding: 3rem;
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 4px solid #e2e8f0;
  border-top: 4px solid #3b82f6;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin: 0 auto 1rem;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.no-data {
  text-align: center;
  padding: 3rem;
  color: #64748b;
}

.no-data i {
  font-size: 3rem;
  margin-bottom: 1rem;
  opacity: 0.5;
}

.no-data h3 {
  margin: 0 0 0.5rem 0;
  color: #374151;
}

/* Icons */
.icon-shopping-cart::before { content: "🛍️"; font-size: 1.2em; }
.icon-filter-x::before { content: "🗂️"; font-size: 1.1em; }
.icon-refresh::before { content: "🔄"; font-size: 1.1em; }
.icon-download::before { content: "💾"; font-size: 1.1em; }
.icon-search::before { content: "🔍"; font-size: 1.1em; }
.icon-alert::before { content: "⚠️"; font-size: 1.1em; }
.icon-eye::before { content: "👀"; font-size: 1.1em; }
.icon-x::before { content: "❌"; font-size: 1.1em; }
.icon-chevron-left::before { content: "⬅️"; font-size: 1.1em; }
.icon-chevron-right::before { content: "➡️"; font-size: 1.1em; }
.icon-chevron-down::before { content: "🔽"; font-size: 0.9em; }
.icon-layers::before { content: "📊"; font-size: 1.1em; }
.icon-truck::before { content: "🚛"; font-size: 1.1em; }
.icon-calendar::before { content: "📅"; font-size: 1.1em; }
.icon-package::before { content: "📦"; font-size: 1.1em; }

/* Responsive */
@media (max-width: 768px) {
  .order-management-container {
    padding: 1rem;
  }
  
  .header-content {
    flex-direction: column;
    align-items: stretch;
  }
  
  .filters-section {
    flex-direction: column;
  }
  
  .search-box {
    min-width: auto;
  }
  
  .filters {
    justify-content: stretch;
  }
  
  .filter-select,
  .filter-date {
    min-width: auto;
    flex: 1;
  }
  
  .orders-table {
    font-size: 0.875rem;
  }
  
  .orders-table th,
  .orders-table td {
    padding: 0.75rem 0.5rem;
  }
  
  .pagination-container {
    flex-direction: column;
    gap: 1rem;
  }
  
  .stats-summary {
    grid-template-columns: 1fr;
  }
}

/* Vue détaillée des commandes */
.detailed-orders-container {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.order-card {
  background: white;
  border-radius: 16px;
  border: 1px solid rgba(255, 255, 255, 0.2);
  overflow: hidden;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  backdrop-filter: blur(10px);
  transition: all 0.3s ease;
  position: relative;
}

.order-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15);
}

.order-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, #667eea 0%, #764ba2 100%);
}

.order-header {
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
  padding: 2rem;
  border-bottom: 1px solid rgba(229, 231, 235, 0.5);
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  gap: 1rem;
}

.order-title {
  font-size: 1.25rem;
  font-weight: bold;
  color: #1f2937;
  margin: 0 0 0.5rem 0;
}

.client-info {
  color: #4b5563;
  margin-bottom: 0.75rem;
}

.order-meta {
  display: flex;
  gap: 1rem;
  align-items: center;
  flex-wrap: wrap;
}

.order-date {
  color: #6b7280;
  font-size: 0.875rem;
}

.order-amount {
  font-weight: bold;
  color: #059669;
  font-size: 1.1rem;
}

.supplier-orders {
  padding: 1.5rem;
}

.supplier-orders-title {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 1.1rem;
  font-weight: 600;
  color: #374151;
  margin: 0 0 1rem 0;
}

.no-supplier-orders {
  text-align: center;
  color: #6b7280;
  font-style: italic;
  padding: 2rem;
}

.supplier-order-card {
  background: #f9fafb;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  padding: 1rem;
  margin-bottom: 1rem;
}

.supplier-order-card:last-child {
  margin-bottom: 0;
}

.supplier-order-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 1rem;
}

.supplier-info strong {
  color: #1f2937;
  font-size: 1rem;
}

.supplier-email {
  color: #6b7280;
  font-size: 0.875rem;
  display: block;
  margin-top: 0.25rem;
}

.supplier-order-meta {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  gap: 0.5rem;
}

.supplier-reference {
  font-family: monospace;
  background: #e5e7eb;
  padding: 0.25rem 0.5rem;
  border-radius: 4px;
  font-size: 0.875rem;
}

.supplier-amount {
  font-weight: bold;
  color: #059669;
}

.delivery-info {
  background: #eff6ff;
  border: 1px solid #dbeafe;
  border-radius: 6px;
  padding: 0.75rem;
  margin-bottom: 1rem;
  display: flex;
  gap: 1rem;
  flex-wrap: wrap;
}

.delivery-date,
.delivery-number {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  color: #1e40af;
  font-size: 0.875rem;
}

.order-lines {
  border-top: 1px solid #e5e7eb;
  padding-top: 1rem;
}

.order-line {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.5rem 0;
  border-bottom: 1px solid #f3f4f6;
}

.order-line:last-child {
  border-bottom: none;
}

.product-info {
  flex: 1;
}

.product-name {
  font-weight: 500;
  color: #1f2937;
  display: block;
}

.product-ref {
  color: #6b7280;
  font-size: 0.875rem;
  font-family: monospace;
}

.quantity-price {
  display: flex;
  align-items: center;
  gap: 1rem;
  font-size: 0.875rem;
}

.quantity {
  color: #6b7280;
}

.unit-price {
  color: #4b5563;
}

.line-total {
  font-weight: 600;
  color: #1f2937;
  min-width: 80px;
  text-align: right;
}

/* Responsive pour la vue détaillée */
@media (max-width: 768px) {
  .order-meta {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.5rem;
  }

  .supplier-order-header {
    flex-direction: column;
    gap: 1rem;
  }

  .supplier-order-meta {
    align-items: flex-start;
  }

  .delivery-info {
    flex-direction: column;
    gap: 0.5rem;
  }

  .order-line {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.5rem;
  }

  .quantity-price {
    justify-content: space-between;
    width: 100%;
  }
}

/* Styles pour les nouvelles fonctionnalités */
.order-actions {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.order-actions .btn {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  transition: all 0.2s ease;
}

.order-actions .btn.active {
  background-color: #3182ce;
  color: white;
}

.icon-chevron-down {
  transition: transform 0.2s ease;
}

.icon-chevron-down.rotated {
  transform: rotate(180deg);
}

/* Amélioration des boutons d'action */
.action-buttons {
  display: flex;
  gap: 0.5rem;
  align-items: center;
}

.btn-sm {
  padding: 0.375rem 0.75rem;
  font-size: 0.875rem;
  border-radius: 6px;
  border: 1px solid #e2e8f0;
  background: white;
  color: #4a5568;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  gap: 0.375rem;
}

.btn-sm:hover {
  background-color: #f7fafc;
  border-color: #cbd5e0;
}

.btn-sm.btn-primary {
  background-color: #3182ce;
  border-color: #3182ce;
  color: white;
}

.btn-sm.btn-primary:hover {
  background-color: #2c5aa0;
  border-color: #2c5aa0;
}

.btn-sm.btn-outline {
  background: transparent;
  border-color: #e2e8f0;
  color: #4a5568;
}

.btn-sm.btn-outline:hover {
  background-color: #f7fafc;
  border-color: #cbd5e0;
}

/* Statistiques simplifiées */
.stats-summary {
  display: flex;
  gap: 1rem;
  margin-bottom: 1rem;
}

.stat-card {
  background: white;
  border-radius: 8px;
  padding: 1rem;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  min-width: 120px;
  text-align: center;
}

.stat-value {
  font-size: 1.5rem;
  font-weight: 600;
  color: #2d3748;
  margin-bottom: 0.25rem;
}

.stat-label {
  font-size: 0.875rem;
  color: #718096;
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

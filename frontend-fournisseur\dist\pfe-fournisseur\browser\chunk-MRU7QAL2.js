import {
  FormeService,
  MarqueService,
  TauxTVAService
} from "./chunk-KTQKKI3U.js";
import {
  ProduitService
} from "./chunk-YIUF6N3Y.js";
import {
  ImageUrlService
} from "./chunk-CZWG52C5.js";
import {
  SousCategorieService
} from "./chunk-VGHANLZK.js";
import {
  CategorieService
} from "./chunk-LBLEENAN.js";
import {
  AuthService
} from "./chunk-ZSXXNL7U.js";
import {
  CheckboxControlValueAccessor,
  DefaultValueAccessor,
  FormsModule,
  MaxValidator,
  MinValidator,
  NgControlStatus,
  NgControlStatusGroup,
  NgForm,
  NgModel,
  NgSelectOption,
  NumberValueAccessor,
  RequiredValidator,
  SelectControlValueAccessor,
  ɵNgNoValidate,
  ɵNgSelectMultipleOption
} from "./chunk-HQBVYEOO.js";
import "./chunk-GFHHPDQ6.js";
import "./chunk-7JDDWGD3.js";
import {
  CommonModule,
  Component,
  DatePipe,
  DecimalPipe,
  NgClass,
  NgForOf,
  NgIf,
  computed,
  setClassMetadata,
  signal,
  ɵsetClassDebugInfo,
  ɵɵadvance,
  ɵɵclassMap,
  ɵɵdefineComponent,
  ɵɵdirectiveInject,
  ɵɵelement,
  ɵɵelementEnd,
  ɵɵelementStart,
  ɵɵgetCurrentView,
  ɵɵlistener,
  ɵɵnextContext,
  ɵɵpipe,
  ɵɵpipeBind2,
  ɵɵproperty,
  ɵɵpureFunction1,
  ɵɵreference,
  ɵɵresetView,
  ɵɵrestoreView,
  ɵɵsanitizeUrl,
  ɵɵtemplate,
  ɵɵtemplateRefExtractor,
  ɵɵtext,
  ɵɵtextInterpolate,
  ɵɵtextInterpolate1,
  ɵɵtextInterpolate2,
  ɵɵtwoWayBindingSet,
  ɵɵtwoWayListener,
  ɵɵtwoWayProperty
} from "./chunk-UBZQS7JS.js";

// src/app/components/products/products.component.ts
var _c0 = (a0) => ({ "stock-critique": a0 });
function ProductsComponent_div_32_Template(rf, ctx) {
  if (rf & 1) {
    const _r2 = \u0275\u0275getCurrentView();
    \u0275\u0275elementStart(0, "div", 20)(1, "h3");
    \u0275\u0275text(2, "\u274C Erreur");
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(3, "p");
    \u0275\u0275text(4);
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(5, "button", 6);
    \u0275\u0275listener("click", function ProductsComponent_div_32_Template_button_click_5_listener() {
      \u0275\u0275restoreView(_r2);
      const ctx_r2 = \u0275\u0275nextContext();
      return \u0275\u0275resetView(ctx_r2.loadData());
    });
    \u0275\u0275text(6, "\u{1F504} R\xE9essayer");
    \u0275\u0275elementEnd()();
  }
  if (rf & 2) {
    const ctx_r2 = \u0275\u0275nextContext();
    \u0275\u0275advance(4);
    \u0275\u0275textInterpolate(ctx_r2.error());
  }
}
function ProductsComponent_div_33_Template(rf, ctx) {
  if (rf & 1) {
    const _r4 = \u0275\u0275getCurrentView();
    \u0275\u0275elementStart(0, "div", 21)(1, "div", 22)(2, "h1");
    \u0275\u0275text(3, "\u{1F4E6} Gestion des Produits");
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(4, "p");
    \u0275\u0275text(5, "G\xE9rez votre catalogue de produits et suivez vos stocks");
    \u0275\u0275elementEnd()();
    \u0275\u0275elementStart(6, "div", 23)(7, "button", 6);
    \u0275\u0275listener("click", function ProductsComponent_div_33_Template_button_click_7_listener() {
      \u0275\u0275restoreView(_r4);
      const ctx_r2 = \u0275\u0275nextContext();
      return \u0275\u0275resetView(ctx_r2.openAddForm());
    });
    \u0275\u0275text(8, " \u2795 Nouveau Produit ");
    \u0275\u0275elementEnd()()();
  }
}
function ProductsComponent_div_34_div_27_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "div", 29)(1, "div", 25);
    \u0275\u0275text(2, "\u26A0\uFE0F");
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(3, "div", 26)(4, "div", 27);
    \u0275\u0275text(5);
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(6, "div", 10);
    \u0275\u0275text(7, "Stock faible");
    \u0275\u0275elementEnd()()();
  }
  if (rf & 2) {
    const ctx_r2 = \u0275\u0275nextContext(2);
    \u0275\u0275advance(5);
    \u0275\u0275textInterpolate(ctx_r2.getLowStockCount());
  }
}
function ProductsComponent_div_34_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "div", 24)(1, "div", 7)(2, "div", 8)(3, "div", 25);
    \u0275\u0275text(4, "\u{1F4E6}");
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(5, "div", 26)(6, "div", 27);
    \u0275\u0275text(7);
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(8, "div", 10);
    \u0275\u0275text(9, "Produits");
    \u0275\u0275elementEnd()()();
    \u0275\u0275elementStart(10, "div", 8)(11, "div", 25);
    \u0275\u0275text(12, "\u{1F4CA}");
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(13, "div", 26)(14, "div", 27);
    \u0275\u0275text(15);
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(16, "div", 10);
    \u0275\u0275text(17, "Unit\xE9s en stock");
    \u0275\u0275elementEnd()()();
    \u0275\u0275elementStart(18, "div", 8)(19, "div", 25);
    \u0275\u0275text(20, "\u{1F4B0}");
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(21, "div", 26)(22, "div", 27);
    \u0275\u0275text(23);
    \u0275\u0275pipe(24, "number");
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(25, "div", 10);
    \u0275\u0275text(26, "Valeur du stock");
    \u0275\u0275elementEnd()()();
    \u0275\u0275template(27, ProductsComponent_div_34_div_27_Template, 8, 1, "div", 28);
    \u0275\u0275elementEnd()();
  }
  if (rf & 2) {
    const ctx_r2 = \u0275\u0275nextContext();
    \u0275\u0275advance(7);
    \u0275\u0275textInterpolate(ctx_r2.products().length);
    \u0275\u0275advance(8);
    \u0275\u0275textInterpolate(ctx_r2.getTotalStock());
    \u0275\u0275advance(8);
    \u0275\u0275textInterpolate1("", \u0275\u0275pipeBind2(24, 4, ctx_r2.getTotalValue(), "1.0-0"), " DT");
    \u0275\u0275advance(4);
    \u0275\u0275property("ngIf", ctx_r2.getLowStockCount() > 0);
  }
}
function ProductsComponent_div_35_Template(rf, ctx) {
  if (rf & 1) {
    const _r5 = \u0275\u0275getCurrentView();
    \u0275\u0275elementStart(0, "div", 30)(1, "div", 31)(2, "div", 11)(3, "input", 32);
    \u0275\u0275twoWayListener("ngModelChange", function ProductsComponent_div_35_Template_input_ngModelChange_3_listener($event) {
      \u0275\u0275restoreView(_r5);
      const ctx_r2 = \u0275\u0275nextContext();
      \u0275\u0275twoWayBindingSet(ctx_r2.searchQuery, $event) || (ctx_r2.searchQuery = $event);
      return \u0275\u0275resetView($event);
    });
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(4, "span", 33);
    \u0275\u0275text(5, "\u{1F50D}");
    \u0275\u0275elementEnd()();
    \u0275\u0275elementStart(6, "div", 34)(7, "label", 35)(8, "input", 36);
    \u0275\u0275twoWayListener("ngModelChange", function ProductsComponent_div_35_Template_input_ngModelChange_8_listener($event) {
      \u0275\u0275restoreView(_r5);
      const ctx_r2 = \u0275\u0275nextContext();
      \u0275\u0275twoWayBindingSet(ctx_r2.filters.stockFaible, $event) || (ctx_r2.filters.stockFaible = $event);
      return \u0275\u0275resetView($event);
    });
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(9, "span");
    \u0275\u0275text(10, "Stock faible");
    \u0275\u0275elementEnd()();
    \u0275\u0275elementStart(11, "label", 35)(12, "input", 36);
    \u0275\u0275twoWayListener("ngModelChange", function ProductsComponent_div_35_Template_input_ngModelChange_12_listener($event) {
      \u0275\u0275restoreView(_r5);
      const ctx_r2 = \u0275\u0275nextContext();
      \u0275\u0275twoWayBindingSet(ctx_r2.filters.enPromotion, $event) || (ctx_r2.filters.enPromotion = $event);
      return \u0275\u0275resetView($event);
    });
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(13, "span");
    \u0275\u0275text(14, "En promotion");
    \u0275\u0275elementEnd()();
    \u0275\u0275elementStart(15, "label", 35)(16, "input", 36);
    \u0275\u0275twoWayListener("ngModelChange", function ProductsComponent_div_35_Template_input_ngModelChange_16_listener($event) {
      \u0275\u0275restoreView(_r5);
      const ctx_r2 = \u0275\u0275nextContext();
      \u0275\u0275twoWayBindingSet(ctx_r2.filters.misEnAvant, $event) || (ctx_r2.filters.misEnAvant = $event);
      return \u0275\u0275resetView($event);
    });
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(17, "span");
    \u0275\u0275text(18, "Mis en avant");
    \u0275\u0275elementEnd()()()()();
  }
  if (rf & 2) {
    const ctx_r2 = \u0275\u0275nextContext();
    \u0275\u0275advance(3);
    \u0275\u0275twoWayProperty("ngModel", ctx_r2.searchQuery);
    \u0275\u0275advance(5);
    \u0275\u0275twoWayProperty("ngModel", ctx_r2.filters.stockFaible);
    \u0275\u0275advance(4);
    \u0275\u0275twoWayProperty("ngModel", ctx_r2.filters.enPromotion);
    \u0275\u0275advance(4);
    \u0275\u0275twoWayProperty("ngModel", ctx_r2.filters.misEnAvant);
  }
}
function ProductsComponent_div_36_div_1_tr_21_span_7_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "span", 75);
    \u0275\u0275text(1);
    \u0275\u0275pipe(2, "date");
    \u0275\u0275elementEnd();
  }
  if (rf & 2) {
    const product_r7 = \u0275\u0275nextContext().$implicit;
    \u0275\u0275advance();
    \u0275\u0275textInterpolate1("Ajout\xE9 le ", \u0275\u0275pipeBind2(2, 1, product_r7.dateAjout, "dd/MM/yyyy"), "");
  }
}
function ProductsComponent_div_36_div_1_tr_21_span_8_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "span", 76);
    \u0275\u0275text(1, "\u{1F3F7}\uFE0F PROMO");
    \u0275\u0275elementEnd();
  }
}
function ProductsComponent_div_36_div_1_tr_21_div_9_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "div", 77);
    \u0275\u0275text(1);
    \u0275\u0275elementEnd();
  }
  if (rf & 2) {
    const product_r7 = \u0275\u0275nextContext().$implicit;
    \u0275\u0275advance();
    \u0275\u0275textInterpolate(product_r7.description);
  }
}
function ProductsComponent_div_36_div_1_tr_21_div_13_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "div", 78);
    \u0275\u0275text(1);
    \u0275\u0275elementEnd();
  }
  if (rf & 2) {
    const product_r7 = \u0275\u0275nextContext().$implicit;
    \u0275\u0275advance();
    \u0275\u0275textInterpolate1(" Fournisseur: ", product_r7.referenceFournisseur, " ");
  }
}
function ProductsComponent_div_36_div_1_tr_21_div_16_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "div", 79)(1, "span", 80);
    \u0275\u0275text(2, "Prix initial:");
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(3, "span", 81);
    \u0275\u0275text(4);
    \u0275\u0275elementEnd()();
  }
  if (rf & 2) {
    const product_r7 = \u0275\u0275nextContext().$implicit;
    const ctx_r2 = \u0275\u0275nextContext(3);
    \u0275\u0275advance(4);
    \u0275\u0275textInterpolate(ctx_r2.formatPrice(product_r7.prixVenteTTC));
  }
}
function ProductsComponent_div_36_div_1_tr_21_span_18_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "span", 80);
    \u0275\u0275text(1, "Prix final:");
    \u0275\u0275elementEnd();
  }
}
function ProductsComponent_div_36_div_1_tr_21_div_21_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "div", 82);
    \u0275\u0275text(1);
    \u0275\u0275elementEnd();
  }
  if (rf & 2) {
    const product_r7 = \u0275\u0275nextContext().$implicit;
    \u0275\u0275advance();
    \u0275\u0275textInterpolate1(" -", product_r7.pourcentageRemiseTotale, "% ");
  }
}
function ProductsComponent_div_36_div_1_tr_21_div_26_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "div", 83);
    \u0275\u0275text(1, " \u26A0\uFE0F Stock critique ");
    \u0275\u0275elementEnd();
  }
}
function ProductsComponent_div_36_div_1_tr_21_div_30_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "div", 84);
    \u0275\u0275text(1);
    \u0275\u0275elementEnd();
  }
  if (rf & 2) {
    const product_r7 = \u0275\u0275nextContext().$implicit;
    \u0275\u0275advance();
    \u0275\u0275textInterpolate(product_r7.marque == null ? null : product_r7.marque.name);
  }
}
function ProductsComponent_div_36_div_1_tr_21_Template(rf, ctx) {
  if (rf & 1) {
    const _r6 = \u0275\u0275getCurrentView();
    \u0275\u0275elementStart(0, "tr", 42)(1, "td", 43)(2, "img", 44);
    \u0275\u0275listener("error", function ProductsComponent_div_36_div_1_tr_21_Template_img_error_2_listener($event) {
      \u0275\u0275restoreView(_r6);
      const ctx_r2 = \u0275\u0275nextContext(3);
      return \u0275\u0275resetView(ctx_r2.onImageError($event));
    });
    \u0275\u0275elementEnd()();
    \u0275\u0275elementStart(3, "td", 45)(4, "div", 46);
    \u0275\u0275text(5);
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(6, "div", 47);
    \u0275\u0275template(7, ProductsComponent_div_36_div_1_tr_21_span_7_Template, 3, 4, "span", 48)(8, ProductsComponent_div_36_div_1_tr_21_span_8_Template, 2, 0, "span", 49);
    \u0275\u0275elementEnd();
    \u0275\u0275template(9, ProductsComponent_div_36_div_1_tr_21_div_9_Template, 2, 1, "div", 50);
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(10, "td", 51)(11, "div", 52);
    \u0275\u0275text(12);
    \u0275\u0275elementEnd();
    \u0275\u0275template(13, ProductsComponent_div_36_div_1_tr_21_div_13_Template, 2, 1, "div", 53);
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(14, "td", 54)(15, "div", 55);
    \u0275\u0275template(16, ProductsComponent_div_36_div_1_tr_21_div_16_Template, 5, 1, "div", 56);
    \u0275\u0275elementStart(17, "div", 57);
    \u0275\u0275template(18, ProductsComponent_div_36_div_1_tr_21_span_18_Template, 2, 0, "span", 58);
    \u0275\u0275elementStart(19, "span", 59);
    \u0275\u0275text(20);
    \u0275\u0275elementEnd()();
    \u0275\u0275template(21, ProductsComponent_div_36_div_1_tr_21_div_21_Template, 2, 1, "div", 60);
    \u0275\u0275elementEnd()();
    \u0275\u0275elementStart(22, "td", 61)(23, "div", 62)(24, "span", 63);
    \u0275\u0275text(25);
    \u0275\u0275elementEnd();
    \u0275\u0275template(26, ProductsComponent_div_36_div_1_tr_21_div_26_Template, 2, 0, "div", 64);
    \u0275\u0275elementEnd()();
    \u0275\u0275elementStart(27, "td", 65)(28, "div", 66);
    \u0275\u0275text(29);
    \u0275\u0275elementEnd();
    \u0275\u0275template(30, ProductsComponent_div_36_div_1_tr_21_div_30_Template, 2, 1, "div", 67);
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(31, "td", 68)(32, "span", 69);
    \u0275\u0275text(33);
    \u0275\u0275elementEnd()();
    \u0275\u0275elementStart(34, "td", 70)(35, "div", 71)(36, "button", 72);
    \u0275\u0275listener("click", function ProductsComponent_div_36_div_1_tr_21_Template_button_click_36_listener() {
      const product_r7 = \u0275\u0275restoreView(_r6).$implicit;
      const ctx_r2 = \u0275\u0275nextContext(3);
      return \u0275\u0275resetView(ctx_r2.viewProductDetails(product_r7));
    });
    \u0275\u0275text(37, " \u{1F441}\uFE0F ");
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(38, "button", 73);
    \u0275\u0275listener("click", function ProductsComponent_div_36_div_1_tr_21_Template_button_click_38_listener() {
      const product_r7 = \u0275\u0275restoreView(_r6).$implicit;
      const ctx_r2 = \u0275\u0275nextContext(3);
      return \u0275\u0275resetView(ctx_r2.editProduct(product_r7));
    });
    \u0275\u0275text(39, " \u270F\uFE0F ");
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(40, "button", 74);
    \u0275\u0275listener("click", function ProductsComponent_div_36_div_1_tr_21_Template_button_click_40_listener() {
      const product_r7 = \u0275\u0275restoreView(_r6).$implicit;
      const ctx_r2 = \u0275\u0275nextContext(3);
      return \u0275\u0275resetView(ctx_r2.deleteProduct(product_r7));
    });
    \u0275\u0275text(41, " \u{1F5D1}\uFE0F ");
    \u0275\u0275elementEnd()()()();
  }
  if (rf & 2) {
    const product_r7 = ctx.$implicit;
    const ctx_r2 = \u0275\u0275nextContext(3);
    \u0275\u0275advance(2);
    \u0275\u0275property("src", ctx_r2.getMainProductImage(product_r7), \u0275\u0275sanitizeUrl)("alt", product_r7.nom);
    \u0275\u0275advance(3);
    \u0275\u0275textInterpolate(product_r7.nom);
    \u0275\u0275advance(2);
    \u0275\u0275property("ngIf", product_r7.dateAjout);
    \u0275\u0275advance();
    \u0275\u0275property("ngIf", ctx_r2.hasPromotion(product_r7));
    \u0275\u0275advance();
    \u0275\u0275property("ngIf", product_r7.description);
    \u0275\u0275advance(3);
    \u0275\u0275textInterpolate(product_r7.referenceOriginal);
    \u0275\u0275advance();
    \u0275\u0275property("ngIf", product_r7.referenceFournisseur);
    \u0275\u0275advance(3);
    \u0275\u0275property("ngIf", ctx_r2.hasPromotion(product_r7));
    \u0275\u0275advance(2);
    \u0275\u0275property("ngIf", ctx_r2.hasPromotion(product_r7));
    \u0275\u0275advance(2);
    \u0275\u0275textInterpolate(ctx_r2.formatPrice(ctx_r2.getFinalPrice(product_r7)));
    \u0275\u0275advance();
    \u0275\u0275property("ngIf", ctx_r2.hasPromotion(product_r7) && product_r7.pourcentageRemiseTotale);
    \u0275\u0275advance(3);
    \u0275\u0275property("ngClass", \u0275\u0275pureFunction1(20, _c0, product_r7.stock <= 10));
    \u0275\u0275advance();
    \u0275\u0275textInterpolate1(" ", product_r7.stock, " ");
    \u0275\u0275advance();
    \u0275\u0275property("ngIf", product_r7.stock <= 10);
    \u0275\u0275advance(3);
    \u0275\u0275textInterpolate(product_r7.sousCategorie == null ? null : product_r7.sousCategorie.nom);
    \u0275\u0275advance();
    \u0275\u0275property("ngIf", product_r7.marque == null ? null : product_r7.marque.name);
    \u0275\u0275advance(2);
    \u0275\u0275classMap(ctx_r2.getStatusClass(product_r7));
    \u0275\u0275advance();
    \u0275\u0275textInterpolate1(" ", ctx_r2.getStatusText(product_r7), " ");
  }
}
function ProductsComponent_div_36_div_1_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "div", 39)(1, "table", 40)(2, "thead")(3, "tr")(4, "th");
    \u0275\u0275text(5, "Image");
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(6, "th");
    \u0275\u0275text(7, "Produit");
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(8, "th");
    \u0275\u0275text(9, "R\xE9f\xE9rence");
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(10, "th");
    \u0275\u0275text(11, "Prix");
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(12, "th");
    \u0275\u0275text(13, "Stock");
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(14, "th");
    \u0275\u0275text(15, "Cat\xE9gorie");
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(16, "th");
    \u0275\u0275text(17, "Statut");
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(18, "th");
    \u0275\u0275text(19, "Actions");
    \u0275\u0275elementEnd()()();
    \u0275\u0275elementStart(20, "tbody");
    \u0275\u0275template(21, ProductsComponent_div_36_div_1_tr_21_Template, 42, 22, "tr", 41);
    \u0275\u0275elementEnd()()();
  }
  if (rf & 2) {
    const ctx_r2 = \u0275\u0275nextContext(2);
    \u0275\u0275advance(21);
    \u0275\u0275property("ngForOf", ctx_r2.currentPageProducts());
  }
}
function ProductsComponent_div_36_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "div", 37);
    \u0275\u0275template(1, ProductsComponent_div_36_div_1_Template, 22, 1, "div", 38);
    \u0275\u0275elementEnd();
  }
  if (rf & 2) {
    const ctx_r2 = \u0275\u0275nextContext();
    \u0275\u0275advance();
    \u0275\u0275property("ngIf", ctx_r2.currentPageProducts().length > 0);
  }
}
function ProductsComponent_ng_template_37_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "div", 85);
    \u0275\u0275element(1, "div", 86);
    \u0275\u0275elementStart(2, "p");
    \u0275\u0275text(3, "Chargement des produits...");
    \u0275\u0275elementEnd()();
  }
}
function ProductsComponent_div_39_Template(rf, ctx) {
  if (rf & 1) {
    const _r8 = \u0275\u0275getCurrentView();
    \u0275\u0275elementStart(0, "div", 87)(1, "h3");
    \u0275\u0275text(2, "Aucun produit trouv\xE9");
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(3, "p");
    \u0275\u0275text(4, "Commencez par ajouter votre premier produit");
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(5, "button", 6);
    \u0275\u0275listener("click", function ProductsComponent_div_39_Template_button_click_5_listener() {
      \u0275\u0275restoreView(_r8);
      const ctx_r2 = \u0275\u0275nextContext();
      return \u0275\u0275resetView(ctx_r2.openAddForm());
    });
    \u0275\u0275text(6, " \u2795 Ajouter votre premier produit ");
    \u0275\u0275elementEnd()();
  }
}
function ProductsComponent_div_40_div_7_div_20_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "div", 96)(1, "label");
    \u0275\u0275text(2, "R\xE9f\xE9rence fournisseur :");
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(3, "span");
    \u0275\u0275text(4);
    \u0275\u0275elementEnd()();
  }
  if (rf & 2) {
    const ctx_r2 = \u0275\u0275nextContext(3);
    \u0275\u0275advance(4);
    \u0275\u0275textInterpolate(ctx_r2.selectedProductDetails().referenceFournisseur);
  }
}
function ProductsComponent_div_40_div_7_div_44_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "div", 96)(1, "label");
    \u0275\u0275text(2, "Prix apr\xE8s remise outlet :");
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(3, "span", 101);
    \u0275\u0275text(4);
    \u0275\u0275elementEnd()();
  }
  if (rf & 2) {
    const ctx_r2 = \u0275\u0275nextContext(3);
    \u0275\u0275advance(4);
    \u0275\u0275textInterpolate(ctx_r2.formatPrice(ctx_r2.selectedProductDetails().prixApresRemisesOutlet));
  }
}
function ProductsComponent_div_40_div_7_div_45_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "div", 96)(1, "label");
    \u0275\u0275text(2, "Prix final apr\xE8s toutes promotions :");
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(3, "span", 102);
    \u0275\u0275text(4);
    \u0275\u0275elementEnd()();
  }
  if (rf & 2) {
    const ctx_r2 = \u0275\u0275nextContext(3);
    \u0275\u0275advance(4);
    \u0275\u0275textInterpolate(ctx_r2.formatPrice(ctx_r2.selectedProductDetails().prixApresRemises));
  }
}
function ProductsComponent_div_40_div_7_div_51_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "div", 96)(1, "label");
    \u0275\u0275text(2, "\xC9conomie totale :");
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(3, "span", 103);
    \u0275\u0275text(4);
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(5, "small", 104);
    \u0275\u0275text(6);
    \u0275\u0275elementEnd()();
  }
  if (rf & 2) {
    const ctx_r2 = \u0275\u0275nextContext(3);
    \u0275\u0275advance(4);
    \u0275\u0275textInterpolate1("-", ctx_r2.selectedProductDetails().pourcentageRemiseTotale, "%");
    \u0275\u0275advance(2);
    \u0275\u0275textInterpolate1(" (", ctx_r2.formatPrice((ctx_r2.selectedProductDetails().prixVenteTTC || 0) - (ctx_r2.selectedProductDetails().prixApresRemises || 0)), " d'\xE9conomie) ");
  }
}
function ProductsComponent_div_40_div_7_div_55_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "div", 96)(1, "label");
    \u0275\u0275text(2, "Sous-cat\xE9gorie :");
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(3, "span");
    \u0275\u0275text(4);
    \u0275\u0275elementEnd()();
  }
  if (rf & 2) {
    const ctx_r2 = \u0275\u0275nextContext(3);
    \u0275\u0275advance(4);
    \u0275\u0275textInterpolate(ctx_r2.selectedProductDetails().sousCategorie.nom);
  }
}
function ProductsComponent_div_40_div_7_div_56_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "div", 96)(1, "label");
    \u0275\u0275text(2, "Marque :");
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(3, "span");
    \u0275\u0275text(4);
    \u0275\u0275elementEnd()();
  }
  if (rf & 2) {
    const ctx_r2 = \u0275\u0275nextContext(3);
    \u0275\u0275advance(4);
    \u0275\u0275textInterpolate(ctx_r2.selectedProductDetails().marque.name);
  }
}
function ProductsComponent_div_40_div_7_div_57_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "div", 96)(1, "label");
    \u0275\u0275text(2, "Forme :");
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(3, "span");
    \u0275\u0275text(4);
    \u0275\u0275elementEnd()();
  }
  if (rf & 2) {
    const ctx_r2 = \u0275\u0275nextContext(3);
    \u0275\u0275advance(4);
    \u0275\u0275textInterpolate(ctx_r2.selectedProductDetails().forme.nom);
  }
}
function ProductsComponent_div_40_div_7_div_58_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "div", 96)(1, "label");
    \u0275\u0275text(2, "Taux TVA :");
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(3, "span");
    \u0275\u0275text(4);
    \u0275\u0275elementEnd()();
  }
  if (rf & 2) {
    const ctx_r2 = \u0275\u0275nextContext(3);
    \u0275\u0275advance(4);
    \u0275\u0275textInterpolate2("", ctx_r2.selectedProductDetails().tauxTVA.libelle, " (", ctx_r2.selectedProductDetails().tauxTVA.taux, "%)");
  }
}
function ProductsComponent_div_40_div_7_div_59_div_4_span_2_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "span", 110);
    \u0275\u0275text(1, "Principale");
    \u0275\u0275elementEnd();
  }
}
function ProductsComponent_div_40_div_7_div_59_div_4_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "div", 107);
    \u0275\u0275element(1, "img", 108);
    \u0275\u0275template(2, ProductsComponent_div_40_div_7_div_59_div_4_span_2_Template, 2, 0, "span", 109);
    \u0275\u0275elementEnd();
  }
  if (rf & 2) {
    const image_r11 = ctx.$implicit;
    const ctx_r2 = \u0275\u0275nextContext(4);
    \u0275\u0275advance();
    \u0275\u0275property("src", ctx_r2.imageUrlService.getProduitImageUrl(image_r11.imageUrl), \u0275\u0275sanitizeUrl)("alt", image_r11.altText || "Image produit");
    \u0275\u0275advance();
    \u0275\u0275property("ngIf", image_r11.isMain);
  }
}
function ProductsComponent_div_40_div_7_div_59_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "div", 95)(1, "h4");
    \u0275\u0275text(2, "Images");
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(3, "div", 105);
    \u0275\u0275template(4, ProductsComponent_div_40_div_7_div_59_div_4_Template, 3, 3, "div", 106);
    \u0275\u0275elementEnd()();
  }
  if (rf & 2) {
    const ctx_r2 = \u0275\u0275nextContext(3);
    \u0275\u0275advance(4);
    \u0275\u0275property("ngForOf", ctx_r2.selectedProductDetails().images);
  }
}
function ProductsComponent_div_40_div_7_Template(rf, ctx) {
  if (rf & 1) {
    const _r10 = \u0275\u0275getCurrentView();
    \u0275\u0275elementStart(0, "div", 93)(1, "div", 94)(2, "div", 95)(3, "h4");
    \u0275\u0275text(4, "Informations g\xE9n\xE9rales");
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(5, "div", 96)(6, "label");
    \u0275\u0275text(7, "Nom :");
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(8, "span");
    \u0275\u0275text(9);
    \u0275\u0275elementEnd()();
    \u0275\u0275elementStart(10, "div", 96)(11, "label");
    \u0275\u0275text(12, "Description :");
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(13, "span");
    \u0275\u0275text(14);
    \u0275\u0275elementEnd()();
    \u0275\u0275elementStart(15, "div", 96)(16, "label");
    \u0275\u0275text(17, "R\xE9f\xE9rence originale :");
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(18, "span");
    \u0275\u0275text(19);
    \u0275\u0275elementEnd()();
    \u0275\u0275template(20, ProductsComponent_div_40_div_7_div_20_Template, 5, 1, "div", 97);
    \u0275\u0275elementStart(21, "div", 96)(22, "label");
    \u0275\u0275text(23, "Code \xE0 barres :");
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(24, "span");
    \u0275\u0275text(25);
    \u0275\u0275elementEnd()()();
    \u0275\u0275elementStart(26, "div", 95)(27, "h4");
    \u0275\u0275text(28, "Prix et stock");
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(29, "div", 96)(30, "label");
    \u0275\u0275text(31, "Prix d'achat HT :");
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(32, "span");
    \u0275\u0275text(33);
    \u0275\u0275elementEnd()();
    \u0275\u0275elementStart(34, "div", 96)(35, "label");
    \u0275\u0275text(36, "Prix de vente HT :");
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(37, "span");
    \u0275\u0275text(38);
    \u0275\u0275elementEnd()();
    \u0275\u0275elementStart(39, "div", 96)(40, "label");
    \u0275\u0275text(41, "Prix de vente TTC :");
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(42, "span");
    \u0275\u0275text(43);
    \u0275\u0275elementEnd()();
    \u0275\u0275template(44, ProductsComponent_div_40_div_7_div_44_Template, 5, 1, "div", 97)(45, ProductsComponent_div_40_div_7_div_45_Template, 5, 1, "div", 97);
    \u0275\u0275elementStart(46, "div", 96)(47, "label");
    \u0275\u0275text(48, "Stock :");
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(49, "span");
    \u0275\u0275text(50);
    \u0275\u0275elementEnd()();
    \u0275\u0275template(51, ProductsComponent_div_40_div_7_div_51_Template, 7, 2, "div", 97);
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(52, "div", 95)(53, "h4");
    \u0275\u0275text(54, "Classifications");
    \u0275\u0275elementEnd();
    \u0275\u0275template(55, ProductsComponent_div_40_div_7_div_55_Template, 5, 1, "div", 97)(56, ProductsComponent_div_40_div_7_div_56_Template, 5, 1, "div", 97)(57, ProductsComponent_div_40_div_7_div_57_Template, 5, 1, "div", 97)(58, ProductsComponent_div_40_div_7_div_58_Template, 5, 2, "div", 97);
    \u0275\u0275elementEnd();
    \u0275\u0275template(59, ProductsComponent_div_40_div_7_div_59_Template, 5, 1, "div", 98);
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(60, "div", 99)(61, "button", 6);
    \u0275\u0275listener("click", function ProductsComponent_div_40_div_7_Template_button_click_61_listener() {
      \u0275\u0275restoreView(_r10);
      const ctx_r2 = \u0275\u0275nextContext(2);
      ctx_r2.editProduct(ctx_r2.selectedProductDetails());
      return \u0275\u0275resetView(ctx_r2.closeDetails());
    });
    \u0275\u0275text(62, " \u270F\uFE0F Modifier ce produit ");
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(63, "button", 100);
    \u0275\u0275listener("click", function ProductsComponent_div_40_div_7_Template_button_click_63_listener() {
      \u0275\u0275restoreView(_r10);
      const ctx_r2 = \u0275\u0275nextContext(2);
      return \u0275\u0275resetView(ctx_r2.closeDetails());
    });
    \u0275\u0275text(64, " Fermer ");
    \u0275\u0275elementEnd()()();
  }
  if (rf & 2) {
    let tmp_15_0;
    const ctx_r2 = \u0275\u0275nextContext(2);
    \u0275\u0275advance(9);
    \u0275\u0275textInterpolate(ctx_r2.selectedProductDetails().nom);
    \u0275\u0275advance(5);
    \u0275\u0275textInterpolate(ctx_r2.selectedProductDetails().description);
    \u0275\u0275advance(5);
    \u0275\u0275textInterpolate(ctx_r2.selectedProductDetails().referenceOriginal);
    \u0275\u0275advance();
    \u0275\u0275property("ngIf", ctx_r2.selectedProductDetails().referenceFournisseur);
    \u0275\u0275advance(5);
    \u0275\u0275textInterpolate(ctx_r2.selectedProductDetails().codeABarre);
    \u0275\u0275advance(8);
    \u0275\u0275textInterpolate(ctx_r2.formatPrice(ctx_r2.selectedProductDetails().prixAchatHT));
    \u0275\u0275advance(5);
    \u0275\u0275textInterpolate(ctx_r2.formatPrice(ctx_r2.selectedProductDetails().prixVenteHT));
    \u0275\u0275advance(5);
    \u0275\u0275textInterpolate(ctx_r2.formatPrice(ctx_r2.selectedProductDetails().prixVenteTTC));
    \u0275\u0275advance();
    \u0275\u0275property("ngIf", ctx_r2.selectedProductDetails().prixApresRemisesOutlet && ctx_r2.selectedProductDetails().prixApresRemisesOutlet !== ctx_r2.selectedProductDetails().prixVenteTTC);
    \u0275\u0275advance();
    \u0275\u0275property("ngIf", ctx_r2.hasPromotion(ctx_r2.selectedProductDetails()));
    \u0275\u0275advance(4);
    \u0275\u0275classMap(ctx_r2.getStatusClass(ctx_r2.selectedProductDetails()));
    \u0275\u0275advance();
    \u0275\u0275textInterpolate1("", ctx_r2.selectedProductDetails().stock, " unit\xE9s");
    \u0275\u0275advance();
    \u0275\u0275property("ngIf", ((tmp_15_0 = ctx_r2.selectedProductDetails()) == null ? null : tmp_15_0.pourcentageRemiseTotale) && ctx_r2.selectedProductDetails().pourcentageRemiseTotale > 0);
    \u0275\u0275advance(4);
    \u0275\u0275property("ngIf", ctx_r2.selectedProductDetails().sousCategorie);
    \u0275\u0275advance();
    \u0275\u0275property("ngIf", ctx_r2.selectedProductDetails().marque);
    \u0275\u0275advance();
    \u0275\u0275property("ngIf", ctx_r2.selectedProductDetails().forme);
    \u0275\u0275advance();
    \u0275\u0275property("ngIf", ctx_r2.selectedProductDetails().tauxTVA);
    \u0275\u0275advance();
    \u0275\u0275property("ngIf", ctx_r2.selectedProductDetails().images && ctx_r2.selectedProductDetails().images.length > 0);
  }
}
function ProductsComponent_div_40_Template(rf, ctx) {
  if (rf & 1) {
    const _r9 = \u0275\u0275getCurrentView();
    \u0275\u0275elementStart(0, "div", 88);
    \u0275\u0275listener("click", function ProductsComponent_div_40_Template_div_click_0_listener() {
      \u0275\u0275restoreView(_r9);
      const ctx_r2 = \u0275\u0275nextContext();
      return \u0275\u0275resetView(ctx_r2.closeDetails());
    });
    \u0275\u0275elementStart(1, "div", 89);
    \u0275\u0275listener("click", function ProductsComponent_div_40_Template_div_click_1_listener($event) {
      \u0275\u0275restoreView(_r9);
      return \u0275\u0275resetView($event.stopPropagation());
    });
    \u0275\u0275elementStart(2, "div", 90)(3, "h3");
    \u0275\u0275text(4, "D\xE9tails du produit");
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(5, "button", 91);
    \u0275\u0275listener("click", function ProductsComponent_div_40_Template_button_click_5_listener() {
      \u0275\u0275restoreView(_r9);
      const ctx_r2 = \u0275\u0275nextContext();
      return \u0275\u0275resetView(ctx_r2.closeDetails());
    });
    \u0275\u0275text(6, " \u2715 Fermer ");
    \u0275\u0275elementEnd()();
    \u0275\u0275template(7, ProductsComponent_div_40_div_7_Template, 65, 19, "div", 92);
    \u0275\u0275elementEnd()();
  }
  if (rf & 2) {
    const ctx_r2 = \u0275\u0275nextContext();
    \u0275\u0275advance(7);
    \u0275\u0275property("ngIf", ctx_r2.selectedProductDetails());
  }
}
function ProductsComponent_div_41_div_49_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "div", 117)(1, "div", 150)(2, "strong");
    \u0275\u0275text(3);
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(4, "small", 104);
    \u0275\u0275text(5);
    \u0275\u0275elementEnd()()();
  }
  if (rf & 2) {
    const ctx_r2 = \u0275\u0275nextContext(2);
    \u0275\u0275advance(3);
    \u0275\u0275textInterpolate1("Prix de vente TTC calcul\xE9 : ", ctx_r2.formatPrice(ctx_r2.calculatedPrixTTC), "");
    \u0275\u0275advance(2);
    \u0275\u0275textInterpolate1("(TVA ", ctx_r2.selectedTauxTVA, "% incluse)");
  }
}
function ProductsComponent_div_41_option_72_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "option", 151);
    \u0275\u0275text(1);
    \u0275\u0275elementEnd();
  }
  if (rf & 2) {
    const cat_r14 = ctx.$implicit;
    \u0275\u0275property("value", cat_r14.id);
    \u0275\u0275advance();
    \u0275\u0275textInterpolate1(" ", cat_r14.nom, " ");
  }
}
function ProductsComponent_div_41_option_80_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "option", 151);
    \u0275\u0275text(1);
    \u0275\u0275elementEnd();
  }
  if (rf & 2) {
    const subCat_r15 = ctx.$implicit;
    \u0275\u0275property("value", subCat_r15.id);
    \u0275\u0275advance();
    \u0275\u0275textInterpolate1(" ", subCat_r15.nom, " ");
  }
}
function ProductsComponent_div_41_option_91_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "option", 151);
    \u0275\u0275text(1);
    \u0275\u0275elementEnd();
  }
  if (rf & 2) {
    const marque_r16 = ctx.$implicit;
    \u0275\u0275property("value", marque_r16.id);
    \u0275\u0275advance();
    \u0275\u0275textInterpolate1(" ", marque_r16.name, " ");
  }
}
function ProductsComponent_div_41_option_99_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "option", 151);
    \u0275\u0275text(1);
    \u0275\u0275elementEnd();
  }
  if (rf & 2) {
    const forme_r17 = ctx.$implicit;
    \u0275\u0275property("value", forme_r17.id);
    \u0275\u0275advance();
    \u0275\u0275textInterpolate1(" ", forme_r17.nom, " ");
  }
}
function ProductsComponent_div_41_option_108_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "option", 151);
    \u0275\u0275text(1);
    \u0275\u0275elementEnd();
  }
  if (rf & 2) {
    const taux_r18 = ctx.$implicit;
    \u0275\u0275property("value", taux_r18.id);
    \u0275\u0275advance();
    \u0275\u0275textInterpolate2(" ", taux_r18.libelle, " (", taux_r18.taux, "%) ");
  }
}
function ProductsComponent_div_41_div_112_div_4_span_5_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "span", 159);
    \u0275\u0275text(1, "(Principale)");
    \u0275\u0275elementEnd();
  }
}
function ProductsComponent_div_41_div_112_div_4_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "div", 155);
    \u0275\u0275element(1, "img", 108);
    \u0275\u0275elementStart(2, "div", 156)(3, "span", 157);
    \u0275\u0275text(4);
    \u0275\u0275elementEnd();
    \u0275\u0275template(5, ProductsComponent_div_41_div_112_div_4_span_5_Template, 2, 0, "span", 158);
    \u0275\u0275elementEnd()();
  }
  if (rf & 2) {
    const image_r19 = ctx.$implicit;
    const i_r20 = ctx.index;
    const ctx_r2 = \u0275\u0275nextContext(3);
    \u0275\u0275advance();
    \u0275\u0275property("src", ctx_r2.imageUrlService.getProduitImageUrl(image_r19.imageUrl), \u0275\u0275sanitizeUrl)("alt", image_r19.altText || "Image produit");
    \u0275\u0275advance(3);
    \u0275\u0275textInterpolate1("Image ", i_r20 + 1, "");
    \u0275\u0275advance();
    \u0275\u0275property("ngIf", image_r19.isMain);
  }
}
function ProductsComponent_div_41_div_112_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "div", 152)(1, "h6");
    \u0275\u0275text(2, "Images actuelles :");
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(3, "div", 153);
    \u0275\u0275template(4, ProductsComponent_div_41_div_112_div_4_Template, 6, 4, "div", 154);
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(5, "small", 128);
    \u0275\u0275text(6, " Les nouvelles images s\xE9lectionn\xE9es ci-dessous remplaceront les images existantes. ");
    \u0275\u0275elementEnd()();
  }
  if (rf & 2) {
    const ctx_r2 = \u0275\u0275nextContext(2);
    \u0275\u0275advance(4);
    \u0275\u0275property("ngForOf", ctx_r2.selectedProduct().images);
  }
}
function ProductsComponent_div_41_span_120_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "span");
    \u0275\u0275text(1, "\u{1F4F7} Choisir une image");
    \u0275\u0275elementEnd();
  }
}
function ProductsComponent_div_41_span_121_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "span");
    \u0275\u0275text(1, "\u2795 Ajouter autre image");
    \u0275\u0275elementEnd();
  }
}
function ProductsComponent_div_41_span_124_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "span");
    \u0275\u0275text(1, " (Maximum atteint)");
    \u0275\u0275elementEnd();
  }
}
function ProductsComponent_div_41_div_127_div_1_span_5_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "span", 159);
    \u0275\u0275text(1);
    \u0275\u0275elementEnd();
  }
  if (rf & 2) {
    const ctx_r2 = \u0275\u0275nextContext(4);
    \u0275\u0275advance();
    \u0275\u0275textInterpolate1("(", ctx_r2.isEditMode() ? "Nouvelle image principale" : "Image principale", ")");
  }
}
function ProductsComponent_div_41_div_127_div_1_Template(rf, ctx) {
  if (rf & 1) {
    const _r22 = \u0275\u0275getCurrentView();
    \u0275\u0275elementStart(0, "div", 162);
    \u0275\u0275element(1, "img", 108);
    \u0275\u0275elementStart(2, "div", 156)(3, "span", 157);
    \u0275\u0275text(4);
    \u0275\u0275elementEnd();
    \u0275\u0275template(5, ProductsComponent_div_41_div_127_div_1_span_5_Template, 2, 1, "span", 158);
    \u0275\u0275elementStart(6, "button", 163);
    \u0275\u0275listener("click", function ProductsComponent_div_41_div_127_div_1_Template_button_click_6_listener() {
      const i_r23 = \u0275\u0275restoreView(_r22).index;
      const ctx_r2 = \u0275\u0275nextContext(3);
      return \u0275\u0275resetView(ctx_r2.removeImageFile(i_r23));
    });
    \u0275\u0275text(7, "\xD7");
    \u0275\u0275elementEnd()()();
  }
  if (rf & 2) {
    const file_r24 = ctx.$implicit;
    const i_r23 = ctx.index;
    const ctx_r2 = \u0275\u0275nextContext(3);
    \u0275\u0275advance();
    \u0275\u0275property("src", ctx_r2.getImagePreview(file_r24), \u0275\u0275sanitizeUrl)("alt", file_r24.name);
    \u0275\u0275advance(3);
    \u0275\u0275textInterpolate(file_r24.name);
    \u0275\u0275advance();
    \u0275\u0275property("ngIf", i_r23 === 0);
  }
}
function ProductsComponent_div_41_div_127_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "div", 160);
    \u0275\u0275template(1, ProductsComponent_div_41_div_127_div_1_Template, 8, 4, "div", 161);
    \u0275\u0275elementEnd();
  }
  if (rf & 2) {
    const ctx_r2 = \u0275\u0275nextContext(2);
    \u0275\u0275advance();
    \u0275\u0275property("ngForOf", ctx_r2.selectedImageFiles);
  }
}
function ProductsComponent_div_41_Template(rf, ctx) {
  if (rf & 1) {
    const _r12 = \u0275\u0275getCurrentView();
    \u0275\u0275elementStart(0, "div", 88);
    \u0275\u0275listener("click", function ProductsComponent_div_41_Template_div_click_0_listener() {
      \u0275\u0275restoreView(_r12);
      const ctx_r2 = \u0275\u0275nextContext();
      return \u0275\u0275resetView(ctx_r2.closeForm());
    });
    \u0275\u0275elementStart(1, "div", 111);
    \u0275\u0275listener("click", function ProductsComponent_div_41_Template_div_click_1_listener($event) {
      \u0275\u0275restoreView(_r12);
      return \u0275\u0275resetView($event.stopPropagation());
    });
    \u0275\u0275elementStart(2, "div", 90)(3, "h3");
    \u0275\u0275text(4);
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(5, "div", 112)(6, "button", 113);
    \u0275\u0275listener("click", function ProductsComponent_div_41_Template_button_click_6_listener() {
      \u0275\u0275restoreView(_r12);
      const ctx_r2 = \u0275\u0275nextContext();
      return \u0275\u0275resetView(ctx_r2.refreshDropdowns());
    });
    \u0275\u0275text(7, " \u{1F504} Actualiser ");
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(8, "button", 91);
    \u0275\u0275listener("click", function ProductsComponent_div_41_Template_button_click_8_listener() {
      \u0275\u0275restoreView(_r12);
      const ctx_r2 = \u0275\u0275nextContext();
      return \u0275\u0275resetView(ctx_r2.closeForm());
    });
    \u0275\u0275text(9, " \u2715 Fermer ");
    \u0275\u0275elementEnd()()();
    \u0275\u0275elementStart(10, "form", 114, 1);
    \u0275\u0275listener("ngSubmit", function ProductsComponent_div_41_Template_form_ngSubmit_10_listener() {
      \u0275\u0275restoreView(_r12);
      const productForm_r13 = \u0275\u0275reference(11);
      const ctx_r2 = \u0275\u0275nextContext();
      return \u0275\u0275resetView(ctx_r2.onSubmit(productForm_r13));
    });
    \u0275\u0275elementStart(12, "div", 115)(13, "h4");
    \u0275\u0275text(14, "Informations de base");
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(15, "div", 116)(16, "div", 117)(17, "label");
    \u0275\u0275text(18, "R\xE9f\xE9rence originale *");
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(19, "input", 118);
    \u0275\u0275twoWayListener("ngModelChange", function ProductsComponent_div_41_Template_input_ngModelChange_19_listener($event) {
      \u0275\u0275restoreView(_r12);
      const ctx_r2 = \u0275\u0275nextContext();
      \u0275\u0275twoWayBindingSet(ctx_r2.formData.referenceOriginal, $event) || (ctx_r2.formData.referenceOriginal = $event);
      return \u0275\u0275resetView($event);
    });
    \u0275\u0275elementEnd()();
    \u0275\u0275elementStart(20, "div", 117)(21, "label");
    \u0275\u0275text(22, "R\xE9f\xE9rence fournisseur");
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(23, "input", 119);
    \u0275\u0275twoWayListener("ngModelChange", function ProductsComponent_div_41_Template_input_ngModelChange_23_listener($event) {
      \u0275\u0275restoreView(_r12);
      const ctx_r2 = \u0275\u0275nextContext();
      \u0275\u0275twoWayBindingSet(ctx_r2.formData.referenceFournisseur, $event) || (ctx_r2.formData.referenceFournisseur = $event);
      return \u0275\u0275resetView($event);
    });
    \u0275\u0275elementEnd()()();
    \u0275\u0275elementStart(24, "div", 116)(25, "div", 117)(26, "label");
    \u0275\u0275text(27, "Nom du produit *");
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(28, "input", 120);
    \u0275\u0275twoWayListener("ngModelChange", function ProductsComponent_div_41_Template_input_ngModelChange_28_listener($event) {
      \u0275\u0275restoreView(_r12);
      const ctx_r2 = \u0275\u0275nextContext();
      \u0275\u0275twoWayBindingSet(ctx_r2.formData.nom, $event) || (ctx_r2.formData.nom = $event);
      return \u0275\u0275resetView($event);
    });
    \u0275\u0275elementEnd()();
    \u0275\u0275elementStart(29, "div", 117)(30, "label");
    \u0275\u0275text(31, "Code \xE0 barres");
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(32, "input", 121);
    \u0275\u0275twoWayListener("ngModelChange", function ProductsComponent_div_41_Template_input_ngModelChange_32_listener($event) {
      \u0275\u0275restoreView(_r12);
      const ctx_r2 = \u0275\u0275nextContext();
      \u0275\u0275twoWayBindingSet(ctx_r2.formData.codeABarre, $event) || (ctx_r2.formData.codeABarre = $event);
      return \u0275\u0275resetView($event);
    });
    \u0275\u0275elementEnd()()();
    \u0275\u0275elementStart(33, "div", 117)(34, "label");
    \u0275\u0275text(35, "Description *");
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(36, "textarea", 122);
    \u0275\u0275twoWayListener("ngModelChange", function ProductsComponent_div_41_Template_textarea_ngModelChange_36_listener($event) {
      \u0275\u0275restoreView(_r12);
      const ctx_r2 = \u0275\u0275nextContext();
      \u0275\u0275twoWayBindingSet(ctx_r2.formData.description, $event) || (ctx_r2.formData.description = $event);
      return \u0275\u0275resetView($event);
    });
    \u0275\u0275elementEnd()()();
    \u0275\u0275elementStart(37, "div", 115)(38, "h4");
    \u0275\u0275text(39, "Prix et stock");
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(40, "div", 116)(41, "div", 117)(42, "label");
    \u0275\u0275text(43, "Prix d'achat HT (DT) *");
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(44, "input", 123);
    \u0275\u0275twoWayListener("ngModelChange", function ProductsComponent_div_41_Template_input_ngModelChange_44_listener($event) {
      \u0275\u0275restoreView(_r12);
      const ctx_r2 = \u0275\u0275nextContext();
      \u0275\u0275twoWayBindingSet(ctx_r2.formData.prixAchat, $event) || (ctx_r2.formData.prixAchat = $event);
      return \u0275\u0275resetView($event);
    });
    \u0275\u0275listener("input", function ProductsComponent_div_41_Template_input_input_44_listener() {
      \u0275\u0275restoreView(_r12);
      const ctx_r2 = \u0275\u0275nextContext();
      return \u0275\u0275resetView(ctx_r2.onPrixChange());
    });
    \u0275\u0275elementEnd()();
    \u0275\u0275elementStart(45, "div", 117)(46, "label");
    \u0275\u0275text(47, "Prix de vente HT (DT) *");
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(48, "input", 124);
    \u0275\u0275twoWayListener("ngModelChange", function ProductsComponent_div_41_Template_input_ngModelChange_48_listener($event) {
      \u0275\u0275restoreView(_r12);
      const ctx_r2 = \u0275\u0275nextContext();
      \u0275\u0275twoWayBindingSet(ctx_r2.formData.prixVente, $event) || (ctx_r2.formData.prixVente = $event);
      return \u0275\u0275resetView($event);
    });
    \u0275\u0275listener("input", function ProductsComponent_div_41_Template_input_input_48_listener() {
      \u0275\u0275restoreView(_r12);
      const ctx_r2 = \u0275\u0275nextContext();
      return \u0275\u0275resetView(ctx_r2.onPrixChange());
    });
    \u0275\u0275elementEnd()()();
    \u0275\u0275template(49, ProductsComponent_div_41_div_49_Template, 6, 2, "div", 125);
    \u0275\u0275elementStart(50, "div", 116)(51, "div", 117)(52, "label");
    \u0275\u0275text(53, "Stock initial");
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(54, "input", 126);
    \u0275\u0275twoWayListener("ngModelChange", function ProductsComponent_div_41_Template_input_ngModelChange_54_listener($event) {
      \u0275\u0275restoreView(_r12);
      const ctx_r2 = \u0275\u0275nextContext();
      \u0275\u0275twoWayBindingSet(ctx_r2.formData.stock, $event) || (ctx_r2.formData.stock = $event);
      return \u0275\u0275resetView($event);
    });
    \u0275\u0275elementEnd()();
    \u0275\u0275elementStart(55, "div", 117)(56, "label");
    \u0275\u0275text(57, "Pourcentage de remise outlet (%)");
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(58, "input", 127);
    \u0275\u0275twoWayListener("ngModelChange", function ProductsComponent_div_41_Template_input_ngModelChange_58_listener($event) {
      \u0275\u0275restoreView(_r12);
      const ctx_r2 = \u0275\u0275nextContext();
      \u0275\u0275twoWayBindingSet(ctx_r2.formData.pourcentageRemise, $event) || (ctx_r2.formData.pourcentageRemise = $event);
      return \u0275\u0275resetView($event);
    });
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(59, "small", 128);
    \u0275\u0275text(60, " \u{1F4A1} Remise outlet appliqu\xE9e automatiquement sur ce produit (d\xE9faut: 30%) ");
    \u0275\u0275elementEnd()()()();
    \u0275\u0275elementStart(61, "div", 115)(62, "h4");
    \u0275\u0275text(63, "Cat\xE9gories et classifications");
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(64, "div", 116)(65, "div", 117)(66, "label");
    \u0275\u0275text(67, "Cat\xE9gorie *");
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(68, "div", 129)(69, "select", 130);
    \u0275\u0275twoWayListener("ngModelChange", function ProductsComponent_div_41_Template_select_ngModelChange_69_listener($event) {
      \u0275\u0275restoreView(_r12);
      const ctx_r2 = \u0275\u0275nextContext();
      \u0275\u0275twoWayBindingSet(ctx_r2.formData.categorieId, $event) || (ctx_r2.formData.categorieId = $event);
      return \u0275\u0275resetView($event);
    });
    \u0275\u0275listener("change", function ProductsComponent_div_41_Template_select_change_69_listener($event) {
      \u0275\u0275restoreView(_r12);
      const ctx_r2 = \u0275\u0275nextContext();
      return \u0275\u0275resetView(ctx_r2.onCategorieChange($event));
    });
    \u0275\u0275elementStart(70, "option", 131);
    \u0275\u0275text(71, "S\xE9lectionner");
    \u0275\u0275elementEnd();
    \u0275\u0275template(72, ProductsComponent_div_41_option_72_Template, 2, 2, "option", 132);
    \u0275\u0275elementEnd()()();
    \u0275\u0275elementStart(73, "div", 117)(74, "label");
    \u0275\u0275text(75);
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(76, "div", 129)(77, "select", 133);
    \u0275\u0275twoWayListener("ngModelChange", function ProductsComponent_div_41_Template_select_ngModelChange_77_listener($event) {
      \u0275\u0275restoreView(_r12);
      const ctx_r2 = \u0275\u0275nextContext();
      \u0275\u0275twoWayBindingSet(ctx_r2.formData.sousCategorieId, $event) || (ctx_r2.formData.sousCategorieId = $event);
      return \u0275\u0275resetView($event);
    });
    \u0275\u0275elementStart(78, "option", 131);
    \u0275\u0275text(79, "S\xE9lectionner une sous-cat\xE9gorie");
    \u0275\u0275elementEnd();
    \u0275\u0275template(80, ProductsComponent_div_41_option_80_Template, 2, 2, "option", 134);
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(81, "small", 135);
    \u0275\u0275text(82);
    \u0275\u0275elementEnd()()()();
    \u0275\u0275elementStart(83, "div", 116)(84, "div", 117)(85, "label");
    \u0275\u0275text(86, "Marque *");
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(87, "div", 129)(88, "select", 136);
    \u0275\u0275twoWayListener("ngModelChange", function ProductsComponent_div_41_Template_select_ngModelChange_88_listener($event) {
      \u0275\u0275restoreView(_r12);
      const ctx_r2 = \u0275\u0275nextContext();
      \u0275\u0275twoWayBindingSet(ctx_r2.formData.marqueId, $event) || (ctx_r2.formData.marqueId = $event);
      return \u0275\u0275resetView($event);
    });
    \u0275\u0275elementStart(89, "option", 131);
    \u0275\u0275text(90, "S\xE9lectionner");
    \u0275\u0275elementEnd();
    \u0275\u0275template(91, ProductsComponent_div_41_option_91_Template, 2, 2, "option", 132);
    \u0275\u0275elementEnd()()();
    \u0275\u0275elementStart(92, "div", 117)(93, "label");
    \u0275\u0275text(94, "Forme *");
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(95, "div", 129)(96, "select", 137);
    \u0275\u0275twoWayListener("ngModelChange", function ProductsComponent_div_41_Template_select_ngModelChange_96_listener($event) {
      \u0275\u0275restoreView(_r12);
      const ctx_r2 = \u0275\u0275nextContext();
      \u0275\u0275twoWayBindingSet(ctx_r2.formData.formeId, $event) || (ctx_r2.formData.formeId = $event);
      return \u0275\u0275resetView($event);
    });
    \u0275\u0275elementStart(97, "option", 131);
    \u0275\u0275text(98, "S\xE9lectionner");
    \u0275\u0275elementEnd();
    \u0275\u0275template(99, ProductsComponent_div_41_option_99_Template, 2, 2, "option", 132);
    \u0275\u0275elementEnd()()()();
    \u0275\u0275elementStart(100, "div", 116)(101, "div", 117)(102, "label");
    \u0275\u0275text(103, "Taux TVA *");
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(104, "div", 129)(105, "select", 138);
    \u0275\u0275twoWayListener("ngModelChange", function ProductsComponent_div_41_Template_select_ngModelChange_105_listener($event) {
      \u0275\u0275restoreView(_r12);
      const ctx_r2 = \u0275\u0275nextContext();
      \u0275\u0275twoWayBindingSet(ctx_r2.formData.tauxTVAId, $event) || (ctx_r2.formData.tauxTVAId = $event);
      return \u0275\u0275resetView($event);
    });
    \u0275\u0275listener("change", function ProductsComponent_div_41_Template_select_change_105_listener($event) {
      \u0275\u0275restoreView(_r12);
      const ctx_r2 = \u0275\u0275nextContext();
      return \u0275\u0275resetView(ctx_r2.onTauxTVAChange($event));
    });
    \u0275\u0275elementStart(106, "option", 131);
    \u0275\u0275text(107, "S\xE9lectionner");
    \u0275\u0275elementEnd();
    \u0275\u0275template(108, ProductsComponent_div_41_option_108_Template, 2, 3, "option", 132);
    \u0275\u0275elementEnd()()()()();
    \u0275\u0275elementStart(109, "div", 115)(110, "h4");
    \u0275\u0275text(111, "Images du produit");
    \u0275\u0275elementEnd();
    \u0275\u0275template(112, ProductsComponent_div_41_div_112_Template, 7, 1, "div", 139);
    \u0275\u0275elementStart(113, "div", 117)(114, "label");
    \u0275\u0275text(115);
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(116, "input", 140, 2);
    \u0275\u0275listener("change", function ProductsComponent_div_41_Template_input_change_116_listener($event) {
      \u0275\u0275restoreView(_r12);
      const ctx_r2 = \u0275\u0275nextContext();
      return \u0275\u0275resetView(ctx_r2.onSingleImageSelected($event));
    });
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(118, "div", 141)(119, "button", 142);
    \u0275\u0275listener("click", function ProductsComponent_div_41_Template_button_click_119_listener() {
      \u0275\u0275restoreView(_r12);
      const fileInput_r21 = \u0275\u0275reference(117);
      return \u0275\u0275resetView(fileInput_r21.click());
    });
    \u0275\u0275template(120, ProductsComponent_div_41_span_120_Template, 2, 0, "span", 143)(121, ProductsComponent_div_41_span_121_Template, 2, 0, "span", 143);
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(122, "small", 144);
    \u0275\u0275text(123);
    \u0275\u0275template(124, ProductsComponent_div_41_span_124_Template, 2, 0, "span", 143);
    \u0275\u0275elementEnd()();
    \u0275\u0275elementStart(125, "small", 145);
    \u0275\u0275text(126);
    \u0275\u0275elementEnd();
    \u0275\u0275template(127, ProductsComponent_div_41_div_127_Template, 2, 1, "div", 146);
    \u0275\u0275elementEnd()();
    \u0275\u0275elementStart(128, "div", 147)(129, "button", 148);
    \u0275\u0275listener("click", function ProductsComponent_div_41_Template_button_click_129_listener() {
      \u0275\u0275restoreView(_r12);
      const ctx_r2 = \u0275\u0275nextContext();
      return \u0275\u0275resetView(ctx_r2.closeForm());
    });
    \u0275\u0275text(130, " Annuler ");
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(131, "button", 149);
    \u0275\u0275text(132);
    \u0275\u0275elementEnd()()()()();
  }
  if (rf & 2) {
    let tmp_28_0;
    const productForm_r13 = \u0275\u0275reference(11);
    const ctx_r2 = \u0275\u0275nextContext();
    \u0275\u0275advance(4);
    \u0275\u0275textInterpolate1("", ctx_r2.isEditMode() ? "Modifier" : "Ajouter", " un produit");
    \u0275\u0275advance(15);
    \u0275\u0275twoWayProperty("ngModel", ctx_r2.formData.referenceOriginal);
    \u0275\u0275advance(4);
    \u0275\u0275twoWayProperty("ngModel", ctx_r2.formData.referenceFournisseur);
    \u0275\u0275advance(5);
    \u0275\u0275twoWayProperty("ngModel", ctx_r2.formData.nom);
    \u0275\u0275advance(4);
    \u0275\u0275twoWayProperty("ngModel", ctx_r2.formData.codeABarre);
    \u0275\u0275advance(4);
    \u0275\u0275twoWayProperty("ngModel", ctx_r2.formData.description);
    \u0275\u0275advance(8);
    \u0275\u0275twoWayProperty("ngModel", ctx_r2.formData.prixAchat);
    \u0275\u0275advance(4);
    \u0275\u0275twoWayProperty("ngModel", ctx_r2.formData.prixVente);
    \u0275\u0275advance();
    \u0275\u0275property("ngIf", ctx_r2.calculatedPrixTTC > 0);
    \u0275\u0275advance(5);
    \u0275\u0275twoWayProperty("ngModel", ctx_r2.formData.stock);
    \u0275\u0275advance(4);
    \u0275\u0275twoWayProperty("ngModel", ctx_r2.formData.pourcentageRemise);
    \u0275\u0275advance(11);
    \u0275\u0275twoWayProperty("ngModel", ctx_r2.formData.categorieId);
    \u0275\u0275advance(3);
    \u0275\u0275property("ngForOf", ctx_r2.categoriesDropdown());
    \u0275\u0275advance(3);
    \u0275\u0275textInterpolate1("Sous-cat\xE9gorie * (", ctx_r2.sousCategoriesDropdown().length, " disponibles)");
    \u0275\u0275advance(2);
    \u0275\u0275twoWayProperty("ngModel", ctx_r2.formData.sousCategorieId);
    \u0275\u0275advance(3);
    \u0275\u0275property("ngForOf", ctx_r2.sousCategoriesDropdown())("ngForTrackBy", ctx_r2.trackBySousCategorieId);
    \u0275\u0275advance(2);
    \u0275\u0275textInterpolate1(" Debug: ", ctx_r2.sousCategoriesDropdown().length, " sous-cat\xE9gories charg\xE9es ");
    \u0275\u0275advance(6);
    \u0275\u0275twoWayProperty("ngModel", ctx_r2.formData.marqueId);
    \u0275\u0275advance(3);
    \u0275\u0275property("ngForOf", ctx_r2.marquesDropdown());
    \u0275\u0275advance(5);
    \u0275\u0275twoWayProperty("ngModel", ctx_r2.formData.formeId);
    \u0275\u0275advance(3);
    \u0275\u0275property("ngForOf", ctx_r2.formesDropdown());
    \u0275\u0275advance(6);
    \u0275\u0275twoWayProperty("ngModel", ctx_r2.formData.tauxTVAId);
    \u0275\u0275advance(3);
    \u0275\u0275property("ngForOf", ctx_r2.tauxTVADropdown());
    \u0275\u0275advance(4);
    \u0275\u0275property("ngIf", ctx_r2.isEditMode() && ((tmp_28_0 = ctx_r2.selectedProduct()) == null ? null : tmp_28_0.images == null ? null : tmp_28_0.images.length));
    \u0275\u0275advance(3);
    \u0275\u0275textInterpolate(ctx_r2.isEditMode() ? "Nouvelles images" : "Images du produit");
    \u0275\u0275advance(4);
    \u0275\u0275property("disabled", ctx_r2.selectedImageFiles.length >= 5);
    \u0275\u0275advance();
    \u0275\u0275property("ngIf", ctx_r2.selectedImageFiles.length === 0);
    \u0275\u0275advance();
    \u0275\u0275property("ngIf", ctx_r2.selectedImageFiles.length > 0);
    \u0275\u0275advance(2);
    \u0275\u0275textInterpolate1(" ", ctx_r2.selectedImageFiles.length, "/5 images s\xE9lectionn\xE9es ");
    \u0275\u0275advance();
    \u0275\u0275property("ngIf", ctx_r2.selectedImageFiles.length >= 5);
    \u0275\u0275advance(2);
    \u0275\u0275textInterpolate1(" ", ctx_r2.isEditMode() ? "Ajoutez de nouvelles images pour remplacer les existantes." : "La premi\xE8re image sera l'image principale.", " ");
    \u0275\u0275advance();
    \u0275\u0275property("ngIf", ctx_r2.selectedImageFiles.length > 0);
    \u0275\u0275advance(4);
    \u0275\u0275property("disabled", !productForm_r13.valid || ctx_r2.isLoading());
    \u0275\u0275advance();
    \u0275\u0275textInterpolate1(" ", ctx_r2.isEditMode() ? "Modifier" : "Ajouter", " ");
  }
}
var ProductsComponent = class _ProductsComponent {
  produitService;
  categorieService;
  sousCategorieService;
  marqueService;
  formeService;
  tauxTVAService;
  authService;
  imageUrlService;
  // Signaux pour la gestion d'état
  products = signal([]);
  isLoading = signal(false);
  error = signal(null);
  showForm = signal(false);
  isEditMode = signal(false);
  selectedProduct = signal(null);
  searchQuery = signal("");
  // Dropdowns
  categoriesDropdown = signal([]);
  sousCategoriesDropdown = signal([]);
  marquesDropdown = signal([]);
  formesDropdown = signal([]);
  tauxTVADropdown = signal([]);
  // Gestion des images
  selectedImageFiles = [];
  imagePreviewUrls = [];
  // Filtres
  filters = {
    stockFaible: false,
    enPromotion: false,
    misEnAvant: false
  };
  // Valeurs du formulaire pour ngModel
  formData = {
    referenceOriginal: "",
    referenceFournisseur: "",
    codeABarre: "",
    nom: "",
    description: "",
    prixAchat: "",
    prixVente: "",
    stock: "",
    pourcentageRemise: "0",
    categorieId: "",
    sousCategorieId: "",
    marqueId: "",
    formeId: "",
    tauxTVAId: ""
  };
  // Calcul des prix
  calculatedPrixTTC = 0;
  selectedTauxTVA = 0;
  // Pagination
  currentPage = signal(1);
  itemsPerPage = 12;
  // Affichage des détails
  showDetails = signal(false);
  selectedProductDetails = signal(null);
  loadingDetails = signal(false);
  constructor(produitService, categorieService, sousCategorieService, marqueService, formeService, tauxTVAService, authService, imageUrlService) {
    this.produitService = produitService;
    this.categorieService = categorieService;
    this.sousCategorieService = sousCategorieService;
    this.marqueService = marqueService;
    this.formeService = formeService;
    this.tauxTVAService = tauxTVAService;
    this.authService = authService;
    this.imageUrlService = imageUrlService;
  }
  ngOnInit() {
    console.log("\u{1F680} Initialisation du composant ProductsComponent");
    this.testBackendConnection();
    this.loadData();
    this.loadDropdowns();
  }
  testBackendConnection() {
    console.log("\u{1F517} Test de connexion au backend...");
    this.categorieService.getDropdown().subscribe({
      next: (response) => {
        console.log("\u2705 Backend accessible - Cat\xE9gories:", response);
      },
      error: (error) => {
        console.error("\u274C Backend inaccessible:", error);
        console.error("\u274C Status:", error.status);
        console.error("\u274C Message:", error.message);
        console.error("\u274C URL:", error.url);
      }
    });
  }
  // Computed properties
  stats = computed(() => {
    const products = this.products();
    return {
      total: products.length,
      active: products.filter((p) => p.stock > 0).length,
      outOfStock: products.filter((p) => p.stock === 0).length,
      totalValue: products.reduce((sum, p) => {
        const prixFinal = p.prixApresRemises || p.prixVenteTTC;
        return sum + prixFinal * p.stock;
      }, 0)
    };
  });
  filteredProducts = computed(() => {
    const products = this.products();
    const query = this.searchQuery().toLowerCase();
    if (!query)
      return products;
    return products.filter((product) => product.nom.toLowerCase().includes(query) || product.description?.toLowerCase().includes(query) || product.referenceOriginal.toLowerCase().includes(query));
  });
  currentPageProducts = computed(() => {
    const filtered = this.filteredProducts();
    const start = (this.currentPage() - 1) * this.itemsPerPage;
    const end = start + this.itemsPerPage;
    return filtered.slice(start, end);
  });
  // Méthodes de chargement des données
  loadData() {
    console.log("\u{1F504} ProductsComponent: D\xE9but du chargement des donn\xE9es...");
    this.isLoading.set(true);
    this.error.set(null);
    const isAuth = this.authService.isAuthenticated();
    const currentUser = this.authService.getCurrentUser();
    const token = localStorage.getItem("auth_token") || localStorage.getItem("token");
    console.log("\u{1F50D} ProductsComponent: \xC9tat d'authentification:");
    console.log("  - isAuthenticated:", isAuth);
    console.log("  - currentUser:", currentUser);
    console.log("  - token pr\xE9sent:", !!token);
    console.log("  - localStorage keys:", Object.keys(localStorage));
    if (!currentUser?.id) {
      console.error("\u274C ProductsComponent: Utilisateur non connect\xE9 ou ID manquant");
      console.log("  - currentUser:", currentUser);
      this.error.set("Utilisateur non connect\xE9. Veuillez vous reconnecter.");
      this.isLoading.set(false);
      return;
    }
    console.log(`\u{1F504} ProductsComponent: Chargement des produits pour le fournisseur ID: ${currentUser.id}`);
    this.produitService.getByFournisseur(currentUser.id).subscribe({
      next: (products) => {
        console.log(`\u2705 ProductsComponent: ${products.length} produits du fournisseur charg\xE9s avec images:`, products);
        products.forEach((product) => {
          console.log(`\u{1F5BC}\uFE0F Produit ${product.nom}:`, {
            id: product.id,
            images: product.images,
            imagePrincipaleUrl: product.imagePrincipaleUrl,
            imagesCount: product.images?.length || 0
          });
        });
        this.products.set(products);
        this.isLoading.set(false);
      },
      error: (error) => {
        console.error("\u274C ProductsComponent: Erreur lors du chargement des produits:", error);
        console.log("  - Status:", error.status);
        console.log("  - Message:", error.message);
        console.log("  - URL:", error.url);
        let errorMessage = "Erreur lors du chargement des produits";
        if (error.status === 401) {
          errorMessage = "Non autoris\xE9. Veuillez vous reconnecter.";
        } else if (error.status === 404) {
          errorMessage = "Endpoint non trouv\xE9. V\xE9rifiez le backend.";
        } else if (error.status === 0) {
          errorMessage = "Impossible de contacter le serveur.";
        }
        this.error.set(errorMessage);
        this.isLoading.set(false);
      }
    });
  }
  loadDropdowns() {
    console.log("\u{1F504} D\xE9but du chargement des dropdowns...");
    console.log("\u{1F4CB} Chargement des cat\xE9gories...");
    this.categorieService.getAll().subscribe({
      next: (categories) => {
        console.log("\u2705 Cat\xE9gories re\xE7ues:", categories);
        const categoriesFormatted = categories.map((cat) => ({
          id: cat.id,
          nom: cat.nom,
          sousCategoriesCount: cat.sousCategoriesCount || 0
        }));
        console.log("\u2705 Cat\xE9gories format\xE9es:", categoriesFormatted);
        this.categoriesDropdown.set(categoriesFormatted);
      },
      error: (error) => {
        console.error("\u274C Erreur lors du chargement des cat\xE9gories:", error);
        this.categoriesDropdown.set([]);
      }
    });
    console.log("\u{1F4CB} Chargement des marques...");
    this.marqueService.getAll().subscribe({
      next: (marques) => {
        console.log("\u2705 Marques re\xE7ues:", marques);
        const marquesFormatted = marques.map((marque) => ({
          id: marque.id,
          name: marque.name,
          logo: marque.logo || ""
        }));
        console.log("\u2705 Marques format\xE9es:", marquesFormatted);
        this.marquesDropdown.set(marquesFormatted);
      },
      error: (error) => {
        console.error("\u274C Erreur lors du chargement des marques:", error);
        this.marquesDropdown.set([]);
      }
    });
    console.log("\u{1F4CB} Chargement des formes...");
    this.formeService.getAll().subscribe({
      next: (formes) => {
        console.log("\u2705 Formes re\xE7ues:", formes);
        const formesFormatted = formes.map((forme) => ({
          id: forme.id,
          nom: forme.nom,
          categorieId: forme.categorieId,
          imageUrl: forme.imageUrl || ""
        }));
        console.log("\u2705 Formes format\xE9es:", formesFormatted);
        this.formesDropdown.set(formesFormatted);
      },
      error: (error) => {
        console.error("\u274C Erreur lors du chargement des formes:", error);
        this.formesDropdown.set([]);
      }
    });
    console.log("\u{1F4CB} Chargement des taux TVA...");
    this.tauxTVAService.getAll().subscribe({
      next: (taux) => {
        console.log("\u2705 Taux TVA re\xE7us:", taux);
        const tauxFormatted = taux.map((tva) => ({
          id: tva.id,
          libelle: tva.libelle,
          taux: tva.taux,
          estActif: tva.estActif
        }));
        console.log("\u2705 Taux TVA format\xE9s:", tauxFormatted);
        this.tauxTVADropdown.set(tauxFormatted);
      },
      error: (error) => {
        console.error("\u274C Erreur lors du chargement des taux TVA:", error);
        this.tauxTVADropdown.set([]);
      }
    });
    console.log("\u{1F504} Fin de l'initialisation du chargement des dropdowns");
  }
  // Méthodes d'interface utilisateur
  onSearch(event) {
    this.searchQuery.set(event.target.value);
    this.currentPage.set(1);
  }
  openAddForm() {
    this.isEditMode.set(false);
    this.selectedProduct.set(null);
    this.showForm.set(true);
    this.clearImageSelection();
    this.resetFormData();
    if (this.categoriesDropdown().length === 0) {
      console.log("\u26A0\uFE0F Dropdowns vides, rechargement forc\xE9...");
      this.loadDropdowns();
    }
    console.log("\u{1F4CA} \xC9tat des dropdowns \xE0 l'ouverture du formulaire:");
    console.log("- Cat\xE9gories:", this.categoriesDropdown());
    console.log("- Marques:", this.marquesDropdown());
    console.log("- Formes:", this.formesDropdown());
    console.log("- Taux TVA:", this.tauxTVADropdown());
  }
  resetFormData() {
    this.formData = {
      referenceOriginal: "",
      referenceFournisseur: this.generateUniqueReference(),
      codeABarre: "",
      nom: "",
      description: "",
      prixAchat: "",
      prixVente: "",
      stock: "",
      pourcentageRemise: "30",
      // Valeur par défaut de 30% pour la remise outlet
      categorieId: "",
      sousCategorieId: "",
      marqueId: "",
      formeId: "",
      tauxTVAId: ""
    };
    console.log("\u{1F504} Formulaire r\xE9initialis\xE9 avec remise par d\xE9faut de 30%");
  }
  generateUniqueReference() {
    const timestamp = Date.now();
    const random = Math.floor(Math.random() * 1e3);
    return `FOUR-${timestamp}-${random}`;
  }
  editProduct(product) {
    console.log("\u{1F527} D\xE9but de l'\xE9dition du produit:", product);
    console.log("\u{1F527} ID du produit:", product.id);
    console.log("\u{1F527} Nom du produit:", product.nom);
    this.isEditMode.set(true);
    console.log("\u2705 Mode \xE9dition activ\xE9:", this.isEditMode());
    this.showForm.set(true);
    console.log("\u2705 Formulaire affich\xE9:", this.showForm());
    if (this.categoriesDropdown().length === 0) {
      console.log("\u26A0\uFE0F Dropdowns vides, rechargement...");
      this.loadDropdowns();
    }
    console.log("\u{1F504} Chargement des d\xE9tails complets du produit depuis l'API...");
    this.produitService.getById(product.id).subscribe({
      next: (productDetails) => {
        console.log("\u2705 D\xE9tails complets du produit re\xE7us:", productDetails);
        this.selectedProduct.set(productDetails);
        console.log("\u2705 Produit complet s\xE9lectionn\xE9:", this.selectedProduct());
        setTimeout(() => {
          console.log("\u{1F4DD} Chargement des donn\xE9es compl\xE8tes dans le formulaire...");
          this.loadProductDataIntoForm(productDetails);
        }, 200);
      },
      error: (error) => {
        console.error("\u274C Erreur lors du chargement des d\xE9tails du produit:", error);
        this.selectedProduct.set(product);
        setTimeout(() => {
          this.loadProductDataIntoForm(product);
        }, 200);
      }
    });
  }
  loadProductDataIntoForm(product) {
    console.log("\u{1F4DD} D\xE9but du chargement des donn\xE9es du produit dans le formulaire:", product);
    console.log("\u{1F4DD} Donn\xE9es du produit \xE0 charger:");
    console.log("- ID:", product.id);
    console.log("- Nom:", product.nom);
    console.log("- Prix achat HT:", product.prixAchatHT);
    console.log("- Prix vente HT:", product.prixVenteHT);
    console.log("- Stock:", product.stock);
    console.log("- Marque ID:", product.marqueId);
    console.log("- Forme ID:", product.formeId);
    console.log("- Taux TVA ID:", product.tauxTVAId);
    console.log("- Sous-cat\xE9gorie ID:", product.sousCategorieId);
    setTimeout(() => {
      console.log("\u{1F4DD} Remplissage des champs du formulaire avec formData...");
      console.log("\u{1F4DD} Remplissage des champs de base...");
      this.formData.referenceOriginal = product.referenceOriginal;
      console.log("- R\xE9f\xE9rence originale:", this.formData.referenceOriginal);
      this.formData.referenceFournisseur = product.referenceFournisseur || "";
      console.log("- R\xE9f\xE9rence fournisseur:", this.formData.referenceFournisseur);
      this.formData.codeABarre = product.codeABarre;
      console.log("- Code \xE0 barres:", this.formData.codeABarre);
      this.formData.nom = product.nom;
      console.log("- Nom:", this.formData.nom);
      this.formData.description = product.description || "";
      console.log("- Description:", this.formData.description);
      this.formData.prixAchat = product.prixAchatHT.toString();
      console.log("- Prix achat:", this.formData.prixAchat);
      this.formData.prixVente = product.prixVenteHT.toString();
      console.log("- Prix vente:", this.formData.prixVente);
      this.formData.stock = product.stock.toString();
      console.log("- Stock:", this.formData.stock);
      this.formData.pourcentageRemise = product.pourcentageRemiseTotale?.toString() || "0";
      console.log("- Pourcentage remise:", this.formData.pourcentageRemise);
      console.log("\u{1F4DD} Remplissage des dropdowns...");
      this.formData.marqueId = product.marqueId.toString();
      console.log("- Marque ID:", this.formData.marqueId);
      this.formData.formeId = product.formeId.toString();
      console.log("- Forme ID:", this.formData.formeId);
      this.formData.tauxTVAId = product.tauxTVAId.toString();
      console.log("- Taux TVA ID:", this.formData.tauxTVAId);
      console.log("\u{1F4CB} Chargement des cat\xE9gories et sous-cat\xE9gories...");
      console.log("- Sous-cat\xE9gorie du produit:", product.sousCategorie);
      console.log("- ID sous-cat\xE9gorie:", product.sousCategorieId);
      if (product.sousCategorie?.categorieId || product.sousCategorieId) {
        const categorieId = product.sousCategorie?.categorieId;
        if (categorieId) {
          this.formData.categorieId = categorieId.toString();
          console.log("\u2705 Cat\xE9gorie s\xE9lectionn\xE9e:", this.formData.categorieId);
          this.loadSousCategories(categorieId);
          setTimeout(() => {
            this.formData.sousCategorieId = product.sousCategorieId.toString();
            console.log("\u2705 Sous-cat\xE9gorie s\xE9lectionn\xE9e:", this.formData.sousCategorieId);
          }, 300);
        } else {
          console.log("\u26A0\uFE0F Pas de categorieId dans sousCategorie, recherche...");
          this.formData.sousCategorieId = product.sousCategorieId.toString();
        }
      } else {
        console.log("\u26A0\uFE0F Aucune sous-cat\xE9gorie trouv\xE9e pour ce produit");
      }
      this.selectedTauxTVA = this.tauxTVADropdown().find((t) => t.id === product.tauxTVAId)?.taux || 0;
      this.calculatePrixTTC();
      console.log("\u2705 Donn\xE9es du produit charg\xE9es dans le formulaire");
      console.log("\u{1F4CA} \xC9tat final de formData:", this.formData);
    }, 200);
  }
  loadSousCategories(categorieId) {
    this.sousCategorieService.getByCategorie(categorieId).subscribe({
      next: (sousCategories) => {
        console.log("\u2705 Sous-cat\xE9gories charg\xE9es pour \xE9dition:", sousCategories);
        const sousCategoriesArray = Array.isArray(sousCategories) ? sousCategories : [];
        this.sousCategoriesDropdown.set(sousCategoriesArray);
      },
      error: (error) => {
        console.error("\u274C Erreur lors du chargement des sous-cat\xE9gories pour \xE9dition:", error);
        this.sousCategoriesDropdown.set([]);
      }
    });
  }
  closeForm() {
    this.showForm.set(false);
    this.isEditMode.set(false);
    this.selectedProduct.set(null);
    this.clearImageSelection();
    this.resetFormData();
  }
  viewProductDetails(product) {
    console.log("\u{1F441}\uFE0F Chargement des d\xE9tails complets du produit:", product.id);
    this.loadingDetails.set(true);
    this.produitService.getById(product.id).subscribe({
      next: (productDetails) => {
        console.log("\u2705 D\xE9tails complets du produit charg\xE9s:", productDetails);
        console.log("\u{1F4CA} Relations charg\xE9es:");
        console.log("- Marque:", productDetails.marque);
        console.log("- Forme:", productDetails.forme);
        console.log("- Sous-cat\xE9gorie:", productDetails.sousCategorie);
        console.log("- Taux TVA:", productDetails.tauxTVA);
        this.selectedProductDetails.set(productDetails);
        this.showDetails.set(true);
        this.loadingDetails.set(false);
      },
      error: (error) => {
        console.error("\u274C Erreur lors du chargement des d\xE9tails du produit:", error);
        this.selectedProductDetails.set(product);
        this.showDetails.set(true);
        this.loadingDetails.set(false);
      }
    });
  }
  closeDetails() {
    this.showDetails.set(false);
    this.selectedProductDetails.set(null);
  }
  deleteProduct(product) {
    if (confirm(`\xCAtes-vous s\xFBr de vouloir supprimer "${product.nom}" ?`)) {
      this.produitService.delete(product.id).subscribe({
        next: () => {
          this.loadData();
          alert("Produit supprim\xE9 avec succ\xE8s");
        },
        error: () => alert("Erreur lors de la suppression")
      });
    }
  }
  // Méthodes utilitaires
  formatPrice(price) {
    return new Intl.NumberFormat("fr-TN", {
      style: "currency",
      currency: "TND",
      minimumFractionDigits: 2,
      maximumFractionDigits: 2
    }).format(price).replace("TND", "DT");
  }
  getFinalPrice(product) {
    let finalPrice = product.prixVenteTTC;
    if (product.prixApresRemises && product.prixApresRemises > 0) {
      finalPrice = product.prixApresRemises;
    } else if (product.prixApresAutresPromotions && product.prixApresAutresPromotions > 0) {
      finalPrice = product.prixApresAutresPromotions;
    } else if (product.prixApresRemisesOutlet && product.prixApresRemisesOutlet > 0) {
      finalPrice = product.prixApresRemisesOutlet;
    }
    return finalPrice;
  }
  hasPromotion(product) {
    return !!(product.prixApresRemises && product.prixApresRemises > 0 && product.prixApresRemises < product.prixVenteTTC || product.prixApresAutresPromotions && product.prixApresAutresPromotions > 0 && product.prixApresAutresPromotions < product.prixVenteTTC || product.prixApresRemisesOutlet && product.prixApresRemisesOutlet > 0 && product.prixApresRemisesOutlet < product.prixVenteTTC);
  }
  getStatusClass(product) {
    if (product.stock === 0)
      return "status-out-of-stock";
    if (product.stock <= 5)
      return "status-low-stock";
    return "status-in-stock";
  }
  getStatusText(product) {
    if (product.stock === 0)
      return "Rupture";
    if (product.stock <= 5)
      return "Stock faible";
    return "En stock";
  }
  // Méthodes pour les images
  clearImageSelection() {
    this.selectedImageFiles = [];
    this.imagePreviewUrls = [];
  }
  onImageFilesSelected(event) {
    const input = event.target;
    if (input.files && input.files.length > 0) {
      this.selectedImageFiles = Array.from(input.files);
      this.generateImagePreviews();
    }
  }
  onSingleImageSelected(event) {
    const input = event.target;
    const file = input.files?.[0];
    if (file) {
      console.log("\u{1F4F7} Nouvelle image s\xE9lectionn\xE9e:", file.name);
      if (this.selectedImageFiles.length >= 5) {
        alert("Vous ne pouvez pas ajouter plus de 5 images.");
        return;
      }
      this.selectedImageFiles.push(file);
      const reader = new FileReader();
      reader.onload = (e) => {
        if (e.target?.result) {
          this.imagePreviewUrls.push(e.target.result);
        }
      };
      reader.readAsDataURL(file);
      console.log("\u2705 Image ajout\xE9e. Total:", this.selectedImageFiles.length);
      input.value = "";
    }
  }
  generateImagePreviews() {
    this.imagePreviewUrls = [];
    this.selectedImageFiles.forEach((file) => {
      const reader = new FileReader();
      reader.onload = (e) => {
        if (e.target?.result) {
          this.imagePreviewUrls.push(e.target.result);
        }
      };
      reader.readAsDataURL(file);
    });
  }
  getImagePreview(file) {
    const index = this.selectedImageFiles.indexOf(file);
    return this.imagePreviewUrls[index] || "";
  }
  removeImageFile(index) {
    this.selectedImageFiles.splice(index, 1);
    this.imagePreviewUrls.splice(index, 1);
  }
  onImageError(event) {
    const img = event.target;
    if (img && !img.src.includes("data:image")) {
      console.log("\u274C Erreur de chargement d'image:", img.src);
      img.src = this.imageUrlService.getPlaceholderUrl();
    }
  }
  getMainProductImage(product) {
    console.log("\u{1F5BC}\uFE0F getMainProductImage appel\xE9 pour produit:", {
      id: product.id,
      nom: product.nom,
      images: product.images,
      imagePrincipaleUrl: product.imagePrincipaleUrl,
      imagesLength: product.images?.length
    });
    if (product.imagePrincipaleUrl) {
      console.log("\u{1F5BC}\uFE0F Utilisation de imagePrincipaleUrl:", product.imagePrincipaleUrl);
      const url = this.imageUrlService.getProduitImageUrl(product.imagePrincipaleUrl);
      console.log("\u{1F5BC}\uFE0F URL finale construite:", url);
      return url;
    }
    if (product.images?.length > 0) {
      console.log("\u{1F5BC}\uFE0F Utilisation du tableau images:", product.images);
      const mainImage = product.images.find((img) => img.isMain);
      const imageUrl = mainImage?.imageUrl || product.images[0].imageUrl;
      console.log("\u{1F5BC}\uFE0F Image s\xE9lectionn\xE9e:", { mainImage, imageUrl });
      if (imageUrl) {
        const url = this.imageUrlService.getProduitImageUrl(imageUrl);
        console.log("\u{1F5BC}\uFE0F URL finale construite:", url);
        return url;
      }
    }
    console.log("\u{1F5BC}\uFE0F Aucune image trouv\xE9e, utilisation du placeholder");
    return this.imageUrlService.getPlaceholderUrl();
  }
  // Méthodes pour le calcul des prix
  onPrixChange() {
    this.calculatePrixTTC();
  }
  onTauxTVAChange(event) {
    const tauxTVAId = +event.target.value;
    const selectedTaux = this.tauxTVADropdown().find((t) => t.id === tauxTVAId);
    this.selectedTauxTVA = selectedTaux?.taux || 0;
    this.calculatePrixTTC();
  }
  calculatePrixTTC() {
    const prixVenteHTInput = document.querySelector('input[name="prixVente"]');
    const prixVenteHT = prixVenteHTInput ? +prixVenteHTInput.value : 0;
    if (prixVenteHT > 0 && this.selectedTauxTVA > 0) {
      this.calculatedPrixTTC = prixVenteHT * (1 + this.selectedTauxTVA / 100);
    } else {
      this.calculatedPrixTTC = 0;
    }
  }
  // Méthodes pour les statistiques
  getTotalStock() {
    return this.products().reduce((total, product) => total + (product.stock || 0), 0);
  }
  getTotalValue() {
    return this.products().reduce((total, product) => {
      const stock = product.stock || 0;
      const prix = product.prixVenteHT || 0;
      return total + stock * prix;
    }, 0);
  }
  getLowStockCount() {
    const seuilStock = 10;
    return this.products().filter((product) => (product.stock || 0) <= seuilStock).length;
  }
  getStockLevelClass(stock) {
    if (stock <= 0)
      return "stock-empty";
    if (stock <= 10)
      return "stock-low";
    if (stock <= 50)
      return "stock-medium";
    return "stock-ok";
  }
  // TrackBy functions pour optimiser les performances
  trackBySousCategorieId(index, item) {
    return item.id;
  }
  onCategorieChange(event) {
    const categorieId = +event.target.value;
    console.log("\u{1F504} Changement de cat\xE9gorie:", categorieId);
    if (categorieId) {
      console.log("\u{1F4C2} R\xE9cup\xE9ration des sous-cat\xE9gories pour la cat\xE9gorie:", categorieId);
      this.sousCategorieService.getByCategorie(categorieId).subscribe({
        next: (sousCategories) => {
          console.log("\u2705 Sous-cat\xE9gories re\xE7ues:", sousCategories);
          const sousCategoriesArray = Array.isArray(sousCategories) ? sousCategories : [];
          console.log("\u{1F4CB} Sous-cat\xE9gories array:", sousCategoriesArray);
          this.sousCategoriesDropdown.set(sousCategoriesArray);
          console.log("\u{1F3AF} Signal mis \xE0 jour, valeur actuelle:", this.sousCategoriesDropdown());
        },
        error: (error) => {
          console.error("\u274C Erreur sous-cat\xE9gories:", error);
          this.sousCategoriesDropdown.set([]);
        }
      });
      this.formeService.getDropdown().subscribe({
        next: (formes) => this.formesDropdown.set(formes),
        error: (error) => console.error("\u274C Erreur formes:", error)
      });
      this.tauxTVAService.getDropdown().subscribe({
        next: (taux) => this.tauxTVADropdown.set(taux),
        error: (error) => console.error("\u274C Erreur taux TVA:", error)
      });
    } else {
      this.sousCategoriesDropdown.set([]);
      this.formesDropdown.set([]);
      this.tauxTVADropdown.set([]);
    }
  }
  refreshDropdowns() {
    console.log("\u{1F504} Actualisation manuelle des dropdowns...");
    this.categoriesDropdown.set([]);
    this.sousCategoriesDropdown.set([]);
    this.marquesDropdown.set([]);
    this.formesDropdown.set([]);
    this.tauxTVADropdown.set([]);
    this.loadDropdowns();
    setTimeout(() => {
      console.log("\u{1F4CA} \xC9tat des dropdowns apr\xE8s rechargement:");
      console.log("- Cat\xE9gories:", this.categoriesDropdown());
      console.log("- Marques:", this.marquesDropdown());
      console.log("- Formes:", this.formesDropdown());
      console.log("- Taux TVA:", this.tauxTVADropdown());
    }, 2e3);
  }
  openQuickCreateModal(type) {
    console.log("\u2795 Ouverture du modal de cr\xE9ation rapide pour:", type);
    alert(`Fonctionnalit\xE9 de cr\xE9ation rapide pour ${type} \xE0 impl\xE9menter`);
  }
  onSubmit(form) {
    if (!form.valid) {
      alert("Veuillez remplir tous les champs obligatoires");
      return;
    }
    const formData = form.value;
    const currentUser = this.authService.getCurrentUser();
    if (!currentUser?.id) {
      alert("Utilisateur non connect\xE9");
      return;
    }
    if (this.isEditMode() && this.selectedProduct()) {
      const update = {
        id: this.selectedProduct().id,
        nom: formData.nom,
        description: formData.description || "",
        prixVenteHT: +formData.prixVente || void 0,
        stock: +formData.stock || void 0,
        pourcentageRemise: +formData.pourcentageRemise || void 0,
        imageFiles: this.selectedImageFiles.length > 0 ? this.selectedImageFiles : void 0
      };
      this.produitService.update(this.selectedProduct().id, update).subscribe({
        next: () => {
          this.loadData();
          this.closeForm();
          alert("Produit mis \xE0 jour avec succ\xE8s");
        },
        error: (error) => {
          console.error("\u274C Erreur lors de la mise \xE0 jour:", error);
          alert("Erreur lors de la mise \xE0 jour du produit");
        }
      });
    } else {
      if (!formData.referenceOriginal || !formData.nom || !formData.description || !formData.prixAchat || !formData.prixVente || !formData.sousCategorieId || !formData.marqueId || !formData.formeId || !formData.tauxTVAId) {
        alert("Veuillez remplir tous les champs obligatoires");
        return;
      }
      const newProduct = {
        nom: formData.nom,
        description: formData.description || "",
        referenceOriginal: formData.referenceOriginal,
        referenceFournisseur: formData.referenceFournisseur || void 0,
        codeABarre: formData.codeABarre || `${Date.now()}${Math.floor(Math.random() * 1e3)}`,
        prixAchatHT: +formData.prixAchat || 0,
        prixVenteHT: +formData.prixVente || 0,
        stock: +formData.stock || 0,
        fournisseurId: currentUser.id,
        sousCategorieId: +formData.sousCategorieId,
        marqueId: +formData.marqueId,
        formeId: +formData.formeId,
        tauxTVAId: +formData.tauxTVAId,
        pourcentageRemise: +formData.pourcentageRemise || void 0,
        imageFiles: this.selectedImageFiles.length > 0 ? this.selectedImageFiles : void 0
      };
      this.produitService.create(newProduct).subscribe({
        next: () => {
          this.loadData();
          this.closeForm();
          alert("Produit cr\xE9\xE9 avec succ\xE8s");
        },
        error: (error) => {
          console.error("\u274C Erreur lors de la cr\xE9ation:", error);
          alert("Erreur lors de la cr\xE9ation du produit");
        }
      });
    }
  }
  static \u0275fac = function ProductsComponent_Factory(__ngFactoryType__) {
    return new (__ngFactoryType__ || _ProductsComponent)(\u0275\u0275directiveInject(ProduitService), \u0275\u0275directiveInject(CategorieService), \u0275\u0275directiveInject(SousCategorieService), \u0275\u0275directiveInject(MarqueService), \u0275\u0275directiveInject(FormeService), \u0275\u0275directiveInject(TauxTVAService), \u0275\u0275directiveInject(AuthService), \u0275\u0275directiveInject(ImageUrlService));
  };
  static \u0275cmp = /* @__PURE__ */ \u0275\u0275defineComponent({ type: _ProductsComponent, selectors: [["app-products"]], decls: 42, vars: 14, consts: [["loadingTemplate", ""], ["productForm", "ngForm"], ["fileInput", ""], [1, "products-container"], [1, "products-header"], [1, "btn", "btn-secondary", 2, "margin-right", "10px", 3, "click"], [1, "btn", "btn-primary", 3, "click"], [1, "stats-grid"], [1, "stat-card"], [1, "stat-number"], [1, "stat-label"], [1, "search-box"], ["type", "text", "placeholder", "Rechercher...", 1, "search-input", 3, "input", "value"], ["class", "error-message", "style", "background: #fee; border: 1px solid #fcc; padding: 15px; margin: 20px 0; border-radius: 5px; color: #c00;", 4, "ngIf"], ["class", "page-header", 4, "ngIf"], ["class", "stats-section", 4, "ngIf"], ["class", "toolbar", 4, "ngIf"], ["class", "products-section", 4, "ngIf", "ngIfElse"], ["class", "no-products", 4, "ngIf"], ["class", "modal-overlay", 3, "click", 4, "ngIf"], [1, "error-message", 2, "background", "#fee", "border", "1px solid #fcc", "padding", "15px", "margin", "20px 0", "border-radius", "5px", "color", "#c00"], [1, "page-header"], [1, "header-content"], [1, "header-actions"], [1, "stats-section"], [1, "stat-icon"], [1, "stat-content"], [1, "stat-value"], ["class", "stat-card alert", 4, "ngIf"], [1, "stat-card", "alert"], [1, "toolbar"], [1, "search-section"], ["type", "text", "placeholder", "Rechercher un produit...", 1, "form-control", 3, "ngModelChange", "ngModel"], [1, "search-icon"], [1, "filters"], [1, "filter-checkbox"], ["type", "checkbox", 3, "ngModelChange", "ngModel"], [1, "products-section"], ["class", "table-container", 4, "ngIf"], [1, "table-container"], [1, "products-table"], ["class", "product-row", 4, "ngFor", "ngForOf"], [1, "product-row"], [1, "image-cell"], [1, "product-image", 3, "error", "src", "alt"], [1, "product-info"], [1, "product-name"], [1, "product-meta"], ["class", "date", 4, "ngIf"], ["class", "featured-badge", 4, "ngIf"], ["class", "product-description", 4, "ngIf"], [1, "reference-cell"], [1, "ref-original"], ["class", "ref-fournisseur", 4, "ngIf"], [1, "price-cell"], [1, "price-container"], ["class", "price-initial", 4, "ngIf"], [1, "price-final"], ["class", "price-label", 4, "ngIf"], [1, "price-value", "final"], ["class", "discount-badge", 4, "ngIf"], [1, "stock-cell"], [1, "stock-container"], [1, "stock-value", 3, "ngClass"], ["class", "stock-alert", 4, "ngIf"], [1, "category-cell"], [1, "category"], ["class", "brand", 4, "ngIf"], [1, "status-cell"], [1, "status-badge"], [1, "actions-cell"], [1, "action-buttons"], ["title", "Voir les d\xE9tails", 1, "btn-action", "btn-view", 3, "click"], ["title", "Modifier le produit", 1, "btn-action", "btn-edit", 3, "click"], ["title", "Supprimer le produit", 1, "btn-action", "btn-delete", 3, "click"], [1, "date"], [1, "featured-badge"], [1, "product-description"], [1, "ref-fournisseur"], [1, "price-initial"], [1, "price-label"], [1, "price-value", "initial", "crossed"], [1, "discount-badge"], [1, "stock-alert"], [1, "brand"], [1, "loading"], [1, "spinner"], [1, "no-products"], [1, "modal-overlay", 3, "click"], [1, "modal-content", "details-modal", 3, "click"], [1, "modal-header"], ["type", "button", 1, "btn", "btn-sm", "btn-outline-secondary", 3, "click"], ["class", "product-details-content", 4, "ngIf"], [1, "product-details-content"], [1, "details-grid"], [1, "detail-section"], [1, "detail-item"], ["class", "detail-item", 4, "ngIf"], ["class", "detail-section", 4, "ngIf"], [1, "details-actions"], [1, "btn", "btn-secondary", 3, "click"], [1, "outlet-price"], [1, "promotion-price"], [1, "savings-badge"], [1, "text-muted"], [1, "images-gallery"], ["class", "image-item", 4, "ngFor", "ngForOf"], [1, "image-item"], [3, "src", "alt"], ["class", "main-badge", 4, "ngIf"], [1, "main-badge"], [1, "modal-content", 3, "click"], [1, "modal-actions"], ["type", "button", "title", "Recharger les listes", 1, "btn", "btn-sm", "btn-outline-secondary", 3, "click"], ["name", "productForm", 1, "product-form", 3, "ngSubmit"], [1, "form-section"], [1, "form-row"], [1, "form-group"], ["type", "text", "name", "referenceOriginal", "required", "", "placeholder", "REF-001", 1, "form-control", 3, "ngModelChange", "ngModel"], ["type", "text", "name", "referenceFournisseur", "placeholder", "FOUR-001", 1, "form-control", 3, "ngModelChange", "ngModel"], ["type", "text", "name", "nom", "required", "", "placeholder", "Nom du produit", 1, "form-control", 3, "ngModelChange", "ngModel"], ["type", "text", "name", "codeABarre", "placeholder", "G\xE9n\xE9r\xE9 automatiquement", 1, "form-control", 3, "ngModelChange", "ngModel"], ["name", "description", "required", "", "rows", "3", "placeholder", "Description du produit", 1, "form-control", 3, "ngModelChange", "ngModel"], ["type", "number", "name", "prixAchat", "required", "", "min", "0", "step", "0.01", 1, "form-control", 3, "ngModelChange", "input", "ngModel"], ["type", "number", "name", "prixVente", "required", "", "min", "0", "step", "0.01", 1, "form-control", 3, "ngModelChange", "input", "ngModel"], ["class", "form-group", 4, "ngIf"], ["type", "number", "name", "stock", "min", "0", "placeholder", "0", 1, "form-control", 3, "ngModelChange", "ngModel"], ["type", "number", "name", "pourcentageRemise", "min", "0", "max", "100", "step", "1", "placeholder", "30", "title", "Remise outlet appliqu\xE9e automatiquement (d\xE9faut: 30%)", 1, "form-control", 3, "ngModelChange", "ngModel"], [1, "form-text", "text-muted"], [1, "input-with-button"], ["name", "categorieId", "required", "", 1, "form-control", 3, "ngModelChange", "change", "ngModel"], ["value", ""], [3, "value", 4, "ngFor", "ngForOf"], ["name", "sousCategorieId", "required", "", 1, "form-control", 3, "ngModelChange", "ngModel"], [3, "value", 4, "ngFor", "ngForOf", "ngForTrackBy"], [1, "debug-info", 2, "color", "#666", "font-size", "12px"], ["name", "marqueId", "required", "", 1, "form-control", 3, "ngModelChange", "ngModel"], ["name", "formeId", "required", "", 1, "form-control", 3, "ngModelChange", "ngModel"], ["name", "tauxTVAId", "required", "", 1, "form-control", 3, "ngModelChange", "change", "ngModel"], ["class", "existing-images", 4, "ngIf"], ["type", "file", "accept", "image/*", 2, "display", "none", 3, "change"], [1, "image-upload-actions"], ["type", "button", 1, "btn", "btn-outline-primary", 3, "click", "disabled"], [4, "ngIf"], [1, "form-text", "text-muted", 2, "margin-left", "10px"], [1, "form-text", "text-muted", 2, "margin-top", "5px"], ["class", "image-preview-container", "style", "margin-top: 15px;", 4, "ngIf"], [1, "form-actions"], ["type", "button", 1, "btn", "btn-secondary", 3, "click"], ["type", "submit", 1, "btn", "btn-primary", 3, "disabled"], [1, "price-info"], [3, "value"], [1, "existing-images"], [1, "image-preview-container"], ["class", "image-preview existing", 4, "ngFor", "ngForOf"], [1, "image-preview", "existing"], [1, "image-info"], [1, "image-name"], ["class", "image-main", 4, "ngIf"], [1, "image-main"], [1, "image-preview-container", 2, "margin-top", "15px"], ["class", "image-preview new", 4, "ngFor", "ngForOf"], [1, "image-preview", "new"], ["type", "button", "title", "Supprimer cette image", 1, "btn-remove", 3, "click"]], template: function ProductsComponent_Template(rf, ctx) {
    if (rf & 1) {
      const _r1 = \u0275\u0275getCurrentView();
      \u0275\u0275elementStart(0, "div", 3)(1, "div", 4)(2, "h1");
      \u0275\u0275text(3, "\u{1F6CD}\uFE0F Gestion des Produits");
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(4, "div")(5, "button", 5);
      \u0275\u0275listener("click", function ProductsComponent_Template_button_click_5_listener() {
        \u0275\u0275restoreView(_r1);
        return \u0275\u0275resetView(ctx.loadData());
      });
      \u0275\u0275text(6, " \u{1F504} Recharger ");
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(7, "button", 6);
      \u0275\u0275listener("click", function ProductsComponent_Template_button_click_7_listener() {
        \u0275\u0275restoreView(_r1);
        return \u0275\u0275resetView(ctx.openAddForm());
      });
      \u0275\u0275text(8, " \u2795 Ajouter un produit ");
      \u0275\u0275elementEnd()()();
      \u0275\u0275elementStart(9, "div", 7)(10, "div", 8)(11, "div", 9);
      \u0275\u0275text(12);
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(13, "div", 10);
      \u0275\u0275text(14, "Total Produits");
      \u0275\u0275elementEnd()();
      \u0275\u0275elementStart(15, "div", 8)(16, "div", 9);
      \u0275\u0275text(17);
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(18, "div", 10);
      \u0275\u0275text(19, "En Stock");
      \u0275\u0275elementEnd()();
      \u0275\u0275elementStart(20, "div", 8)(21, "div", 9);
      \u0275\u0275text(22);
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(23, "div", 10);
      \u0275\u0275text(24, "Rupture");
      \u0275\u0275elementEnd()();
      \u0275\u0275elementStart(25, "div", 8)(26, "div", 9);
      \u0275\u0275text(27);
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(28, "div", 10);
      \u0275\u0275text(29, "Valeur Totale");
      \u0275\u0275elementEnd()()();
      \u0275\u0275elementStart(30, "div", 11)(31, "input", 12);
      \u0275\u0275listener("input", function ProductsComponent_Template_input_input_31_listener($event) {
        \u0275\u0275restoreView(_r1);
        return \u0275\u0275resetView(ctx.onSearch($event));
      });
      \u0275\u0275elementEnd()();
      \u0275\u0275template(32, ProductsComponent_div_32_Template, 7, 1, "div", 13)(33, ProductsComponent_div_33_Template, 9, 0, "div", 14)(34, ProductsComponent_div_34_Template, 28, 7, "div", 15)(35, ProductsComponent_div_35_Template, 19, 4, "div", 16)(36, ProductsComponent_div_36_Template, 2, 1, "div", 17)(37, ProductsComponent_ng_template_37_Template, 4, 0, "ng-template", null, 0, \u0275\u0275templateRefExtractor)(39, ProductsComponent_div_39_Template, 7, 0, "div", 18)(40, ProductsComponent_div_40_Template, 8, 1, "div", 19)(41, ProductsComponent_div_41_Template, 133, 35, "div", 19);
      \u0275\u0275elementEnd();
    }
    if (rf & 2) {
      const loadingTemplate_r25 = \u0275\u0275reference(38);
      \u0275\u0275advance(12);
      \u0275\u0275textInterpolate(ctx.stats().total);
      \u0275\u0275advance(5);
      \u0275\u0275textInterpolate(ctx.stats().active);
      \u0275\u0275advance(5);
      \u0275\u0275textInterpolate(ctx.stats().outOfStock);
      \u0275\u0275advance(5);
      \u0275\u0275textInterpolate(ctx.formatPrice(ctx.stats().totalValue));
      \u0275\u0275advance(4);
      \u0275\u0275property("value", ctx.searchQuery());
      \u0275\u0275advance();
      \u0275\u0275property("ngIf", ctx.error());
      \u0275\u0275advance();
      \u0275\u0275property("ngIf", !ctx.isLoading() && !ctx.error());
      \u0275\u0275advance();
      \u0275\u0275property("ngIf", ctx.products().length > 0 && !ctx.isLoading() && !ctx.error());
      \u0275\u0275advance();
      \u0275\u0275property("ngIf", !ctx.isLoading() && !ctx.error());
      \u0275\u0275advance();
      \u0275\u0275property("ngIf", !ctx.isLoading() && !ctx.error())("ngIfElse", loadingTemplate_r25);
      \u0275\u0275advance(3);
      \u0275\u0275property("ngIf", ctx.currentPageProducts().length === 0 && !ctx.isLoading());
      \u0275\u0275advance();
      \u0275\u0275property("ngIf", ctx.showDetails());
      \u0275\u0275advance();
      \u0275\u0275property("ngIf", ctx.showForm());
    }
  }, dependencies: [CommonModule, NgClass, NgForOf, NgIf, DecimalPipe, DatePipe, FormsModule, \u0275NgNoValidate, NgSelectOption, \u0275NgSelectMultipleOption, DefaultValueAccessor, NumberValueAccessor, CheckboxControlValueAccessor, SelectControlValueAccessor, NgControlStatus, NgControlStatusGroup, RequiredValidator, MinValidator, MaxValidator, NgModel, NgForm], styles: ['@charset "UTF-8";\n\n\n\n.products-container[_ngcontent-%COMP%] {\n  padding: 2rem;\n  max-width: 1200px;\n  margin: 0 auto;\n  background: #f8f9fa;\n  min-height: 100vh;\n}\n.products-header[_ngcontent-%COMP%] {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 2rem;\n  background: white;\n  padding: 1.5rem;\n  border-radius: 12px;\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\n}\n.products-header[_ngcontent-%COMP%]   h1[_ngcontent-%COMP%] {\n  margin: 0;\n  color: #333;\n  font-size: 2rem;\n  font-weight: 600;\n}\n.stats-grid[_ngcontent-%COMP%] {\n  display: grid;\n  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));\n  gap: 1rem;\n  margin-bottom: 2rem;\n}\n.stat-card[_ngcontent-%COMP%] {\n  background: white;\n  padding: 1.5rem;\n  border-radius: 12px;\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\n  text-align: center;\n  transition: transform 0.2s ease;\n}\n.stat-card[_ngcontent-%COMP%]:hover {\n  transform: translateY(-2px);\n}\n.stat-number[_ngcontent-%COMP%] {\n  font-size: 2.5rem;\n  font-weight: bold;\n  color: #007bff;\n  margin-bottom: 0.5rem;\n}\n.stat-label[_ngcontent-%COMP%] {\n  color: #666;\n  font-size: 0.9rem;\n  text-transform: uppercase;\n  letter-spacing: 0.5px;\n}\n.search-box[_ngcontent-%COMP%] {\n  margin-bottom: 2rem;\n}\n.search-input[_ngcontent-%COMP%] {\n  width: 100%;\n  max-width: 400px;\n  padding: 0.75rem 1rem;\n  border: 2px solid #e9ecef;\n  border-radius: 8px;\n  font-size: 1rem;\n  transition: border-color 0.2s ease;\n}\n.search-input[_ngcontent-%COMP%]:focus {\n  outline: none;\n  border-color: #007bff;\n  box-shadow: 0 0 0 3px rgba(0, 123, 255, 0.1);\n}\n.products-grid[_ngcontent-%COMP%] {\n  display: grid;\n  grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));\n  gap: 1.5rem;\n  margin-bottom: 2rem;\n}\n.product-card[_ngcontent-%COMP%] {\n  background: white;\n  border-radius: 12px;\n  padding: 1.5rem;\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\n  transition: all 0.3s ease;\n  border: 1px solid #e9ecef;\n  cursor: pointer;\n}\n.product-card[_ngcontent-%COMP%]:hover {\n  transform: translateY(-4px);\n  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);\n}\n.product-header[_ngcontent-%COMP%] {\n  display: flex;\n  justify-content: space-between;\n  align-items: flex-start;\n  margin-bottom: 1rem;\n}\n.product-header[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%] {\n  margin: 0;\n  color: #333;\n  font-size: 1.25rem;\n  font-weight: 600;\n}\n.product-status[_ngcontent-%COMP%] {\n  padding: 0.25rem 0.75rem;\n  border-radius: 20px;\n  font-size: 0.75rem;\n  font-weight: 600;\n  text-transform: uppercase;\n  letter-spacing: 0.5px;\n}\n.product-status.status-in-stock[_ngcontent-%COMP%] {\n  background: #d4edda;\n  color: #155724;\n  border: 1px solid #c3e6cb;\n}\n.product-status.status-low-stock[_ngcontent-%COMP%] {\n  background: #fff3cd;\n  color: #856404;\n  border: 1px solid #ffeaa7;\n}\n.product-status.status-out-of-stock[_ngcontent-%COMP%] {\n  background: #f8d7da;\n  color: #721c24;\n  border: 1px solid #f5c6cb;\n}\n.product-description[_ngcontent-%COMP%] {\n  color: #666;\n  margin-bottom: 1rem;\n  line-height: 1.5;\n  display: -webkit-box;\n  -webkit-line-clamp: 2;\n  -webkit-box-orient: vertical;\n  overflow: hidden;\n}\n.product-details[_ngcontent-%COMP%] {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 1rem;\n  padding: 0.75rem;\n  background: #f8f9fa;\n  border-radius: 8px;\n}\n.price-container[_ngcontent-%COMP%] {\n  display: flex;\n  flex-direction: column;\n  gap: 0.25rem;\n  margin-bottom: 0.75rem;\n}\n.price-container[_ngcontent-%COMP%]   .price-initial[_ngcontent-%COMP%], \n.price-container[_ngcontent-%COMP%]   .price-final[_ngcontent-%COMP%] {\n  display: flex;\n  align-items: center;\n  gap: 0.5rem;\n}\n.price-container[_ngcontent-%COMP%]   .price-initial[_ngcontent-%COMP%]   .price-label[_ngcontent-%COMP%], \n.price-container[_ngcontent-%COMP%]   .price-final[_ngcontent-%COMP%]   .price-label[_ngcontent-%COMP%] {\n  font-size: 0.75rem;\n  color: #6c757d;\n  font-weight: 500;\n}\n.price-container[_ngcontent-%COMP%]   .price-initial[_ngcontent-%COMP%]   .price-value[_ngcontent-%COMP%], \n.price-container[_ngcontent-%COMP%]   .price-final[_ngcontent-%COMP%]   .price-value[_ngcontent-%COMP%] {\n  font-weight: bold;\n}\n.price-container[_ngcontent-%COMP%]   .price-initial[_ngcontent-%COMP%]   .price-value.initial[_ngcontent-%COMP%], \n.price-container[_ngcontent-%COMP%]   .price-final[_ngcontent-%COMP%]   .price-value.initial[_ngcontent-%COMP%] {\n  color: #6c757d;\n}\n.price-container[_ngcontent-%COMP%]   .price-initial[_ngcontent-%COMP%]   .price-value.initial.crossed[_ngcontent-%COMP%], \n.price-container[_ngcontent-%COMP%]   .price-final[_ngcontent-%COMP%]   .price-value.initial.crossed[_ngcontent-%COMP%] {\n  text-decoration: line-through;\n}\n.price-container[_ngcontent-%COMP%]   .price-initial[_ngcontent-%COMP%]   .price-value.final[_ngcontent-%COMP%], \n.price-container[_ngcontent-%COMP%]   .price-final[_ngcontent-%COMP%]   .price-value.final[_ngcontent-%COMP%] {\n  color: #007bff;\n  font-size: 1.1rem;\n}\n.price-container[_ngcontent-%COMP%]   .discount-badge[_ngcontent-%COMP%] {\n  background:\n    linear-gradient(\n      135deg,\n      #dc3545,\n      #c82333);\n  color: white;\n  padding: 0.25rem 0.5rem;\n  border-radius: 12px;\n  font-size: 0.75rem;\n  font-weight: bold;\n  align-self: flex-start;\n  box-shadow: 0 2px 4px rgba(220, 53, 69, 0.3);\n}\n.stock-container[_ngcontent-%COMP%] {\n  display: flex;\n  flex-direction: column;\n  gap: 0.25rem;\n}\n.stock-container[_ngcontent-%COMP%]   .stock-value[_ngcontent-%COMP%] {\n  font-size: 0.875rem;\n  color: #28a745;\n  font-weight: 500;\n}\n.stock-container[_ngcontent-%COMP%]   .stock-value.stock-critique[_ngcontent-%COMP%] {\n  color: #dc3545;\n  font-weight: bold;\n}\n.stock-container[_ngcontent-%COMP%]   .stock-alert[_ngcontent-%COMP%] {\n  font-size: 0.75rem;\n  color: #dc3545;\n  font-weight: bold;\n  background: rgba(220, 53, 69, 0.1);\n  padding: 0.125rem 0.25rem;\n  border-radius: 4px;\n}\n.product-meta[_ngcontent-%COMP%] {\n  display: flex;\n  flex-direction: column;\n  gap: 0.25rem;\n  margin-bottom: 0.5rem;\n}\n.product-meta[_ngcontent-%COMP%]   .product-category[_ngcontent-%COMP%] {\n  font-size: 0.875rem;\n  color: #007bff;\n  font-weight: 500;\n}\n.product-meta[_ngcontent-%COMP%]   .product-brand[_ngcontent-%COMP%] {\n  font-size: 0.75rem;\n  color: #6c757d;\n  font-style: italic;\n}\n.product-meta[_ngcontent-%COMP%]   .product-reference[_ngcontent-%COMP%] {\n  font-size: 0.75rem;\n  color: #6c757d;\n}\n.product-meta[_ngcontent-%COMP%]   .product-reference[_ngcontent-%COMP%]   .ref-original[_ngcontent-%COMP%] {\n  font-weight: 500;\n}\n.product-meta[_ngcontent-%COMP%]   .product-reference[_ngcontent-%COMP%]   .ref-fournisseur[_ngcontent-%COMP%] {\n  color: #28a745;\n  margin-left: 0.25rem;\n}\n.featured-badge[_ngcontent-%COMP%] {\n  position: absolute;\n  top: 0.5rem;\n  right: 0.5rem;\n  background:\n    linear-gradient(\n      135deg,\n      #ffc107,\n      #e0a800);\n  color: #212529;\n  padding: 0.25rem 0.5rem;\n  border-radius: 12px;\n  font-size: 0.75rem;\n  font-weight: bold;\n  box-shadow: 0 2px 4px rgba(255, 193, 7, 0.3);\n}\n.promotion-badge[_ngcontent-%COMP%] {\n  background: #dc3545;\n  color: white;\n  padding: 0.125rem 0.5rem;\n  border-radius: 12px;\n  font-size: 0.7rem;\n  font-weight: bold;\n  text-transform: uppercase;\n}\n.product-stock[_ngcontent-%COMP%] {\n  color: #666;\n  font-weight: 500;\n}\n.product-actions[_ngcontent-%COMP%] {\n  display: flex;\n  gap: 0.5rem;\n  justify-content: flex-end;\n}\n.btn[_ngcontent-%COMP%] {\n  padding: 0.5rem 1rem;\n  border: none;\n  border-radius: 6px;\n  cursor: pointer;\n  font-size: 0.875rem;\n  font-weight: 500;\n  transition: all 0.2s ease;\n  text-decoration: none;\n  display: inline-flex;\n  align-items: center;\n  gap: 0.25rem;\n}\n.btn[_ngcontent-%COMP%]:hover {\n  transform: translateY(-1px);\n}\n.btn[_ngcontent-%COMP%]:active {\n  transform: translateY(0);\n}\n.btn[_ngcontent-%COMP%]:disabled {\n  opacity: 0.6;\n  cursor: not-allowed;\n  transform: none;\n}\n.btn-primary[_ngcontent-%COMP%] {\n  background:\n    linear-gradient(\n      135deg,\n      #007bff,\n      #0056b3);\n  color: white;\n  box-shadow: 0 2px 4px rgba(0, 123, 255, 0.3);\n}\n.btn-primary[_ngcontent-%COMP%]:hover {\n  background:\n    linear-gradient(\n      135deg,\n      #0056b3,\n      #004085);\n  box-shadow: 0 4px 8px rgba(0, 123, 255, 0.4);\n}\n.btn-secondary[_ngcontent-%COMP%] {\n  background: #6c757d;\n  color: white;\n  box-shadow: 0 2px 4px rgba(108, 117, 125, 0.3);\n}\n.btn-secondary[_ngcontent-%COMP%]:hover {\n  background: #545b62;\n  box-shadow: 0 4px 8px rgba(108, 117, 125, 0.4);\n}\n.btn-danger[_ngcontent-%COMP%] {\n  background:\n    linear-gradient(\n      135deg,\n      #dc3545,\n      #c82333);\n  color: white;\n  box-shadow: 0 2px 4px rgba(220, 53, 69, 0.3);\n}\n.btn-danger[_ngcontent-%COMP%]:hover {\n  background:\n    linear-gradient(\n      135deg,\n      #c82333,\n      #a71e2a);\n  box-shadow: 0 4px 8px rgba(220, 53, 69, 0.4);\n}\n.btn-outline-primary[_ngcontent-%COMP%] {\n  background: transparent;\n  color: #007bff;\n  border: 2px solid #007bff;\n}\n.btn-outline-primary[_ngcontent-%COMP%]:hover {\n  background: #007bff;\n  color: white;\n}\n.btn-outline-secondary[_ngcontent-%COMP%] {\n  background: transparent;\n  color: #6c757d;\n  border: 2px solid #6c757d;\n}\n.btn-outline-secondary[_ngcontent-%COMP%]:hover {\n  background: #6c757d;\n  color: white;\n}\n.btn-sm[_ngcontent-%COMP%] {\n  padding: 0.25rem 0.5rem;\n  font-size: 0.75rem;\n}\n.btn-info[_ngcontent-%COMP%] {\n  background:\n    linear-gradient(\n      135deg,\n      #17a2b8,\n      #138496);\n  color: white;\n  box-shadow: 0 2px 4px rgba(23, 162, 184, 0.3);\n}\n.btn-info[_ngcontent-%COMP%]:hover {\n  background:\n    linear-gradient(\n      135deg,\n      #138496,\n      #117a8b);\n  box-shadow: 0 4px 8px rgba(23, 162, 184, 0.4);\n}\n.loading[_ngcontent-%COMP%] {\n  text-align: center;\n  padding: 4rem 2rem;\n  color: #666;\n}\n.loading[_ngcontent-%COMP%]   .spinner[_ngcontent-%COMP%] {\n  width: 40px;\n  height: 40px;\n  border: 4px solid #f3f3f3;\n  border-top: 4px solid #007bff;\n  border-radius: 50%;\n  animation: _ngcontent-%COMP%_spin 1s linear infinite;\n  margin: 0 auto 1rem;\n}\n.loading[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\n  font-size: 1.1rem;\n  margin: 0;\n}\n@keyframes _ngcontent-%COMP%_spin {\n  0% {\n    transform: rotate(0deg);\n  }\n  100% {\n    transform: rotate(360deg);\n  }\n}\n.no-products[_ngcontent-%COMP%] {\n  text-align: center;\n  padding: 4rem 2rem;\n  background: white;\n  border-radius: 12px;\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\n}\n.no-products[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%] {\n  color: #333;\n  margin-bottom: 1rem;\n  font-size: 1.5rem;\n}\n.no-products[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\n  color: #666;\n  margin-bottom: 2rem;\n  font-size: 1.1rem;\n}\n.modal-overlay[_ngcontent-%COMP%] {\n  position: fixed;\n  top: 0;\n  left: 0;\n  right: 0;\n  bottom: 0;\n  background: rgba(0, 0, 0, 0.5);\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  z-index: 1000;\n  padding: 1rem;\n}\n.modal-content[_ngcontent-%COMP%] {\n  background: white;\n  border-radius: 12px;\n  width: 100%;\n  max-width: 800px;\n  max-height: 90vh;\n  overflow-y: auto;\n  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);\n}\n.modal-header[_ngcontent-%COMP%] {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  padding: 1.5rem;\n  border-bottom: 1px solid #e9ecef;\n}\n.modal-header[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%] {\n  margin: 0;\n  color: #333;\n  font-size: 1.5rem;\n}\n.modal-actions[_ngcontent-%COMP%] {\n  display: flex;\n  gap: 0.5rem;\n}\n.product-form[_ngcontent-%COMP%] {\n  padding: 1.5rem;\n}\n.form-section[_ngcontent-%COMP%] {\n  margin-bottom: 2rem;\n  padding: 1.5rem;\n  background: #f8f9fa;\n  border-radius: 8px;\n  border: 1px solid #e9ecef;\n}\n.form-section[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%] {\n  margin: 0 0 1rem 0;\n  color: #333;\n  font-size: 1.2rem;\n  font-weight: 600;\n  padding-bottom: 0.5rem;\n  border-bottom: 2px solid #007bff;\n}\n.form-row[_ngcontent-%COMP%] {\n  display: grid;\n  grid-template-columns: 1fr 1fr;\n  gap: 1rem;\n  margin-bottom: 1rem;\n}\n@media (max-width: 768px) {\n  .form-row[_ngcontent-%COMP%] {\n    grid-template-columns: 1fr;\n  }\n}\n.form-group[_ngcontent-%COMP%] {\n  margin-bottom: 1rem;\n}\n.form-group[_ngcontent-%COMP%]   label[_ngcontent-%COMP%] {\n  display: block;\n  margin-bottom: 0.5rem;\n  color: #333;\n  font-weight: 500;\n  font-size: 0.9rem;\n}\n.form-control[_ngcontent-%COMP%] {\n  width: 100%;\n  padding: 0.75rem;\n  border: 2px solid #e9ecef;\n  border-radius: 6px;\n  font-size: 1rem;\n  transition: border-color 0.2s ease;\n}\n.form-control[_ngcontent-%COMP%]:focus {\n  outline: none;\n  border-color: #007bff;\n  box-shadow: 0 0 0 3px rgba(0, 123, 255, 0.1);\n}\n.form-control.ng-invalid.ng-touched[_ngcontent-%COMP%] {\n  border-color: #dc3545;\n}\n.input-with-button[_ngcontent-%COMP%] {\n  display: flex;\n  gap: 0.5rem;\n}\n.input-with-button[_ngcontent-%COMP%]   .form-control[_ngcontent-%COMP%] {\n  flex: 1;\n}\n.input-with-button[_ngcontent-%COMP%]   .btn[_ngcontent-%COMP%] {\n  flex-shrink: 0;\n}\n.form-text[_ngcontent-%COMP%] {\n  font-size: 0.875rem;\n  color: #6c757d;\n  margin-top: 0.25rem;\n}\n.text-muted[_ngcontent-%COMP%] {\n  color: #6c757d;\n}\n.price-info[_ngcontent-%COMP%] {\n  background: #e7f3ff;\n  border: 1px solid #b3d9ff;\n  border-radius: 6px;\n  padding: 1rem;\n  margin-top: 0.5rem;\n}\n.price-info[_ngcontent-%COMP%]   strong[_ngcontent-%COMP%] {\n  color: #0056b3;\n  font-size: 1.1rem;\n}\n.price-info[_ngcontent-%COMP%]   small[_ngcontent-%COMP%] {\n  display: block;\n  margin-top: 0.25rem;\n}\n.form-actions[_ngcontent-%COMP%] {\n  display: flex;\n  justify-content: flex-end;\n  gap: 1rem;\n  padding: 1.5rem;\n  border-top: 1px solid #e9ecef;\n  background: #f8f9fa;\n  margin: 0 -1.5rem -1.5rem -1.5rem;\n  border-radius: 0 0 12px 12px;\n}\n.existing-images[_ngcontent-%COMP%] {\n  margin-bottom: 1.5rem;\n  padding: 1rem;\n  background: #e8f5e8;\n  border-radius: 8px;\n  border: 1px solid #c3e6cb;\n}\n.existing-images[_ngcontent-%COMP%]   h6[_ngcontent-%COMP%] {\n  margin: 0 0 0.75rem 0;\n  color: #155724;\n  font-weight: 600;\n}\n.image-upload-actions[_ngcontent-%COMP%] {\n  display: flex;\n  align-items: center;\n  gap: 10px;\n  margin-bottom: 10px;\n}\n.image-upload-actions[_ngcontent-%COMP%]   .btn[_ngcontent-%COMP%] {\n  display: flex;\n  align-items: center;\n  gap: 5px;\n}\n.image-upload-actions[_ngcontent-%COMP%]   .btn[_ngcontent-%COMP%]:disabled {\n  opacity: 0.6;\n  cursor: not-allowed;\n}\n.image-upload-actions[_ngcontent-%COMP%]   .form-text[_ngcontent-%COMP%] {\n  margin: 0;\n  font-size: 0.875rem;\n}\n.image-preview-container[_ngcontent-%COMP%] {\n  display: flex;\n  flex-wrap: wrap;\n  gap: 1rem;\n  margin-top: 1rem;\n}\n.image-preview[_ngcontent-%COMP%] {\n  position: relative;\n  width: 120px;\n  height: 120px;\n  border-radius: 8px;\n  overflow: hidden;\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\n}\n.image-preview.existing[_ngcontent-%COMP%] {\n  border: 2px solid #28a745;\n  opacity: 0.9;\n}\n.image-preview.new[_ngcontent-%COMP%] {\n  border: 2px solid #007bff;\n}\n.image-preview[_ngcontent-%COMP%]   img[_ngcontent-%COMP%] {\n  width: 100%;\n  height: 100%;\n  object-fit: cover;\n}\n.image-info[_ngcontent-%COMP%] {\n  position: absolute;\n  bottom: 0;\n  left: 0;\n  right: 0;\n  background: linear-gradient(transparent, rgba(0, 0, 0, 0.8));\n  color: white;\n  padding: 0.5rem 0.25rem 0.25rem;\n  font-size: 0.75rem;\n}\n.image-info[_ngcontent-%COMP%]   .image-name[_ngcontent-%COMP%] {\n  display: block;\n  white-space: nowrap;\n  overflow: hidden;\n  text-overflow: ellipsis;\n  font-weight: 500;\n}\n.image-info[_ngcontent-%COMP%]   .image-main[_ngcontent-%COMP%] {\n  color: #ffc107;\n  font-weight: bold;\n  font-size: 0.7rem;\n}\n.btn-remove[_ngcontent-%COMP%] {\n  position: absolute;\n  top: 0.25rem;\n  right: 0.25rem;\n  background: rgba(220, 53, 69, 0.9);\n  color: white;\n  border: none;\n  border-radius: 50%;\n  width: 24px;\n  height: 24px;\n  cursor: pointer;\n  font-size: 16px;\n  line-height: 1;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  transition: background-color 0.2s ease;\n}\n.btn-remove[_ngcontent-%COMP%]:hover {\n  background: rgb(220, 53, 69);\n}\n@media (max-width: 768px) {\n  .products-container[_ngcontent-%COMP%] {\n    padding: 1rem;\n  }\n  .products-header[_ngcontent-%COMP%] {\n    flex-direction: column;\n    gap: 1rem;\n    text-align: center;\n  }\n  .products-header[_ngcontent-%COMP%]   h1[_ngcontent-%COMP%] {\n    font-size: 1.5rem;\n  }\n  .stats-grid[_ngcontent-%COMP%] {\n    grid-template-columns: 1fr 1fr;\n  }\n  .products-grid[_ngcontent-%COMP%] {\n    grid-template-columns: 1fr;\n  }\n  .product-actions[_ngcontent-%COMP%] {\n    flex-direction: column;\n  }\n  .modal-content[_ngcontent-%COMP%] {\n    margin: 0.5rem;\n    max-height: 95vh;\n  }\n  .form-actions[_ngcontent-%COMP%] {\n    flex-direction: column;\n  }\n}\n.details-modal[_ngcontent-%COMP%] {\n  max-width: 900px;\n}\n.product-details-content[_ngcontent-%COMP%] {\n  padding: 1.5rem;\n}\n.details-grid[_ngcontent-%COMP%] {\n  display: grid;\n  grid-template-columns: 1fr 1fr;\n  gap: 2rem;\n  margin-bottom: 2rem;\n}\n@media (max-width: 768px) {\n  .details-grid[_ngcontent-%COMP%] {\n    grid-template-columns: 1fr;\n  }\n}\n.detail-section[_ngcontent-%COMP%] {\n  background: #f8f9fa;\n  padding: 1.5rem;\n  border-radius: 8px;\n  border: 1px solid #e9ecef;\n}\n.detail-section[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%] {\n  margin: 0 0 1rem 0;\n  color: #333;\n  font-size: 1.1rem;\n  font-weight: 600;\n  padding-bottom: 0.5rem;\n  border-bottom: 2px solid #007bff;\n}\n.detail-item[_ngcontent-%COMP%] {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  padding: 0.5rem 0;\n  border-bottom: 1px solid #e9ecef;\n}\n.detail-item[_ngcontent-%COMP%]:last-child {\n  border-bottom: none;\n}\n.detail-item[_ngcontent-%COMP%]   label[_ngcontent-%COMP%] {\n  font-weight: 600;\n  color: #555;\n  margin-right: 1rem;\n}\n.detail-item[_ngcontent-%COMP%]   span[_ngcontent-%COMP%] {\n  color: #333;\n  text-align: right;\n  flex: 1;\n}\n.promotion-price[_ngcontent-%COMP%] {\n  color: #dc3545;\n  font-weight: bold;\n}\n.images-gallery[_ngcontent-%COMP%] {\n  display: grid;\n  grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));\n  gap: 1rem;\n}\n.image-item[_ngcontent-%COMP%] {\n  position: relative;\n  border-radius: 8px;\n  overflow: hidden;\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\n}\n.image-item[_ngcontent-%COMP%]   img[_ngcontent-%COMP%] {\n  width: 100%;\n  height: 120px;\n  object-fit: cover;\n}\n.image-item[_ngcontent-%COMP%]   .main-badge[_ngcontent-%COMP%] {\n  position: absolute;\n  top: 0.25rem;\n  right: 0.25rem;\n  background: #ffc107;\n  color: #333;\n  padding: 0.125rem 0.5rem;\n  border-radius: 12px;\n  font-size: 0.7rem;\n  font-weight: bold;\n}\n.details-actions[_ngcontent-%COMP%] {\n  display: flex;\n  justify-content: flex-end;\n  gap: 1rem;\n  padding: 1.5rem;\n  border-top: 1px solid #e9ecef;\n  background: #f8f9fa;\n  margin: 0 -1.5rem -1.5rem -1.5rem;\n  border-radius: 0 0 12px 12px;\n}\n.page-header[_ngcontent-%COMP%] {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 32px;\n  padding: 24px;\n  background: white;\n  border-radius: 12px;\n  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);\n}\n.page-header[_ngcontent-%COMP%]   .header-content[_ngcontent-%COMP%]   h1[_ngcontent-%COMP%] {\n  margin: 0 0 8px 0;\n  font-size: 28px;\n  font-weight: 700;\n  color: #1f2937;\n}\n.page-header[_ngcontent-%COMP%]   .header-content[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\n  margin: 0;\n  color: #6b7280;\n  font-size: 16px;\n}\n.stats-section[_ngcontent-%COMP%] {\n  margin-bottom: 32px;\n}\n.stats-section[_ngcontent-%COMP%]   .stats-grid[_ngcontent-%COMP%] {\n  display: grid;\n  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));\n  gap: 20px;\n}\n.stats-section[_ngcontent-%COMP%]   .stat-card[_ngcontent-%COMP%] {\n  display: flex;\n  align-items: center;\n  gap: 16px;\n  padding: 20px;\n  background: white;\n  border-radius: 12px;\n  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);\n  transition: transform 0.2s ease;\n}\n.stats-section[_ngcontent-%COMP%]   .stat-card[_ngcontent-%COMP%]:hover {\n  transform: translateY(-2px);\n}\n.stats-section[_ngcontent-%COMP%]   .stat-card.alert[_ngcontent-%COMP%] {\n  background: #fef2f2;\n  border-left: 4px solid #ef4444;\n}\n.stats-section[_ngcontent-%COMP%]   .stat-card[_ngcontent-%COMP%]   .stat-icon[_ngcontent-%COMP%] {\n  font-size: 32px;\n  width: 48px;\n  height: 48px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  background: #f3f4f6;\n  border-radius: 50%;\n}\n.stats-section[_ngcontent-%COMP%]   .stat-card[_ngcontent-%COMP%]   .stat-value[_ngcontent-%COMP%] {\n  font-size: 24px;\n  font-weight: 700;\n  color: #1f2937;\n  margin-bottom: 4px;\n}\n.stats-section[_ngcontent-%COMP%]   .stat-card[_ngcontent-%COMP%]   .stat-label[_ngcontent-%COMP%] {\n  font-size: 14px;\n  color: #6b7280;\n  font-weight: 500;\n}\n.toolbar[_ngcontent-%COMP%] {\n  margin-bottom: 24px;\n  padding: 20px;\n  background: white;\n  border-radius: 12px;\n  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);\n}\n.toolbar[_ngcontent-%COMP%]   .search-section[_ngcontent-%COMP%] {\n  display: flex;\n  align-items: center;\n  gap: 24px;\n  flex-wrap: wrap;\n}\n.toolbar[_ngcontent-%COMP%]   .search-box[_ngcontent-%COMP%] {\n  position: relative;\n  flex: 1;\n  min-width: 300px;\n}\n.toolbar[_ngcontent-%COMP%]   .search-box[_ngcontent-%COMP%]   input[_ngcontent-%COMP%] {\n  width: 100%;\n  padding: 12px 16px 12px 40px;\n  border: 1px solid #d1d5db;\n  border-radius: 8px;\n  font-size: 14px;\n  background: #f9fafb;\n}\n.toolbar[_ngcontent-%COMP%]   .search-box[_ngcontent-%COMP%]   input[_ngcontent-%COMP%]:focus {\n  outline: none;\n  border-color: #3b82f6;\n  background: white;\n}\n.toolbar[_ngcontent-%COMP%]   .search-box[_ngcontent-%COMP%]   .search-icon[_ngcontent-%COMP%] {\n  position: absolute;\n  left: 12px;\n  top: 50%;\n  transform: translateY(-50%);\n  color: #6b7280;\n  font-size: 16px;\n}\n.toolbar[_ngcontent-%COMP%]   .filters[_ngcontent-%COMP%] {\n  display: flex;\n  gap: 16px;\n  flex-wrap: wrap;\n}\n.toolbar[_ngcontent-%COMP%]   .filters[_ngcontent-%COMP%]   .filter-checkbox[_ngcontent-%COMP%] {\n  display: flex;\n  align-items: center;\n  gap: 8px;\n  cursor: pointer;\n  font-size: 14px;\n  color: #374151;\n}\n.toolbar[_ngcontent-%COMP%]   .filters[_ngcontent-%COMP%]   .filter-checkbox[_ngcontent-%COMP%]   input[type=checkbox][_ngcontent-%COMP%] {\n  width: 16px;\n  height: 16px;\n  accent-color: #3b82f6;\n}\n.stock-empty[_ngcontent-%COMP%] {\n  color: #dc2626;\n  font-weight: 700;\n}\n.stock-low[_ngcontent-%COMP%] {\n  color: #f59e0b;\n  font-weight: 600;\n}\n.stock-medium[_ngcontent-%COMP%] {\n  color: #3b82f6;\n  font-weight: 500;\n}\n.stock-ok[_ngcontent-%COMP%] {\n  color: #059669;\n  font-weight: 500;\n}\n/*# sourceMappingURL=products.component.css.map */'] });
};
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && setClassMetadata(ProductsComponent, [{
    type: Component,
    args: [{ selector: "app-products", standalone: true, imports: [CommonModule, FormsModule], template: `<div class="products-container">
  <!-- Header -->
  <div class="products-header">
    <h1>\u{1F6CD}\uFE0F Gestion des Produits</h1>
    <div>
      <button class="btn btn-secondary" (click)="loadData()" style="margin-right: 10px;">
        \u{1F504} Recharger
      </button>
      <button class="btn btn-primary" (click)="openAddForm()">
        \u2795 Ajouter un produit
      </button>
    </div>
  </div>

  <!-- Statistiques -->
  <div class="stats-grid">
    <div class="stat-card">
      <div class="stat-number">{{ stats().total }}</div>
      <div class="stat-label">Total Produits</div>
    </div>
    <div class="stat-card">
      <div class="stat-number">{{ stats().active }}</div>
      <div class="stat-label">En Stock</div>
    </div>
    <div class="stat-card">
      <div class="stat-number">{{ stats().outOfStock }}</div>
      <div class="stat-label">Rupture</div>
    </div>
    <div class="stat-card">
      <div class="stat-number">{{ formatPrice(stats().totalValue) }}</div>
      <div class="stat-label">Valeur Totale</div>
    </div>
  </div>

  <!-- Recherche -->
  <div class="search-box">
    <input 
      type="text" 
      placeholder="Rechercher..." 
      [value]="searchQuery()"
      (input)="onSearch($event)"
      class="search-input"
    />
  </div>

  <!-- Message d'erreur -->
  <div *ngIf="error()" class="error-message" style="background: #fee; border: 1px solid #fcc; padding: 15px; margin: 20px 0; border-radius: 5px; color: #c00;">
    <h3>\u274C Erreur</h3>
    <p>{{ error() }}</p>
    <button class="btn btn-primary" (click)="loadData()">\u{1F504} R\xE9essayer</button>
  </div>

  <!-- En-t\xEAte avec statistiques -->
  <div class="page-header" *ngIf="!isLoading() && !error()">
    <div class="header-content">
      <h1>\u{1F4E6} Gestion des Produits</h1>
      <p>G\xE9rez votre catalogue de produits et suivez vos stocks</p>
    </div>

    <div class="header-actions">
      <button class="btn btn-primary" (click)="openAddForm()">
        \u2795 Nouveau Produit
      </button>
    </div>
  </div>

  <!-- Statistiques -->
  <div class="stats-section" *ngIf="products().length > 0 && !isLoading() && !error()">
    <div class="stats-grid">
      <div class="stat-card">
        <div class="stat-icon">\u{1F4E6}</div>
        <div class="stat-content">
          <div class="stat-value">{{ products().length }}</div>
          <div class="stat-label">Produits</div>
        </div>
      </div>

      <div class="stat-card">
        <div class="stat-icon">\u{1F4CA}</div>
        <div class="stat-content">
          <div class="stat-value">{{ getTotalStock() }}</div>
          <div class="stat-label">Unit\xE9s en stock</div>
        </div>
      </div>

      <div class="stat-card">
        <div class="stat-icon">\u{1F4B0}</div>
        <div class="stat-content">
          <div class="stat-value">{{ getTotalValue() | number:'1.0-0' }} DT</div>
          <div class="stat-label">Valeur du stock</div>
        </div>
      </div>

      <div class="stat-card alert" *ngIf="getLowStockCount() > 0">
        <div class="stat-icon">\u26A0\uFE0F</div>
        <div class="stat-content">
          <div class="stat-value">{{ getLowStockCount() }}</div>
          <div class="stat-label">Stock faible</div>
        </div>
      </div>
    </div>
  </div>

  <!-- Barre d'outils -->
  <div class="toolbar" *ngIf="!isLoading() && !error()">
    <div class="search-section">
      <div class="search-box">
        <input
          type="text"
          [(ngModel)]="searchQuery"
          placeholder="Rechercher un produit..."
          class="form-control"
        />
        <span class="search-icon">\u{1F50D}</span>
      </div>

      <div class="filters">
        <label class="filter-checkbox">
          <input type="checkbox" [(ngModel)]="filters.stockFaible" />
          <span>Stock faible</span>
        </label>
        <label class="filter-checkbox">
          <input type="checkbox" [(ngModel)]="filters.enPromotion" />
          <span>En promotion</span>
        </label>
        <label class="filter-checkbox">
          <input type="checkbox" [(ngModel)]="filters.misEnAvant" />
          <span>Mis en avant</span>
        </label>
      </div>
    </div>
  </div>

  <!-- Liste des produits -->
  <div class="products-section" *ngIf="!isLoading() && !error(); else loadingTemplate">
    <div class="table-container" *ngIf="currentPageProducts().length > 0">
      <table class="products-table">
        <thead>
          <tr>
            <th>Image</th>
            <th>Produit</th>
            <th>R\xE9f\xE9rence</th>
            <th>Prix</th>
            <th>Stock</th>
            <th>Cat\xE9gorie</th>
            <th>Statut</th>
            <th>Actions</th>
          </tr>
        </thead>
        <tbody>
          <tr *ngFor="let product of currentPageProducts()" class="product-row">
            <td class="image-cell">
              <img
                [src]="getMainProductImage(product)"
                [alt]="product.nom"
                class="product-image"
                (error)="onImageError($event)">
            </td>

            <td class="product-info">
              <div class="product-name">{{ product.nom }}</div>
              <div class="product-meta">
                <span class="date" *ngIf="product.dateAjout">Ajout\xE9 le {{ product.dateAjout | date:'dd/MM/yyyy' }}</span>
                <span *ngIf="hasPromotion(product)" class="featured-badge">\u{1F3F7}\uFE0F PROMO</span>
              </div>
              <div class="product-description" *ngIf="product.description">{{ product.description }}</div>
            </td>

            <td class="reference-cell">
              <div class="ref-original">{{ product.referenceOriginal }}</div>
              <div *ngIf="product.referenceFournisseur" class="ref-fournisseur">
                Fournisseur: {{ product.referenceFournisseur }}
              </div>
            </td>

            <td class="price-cell">
              <div class="price-container">
                <!-- Prix initial (barr\xE9 si promotion) -->
                <div class="price-initial" *ngIf="hasPromotion(product)">
                  <span class="price-label">Prix initial:</span>
                  <span class="price-value initial crossed">{{ formatPrice(product.prixVenteTTC) }}</span>
                </div>

                <!-- Prix final -->
                <div class="price-final">
                  <span class="price-label" *ngIf="hasPromotion(product)">Prix final:</span>
                  <span class="price-value final">{{ formatPrice(getFinalPrice(product)) }}</span>
                </div>

                <!-- Badge de remise -->
                <div class="discount-badge" *ngIf="hasPromotion(product) && product.pourcentageRemiseTotale">
                  -{{ product.pourcentageRemiseTotale }}%
                </div>
              </div>
            </td>

            <td class="stock-cell">
              <div class="stock-container">
                <span class="stock-value" [ngClass]="{'stock-critique': product.stock <= 10}">
                  {{ product.stock }}
                </span>
                <div class="stock-alert" *ngIf="product.stock <= 10">
                  \u26A0\uFE0F Stock critique
                </div>
              </div>
            </td>

            <td class="category-cell">
              <div class="category">{{ product.sousCategorie?.nom }}</div>
              <div class="brand" *ngIf="product.marque?.name">{{ product.marque?.name }}</div>
            </td>

            <td class="status-cell">
              <span class="status-badge" [class]="getStatusClass(product)">
                {{ getStatusText(product) }}
              </span>
            </td>

            <td class="actions-cell">
              <div class="action-buttons">
                <button
                  class="btn-action btn-view"
                  (click)="viewProductDetails(product)"
                  title="Voir les d\xE9tails">
                  \u{1F441}\uFE0F
                </button>

                <button
                  class="btn-action btn-edit"
                  (click)="editProduct(product)"
                  title="Modifier le produit">
                  \u270F\uFE0F
                </button>

                <button
                  class="btn-action btn-delete"
                  (click)="deleteProduct(product)"
                  title="Supprimer le produit">
                  \u{1F5D1}\uFE0F
                </button>
              </div>
            </td>
          </tr>
        </tbody>
      </table>
    </div>
  </div>

  <!-- Template de chargement -->
  <ng-template #loadingTemplate>
    <div class="loading">
      <div class="spinner"></div>
      <p>Chargement des produits...</p>
    </div>
  </ng-template>

  <!-- Message si aucun produit -->
  <div *ngIf="currentPageProducts().length === 0 && !isLoading()" class="no-products">
    <h3>Aucun produit trouv\xE9</h3>
    <p>Commencez par ajouter votre premier produit</p>
    <button class="btn btn-primary" (click)="openAddForm()">
      \u2795 Ajouter votre premier produit
    </button>
  </div>

  <!-- Modal des d\xE9tails du produit -->
  <div class="modal-overlay" *ngIf="showDetails()" (click)="closeDetails()">
    <div class="modal-content details-modal" (click)="$event.stopPropagation()">
      <div class="modal-header">
        <h3>D\xE9tails du produit</h3>
        <button type="button" class="btn btn-sm btn-outline-secondary" (click)="closeDetails()">
          \u2715 Fermer
        </button>
      </div>

      <div class="product-details-content" *ngIf="selectedProductDetails()">
        <div class="details-grid">
          <!-- Informations de base -->
          <div class="detail-section">
            <h4>Informations g\xE9n\xE9rales</h4>
            <div class="detail-item">
              <label>Nom :</label>
              <span>{{ selectedProductDetails()!.nom }}</span>
            </div>
            <div class="detail-item">
              <label>Description :</label>
              <span>{{ selectedProductDetails()!.description }}</span>
            </div>
            <div class="detail-item">
              <label>R\xE9f\xE9rence originale :</label>
              <span>{{ selectedProductDetails()!.referenceOriginal }}</span>
            </div>
            <div class="detail-item" *ngIf="selectedProductDetails()!.referenceFournisseur">
              <label>R\xE9f\xE9rence fournisseur :</label>
              <span>{{ selectedProductDetails()!.referenceFournisseur }}</span>
            </div>
            <div class="detail-item">
              <label>Code \xE0 barres :</label>
              <span>{{ selectedProductDetails()!.codeABarre }}</span>
            </div>
          </div>

          <!-- Prix et stock -->
          <div class="detail-section">
            <h4>Prix et stock</h4>
            <div class="detail-item">
              <label>Prix d'achat HT :</label>
              <span>{{ formatPrice(selectedProductDetails()!.prixAchatHT) }}</span>
            </div>
            <div class="detail-item">
              <label>Prix de vente HT :</label>
              <span>{{ formatPrice(selectedProductDetails()!.prixVenteHT) }}</span>
            </div>
            <div class="detail-item">
              <label>Prix de vente TTC :</label>
              <span>{{ formatPrice(selectedProductDetails()!.prixVenteTTC) }}</span>
            </div>
            <div class="detail-item" *ngIf="selectedProductDetails()!.prixApresRemisesOutlet && selectedProductDetails()!.prixApresRemisesOutlet !== selectedProductDetails()!.prixVenteTTC">
              <label>Prix apr\xE8s remise outlet :</label>
              <span class="outlet-price">{{ formatPrice(selectedProductDetails()!.prixApresRemisesOutlet!) }}</span>
            </div>
            <div class="detail-item" *ngIf="hasPromotion(selectedProductDetails()!)">
              <label>Prix final apr\xE8s toutes promotions :</label>
              <span class="promotion-price">{{ formatPrice(selectedProductDetails()!.prixApresRemises!) }}</span>
            </div>
            <div class="detail-item">
              <label>Stock :</label>
              <span [class]="getStatusClass(selectedProductDetails()!)">{{ selectedProductDetails()!.stock }} unit\xE9s</span>
            </div>
            <div class="detail-item" *ngIf="selectedProductDetails()?.pourcentageRemiseTotale && selectedProductDetails()!.pourcentageRemiseTotale! > 0">
              <label>\xC9conomie totale :</label>
              <span class="savings-badge">-{{ selectedProductDetails()!.pourcentageRemiseTotale }}%</span>
              <small class="text-muted">
                ({{ formatPrice((selectedProductDetails()!.prixVenteTTC || 0) - (selectedProductDetails()!.prixApresRemises || 0)) }} d'\xE9conomie)
              </small>
            </div>
          </div>

          <!-- Cat\xE9gories -->
          <div class="detail-section">
            <h4>Classifications</h4>
            <div class="detail-item" *ngIf="selectedProductDetails()!.sousCategorie">
              <label>Sous-cat\xE9gorie :</label>
              <span>{{ selectedProductDetails()!.sousCategorie!.nom }}</span>
            </div>
            <div class="detail-item" *ngIf="selectedProductDetails()!.marque">
              <label>Marque :</label>
              <span>{{ selectedProductDetails()!.marque!.name }}</span>
            </div>
            <div class="detail-item" *ngIf="selectedProductDetails()!.forme">
              <label>Forme :</label>
              <span>{{ selectedProductDetails()!.forme!.nom }}</span>
            </div>
            <div class="detail-item" *ngIf="selectedProductDetails()!.tauxTVA">
              <label>Taux TVA :</label>
              <span>{{ selectedProductDetails()!.tauxTVA!.libelle }} ({{ selectedProductDetails()!.tauxTVA!.taux }}%)</span>
            </div>
          </div>

          <!-- Images -->
          <div class="detail-section" *ngIf="selectedProductDetails()!.images && selectedProductDetails()!.images!.length > 0">
            <h4>Images</h4>
            <div class="images-gallery">
              <div class="image-item" *ngFor="let image of selectedProductDetails()!.images">
                <img
                  [src]="imageUrlService.getProduitImageUrl(image.imageUrl)"
                  [alt]="image.altText || 'Image produit'"
                />
                <span *ngIf="image.isMain" class="main-badge">Principale</span>
              </div>
            </div>
          </div>
        </div>

        <div class="details-actions">
          <button class="btn btn-primary" (click)="editProduct(selectedProductDetails()!); closeDetails()">
            \u270F\uFE0F Modifier ce produit
          </button>
          <button class="btn btn-secondary" (click)="closeDetails()">
            Fermer
          </button>
        </div>
      </div>
    </div>
  </div>

  <!-- Modal du formulaire -->
  <div class="modal-overlay" *ngIf="showForm()" (click)="closeForm()">
    <div class="modal-content" (click)="$event.stopPropagation()">
      <div class="modal-header">
        <h3>{{ isEditMode() ? 'Modifier' : 'Ajouter' }} un produit</h3>
        <div class="modal-actions">
          <button type="button" class="btn btn-sm btn-outline-secondary" (click)="refreshDropdowns()" title="Recharger les listes">
            \u{1F504} Actualiser
          </button>
          <button type="button" class="btn btn-sm btn-outline-secondary" (click)="closeForm()">
            \u2715 Fermer
          </button>
        </div>
      </div>
      
      <form #productForm="ngForm" name="productForm" (ngSubmit)="onSubmit(productForm)" class="product-form">
        <!-- Informations de base -->
        <div class="form-section">
          <h4>Informations de base</h4>
          
          <div class="form-row">
            <div class="form-group">
              <label>R\xE9f\xE9rence originale *</label>
              <input
                type="text"
                name="referenceOriginal"
                [(ngModel)]="formData.referenceOriginal"
                required
                class="form-control"
                placeholder="REF-001"
              />
            </div>
            <div class="form-group">
              <label>R\xE9f\xE9rence fournisseur</label>
              <input
                type="text"
                name="referenceFournisseur"
                [(ngModel)]="formData.referenceFournisseur"
                class="form-control"
                placeholder="FOUR-001"
              />
            </div>
          </div>

          <div class="form-row">
            <div class="form-group">
              <label>Nom du produit *</label>
              <input
                type="text"
                name="nom"
                [(ngModel)]="formData.nom"
                required
                class="form-control"
                placeholder="Nom du produit"
              />
            </div>
            <div class="form-group">
              <label>Code \xE0 barres</label>
              <input
                type="text"
                name="codeABarre"
                [(ngModel)]="formData.codeABarre"
                class="form-control"
                placeholder="G\xE9n\xE9r\xE9 automatiquement"
              />
            </div>
          </div>

          <div class="form-group">
            <label>Description *</label>
            <textarea
              name="description"
              [(ngModel)]="formData.description"
              required
              class="form-control"
              rows="3"
              placeholder="Description du produit"
            ></textarea>
          </div>
        </div>

        <!-- Prix et stock -->
        <div class="form-section">
          <h4>Prix et stock</h4>
          
          <div class="form-row">
            <div class="form-group">
              <label>Prix d'achat HT (DT) *</label>
              <input
                type="number"
                name="prixAchat"
                [(ngModel)]="formData.prixAchat"
                required
                min="0"
                step="0.01"
                class="form-control"
                (input)="onPrixChange()"
              />
            </div>
            <div class="form-group">
              <label>Prix de vente HT (DT) *</label>
              <input
                type="number"
                name="prixVente"
                [(ngModel)]="formData.prixVente"
                required
                min="0"
                step="0.01"
                class="form-control"
                (input)="onPrixChange()"
              />
            </div>
          </div>

          <!-- Affichage du prix TTC calcul\xE9 -->
          <div class="form-group" *ngIf="calculatedPrixTTC > 0">
            <div class="price-info">
              <strong>Prix de vente TTC calcul\xE9 : {{ formatPrice(calculatedPrixTTC) }}</strong>
              <small class="text-muted">(TVA {{ selectedTauxTVA }}% incluse)</small>
            </div>
          </div>

          <div class="form-row">
            <div class="form-group">
              <label>Stock initial</label>
              <input
                type="number"
                name="stock"
                [(ngModel)]="formData.stock"
                min="0"
                class="form-control"
                placeholder="0"
              />
            </div>
            <div class="form-group">
              <label>Pourcentage de remise outlet (%)</label>
              <input
                type="number"
                name="pourcentageRemise"
                [(ngModel)]="formData.pourcentageRemise"
                min="0"
                max="100"
                step="1"
                class="form-control"
                placeholder="30"
                title="Remise outlet appliqu\xE9e automatiquement (d\xE9faut: 30%)"
              />
              <small class="form-text text-muted">
                \u{1F4A1} Remise outlet appliqu\xE9e automatiquement sur ce produit (d\xE9faut: 30%)
              </small>
            </div>
          </div>
        </div>

        <!-- Cat\xE9gories et classifications -->
        <div class="form-section">
          <h4>Cat\xE9gories et classifications</h4>
          
          <div class="form-row">
            <div class="form-group">
              <label>Cat\xE9gorie *</label>
              <div class="input-with-button">
                <select name="categorieId" [(ngModel)]="formData.categorieId" required class="form-control" (change)="onCategorieChange($event)">
                  <option value="">S\xE9lectionner</option>
                  <option *ngFor="let cat of categoriesDropdown()" [value]="cat.id">
                    {{ cat.nom }}
                  </option>
                </select>
              </div>
            </div>
            <div class="form-group">
              <label>Sous-cat\xE9gorie * ({{ sousCategoriesDropdown().length }} disponibles)</label>
              <div class="input-with-button">
                <select name="sousCategorieId" [(ngModel)]="formData.sousCategorieId" required class="form-control">
                  <option value="">S\xE9lectionner une sous-cat\xE9gorie</option>
                  <option *ngFor="let subCat of sousCategoriesDropdown(); trackBy: trackBySousCategorieId" [value]="subCat.id">
                    {{ subCat.nom }}
                  </option>
                </select>
                <!-- Debug info -->
                <small class="debug-info" style="color: #666; font-size: 12px;">
                  Debug: {{ sousCategoriesDropdown().length }} sous-cat\xE9gories charg\xE9es
                </small>
              </div>
            </div>
          </div>

          <div class="form-row">
            <div class="form-group">
              <label>Marque *</label>
              <div class="input-with-button">
                <select name="marqueId" [(ngModel)]="formData.marqueId" required class="form-control">
                  <option value="">S\xE9lectionner</option>
                  <option *ngFor="let marque of marquesDropdown()" [value]="marque.id">
                    {{ marque.name }}
                  </option>
                </select>
              </div>
            </div>
            <div class="form-group">
              <label>Forme *</label>
              <div class="input-with-button">
                <select name="formeId" [(ngModel)]="formData.formeId" required class="form-control">
                  <option value="">S\xE9lectionner</option>
                  <option *ngFor="let forme of formesDropdown()" [value]="forme.id">
                    {{ forme.nom }}
                  </option>
                </select>
              </div>
            </div>
          </div>

          <div class="form-row">
            <div class="form-group">
              <label>Taux TVA *</label>
              <div class="input-with-button">
                <select name="tauxTVAId" [(ngModel)]="formData.tauxTVAId" required class="form-control" (change)="onTauxTVAChange($event)">
                  <option value="">S\xE9lectionner</option>
                  <option *ngFor="let taux of tauxTVADropdown()" [value]="taux.id">
                    {{ taux.libelle }} ({{ taux.taux }}%)
                  </option>
                </select>
              </div>
            </div>
          </div>
        </div>

        <!-- Images du produit -->
        <div class="form-section">
          <h4>Images du produit</h4>

          <!-- Images existantes (en mode \xE9dition) -->
          <div *ngIf="isEditMode() && selectedProduct()?.images?.length" class="existing-images">
            <h6>Images actuelles :</h6>
            <div class="image-preview-container">
              <div class="image-preview existing" *ngFor="let image of selectedProduct()!.images; let i = index">
                <img
                  [src]="imageUrlService.getProduitImageUrl(image.imageUrl)"
                  [alt]="image.altText || 'Image produit'"
                />
                <div class="image-info">
                  <span class="image-name">Image {{ i + 1 }}</span>
                  <span class="image-main" *ngIf="image.isMain">(Principale)</span>
                </div>
              </div>
            </div>
            <small class="form-text text-muted">
              Les nouvelles images s\xE9lectionn\xE9es ci-dessous remplaceront les images existantes.
            </small>
          </div>

          <!-- S\xE9lection d'images une par une -->
          <div class="form-group">
            <label>{{ isEditMode() ? 'Nouvelles images' : 'Images du produit' }}</label>

            <!-- Input file cach\xE9 -->
            <input
              #fileInput
              type="file"
              accept="image/*"
              style="display: none;"
              (change)="onSingleImageSelected($event)"
            />

            <!-- Bouton pour ajouter la premi\xE8re image ou d'autres images -->
            <div class="image-upload-actions">
              <button
                type="button"
                class="btn btn-outline-primary"
                (click)="fileInput.click()"
                [disabled]="selectedImageFiles.length >= 5"
              >
                <span *ngIf="selectedImageFiles.length === 0">\u{1F4F7} Choisir une image</span>
                <span *ngIf="selectedImageFiles.length > 0">\u2795 Ajouter autre image</span>
              </button>

              <small class="form-text text-muted" style="margin-left: 10px;">
                {{ selectedImageFiles.length }}/5 images s\xE9lectionn\xE9es
                <span *ngIf="selectedImageFiles.length >= 5"> (Maximum atteint)</span>
              </small>
            </div>

            <small class="form-text text-muted" style="margin-top: 5px;">
              {{ isEditMode() ? 'Ajoutez de nouvelles images pour remplacer les existantes.' : 'La premi\xE8re image sera l\\'image principale.' }}
            </small>

            <!-- Pr\xE9visualisation des images s\xE9lectionn\xE9es -->
            <div class="image-preview-container" *ngIf="selectedImageFiles.length > 0" style="margin-top: 15px;">
              <div class="image-preview new" *ngFor="let file of selectedImageFiles; let i = index">
                <img [src]="getImagePreview(file)" [alt]="file.name" />
                <div class="image-info">
                  <span class="image-name">{{ file.name }}</span>
                  <span class="image-main" *ngIf="i === 0">({{ isEditMode() ? 'Nouvelle image principale' : 'Image principale' }})</span>
                  <button type="button" class="btn-remove" (click)="removeImageFile(i)" title="Supprimer cette image">\xD7</button>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Actions -->
        <div class="form-actions">
          <button type="button" class="btn btn-secondary" (click)="closeForm()">
            Annuler
          </button>
          <button type="submit" class="btn btn-primary" [disabled]="!productForm.valid || isLoading()">
            {{ isEditMode() ? 'Modifier' : 'Ajouter' }}
          </button>
        </div>
      </form>
    </div>
  </div>
</div>
`, styles: ['@charset "UTF-8";\n\n/* src/app/components/products/products.component.scss */\n.products-container {\n  padding: 2rem;\n  max-width: 1200px;\n  margin: 0 auto;\n  background: #f8f9fa;\n  min-height: 100vh;\n}\n.products-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 2rem;\n  background: white;\n  padding: 1.5rem;\n  border-radius: 12px;\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\n}\n.products-header h1 {\n  margin: 0;\n  color: #333;\n  font-size: 2rem;\n  font-weight: 600;\n}\n.stats-grid {\n  display: grid;\n  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));\n  gap: 1rem;\n  margin-bottom: 2rem;\n}\n.stat-card {\n  background: white;\n  padding: 1.5rem;\n  border-radius: 12px;\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\n  text-align: center;\n  transition: transform 0.2s ease;\n}\n.stat-card:hover {\n  transform: translateY(-2px);\n}\n.stat-number {\n  font-size: 2.5rem;\n  font-weight: bold;\n  color: #007bff;\n  margin-bottom: 0.5rem;\n}\n.stat-label {\n  color: #666;\n  font-size: 0.9rem;\n  text-transform: uppercase;\n  letter-spacing: 0.5px;\n}\n.search-box {\n  margin-bottom: 2rem;\n}\n.search-input {\n  width: 100%;\n  max-width: 400px;\n  padding: 0.75rem 1rem;\n  border: 2px solid #e9ecef;\n  border-radius: 8px;\n  font-size: 1rem;\n  transition: border-color 0.2s ease;\n}\n.search-input:focus {\n  outline: none;\n  border-color: #007bff;\n  box-shadow: 0 0 0 3px rgba(0, 123, 255, 0.1);\n}\n.products-grid {\n  display: grid;\n  grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));\n  gap: 1.5rem;\n  margin-bottom: 2rem;\n}\n.product-card {\n  background: white;\n  border-radius: 12px;\n  padding: 1.5rem;\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\n  transition: all 0.3s ease;\n  border: 1px solid #e9ecef;\n  cursor: pointer;\n}\n.product-card:hover {\n  transform: translateY(-4px);\n  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);\n}\n.product-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: flex-start;\n  margin-bottom: 1rem;\n}\n.product-header h3 {\n  margin: 0;\n  color: #333;\n  font-size: 1.25rem;\n  font-weight: 600;\n}\n.product-status {\n  padding: 0.25rem 0.75rem;\n  border-radius: 20px;\n  font-size: 0.75rem;\n  font-weight: 600;\n  text-transform: uppercase;\n  letter-spacing: 0.5px;\n}\n.product-status.status-in-stock {\n  background: #d4edda;\n  color: #155724;\n  border: 1px solid #c3e6cb;\n}\n.product-status.status-low-stock {\n  background: #fff3cd;\n  color: #856404;\n  border: 1px solid #ffeaa7;\n}\n.product-status.status-out-of-stock {\n  background: #f8d7da;\n  color: #721c24;\n  border: 1px solid #f5c6cb;\n}\n.product-description {\n  color: #666;\n  margin-bottom: 1rem;\n  line-height: 1.5;\n  display: -webkit-box;\n  -webkit-line-clamp: 2;\n  -webkit-box-orient: vertical;\n  overflow: hidden;\n}\n.product-details {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 1rem;\n  padding: 0.75rem;\n  background: #f8f9fa;\n  border-radius: 8px;\n}\n.price-container {\n  display: flex;\n  flex-direction: column;\n  gap: 0.25rem;\n  margin-bottom: 0.75rem;\n}\n.price-container .price-initial,\n.price-container .price-final {\n  display: flex;\n  align-items: center;\n  gap: 0.5rem;\n}\n.price-container .price-initial .price-label,\n.price-container .price-final .price-label {\n  font-size: 0.75rem;\n  color: #6c757d;\n  font-weight: 500;\n}\n.price-container .price-initial .price-value,\n.price-container .price-final .price-value {\n  font-weight: bold;\n}\n.price-container .price-initial .price-value.initial,\n.price-container .price-final .price-value.initial {\n  color: #6c757d;\n}\n.price-container .price-initial .price-value.initial.crossed,\n.price-container .price-final .price-value.initial.crossed {\n  text-decoration: line-through;\n}\n.price-container .price-initial .price-value.final,\n.price-container .price-final .price-value.final {\n  color: #007bff;\n  font-size: 1.1rem;\n}\n.price-container .discount-badge {\n  background:\n    linear-gradient(\n      135deg,\n      #dc3545,\n      #c82333);\n  color: white;\n  padding: 0.25rem 0.5rem;\n  border-radius: 12px;\n  font-size: 0.75rem;\n  font-weight: bold;\n  align-self: flex-start;\n  box-shadow: 0 2px 4px rgba(220, 53, 69, 0.3);\n}\n.stock-container {\n  display: flex;\n  flex-direction: column;\n  gap: 0.25rem;\n}\n.stock-container .stock-value {\n  font-size: 0.875rem;\n  color: #28a745;\n  font-weight: 500;\n}\n.stock-container .stock-value.stock-critique {\n  color: #dc3545;\n  font-weight: bold;\n}\n.stock-container .stock-alert {\n  font-size: 0.75rem;\n  color: #dc3545;\n  font-weight: bold;\n  background: rgba(220, 53, 69, 0.1);\n  padding: 0.125rem 0.25rem;\n  border-radius: 4px;\n}\n.product-meta {\n  display: flex;\n  flex-direction: column;\n  gap: 0.25rem;\n  margin-bottom: 0.5rem;\n}\n.product-meta .product-category {\n  font-size: 0.875rem;\n  color: #007bff;\n  font-weight: 500;\n}\n.product-meta .product-brand {\n  font-size: 0.75rem;\n  color: #6c757d;\n  font-style: italic;\n}\n.product-meta .product-reference {\n  font-size: 0.75rem;\n  color: #6c757d;\n}\n.product-meta .product-reference .ref-original {\n  font-weight: 500;\n}\n.product-meta .product-reference .ref-fournisseur {\n  color: #28a745;\n  margin-left: 0.25rem;\n}\n.featured-badge {\n  position: absolute;\n  top: 0.5rem;\n  right: 0.5rem;\n  background:\n    linear-gradient(\n      135deg,\n      #ffc107,\n      #e0a800);\n  color: #212529;\n  padding: 0.25rem 0.5rem;\n  border-radius: 12px;\n  font-size: 0.75rem;\n  font-weight: bold;\n  box-shadow: 0 2px 4px rgba(255, 193, 7, 0.3);\n}\n.promotion-badge {\n  background: #dc3545;\n  color: white;\n  padding: 0.125rem 0.5rem;\n  border-radius: 12px;\n  font-size: 0.7rem;\n  font-weight: bold;\n  text-transform: uppercase;\n}\n.product-stock {\n  color: #666;\n  font-weight: 500;\n}\n.product-actions {\n  display: flex;\n  gap: 0.5rem;\n  justify-content: flex-end;\n}\n.btn {\n  padding: 0.5rem 1rem;\n  border: none;\n  border-radius: 6px;\n  cursor: pointer;\n  font-size: 0.875rem;\n  font-weight: 500;\n  transition: all 0.2s ease;\n  text-decoration: none;\n  display: inline-flex;\n  align-items: center;\n  gap: 0.25rem;\n}\n.btn:hover {\n  transform: translateY(-1px);\n}\n.btn:active {\n  transform: translateY(0);\n}\n.btn:disabled {\n  opacity: 0.6;\n  cursor: not-allowed;\n  transform: none;\n}\n.btn-primary {\n  background:\n    linear-gradient(\n      135deg,\n      #007bff,\n      #0056b3);\n  color: white;\n  box-shadow: 0 2px 4px rgba(0, 123, 255, 0.3);\n}\n.btn-primary:hover {\n  background:\n    linear-gradient(\n      135deg,\n      #0056b3,\n      #004085);\n  box-shadow: 0 4px 8px rgba(0, 123, 255, 0.4);\n}\n.btn-secondary {\n  background: #6c757d;\n  color: white;\n  box-shadow: 0 2px 4px rgba(108, 117, 125, 0.3);\n}\n.btn-secondary:hover {\n  background: #545b62;\n  box-shadow: 0 4px 8px rgba(108, 117, 125, 0.4);\n}\n.btn-danger {\n  background:\n    linear-gradient(\n      135deg,\n      #dc3545,\n      #c82333);\n  color: white;\n  box-shadow: 0 2px 4px rgba(220, 53, 69, 0.3);\n}\n.btn-danger:hover {\n  background:\n    linear-gradient(\n      135deg,\n      #c82333,\n      #a71e2a);\n  box-shadow: 0 4px 8px rgba(220, 53, 69, 0.4);\n}\n.btn-outline-primary {\n  background: transparent;\n  color: #007bff;\n  border: 2px solid #007bff;\n}\n.btn-outline-primary:hover {\n  background: #007bff;\n  color: white;\n}\n.btn-outline-secondary {\n  background: transparent;\n  color: #6c757d;\n  border: 2px solid #6c757d;\n}\n.btn-outline-secondary:hover {\n  background: #6c757d;\n  color: white;\n}\n.btn-sm {\n  padding: 0.25rem 0.5rem;\n  font-size: 0.75rem;\n}\n.btn-info {\n  background:\n    linear-gradient(\n      135deg,\n      #17a2b8,\n      #138496);\n  color: white;\n  box-shadow: 0 2px 4px rgba(23, 162, 184, 0.3);\n}\n.btn-info:hover {\n  background:\n    linear-gradient(\n      135deg,\n      #138496,\n      #117a8b);\n  box-shadow: 0 4px 8px rgba(23, 162, 184, 0.4);\n}\n.loading {\n  text-align: center;\n  padding: 4rem 2rem;\n  color: #666;\n}\n.loading .spinner {\n  width: 40px;\n  height: 40px;\n  border: 4px solid #f3f3f3;\n  border-top: 4px solid #007bff;\n  border-radius: 50%;\n  animation: spin 1s linear infinite;\n  margin: 0 auto 1rem;\n}\n.loading p {\n  font-size: 1.1rem;\n  margin: 0;\n}\n@keyframes spin {\n  0% {\n    transform: rotate(0deg);\n  }\n  100% {\n    transform: rotate(360deg);\n  }\n}\n.no-products {\n  text-align: center;\n  padding: 4rem 2rem;\n  background: white;\n  border-radius: 12px;\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\n}\n.no-products h3 {\n  color: #333;\n  margin-bottom: 1rem;\n  font-size: 1.5rem;\n}\n.no-products p {\n  color: #666;\n  margin-bottom: 2rem;\n  font-size: 1.1rem;\n}\n.modal-overlay {\n  position: fixed;\n  top: 0;\n  left: 0;\n  right: 0;\n  bottom: 0;\n  background: rgba(0, 0, 0, 0.5);\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  z-index: 1000;\n  padding: 1rem;\n}\n.modal-content {\n  background: white;\n  border-radius: 12px;\n  width: 100%;\n  max-width: 800px;\n  max-height: 90vh;\n  overflow-y: auto;\n  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);\n}\n.modal-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  padding: 1.5rem;\n  border-bottom: 1px solid #e9ecef;\n}\n.modal-header h3 {\n  margin: 0;\n  color: #333;\n  font-size: 1.5rem;\n}\n.modal-actions {\n  display: flex;\n  gap: 0.5rem;\n}\n.product-form {\n  padding: 1.5rem;\n}\n.form-section {\n  margin-bottom: 2rem;\n  padding: 1.5rem;\n  background: #f8f9fa;\n  border-radius: 8px;\n  border: 1px solid #e9ecef;\n}\n.form-section h4 {\n  margin: 0 0 1rem 0;\n  color: #333;\n  font-size: 1.2rem;\n  font-weight: 600;\n  padding-bottom: 0.5rem;\n  border-bottom: 2px solid #007bff;\n}\n.form-row {\n  display: grid;\n  grid-template-columns: 1fr 1fr;\n  gap: 1rem;\n  margin-bottom: 1rem;\n}\n@media (max-width: 768px) {\n  .form-row {\n    grid-template-columns: 1fr;\n  }\n}\n.form-group {\n  margin-bottom: 1rem;\n}\n.form-group label {\n  display: block;\n  margin-bottom: 0.5rem;\n  color: #333;\n  font-weight: 500;\n  font-size: 0.9rem;\n}\n.form-control {\n  width: 100%;\n  padding: 0.75rem;\n  border: 2px solid #e9ecef;\n  border-radius: 6px;\n  font-size: 1rem;\n  transition: border-color 0.2s ease;\n}\n.form-control:focus {\n  outline: none;\n  border-color: #007bff;\n  box-shadow: 0 0 0 3px rgba(0, 123, 255, 0.1);\n}\n.form-control.ng-invalid.ng-touched {\n  border-color: #dc3545;\n}\n.input-with-button {\n  display: flex;\n  gap: 0.5rem;\n}\n.input-with-button .form-control {\n  flex: 1;\n}\n.input-with-button .btn {\n  flex-shrink: 0;\n}\n.form-text {\n  font-size: 0.875rem;\n  color: #6c757d;\n  margin-top: 0.25rem;\n}\n.text-muted {\n  color: #6c757d;\n}\n.price-info {\n  background: #e7f3ff;\n  border: 1px solid #b3d9ff;\n  border-radius: 6px;\n  padding: 1rem;\n  margin-top: 0.5rem;\n}\n.price-info strong {\n  color: #0056b3;\n  font-size: 1.1rem;\n}\n.price-info small {\n  display: block;\n  margin-top: 0.25rem;\n}\n.form-actions {\n  display: flex;\n  justify-content: flex-end;\n  gap: 1rem;\n  padding: 1.5rem;\n  border-top: 1px solid #e9ecef;\n  background: #f8f9fa;\n  margin: 0 -1.5rem -1.5rem -1.5rem;\n  border-radius: 0 0 12px 12px;\n}\n.existing-images {\n  margin-bottom: 1.5rem;\n  padding: 1rem;\n  background: #e8f5e8;\n  border-radius: 8px;\n  border: 1px solid #c3e6cb;\n}\n.existing-images h6 {\n  margin: 0 0 0.75rem 0;\n  color: #155724;\n  font-weight: 600;\n}\n.image-upload-actions {\n  display: flex;\n  align-items: center;\n  gap: 10px;\n  margin-bottom: 10px;\n}\n.image-upload-actions .btn {\n  display: flex;\n  align-items: center;\n  gap: 5px;\n}\n.image-upload-actions .btn:disabled {\n  opacity: 0.6;\n  cursor: not-allowed;\n}\n.image-upload-actions .form-text {\n  margin: 0;\n  font-size: 0.875rem;\n}\n.image-preview-container {\n  display: flex;\n  flex-wrap: wrap;\n  gap: 1rem;\n  margin-top: 1rem;\n}\n.image-preview {\n  position: relative;\n  width: 120px;\n  height: 120px;\n  border-radius: 8px;\n  overflow: hidden;\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\n}\n.image-preview.existing {\n  border: 2px solid #28a745;\n  opacity: 0.9;\n}\n.image-preview.new {\n  border: 2px solid #007bff;\n}\n.image-preview img {\n  width: 100%;\n  height: 100%;\n  object-fit: cover;\n}\n.image-info {\n  position: absolute;\n  bottom: 0;\n  left: 0;\n  right: 0;\n  background: linear-gradient(transparent, rgba(0, 0, 0, 0.8));\n  color: white;\n  padding: 0.5rem 0.25rem 0.25rem;\n  font-size: 0.75rem;\n}\n.image-info .image-name {\n  display: block;\n  white-space: nowrap;\n  overflow: hidden;\n  text-overflow: ellipsis;\n  font-weight: 500;\n}\n.image-info .image-main {\n  color: #ffc107;\n  font-weight: bold;\n  font-size: 0.7rem;\n}\n.btn-remove {\n  position: absolute;\n  top: 0.25rem;\n  right: 0.25rem;\n  background: rgba(220, 53, 69, 0.9);\n  color: white;\n  border: none;\n  border-radius: 50%;\n  width: 24px;\n  height: 24px;\n  cursor: pointer;\n  font-size: 16px;\n  line-height: 1;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  transition: background-color 0.2s ease;\n}\n.btn-remove:hover {\n  background: rgb(220, 53, 69);\n}\n@media (max-width: 768px) {\n  .products-container {\n    padding: 1rem;\n  }\n  .products-header {\n    flex-direction: column;\n    gap: 1rem;\n    text-align: center;\n  }\n  .products-header h1 {\n    font-size: 1.5rem;\n  }\n  .stats-grid {\n    grid-template-columns: 1fr 1fr;\n  }\n  .products-grid {\n    grid-template-columns: 1fr;\n  }\n  .product-actions {\n    flex-direction: column;\n  }\n  .modal-content {\n    margin: 0.5rem;\n    max-height: 95vh;\n  }\n  .form-actions {\n    flex-direction: column;\n  }\n}\n.details-modal {\n  max-width: 900px;\n}\n.product-details-content {\n  padding: 1.5rem;\n}\n.details-grid {\n  display: grid;\n  grid-template-columns: 1fr 1fr;\n  gap: 2rem;\n  margin-bottom: 2rem;\n}\n@media (max-width: 768px) {\n  .details-grid {\n    grid-template-columns: 1fr;\n  }\n}\n.detail-section {\n  background: #f8f9fa;\n  padding: 1.5rem;\n  border-radius: 8px;\n  border: 1px solid #e9ecef;\n}\n.detail-section h4 {\n  margin: 0 0 1rem 0;\n  color: #333;\n  font-size: 1.1rem;\n  font-weight: 600;\n  padding-bottom: 0.5rem;\n  border-bottom: 2px solid #007bff;\n}\n.detail-item {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  padding: 0.5rem 0;\n  border-bottom: 1px solid #e9ecef;\n}\n.detail-item:last-child {\n  border-bottom: none;\n}\n.detail-item label {\n  font-weight: 600;\n  color: #555;\n  margin-right: 1rem;\n}\n.detail-item span {\n  color: #333;\n  text-align: right;\n  flex: 1;\n}\n.promotion-price {\n  color: #dc3545;\n  font-weight: bold;\n}\n.images-gallery {\n  display: grid;\n  grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));\n  gap: 1rem;\n}\n.image-item {\n  position: relative;\n  border-radius: 8px;\n  overflow: hidden;\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\n}\n.image-item img {\n  width: 100%;\n  height: 120px;\n  object-fit: cover;\n}\n.image-item .main-badge {\n  position: absolute;\n  top: 0.25rem;\n  right: 0.25rem;\n  background: #ffc107;\n  color: #333;\n  padding: 0.125rem 0.5rem;\n  border-radius: 12px;\n  font-size: 0.7rem;\n  font-weight: bold;\n}\n.details-actions {\n  display: flex;\n  justify-content: flex-end;\n  gap: 1rem;\n  padding: 1.5rem;\n  border-top: 1px solid #e9ecef;\n  background: #f8f9fa;\n  margin: 0 -1.5rem -1.5rem -1.5rem;\n  border-radius: 0 0 12px 12px;\n}\n.page-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 32px;\n  padding: 24px;\n  background: white;\n  border-radius: 12px;\n  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);\n}\n.page-header .header-content h1 {\n  margin: 0 0 8px 0;\n  font-size: 28px;\n  font-weight: 700;\n  color: #1f2937;\n}\n.page-header .header-content p {\n  margin: 0;\n  color: #6b7280;\n  font-size: 16px;\n}\n.stats-section {\n  margin-bottom: 32px;\n}\n.stats-section .stats-grid {\n  display: grid;\n  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));\n  gap: 20px;\n}\n.stats-section .stat-card {\n  display: flex;\n  align-items: center;\n  gap: 16px;\n  padding: 20px;\n  background: white;\n  border-radius: 12px;\n  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);\n  transition: transform 0.2s ease;\n}\n.stats-section .stat-card:hover {\n  transform: translateY(-2px);\n}\n.stats-section .stat-card.alert {\n  background: #fef2f2;\n  border-left: 4px solid #ef4444;\n}\n.stats-section .stat-card .stat-icon {\n  font-size: 32px;\n  width: 48px;\n  height: 48px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  background: #f3f4f6;\n  border-radius: 50%;\n}\n.stats-section .stat-card .stat-value {\n  font-size: 24px;\n  font-weight: 700;\n  color: #1f2937;\n  margin-bottom: 4px;\n}\n.stats-section .stat-card .stat-label {\n  font-size: 14px;\n  color: #6b7280;\n  font-weight: 500;\n}\n.toolbar {\n  margin-bottom: 24px;\n  padding: 20px;\n  background: white;\n  border-radius: 12px;\n  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);\n}\n.toolbar .search-section {\n  display: flex;\n  align-items: center;\n  gap: 24px;\n  flex-wrap: wrap;\n}\n.toolbar .search-box {\n  position: relative;\n  flex: 1;\n  min-width: 300px;\n}\n.toolbar .search-box input {\n  width: 100%;\n  padding: 12px 16px 12px 40px;\n  border: 1px solid #d1d5db;\n  border-radius: 8px;\n  font-size: 14px;\n  background: #f9fafb;\n}\n.toolbar .search-box input:focus {\n  outline: none;\n  border-color: #3b82f6;\n  background: white;\n}\n.toolbar .search-box .search-icon {\n  position: absolute;\n  left: 12px;\n  top: 50%;\n  transform: translateY(-50%);\n  color: #6b7280;\n  font-size: 16px;\n}\n.toolbar .filters {\n  display: flex;\n  gap: 16px;\n  flex-wrap: wrap;\n}\n.toolbar .filters .filter-checkbox {\n  display: flex;\n  align-items: center;\n  gap: 8px;\n  cursor: pointer;\n  font-size: 14px;\n  color: #374151;\n}\n.toolbar .filters .filter-checkbox input[type=checkbox] {\n  width: 16px;\n  height: 16px;\n  accent-color: #3b82f6;\n}\n.stock-empty {\n  color: #dc2626;\n  font-weight: 700;\n}\n.stock-low {\n  color: #f59e0b;\n  font-weight: 600;\n}\n.stock-medium {\n  color: #3b82f6;\n  font-weight: 500;\n}\n.stock-ok {\n  color: #059669;\n  font-weight: 500;\n}\n/*# sourceMappingURL=products.component.css.map */\n'] }]
  }], () => [{ type: ProduitService }, { type: CategorieService }, { type: SousCategorieService }, { type: MarqueService }, { type: FormeService }, { type: TauxTVAService }, { type: AuthService }, { type: ImageUrlService }], null);
})();
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && \u0275setClassDebugInfo(ProductsComponent, { className: "ProductsComponent", filePath: "src/app/components/products/products.component.ts", lineNumber: 32 });
})();
export {
  ProductsComponent
};
//# sourceMappingURL=chunk-MRU7QAL2.js.map

{"version": 3, "sources": ["angular:styles/component:css;19ca2ec987c9df18e28f8f172fd7ae736b83ef4fbce9173e10fcd315a82e9432;C:/Users/<USER>/Desktop/STE/PFE/PFE/frontend-fournisseur/src/app/components/notification-icon/notification-icon.component.ts"], "sourcesContent": ["\n    .notification-wrapper {\n      position: relative;\n      display: inline-block;\n      z-index: 999;\n    }\n\n    .notification-btn {\n      position: relative;\n      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n      border: none;\n      cursor: pointer;\n      padding: 12px;\n      border-radius: 50%;\n      transition: all 0.3s ease;\n      color: white;\n      z-index: 1001;\n      outline: none;\n      font-size: 18px;\n      width: 48px;\n      height: 48px;\n      display: flex;\n      align-items: center;\n      justify-content: center;\n      box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);\n    }\n\n    .notification-btn:hover {\n      background: linear-gradient(135deg, #5a67d8 0%, #6b46c1 100%);\n      transform: translateY(-2px);\n      box-shadow: 0 6px 20px rgba(102, 126, 234, 0.4);\n    }\n\n    .notification-btn:focus {\n      outline: none;\n      box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.3);\n    }\n\n    .notification-btn:active {\n      transform: translateY(0) scale(0.95);\n      box-shadow: 0 2px 8px rgba(102, 126, 234, 0.3);\n    }\n\n    .notification-badge {\n      position: absolute;\n      top: -2px;\n      right: -2px;\n      background: linear-gradient(135deg, #ff4757 0%, #ff3742 100%);\n      color: white;\n      border: 2px solid white;\n      border-radius: 50%;\n      min-width: 22px;\n      height: 22px;\n      font-size: 11px;\n      display: flex;\n      align-items: center;\n      justify-content: center;\n      font-weight: bold;\n      box-shadow: 0 2px 8px rgba(255, 71, 87, 0.4);\n      animation: pulse 2s infinite;\n    }\n\n    @keyframes pulse {\n      0% {\n        box-shadow: 0 2px 8px rgba(255, 71, 87, 0.4);\n      }\n      50% {\n        box-shadow: 0 2px 8px rgba(255, 71, 87, 0.6), 0 0 0 4px rgba(255, 71, 87, 0.2);\n      }\n      100% {\n        box-shadow: 0 2px 8px rgba(255, 71, 87, 0.4);\n      }\n    }\n\n    .notification-menu {\n      position: absolute;\n      top: calc(100% + 8px);\n      right: 0;\n      width: 380px;\n      max-width: 90vw;\n      max-height: 500px;\n      background: white;\n      border: 1px solid #e5e7eb;\n      border-radius: 12px;\n      box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);\n      z-index: 1000;\n      overflow: hidden;\n      transform-origin: top right;\n      animation: slideDown 0.2s ease-out;\n    }\n\n    @keyframes slideDown {\n      from {\n        opacity: 0;\n        transform: translateY(-10px) scale(0.95);\n      }\n      to {\n        opacity: 1;\n        transform: translateY(0) scale(1);\n      }\n    }\n\n    /* Responsive pour mobile */\n    @media (max-width: 768px) {\n      .notification-menu {\n        position: fixed;\n        top: 60px;\n        left: 10px;\n        right: 10px;\n        width: auto;\n        max-width: none;\n        transform-origin: top center;\n      }\n    }\n\n    /* Ajustement pour les petits écrans */\n    @media (max-width: 480px) {\n      .notification-menu {\n        top: 50px;\n        left: 5px;\n        right: 5px;\n        max-height: 70vh;\n      }\n    }\n\n    /* Flèche pointant vers le bouton */\n    .notification-arrow {\n      position: absolute;\n      top: -8px;\n      right: 20px;\n      width: 0;\n      height: 0;\n      border-left: 8px solid transparent;\n      border-right: 8px solid transparent;\n      border-bottom: 8px solid white;\n      z-index: 1001;\n    }\n\n    .notification-arrow::before {\n      content: '';\n      position: absolute;\n      top: 1px;\n      left: -8px;\n      width: 0;\n      height: 0;\n      border-left: 8px solid transparent;\n      border-right: 8px solid transparent;\n      border-bottom: 8px solid #e5e7eb;\n    }\n\n    /* Masquer la flèche sur mobile */\n    @media (max-width: 768px) {\n      .notification-arrow {\n        display: none;\n      }\n    }\n\n    .notification-header {\n      display: flex;\n      justify-content: space-between;\n      align-items: center;\n      padding: 16px;\n      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n      color: white;\n    }\n\n    .notification-header h4 {\n      margin: 0;\n      font-size: 16px;\n      font-weight: 500;\n    }\n\n    .mark-all-read-btn {\n      background: none;\n      border: none;\n      color: white;\n      font-size: 12px;\n      cursor: pointer;\n      padding: 4px 8px;\n      border-radius: 4px;\n      transition: background-color 0.2s;\n    }\n\n    .mark-all-read-btn:hover {\n      background-color: rgba(255, 255, 255, 0.2);\n    }\n\n    .notification-divider {\n      height: 1px;\n      background-color: #e0e0e0;\n    }\n\n    .notification-list {\n      max-height: 300px;\n      overflow-y: auto;\n    }\n\n    .no-notifications {\n      text-align: center;\n      padding: 32px 16px;\n      color: #666;\n    }\n\n    .empty-icon {\n      margin-bottom: 16px;\n      color: #ccc;\n    }\n\n    .no-notifications p {\n      margin: 0;\n      font-size: 16px;\n      font-weight: 500;\n    }\n\n    .no-notifications-subtext {\n      display: block;\n      margin-top: 8px;\n      font-size: 14px;\n      color: #cbd5e1;\n      font-style: italic;\n    }\n\n    .notification-item {\n      display: flex;\n      align-items: flex-start;\n      padding: 12px 16px;\n      border-bottom: 1px solid #e0e0e0;\n      cursor: pointer;\n      transition: background-color 0.2s;\n    }\n\n    .notification-item:hover {\n      background-color: #f5f5f5;\n    }\n\n    .notification-item.unread {\n      background-color: #e3f2fd;\n      border-left: 3px solid #2196f3;\n    }\n\n    .notification-content {\n      flex: 1;\n      margin-right: 8px;\n    }\n\n    .notification-text {\n      margin: 0 0 4px 0;\n      font-size: 14px;\n      line-height: 1.4;\n    }\n\n    .notification-date {\n      font-size: 12px;\n      color: #666;\n    }\n\n    .delete-btn {\n      background: none;\n      border: none;\n      cursor: pointer;\n      opacity: 0;\n      transition: opacity 0.2s;\n      width: 32px;\n      height: 32px;\n      border-radius: 4px;\n      display: flex;\n      align-items: center;\n      justify-content: center;\n      color: #666;\n    }\n\n    .delete-btn:hover {\n      background-color: rgba(244, 67, 54, 0.1);\n      color: #f44336;\n    }\n\n    .notification-item:hover .delete-btn {\n      opacity: 1;\n    }\n\n    .view-all {\n      text-align: center;\n      padding: 16px;\n      border-top: 1px solid #e0e0e0;\n    }\n\n    .view-all button {\n      background: none;\n      border: none;\n      color: #2196f3;\n      cursor: pointer;\n      font-size: 14px;\n      padding: 8px 16px;\n      border-radius: 4px;\n      transition: background-color 0.2s;\n    }\n\n    .view-all button:hover {\n      background-color: rgba(33, 150, 243, 0.1);\n    }\n\n    .demandes-section {\n      padding: 12px 16px;\n      background: #f8f9ff;\n      border-left: 4px solid #3b82f6;\n    }\n\n    .section-header {\n      display: flex;\n      justify-content: space-between;\n      align-items: center;\n      margin-bottom: 8px;\n      font-weight: 500;\n      color: #1e293b;\n      font-size: 14px;\n    }\n\n    .demandes-count {\n      background: #3b82f6;\n      color: white;\n      border-radius: 12px;\n      padding: 2px 8px;\n      font-size: 12px;\n      font-weight: bold;\n    }\n\n    .demande-item {\n      cursor: pointer;\n      padding: 8px 0;\n      transition: opacity 0.2s;\n    }\n\n    .demande-item:hover {\n      opacity: 0.8;\n    }\n\n    .demande-content {\n      display: flex;\n      flex-direction: column;\n      gap: 4px;\n    }\n\n    .demande-title {\n      font-weight: 500;\n      color: #1e293b;\n      font-size: 14px;\n    }\n\n    .demande-message {\n      color: #64748b;\n      font-size: 12px;\n    }\n\n    /* Styles pour les notifications admin */\n    .admin-notifications-section {\n      padding: 12px 16px;\n      background: #f0f9ff;\n      border-left: 4px solid #3b82f6;\n      margin-bottom: 8px;\n    }\n\n    .admin-count {\n      background: #3b82f6;\n      color: white;\n      border-radius: 12px;\n      padding: 2px 8px;\n      font-size: 12px;\n      font-weight: bold;\n    }\n\n    .admin-notification-item {\n      display: flex;\n      align-items: flex-start;\n      gap: 12px;\n      padding: 12px;\n      margin: 8px 0;\n      background: white;\n      border-radius: 8px;\n      cursor: pointer;\n      transition: all 0.2s ease;\n      border-left: 3px solid transparent;\n    }\n\n    .admin-notification-item:hover {\n      background: #f8fafc;\n      transform: translateX(2px);\n    }\n\n    .admin-notification-item.unread {\n      border-left-color: #3b82f6;\n      background: #f0f9ff;\n    }\n\n    .admin-notification-item.high-priority {\n      border-left-color: #ef4444;\n      background: #fef2f2;\n    }\n\n    .admin-notification-icon {\n      font-size: 18px;\n      min-width: 24px;\n      text-align: center;\n    }\n\n    .admin-notification-content {\n      flex: 1;\n      min-width: 0;\n    }\n\n    .admin-notification-title {\n      font-weight: 600;\n      color: #1e293b;\n      margin-bottom: 4px;\n      font-size: 14px;\n    }\n\n    .admin-notification-message {\n      color: #64748b;\n      font-size: 12px;\n      line-height: 1.4;\n      margin-bottom: 8px;\n    }\n\n    .admin-notification-meta {\n      display: flex;\n      align-items: center;\n      gap: 8px;\n    }\n\n    .admin-notification-date {\n      color: #94a3b8;\n      font-size: 11px;\n    }\n\n    .priority-badge {\n      padding: 2px 6px;\n      border-radius: 10px;\n      font-size: 10px;\n      font-weight: 600;\n      text-transform: uppercase;\n    }\n\n    .priority-badge.high {\n      background: #fee2e2;\n      color: #dc2626;\n    }\n\n    .priority-badge.medium {\n      background: #fef3c7;\n      color: #d97706;\n    }\n\n    .admin-delete-btn {\n      background: none;\n      border: none;\n      cursor: pointer;\n      padding: 4px;\n      border-radius: 4px;\n      color: #94a3b8;\n      transition: all 0.2s ease;\n    }\n\n    .admin-delete-btn:hover {\n      background: #fee2e2;\n      color: #dc2626;\n    }\n\n    .view-all-admin {\n      text-align: center;\n      margin-top: 12px;\n      padding-top: 12px;\n      border-top: 1px solid rgba(59, 130, 246, 0.2);\n    }\n\n    .view-all-admin button {\n      background: none;\n      border: 1px solid #3b82f6;\n      color: #3b82f6;\n      padding: 8px 16px;\n      border-radius: 6px;\n      cursor: pointer;\n      font-size: 12px;\n      transition: all 0.2s ease;\n    }\n\n    .view-all-admin button:hover {\n      background: #3b82f6;\n      color: white;\n    }\n  "], "mappings": ";AACI,CAAC;AACC,YAAU;AACV,WAAS;AACT,WAAS;AACX;AAEA,CAAC;AACC,YAAU;AACV;AAAA,IAAY;AAAA,MAAgB,MAAhB;AAAA,MAAwB,QAAQ,EAAhC;AAAA,MAAoC,QAAQ;AACxD,UAAQ;AACR,UAAQ;AACR,WAAS;AACT,iBAAe;AACf,cAAY,IAAI,KAAK;AACrB,SAAO;AACP,WAAS;AACT,WAAS;AACT,aAAW;AACX,SAAO;AACP,UAAQ;AACR,WAAS;AACT,eAAa;AACb,mBAAiB;AACjB,cAAY,EAAE,IAAI,KAAK,KAAK,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE;AAC7C;AAEA,CApBC,gBAoBgB;AACf;AAAA,IAAY;AAAA,MAAgB,MAAhB;AAAA,MAAwB,QAAQ,EAAhC;AAAA,MAAoC,QAAQ;AACxD,aAAW,WAAW;AACtB,cAAY,EAAE,IAAI,KAAK,KAAK,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE;AAC7C;AAEA,CA1BC,gBA0BgB;AACf,WAAS;AACT,cAAY,EAAE,EAAE,EAAE,IAAI,KAAK,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE;AAC5C;AAEA,CA/BC,gBA+BgB;AACf,aAAW,WAAW,GAAG,MAAM;AAC/B,cAAY,EAAE,IAAI,IAAI,KAAK,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE;AAC5C;AAEA,CAAC;AACC,YAAU;AACV,OAAK;AACL,SAAO;AACP;AAAA,IAAY;AAAA,MAAgB,MAAhB;AAAA,MAAwB,QAAQ,EAAhC;AAAA,MAAoC,QAAQ;AACxD,SAAO;AACP,UAAQ,IAAI,MAAM;AAClB,iBAAe;AACf,aAAW;AACX,UAAQ;AACR,aAAW;AACX,WAAS;AACT,eAAa;AACb,mBAAiB;AACjB,eAAa;AACb,cAAY,EAAE,IAAI,IAAI,KAAK,GAAG,EAAE,EAAE,EAAE,EAAE,EAAE;AACxC,aAAW,MAAM,GAAG;AACtB;AAEA,WAHa;AAIX;AACE,gBAAY,EAAE,IAAI,IAAI,KAAK,GAAG,EAAE,EAAE,EAAE,EAAE,EAAE;AAC1C;AACA;AACE,gBAAY,EAAE,IAAI,IAAI,KAAK,GAAG,EAAE,EAAE,EAAE,EAAE,EAAE,IAAI,EAAE,EAAE,EAAE,EAAE,IAAI,KAAK,GAAG,EAAE,EAAE,EAAE,EAAE,EAAE;AAC5E;AACA;AACE,gBAAY,EAAE,IAAI,IAAI,KAAK,GAAG,EAAE,EAAE,EAAE,EAAE,EAAE;AAC1C;AACF;AAEA,CAAC;AACC,YAAU;AACV,OAAK,KAAK,KAAK,EAAE;AACjB,SAAO;AACP,SAAO;AACP,aAAW;AACX,cAAY;AACZ,cAAY;AACZ,UAAQ,IAAI,MAAM;AAClB,iBAAe;AACf,cAAY,EAAE,KAAK,KAAK,KAAK,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE;AACtC,WAAS;AACT,YAAU;AACV,oBAAkB,IAAI;AACtB,aAAW,UAAU,KAAK;AAC5B;AAEA,WAHa;AAIX;AACE,aAAS;AACT,eAAW,WAAW,OAAO,MAAM;AACrC;AACA;AACE,aAAS;AACT,eAAW,WAAW,GAAG,MAAM;AACjC;AACF;AAGA,OAAO,CAAC,SAAS,EAAE;AACjB,GA9BD;AA+BG,cAAU;AACV,SAAK;AACL,UAAM;AACN,WAAO;AACP,WAAO;AACP,eAAW;AACX,sBAAkB,IAAI;AACxB;AACF;AAGA,OAAO,CAAC,SAAS,EAAE;AACjB,GA3CD;AA4CG,SAAK;AACL,UAAM;AACN,WAAO;AACP,gBAAY;AACd;AACF;AAGA,CAAC;AACC,YAAU;AACV,OAAK;AACL,SAAO;AACP,SAAO;AACP,UAAQ;AACR,eAAa,IAAI,MAAM;AACvB,gBAAc,IAAI,MAAM;AACxB,iBAAe,IAAI,MAAM;AACzB,WAAS;AACX;AAEA,CAZC,kBAYkB;AACjB,WAAS;AACT,YAAU;AACV,OAAK;AACL,QAAM;AACN,SAAO;AACP,UAAQ;AACR,eAAa,IAAI,MAAM;AACvB,gBAAc,IAAI,MAAM;AACxB,iBAAe,IAAI,MAAM;AAC3B;AAGA,OAAO,CAAC,SAAS,EAAE;AACjB,GA1BD;AA2BG,aAAS;AACX;AACF;AAEA,CAAC;AACC,WAAS;AACT,mBAAiB;AACjB,eAAa;AACb,WAAS;AACT;AAAA,IAAY;AAAA,MAAgB,MAAhB;AAAA,MAAwB,QAAQ,EAAhC;AAAA,MAAoC,QAAQ;AACxD,SAAO;AACT;AAEA,CATC,oBASoB;AACnB,UAAQ;AACR,aAAW;AACX,eAAa;AACf;AAEA,CAAC;AACC,cAAY;AACZ,UAAQ;AACR,SAAO;AACP,aAAW;AACX,UAAQ;AACR,WAAS,IAAI;AACb,iBAAe;AACf,cAAY,iBAAiB;AAC/B;AAEA,CAXC,iBAWiB;AAChB,oBAAkB,KAAK,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE;AACxC;AAEA,CAAC;AACC,UAAQ;AACR,oBAAkB;AACpB;AAEA,CAAC;AACC,cAAY;AACZ,cAAY;AACd;AAEA,CAAC;AACC,cAAY;AACZ,WAAS,KAAK;AACd,SAAO;AACT;AAEA,CAAC;AACC,iBAAe;AACf,SAAO;AACT;AAEA,CAXC,iBAWiB;AAChB,UAAQ;AACR,aAAW;AACX,eAAa;AACf;AAEA,CAAC;AACC,WAAS;AACT,cAAY;AACZ,aAAW;AACX,SAAO;AACP,cAAY;AACd;AAEA,CAAC;AACC,WAAS;AACT,eAAa;AACb,WAAS,KAAK;AACd,iBAAe,IAAI,MAAM;AACzB,UAAQ;AACR,cAAY,iBAAiB;AAC/B;AAEA,CATC,iBASiB;AAChB,oBAAkB;AACpB;AAEA,CAbC,iBAaiB,CAAC;AACjB,oBAAkB;AAClB,eAAa,IAAI,MAAM;AACzB;AAEA,CAAC;AACC,QAAM;AACN,gBAAc;AAChB;AAEA,CAAC;AACC,UAAQ,EAAE,EAAE,IAAI;AAChB,aAAW;AACX,eAAa;AACf;AAEA,CAAC;AACC,aAAW;AACX,SAAO;AACT;AAEA,CAAC;AACC,cAAY;AACZ,UAAQ;AACR,UAAQ;AACR,WAAS;AACT,cAAY,QAAQ;AACpB,SAAO;AACP,UAAQ;AACR,iBAAe;AACf,WAAS;AACT,eAAa;AACb,mBAAiB;AACjB,SAAO;AACT;AAEA,CAfC,UAeU;AACT,oBAAkB,KAAK,GAAG,EAAE,EAAE,EAAE,EAAE,EAAE;AACpC,SAAO;AACT;AAEA,CAtDC,iBAsDiB,OAAO,CApBxB;AAqBC,WAAS;AACX;AAEA,CAAC;AACC,cAAY;AACZ,WAAS;AACT,cAAY,IAAI,MAAM;AACxB;AAEA,CANC,SAMS;AACR,cAAY;AACZ,UAAQ;AACR,SAAO;AACP,UAAQ;AACR,aAAW;AACX,WAAS,IAAI;AACb,iBAAe;AACf,cAAY,iBAAiB;AAC/B;AAEA,CAjBC,SAiBS,MAAM;AACd,oBAAkB,KAAK,EAAE,EAAE,GAAG,EAAE,GAAG,EAAE;AACvC;AAEA,CAAC;AACC,WAAS,KAAK;AACd,cAAY;AACZ,eAAa,IAAI,MAAM;AACzB;AAEA,CAAC;AACC,WAAS;AACT,mBAAiB;AACjB,eAAa;AACb,iBAAe;AACf,eAAa;AACb,SAAO;AACP,aAAW;AACb;AAEA,CAAC;AACC,cAAY;AACZ,SAAO;AACP,iBAAe;AACf,WAAS,IAAI;AACb,aAAW;AACX,eAAa;AACf;AAEA,CAAC;AACC,UAAQ;AACR,WAAS,IAAI;AACb,cAAY,QAAQ;AACtB;AAEA,CANC,YAMY;AACX,WAAS;AACX;AAEA,CAAC;AACC,WAAS;AACT,kBAAgB;AAChB,OAAK;AACP;AAEA,CAAC;AACC,eAAa;AACb,SAAO;AACP,aAAW;AACb;AAEA,CAAC;AACC,SAAO;AACP,aAAW;AACb;AAGA,CAAC;AACC,WAAS,KAAK;AACd,cAAY;AACZ,eAAa,IAAI,MAAM;AACvB,iBAAe;AACjB;AAEA,CAAC;AACC,cAAY;AACZ,SAAO;AACP,iBAAe;AACf,WAAS,IAAI;AACb,aAAW;AACX,eAAa;AACf;AAEA,CAAC;AACC,WAAS;AACT,eAAa;AACb,OAAK;AACL,WAAS;AACT,UAAQ,IAAI;AACZ,cAAY;AACZ,iBAAe;AACf,UAAQ;AACR,cAAY,IAAI,KAAK;AACrB,eAAa,IAAI,MAAM;AACzB;AAEA,CAbC,uBAauB;AACtB,cAAY;AACZ,aAAW,WAAW;AACxB;AAEA,CAlBC,uBAkBuB,CAzJL;AA0JjB,qBAAmB;AACnB,cAAY;AACd;AAEA,CAvBC,uBAuBuB,CAAC;AACvB,qBAAmB;AACnB,cAAY;AACd;AAEA,CAAC;AACC,aAAW;AACX,aAAW;AACX,cAAY;AACd;AAEA,CAAC;AACC,QAAM;AACN,aAAW;AACb;AAEA,CAAC;AACC,eAAa;AACb,SAAO;AACP,iBAAe;AACf,aAAW;AACb;AAEA,CAAC;AACC,SAAO;AACP,aAAW;AACX,eAAa;AACb,iBAAe;AACjB;AAEA,CAAC;AACC,WAAS;AACT,eAAa;AACb,OAAK;AACP;AAEA,CAAC;AACC,SAAO;AACP,aAAW;AACb;AAEA,CAAC;AACC,WAAS,IAAI;AACb,iBAAe;AACf,aAAW;AACX,eAAa;AACb,kBAAgB;AAClB;AAEA,CARC,cAQc,CAAC;AACd,cAAY;AACZ,SAAO;AACT;AAEA,CAbC,cAac,CAAC;AACd,cAAY;AACZ,SAAO;AACT;AAEA,CAAC;AACC,cAAY;AACZ,UAAQ;AACR,UAAQ;AACR,WAAS;AACT,iBAAe;AACf,SAAO;AACP,cAAY,IAAI,KAAK;AACvB;AAEA,CAVC,gBAUgB;AACf,cAAY;AACZ,SAAO;AACT;AAEA,CAAC;AACC,cAAY;AACZ,cAAY;AACZ,eAAa;AACb,cAAY,IAAI,MAAM,KAAK,EAAE,EAAE,GAAG,EAAE,GAAG,EAAE;AAC3C;AAEA,CAPC,eAOe;AACd,cAAY;AACZ,UAAQ,IAAI,MAAM;AAClB,SAAO;AACP,WAAS,IAAI;AACb,iBAAe;AACf,UAAQ;AACR,aAAW;AACX,cAAY,IAAI,KAAK;AACvB;AAEA,CAlBC,eAkBe,MAAM;AACpB,cAAY;AACZ,SAAO;AACT;", "names": []}
import {
  AdminAuthService
} from "./chunk-2RV3R4JN.js";
import {
  Router
} from "./chunk-6BVUYNW4.js";
import {
  Default<PERSON><PERSON>ueAccessor,
  FormBuilder,
  FormControlName,
  FormGroupDirective,
  MaxLengthValidator,
  NgControlStatus,
  NgControlStatusGroup,
  ReactiveFormsModule,
  Validators,
  ɵNgNoValidate
} from "./chunk-HQBVYEOO.js";
import "./chunk-GFHHPDQ6.js";
import "./chunk-7JDDWGD3.js";
import {
  CommonModule,
  Component,
  NgIf,
  setClassMetadata,
  ɵsetClassDebugInfo,
  ɵɵadvance,
  ɵɵclassProp,
  ɵɵdefineComponent,
  ɵɵdirectiveInject,
  ɵɵelement,
  ɵɵelementEnd,
  ɵɵelementStart,
  ɵɵgetCurrentView,
  ɵɵlistener,
  ɵɵnamespaceHTML,
  ɵɵnamespaceSVG,
  ɵɵnextContext,
  ɵɵproperty,
  ɵɵresetView,
  ɵɵrestoreView,
  ɵɵtemplate,
  ɵɵtext,
  ɵɵtextInterpolate,
  ɵɵtextInterpolate1
} from "./chunk-UBZQS7JS.js";

// src/app/components/admin/auth/admin-login/admin-login.component.ts
function AdminLoginComponent_div_19_div_13_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "div", 39);
    \u0275\u0275text(1);
    \u0275\u0275elementEnd();
  }
  if (rf & 2) {
    const ctx_r1 = \u0275\u0275nextContext(2);
    \u0275\u0275advance();
    \u0275\u0275textInterpolate1(" ", ctx_r1.getFieldError(ctx_r1.loginForm, "email"), " ");
  }
}
function AdminLoginComponent_div_19_div_20_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "div", 39);
    \u0275\u0275text(1);
    \u0275\u0275elementEnd();
  }
  if (rf & 2) {
    const ctx_r1 = \u0275\u0275nextContext(2);
    \u0275\u0275advance();
    \u0275\u0275textInterpolate1(" ", ctx_r1.getFieldError(ctx_r1.loginForm, "password"), " ");
  }
}
function AdminLoginComponent_div_19_div_22_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "div", 40)(1, "span", 41);
    \u0275\u0275text(2, "\u26A0\uFE0F");
    \u0275\u0275elementEnd();
    \u0275\u0275text(3);
    \u0275\u0275elementEnd();
  }
  if (rf & 2) {
    const ctx_r1 = \u0275\u0275nextContext(2);
    \u0275\u0275advance(3);
    \u0275\u0275textInterpolate1(" ", ctx_r1.errorMessage, " ");
  }
}
function AdminLoginComponent_div_19_div_23_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "div", 42)(1, "span", 41);
    \u0275\u0275text(2, "\u2705");
    \u0275\u0275elementEnd();
    \u0275\u0275text(3);
    \u0275\u0275elementEnd();
  }
  if (rf & 2) {
    const ctx_r1 = \u0275\u0275nextContext(2);
    \u0275\u0275advance(3);
    \u0275\u0275textInterpolate1(" ", ctx_r1.successMessage, " ");
  }
}
function AdminLoginComponent_div_19_span_25_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "span", 43)(1, "span", 44);
    \u0275\u0275text(2, "\u{1F680}");
    \u0275\u0275elementEnd();
    \u0275\u0275text(3, " Se connecter ");
    \u0275\u0275elementEnd();
  }
}
function AdminLoginComponent_div_19_span_26_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275element(0, "span", 45);
  }
}
function AdminLoginComponent_div_19_Template(rf, ctx) {
  if (rf & 1) {
    const _r1 = \u0275\u0275getCurrentView();
    \u0275\u0275elementStart(0, "div", 23)(1, "div", 24)(2, "h2");
    \u0275\u0275text(3, "Connexion Administrateur");
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(4, "p");
    \u0275\u0275text(5, "Connectez-vous avec vos identifiants s\xE9curis\xE9s");
    \u0275\u0275elementEnd()();
    \u0275\u0275elementStart(6, "form", 25);
    \u0275\u0275listener("ngSubmit", function AdminLoginComponent_div_19_Template_form_ngSubmit_6_listener() {
      \u0275\u0275restoreView(_r1);
      const ctx_r1 = \u0275\u0275nextContext();
      return \u0275\u0275resetView(ctx_r1.onLogin());
    });
    \u0275\u0275elementStart(7, "div", 26)(8, "label", 27)(9, "span", 28);
    \u0275\u0275text(10, "\u{1F464}");
    \u0275\u0275elementEnd();
    \u0275\u0275text(11, " Nom d'utilisateur ");
    \u0275\u0275elementEnd();
    \u0275\u0275element(12, "input", 29);
    \u0275\u0275template(13, AdminLoginComponent_div_19_div_13_Template, 2, 1, "div", 30);
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(14, "div", 26)(15, "label", 31)(16, "span", 28);
    \u0275\u0275text(17, "\u{1F512}");
    \u0275\u0275elementEnd();
    \u0275\u0275text(18, " Mot de passe ");
    \u0275\u0275elementEnd();
    \u0275\u0275element(19, "input", 32);
    \u0275\u0275template(20, AdminLoginComponent_div_19_div_20_Template, 2, 1, "div", 30);
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(21, "div", 33);
    \u0275\u0275template(22, AdminLoginComponent_div_19_div_22_Template, 4, 1, "div", 34)(23, AdminLoginComponent_div_19_div_23_Template, 4, 1, "div", 35);
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(24, "button", 36);
    \u0275\u0275template(25, AdminLoginComponent_div_19_span_25_Template, 4, 0, "span", 37)(26, AdminLoginComponent_div_19_span_26_Template, 1, 0, "span", 38);
    \u0275\u0275elementEnd()()();
  }
  if (rf & 2) {
    const ctx_r1 = \u0275\u0275nextContext();
    \u0275\u0275advance(6);
    \u0275\u0275property("formGroup", ctx_r1.loginForm);
    \u0275\u0275advance(6);
    \u0275\u0275classProp("error", ctx_r1.hasFieldError(ctx_r1.loginForm, "email"));
    \u0275\u0275advance();
    \u0275\u0275property("ngIf", ctx_r1.hasFieldError(ctx_r1.loginForm, "email"));
    \u0275\u0275advance(6);
    \u0275\u0275classProp("error", ctx_r1.hasFieldError(ctx_r1.loginForm, "password"));
    \u0275\u0275advance();
    \u0275\u0275property("ngIf", ctx_r1.hasFieldError(ctx_r1.loginForm, "password"));
    \u0275\u0275advance(2);
    \u0275\u0275property("ngIf", ctx_r1.errorMessage);
    \u0275\u0275advance();
    \u0275\u0275property("ngIf", ctx_r1.successMessage);
    \u0275\u0275advance();
    \u0275\u0275classProp("loading", ctx_r1.isLoading);
    \u0275\u0275property("disabled", ctx_r1.isLoading);
    \u0275\u0275advance();
    \u0275\u0275property("ngIf", !ctx_r1.isLoading);
    \u0275\u0275advance();
    \u0275\u0275property("ngIf", ctx_r1.isLoading);
  }
}
function AdminLoginComponent_div_20_div_15_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "div", 39);
    \u0275\u0275text(1);
    \u0275\u0275elementEnd();
  }
  if (rf & 2) {
    const ctx_r1 = \u0275\u0275nextContext(2);
    \u0275\u0275advance();
    \u0275\u0275textInterpolate1(" ", ctx_r1.getFieldError(ctx_r1.otpForm, "otpCode"), " ");
  }
}
function AdminLoginComponent_div_20_div_17_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "div", 40)(1, "span", 41);
    \u0275\u0275text(2, "\u26A0\uFE0F");
    \u0275\u0275elementEnd();
    \u0275\u0275text(3);
    \u0275\u0275elementEnd();
  }
  if (rf & 2) {
    const ctx_r1 = \u0275\u0275nextContext(2);
    \u0275\u0275advance(3);
    \u0275\u0275textInterpolate1(" ", ctx_r1.errorMessage, " ");
  }
}
function AdminLoginComponent_div_20_div_18_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "div", 42)(1, "span", 41);
    \u0275\u0275text(2, "\u2705");
    \u0275\u0275elementEnd();
    \u0275\u0275text(3);
    \u0275\u0275elementEnd();
  }
  if (rf & 2) {
    const ctx_r1 = \u0275\u0275nextContext(2);
    \u0275\u0275advance(3);
    \u0275\u0275textInterpolate1(" ", ctx_r1.successMessage, " ");
  }
}
function AdminLoginComponent_div_20_span_23_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "span", 43)(1, "span", 44);
    \u0275\u0275text(2, "\u{1F513}");
    \u0275\u0275elementEnd();
    \u0275\u0275text(3, " V\xE9rifier ");
    \u0275\u0275elementEnd();
  }
}
function AdminLoginComponent_div_20_span_24_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275element(0, "span", 45);
  }
}
function AdminLoginComponent_div_20_Template(rf, ctx) {
  if (rf & 1) {
    const _r3 = \u0275\u0275getCurrentView();
    \u0275\u0275elementStart(0, "div", 46)(1, "div", 24)(2, "h2");
    \u0275\u0275text(3, "Authentification \xE0 deux facteurs");
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(4, "p");
    \u0275\u0275text(5, "Saisissez le code \xE0 6 chiffres envoy\xE9 \xE0 ");
    \u0275\u0275elementStart(6, "strong");
    \u0275\u0275text(7);
    \u0275\u0275elementEnd()()();
    \u0275\u0275elementStart(8, "form", 47);
    \u0275\u0275listener("ngSubmit", function AdminLoginComponent_div_20_Template_form_ngSubmit_8_listener() {
      \u0275\u0275restoreView(_r3);
      const ctx_r1 = \u0275\u0275nextContext();
      return \u0275\u0275resetView(ctx_r1.onVerifyOTP());
    });
    \u0275\u0275elementStart(9, "div", 26)(10, "label", 48)(11, "span", 28);
    \u0275\u0275text(12, "\u{1F510}");
    \u0275\u0275elementEnd();
    \u0275\u0275text(13, " Code de v\xE9rification ");
    \u0275\u0275elementEnd();
    \u0275\u0275element(14, "input", 49);
    \u0275\u0275template(15, AdminLoginComponent_div_20_div_15_Template, 2, 1, "div", 30);
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(16, "div", 33);
    \u0275\u0275template(17, AdminLoginComponent_div_20_div_17_Template, 4, 1, "div", 34)(18, AdminLoginComponent_div_20_div_18_Template, 4, 1, "div", 35);
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(19, "div", 50)(20, "button", 51);
    \u0275\u0275listener("click", function AdminLoginComponent_div_20_Template_button_click_20_listener() {
      \u0275\u0275restoreView(_r3);
      const ctx_r1 = \u0275\u0275nextContext();
      return \u0275\u0275resetView(ctx_r1.backToLogin());
    });
    \u0275\u0275text(21, " \u2190 Retour ");
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(22, "button", 52);
    \u0275\u0275template(23, AdminLoginComponent_div_20_span_23_Template, 4, 0, "span", 37)(24, AdminLoginComponent_div_20_span_24_Template, 1, 0, "span", 38);
    \u0275\u0275elementEnd()()()();
  }
  if (rf & 2) {
    const ctx_r1 = \u0275\u0275nextContext();
    \u0275\u0275advance(7);
    \u0275\u0275textInterpolate(ctx_r1.userEmail);
    \u0275\u0275advance();
    \u0275\u0275property("formGroup", ctx_r1.otpForm);
    \u0275\u0275advance(6);
    \u0275\u0275classProp("error", ctx_r1.hasFieldError(ctx_r1.otpForm, "otpCode"));
    \u0275\u0275advance();
    \u0275\u0275property("ngIf", ctx_r1.hasFieldError(ctx_r1.otpForm, "otpCode"));
    \u0275\u0275advance(2);
    \u0275\u0275property("ngIf", ctx_r1.errorMessage);
    \u0275\u0275advance();
    \u0275\u0275property("ngIf", ctx_r1.successMessage);
    \u0275\u0275advance(2);
    \u0275\u0275property("disabled", ctx_r1.isLoading);
    \u0275\u0275advance(2);
    \u0275\u0275classProp("loading", ctx_r1.isLoading);
    \u0275\u0275property("disabled", ctx_r1.isLoading);
    \u0275\u0275advance();
    \u0275\u0275property("ngIf", !ctx_r1.isLoading);
    \u0275\u0275advance();
    \u0275\u0275property("ngIf", ctx_r1.isLoading);
  }
}
var AdminLoginComponent = class _AdminLoginComponent {
  formBuilder;
  adminAuthService;
  router;
  loginForm;
  otpForm;
  isLoading = false;
  showOTPForm = false;
  sessionToken = "";
  userEmail = "";
  errorMessage = "";
  successMessage = "";
  // Animation states
  isFormVisible = false;
  constructor(formBuilder, adminAuthService, router) {
    this.formBuilder = formBuilder;
    this.adminAuthService = adminAuthService;
    this.router = router;
    this.loginForm = this.createLoginForm();
    this.otpForm = this.createOTPForm();
  }
  ngOnInit() {
    const currentUser = this.adminAuthService.getCurrentUser();
    console.log("\u{1F50D} \xC9tat auth admin au chargement:", currentUser);
    if (currentUser) {
      console.log("Admin d\xE9j\xE0 connect\xE9, redirection vers dashboard");
      this.redirectAfterLogin();
      return;
    }
    setTimeout(() => {
      this.isFormVisible = true;
    }, 100);
  }
  // Méthode temporaire pour debug
  forceLogout() {
    this.adminAuthService.forceLogout();
    console.log("\u{1F512} D\xE9connexion forc\xE9e effectu\xE9e");
    location.reload();
  }
  /**
   * Créer le formulaire de connexion
   */
  createLoginForm() {
    return this.formBuilder.group({
      email: ["", [Validators.required, Validators.minLength(3)]],
      // Changé pour username
      password: ["", [Validators.required, Validators.minLength(6)]],
      rememberMe: [false]
    });
  }
  /**
   * Créer le formulaire OTP
   */
  createOTPForm() {
    return this.formBuilder.group({
      otpCode: ["", [Validators.required, Validators.pattern(/^\d{6}$/)]]
    });
  }
  /**
   * Connexion avec email/mot de passe
   */
  onLogin() {
    if (this.loginForm.invalid) {
      this.markFormGroupTouched(this.loginForm);
      return;
    }
    this.isLoading = true;
    this.errorMessage = "";
    this.successMessage = "";
    const credentials = this.loginForm.value;
    this.adminAuthService.login(credentials).subscribe({
      next: (response) => {
        this.isLoading = false;
        if (response.requiresOTP) {
          this.showOTPForm = true;
          this.sessionToken = response.sessionToken;
          this.userEmail = credentials.email;
          this.successMessage = response.message || "Code OTP envoy\xE9";
        } else {
          this.successMessage = "Connexion r\xE9ussie";
          setTimeout(() => {
            this.adminAuthService.isAuthenticated$.subscribe((isAuth) => {
              if (isAuth) {
                this.redirectAfterLogin();
              }
            });
          }, 500);
        }
      },
      error: (error) => {
        this.isLoading = false;
        this.errorMessage = error.message || "Erreur de connexion";
      }
    });
  }
  /**
   * Vérification du code OTP
   */
  onVerifyOTP() {
    if (this.otpForm.invalid) {
      this.markFormGroupTouched(this.otpForm);
      return;
    }
    this.isLoading = true;
    this.errorMessage = "";
    const otpRequest = {
      email: this.userEmail,
      otpCode: this.otpForm.value.otpCode,
      sessionToken: this.sessionToken
    };
    this.adminAuthService.verifyOTP(otpRequest).subscribe({
      next: (response) => {
        this.isLoading = false;
        this.successMessage = "Authentification r\xE9ussie";
        setTimeout(() => this.redirectAfterLogin(), 1e3);
      },
      error: (error) => {
        this.isLoading = false;
        this.errorMessage = error.message || "Code OTP invalide";
      }
    });
  }
  /**
   * Retour au formulaire de connexion
   */
  backToLogin() {
    this.showOTPForm = false;
    this.sessionToken = "";
    this.userEmail = "";
    this.otpForm.reset();
    this.errorMessage = "";
    this.successMessage = "";
  }
  /**
   * Redirection après connexion
   */
  redirectAfterLogin() {
    const redirectUrl = localStorage.getItem("admin_redirect_url") || "/admin/dashboard";
    localStorage.removeItem("admin_redirect_url");
    console.log("Redirection admin vers:", redirectUrl);
    this.router.navigate([redirectUrl]);
  }
  /**
   * Marquer tous les champs comme touchés
   */
  markFormGroupTouched(formGroup) {
    Object.keys(formGroup.controls).forEach((key) => {
      const control = formGroup.get(key);
      control?.markAsTouched();
    });
  }
  /**
   * Vérifier si un champ a une erreur
   */
  hasFieldError(form, fieldName) {
    const field = form.get(fieldName);
    return !!(field && field.invalid && field.touched);
  }
  /**
   * Obtenir le message d'erreur d'un champ
   */
  getFieldError(form, fieldName) {
    const field = form.get(fieldName);
    if (field && field.errors && field.touched) {
      if (field.errors["required"]) {
        return "Ce champ est obligatoire";
      }
      if (field.errors["minlength"]) {
        return `Minimum ${field.errors["minlength"].requiredLength} caract\xE8res`;
      }
      if (field.errors["pattern"]) {
        if (fieldName === "otpCode") {
          return "Le code doit contenir 6 chiffres";
        }
        return "Format invalide";
      }
    }
    return "";
  }
  static \u0275fac = function AdminLoginComponent_Factory(__ngFactoryType__) {
    return new (__ngFactoryType__ || _AdminLoginComponent)(\u0275\u0275directiveInject(FormBuilder), \u0275\u0275directiveInject(AdminAuthService), \u0275\u0275directiveInject(Router));
  };
  static \u0275cmp = /* @__PURE__ */ \u0275\u0275defineComponent({ type: _AdminLoginComponent, selectors: [["app-admin-login"]], decls: 31, vars: 4, consts: [[1, "admin-login-page"], [1, "login-background"], [1, "animated-shapes"], [1, "shape", "shape-1"], [1, "shape", "shape-2"], [1, "shape", "shape-3"], [1, "shape", "shape-4"], [1, "login-container"], [1, "login-header"], [1, "logo-container"], [1, "logo-icon"], ["width", "48", "height", "48", "viewBox", "0 0 24 24", "fill", "none", "stroke", "currentColor", "stroke-width", "2"], ["d", "M12 2L2 7l10 5 10-5-10-5z"], ["d", "M2 17l10 5 10-5"], ["d", "M2 12l10 5 10-5"], [1, "logo-title"], [1, "login-subtitle"], ["class", "login-card", 4, "ngIf"], ["class", "login-card otp-card", 4, "ngIf"], [1, "login-footer"], [1, "footer-text"], [1, "security-badges"], [1, "security-badge"], [1, "login-card"], [1, "card-header"], [1, "login-form", 3, "ngSubmit", "formGroup"], [1, "form-group"], ["for", "email", 1, "form-label"], [1, "label-icon"], ["type", "text", "id", "email", "formControlName", "email", "placeholder", "adminOptiLet", "autocomplete", "username", 1, "form-control"], ["class", "error-message", 4, "ngIf"], ["for", "password", 1, "form-label"], ["type", "password", "id", "password", "formControlName", "password", "placeholder", "\u2022\u2022\u2022\u2022\u2022\u2022\u2022\u2022", "autocomplete", "current-password", 1, "form-control"], [1, "message-container"], ["class", "error-alert", 4, "ngIf"], ["class", "success-alert", 4, "ngIf"], ["type", "submit", 1, "login-button", 3, "disabled"], ["class", "button-content", 4, "ngIf"], ["class", "loading-spinner", 4, "ngIf"], [1, "error-message"], [1, "error-alert"], [1, "alert-icon"], [1, "success-alert"], [1, "button-content"], [1, "button-icon"], [1, "loading-spinner"], [1, "login-card", "otp-card"], [1, "otp-form", 3, "ngSubmit", "formGroup"], ["for", "otpCode", 1, "form-label"], ["type", "text", "id", "otpCode", "formControlName", "otpCode", "placeholder", "123456", "maxlength", "6", "autocomplete", "one-time-code", 1, "form-control", "otp-input"], [1, "otp-buttons"], ["type", "button", 1, "back-button", 3, "click", "disabled"], ["type", "submit", 1, "verify-button", 3, "disabled"]], template: function AdminLoginComponent_Template(rf, ctx) {
    if (rf & 1) {
      \u0275\u0275elementStart(0, "div", 0)(1, "div", 1)(2, "div", 2);
      \u0275\u0275element(3, "div", 3)(4, "div", 4)(5, "div", 5)(6, "div", 6);
      \u0275\u0275elementEnd()();
      \u0275\u0275elementStart(7, "div", 7)(8, "div", 8)(9, "div", 9)(10, "div", 10);
      \u0275\u0275namespaceSVG();
      \u0275\u0275elementStart(11, "svg", 11);
      \u0275\u0275element(12, "path", 12)(13, "path", 13)(14, "path", 14);
      \u0275\u0275elementEnd()();
      \u0275\u0275namespaceHTML();
      \u0275\u0275elementStart(15, "h1", 15);
      \u0275\u0275text(16, "Admin Panel");
      \u0275\u0275elementEnd()();
      \u0275\u0275elementStart(17, "p", 16);
      \u0275\u0275text(18, "Acc\xE8s s\xE9curis\xE9 \xE0 l'administration");
      \u0275\u0275elementEnd()();
      \u0275\u0275template(19, AdminLoginComponent_div_19_Template, 27, 14, "div", 17)(20, AdminLoginComponent_div_20_Template, 25, 13, "div", 18);
      \u0275\u0275elementStart(21, "div", 19)(22, "p", 20);
      \u0275\u0275text(23, " \xA9 2024 Optique Vision - Administration s\xE9curis\xE9e ");
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(24, "div", 21)(25, "span", 22);
      \u0275\u0275text(26, "\u{1F512} SSL");
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(27, "span", 22);
      \u0275\u0275text(28, "\u{1F6E1}\uFE0F 2FA");
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(29, "span", 22);
      \u0275\u0275text(30, "\u{1F4CA} Audit");
      \u0275\u0275elementEnd()()()()();
    }
    if (rf & 2) {
      \u0275\u0275advance(7);
      \u0275\u0275classProp("visible", ctx.isFormVisible);
      \u0275\u0275advance(12);
      \u0275\u0275property("ngIf", !ctx.showOTPForm);
      \u0275\u0275advance();
      \u0275\u0275property("ngIf", ctx.showOTPForm);
    }
  }, dependencies: [CommonModule, NgIf, ReactiveFormsModule, \u0275NgNoValidate, DefaultValueAccessor, NgControlStatus, NgControlStatusGroup, MaxLengthValidator, FormGroupDirective, FormControlName], styles: ['\n\n.admin-login-page[_ngcontent-%COMP%] {\n  min-height: 100vh;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  background:\n    linear-gradient(\n      135deg,\n      #f1f5f9 0%,\n      #e2e8f0 100%);\n  padding: 2rem;\n}\n.login-container[_ngcontent-%COMP%] {\n  width: 100%;\n  max-width: 450px;\n  background: rgba(255, 255, 255, 0.98);\n  -webkit-backdrop-filter: blur(10px);\n  backdrop-filter: blur(10px);\n  border: 1px solid rgba(255, 255, 255, 0.2);\n  border-radius: 16px;\n  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);\n  padding: 3rem;\n  position: relative;\n}\n.login-container[_ngcontent-%COMP%]::before {\n  content: "";\n  position: absolute;\n  top: 0;\n  left: 0;\n  right: 0;\n  height: 4px;\n  background:\n    linear-gradient(\n      90deg,\n      #1e293b 0%,\n      #475569 100%);\n  border-radius: 16px 16px 0 0;\n}\n.login-header[_ngcontent-%COMP%] {\n  text-align: center;\n  margin-bottom: 2rem;\n}\n.logo-container[_ngcontent-%COMP%] {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  gap: 1rem;\n  margin-bottom: 2rem;\n}\n.logo-icon[_ngcontent-%COMP%] {\n  width: 60px;\n  height: 60px;\n  background: #1e293b;\n  border-radius: 12px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  color: white;\n  font-size: 1.5rem;\n}\n.logo-title[_ngcontent-%COMP%] {\n  font-size: 1.75rem;\n  font-weight: 700;\n  color: #1e293b;\n  margin: 0;\n}\n.login-subtitle[_ngcontent-%COMP%] {\n  color: #64748b;\n  font-size: 0.875rem;\n  margin: 0;\n  font-weight: 500;\n}\n.card-header[_ngcontent-%COMP%] {\n  text-align: center;\n  margin-bottom: 2rem;\n}\n.card-header[_ngcontent-%COMP%]   h2[_ngcontent-%COMP%] {\n  font-size: 1.5rem;\n  font-weight: 600;\n  color: #1e293b;\n  margin: 0 0 0.5rem 0;\n}\n.card-header[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\n  color: #64748b;\n  font-size: 0.875rem;\n  margin: 0;\n}\n.login-form[_ngcontent-%COMP%], \n.otp-form[_ngcontent-%COMP%] {\n  display: flex;\n  flex-direction: column;\n  gap: 1.5rem;\n}\n.form-group[_ngcontent-%COMP%] {\n  display: flex;\n  flex-direction: column;\n  gap: 0.5rem;\n}\n.form-label[_ngcontent-%COMP%] {\n  display: flex;\n  align-items: center;\n  gap: 0.5rem;\n  font-size: 0.875rem;\n  font-weight: 500;\n  color: #374151;\n}\n.label-icon[_ngcontent-%COMP%] {\n  font-size: 1rem;\n}\n.form-control[_ngcontent-%COMP%] {\n  padding: 1rem 1.25rem;\n  border: 1px solid #d1d5db;\n  border-radius: 8px;\n  font-size: 1rem;\n  transition: all 0.2s ease;\n  background: white;\n}\n.form-control[_ngcontent-%COMP%]:focus {\n  outline: none;\n  border-color: #1e293b;\n  box-shadow: 0 0 0 3px rgba(30, 41, 59, 0.1);\n}\n.form-control.error[_ngcontent-%COMP%] {\n  border-color: #ef4444;\n  box-shadow: 0 0 0 3px rgba(239, 68, 68, 0.1);\n}\n.otp-input[_ngcontent-%COMP%] {\n  text-align: center;\n  font-size: 1.5rem;\n  font-weight: 600;\n  letter-spacing: 0.5rem;\n  font-family: monospace;\n}\n.checkbox-group[_ngcontent-%COMP%] {\n  margin: 0.5rem 0;\n}\n.checkbox-label[_ngcontent-%COMP%] {\n  display: flex;\n  align-items: center;\n  gap: 0.75rem;\n  cursor: pointer;\n  font-size: 0.875rem;\n  color: #374151;\n}\n.checkbox-input[_ngcontent-%COMP%] {\n  display: none;\n}\n.checkbox-custom[_ngcontent-%COMP%] {\n  width: 20px;\n  height: 20px;\n  border: 2px solid #d1d5db;\n  border-radius: 4px;\n  position: relative;\n  transition: all 0.3s ease;\n}\n.checkbox-input[_ngcontent-%COMP%]:checked    + .checkbox-custom[_ngcontent-%COMP%] {\n  background: #667eea;\n  border-color: #667eea;\n}\n.checkbox-input[_ngcontent-%COMP%]:checked    + .checkbox-custom[_ngcontent-%COMP%]::after {\n  content: "\\2713";\n  position: absolute;\n  top: 50%;\n  left: 50%;\n  transform: translate(-50%, -50%);\n  color: white;\n  font-size: 12px;\n  font-weight: bold;\n}\n.message-container[_ngcontent-%COMP%] {\n  margin: 1rem 0;\n}\n.error-alert[_ngcontent-%COMP%], \n.success-alert[_ngcontent-%COMP%] {\n  display: flex;\n  align-items: center;\n  gap: 0.5rem;\n  padding: 0.75rem 1rem;\n  border-radius: 8px;\n  font-size: 0.875rem;\n  font-weight: 500;\n}\n.error-alert[_ngcontent-%COMP%] {\n  background: #fef2f2;\n  color: #dc2626;\n  border: 1px solid #fecaca;\n}\n.success-alert[_ngcontent-%COMP%] {\n  background: #f0fdf4;\n  color: #16a34a;\n  border: 1px solid #bbf7d0;\n}\n.error-message[_ngcontent-%COMP%] {\n  color: #ef4444;\n  font-size: 0.75rem;\n  margin-top: 0.25rem;\n  font-weight: 500;\n}\n.login-button[_ngcontent-%COMP%], \n.verify-button[_ngcontent-%COMP%] {\n  width: 100%;\n  padding: 1rem;\n  background: #1e293b;\n  color: white;\n  border: none;\n  border-radius: 8px;\n  font-size: 1rem;\n  font-weight: 600;\n  cursor: pointer;\n  transition: all 0.2s ease;\n}\n.login-button[_ngcontent-%COMP%]:hover:not(:disabled), \n.verify-button[_ngcontent-%COMP%]:hover:not(:disabled) {\n  background: #334155;\n  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);\n}\n.login-button[_ngcontent-%COMP%]:disabled, \n.verify-button[_ngcontent-%COMP%]:disabled {\n  opacity: 0.6;\n  cursor: not-allowed;\n  background: #94a3b8;\n}\n.button-content[_ngcontent-%COMP%] {\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  gap: 0.5rem;\n}\n.button-icon[_ngcontent-%COMP%] {\n  font-size: 1.125rem;\n}\n.loading-spinner[_ngcontent-%COMP%] {\n  width: 20px;\n  height: 20px;\n  border: 2px solid rgba(255, 255, 255, 0.3);\n  border-top: 2px solid white;\n  border-radius: 50%;\n}\n.otp-buttons[_ngcontent-%COMP%] {\n  display: flex;\n  gap: 1rem;\n  margin-top: 1rem;\n}\n.back-button[_ngcontent-%COMP%] {\n  flex: 1;\n  padding: 0.875rem;\n  background: #f8fafc;\n  color: #64748b;\n  border: 2px solid #e2e8f0;\n  border-radius: 12px;\n  font-size: 0.875rem;\n  font-weight: 500;\n  cursor: pointer;\n  transition: all 0.3s ease;\n}\n.back-button[_ngcontent-%COMP%]:hover:not(:disabled) {\n  background: #e2e8f0;\n  color: #475569;\n}\n.verify-button[_ngcontent-%COMP%] {\n  flex: 2;\n}\n.login-info[_ngcontent-%COMP%] {\n  margin-top: 2rem;\n  padding-top: 1.5rem;\n  border-top: 1px solid #e5e7eb;\n  text-align: center;\n}\n.info-title[_ngcontent-%COMP%] {\n  font-size: 0.875rem;\n  color: #64748b;\n  margin: 0 0 1rem 0;\n  font-weight: 500;\n}\n.info-content[_ngcontent-%COMP%] {\n  text-align: center;\n}\n.info-content[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\n  font-size: 0.75rem;\n  color: #64748b;\n  margin: 0.5rem 0;\n}\n.info-content[_ngcontent-%COMP%]   strong[_ngcontent-%COMP%] {\n  color: #374151;\n  font-weight: 600;\n}\n.login-footer[_ngcontent-%COMP%] {\n  text-align: center;\n  margin-top: 2rem;\n  color: rgba(255, 255, 255, 0.8);\n}\n.footer-text[_ngcontent-%COMP%] {\n  font-size: 0.75rem;\n  margin: 0 0 1rem 0;\n}\n.security-badges[_ngcontent-%COMP%] {\n  display: flex;\n  justify-content: center;\n  gap: 1rem;\n}\n.security-badge[_ngcontent-%COMP%] {\n  font-size: 0.75rem;\n  padding: 0.25rem 0.5rem;\n  background: rgba(255, 255, 255, 0.1);\n  border-radius: 12px;\n  -webkit-backdrop-filter: blur(10px);\n  backdrop-filter: blur(10px);\n}\n@media (max-width: 768px) {\n  .login-container[_ngcontent-%COMP%] {\n    padding: 1rem;\n    max-width: 100%;\n  }\n  .login-card[_ngcontent-%COMP%] {\n    padding: 2rem;\n  }\n  .logo-title[_ngcontent-%COMP%] {\n    font-size: 1.5rem;\n  }\n  .otp-buttons[_ngcontent-%COMP%] {\n    flex-direction: column;\n  }\n  .security-badges[_ngcontent-%COMP%] {\n    flex-wrap: wrap;\n    gap: 0.5rem;\n  }\n}\n/*# sourceMappingURL=admin-login.component.css.map */'] });
};
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && setClassMetadata(AdminLoginComponent, [{
    type: Component,
    args: [{ selector: "app-admin-login", standalone: true, imports: [CommonModule, ReactiveFormsModule], template: `<div class="admin-login-page">
  <!-- Background avec animations -->
  <div class="login-background">
    <div class="animated-shapes">
      <div class="shape shape-1"></div>
      <div class="shape shape-2"></div>
      <div class="shape shape-3"></div>
      <div class="shape shape-4"></div>
    </div>
  </div>

  <!-- Container principal -->
  <div class="login-container" [class.visible]="isFormVisible">
    
    <!-- Logo et titre -->
    <div class="login-header">
      <div class="logo-container">
        <div class="logo-icon">
          <svg width="48" height="48" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
            <path d="M12 2L2 7l10 5 10-5-10-5z"></path>
            <path d="M2 17l10 5 10-5"></path>
            <path d="M2 12l10 5 10-5"></path>
          </svg>
        </div>
        <h1 class="logo-title">Admin Panel</h1>
      </div>
      <p class="login-subtitle">Acc\xE8s s\xE9curis\xE9 \xE0 l'administration</p>
    </div>

    <!-- Formulaire de connexion -->
    <div class="login-card" *ngIf="!showOTPForm">
      <div class="card-header">
        <h2>Connexion Administrateur</h2>
        <p>Connectez-vous avec vos identifiants s\xE9curis\xE9s</p>
      </div>

      <form [formGroup]="loginForm" (ngSubmit)="onLogin()" class="login-form">
        <!-- Username -->
        <div class="form-group">
          <label for="email" class="form-label">
            <span class="label-icon">\u{1F464}</span>
            Nom d'utilisateur
          </label>
          <input
            type="text"
            id="email"
            formControlName="email"
            class="form-control"
            [class.error]="hasFieldError(loginForm, 'email')"
            placeholder="adminOptiLet"
            autocomplete="username"
          />
          <div class="error-message" *ngIf="hasFieldError(loginForm, 'email')">
            {{ getFieldError(loginForm, 'email') }}
          </div>
        </div>

        <!-- Mot de passe -->
        <div class="form-group">
          <label for="password" class="form-label">
            <span class="label-icon">\u{1F512}</span>
            Mot de passe
          </label>
          <input
            type="password"
            id="password"
            formControlName="password"
            class="form-control"
            [class.error]="hasFieldError(loginForm, 'password')"
            placeholder="\u2022\u2022\u2022\u2022\u2022\u2022\u2022\u2022"
            autocomplete="current-password"
          />
          <div class="error-message" *ngIf="hasFieldError(loginForm, 'password')">
            {{ getFieldError(loginForm, 'password') }}
          </div>
        </div>

        <!-- Messages -->
        <div class="message-container">
          <div class="error-alert" *ngIf="errorMessage">
            <span class="alert-icon">\u26A0\uFE0F</span>
            {{ errorMessage }}
          </div>
          <div class="success-alert" *ngIf="successMessage">
            <span class="alert-icon">\u2705</span>
            {{ successMessage }}
          </div>
        </div>

        <!-- Bouton de connexion -->
        <button
          type="submit"
          class="login-button"
          [disabled]="isLoading"
          [class.loading]="isLoading"
        >
          <span class="button-content" *ngIf="!isLoading">
            <span class="button-icon">\u{1F680}</span>
            Se connecter
          </span>
          <span class="loading-spinner" *ngIf="isLoading"></span>
        </button>
      </form>
    </div>

    <!-- Formulaire OTP -->
    <div class="login-card otp-card" *ngIf="showOTPForm">
      <div class="card-header">
        <h2>Authentification \xE0 deux facteurs</h2>
        <p>Saisissez le code \xE0 6 chiffres envoy\xE9 \xE0 <strong>{{ userEmail }}</strong></p>
      </div>

      <form [formGroup]="otpForm" (ngSubmit)="onVerifyOTP()" class="otp-form">
        <!-- Code OTP -->
        <div class="form-group">
          <label for="otpCode" class="form-label">
            <span class="label-icon">\u{1F510}</span>
            Code de v\xE9rification
          </label>
          <input
            type="text"
            id="otpCode"
            formControlName="otpCode"
            class="form-control otp-input"
            [class.error]="hasFieldError(otpForm, 'otpCode')"
            placeholder="123456"
            maxlength="6"
            autocomplete="one-time-code"
          />
          <div class="error-message" *ngIf="hasFieldError(otpForm, 'otpCode')">
            {{ getFieldError(otpForm, 'otpCode') }}
          </div>
        </div>

        <!-- Messages -->
        <div class="message-container">
          <div class="error-alert" *ngIf="errorMessage">
            <span class="alert-icon">\u26A0\uFE0F</span>
            {{ errorMessage }}
          </div>
          <div class="success-alert" *ngIf="successMessage">
            <span class="alert-icon">\u2705</span>
            {{ successMessage }}
          </div>
        </div>

        <!-- Boutons -->
        <div class="otp-buttons">
          <button
            type="button"
            class="back-button"
            (click)="backToLogin()"
            [disabled]="isLoading"
          >
            \u2190 Retour
          </button>
          
          <button
            type="submit"
            class="verify-button"
            [disabled]="isLoading"
            [class.loading]="isLoading"
          >
            <span class="button-content" *ngIf="!isLoading">
              <span class="button-icon">\u{1F513}</span>
              V\xE9rifier
            </span>
            <span class="loading-spinner" *ngIf="isLoading"></span>
          </button>
        </div>


      </form>
    </div>

    <!-- Footer -->
    <div class="login-footer">
      <p class="footer-text">
        \xA9 2024 Optique Vision - Administration s\xE9curis\xE9e
      </p>
      <div class="security-badges">
        <span class="security-badge">\u{1F512} SSL</span>
        <span class="security-badge">\u{1F6E1}\uFE0F 2FA</span>
        <span class="security-badge">\u{1F4CA} Audit</span>
      </div>
    </div>
  </div>
</div>
`, styles: ['/* src/app/components/admin/auth/admin-login/admin-login.component.css */\n.admin-login-page {\n  min-height: 100vh;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  background:\n    linear-gradient(\n      135deg,\n      #f1f5f9 0%,\n      #e2e8f0 100%);\n  padding: 2rem;\n}\n.login-container {\n  width: 100%;\n  max-width: 450px;\n  background: rgba(255, 255, 255, 0.98);\n  -webkit-backdrop-filter: blur(10px);\n  backdrop-filter: blur(10px);\n  border: 1px solid rgba(255, 255, 255, 0.2);\n  border-radius: 16px;\n  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);\n  padding: 3rem;\n  position: relative;\n}\n.login-container::before {\n  content: "";\n  position: absolute;\n  top: 0;\n  left: 0;\n  right: 0;\n  height: 4px;\n  background:\n    linear-gradient(\n      90deg,\n      #1e293b 0%,\n      #475569 100%);\n  border-radius: 16px 16px 0 0;\n}\n.login-header {\n  text-align: center;\n  margin-bottom: 2rem;\n}\n.logo-container {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  gap: 1rem;\n  margin-bottom: 2rem;\n}\n.logo-icon {\n  width: 60px;\n  height: 60px;\n  background: #1e293b;\n  border-radius: 12px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  color: white;\n  font-size: 1.5rem;\n}\n.logo-title {\n  font-size: 1.75rem;\n  font-weight: 700;\n  color: #1e293b;\n  margin: 0;\n}\n.login-subtitle {\n  color: #64748b;\n  font-size: 0.875rem;\n  margin: 0;\n  font-weight: 500;\n}\n.card-header {\n  text-align: center;\n  margin-bottom: 2rem;\n}\n.card-header h2 {\n  font-size: 1.5rem;\n  font-weight: 600;\n  color: #1e293b;\n  margin: 0 0 0.5rem 0;\n}\n.card-header p {\n  color: #64748b;\n  font-size: 0.875rem;\n  margin: 0;\n}\n.login-form,\n.otp-form {\n  display: flex;\n  flex-direction: column;\n  gap: 1.5rem;\n}\n.form-group {\n  display: flex;\n  flex-direction: column;\n  gap: 0.5rem;\n}\n.form-label {\n  display: flex;\n  align-items: center;\n  gap: 0.5rem;\n  font-size: 0.875rem;\n  font-weight: 500;\n  color: #374151;\n}\n.label-icon {\n  font-size: 1rem;\n}\n.form-control {\n  padding: 1rem 1.25rem;\n  border: 1px solid #d1d5db;\n  border-radius: 8px;\n  font-size: 1rem;\n  transition: all 0.2s ease;\n  background: white;\n}\n.form-control:focus {\n  outline: none;\n  border-color: #1e293b;\n  box-shadow: 0 0 0 3px rgba(30, 41, 59, 0.1);\n}\n.form-control.error {\n  border-color: #ef4444;\n  box-shadow: 0 0 0 3px rgba(239, 68, 68, 0.1);\n}\n.otp-input {\n  text-align: center;\n  font-size: 1.5rem;\n  font-weight: 600;\n  letter-spacing: 0.5rem;\n  font-family: monospace;\n}\n.checkbox-group {\n  margin: 0.5rem 0;\n}\n.checkbox-label {\n  display: flex;\n  align-items: center;\n  gap: 0.75rem;\n  cursor: pointer;\n  font-size: 0.875rem;\n  color: #374151;\n}\n.checkbox-input {\n  display: none;\n}\n.checkbox-custom {\n  width: 20px;\n  height: 20px;\n  border: 2px solid #d1d5db;\n  border-radius: 4px;\n  position: relative;\n  transition: all 0.3s ease;\n}\n.checkbox-input:checked + .checkbox-custom {\n  background: #667eea;\n  border-color: #667eea;\n}\n.checkbox-input:checked + .checkbox-custom::after {\n  content: "\\2713";\n  position: absolute;\n  top: 50%;\n  left: 50%;\n  transform: translate(-50%, -50%);\n  color: white;\n  font-size: 12px;\n  font-weight: bold;\n}\n.message-container {\n  margin: 1rem 0;\n}\n.error-alert,\n.success-alert {\n  display: flex;\n  align-items: center;\n  gap: 0.5rem;\n  padding: 0.75rem 1rem;\n  border-radius: 8px;\n  font-size: 0.875rem;\n  font-weight: 500;\n}\n.error-alert {\n  background: #fef2f2;\n  color: #dc2626;\n  border: 1px solid #fecaca;\n}\n.success-alert {\n  background: #f0fdf4;\n  color: #16a34a;\n  border: 1px solid #bbf7d0;\n}\n.error-message {\n  color: #ef4444;\n  font-size: 0.75rem;\n  margin-top: 0.25rem;\n  font-weight: 500;\n}\n.login-button,\n.verify-button {\n  width: 100%;\n  padding: 1rem;\n  background: #1e293b;\n  color: white;\n  border: none;\n  border-radius: 8px;\n  font-size: 1rem;\n  font-weight: 600;\n  cursor: pointer;\n  transition: all 0.2s ease;\n}\n.login-button:hover:not(:disabled),\n.verify-button:hover:not(:disabled) {\n  background: #334155;\n  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);\n}\n.login-button:disabled,\n.verify-button:disabled {\n  opacity: 0.6;\n  cursor: not-allowed;\n  background: #94a3b8;\n}\n.button-content {\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  gap: 0.5rem;\n}\n.button-icon {\n  font-size: 1.125rem;\n}\n.loading-spinner {\n  width: 20px;\n  height: 20px;\n  border: 2px solid rgba(255, 255, 255, 0.3);\n  border-top: 2px solid white;\n  border-radius: 50%;\n}\n.otp-buttons {\n  display: flex;\n  gap: 1rem;\n  margin-top: 1rem;\n}\n.back-button {\n  flex: 1;\n  padding: 0.875rem;\n  background: #f8fafc;\n  color: #64748b;\n  border: 2px solid #e2e8f0;\n  border-radius: 12px;\n  font-size: 0.875rem;\n  font-weight: 500;\n  cursor: pointer;\n  transition: all 0.3s ease;\n}\n.back-button:hover:not(:disabled) {\n  background: #e2e8f0;\n  color: #475569;\n}\n.verify-button {\n  flex: 2;\n}\n.login-info {\n  margin-top: 2rem;\n  padding-top: 1.5rem;\n  border-top: 1px solid #e5e7eb;\n  text-align: center;\n}\n.info-title {\n  font-size: 0.875rem;\n  color: #64748b;\n  margin: 0 0 1rem 0;\n  font-weight: 500;\n}\n.info-content {\n  text-align: center;\n}\n.info-content p {\n  font-size: 0.75rem;\n  color: #64748b;\n  margin: 0.5rem 0;\n}\n.info-content strong {\n  color: #374151;\n  font-weight: 600;\n}\n.login-footer {\n  text-align: center;\n  margin-top: 2rem;\n  color: rgba(255, 255, 255, 0.8);\n}\n.footer-text {\n  font-size: 0.75rem;\n  margin: 0 0 1rem 0;\n}\n.security-badges {\n  display: flex;\n  justify-content: center;\n  gap: 1rem;\n}\n.security-badge {\n  font-size: 0.75rem;\n  padding: 0.25rem 0.5rem;\n  background: rgba(255, 255, 255, 0.1);\n  border-radius: 12px;\n  -webkit-backdrop-filter: blur(10px);\n  backdrop-filter: blur(10px);\n}\n@media (max-width: 768px) {\n  .login-container {\n    padding: 1rem;\n    max-width: 100%;\n  }\n  .login-card {\n    padding: 2rem;\n  }\n  .logo-title {\n    font-size: 1.5rem;\n  }\n  .otp-buttons {\n    flex-direction: column;\n  }\n  .security-badges {\n    flex-wrap: wrap;\n    gap: 0.5rem;\n  }\n}\n/*# sourceMappingURL=admin-login.component.css.map */\n'] }]
  }], () => [{ type: FormBuilder }, { type: AdminAuthService }, { type: Router }], null);
})();
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && \u0275setClassDebugInfo(AdminLoginComponent, { className: "AdminLoginComponent", filePath: "src/app/components/admin/auth/admin-login/admin-login.component.ts", lineNumber: 15 });
})();
export {
  AdminLoginComponent
};
//# sourceMappingURL=chunk-CEJLOO4X.js.map

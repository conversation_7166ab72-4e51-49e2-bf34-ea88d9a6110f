import {
  AuthService
} from "./chunk-ZSXXNL7U.js";
import {
  FormsModule,
  NgSelectOption,
  ɵNgSelectMultipleOption
} from "./chunk-HQBVYEOO.js";
import {
  environment
} from "./chunk-GFHHPDQ6.js";
import {
  HttpClient,
  HttpParams
} from "./chunk-7JDDWGD3.js";
import {
  CommonModule,
  Component,
  Injectable,
  computed,
  map,
  setClassMetadata,
  signal,
  tap,
  ɵsetClassDebugInfo,
  ɵɵadvance,
  ɵɵclassMap,
  ɵɵclassProp,
  ɵɵconditional,
  ɵɵdefineComponent,
  ɵɵdefineInjectable,
  ɵɵdirectiveInject,
  ɵɵelement,
  ɵɵelementEnd,
  ɵɵelementStart,
  ɵɵgetCurrentView,
  ɵɵinject,
  ɵɵlistener,
  ɵɵnextContext,
  ɵɵproperty,
  ɵɵpureFunction0,
  ɵɵrepeater,
  ɵɵrepeaterCreate,
  ɵɵrepeaterTrackByIndex,
  ɵɵresetView,
  ɵɵrestoreView,
  ɵɵtemplate,
  ɵɵtext,
  ɵɵtextInterpolate,
  ɵɵtextInterpolate1,
  ɵɵtextInterpolate3
} from "./chunk-UBZQS7JS.js";

// src/app/models/commande.model.ts
var StatutCommande;
(function(StatutCommande2) {
  StatutCommande2["EnAttente"] = "EnAttente";
  StatutCommande2["Confirmee"] = "Confirmee";
  StatutCommande2["EnPreparation"] = "EnPreparation";
  StatutCommande2["Expediee"] = "Expediee";
  StatutCommande2["Livree"] = "Livree";
  StatutCommande2["Annulee"] = "Annulee";
})(StatutCommande || (StatutCommande = {}));
var StatutCommandeFournisseur;
(function(StatutCommandeFournisseur2) {
  StatutCommandeFournisseur2["Nouvelle"] = "Nouvelle";
  StatutCommandeFournisseur2["Acceptee"] = "Acceptee";
  StatutCommandeFournisseur2["EnPreparation"] = "EnPreparation";
  StatutCommandeFournisseur2["Prete"] = "Prete";
  StatutCommandeFournisseur2["Expediee"] = "Expediee";
  StatutCommandeFournisseur2["Livree"] = "Livree";
  StatutCommandeFournisseur2["Refusee"] = "Refusee";
})(StatutCommandeFournisseur || (StatutCommandeFournisseur = {}));
var StatutLivraison;
(function(StatutLivraison2) {
  StatutLivraison2["EnAttente"] = "EnAttente";
  StatutLivraison2["EnCours"] = "EnCours";
  StatutLivraison2["Livree"] = "Livree";
  StatutLivraison2["Echec"] = "Echec";
  StatutLivraison2["Retournee"] = "Retournee";
})(StatutLivraison || (StatutLivraison = {}));

// src/app/services/commande.service.ts
var CommandeService = class _CommandeService {
  http;
  API_URL = environment.apiUrl || "https://localhost:7264/api";
  constructor(http) {
    this.http = http;
  }
  // ===== COMMANDES FOURNISSEUR =====
  /**
   * GET /api/CommandeFournisseur - Obtenir toutes les commandes fournisseur avec filtres
   */
  getCommandesFournisseur(filters) {
    let params = new HttpParams();
    if (filters) {
      Object.keys(filters).forEach((key) => {
        if (filters[key] !== null && filters[key] !== void 0) {
          params = params.set(key, filters[key].toString());
        }
      });
    }
    console.log("\u{1F4CB} R\xE9cup\xE9ration des commandes fournisseur avec filtres:", filters);
    return this.http.get(`${this.API_URL}/CommandeFournisseur`, { params }).pipe(tap((response) => console.log("\u2705 Commandes fournisseur r\xE9cup\xE9r\xE9es:", response)));
  }
  /**
   * POST /api/CommandeFournisseur - Créer une commande fournisseur
   */
  createCommandeFournisseur(commande) {
    console.log("\u2795 Cr\xE9ation d'une nouvelle commande fournisseur:", commande);
    return this.http.post(`${this.API_URL}/CommandeFournisseur`, commande).pipe(tap((response) => console.log("\u2705 Commande fournisseur cr\xE9\xE9e:", response)));
  }
  /**
   * GET /api/CommandeFournisseur/{id} - Obtenir une commande fournisseur par ID
   */
  getCommandeFournisseurById(id) {
    console.log("\u{1F50D} R\xE9cup\xE9ration de la commande fournisseur ID:", id);
    return this.http.get(`${this.API_URL}/CommandeFournisseur/${id}`).pipe(tap((response) => console.log("\u2705 Commande fournisseur r\xE9cup\xE9r\xE9e:", response)));
  }
  /**
   * PUT /api/CommandeFournisseur/{id}/statut - Mettre à jour le statut d'une commande fournisseur
   */
  updateStatutCommandeFournisseur(id, request) {
    console.log("\u{1F504} Mise \xE0 jour du statut de la commande fournisseur ID:", id, request);
    return this.http.put(`${this.API_URL}/CommandeFournisseur/${id}/statut`, request).pipe(tap((response) => console.log("\u2705 Statut de la commande fournisseur mis \xE0 jour:", response)));
  }
  // ===== LIGNES COMMANDE FOURNISSEUR =====
  /**
   * Obtenir toutes les lignes de commande
   */
  getLignesCommandeFournisseur() {
    return this.http.get(`${this.API_URL}/LignesCommandeFournisseur`);
  }
  /**
   * Créer une ligne de commande
   */
  createLigneCommandeFournisseur(ligne) {
    return this.http.post(`${this.API_URL}/LignesCommandeFournisseur`, ligne);
  }
  /**
   * Obtenir une ligne de commande par ID
   */
  getLigneCommandeFournisseurById(id) {
    return this.http.get(`${this.API_URL}/LignesCommandeFournisseur/${id}`);
  }
  /**
   * Mettre à jour une ligne de commande
   */
  updateLigneCommandeFournisseur(id, ligne) {
    return this.http.put(`${this.API_URL}/LignesCommandeFournisseur/${id}`, ligne);
  }
  /**
   * Supprimer une ligne de commande
   */
  deleteLigneCommandeFournisseur(id) {
    return this.http.delete(`${this.API_URL}/LignesCommandeFournisseur/${id}`);
  }
  // ===== LIVRAISONS =====
  /**
   * Obtenir une livraison par ID
   */
  getLivraisonById(id) {
    return this.http.get(`${this.API_URL}/Livraison/${id}`);
  }
  /**
   * Mettre à jour une livraison
   */
  updateLivraison(id, livraison) {
    return this.http.put(`${this.API_URL}/Livraison/${id}`, livraison);
  }
  /**
   * Obtenir la livraison d'une commande
   */
  getLivraisonByCommande(commandeId) {
    return this.http.get(`${this.API_URL}/Livraison/commande/${commandeId}`);
  }
  /**
   * Créer une livraison
   */
  createLivraison(livraison) {
    return this.http.post(`${this.API_URL}/Livraison`, livraison);
  }
  /**
   * Mettre à jour le statut d'une livraison
   */
  updateStatutLivraison(livraisonId, statut) {
    return this.http.post(`${this.API_URL}/Livraison/${livraisonId}/statut`, { statut });
  }
  /**
   * Obtenir les statuts disponibles pour une livraison
   */
  getStatutsDisponibles(livraisonId) {
    return this.http.get(`${this.API_URL}/Livraison/${livraisonId}/statuts-disponibles`);
  }
  /**
   * Obtenir l'étiquette d'une livraison
   */
  getEtiquetteLivraison(livraisonId) {
    return this.http.get(`${this.API_URL}/Livraison/${livraisonId}/etiquette`, {
      responseType: "blob"
    });
  }
  // ===== MÉTHODES UTILITAIRES =====
  /**
   * Obtenir les commandes d'un fournisseur spécifique
   */
  getCommandesByFournisseur(fournisseurId, statut) {
    let params = new HttpParams();
    if (statut) {
      params = params.set("statut", statut);
    }
    console.log("\u{1F50D} Appel API getCommandesByFournisseur avec fournisseurId:", fournisseurId);
    return this.http.get(`${this.API_URL}/CommandeFournisseur`, {
      params: params.set("fournisseurId", fournisseurId.toString())
    }).pipe(map((response) => {
      console.log("\u{1F4E6} R\xE9ponse API brute:", response);
      const commandes = response.value || [];
      console.log("\u{1F4CB} Commandes extraites:", commandes);
      commandes.forEach((commande) => {
        console.log(`\u{1F4E6} Commande ${commande.id}: ${commande.lignesCommande?.length || 0} lignes`);
      });
      return commandes;
    }), tap((commandes) => console.log("\u2705 Commandes finales retourn\xE9es:", commandes)));
  }
  /**
   * Obtenir les livraisons d'un fournisseur
   */
  getLivraisonsByFournisseur(fournisseurId) {
    return this.http.get(`${this.API_URL}/Livraison`, {
      params: new HttpParams().set("fournisseurId", fournisseurId.toString())
    });
  }
  /**
   * Calculer le total d'une commande
   */
  calculateTotal(lignes) {
    return lignes.reduce((total, ligne) => total + ligne.montantTotal, 0);
  }
  /**
   * Vérifier si une commande peut être modifiée
   */
  canModifyCommande(statut) {
    return [StatutCommandeFournisseur.Nouvelle, StatutCommandeFournisseur.Acceptee].includes(statut);
  }
  static \u0275fac = function CommandeService_Factory(__ngFactoryType__) {
    return new (__ngFactoryType__ || _CommandeService)(\u0275\u0275inject(HttpClient));
  };
  static \u0275prov = /* @__PURE__ */ \u0275\u0275defineInjectable({ token: _CommandeService, factory: _CommandeService.\u0275fac, providedIn: "root" });
};
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && setClassMetadata(CommandeService, [{
    type: Injectable,
    args: [{
      providedIn: "root"
    }]
  }], () => [{ type: HttpClient }], null);
})();

// src/app/components/orders/orders.component.ts
var _c0 = () => [];
var _forTrack0 = ($index, $item) => $item.id;
function OrdersComponent_Conditional_36_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "div", 20);
    \u0275\u0275element(1, "div", 22);
    \u0275\u0275elementStart(2, "p");
    \u0275\u0275text(3, "Chargement des commandes...");
    \u0275\u0275elementEnd()();
  }
}
function OrdersComponent_Conditional_37_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "div", 21)(1, "strong");
    \u0275\u0275text(2, "\u274C Erreur:");
    \u0275\u0275elementEnd();
    \u0275\u0275text(3);
    \u0275\u0275elementEnd();
  }
  if (rf & 2) {
    const ctx_r0 = \u0275\u0275nextContext();
    \u0275\u0275advance(3);
    \u0275\u0275textInterpolate1(" ", ctx_r0.error(), " ");
  }
}
function OrdersComponent_Conditional_38_Conditional_1_Conditional_3_Template(rf, ctx) {
  if (rf & 1) {
    const _r2 = \u0275\u0275getCurrentView();
    \u0275\u0275elementStart(0, "button", 27);
    \u0275\u0275listener("click", function OrdersComponent_Conditional_38_Conditional_1_Conditional_3_Template_button_click_0_listener() {
      \u0275\u0275restoreView(_r2);
      const ctx_r0 = \u0275\u0275nextContext(3);
      return \u0275\u0275resetView(ctx_r0.clearFilters());
    });
    \u0275\u0275text(1, " Voir toutes les commandes ");
    \u0275\u0275elementEnd();
  }
}
function OrdersComponent_Conditional_38_Conditional_1_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "div", 24)(1, "p");
    \u0275\u0275text(2, "\u{1F4ED} Aucune commande trouv\xE9e");
    \u0275\u0275elementEnd();
    \u0275\u0275template(3, OrdersComponent_Conditional_38_Conditional_1_Conditional_3_Template, 2, 0, "button", 26);
    \u0275\u0275elementEnd();
  }
  if (rf & 2) {
    const ctx_r0 = \u0275\u0275nextContext(2);
    \u0275\u0275advance(3);
    \u0275\u0275conditional(ctx_r0.searchTerm() || ctx_r0.selectedStatus() ? 3 : -1);
  }
}
function OrdersComponent_Conditional_38_Conditional_2_For_1_Conditional_16_Conditional_21_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "div", 41)(1, "strong");
    \u0275\u0275text(2, "Bon de livraison:");
    \u0275\u0275elementEnd();
    \u0275\u0275text(3);
    \u0275\u0275elementEnd();
  }
  if (rf & 2) {
    const order_r4 = \u0275\u0275nextContext(2).$implicit;
    \u0275\u0275advance(3);
    \u0275\u0275textInterpolate1(" ", order_r4.numeroBonLivraison, " ");
  }
}
function OrdersComponent_Conditional_38_Conditional_2_For_1_Conditional_16_Conditional_25_For_2_Conditional_8_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "span", 56);
    \u0275\u0275text(1, "\u{1F5BC}\uFE0F Image disponible");
    \u0275\u0275elementEnd();
  }
}
function OrdersComponent_Conditional_38_Conditional_2_For_1_Conditional_16_Conditional_25_For_2_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "div", 51)(1, "div", 52)(2, "div", 53)(3, "strong");
    \u0275\u0275text(4);
    \u0275\u0275elementEnd()();
    \u0275\u0275elementStart(5, "div", 54)(6, "span", 55);
    \u0275\u0275text(7);
    \u0275\u0275elementEnd();
    \u0275\u0275template(8, OrdersComponent_Conditional_38_Conditional_2_For_1_Conditional_16_Conditional_25_For_2_Conditional_8_Template, 2, 0, "span", 56);
    \u0275\u0275elementEnd()();
    \u0275\u0275elementStart(9, "div", 57)(10, "span", 58);
    \u0275\u0275text(11);
    \u0275\u0275elementEnd()();
    \u0275\u0275elementStart(12, "div", 59)(13, "div", 60);
    \u0275\u0275text(14);
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(15, "div", 61)(16, "strong");
    \u0275\u0275text(17);
    \u0275\u0275elementEnd()()()();
  }
  if (rf & 2) {
    const ligne_r5 = ctx.$implicit;
    const ctx_r0 = \u0275\u0275nextContext(6);
    \u0275\u0275advance(4);
    \u0275\u0275textInterpolate(ligne_r5.nomProduit);
    \u0275\u0275advance(3);
    \u0275\u0275textInterpolate1("R\xE9f: ", ligne_r5.referenceProduit || "N/A", "");
    \u0275\u0275advance();
    \u0275\u0275conditional(ligne_r5.imagePrincipale ? 8 : -1);
    \u0275\u0275advance(3);
    \u0275\u0275textInterpolate1("Qt\xE9: ", ligne_r5.quantite, "");
    \u0275\u0275advance(3);
    \u0275\u0275textInterpolate1("", ctx_r0.formatPrice(ligne_r5.prixUnitaire), "/u");
    \u0275\u0275advance(3);
    \u0275\u0275textInterpolate(ctx_r0.formatPrice(ligne_r5.totalLigne));
  }
}
function OrdersComponent_Conditional_38_Conditional_2_For_1_Conditional_16_Conditional_25_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "div", 43);
    \u0275\u0275repeaterCreate(1, OrdersComponent_Conditional_38_Conditional_2_For_1_Conditional_16_Conditional_25_For_2_Template, 18, 6, "div", 51, _forTrack0);
    \u0275\u0275elementEnd();
  }
  if (rf & 2) {
    const order_r4 = \u0275\u0275nextContext(2).$implicit;
    \u0275\u0275advance();
    \u0275\u0275repeater(order_r4.lignesCommande);
  }
}
function OrdersComponent_Conditional_38_Conditional_2_For_1_Conditional_16_Conditional_26_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "p", 44);
    \u0275\u0275text(1, "Aucun produit trouv\xE9 pour cette commande");
    \u0275\u0275elementEnd();
  }
}
function OrdersComponent_Conditional_38_Conditional_2_For_1_Conditional_16_Conditional_31_Template(rf, ctx) {
  if (rf & 1) {
    const _r6 = \u0275\u0275getCurrentView();
    \u0275\u0275elementStart(0, "button", 62);
    \u0275\u0275listener("click", function OrdersComponent_Conditional_38_Conditional_2_For_1_Conditional_16_Conditional_31_Template_button_click_0_listener() {
      \u0275\u0275restoreView(_r6);
      const order_r4 = \u0275\u0275nextContext(2).$implicit;
      const ctx_r0 = \u0275\u0275nextContext(3);
      return \u0275\u0275resetView(ctx_r0.updateOrderStatus(order_r4.id, "Acceptee"));
    });
    \u0275\u0275text(1, " \u2705 Accepter ");
    \u0275\u0275elementEnd();
  }
}
function OrdersComponent_Conditional_38_Conditional_2_For_1_Conditional_16_Conditional_32_Template(rf, ctx) {
  if (rf & 1) {
    const _r7 = \u0275\u0275getCurrentView();
    \u0275\u0275elementStart(0, "button", 63);
    \u0275\u0275listener("click", function OrdersComponent_Conditional_38_Conditional_2_For_1_Conditional_16_Conditional_32_Template_button_click_0_listener() {
      \u0275\u0275restoreView(_r7);
      const order_r4 = \u0275\u0275nextContext(2).$implicit;
      const ctx_r0 = \u0275\u0275nextContext(3);
      return \u0275\u0275resetView(ctx_r0.updateOrderStatus(order_r4.id, "EnPreparation"));
    });
    \u0275\u0275text(1, " \u{1F4E6} Pr\xE9parer ");
    \u0275\u0275elementEnd();
  }
}
function OrdersComponent_Conditional_38_Conditional_2_For_1_Conditional_16_Conditional_33_Template(rf, ctx) {
  if (rf & 1) {
    const _r8 = \u0275\u0275getCurrentView();
    \u0275\u0275elementStart(0, "button", 64);
    \u0275\u0275listener("click", function OrdersComponent_Conditional_38_Conditional_2_For_1_Conditional_16_Conditional_33_Template_button_click_0_listener() {
      \u0275\u0275restoreView(_r8);
      const order_r4 = \u0275\u0275nextContext(2).$implicit;
      const ctx_r0 = \u0275\u0275nextContext(3);
      return \u0275\u0275resetView(ctx_r0.updateOrderStatus(order_r4.id, "Expediee"));
    });
    \u0275\u0275text(1, " \u{1F69A} Exp\xE9dier ");
    \u0275\u0275elementEnd();
  }
}
function OrdersComponent_Conditional_38_Conditional_2_For_1_Conditional_16_Conditional_34_Template(rf, ctx) {
  if (rf & 1) {
    const _r9 = \u0275\u0275getCurrentView();
    \u0275\u0275elementStart(0, "button", 27);
    \u0275\u0275listener("click", function OrdersComponent_Conditional_38_Conditional_2_For_1_Conditional_16_Conditional_34_Template_button_click_0_listener() {
      \u0275\u0275restoreView(_r9);
      const order_r4 = \u0275\u0275nextContext(2).$implicit;
      const ctx_r0 = \u0275\u0275nextContext(3);
      return \u0275\u0275resetView(ctx_r0.updateOrderStatus(order_r4.id, "Livree"));
    });
    \u0275\u0275text(1, " \u2705 Marquer comme livr\xE9e ");
    \u0275\u0275elementEnd();
  }
}
function OrdersComponent_Conditional_38_Conditional_2_For_1_Conditional_16_Conditional_35_Template(rf, ctx) {
  if (rf & 1) {
    const _r10 = \u0275\u0275getCurrentView();
    \u0275\u0275elementStart(0, "button", 65);
    \u0275\u0275listener("click", function OrdersComponent_Conditional_38_Conditional_2_For_1_Conditional_16_Conditional_35_Template_button_click_0_listener() {
      \u0275\u0275restoreView(_r10);
      const order_r4 = \u0275\u0275nextContext(2).$implicit;
      const ctx_r0 = \u0275\u0275nextContext(3);
      return \u0275\u0275resetView(ctx_r0.updateOrderStatus(order_r4.id, "Refusee"));
    });
    \u0275\u0275text(1, " \u274C Refuser ");
    \u0275\u0275elementEnd();
  }
}
function OrdersComponent_Conditional_38_Conditional_2_For_1_Conditional_16_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "div", 38)(1, "div", 39)(2, "h4");
    \u0275\u0275text(3, "\u{1F4CB} Informations Commande");
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(4, "div", 40)(5, "div", 41)(6, "strong");
    \u0275\u0275text(7, "R\xE9f\xE9rence:");
    \u0275\u0275elementEnd();
    \u0275\u0275text(8);
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(9, "div", 41)(10, "strong");
    \u0275\u0275text(11, "Fournisseur:");
    \u0275\u0275elementEnd();
    \u0275\u0275text(12);
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(13, "div", 41)(14, "strong");
    \u0275\u0275text(15, "Matricule:");
    \u0275\u0275elementEnd();
    \u0275\u0275text(16);
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(17, "div", 41)(18, "strong");
    \u0275\u0275text(19, "Frais livraison:");
    \u0275\u0275elementEnd();
    \u0275\u0275text(20);
    \u0275\u0275elementEnd();
    \u0275\u0275template(21, OrdersComponent_Conditional_38_Conditional_2_For_1_Conditional_16_Conditional_21_Template, 4, 1, "div", 41);
    \u0275\u0275elementEnd()();
    \u0275\u0275elementStart(22, "div", 42)(23, "h4");
    \u0275\u0275text(24, "\u{1F6CD}\uFE0F Produits Command\xE9s");
    \u0275\u0275elementEnd();
    \u0275\u0275template(25, OrdersComponent_Conditional_38_Conditional_2_For_1_Conditional_16_Conditional_25_Template, 3, 0, "div", 43)(26, OrdersComponent_Conditional_38_Conditional_2_For_1_Conditional_16_Conditional_26_Template, 2, 0, "p", 44);
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(27, "div", 45)(28, "h4");
    \u0275\u0275text(29, "\u26A1 Actions");
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(30, "div", 46);
    \u0275\u0275template(31, OrdersComponent_Conditional_38_Conditional_2_For_1_Conditional_16_Conditional_31_Template, 2, 0, "button", 47)(32, OrdersComponent_Conditional_38_Conditional_2_For_1_Conditional_16_Conditional_32_Template, 2, 0, "button", 48)(33, OrdersComponent_Conditional_38_Conditional_2_For_1_Conditional_16_Conditional_33_Template, 2, 0, "button", 49)(34, OrdersComponent_Conditional_38_Conditional_2_For_1_Conditional_16_Conditional_34_Template, 2, 0, "button", 26)(35, OrdersComponent_Conditional_38_Conditional_2_For_1_Conditional_16_Conditional_35_Template, 2, 0, "button", 50);
    \u0275\u0275elementEnd()()();
  }
  if (rf & 2) {
    const order_r4 = \u0275\u0275nextContext().$implicit;
    const ctx_r0 = \u0275\u0275nextContext(3);
    \u0275\u0275advance(8);
    \u0275\u0275textInterpolate1(" ", order_r4.reference, " ");
    \u0275\u0275advance(4);
    \u0275\u0275textInterpolate1(" ", order_r4.nomFournisseur, " ");
    \u0275\u0275advance(4);
    \u0275\u0275textInterpolate1(" ", order_r4.matriculeFiscale || "Non renseign\xE9", " ");
    \u0275\u0275advance(4);
    \u0275\u0275textInterpolate1(" ", ctx_r0.formatPrice(order_r4.fraisLivraison), " ");
    \u0275\u0275advance();
    \u0275\u0275conditional(order_r4.numeroBonLivraison ? 21 : -1);
    \u0275\u0275advance(4);
    \u0275\u0275conditional(order_r4.lignesCommande && order_r4.lignesCommande.length > 0 ? 25 : 26);
    \u0275\u0275advance(6);
    \u0275\u0275conditional(ctx_r0.canConfirmOrder(order_r4.statut) ? 31 : -1);
    \u0275\u0275advance();
    \u0275\u0275conditional(ctx_r0.canPrepareOrder(order_r4.statut) ? 32 : -1);
    \u0275\u0275advance();
    \u0275\u0275conditional(ctx_r0.canShipOrder(order_r4.statut) ? 33 : -1);
    \u0275\u0275advance();
    \u0275\u0275conditional(ctx_r0.canDeliverOrder(order_r4.statut) ? 34 : -1);
    \u0275\u0275advance();
    \u0275\u0275conditional(ctx_r0.canCancelOrder(order_r4.statut) ? 35 : -1);
  }
}
function OrdersComponent_Conditional_38_Conditional_2_For_1_Template(rf, ctx) {
  if (rf & 1) {
    const _r3 = \u0275\u0275getCurrentView();
    \u0275\u0275elementStart(0, "div", 28)(1, "div", 29);
    \u0275\u0275listener("click", function OrdersComponent_Conditional_38_Conditional_2_For_1_Template_div_click_1_listener() {
      const order_r4 = \u0275\u0275restoreView(_r3).$implicit;
      const ctx_r0 = \u0275\u0275nextContext(3);
      return \u0275\u0275resetView(ctx_r0.toggleOrderDetails(order_r4.id));
    });
    \u0275\u0275elementStart(2, "div", 30)(3, "div", 31)(4, "strong");
    \u0275\u0275text(5);
    \u0275\u0275elementEnd()();
    \u0275\u0275elementStart(6, "div", 32)(7, "span", 33);
    \u0275\u0275text(8);
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(9, "span", 34);
    \u0275\u0275text(10);
    \u0275\u0275elementEnd()()();
    \u0275\u0275elementStart(11, "div", 35)(12, "span", 36);
    \u0275\u0275text(13);
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(14, "span", 37);
    \u0275\u0275text(15, " \u25BC ");
    \u0275\u0275elementEnd()()();
    \u0275\u0275template(16, OrdersComponent_Conditional_38_Conditional_2_For_1_Conditional_16_Template, 36, 11, "div", 38);
    \u0275\u0275elementEnd();
  }
  if (rf & 2) {
    const order_r4 = ctx.$implicit;
    const ctx_r0 = \u0275\u0275nextContext(3);
    \u0275\u0275advance(5);
    \u0275\u0275textInterpolate1("\u{1F4CB} ", order_r4.reference, "");
    \u0275\u0275advance(3);
    \u0275\u0275textInterpolate1("\u{1F4C5} ", ctx_r0.formatDate(order_r4.dateCreation), "");
    \u0275\u0275advance(2);
    \u0275\u0275textInterpolate1("\u{1F4B0} ", ctx_r0.formatPrice(order_r4.montantTotal), "");
    \u0275\u0275advance(2);
    \u0275\u0275classMap(ctx_r0.getStatusClass(order_r4.statut));
    \u0275\u0275advance();
    \u0275\u0275textInterpolate1(" ", ctx_r0.getStatusText(order_r4.statut), " ");
    \u0275\u0275advance();
    \u0275\u0275classProp("expanded", ctx_r0.isOrderExpanded(order_r4.id));
    \u0275\u0275advance(2);
    \u0275\u0275conditional(ctx_r0.isOrderExpanded(order_r4.id) ? 16 : -1);
  }
}
function OrdersComponent_Conditional_38_Conditional_2_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275repeaterCreate(0, OrdersComponent_Conditional_38_Conditional_2_For_1_Template, 17, 9, "div", 28, _forTrack0);
  }
  if (rf & 2) {
    const ctx_r0 = \u0275\u0275nextContext(2);
    \u0275\u0275repeater(ctx_r0.paginatedOrders());
  }
}
function OrdersComponent_Conditional_38_Conditional_3_For_7_Template(rf, ctx) {
  if (rf & 1) {
    const _r12 = \u0275\u0275getCurrentView();
    \u0275\u0275elementStart(0, "button", 70);
    \u0275\u0275listener("click", function OrdersComponent_Conditional_38_Conditional_3_For_7_Template_button_click_0_listener() {
      const $index_r13 = \u0275\u0275restoreView(_r12).$index;
      const ctx_r0 = \u0275\u0275nextContext(3);
      return \u0275\u0275resetView(ctx_r0.onPageChange($index_r13 + 1));
    });
    \u0275\u0275text(1);
    \u0275\u0275elementEnd();
  }
  if (rf & 2) {
    const $index_r13 = ctx.$index;
    const ctx_r0 = \u0275\u0275nextContext(3);
    \u0275\u0275classProp("btn-primary", $index_r13 + 1 === ctx_r0.currentPage())("btn-outline-primary", $index_r13 + 1 !== ctx_r0.currentPage());
    \u0275\u0275advance();
    \u0275\u0275textInterpolate1(" ", $index_r13 + 1, " ");
  }
}
function OrdersComponent_Conditional_38_Conditional_3_Template(rf, ctx) {
  if (rf & 1) {
    const _r11 = \u0275\u0275getCurrentView();
    \u0275\u0275elementStart(0, "div", 25)(1, "div", 66);
    \u0275\u0275text(2);
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(3, "div", 67)(4, "button", 68);
    \u0275\u0275listener("click", function OrdersComponent_Conditional_38_Conditional_3_Template_button_click_4_listener() {
      \u0275\u0275restoreView(_r11);
      const ctx_r0 = \u0275\u0275nextContext(2);
      return \u0275\u0275resetView(ctx_r0.onPageChange(ctx_r0.currentPage() - 1));
    });
    \u0275\u0275text(5, " \u2190 Pr\xE9c\xE9dent ");
    \u0275\u0275elementEnd();
    \u0275\u0275repeaterCreate(6, OrdersComponent_Conditional_38_Conditional_3_For_7_Template, 2, 5, "button", 69, \u0275\u0275repeaterTrackByIndex);
    \u0275\u0275elementStart(8, "button", 68);
    \u0275\u0275listener("click", function OrdersComponent_Conditional_38_Conditional_3_Template_button_click_8_listener() {
      \u0275\u0275restoreView(_r11);
      const ctx_r0 = \u0275\u0275nextContext(2);
      return \u0275\u0275resetView(ctx_r0.onPageChange(ctx_r0.currentPage() + 1));
    });
    \u0275\u0275text(9, " Suivant \u2192 ");
    \u0275\u0275elementEnd()()();
  }
  if (rf & 2) {
    const ctx_r0 = \u0275\u0275nextContext(2);
    \u0275\u0275advance(2);
    \u0275\u0275textInterpolate3(" Affichage ", (ctx_r0.currentPage() - 1) * ctx_r0.pageSize() + 1, " \xE0 ", ctx_r0.Math.min(ctx_r0.currentPage() * ctx_r0.pageSize(), ctx_r0.filteredOrders().length), " sur ", ctx_r0.filteredOrders().length, " commandes ");
    \u0275\u0275advance(2);
    \u0275\u0275property("disabled", ctx_r0.currentPage() === 1);
    \u0275\u0275advance(2);
    \u0275\u0275repeater(\u0275\u0275pureFunction0(5, _c0).constructor(ctx_r0.totalPages()));
    \u0275\u0275advance(2);
    \u0275\u0275property("disabled", ctx_r0.currentPage() === ctx_r0.totalPages());
  }
}
function OrdersComponent_Conditional_38_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "div", 23);
    \u0275\u0275template(1, OrdersComponent_Conditional_38_Conditional_1_Template, 4, 1, "div", 24)(2, OrdersComponent_Conditional_38_Conditional_2_Template, 2, 0);
    \u0275\u0275elementEnd();
    \u0275\u0275template(3, OrdersComponent_Conditional_38_Conditional_3_Template, 10, 6, "div", 25);
  }
  if (rf & 2) {
    const ctx_r0 = \u0275\u0275nextContext();
    \u0275\u0275advance();
    \u0275\u0275conditional(ctx_r0.paginatedOrders().length === 0 ? 1 : 2);
    \u0275\u0275advance(2);
    \u0275\u0275conditional(ctx_r0.totalPages() > 1 ? 3 : -1);
  }
}
var OrdersComponent = class _OrdersComponent {
  commandeService;
  authService;
  // Angular 19: Signals
  orders = signal([]);
  isLoading = signal(false);
  error = signal("");
  searchTerm = signal("");
  selectedStatus = signal("");
  currentPage = signal(1);
  pageSize = signal(10);
  expandedOrderId = signal(null);
  // Computed signals
  filteredOrders = computed(() => {
    const orders = this.orders();
    const search = this.searchTerm().toLowerCase();
    const status = this.selectedStatus();
    return orders.filter((order) => {
      const matchesSearch = !search || order.reference.toLowerCase().includes(search) || order.nomFournisseur?.toLowerCase().includes(search);
      const matchesStatus = !status || order.statut === status;
      return matchesSearch && matchesStatus;
    });
  });
  paginatedOrders = computed(() => {
    const filtered = this.filteredOrders();
    const page = this.currentPage();
    const size = this.pageSize();
    const start = (page - 1) * size;
    return filtered.slice(start, start + size);
  });
  totalPages = computed(() => {
    return Math.ceil(this.filteredOrders().length / this.pageSize());
  });
  constructor(commandeService, authService) {
    this.commandeService = commandeService;
    this.authService = authService;
  }
  ngOnInit() {
    console.log("\u{1F504} OrdersComponent - ngOnInit appel\xE9");
    this.loadOrders();
  }
  loadOrders() {
    console.log("\u{1F504} loadOrders - D\xE9but du chargement");
    this.isLoading.set(true);
    this.error.set("");
    const currentUser = this.authService.getCurrentUser();
    console.log("\u{1F464} Utilisateur r\xE9cup\xE9r\xE9:", currentUser);
    if (!currentUser?.id) {
      console.error("\u274C Aucun utilisateur connect\xE9");
      this.error.set("Utilisateur non connect\xE9");
      this.isLoading.set(false);
      return;
    }
    console.log("\u{1F50D} Appel API getCommandesByFournisseur avec ID:", currentUser.id);
    this.commandeService.getCommandesByFournisseur(currentUser.id).subscribe({
      next: (orders) => {
        console.log("\u{1F4E6} Commandes fournisseur r\xE9cup\xE9r\xE9es:", orders);
        this.orders.set(orders);
        this.isLoading.set(false);
      },
      error: (error) => {
        console.error("\u274C Erreur lors du chargement des commandes:", error);
        this.error.set("Erreur lors du chargement des commandes");
        this.isLoading.set(false);
      }
    });
  }
  // Méthodes utilitaires
  formatDate(date) {
    return new Date(date).toLocaleDateString("fr-FR", {
      day: "2-digit",
      month: "2-digit",
      year: "numeric",
      hour: "2-digit",
      minute: "2-digit"
    });
  }
  formatPrice(price) {
    return new Intl.NumberFormat("fr-FR", {
      style: "currency",
      currency: "EUR"
    }).format(price);
  }
  getStatusClass(status) {
    const statusClasses = {
      "Nouvelle": "status-pending",
      "EnAttente": "status-pending",
      "Acceptee": "status-confirmed",
      "Confirmee": "status-confirmed",
      "EnPreparation": "status-preparing",
      "Prete": "status-preparing",
      "Expediee": "status-shipped",
      "Livree": "status-delivered",
      "Refusee": "status-cancelled",
      "Annulee": "status-cancelled"
    };
    return statusClasses[status] || "status-default";
  }
  getStatusText(status) {
    const statusTexts = {
      "Nouvelle": "Nouvelle",
      "EnAttente": "En attente",
      "Acceptee": "Accept\xE9e",
      "Confirmee": "Confirm\xE9e",
      "EnPreparation": "En pr\xE9paration",
      "Prete": "Pr\xEAte",
      "Expediee": "Exp\xE9di\xE9e",
      "Livree": "Livr\xE9e",
      "Refusee": "Refus\xE9e",
      "Annulee": "Annul\xE9e"
    };
    return statusTexts[status] || status;
  }
  // Actions sur les commandes
  toggleOrderDetails(orderId) {
    const current = this.expandedOrderId();
    this.expandedOrderId.set(current === orderId ? null : orderId);
  }
  isOrderExpanded(orderId) {
    return this.expandedOrderId() === orderId;
  }
  updateOrderStatus(orderId, newStatus) {
    const request = {
      statut: newStatus,
      numeroBonLivraison: newStatus === "Livree" ? `BL-${Date.now()}` : void 0
    };
    this.commandeService.updateStatutCommandeFournisseur(orderId, request).subscribe({
      next: () => {
        console.log("\u2705 Statut de commande mis \xE0 jour");
        this.loadOrders();
      },
      error: (error) => {
        console.error("\u274C Erreur lors de la mise \xE0 jour du statut:", error);
        this.error.set("Erreur lors de la mise \xE0 jour du statut");
      }
    });
  }
  // Pagination
  onPageChange(page) {
    if (page >= 1 && page <= this.totalPages()) {
      this.currentPage.set(page);
    }
  }
  // Filtres
  onSearchChange(event) {
    const target = event.target;
    this.searchTerm.set(target.value);
    this.currentPage.set(1);
  }
  onStatusChange(event) {
    const target = event.target;
    this.selectedStatus.set(target.value);
    this.currentPage.set(1);
  }
  clearFilters() {
    this.searchTerm.set("");
    this.selectedStatus.set("");
    this.currentPage.set(1);
  }
  // Méthodes pour les actions sur les commandes
  canConfirmOrder(status) {
    return status === "EnAttente" || status === "Nouvelle";
  }
  canPrepareOrder(status) {
    return status === "Confirmee" || status === "Acceptee";
  }
  canShipOrder(status) {
    return status === "EnPreparation" || status === "Prete";
  }
  canDeliverOrder(status) {
    return status === "Expediee";
  }
  canCancelOrder(status) {
    return ["EnAttente", "Nouvelle", "Confirmee", "Acceptee"].includes(status);
  }
  // Math object for template
  Math = Math;
  static \u0275fac = function OrdersComponent_Factory(__ngFactoryType__) {
    return new (__ngFactoryType__ || _OrdersComponent)(\u0275\u0275directiveInject(CommandeService), \u0275\u0275directiveInject(AuthService));
  };
  static \u0275cmp = /* @__PURE__ */ \u0275\u0275defineComponent({ type: _OrdersComponent, selectors: [["app-orders"]], decls: 39, vars: 5, consts: [[1, "orders-container"], [1, "orders-header"], [1, "header-actions"], [1, "btn", "btn-secondary", 3, "click"], [1, "filters-section"], [1, "filters-row"], [1, "filter-group"], ["for", "search"], ["id", "search", "type", "text", "placeholder", "R\xE9f\xE9rence, client...", 1, "form-control", 3, "input", "value"], ["for", "status"], ["id", "status", 1, "form-control", 3, "change", "value"], ["value", ""], ["value", "Nouvelle"], ["value", "Acceptee"], ["value", "EnPreparation"], ["value", "Prete"], ["value", "Expediee"], ["value", "Livree"], ["value", "Refusee"], [1, "btn", "btn-outline-secondary", 3, "click"], [1, "loading-container"], [1, "alert", "alert-danger"], [1, "spinner"], [1, "orders-list"], [1, "no-orders"], [1, "pagination-container"], [1, "btn", "btn-primary"], [1, "btn", "btn-primary", 3, "click"], [1, "order-card"], [1, "order-header", 3, "click"], [1, "order-info"], [1, "order-reference"], [1, "order-meta"], [1, "order-date"], [1, "order-total"], [1, "order-status"], [1, "status-badge"], [1, "expand-icon"], [1, "order-details"], [1, "client-info"], [1, "info-grid"], [1, "info-item"], [1, "products-section"], [1, "products-list"], [1, "no-products"], [1, "order-actions"], [1, "actions-buttons"], [1, "btn", "btn-success"], [1, "btn", "btn-warning"], [1, "btn", "btn-info"], [1, "btn", "btn-danger"], [1, "product-item"], [1, "product-info"], [1, "product-name"], [1, "product-details"], [1, "product-ref"], [1, "product-image"], [1, "product-quantity"], [1, "quantity"], [1, "product-price"], [1, "unit-price"], [1, "total-price"], [1, "btn", "btn-success", 3, "click"], [1, "btn", "btn-warning", 3, "click"], [1, "btn", "btn-info", 3, "click"], [1, "btn", "btn-danger", 3, "click"], [1, "pagination-info"], [1, "pagination"], [1, "btn", "btn-outline-primary", 3, "click", "disabled"], [1, "btn", 3, "btn-primary", "btn-outline-primary"], [1, "btn", 3, "click"]], template: function OrdersComponent_Template(rf, ctx) {
    if (rf & 1) {
      \u0275\u0275elementStart(0, "div", 0)(1, "div", 1)(2, "h1");
      \u0275\u0275text(3, "\u{1F4E6} Gestion des Commandes");
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(4, "div", 2)(5, "button", 3);
      \u0275\u0275listener("click", function OrdersComponent_Template_button_click_5_listener() {
        return ctx.loadOrders();
      });
      \u0275\u0275text(6, " \u{1F504} Actualiser ");
      \u0275\u0275elementEnd()()();
      \u0275\u0275elementStart(7, "div", 4)(8, "div", 5)(9, "div", 6)(10, "label", 7);
      \u0275\u0275text(11, "\u{1F50D} Rechercher:");
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(12, "input", 8);
      \u0275\u0275listener("input", function OrdersComponent_Template_input_input_12_listener($event) {
        return ctx.onSearchChange($event);
      });
      \u0275\u0275elementEnd()();
      \u0275\u0275elementStart(13, "div", 6)(14, "label", 9);
      \u0275\u0275text(15, "\u{1F4CA} Statut:");
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(16, "select", 10);
      \u0275\u0275listener("change", function OrdersComponent_Template_select_change_16_listener($event) {
        return ctx.onStatusChange($event);
      });
      \u0275\u0275elementStart(17, "option", 11);
      \u0275\u0275text(18, "Tous les statuts");
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(19, "option", 12);
      \u0275\u0275text(20, "Nouvelle");
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(21, "option", 13);
      \u0275\u0275text(22, "Accept\xE9e");
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(23, "option", 14);
      \u0275\u0275text(24, "En pr\xE9paration");
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(25, "option", 15);
      \u0275\u0275text(26, "Pr\xEAte");
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(27, "option", 16);
      \u0275\u0275text(28, "Exp\xE9di\xE9e");
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(29, "option", 17);
      \u0275\u0275text(30, "Livr\xE9e");
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(31, "option", 18);
      \u0275\u0275text(32, "Refus\xE9e");
      \u0275\u0275elementEnd()()();
      \u0275\u0275elementStart(33, "div", 6)(34, "button", 19);
      \u0275\u0275listener("click", function OrdersComponent_Template_button_click_34_listener() {
        return ctx.clearFilters();
      });
      \u0275\u0275text(35, " \u{1F5D1}\uFE0F Effacer filtres ");
      \u0275\u0275elementEnd()()()();
      \u0275\u0275template(36, OrdersComponent_Conditional_36_Template, 4, 0, "div", 20)(37, OrdersComponent_Conditional_37_Template, 4, 1, "div", 21)(38, OrdersComponent_Conditional_38_Template, 4, 2);
      \u0275\u0275elementEnd();
    }
    if (rf & 2) {
      \u0275\u0275advance(12);
      \u0275\u0275property("value", ctx.searchTerm());
      \u0275\u0275advance(4);
      \u0275\u0275property("value", ctx.selectedStatus());
      \u0275\u0275advance(20);
      \u0275\u0275conditional(ctx.isLoading() ? 36 : -1);
      \u0275\u0275advance();
      \u0275\u0275conditional(ctx.error() ? 37 : -1);
      \u0275\u0275advance();
      \u0275\u0275conditional(!ctx.isLoading() && !ctx.error() ? 38 : -1);
    }
  }, dependencies: [CommonModule, FormsModule, NgSelectOption, \u0275NgSelectMultipleOption], styles: ["\n\n.orders-container[_ngcontent-%COMP%] {\n  padding: 20px;\n  max-width: 1200px;\n  margin: 0 auto;\n}\n.orders-header[_ngcontent-%COMP%] {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 30px;\n  padding-bottom: 20px;\n  border-bottom: 2px solid #e9ecef;\n}\n.orders-header[_ngcontent-%COMP%]   h1[_ngcontent-%COMP%] {\n  color: #2c3e50;\n  margin: 0;\n  font-size: 2rem;\n}\n.header-actions[_ngcontent-%COMP%] {\n  display: flex;\n  gap: 10px;\n}\n.filters-section[_ngcontent-%COMP%] {\n  background: #f8f9fa;\n  padding: 20px;\n  border-radius: 8px;\n  margin-bottom: 20px;\n}\n.filters-row[_ngcontent-%COMP%] {\n  display: flex;\n  gap: 20px;\n  align-items: end;\n  flex-wrap: wrap;\n}\n.filter-group[_ngcontent-%COMP%] {\n  display: flex;\n  flex-direction: column;\n  gap: 5px;\n}\n.filter-group[_ngcontent-%COMP%]   label[_ngcontent-%COMP%] {\n  font-weight: 600;\n  color: #495057;\n  font-size: 0.9rem;\n}\n.form-control[_ngcontent-%COMP%] {\n  padding: 8px 12px;\n  border: 1px solid #ced4da;\n  border-radius: 4px;\n  font-size: 0.9rem;\n  min-width: 200px;\n}\n.form-control[_ngcontent-%COMP%]:focus {\n  outline: none;\n  border-color: #007bff;\n  box-shadow: 0 0 0 2px rgba(0, 123, 255, 0.25);\n}\n.loading-container[_ngcontent-%COMP%] {\n  text-align: center;\n  padding: 40px;\n}\n.spinner[_ngcontent-%COMP%] {\n  border: 4px solid #f3f3f3;\n  border-top: 4px solid #007bff;\n  border-radius: 50%;\n  width: 40px;\n  height: 40px;\n  animation: _ngcontent-%COMP%_spin 1s linear infinite;\n  margin: 0 auto 20px;\n}\n@keyframes _ngcontent-%COMP%_spin {\n  0% {\n    transform: rotate(0deg);\n  }\n  100% {\n    transform: rotate(360deg);\n  }\n}\n.orders-list[_ngcontent-%COMP%] {\n  display: flex;\n  flex-direction: column;\n  gap: 15px;\n}\n.no-orders[_ngcontent-%COMP%] {\n  text-align: center;\n  padding: 40px;\n  color: #6c757d;\n}\n.no-orders[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\n  font-size: 1.2rem;\n  margin-bottom: 20px;\n}\n.order-card[_ngcontent-%COMP%] {\n  background: white;\n  border: 1px solid #dee2e6;\n  border-radius: 8px;\n  overflow: hidden;\n  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);\n  transition: box-shadow 0.2s ease;\n}\n.order-card[_ngcontent-%COMP%]:hover {\n  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);\n}\n.order-header[_ngcontent-%COMP%] {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  padding: 20px;\n  cursor: pointer;\n  background: #f8f9fa;\n  border-bottom: 1px solid #dee2e6;\n}\n.order-header[_ngcontent-%COMP%]:hover {\n  background: #e9ecef;\n}\n.order-info[_ngcontent-%COMP%] {\n  flex: 1;\n}\n.order-reference[_ngcontent-%COMP%] {\n  font-size: 1.1rem;\n  margin-bottom: 8px;\n}\n.order-meta[_ngcontent-%COMP%] {\n  display: flex;\n  gap: 20px;\n  color: #6c757d;\n  font-size: 0.9rem;\n}\n.order-status[_ngcontent-%COMP%] {\n  display: flex;\n  align-items: center;\n  gap: 15px;\n}\n.status-badge[_ngcontent-%COMP%] {\n  padding: 6px 12px;\n  border-radius: 20px;\n  font-size: 0.8rem;\n  font-weight: 600;\n  text-transform: uppercase;\n}\n.status-pending[_ngcontent-%COMP%] {\n  background: #fff3cd;\n  color: #856404;\n}\n.status-confirmed[_ngcontent-%COMP%] {\n  background: #d4edda;\n  color: #155724;\n}\n.status-preparing[_ngcontent-%COMP%] {\n  background: #cce5ff;\n  color: #004085;\n}\n.status-shipped[_ngcontent-%COMP%] {\n  background: #e2e3e5;\n  color: #383d41;\n}\n.status-delivered[_ngcontent-%COMP%] {\n  background: #d1ecf1;\n  color: #0c5460;\n}\n.status-cancelled[_ngcontent-%COMP%] {\n  background: #f8d7da;\n  color: #721c24;\n}\n.status-default[_ngcontent-%COMP%] {\n  background: #e9ecef;\n  color: #495057;\n}\n.expand-icon[_ngcontent-%COMP%] {\n  transition: transform 0.2s ease;\n  font-size: 0.8rem;\n  color: #6c757d;\n}\n.expand-icon.expanded[_ngcontent-%COMP%] {\n  transform: rotate(180deg);\n}\n.order-details[_ngcontent-%COMP%] {\n  padding: 20px;\n  background: white;\n}\n.order-details[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%] {\n  color: #495057;\n  margin-bottom: 15px;\n  font-size: 1rem;\n  border-bottom: 1px solid #dee2e6;\n  padding-bottom: 8px;\n}\n.client-info[_ngcontent-%COMP%], \n.shipping-info[_ngcontent-%COMP%] {\n  margin-bottom: 25px;\n}\n.info-grid[_ngcontent-%COMP%] {\n  display: grid;\n  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));\n  gap: 10px;\n}\n.info-item[_ngcontent-%COMP%] {\n  font-size: 0.9rem;\n}\n.info-item[_ngcontent-%COMP%]   strong[_ngcontent-%COMP%] {\n  color: #495057;\n}\n.address[_ngcontent-%COMP%] {\n  background: #f8f9fa;\n  padding: 15px;\n  border-radius: 4px;\n  border-left: 4px solid #007bff;\n}\n.address[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\n  margin: 0;\n  line-height: 1.4;\n}\n.products-section[_ngcontent-%COMP%] {\n  margin-bottom: 25px;\n}\n.products-list[_ngcontent-%COMP%] {\n  display: flex;\n  flex-direction: column;\n  gap: 10px;\n}\n.product-item[_ngcontent-%COMP%] {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  padding: 15px;\n  background: #f8f9fa;\n  border-radius: 4px;\n  border: 1px solid #dee2e6;\n}\n.product-info[_ngcontent-%COMP%] {\n  flex: 1;\n}\n.product-name[_ngcontent-%COMP%] {\n  font-size: 1rem;\n  margin-bottom: 5px;\n}\n.product-details[_ngcontent-%COMP%] {\n  display: flex;\n  gap: 15px;\n  font-size: 0.8rem;\n  color: #6c757d;\n}\n.product-quantity[_ngcontent-%COMP%] {\n  text-align: center;\n  min-width: 80px;\n}\n.quantity[_ngcontent-%COMP%] {\n  font-weight: 600;\n  color: #495057;\n}\n.product-price[_ngcontent-%COMP%] {\n  text-align: right;\n  min-width: 120px;\n}\n.unit-price[_ngcontent-%COMP%] {\n  font-size: 0.8rem;\n  color: #6c757d;\n}\n.total-price[_ngcontent-%COMP%] {\n  font-size: 1rem;\n  color: #28a745;\n}\n.no-products[_ngcontent-%COMP%] {\n  text-align: center;\n  color: #6c757d;\n  font-style: italic;\n  padding: 20px;\n}\n.order-actions[_ngcontent-%COMP%] {\n  border-top: 1px solid #dee2e6;\n  padding-top: 20px;\n}\n.actions-buttons[_ngcontent-%COMP%] {\n  display: flex;\n  gap: 10px;\n  flex-wrap: wrap;\n}\n.btn[_ngcontent-%COMP%] {\n  padding: 8px 16px;\n  border: none;\n  border-radius: 4px;\n  cursor: pointer;\n  font-size: 0.9rem;\n  font-weight: 500;\n  text-decoration: none;\n  display: inline-flex;\n  align-items: center;\n  gap: 5px;\n  transition: all 0.2s ease;\n}\n.btn[_ngcontent-%COMP%]:disabled {\n  opacity: 0.6;\n  cursor: not-allowed;\n}\n.btn-primary[_ngcontent-%COMP%] {\n  background: #007bff;\n  color: white;\n}\n.btn-primary[_ngcontent-%COMP%]:hover:not(:disabled) {\n  background: #0056b3;\n}\n.btn-success[_ngcontent-%COMP%] {\n  background: #28a745;\n  color: white;\n}\n.btn-success[_ngcontent-%COMP%]:hover:not(:disabled) {\n  background: #1e7e34;\n}\n.btn-warning[_ngcontent-%COMP%] {\n  background: #ffc107;\n  color: #212529;\n}\n.btn-warning[_ngcontent-%COMP%]:hover:not(:disabled) {\n  background: #e0a800;\n}\n.btn-info[_ngcontent-%COMP%] {\n  background: #17a2b8;\n  color: white;\n}\n.btn-info[_ngcontent-%COMP%]:hover:not(:disabled) {\n  background: #117a8b;\n}\n.btn-danger[_ngcontent-%COMP%] {\n  background: #dc3545;\n  color: white;\n}\n.btn-danger[_ngcontent-%COMP%]:hover:not(:disabled) {\n  background: #c82333;\n}\n.btn-secondary[_ngcontent-%COMP%] {\n  background: #6c757d;\n  color: white;\n}\n.btn-secondary[_ngcontent-%COMP%]:hover:not(:disabled) {\n  background: #545b62;\n}\n.btn-outline-primary[_ngcontent-%COMP%] {\n  background: transparent;\n  color: #007bff;\n  border: 1px solid #007bff;\n}\n.btn-outline-primary[_ngcontent-%COMP%]:hover:not(:disabled) {\n  background: #007bff;\n  color: white;\n}\n.btn-outline-secondary[_ngcontent-%COMP%] {\n  background: transparent;\n  color: #6c757d;\n  border: 1px solid #6c757d;\n}\n.btn-outline-secondary[_ngcontent-%COMP%]:hover:not(:disabled) {\n  background: #6c757d;\n  color: white;\n}\n.pagination-container[_ngcontent-%COMP%] {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-top: 30px;\n  padding-top: 20px;\n  border-top: 1px solid #dee2e6;\n}\n.pagination-info[_ngcontent-%COMP%] {\n  color: #6c757d;\n  font-size: 0.9rem;\n}\n.pagination[_ngcontent-%COMP%] {\n  display: flex;\n  gap: 5px;\n}\n.alert[_ngcontent-%COMP%] {\n  padding: 15px;\n  border-radius: 4px;\n  margin-bottom: 20px;\n}\n.alert-danger[_ngcontent-%COMP%] {\n  background: #f8d7da;\n  color: #721c24;\n  border: 1px solid #f5c6cb;\n}\n@media (max-width: 768px) {\n  .orders-container[_ngcontent-%COMP%] {\n    padding: 10px;\n  }\n  .orders-header[_ngcontent-%COMP%] {\n    flex-direction: column;\n    gap: 15px;\n    align-items: stretch;\n  }\n  .filters-row[_ngcontent-%COMP%] {\n    flex-direction: column;\n    gap: 15px;\n  }\n  .form-control[_ngcontent-%COMP%] {\n    min-width: auto;\n  }\n  .order-header[_ngcontent-%COMP%] {\n    flex-direction: column;\n    gap: 15px;\n    align-items: stretch;\n  }\n  .order-meta[_ngcontent-%COMP%] {\n    flex-direction: column;\n    gap: 5px;\n  }\n  .product-item[_ngcontent-%COMP%] {\n    flex-direction: column;\n    gap: 10px;\n    align-items: stretch;\n  }\n  .actions-buttons[_ngcontent-%COMP%] {\n    flex-direction: column;\n  }\n  .pagination-container[_ngcontent-%COMP%] {\n    flex-direction: column;\n    gap: 15px;\n  }\n}\n/*# sourceMappingURL=orders.component.css.map */"] });
};
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && setClassMetadata(OrdersComponent, [{
    type: Component,
    args: [{ selector: "app-orders", standalone: true, imports: [CommonModule, FormsModule], template: `<div class="orders-container">
  <!-- Header -->
  <div class="orders-header">
    <h1>\u{1F4E6} Gestion des Commandes</h1>
    <div class="header-actions">
      <button class="btn btn-secondary" (click)="loadOrders()">
        \u{1F504} Actualiser
      </button>
    </div>
  </div>

  <!-- Filtres -->
  <div class="filters-section">
    <div class="filters-row">
      <div class="filter-group">
        <label for="search">\u{1F50D} Rechercher:</label>
        <input
          id="search"
          type="text"
          class="form-control"
          placeholder="R\xE9f\xE9rence, client..."
          [value]="searchTerm()"
          (input)="onSearchChange($event)"
        />
      </div>

      <div class="filter-group">
        <label for="status">\u{1F4CA} Statut:</label>
        <select
          id="status"
          class="form-control"
          [value]="selectedStatus()"
          (change)="onStatusChange($event)"
        >
          <option value="">Tous les statuts</option>
          <option value="Nouvelle">Nouvelle</option>
          <option value="Acceptee">Accept\xE9e</option>
          <option value="EnPreparation">En pr\xE9paration</option>
          <option value="Prete">Pr\xEAte</option>
          <option value="Expediee">Exp\xE9di\xE9e</option>
          <option value="Livree">Livr\xE9e</option>
          <option value="Refusee">Refus\xE9e</option>
        </select>
      </div>

      <div class="filter-group">
        <button class="btn btn-outline-secondary" (click)="clearFilters()">
          \u{1F5D1}\uFE0F Effacer filtres
        </button>
      </div>
    </div>
  </div>

  <!-- Loading -->
  @if (isLoading()) {
    <div class="loading-container">
      <div class="spinner"></div>
      <p>Chargement des commandes...</p>
    </div>
  }

  <!-- Error -->
  @if (error()) {
    <div class="alert alert-danger">
      <strong>\u274C Erreur:</strong> {{ error() }}
    </div>
  }

  <!-- Orders List -->
  @if (!isLoading() && !error()) {
    <div class="orders-list">
      @if (paginatedOrders().length === 0) {
        <div class="no-orders">
          <p>\u{1F4ED} Aucune commande trouv\xE9e</p>
          @if (searchTerm() || selectedStatus()) {
            <button class="btn btn-primary" (click)="clearFilters()">
              Voir toutes les commandes
            </button>
          }
        </div>
      } @else {
        @for (order of paginatedOrders(); track order.id) {
          <div class="order-card">
            <!-- Order Header -->
            <div class="order-header" (click)="toggleOrderDetails(order.id)">
              <div class="order-info">
                <div class="order-reference">
                  <strong>\u{1F4CB} {{ order.reference }}</strong>
                </div>
                <div class="order-meta">
                  <span class="order-date">\u{1F4C5} {{ formatDate(order.dateCreation) }}</span>
                  <span class="order-total">\u{1F4B0} {{ formatPrice(order.montantTotal) }}</span>
                </div>
              </div>
              
              <div class="order-status">
                <span class="status-badge" [class]="getStatusClass(order.statut)">
                  {{ getStatusText(order.statut) }}
                </span>
                <span class="expand-icon" [class.expanded]="isOrderExpanded(order.id)">
                  \u25BC
                </span>
              </div>
            </div>

            <!-- Order Details (Expandable) -->
            @if (isOrderExpanded(order.id)) {
              <div class="order-details">
                <!-- Order Info -->
                <div class="client-info">
                  <h4>\u{1F4CB} Informations Commande</h4>
                  <div class="info-grid">
                    <div class="info-item">
                      <strong>R\xE9f\xE9rence:</strong> {{ order.reference }}
                    </div>
                    <div class="info-item">
                      <strong>Fournisseur:</strong> {{ order.nomFournisseur }}
                    </div>
                    <div class="info-item">
                      <strong>Matricule:</strong> {{ order.matriculeFiscale || 'Non renseign\xE9' }}
                    </div>
                    <div class="info-item">
                      <strong>Frais livraison:</strong> {{ formatPrice(order.fraisLivraison) }}
                    </div>
                    @if (order.numeroBonLivraison) {
                      <div class="info-item">
                        <strong>Bon de livraison:</strong> {{ order.numeroBonLivraison }}
                      </div>
                    }
                  </div>
                </div>



                <!-- Products -->
                <div class="products-section">
                  <h4>\u{1F6CD}\uFE0F Produits Command\xE9s</h4>
                  @if (order.lignesCommande && order.lignesCommande.length > 0) {
                    <div class="products-list">
                      @for (ligne of order.lignesCommande; track ligne.id) {
                        <div class="product-item">
                          <div class="product-info">
                            <div class="product-name">
                              <strong>{{ ligne.nomProduit }}</strong>
                            </div>
                            <div class="product-details">
                              <span class="product-ref">R\xE9f: {{ ligne.referenceProduit || 'N/A' }}</span>
                              @if (ligne.imagePrincipale) {
                                <span class="product-image">\u{1F5BC}\uFE0F Image disponible</span>
                              }
                            </div>
                          </div>
                          <div class="product-quantity">
                            <span class="quantity">Qt\xE9: {{ ligne.quantite }}</span>
                          </div>
                          <div class="product-price">
                            <div class="unit-price">{{ formatPrice(ligne.prixUnitaire) }}/u</div>
                            <div class="total-price"><strong>{{ formatPrice(ligne.totalLigne) }}</strong></div>
                          </div>
                        </div>
                      }
                    </div>
                  } @else {
                    <p class="no-products">Aucun produit trouv\xE9 pour cette commande</p>
                  }
                </div>

                <!-- Order Actions -->
                <div class="order-actions">
                  <h4>\u26A1 Actions</h4>
                  <div class="actions-buttons">
                    @if (canConfirmOrder(order.statut)) {
                      <button
                        class="btn btn-success"
                        (click)="updateOrderStatus(order.id, 'Acceptee')"
                      >
                        \u2705 Accepter
                      </button>
                    }

                    @if (canPrepareOrder(order.statut)) {
                      <button
                        class="btn btn-warning"
                        (click)="updateOrderStatus(order.id, 'EnPreparation')"
                      >
                        \u{1F4E6} Pr\xE9parer
                      </button>
                    }

                    @if (canShipOrder(order.statut)) {
                      <button
                        class="btn btn-info"
                        (click)="updateOrderStatus(order.id, 'Expediee')"
                      >
                        \u{1F69A} Exp\xE9dier
                      </button>
                    }

                    @if (canDeliverOrder(order.statut)) {
                      <button
                        class="btn btn-primary"
                        (click)="updateOrderStatus(order.id, 'Livree')"
                      >
                        \u2705 Marquer comme livr\xE9e
                      </button>
                    }

                    @if (canCancelOrder(order.statut)) {
                      <button
                        class="btn btn-danger"
                        (click)="updateOrderStatus(order.id, 'Refusee')"
                      >
                        \u274C Refuser
                      </button>
                    }
                  </div>
                </div>
              </div>
            }
          </div>
        }
      }
    </div>

    <!-- Pagination -->
    @if (totalPages() > 1) {
      <div class="pagination-container">
        <div class="pagination-info">
          Affichage {{ (currentPage() - 1) * pageSize() + 1 }} \xE0 
          {{ Math.min(currentPage() * pageSize(), filteredOrders().length) }} 
          sur {{ filteredOrders().length }} commandes
        </div>
        
        <div class="pagination">
          <button 
            class="btn btn-outline-primary"
            [disabled]="currentPage() === 1"
            (click)="onPageChange(currentPage() - 1)"
          >
            \u2190 Pr\xE9c\xE9dent
          </button>
          
          @for (page of [].constructor(totalPages()); track $index) {
            <button 
              class="btn"
              [class.btn-primary]="$index + 1 === currentPage()"
              [class.btn-outline-primary]="$index + 1 !== currentPage()"
              (click)="onPageChange($index + 1)"
            >
              {{ $index + 1 }}
            </button>
          }
          
          <button 
            class="btn btn-outline-primary"
            [disabled]="currentPage() === totalPages()"
            (click)="onPageChange(currentPage() + 1)"
          >
            Suivant \u2192
          </button>
        </div>
      </div>
    }
  }
</div>
`, styles: ["/* src/app/components/orders/orders.component.css */\n.orders-container {\n  padding: 20px;\n  max-width: 1200px;\n  margin: 0 auto;\n}\n.orders-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 30px;\n  padding-bottom: 20px;\n  border-bottom: 2px solid #e9ecef;\n}\n.orders-header h1 {\n  color: #2c3e50;\n  margin: 0;\n  font-size: 2rem;\n}\n.header-actions {\n  display: flex;\n  gap: 10px;\n}\n.filters-section {\n  background: #f8f9fa;\n  padding: 20px;\n  border-radius: 8px;\n  margin-bottom: 20px;\n}\n.filters-row {\n  display: flex;\n  gap: 20px;\n  align-items: end;\n  flex-wrap: wrap;\n}\n.filter-group {\n  display: flex;\n  flex-direction: column;\n  gap: 5px;\n}\n.filter-group label {\n  font-weight: 600;\n  color: #495057;\n  font-size: 0.9rem;\n}\n.form-control {\n  padding: 8px 12px;\n  border: 1px solid #ced4da;\n  border-radius: 4px;\n  font-size: 0.9rem;\n  min-width: 200px;\n}\n.form-control:focus {\n  outline: none;\n  border-color: #007bff;\n  box-shadow: 0 0 0 2px rgba(0, 123, 255, 0.25);\n}\n.loading-container {\n  text-align: center;\n  padding: 40px;\n}\n.spinner {\n  border: 4px solid #f3f3f3;\n  border-top: 4px solid #007bff;\n  border-radius: 50%;\n  width: 40px;\n  height: 40px;\n  animation: spin 1s linear infinite;\n  margin: 0 auto 20px;\n}\n@keyframes spin {\n  0% {\n    transform: rotate(0deg);\n  }\n  100% {\n    transform: rotate(360deg);\n  }\n}\n.orders-list {\n  display: flex;\n  flex-direction: column;\n  gap: 15px;\n}\n.no-orders {\n  text-align: center;\n  padding: 40px;\n  color: #6c757d;\n}\n.no-orders p {\n  font-size: 1.2rem;\n  margin-bottom: 20px;\n}\n.order-card {\n  background: white;\n  border: 1px solid #dee2e6;\n  border-radius: 8px;\n  overflow: hidden;\n  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);\n  transition: box-shadow 0.2s ease;\n}\n.order-card:hover {\n  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);\n}\n.order-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  padding: 20px;\n  cursor: pointer;\n  background: #f8f9fa;\n  border-bottom: 1px solid #dee2e6;\n}\n.order-header:hover {\n  background: #e9ecef;\n}\n.order-info {\n  flex: 1;\n}\n.order-reference {\n  font-size: 1.1rem;\n  margin-bottom: 8px;\n}\n.order-meta {\n  display: flex;\n  gap: 20px;\n  color: #6c757d;\n  font-size: 0.9rem;\n}\n.order-status {\n  display: flex;\n  align-items: center;\n  gap: 15px;\n}\n.status-badge {\n  padding: 6px 12px;\n  border-radius: 20px;\n  font-size: 0.8rem;\n  font-weight: 600;\n  text-transform: uppercase;\n}\n.status-pending {\n  background: #fff3cd;\n  color: #856404;\n}\n.status-confirmed {\n  background: #d4edda;\n  color: #155724;\n}\n.status-preparing {\n  background: #cce5ff;\n  color: #004085;\n}\n.status-shipped {\n  background: #e2e3e5;\n  color: #383d41;\n}\n.status-delivered {\n  background: #d1ecf1;\n  color: #0c5460;\n}\n.status-cancelled {\n  background: #f8d7da;\n  color: #721c24;\n}\n.status-default {\n  background: #e9ecef;\n  color: #495057;\n}\n.expand-icon {\n  transition: transform 0.2s ease;\n  font-size: 0.8rem;\n  color: #6c757d;\n}\n.expand-icon.expanded {\n  transform: rotate(180deg);\n}\n.order-details {\n  padding: 20px;\n  background: white;\n}\n.order-details h4 {\n  color: #495057;\n  margin-bottom: 15px;\n  font-size: 1rem;\n  border-bottom: 1px solid #dee2e6;\n  padding-bottom: 8px;\n}\n.client-info,\n.shipping-info {\n  margin-bottom: 25px;\n}\n.info-grid {\n  display: grid;\n  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));\n  gap: 10px;\n}\n.info-item {\n  font-size: 0.9rem;\n}\n.info-item strong {\n  color: #495057;\n}\n.address {\n  background: #f8f9fa;\n  padding: 15px;\n  border-radius: 4px;\n  border-left: 4px solid #007bff;\n}\n.address p {\n  margin: 0;\n  line-height: 1.4;\n}\n.products-section {\n  margin-bottom: 25px;\n}\n.products-list {\n  display: flex;\n  flex-direction: column;\n  gap: 10px;\n}\n.product-item {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  padding: 15px;\n  background: #f8f9fa;\n  border-radius: 4px;\n  border: 1px solid #dee2e6;\n}\n.product-info {\n  flex: 1;\n}\n.product-name {\n  font-size: 1rem;\n  margin-bottom: 5px;\n}\n.product-details {\n  display: flex;\n  gap: 15px;\n  font-size: 0.8rem;\n  color: #6c757d;\n}\n.product-quantity {\n  text-align: center;\n  min-width: 80px;\n}\n.quantity {\n  font-weight: 600;\n  color: #495057;\n}\n.product-price {\n  text-align: right;\n  min-width: 120px;\n}\n.unit-price {\n  font-size: 0.8rem;\n  color: #6c757d;\n}\n.total-price {\n  font-size: 1rem;\n  color: #28a745;\n}\n.no-products {\n  text-align: center;\n  color: #6c757d;\n  font-style: italic;\n  padding: 20px;\n}\n.order-actions {\n  border-top: 1px solid #dee2e6;\n  padding-top: 20px;\n}\n.actions-buttons {\n  display: flex;\n  gap: 10px;\n  flex-wrap: wrap;\n}\n.btn {\n  padding: 8px 16px;\n  border: none;\n  border-radius: 4px;\n  cursor: pointer;\n  font-size: 0.9rem;\n  font-weight: 500;\n  text-decoration: none;\n  display: inline-flex;\n  align-items: center;\n  gap: 5px;\n  transition: all 0.2s ease;\n}\n.btn:disabled {\n  opacity: 0.6;\n  cursor: not-allowed;\n}\n.btn-primary {\n  background: #007bff;\n  color: white;\n}\n.btn-primary:hover:not(:disabled) {\n  background: #0056b3;\n}\n.btn-success {\n  background: #28a745;\n  color: white;\n}\n.btn-success:hover:not(:disabled) {\n  background: #1e7e34;\n}\n.btn-warning {\n  background: #ffc107;\n  color: #212529;\n}\n.btn-warning:hover:not(:disabled) {\n  background: #e0a800;\n}\n.btn-info {\n  background: #17a2b8;\n  color: white;\n}\n.btn-info:hover:not(:disabled) {\n  background: #117a8b;\n}\n.btn-danger {\n  background: #dc3545;\n  color: white;\n}\n.btn-danger:hover:not(:disabled) {\n  background: #c82333;\n}\n.btn-secondary {\n  background: #6c757d;\n  color: white;\n}\n.btn-secondary:hover:not(:disabled) {\n  background: #545b62;\n}\n.btn-outline-primary {\n  background: transparent;\n  color: #007bff;\n  border: 1px solid #007bff;\n}\n.btn-outline-primary:hover:not(:disabled) {\n  background: #007bff;\n  color: white;\n}\n.btn-outline-secondary {\n  background: transparent;\n  color: #6c757d;\n  border: 1px solid #6c757d;\n}\n.btn-outline-secondary:hover:not(:disabled) {\n  background: #6c757d;\n  color: white;\n}\n.pagination-container {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-top: 30px;\n  padding-top: 20px;\n  border-top: 1px solid #dee2e6;\n}\n.pagination-info {\n  color: #6c757d;\n  font-size: 0.9rem;\n}\n.pagination {\n  display: flex;\n  gap: 5px;\n}\n.alert {\n  padding: 15px;\n  border-radius: 4px;\n  margin-bottom: 20px;\n}\n.alert-danger {\n  background: #f8d7da;\n  color: #721c24;\n  border: 1px solid #f5c6cb;\n}\n@media (max-width: 768px) {\n  .orders-container {\n    padding: 10px;\n  }\n  .orders-header {\n    flex-direction: column;\n    gap: 15px;\n    align-items: stretch;\n  }\n  .filters-row {\n    flex-direction: column;\n    gap: 15px;\n  }\n  .form-control {\n    min-width: auto;\n  }\n  .order-header {\n    flex-direction: column;\n    gap: 15px;\n    align-items: stretch;\n  }\n  .order-meta {\n    flex-direction: column;\n    gap: 5px;\n  }\n  .product-item {\n    flex-direction: column;\n    gap: 10px;\n    align-items: stretch;\n  }\n  .actions-buttons {\n    flex-direction: column;\n  }\n  .pagination-container {\n    flex-direction: column;\n    gap: 15px;\n  }\n}\n/*# sourceMappingURL=orders.component.css.map */\n"] }]
  }], () => [{ type: CommandeService }, { type: AuthService }], null);
})();
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && \u0275setClassDebugInfo(OrdersComponent, { className: "OrdersComponent", filePath: "src/app/components/orders/orders.component.ts", lineNumber: 15 });
})();
export {
  OrdersComponent
};
//# sourceMappingURL=chunk-3QGRCFWL.js.map

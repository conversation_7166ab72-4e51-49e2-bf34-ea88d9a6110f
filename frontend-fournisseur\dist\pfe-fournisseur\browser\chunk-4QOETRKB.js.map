{"version": 3, "sources": ["src/app/components/auth/login/login.component.ts", "src/app/components/auth/login/login.component.html"], "sourcesContent": ["import { Component, OnInit, signal, computed, effect } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { FormBuilder, FormGroup, Validators, ReactiveFormsModule } from '@angular/forms';\nimport { Router, ActivatedRoute, RouterModule } from '@angular/router';\nimport { AuthService } from '../../../services/auth.service';\nimport { LoginRequest } from '../../../models';\n\n@Component({\n  selector: 'app-login',\n  standalone: true,\n  imports: [CommonModule, ReactiveFormsModule, RouterModule],\n  templateUrl: './login.component.html',\n  styleUrls: ['./login.component.css']\n})\nexport class LoginComponent implements OnInit {\n  loginForm: FormGroup;\n\n  // Angular 19: Signals pour l'état du composant\n  isLoading = signal(false);\n  errorMessage = signal('');\n  returnUrl = signal('');\n  isFormValid = signal(false);\n\n  // Angular 19: Computed signals pour l'état du formulaire\n  canSubmit = computed(() => this.isFormValid() && !this.isLoading());\n  hasError = computed(() => this.errorMessage().length > 0);\n\n  constructor(\n    private formBuilder: FormBuilder,\n    private authService: AuthService,\n    private router: Router,\n    private route: ActivatedRoute\n  ) {\n    this.loginForm = this.formBuilder.group({\n      email: ['', [Validators.required, Validators.email]],\n      password: ['', [Validators.required, Validators.minLength(6)]]\n    });\n\n    // Angular 19: Écouter les changements du formulaire pour mettre à jour les signals\n    this.loginForm.statusChanges.subscribe(() => {\n      this.isFormValid.set(this.loginForm.valid);\n    });\n\n    // Initialiser l'état du formulaire\n    this.isFormValid.set(this.loginForm.valid);\n\n    // Angular 19: Effect pour logger les changements d'état\n    effect(() => {\n      if (this.hasError()) {\n        console.log('❌ Erreur de connexion:', this.errorMessage());\n      }\n      if (this.isLoading()) {\n        console.log('⏳ Connexion en cours...');\n      }\n    });\n  }\n\n  ngOnInit(): void {\n    // Rediriger si déjà connecté\n    if (this.authService.isAuthenticated()) {\n      this.router.navigate(['/dashboard']);\n      return;\n    }\n\n    // Angular 19: Récupérer l'URL de retour avec signal\n    this.returnUrl.set(this.route.snapshot.queryParams['returnUrl'] || '/dashboard');\n  }\n\n  onSubmit(): void {\n    if (this.loginForm.valid) {\n      // Angular 19: Mise à jour des signals\n      this.isLoading.set(true);\n      this.errorMessage.set('');\n\n      const credentials: LoginRequest = {\n        email: this.loginForm.value.email!,\n        password: this.loginForm.value.password!  // Utiliser password directement\n      };\n\n      this.authService.login(credentials).subscribe({\n        next: (response) => {\n          this.isLoading.set(false);\n\n          // Rediriger selon le rôle de l'utilisateur\n          const userRole = response.utilisateur.role;\n\n          if (userRole === 'Fournisseur') {\n            // Rediriger vers le dashboard fournisseur\n            this.router.navigate(['/dashboard']);\n          } else if (userRole === 'Admin') {\n            // Rediriger vers le dashboard admin\n            this.router.navigate(['/admin']);\n          } else {\n            this.errorMessage.set('Accès réservé aux fournisseurs et administrateurs uniquement.');\n            this.authService.logout();\n          }\n        },\n        error: (error) => {\n          this.isLoading.set(false);\n          this.errorMessage.set(error.error?.message || 'Erreur de connexion. Vérifiez vos identifiants.');\n        }\n      });\n    } else {\n      this.markFormGroupTouched();\n    }\n  }\n\n  private markFormGroupTouched(): void {\n    Object.keys(this.loginForm.controls).forEach(key => {\n      const control = this.loginForm.get(key);\n      control?.markAsTouched();\n    });\n  }\n\n  /**\n   * Remplir le formulaire avec des comptes de test\n   */\n  fillTestAccount(type: 'fournisseur' | 'fournisseur2'): void {\n    if (type === 'fournisseur') {\n      this.loginForm.patchValue({\n        email: '<EMAIL>',\n        password: 'Fournisseur123!'\n      });\n    } else if (type === 'fournisseur2') {\n      this.loginForm.patchValue({\n        email: '<EMAIL>',\n        password: 'password123'\n      });\n    }\n  }\n\n  /**\n   * Tester la connexion API\n   */\n  testApiConnection(): void {\n    console.log('🔍 Test de la connexion API...');\n\n    // Tester plusieurs URLs\n    const testUrls = [\n      'http://localhost:7264/api/Auth/login',\n      'https://localhost:7264/api/Auth/login',\n      'http://localhost:5000/api/Auth/login',\n      'https://localhost:5001/api/Auth/login',\n      'http://localhost:7265/api/Auth/login'\n    ];\n\n    console.log('🔍 Test de connectivité sur plusieurs ports...');\n\n    testUrls.forEach((url, index) => {\n      fetch(url, {\n        method: 'POST',\n        headers: { 'Content-Type': 'application/json' },\n        body: JSON.stringify({ email: 'test', motDePasse: 'test' })\n      })\n      .then(response => {\n        console.log(`✅ ${url} - Status: ${response.status} (${response.statusText})`);\n        if (response.status !== 0) {\n          alert(`✅ Backend trouvé sur: ${url}\\nStatus: ${response.status}`);\n        }\n      })\n      .catch(error => {\n        console.log(`❌ ${url} - Erreur: ${error.message}`);\n        if (index === testUrls.length - 1) {\n          alert('❌ Aucun backend accessible sur les ports testés.\\n\\nVérifiez que votre backend ASP.NET Core est démarré.');\n        }\n      });\n    });\n\n    console.log('⚙️ Configuration: Backend API test');\n  }\n\n  // Getters pour faciliter l'accès aux contrôles dans le template\n  get email() { return this.loginForm.get('email'); }\n  get password() { return this.loginForm.get('password'); }\n}\n", "<div class=\"login-container\">\n  <div class=\"login-card\">\n    <div class=\"login-header\">\n      <h1>Connexion Fournisseur</h1>\n      <p>Accédez à votre espace fournisseur</p>\n    </div>\n\n    <form [formGroup]=\"loginForm\" (ngSubmit)=\"onSubmit()\" class=\"login-form\" autocomplete=\"off\">\n      <!-- Email -->\n      <div class=\"form-group\">\n        <label for=\"email\">Email</label>\n        <input\n          type=\"email\"\n          id=\"email\"\n          formControlName=\"email\"\n          class=\"form-control\"\n          [class.is-invalid]=\"email?.invalid && email?.touched\"\n          placeholder=\"Votre adresse email\"\n          autocomplete=\"new-email\"\n          autocapitalize=\"off\"\n          autocorrect=\"off\"\n          spellcheck=\"false\"\n        />\n        <!-- Angular 19: Nouveau control flow -->\n        @if (email?.invalid && email?.touched) {\n          <div class=\"invalid-feedback\">\n            @if (email?.errors?.['required']) {\n              <div>L'email est requis</div>\n            }\n            @if (email?.errors?.['email']) {\n              <div>Format d'email invalide</div>\n            }\n          </div>\n        }\n      </div>\n\n      <!-- Mot de passe -->\n      <div class=\"form-group\">\n        <label for=\"password\">Mot de passe</label>\n        <input\n          type=\"password\"\n          id=\"password\"\n          formControlName=\"password\"\n          class=\"form-control\"\n          [class.is-invalid]=\"password?.invalid && password?.touched\"\n          placeholder=\"Votre mot de passe\"\n          autocomplete=\"new-password\"\n          autocapitalize=\"off\"\n          autocorrect=\"off\"\n          spellcheck=\"false\"\n        />\n        <!-- Angular 19: Nouveau control flow -->\n        @if (password?.invalid && password?.touched) {\n          <div class=\"invalid-feedback\">\n            @if (password?.errors?.['required']) {\n              <div>Le mot de passe est requis</div>\n            }\n            @if (password?.errors?.['minlength']) {\n              <div>Le mot de passe doit contenir au moins 6 caractères</div>\n            }\n          </div>\n        }\n      </div>\n\n      <!-- Angular 19: Message d'erreur avec nouveau control flow -->\n      @if (hasError()) {\n        <div class=\"alert alert-danger\">\n          {{ errorMessage() }}\n        </div>\n      }\n\n      <!-- Angular 19: Bouton de connexion avec signals -->\n      <button\n        type=\"submit\"\n        class=\"btn btn-primary btn-login\"\n        [disabled]=\"!canSubmit()\"\n      >\n        @if (isLoading()) {\n          <span class=\"spinner\"></span>\n        }\n        {{ isLoading() ? 'Connexion...' : 'Se connecter' }}\n      </button>\n    </form>\n\n   \n\n    <div class=\"login-footer\">\n      <p>Pas encore de compte ?\n        <a routerLink=\"/register\" class=\"register-link\">Créer un compte fournisseur</a>\n      </p>\n    </div>\n  </div>\n</div>\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AC2Bc,IAAA,yBAAA,GAAA,KAAA;AAAK,IAAA,iBAAA,GAAA,oBAAA;AAAkB,IAAA,uBAAA;;;;;AAGvB,IAAA,yBAAA,GAAA,KAAA;AAAK,IAAA,iBAAA,GAAA,yBAAA;AAAuB,IAAA,uBAAA;;;;;AALhC,IAAA,yBAAA,GAAA,OAAA,CAAA;AACE,IAAA,qBAAA,GAAA,sDAAA,GAAA,GAAA,KAAA,EAAmC,GAAA,sDAAA,GAAA,GAAA,KAAA;AAMrC,IAAA,uBAAA;;;;AANE,IAAA,oBAAA;AAAA,IAAA,yBAAA,OAAA,SAAA,OAAA,OAAA,OAAA,MAAA,UAAA,OAAA,OAAA,OAAA,MAAA,OAAA,UAAA,KAAA,IAAA,EAAA;AAGA,IAAA,oBAAA;AAAA,IAAA,yBAAA,OAAA,SAAA,OAAA,OAAA,OAAA,MAAA,UAAA,OAAA,OAAA,OAAA,MAAA,OAAA,OAAA,KAAA,IAAA,EAAA;;;;;AA0BE,IAAA,yBAAA,GAAA,KAAA;AAAK,IAAA,iBAAA,GAAA,4BAAA;AAA0B,IAAA,uBAAA;;;;;AAG/B,IAAA,yBAAA,GAAA,KAAA;AAAK,IAAA,iBAAA,GAAA,wDAAA;AAAmD,IAAA,uBAAA;;;;;AAL5D,IAAA,yBAAA,GAAA,OAAA,CAAA;AACE,IAAA,qBAAA,GAAA,sDAAA,GAAA,GAAA,KAAA,EAAsC,GAAA,sDAAA,GAAA,GAAA,KAAA;AAMxC,IAAA,uBAAA;;;;AANE,IAAA,oBAAA;AAAA,IAAA,yBAAA,OAAA,YAAA,OAAA,OAAA,OAAA,SAAA,UAAA,OAAA,OAAA,OAAA,SAAA,OAAA,UAAA,KAAA,IAAA,EAAA;AAGA,IAAA,oBAAA;AAAA,IAAA,yBAAA,OAAA,YAAA,OAAA,OAAA,OAAA,SAAA,UAAA,OAAA,OAAA,OAAA,SAAA,OAAA,WAAA,KAAA,IAAA,EAAA;;;;;AASJ,IAAA,yBAAA,GAAA,OAAA,EAAA;AACE,IAAA,iBAAA,CAAA;AACF,IAAA,uBAAA;;;;AADE,IAAA,oBAAA;AAAA,IAAA,6BAAA,KAAA,OAAA,aAAA,GAAA,GAAA;;;;;AAWA,IAAA,oBAAA,GAAA,QAAA,EAAA;;;ADhEJ,IAAO,iBAAP,MAAO,gBAAc;EAcf;EACA;EACA;EACA;EAhBV;;EAGA,YAAY,OAAO,KAAK;EACxB,eAAe,OAAO,EAAE;EACxB,YAAY,OAAO,EAAE;EACrB,cAAc,OAAO,KAAK;;EAG1B,YAAY,SAAS,MAAM,KAAK,YAAW,KAAM,CAAC,KAAK,UAAS,CAAE;EAClE,WAAW,SAAS,MAAM,KAAK,aAAY,EAAG,SAAS,CAAC;EAExD,YACU,aACA,aACA,QACA,OAAqB;AAHrB,SAAA,cAAA;AACA,SAAA,cAAA;AACA,SAAA,SAAA;AACA,SAAA,QAAA;AAER,SAAK,YAAY,KAAK,YAAY,MAAM;MACtC,OAAO,CAAC,IAAI,CAAC,WAAW,UAAU,WAAW,KAAK,CAAC;MACnD,UAAU,CAAC,IAAI,CAAC,WAAW,UAAU,WAAW,UAAU,CAAC,CAAC,CAAC;KAC9D;AAGD,SAAK,UAAU,cAAc,UAAU,MAAK;AAC1C,WAAK,YAAY,IAAI,KAAK,UAAU,KAAK;IAC3C,CAAC;AAGD,SAAK,YAAY,IAAI,KAAK,UAAU,KAAK;AAGzC,WAAO,MAAK;AACV,UAAI,KAAK,SAAQ,GAAI;AACnB,gBAAQ,IAAI,+BAA0B,KAAK,aAAY,CAAE;MAC3D;AACA,UAAI,KAAK,UAAS,GAAI;AACpB,gBAAQ,IAAI,8BAAyB;MACvC;IACF,CAAC;EACH;EAEA,WAAQ;AAEN,QAAI,KAAK,YAAY,gBAAe,GAAI;AACtC,WAAK,OAAO,SAAS,CAAC,YAAY,CAAC;AACnC;IACF;AAGA,SAAK,UAAU,IAAI,KAAK,MAAM,SAAS,YAAY,WAAW,KAAK,YAAY;EACjF;EAEA,WAAQ;AACN,QAAI,KAAK,UAAU,OAAO;AAExB,WAAK,UAAU,IAAI,IAAI;AACvB,WAAK,aAAa,IAAI,EAAE;AAExB,YAAM,cAA4B;QAChC,OAAO,KAAK,UAAU,MAAM;QAC5B,UAAU,KAAK,UAAU,MAAM;;;AAGjC,WAAK,YAAY,MAAM,WAAW,EAAE,UAAU;QAC5C,MAAM,CAAC,aAAY;AACjB,eAAK,UAAU,IAAI,KAAK;AAGxB,gBAAM,WAAW,SAAS,YAAY;AAEtC,cAAI,aAAa,eAAe;AAE9B,iBAAK,OAAO,SAAS,CAAC,YAAY,CAAC;UACrC,WAAW,aAAa,SAAS;AAE/B,iBAAK,OAAO,SAAS,CAAC,QAAQ,CAAC;UACjC,OAAO;AACL,iBAAK,aAAa,IAAI,wEAA+D;AACrF,iBAAK,YAAY,OAAM;UACzB;QACF;QACA,OAAO,CAAC,UAAS;AACf,eAAK,UAAU,IAAI,KAAK;AACxB,eAAK,aAAa,IAAI,MAAM,OAAO,WAAW,oDAAiD;QACjG;OACD;IACH,OAAO;AACL,WAAK,qBAAoB;IAC3B;EACF;EAEQ,uBAAoB;AAC1B,WAAO,KAAK,KAAK,UAAU,QAAQ,EAAE,QAAQ,SAAM;AACjD,YAAM,UAAU,KAAK,UAAU,IAAI,GAAG;AACtC,eAAS,cAAa;IACxB,CAAC;EACH;;;;EAKA,gBAAgB,MAAoC;AAClD,QAAI,SAAS,eAAe;AAC1B,WAAK,UAAU,WAAW;QACxB,OAAO;QACP,UAAU;OACX;IACH,WAAW,SAAS,gBAAgB;AAClC,WAAK,UAAU,WAAW;QACxB,OAAO;QACP,UAAU;OACX;IACH;EACF;;;;EAKA,oBAAiB;AACf,YAAQ,IAAI,uCAAgC;AAG5C,UAAM,WAAW;MACf;MACA;MACA;MACA;MACA;;AAGF,YAAQ,IAAI,0DAAgD;AAE5D,aAAS,QAAQ,CAAC,KAAK,UAAS;AAC9B,YAAM,KAAK;QACT,QAAQ;QACR,SAAS,EAAE,gBAAgB,mBAAkB;QAC7C,MAAM,KAAK,UAAU,EAAE,OAAO,QAAQ,YAAY,OAAM,CAAE;OAC3D,EACA,KAAK,cAAW;AACf,gBAAQ,IAAI,UAAK,GAAG,cAAc,SAAS,MAAM,KAAK,SAAS,UAAU,GAAG;AAC5E,YAAI,SAAS,WAAW,GAAG;AACzB,gBAAM,iCAAyB,GAAG;UAAa,SAAS,MAAM,EAAE;QAClE;MACF,CAAC,EACA,MAAM,WAAQ;AACb,gBAAQ,IAAI,UAAK,GAAG,cAAc,MAAM,OAAO,EAAE;AACjD,YAAI,UAAU,SAAS,SAAS,GAAG;AACjC,gBAAM,2HAA0G;QAClH;MACF,CAAC;IACH,CAAC;AAED,YAAQ,IAAI,8CAAoC;EAClD;;EAGA,IAAI,QAAK;AAAK,WAAO,KAAK,UAAU,IAAI,OAAO;EAAG;EAClD,IAAI,WAAQ;AAAK,WAAO,KAAK,UAAU,IAAI,UAAU;EAAG;;qCA/J7C,iBAAc,4BAAA,WAAA,GAAA,4BAAA,WAAA,GAAA,4BAAA,MAAA,GAAA,4BAAA,cAAA,CAAA;EAAA;yEAAd,iBAAc,WAAA,CAAA,CAAA,WAAA,CAAA,GAAA,OAAA,IAAA,MAAA,IAAA,QAAA,CAAA,CAAA,GAAA,iBAAA,GAAA,CAAA,GAAA,YAAA,GAAA,CAAA,GAAA,cAAA,GAAA,CAAA,gBAAA,OAAA,GAAA,cAAA,GAAA,YAAA,WAAA,GAAA,CAAA,GAAA,YAAA,GAAA,CAAA,OAAA,OAAA,GAAA,CAAA,QAAA,SAAA,MAAA,SAAA,mBAAA,SAAA,eAAA,uBAAA,gBAAA,aAAA,kBAAA,OAAA,eAAA,OAAA,cAAA,SAAA,GAAA,cAAA,GAAA,CAAA,GAAA,kBAAA,GAAA,CAAA,OAAA,UAAA,GAAA,CAAA,QAAA,YAAA,MAAA,YAAA,mBAAA,YAAA,eAAA,sBAAA,gBAAA,gBAAA,kBAAA,OAAA,eAAA,OAAA,cAAA,SAAA,GAAA,cAAA,GAAA,CAAA,GAAA,SAAA,cAAA,GAAA,CAAA,QAAA,UAAA,GAAA,OAAA,eAAA,aAAA,GAAA,UAAA,GAAA,CAAA,GAAA,SAAA,GAAA,CAAA,GAAA,cAAA,GAAA,CAAA,cAAA,aAAA,GAAA,eAAA,CAAA,GAAA,UAAA,SAAA,wBAAA,IAAA,KAAA;AAAA,QAAA,KAAA,GAAA;ACd3B,MAAA,yBAAA,GAAA,OAAA,CAAA,EAA6B,GAAA,OAAA,CAAA,EACH,GAAA,OAAA,CAAA,EACI,GAAA,IAAA;AACpB,MAAA,iBAAA,GAAA,uBAAA;AAAqB,MAAA,uBAAA;AACzB,MAAA,yBAAA,GAAA,GAAA;AAAG,MAAA,iBAAA,GAAA,0CAAA;AAAkC,MAAA,uBAAA,EAAI;AAG3C,MAAA,yBAAA,GAAA,QAAA,CAAA;AAA8B,MAAA,qBAAA,YAAA,SAAA,mDAAA;AAAA,eAAY,IAAA,SAAA;MAAU,CAAA;AAElD,MAAA,yBAAA,GAAA,OAAA,CAAA,EAAwB,GAAA,SAAA,CAAA;AACH,MAAA,iBAAA,IAAA,OAAA;AAAK,MAAA,uBAAA;AACxB,MAAA,oBAAA,IAAA,SAAA,CAAA;AAaA,MAAA,qBAAA,IAAA,wCAAA,GAAA,GAAA,OAAA,CAAA;AAUF,MAAA,uBAAA;AAGA,MAAA,yBAAA,IAAA,OAAA,CAAA,EAAwB,IAAA,SAAA,CAAA;AACA,MAAA,iBAAA,IAAA,cAAA;AAAY,MAAA,uBAAA;AAClC,MAAA,oBAAA,IAAA,SAAA,CAAA;AAaA,MAAA,qBAAA,IAAA,wCAAA,GAAA,GAAA,OAAA,CAAA;AAUF,MAAA,uBAAA;AAGA,MAAA,qBAAA,IAAA,wCAAA,GAAA,GAAA,OAAA,EAAA;AAOA,MAAA,yBAAA,IAAA,UAAA,EAAA;AAKE,MAAA,qBAAA,IAAA,wCAAA,GAAA,GAAA,QAAA,EAAA;AAGA,MAAA,iBAAA,EAAA;AACF,MAAA,uBAAA,EAAS;AAKX,MAAA,yBAAA,IAAA,OAAA,EAAA,EAA0B,IAAA,GAAA;AACrB,MAAA,iBAAA,IAAA,yBAAA;AACD,MAAA,yBAAA,IAAA,KAAA,EAAA;AAAgD,MAAA,iBAAA,IAAA,gCAAA;AAA2B,MAAA,uBAAA,EAAI,EAC7E,EACA,EACF;;;AApFE,MAAA,oBAAA,CAAA;AAAA,MAAA,qBAAA,aAAA,IAAA,SAAA;AASA,MAAA,oBAAA,CAAA;AAAA,MAAA,sBAAA,eAAA,IAAA,SAAA,OAAA,OAAA,IAAA,MAAA,aAAA,IAAA,SAAA,OAAA,OAAA,IAAA,MAAA,QAAA;AAQF,MAAA,oBAAA;AAAA,MAAA,yBAAA,IAAA,SAAA,OAAA,OAAA,IAAA,MAAA,aAAA,IAAA,SAAA,OAAA,OAAA,IAAA,MAAA,WAAA,KAAA,EAAA;AAoBE,MAAA,oBAAA,CAAA;AAAA,MAAA,sBAAA,eAAA,IAAA,YAAA,OAAA,OAAA,IAAA,SAAA,aAAA,IAAA,YAAA,OAAA,OAAA,IAAA,SAAA,QAAA;AAQF,MAAA,oBAAA;AAAA,MAAA,yBAAA,IAAA,YAAA,OAAA,OAAA,IAAA,SAAA,aAAA,IAAA,YAAA,OAAA,OAAA,IAAA,SAAA,WAAA,KAAA,EAAA;AAaF,MAAA,oBAAA;AAAA,MAAA,wBAAA,IAAA,SAAA,IAAA,KAAA,EAAA;AAUE,MAAA,oBAAA;AAAA,MAAA,qBAAA,YAAA,CAAA,IAAA,UAAA,CAAA;AAEA,MAAA,oBAAA;AAAA,MAAA,wBAAA,IAAA,UAAA,IAAA,KAAA,EAAA;AAGA,MAAA,oBAAA;AAAA,MAAA,6BAAA,KAAA,IAAA,UAAA,IAAA,iBAAA,gBAAA,GAAA;;oBDtEI,cAAc,qBAAmB,oBAAA,sBAAA,iBAAA,sBAAA,oBAAA,iBAAE,cAAY,UAAA,GAAA,QAAA,CAAA,unRAAA,EAAA,CAAA;;;sEAI9C,gBAAc,CAAA;UAP1B;uBACW,aAAW,YACT,MAAI,SACP,CAAC,cAAc,qBAAqB,YAAY,GAAC,UAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;GAAA,QAAA,CAAA,kgPAAA,EAAA,CAAA;;;;6EAI/C,gBAAc,EAAA,WAAA,kBAAA,UAAA,oDAAA,YAAA,GAAA,CAAA;AAAA,GAAA;", "names": []}
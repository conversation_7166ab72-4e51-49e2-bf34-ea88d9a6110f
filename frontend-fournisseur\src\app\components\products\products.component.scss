.products-container {
  padding: 2rem;
  max-width: 1200px;
  margin: 0 auto;
  background: #f8f9fa;
  min-height: 100vh;
}

.products-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 2rem;
  background: white;
  padding: 1.5rem;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);

  h1 {
    margin: 0;
    color: #333;
    font-size: 2rem;
    font-weight: 600;
  }
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1rem;
  margin-bottom: 2rem;
}

.stat-card {
  background: white;
  padding: 1.5rem;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
  text-align: center;
  transition: transform 0.2s ease;

  &:hover {
    transform: translateY(-2px);
  }
}

.stat-number {
  font-size: 2.5rem;
  font-weight: bold;
  color: #007bff;
  margin-bottom: 0.5rem;
}

.stat-label {
  color: #666;
  font-size: 0.9rem;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.search-box {
  margin-bottom: 2rem;
}

.search-input {
  width: 100%;
  max-width: 400px;
  padding: 0.75rem 1rem;
  border: 2px solid #e9ecef;
  border-radius: 8px;
  font-size: 1rem;
  transition: border-color 0.2s ease;

  &:focus {
    outline: none;
    border-color: #007bff;
    box-shadow: 0 0 0 3px rgba(0, 123, 255, 0.1);
  }
}

.products-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));
  gap: 1.5rem;
  margin-bottom: 2rem;
}

.product-card {
  background: white;
  border-radius: 12px;
  padding: 1.5rem;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
  transition: all 0.3s ease;
  border: 1px solid #e9ecef;
  cursor: pointer;

  &:hover {
    transform: translateY(-4px);
    box-shadow: 0 8px 25px rgba(0,0,0,0.15);
  }
}

.product-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 1rem;

  h3 {
    margin: 0;
    color: #333;
    font-size: 1.25rem;
    font-weight: 600;
  }
}

.product-status {
  padding: 0.25rem 0.75rem;
  border-radius: 20px;
  font-size: 0.75rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;

  &.status-in-stock {
    background: #d4edda;
    color: #155724;
    border: 1px solid #c3e6cb;
  }

  &.status-low-stock {
    background: #fff3cd;
    color: #856404;
    border: 1px solid #ffeaa7;
  }

  &.status-out-of-stock {
    background: #f8d7da;
    color: #721c24;
    border: 1px solid #f5c6cb;
  }
}

.product-description {
  color: #666;
  margin-bottom: 1rem;
  line-height: 1.5;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.product-details {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
  padding: 0.75rem;
  background: #f8f9fa;
  border-radius: 8px;
}

// Styles pour les prix (inspirés de la page admin)
.price-container {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
  margin-bottom: 0.75rem;

  .price-initial, .price-final {
    display: flex;
    align-items: center;
    gap: 0.5rem;

    .price-label {
      font-size: 0.75rem;
      color: #6c757d;
      font-weight: 500;
    }

    .price-value {
      font-weight: bold;

      &.initial {
        color: #6c757d;

        &.crossed {
          text-decoration: line-through;
        }
      }

      &.final {
        color: #007bff;
        font-size: 1.1rem;
      }
    }
  }

  .discount-badge {
    background: linear-gradient(135deg, #dc3545, #c82333);
    color: white;
    padding: 0.25rem 0.5rem;
    border-radius: 12px;
    font-size: 0.75rem;
    font-weight: bold;
    align-self: flex-start;
    box-shadow: 0 2px 4px rgba(220, 53, 69, 0.3);
  }
}

// Styles pour le stock
.stock-container {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;

  .stock-value {
    font-size: 0.875rem;
    color: #28a745;
    font-weight: 500;

    &.stock-critique {
      color: #dc3545;
      font-weight: bold;
    }
  }

  .stock-alert {
    font-size: 0.75rem;
    color: #dc3545;
    font-weight: bold;
    background: rgba(220, 53, 69, 0.1);
    padding: 0.125rem 0.25rem;
    border-radius: 4px;
  }
}

// Styles pour les métadonnées du produit
.product-meta {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
  margin-bottom: 0.5rem;

  .product-category {
    font-size: 0.875rem;
    color: #007bff;
    font-weight: 500;
  }

  .product-brand {
    font-size: 0.75rem;
    color: #6c757d;
    font-style: italic;
  }

  .product-reference {
    font-size: 0.75rem;
    color: #6c757d;

    .ref-original {
      font-weight: 500;
    }

    .ref-fournisseur {
      color: #28a745;
      margin-left: 0.25rem;
    }
  }
}

// Badge "En avant" (si on l'ajoute plus tard)
.featured-badge {
  position: absolute;
  top: 0.5rem;
  right: 0.5rem;
  background: linear-gradient(135deg, #ffc107, #e0a800);
  color: #212529;
  padding: 0.25rem 0.5rem;
  border-radius: 12px;
  font-size: 0.75rem;
  font-weight: bold;
  box-shadow: 0 2px 4px rgba(255, 193, 7, 0.3);
}

.promotion-badge {
    background: #dc3545;
    color: white;
    padding: 0.125rem 0.5rem;
    border-radius: 12px;
    font-size: 0.7rem;
    font-weight: bold;
    text-transform: uppercase;
  }

.product-stock {
  color: #666;
  font-weight: 500;
}

.product-actions {
  display: flex;
  gap: 0.5rem;
  justify-content: flex-end;
}

.btn {
  padding: 0.5rem 1rem;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  font-size: 0.875rem;
  font-weight: 500;
  transition: all 0.2s ease;
  text-decoration: none;
  display: inline-flex;
  align-items: center;
  gap: 0.25rem;

  &:hover {
    transform: translateY(-1px);
  }

  &:active {
    transform: translateY(0);
  }

  &:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none;
  }
}

.btn-primary {
  background: linear-gradient(135deg, #007bff, #0056b3);
  color: white;
  box-shadow: 0 2px 4px rgba(0, 123, 255, 0.3);

  &:hover {
    background: linear-gradient(135deg, #0056b3, #004085);
    box-shadow: 0 4px 8px rgba(0, 123, 255, 0.4);
  }
}

.btn-secondary {
  background: #6c757d;
  color: white;
  box-shadow: 0 2px 4px rgba(108, 117, 125, 0.3);

  &:hover {
    background: #545b62;
    box-shadow: 0 4px 8px rgba(108, 117, 125, 0.4);
  }
}

.btn-danger {
  background: linear-gradient(135deg, #dc3545, #c82333);
  color: white;
  box-shadow: 0 2px 4px rgba(220, 53, 69, 0.3);

  &:hover {
    background: linear-gradient(135deg, #c82333, #a71e2a);
    box-shadow: 0 4px 8px rgba(220, 53, 69, 0.4);
  }
}

.btn-outline-primary {
  background: transparent;
  color: #007bff;
  border: 2px solid #007bff;

  &:hover {
    background: #007bff;
    color: white;
  }
}

.btn-outline-secondary {
  background: transparent;
  color: #6c757d;
  border: 2px solid #6c757d;

  &:hover {
    background: #6c757d;
    color: white;
  }
}

.btn-sm {
  padding: 0.25rem 0.5rem;
  font-size: 0.75rem;
}

.btn-info {
  background: linear-gradient(135deg, #17a2b8, #138496);
  color: white;
  box-shadow: 0 2px 4px rgba(23, 162, 184, 0.3);

  &:hover {
    background: linear-gradient(135deg, #138496, #117a8b);
    box-shadow: 0 4px 8px rgba(23, 162, 184, 0.4);
  }
}

.loading {
  text-align: center;
  padding: 4rem 2rem;
  color: #666;

  .spinner {
    width: 40px;
    height: 40px;
    border: 4px solid #f3f3f3;
    border-top: 4px solid #007bff;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin: 0 auto 1rem;
  }

  p {
    font-size: 1.1rem;
    margin: 0;
  }
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.no-products {
  text-align: center;
  padding: 4rem 2rem;
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);

  h3 {
    color: #333;
    margin-bottom: 1rem;
    font-size: 1.5rem;
  }

  p {
    color: #666;
    margin-bottom: 2rem;
    font-size: 1.1rem;
  }
}

// Modal styles
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  padding: 1rem;
}

.modal-content {
  background: white;
  border-radius: 12px;
  width: 100%;
  max-width: 800px;
  max-height: 90vh;
  overflow-y: auto;
  box-shadow: 0 10px 30px rgba(0,0,0,0.3);
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1.5rem;
  border-bottom: 1px solid #e9ecef;

  h3 {
    margin: 0;
    color: #333;
    font-size: 1.5rem;
  }
}

.modal-actions {
  display: flex;
  gap: 0.5rem;
}

.product-form {
  padding: 1.5rem;
}

.form-section {
  margin-bottom: 2rem;
  padding: 1.5rem;
  background: #f8f9fa;
  border-radius: 8px;
  border: 1px solid #e9ecef;

  h4 {
    margin: 0 0 1rem 0;
    color: #333;
    font-size: 1.2rem;
    font-weight: 600;
    padding-bottom: 0.5rem;
    border-bottom: 2px solid #007bff;
  }
}

.form-row {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 1rem;
  margin-bottom: 1rem;

  @media (max-width: 768px) {
    grid-template-columns: 1fr;
  }
}

.form-group {
  margin-bottom: 1rem;

  label {
    display: block;
    margin-bottom: 0.5rem;
    color: #333;
    font-weight: 500;
    font-size: 0.9rem;
  }
}

.form-control {
  width: 100%;
  padding: 0.75rem;
  border: 2px solid #e9ecef;
  border-radius: 6px;
  font-size: 1rem;
  transition: border-color 0.2s ease;

  &:focus {
    outline: none;
    border-color: #007bff;
    box-shadow: 0 0 0 3px rgba(0, 123, 255, 0.1);
  }

  &.ng-invalid.ng-touched {
    border-color: #dc3545;
  }
}

.input-with-button {
  display: flex;
  gap: 0.5rem;

  .form-control {
    flex: 1;
  }

  .btn {
    flex-shrink: 0;
  }
}

.form-text {
  font-size: 0.875rem;
  color: #6c757d;
  margin-top: 0.25rem;
}

.text-muted {
  color: #6c757d;
}

.price-info {
  background: #e7f3ff;
  border: 1px solid #b3d9ff;
  border-radius: 6px;
  padding: 1rem;
  margin-top: 0.5rem;

  strong {
    color: #0056b3;
    font-size: 1.1rem;
  }

  small {
    display: block;
    margin-top: 0.25rem;
  }
}

.form-actions {
  display: flex;
  justify-content: flex-end;
  gap: 1rem;
  padding: 1.5rem;
  border-top: 1px solid #e9ecef;
  background: #f8f9fa;
  margin: 0 -1.5rem -1.5rem -1.5rem;
  border-radius: 0 0 12px 12px;
}

// Styles pour la gestion des images
.existing-images {
  margin-bottom: 1.5rem;
  padding: 1rem;
  background: #e8f5e8;
  border-radius: 8px;
  border: 1px solid #c3e6cb;

  h6 {
    margin: 0 0 0.75rem 0;
    color: #155724;
    font-weight: 600;
  }
}

// Styles pour l'upload d'images
.image-upload-actions {
  display: flex;
  align-items: center;
  gap: 10px;
  margin-bottom: 10px;

  .btn {
    display: flex;
    align-items: center;
    gap: 5px;

    &:disabled {
      opacity: 0.6;
      cursor: not-allowed;
    }
  }

  .form-text {
    margin: 0;
    font-size: 0.875rem;
  }
}

.image-preview-container {
  display: flex;
  flex-wrap: wrap;
  gap: 1rem;
  margin-top: 1rem;
}

.image-preview {
  position: relative;
  width: 120px;
  height: 120px;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);

  &.existing {
    border: 2px solid #28a745;
    opacity: 0.9;
  }

  &.new {
    border: 2px solid #007bff;
  }

  img {
    width: 100%;
    height: 100%;
    object-fit: cover;
  }
}

.image-info {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  background: linear-gradient(transparent, rgba(0, 0, 0, 0.8));
  color: white;
  padding: 0.5rem 0.25rem 0.25rem;
  font-size: 0.75rem;

  .image-name {
    display: block;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    font-weight: 500;
  }

  .image-main {
    color: #ffc107;
    font-weight: bold;
    font-size: 0.7rem;
  }
}

.btn-remove {
  position: absolute;
  top: 0.25rem;
  right: 0.25rem;
  background: rgba(220, 53, 69, 0.9);
  color: white;
  border: none;
  border-radius: 50%;
  width: 24px;
  height: 24px;
  cursor: pointer;
  font-size: 16px;
  line-height: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: background-color 0.2s ease;

  &:hover {
    background: rgba(220, 53, 69, 1);
  }
}

// Responsive design
@media (max-width: 768px) {
  .products-container {
    padding: 1rem;
  }

  .products-header {
    flex-direction: column;
    gap: 1rem;
    text-align: center;

    h1 {
      font-size: 1.5rem;
    }
  }

  .stats-grid {
    grid-template-columns: 1fr 1fr;
  }

  .products-grid {
    grid-template-columns: 1fr;
  }

  .product-actions {
    flex-direction: column;
  }

  .modal-content {
    margin: 0.5rem;
    max-height: 95vh;
  }

  .form-actions {
    flex-direction: column;
  }
}

// Styles pour le modal des détails
.details-modal {
  max-width: 900px;
}

.product-details-content {
  padding: 1.5rem;
}

.details-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 2rem;
  margin-bottom: 2rem;

  @media (max-width: 768px) {
    grid-template-columns: 1fr;
  }
}

.detail-section {
  background: #f8f9fa;
  padding: 1.5rem;
  border-radius: 8px;
  border: 1px solid #e9ecef;

  h4 {
    margin: 0 0 1rem 0;
    color: #333;
    font-size: 1.1rem;
    font-weight: 600;
    padding-bottom: 0.5rem;
    border-bottom: 2px solid #007bff;
  }
}

.detail-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.5rem 0;
  border-bottom: 1px solid #e9ecef;

  &:last-child {
    border-bottom: none;
  }

  label {
    font-weight: 600;
    color: #555;
    margin-right: 1rem;
  }

  span {
    color: #333;
    text-align: right;
    flex: 1;
  }
}

.promotion-price {
  color: #dc3545;
  font-weight: bold;
}

.images-gallery {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
  gap: 1rem;
}

.image-item {
  position: relative;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);

  img {
    width: 100%;
    height: 120px;
    object-fit: cover;
  }

  .main-badge {
    position: absolute;
    top: 0.25rem;
    right: 0.25rem;
    background: #ffc107;
    color: #333;
    padding: 0.125rem 0.5rem;
    border-radius: 12px;
    font-size: 0.7rem;
    font-weight: bold;
  }
}

.details-actions {
  display: flex;
  justify-content: flex-end;
  gap: 1rem;
  padding: 1.5rem;
  border-top: 1px solid #e9ecef;
  background: #f8f9fa;
  margin: 0 -1.5rem -1.5rem -1.5rem;
  border-radius: 0 0 12px 12px;
}

/* Nouveaux styles inspirés du stock dépôt */
.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 32px;
  padding: 24px;
  background: white;
  border-radius: 12px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);

  .header-content h1 {
    margin: 0 0 8px 0;
    font-size: 28px;
    font-weight: 700;
    color: #1f2937;
  }

  .header-content p {
    margin: 0;
    color: #6b7280;
    font-size: 16px;
  }
}

.stats-section {
  margin-bottom: 32px;

  .stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;
  }

  .stat-card {
    display: flex;
    align-items: center;
    gap: 16px;
    padding: 20px;
    background: white;
    border-radius: 12px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    transition: transform 0.2s ease;

    &:hover {
      transform: translateY(-2px);
    }

    &.alert {
      background: #fef2f2;
      border-left: 4px solid #ef4444;
    }

    .stat-icon {
      font-size: 32px;
      width: 48px;
      height: 48px;
      display: flex;
      align-items: center;
      justify-content: center;
      background: #f3f4f6;
      border-radius: 50%;
    }

    .stat-value {
      font-size: 24px;
      font-weight: 700;
      color: #1f2937;
      margin-bottom: 4px;
    }

    .stat-label {
      font-size: 14px;
      color: #6b7280;
      font-weight: 500;
    }
  }
}

.toolbar {
  margin-bottom: 24px;
  padding: 20px;
  background: white;
  border-radius: 12px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);

  .search-section {
    display: flex;
    align-items: center;
    gap: 24px;
    flex-wrap: wrap;
  }

  .search-box {
    position: relative;
    flex: 1;
    min-width: 300px;

    input {
      width: 100%;
      padding: 12px 16px 12px 40px;
      border: 1px solid #d1d5db;
      border-radius: 8px;
      font-size: 14px;
      background: #f9fafb;

      &:focus {
        outline: none;
        border-color: #3b82f6;
        background: white;
      }
    }

    .search-icon {
      position: absolute;
      left: 12px;
      top: 50%;
      transform: translateY(-50%);
      color: #6b7280;
      font-size: 16px;
    }
  }

  .filters {
    display: flex;
    gap: 16px;
    flex-wrap: wrap;

    .filter-checkbox {
      display: flex;
      align-items: center;
      gap: 8px;
      cursor: pointer;
      font-size: 14px;
      color: #374151;

      input[type="checkbox"] {
        width: 16px;
        height: 16px;
        accent-color: #3b82f6;
      }
    }
  }
}

/* Classes de niveau de stock */
.stock-empty {
  color: #dc2626;
  font-weight: 700;
}

.stock-low {
  color: #f59e0b;
  font-weight: 600;
}

.stock-medium {
  color: #3b82f6;
  font-weight: 500;
}

.stock-ok {
  color: #059669;
  font-weight: 500;
}

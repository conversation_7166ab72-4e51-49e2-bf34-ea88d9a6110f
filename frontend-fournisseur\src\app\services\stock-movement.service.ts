import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Observable, BehaviorSubject } from 'rxjs';
import { environment } from '../../environments/environment';

export interface StockMovement {
  id: string;
  productId: number;
  productName: string;
  type: 'in' | 'out';
  quantity: number;
  reason: string;
  oldStock: number;
  newStock: number;
  date: string;
  user: string;
}

export interface CreateStockMovementDto {
  productId: number;
  productName: string;
  type: 'in' | 'out';
  quantity: number;
  reason: string;
  oldStock: number;
  newStock: number;
}

@Injectable({
  providedIn: 'root'
})
export class StockMovementService {
  private readonly API_URL = `${environment.apiUrl}/api/stock-movements`;
  private movementsSubject = new BehaviorSubject<StockMovement[]>([]);
  public movements$ = this.movementsSubject.asObservable();

  // Stockage local temporaire des mouvements
  private localMovements: StockMovement[] = [];

  constructor(private http: HttpClient) {
    this.loadMovementsFromStorage();
  }

  /**
   * Charger les mouvements depuis le localStorage
   */
  private loadMovementsFromStorage(): void {
    try {
      const stored = localStorage.getItem('stock-movements');
      if (stored) {
        this.localMovements = JSON.parse(stored);
        this.movementsSubject.next(this.localMovements);
      }
    } catch (error) {
      console.error('Erreur lors du chargement des mouvements:', error);
      this.localMovements = [];
    }
  }

  /**
   * Sauvegarder les mouvements dans le localStorage
   */
  private saveMovementsToStorage(): void {
    try {
      localStorage.setItem('stock-movements', JSON.stringify(this.localMovements));
    } catch (error) {
      console.error('Erreur lors de la sauvegarde des mouvements:', error);
    }
  }

  /**
   * Obtenir tous les mouvements de stock
   */
  getAllMovements(): Observable<StockMovement[]> {
    // Pour l'instant, retourner les mouvements locaux
    // Plus tard, cela pourrait être remplacé par un appel API
    return new Observable(observer => {
      observer.next(this.localMovements);
      observer.complete();
    });
  }

  /**
   * Ajouter un nouveau mouvement de stock
   */
  addMovement(movementDto: CreateStockMovementDto): Observable<StockMovement> {
    return new Observable(observer => {
      try {
        const movement: StockMovement = {
          id: this.generateId(),
          ...movementDto,
          date: new Date().toISOString(),
          user: 'Fournisseur' // À remplacer par l'utilisateur connecté
        };

        // Ajouter au début de la liste (plus récent en premier)
        this.localMovements.unshift(movement);
        
        // Limiter à 100 mouvements pour éviter une surcharge
        if (this.localMovements.length > 100) {
          this.localMovements = this.localMovements.slice(0, 100);
        }

        this.saveMovementsToStorage();
        this.movementsSubject.next(this.localMovements);

        observer.next(movement);
        observer.complete();
      } catch (error) {
        observer.error(error);
      }
    });
  }

  /**
   * Obtenir les mouvements pour un produit spécifique
   */
  getMovementsByProduct(productId: number): Observable<StockMovement[]> {
    return new Observable(observer => {
      const productMovements = this.localMovements.filter(m => m.productId === productId);
      observer.next(productMovements);
      observer.complete();
    });
  }

  /**
   * Supprimer tous les mouvements (utile pour les tests)
   */
  clearAllMovements(): Observable<boolean> {
    return new Observable(observer => {
      try {
        this.localMovements = [];
        this.saveMovementsToStorage();
        this.movementsSubject.next(this.localMovements);
        observer.next(true);
        observer.complete();
      } catch (error) {
        observer.error(error);
      }
    });
  }

  /**
   * Générer un ID unique pour les mouvements
   */
  private generateId(): string {
    return Date.now().toString(36) + Math.random().toString(36).substr(2);
  }

  /**
   * Obtenir les statistiques des mouvements
   */
  getMovementStats(): Observable<{
    totalMovements: number;
    entriesCount: number;
    exitsCount: number;
    totalQuantityIn: number;
    totalQuantityOut: number;
  }> {
    return new Observable(observer => {
      const stats = {
        totalMovements: this.localMovements.length,
        entriesCount: this.localMovements.filter(m => m.type === 'in').length,
        exitsCount: this.localMovements.filter(m => m.type === 'out').length,
        totalQuantityIn: this.localMovements
          .filter(m => m.type === 'in')
          .reduce((sum, m) => sum + m.quantity, 0),
        totalQuantityOut: this.localMovements
          .filter(m => m.type === 'out')
          .reduce((sum, m) => sum + m.quantity, 0)
      };
      
      observer.next(stats);
      observer.complete();
    });
  }
}

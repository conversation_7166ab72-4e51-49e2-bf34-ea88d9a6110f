import {
  AuthService
} from "./chunk-ZSXXNL7U.js";
import {
  ActivatedRoute,
  Router,
  RouterLink,
  RouterModule
} from "./chunk-6BVUYNW4.js";
import {
  Default<PERSON><PERSON>ue<PERSON>ccessor,
  FormBuilder,
  FormControlName,
  FormGroupDirective,
  NgControlStatus,
  NgControlStatusGroup,
  ReactiveFormsModule,
  Validators,
  ɵNgNoValidate
} from "./chunk-HQBVYEOO.js";
import "./chunk-7JDDWGD3.js";
import {
  CommonModule,
  Component,
  computed,
  effect,
  setClassMetadata,
  signal,
  ɵsetClassDebugInfo,
  ɵɵadvance,
  ɵɵclassProp,
  ɵɵconditional,
  ɵɵdefineComponent,
  ɵɵdirectiveInject,
  ɵɵelement,
  ɵɵelementEnd,
  ɵɵelementStart,
  ɵɵlistener,
  ɵɵnextContext,
  ɵɵproperty,
  ɵɵtemplate,
  ɵɵtext,
  ɵɵtextInterpolate1
} from "./chunk-UBZQS7JS.js";

// src/app/components/auth/login/login.component.ts
function LoginComponent_Conditional_12_Conditional_1_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "div");
    \u0275\u0275text(1, "L'email est requis");
    \u0275\u0275elementEnd();
  }
}
function LoginComponent_Conditional_12_Conditional_2_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "div");
    \u0275\u0275text(1, "Format d'email invalide");
    \u0275\u0275elementEnd();
  }
}
function LoginComponent_Conditional_12_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "div", 7);
    \u0275\u0275template(1, LoginComponent_Conditional_12_Conditional_1_Template, 2, 0, "div")(2, LoginComponent_Conditional_12_Conditional_2_Template, 2, 0, "div");
    \u0275\u0275elementEnd();
  }
  if (rf & 2) {
    const ctx_r0 = \u0275\u0275nextContext();
    \u0275\u0275advance();
    \u0275\u0275conditional((ctx_r0.email == null ? null : ctx_r0.email.errors == null ? null : ctx_r0.email.errors["required"]) ? 1 : -1);
    \u0275\u0275advance();
    \u0275\u0275conditional((ctx_r0.email == null ? null : ctx_r0.email.errors == null ? null : ctx_r0.email.errors["email"]) ? 2 : -1);
  }
}
function LoginComponent_Conditional_17_Conditional_1_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "div");
    \u0275\u0275text(1, "Le mot de passe est requis");
    \u0275\u0275elementEnd();
  }
}
function LoginComponent_Conditional_17_Conditional_2_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "div");
    \u0275\u0275text(1, "Le mot de passe doit contenir au moins 6 caract\xE8res");
    \u0275\u0275elementEnd();
  }
}
function LoginComponent_Conditional_17_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "div", 7);
    \u0275\u0275template(1, LoginComponent_Conditional_17_Conditional_1_Template, 2, 0, "div")(2, LoginComponent_Conditional_17_Conditional_2_Template, 2, 0, "div");
    \u0275\u0275elementEnd();
  }
  if (rf & 2) {
    const ctx_r0 = \u0275\u0275nextContext();
    \u0275\u0275advance();
    \u0275\u0275conditional((ctx_r0.password == null ? null : ctx_r0.password.errors == null ? null : ctx_r0.password.errors["required"]) ? 1 : -1);
    \u0275\u0275advance();
    \u0275\u0275conditional((ctx_r0.password == null ? null : ctx_r0.password.errors == null ? null : ctx_r0.password.errors["minlength"]) ? 2 : -1);
  }
}
function LoginComponent_Conditional_18_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "div", 10);
    \u0275\u0275text(1);
    \u0275\u0275elementEnd();
  }
  if (rf & 2) {
    const ctx_r0 = \u0275\u0275nextContext();
    \u0275\u0275advance();
    \u0275\u0275textInterpolate1(" ", ctx_r0.errorMessage(), " ");
  }
}
function LoginComponent_Conditional_20_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275element(0, "span", 12);
  }
}
var LoginComponent = class _LoginComponent {
  formBuilder;
  authService;
  router;
  route;
  loginForm;
  // Angular 19: Signals pour l'état du composant
  isLoading = signal(false);
  errorMessage = signal("");
  returnUrl = signal("");
  isFormValid = signal(false);
  // Angular 19: Computed signals pour l'état du formulaire
  canSubmit = computed(() => this.isFormValid() && !this.isLoading());
  hasError = computed(() => this.errorMessage().length > 0);
  constructor(formBuilder, authService, router, route) {
    this.formBuilder = formBuilder;
    this.authService = authService;
    this.router = router;
    this.route = route;
    this.loginForm = this.formBuilder.group({
      email: ["", [Validators.required, Validators.email]],
      password: ["", [Validators.required, Validators.minLength(6)]]
    });
    this.loginForm.statusChanges.subscribe(() => {
      this.isFormValid.set(this.loginForm.valid);
    });
    this.isFormValid.set(this.loginForm.valid);
    effect(() => {
      if (this.hasError()) {
        console.log("\u274C Erreur de connexion:", this.errorMessage());
      }
      if (this.isLoading()) {
        console.log("\u23F3 Connexion en cours...");
      }
    });
  }
  ngOnInit() {
    if (this.authService.isAuthenticated()) {
      this.router.navigate(["/dashboard"]);
      return;
    }
    this.returnUrl.set(this.route.snapshot.queryParams["returnUrl"] || "/dashboard");
  }
  onSubmit() {
    if (this.loginForm.valid) {
      this.isLoading.set(true);
      this.errorMessage.set("");
      const credentials = {
        email: this.loginForm.value.email,
        password: this.loginForm.value.password
        // Utiliser password directement
      };
      this.authService.login(credentials).subscribe({
        next: (response) => {
          this.isLoading.set(false);
          const userRole = response.utilisateur.role;
          if (userRole === "Fournisseur") {
            this.router.navigate(["/dashboard"]);
          } else if (userRole === "Admin") {
            this.router.navigate(["/admin"]);
          } else {
            this.errorMessage.set("Acc\xE8s r\xE9serv\xE9 aux fournisseurs et administrateurs uniquement.");
            this.authService.logout();
          }
        },
        error: (error) => {
          this.isLoading.set(false);
          this.errorMessage.set(error.error?.message || "Erreur de connexion. V\xE9rifiez vos identifiants.");
        }
      });
    } else {
      this.markFormGroupTouched();
    }
  }
  markFormGroupTouched() {
    Object.keys(this.loginForm.controls).forEach((key) => {
      const control = this.loginForm.get(key);
      control?.markAsTouched();
    });
  }
  /**
   * Remplir le formulaire avec des comptes de test
   */
  fillTestAccount(type) {
    if (type === "fournisseur") {
      this.loginForm.patchValue({
        email: "<EMAIL>",
        password: "Fournisseur123!"
      });
    } else if (type === "fournisseur2") {
      this.loginForm.patchValue({
        email: "<EMAIL>",
        password: "password123"
      });
    }
  }
  /**
   * Tester la connexion API
   */
  testApiConnection() {
    console.log("\u{1F50D} Test de la connexion API...");
    const testUrls = [
      "http://localhost:7264/api/Auth/login",
      "https://localhost:7264/api/Auth/login",
      "http://localhost:5000/api/Auth/login",
      "https://localhost:5001/api/Auth/login",
      "http://localhost:7265/api/Auth/login"
    ];
    console.log("\u{1F50D} Test de connectivit\xE9 sur plusieurs ports...");
    testUrls.forEach((url, index) => {
      fetch(url, {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({ email: "test", motDePasse: "test" })
      }).then((response) => {
        console.log(`\u2705 ${url} - Status: ${response.status} (${response.statusText})`);
        if (response.status !== 0) {
          alert(`\u2705 Backend trouv\xE9 sur: ${url}
Status: ${response.status}`);
        }
      }).catch((error) => {
        console.log(`\u274C ${url} - Erreur: ${error.message}`);
        if (index === testUrls.length - 1) {
          alert("\u274C Aucun backend accessible sur les ports test\xE9s.\n\nV\xE9rifiez que votre backend ASP.NET Core est d\xE9marr\xE9.");
        }
      });
    });
    console.log("\u2699\uFE0F Configuration: Backend API test");
  }
  // Getters pour faciliter l'accès aux contrôles dans le template
  get email() {
    return this.loginForm.get("email");
  }
  get password() {
    return this.loginForm.get("password");
  }
  static \u0275fac = function LoginComponent_Factory(__ngFactoryType__) {
    return new (__ngFactoryType__ || _LoginComponent)(\u0275\u0275directiveInject(FormBuilder), \u0275\u0275directiveInject(AuthService), \u0275\u0275directiveInject(Router), \u0275\u0275directiveInject(ActivatedRoute));
  };
  static \u0275cmp = /* @__PURE__ */ \u0275\u0275defineComponent({ type: _LoginComponent, selectors: [["app-login"]], decls: 27, vars: 11, consts: [[1, "login-container"], [1, "login-card"], [1, "login-header"], ["autocomplete", "off", 1, "login-form", 3, "ngSubmit", "formGroup"], [1, "form-group"], ["for", "email"], ["type", "email", "id", "email", "formControlName", "email", "placeholder", "Votre adresse email", "autocomplete", "new-email", "autocapitalize", "off", "autocorrect", "off", "spellcheck", "false", 1, "form-control"], [1, "invalid-feedback"], ["for", "password"], ["type", "password", "id", "password", "formControlName", "password", "placeholder", "Votre mot de passe", "autocomplete", "new-password", "autocapitalize", "off", "autocorrect", "off", "spellcheck", "false", 1, "form-control"], [1, "alert", "alert-danger"], ["type", "submit", 1, "btn", "btn-primary", "btn-login", 3, "disabled"], [1, "spinner"], [1, "login-footer"], ["routerLink", "/register", 1, "register-link"]], template: function LoginComponent_Template(rf, ctx) {
    if (rf & 1) {
      \u0275\u0275elementStart(0, "div", 0)(1, "div", 1)(2, "div", 2)(3, "h1");
      \u0275\u0275text(4, "Connexion Fournisseur");
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(5, "p");
      \u0275\u0275text(6, "Acc\xE9dez \xE0 votre espace fournisseur");
      \u0275\u0275elementEnd()();
      \u0275\u0275elementStart(7, "form", 3);
      \u0275\u0275listener("ngSubmit", function LoginComponent_Template_form_ngSubmit_7_listener() {
        return ctx.onSubmit();
      });
      \u0275\u0275elementStart(8, "div", 4)(9, "label", 5);
      \u0275\u0275text(10, "Email");
      \u0275\u0275elementEnd();
      \u0275\u0275element(11, "input", 6);
      \u0275\u0275template(12, LoginComponent_Conditional_12_Template, 3, 2, "div", 7);
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(13, "div", 4)(14, "label", 8);
      \u0275\u0275text(15, "Mot de passe");
      \u0275\u0275elementEnd();
      \u0275\u0275element(16, "input", 9);
      \u0275\u0275template(17, LoginComponent_Conditional_17_Template, 3, 2, "div", 7);
      \u0275\u0275elementEnd();
      \u0275\u0275template(18, LoginComponent_Conditional_18_Template, 2, 1, "div", 10);
      \u0275\u0275elementStart(19, "button", 11);
      \u0275\u0275template(20, LoginComponent_Conditional_20_Template, 1, 0, "span", 12);
      \u0275\u0275text(21);
      \u0275\u0275elementEnd()();
      \u0275\u0275elementStart(22, "div", 13)(23, "p");
      \u0275\u0275text(24, "Pas encore de compte ? ");
      \u0275\u0275elementStart(25, "a", 14);
      \u0275\u0275text(26, "Cr\xE9er un compte fournisseur");
      \u0275\u0275elementEnd()()()()();
    }
    if (rf & 2) {
      \u0275\u0275advance(7);
      \u0275\u0275property("formGroup", ctx.loginForm);
      \u0275\u0275advance(4);
      \u0275\u0275classProp("is-invalid", (ctx.email == null ? null : ctx.email.invalid) && (ctx.email == null ? null : ctx.email.touched));
      \u0275\u0275advance();
      \u0275\u0275conditional((ctx.email == null ? null : ctx.email.invalid) && (ctx.email == null ? null : ctx.email.touched) ? 12 : -1);
      \u0275\u0275advance(4);
      \u0275\u0275classProp("is-invalid", (ctx.password == null ? null : ctx.password.invalid) && (ctx.password == null ? null : ctx.password.touched));
      \u0275\u0275advance();
      \u0275\u0275conditional((ctx.password == null ? null : ctx.password.invalid) && (ctx.password == null ? null : ctx.password.touched) ? 17 : -1);
      \u0275\u0275advance();
      \u0275\u0275conditional(ctx.hasError() ? 18 : -1);
      \u0275\u0275advance();
      \u0275\u0275property("disabled", !ctx.canSubmit());
      \u0275\u0275advance();
      \u0275\u0275conditional(ctx.isLoading() ? 20 : -1);
      \u0275\u0275advance();
      \u0275\u0275textInterpolate1(" ", ctx.isLoading() ? "Connexion..." : "Se connecter", " ");
    }
  }, dependencies: [CommonModule, ReactiveFormsModule, \u0275NgNoValidate, DefaultValueAccessor, NgControlStatus, NgControlStatusGroup, FormGroupDirective, FormControlName, RouterModule, RouterLink], styles: ['\n\n.login-container[_ngcontent-%COMP%] {\n  min-height: 100vh;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  background:\n    linear-gradient(\n      135deg,\n      #f8fafc 0%,\n      #e2e8f0 100%);\n  padding: 2rem;\n  position: relative;\n}\n.login-container[_ngcontent-%COMP%]::before {\n  content: "";\n  position: absolute;\n  top: 0;\n  left: 0;\n  right: 0;\n  bottom: 0;\n  background-image:\n    radial-gradient(\n      circle at 25% 25%,\n      rgba(59, 130, 246, 0.05) 0%,\n      transparent 50%),\n    radial-gradient(\n      circle at 75% 75%,\n      rgba(16, 185, 129, 0.05) 0%,\n      transparent 50%);\n  pointer-events: none;\n}\n.login-card[_ngcontent-%COMP%] {\n  background: rgba(255, 255, 255, 0.98);\n  -webkit-backdrop-filter: blur(10px);\n  backdrop-filter: blur(10px);\n  border: 1px solid rgba(255, 255, 255, 0.2);\n  border-radius: 16px;\n  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);\n  padding: 3rem;\n  width: 100%;\n  max-width: 480px;\n  position: relative;\n  transition: all 0.3s ease;\n}\n.login-card[_ngcontent-%COMP%]:hover {\n  box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.15), 0 20px 25px -5px rgba(0, 0, 0, 0.1);\n}\n.login-card[_ngcontent-%COMP%]::before {\n  content: "";\n  position: absolute;\n  top: 0;\n  left: 0;\n  right: 0;\n  height: 4px;\n  background:\n    linear-gradient(\n      90deg,\n      #3b82f6 0%,\n      #10b981 100%);\n  border-radius: 16px 16px 0 0;\n}\n.login-header[_ngcontent-%COMP%] {\n  text-align: center;\n  margin-bottom: 2.5rem;\n  position: relative;\n}\n.login-header[_ngcontent-%COMP%]::before {\n  content: "\\1f3e2";\n  display: block;\n  font-size: 2.5rem;\n  margin-bottom: 1rem;\n  opacity: 0.8;\n}\n.login-header[_ngcontent-%COMP%]   h1[_ngcontent-%COMP%] {\n  color: #1e293b;\n  margin-bottom: 0.5rem;\n  font-size: 2rem;\n  font-weight: 700;\n  letter-spacing: -0.025em;\n}\n.login-header[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\n  color: #64748b;\n  margin: 0;\n  font-size: 1rem;\n  font-weight: 500;\n}\n.login-form[_ngcontent-%COMP%] {\n  margin-bottom: 2rem;\n}\n.form-group[_ngcontent-%COMP%] {\n  margin-bottom: 2rem;\n  position: relative;\n}\n.form-group[_ngcontent-%COMP%]   label[_ngcontent-%COMP%] {\n  display: block;\n  margin-bottom: 0.5rem;\n  color: #374151;\n  font-weight: 600;\n  font-size: 0.875rem;\n  letter-spacing: 0.025em;\n}\n.form-control[_ngcontent-%COMP%] {\n  width: 100%;\n  padding: 1rem 1.25rem;\n  border: 1px solid #d1d5db;\n  border-radius: 8px;\n  font-size: 1rem;\n  font-weight: 400;\n  background: #ffffff;\n  transition: all 0.2s ease;\n  box-sizing: border-box;\n}\n.form-control[_ngcontent-%COMP%]::placeholder {\n  color: #9ca3af;\n  font-weight: 400;\n}\n.form-control[_ngcontent-%COMP%]:focus {\n  outline: none;\n  border-color: #3b82f6;\n  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);\n}\n.form-control.is-invalid[_ngcontent-%COMP%] {\n  border-color: #ef4444;\n  box-shadow: 0 0 0 3px rgba(239, 68, 68, 0.1);\n}\n.invalid-feedback[_ngcontent-%COMP%] {\n  color: #ef4444;\n  font-size: 0.875rem;\n  font-weight: 500;\n  margin-top: 0.5rem;\n  display: flex;\n  align-items: center;\n  gap: 0.5rem;\n}\n.invalid-feedback[_ngcontent-%COMP%]::before {\n  content: "\\26a0\\fe0f";\n  font-size: 0.875rem;\n}\n.alert[_ngcontent-%COMP%] {\n  padding: 1.25rem 1.5rem;\n  border-radius: 16px;\n  margin-bottom: 1.5rem;\n  font-size: 0.95rem;\n  font-weight: 500;\n  position: relative;\n  overflow: hidden;\n  -webkit-backdrop-filter: blur(10px);\n  backdrop-filter: blur(10px);\n}\n.alert[_ngcontent-%COMP%]::before {\n  content: "";\n  position: absolute;\n  top: 0;\n  left: 0;\n  width: 4px;\n  height: 100%;\n  background: currentColor;\n}\n.alert-danger[_ngcontent-%COMP%] {\n  background: rgba(254, 242, 242, 0.9);\n  color: #dc2626;\n  border: 1px solid rgba(239, 68, 68, 0.2);\n}\n.alert-danger[_ngcontent-%COMP%]::after {\n  content: "\\274c";\n  position: absolute;\n  top: 1rem;\n  right: 1rem;\n  font-size: 1.25rem;\n}\n.btn[_ngcontent-%COMP%] {\n  padding: 1rem 2rem;\n  border: none;\n  border-radius: 8px;\n  font-size: 1rem;\n  font-weight: 600;\n  cursor: pointer;\n  transition: all 0.2s ease;\n  text-decoration: none;\n  display: inline-block;\n  text-align: center;\n  position: relative;\n}\n.btn-primary[_ngcontent-%COMP%] {\n  background: #3b82f6;\n  color: white;\n  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);\n}\n.btn-primary[_ngcontent-%COMP%]:hover:not(:disabled) {\n  background: #2563eb;\n  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);\n}\n.btn-primary[_ngcontent-%COMP%]:active:not(:disabled) {\n  background: #1d4ed8;\n}\n.btn-primary[_ngcontent-%COMP%]:disabled {\n  opacity: 0.6;\n  cursor: not-allowed;\n  background: #94a3b8;\n}\n.btn-login[_ngcontent-%COMP%] {\n  width: 100%;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  gap: 0.75rem;\n}\n.spinner[_ngcontent-%COMP%] {\n  width: 20px;\n  height: 20px;\n  border: 3px solid rgba(255, 255, 255, 0.3);\n  border-top: 3px solid white;\n  border-radius: 50%;\n  display: inline-block;\n}\n.login-footer[_ngcontent-%COMP%] {\n  text-align: center;\n  margin-top: 2.5rem;\n  padding-top: 2rem;\n  border-top: 1px solid rgba(226, 232, 240, 0.6);\n}\n.login-footer[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\n  color: #64748b;\n  font-size: 0.95rem;\n  margin: 0 0 1rem 0;\n}\n.register-link[_ngcontent-%COMP%] {\n  color: #3b82f6;\n  text-decoration: none;\n  font-size: 0.95rem;\n  font-weight: 600;\n  position: relative;\n  transition: all 0.3s ease;\n}\n.register-link[_ngcontent-%COMP%]::after {\n  content: "";\n  position: absolute;\n  bottom: -2px;\n  left: 0;\n  width: 0;\n  height: 2px;\n  background:\n    linear-gradient(\n      90deg,\n      #3b82f6,\n      #6366f1);\n  transition: width 0.3s ease;\n}\n.register-link[_ngcontent-%COMP%]:hover {\n  color: #6366f1;\n  transform: translateY(-1px);\n}\n.register-link[_ngcontent-%COMP%]:hover::after {\n  width: 100%;\n}\n.admin-link[_ngcontent-%COMP%] {\n  margin-top: 15px;\n  text-align: center;\n}\n.admin-link-btn[_ngcontent-%COMP%] {\n  display: inline-flex;\n  align-items: center;\n  gap: 10px;\n  padding: 12px 24px;\n  background:\n    linear-gradient(\n      135deg,\n      #3b82f6 0%,\n      #6366f1 50%,\n      #8b5cf6 100%);\n  color: white;\n  text-decoration: none;\n  border-radius: 14px;\n  font-weight: 700;\n  font-size: 0.95rem;\n  transition: all 0.3s ease;\n  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.4), 0 2px 6px rgba(99, 102, 241, 0.3);\n  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);\n}\n.admin-link-btn[_ngcontent-%COMP%]:hover {\n  background:\n    linear-gradient(\n      135deg,\n      #6366f1 0%,\n      #8b5cf6 50%,\n      #a855f7 100%);\n  transform: translateY(-3px) scale(1.05);\n  box-shadow: 0 8px 20px rgba(59, 130, 246, 0.5), 0 4px 12px rgba(99, 102, 241, 0.4);\n}\n@media (max-width: 640px) {\n  .login-container[_ngcontent-%COMP%] {\n    padding: 1rem;\n  }\n  .login-card[_ngcontent-%COMP%] {\n    padding: 2rem 1.5rem;\n    margin: 1rem;\n  }\n  .login-header[_ngcontent-%COMP%]   h1[_ngcontent-%COMP%] {\n    font-size: 1.75rem;\n  }\n  .login-header[_ngcontent-%COMP%]::before {\n    font-size: 2.5rem;\n  }\n  .form-control[_ngcontent-%COMP%] {\n    padding: 1rem 1.25rem;\n  }\n  .btn-login[_ngcontent-%COMP%] {\n    padding: 1rem 1.5rem;\n    font-size: 1rem;\n  }\n}\n@media (max-width: 480px) {\n  .login-card[_ngcontent-%COMP%] {\n    padding: 1.5rem 1rem;\n  }\n  .login-header[_ngcontent-%COMP%]   h1[_ngcontent-%COMP%] {\n    font-size: 1.5rem;\n  }\n  .login-header[_ngcontent-%COMP%]::before {\n    font-size: 2rem;\n  }\n}\n@media (prefers-color-scheme: dark) {\n  .login-card[_ngcontent-%COMP%] {\n    background:\n      linear-gradient(\n        135deg,\n        rgba(15, 23, 42, 0.95) 0%,\n        rgba(30, 41, 59, 0.92) 25%,\n        rgba(51, 65, 85, 0.90) 50%,\n        rgba(30, 41, 59, 0.92) 75%,\n        rgba(15, 23, 42, 0.95) 100%);\n    border: 1px solid rgba(255, 255, 255, 0.1);\n  }\n  .login-header[_ngcontent-%COMP%]   h1[_ngcontent-%COMP%] {\n    background:\n      linear-gradient(\n        135deg,\n        #60a5fa 0%,\n        #818cf8 25%,\n        #a78bfa 50%,\n        #c084fc 75%,\n        #f472b6 100%);\n    -webkit-background-clip: text;\n    -webkit-text-fill-color: transparent;\n    background-clip: text;\n  }\n  .login-header[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\n    color: #94a3b8;\n  }\n  .form-group[_ngcontent-%COMP%]   label[_ngcontent-%COMP%] {\n    color: #e2e8f0;\n  }\n  .form-control[_ngcontent-%COMP%] {\n    background: rgba(30, 41, 59, 0.8);\n    border-color: rgba(71, 85, 105, 0.8);\n    color: #e2e8f0;\n  }\n  .form-control[_ngcontent-%COMP%]::placeholder {\n    color: #64748b;\n  }\n}\n/*# sourceMappingURL=login.component.css.map */'] });
};
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && setClassMetadata(LoginComponent, [{
    type: Component,
    args: [{ selector: "app-login", standalone: true, imports: [CommonModule, ReactiveFormsModule, RouterModule], template: `<div class="login-container">
  <div class="login-card">
    <div class="login-header">
      <h1>Connexion Fournisseur</h1>
      <p>Acc\xE9dez \xE0 votre espace fournisseur</p>
    </div>

    <form [formGroup]="loginForm" (ngSubmit)="onSubmit()" class="login-form" autocomplete="off">
      <!-- Email -->
      <div class="form-group">
        <label for="email">Email</label>
        <input
          type="email"
          id="email"
          formControlName="email"
          class="form-control"
          [class.is-invalid]="email?.invalid && email?.touched"
          placeholder="Votre adresse email"
          autocomplete="new-email"
          autocapitalize="off"
          autocorrect="off"
          spellcheck="false"
        />
        <!-- Angular 19: Nouveau control flow -->
        @if (email?.invalid && email?.touched) {
          <div class="invalid-feedback">
            @if (email?.errors?.['required']) {
              <div>L'email est requis</div>
            }
            @if (email?.errors?.['email']) {
              <div>Format d'email invalide</div>
            }
          </div>
        }
      </div>

      <!-- Mot de passe -->
      <div class="form-group">
        <label for="password">Mot de passe</label>
        <input
          type="password"
          id="password"
          formControlName="password"
          class="form-control"
          [class.is-invalid]="password?.invalid && password?.touched"
          placeholder="Votre mot de passe"
          autocomplete="new-password"
          autocapitalize="off"
          autocorrect="off"
          spellcheck="false"
        />
        <!-- Angular 19: Nouveau control flow -->
        @if (password?.invalid && password?.touched) {
          <div class="invalid-feedback">
            @if (password?.errors?.['required']) {
              <div>Le mot de passe est requis</div>
            }
            @if (password?.errors?.['minlength']) {
              <div>Le mot de passe doit contenir au moins 6 caract\xE8res</div>
            }
          </div>
        }
      </div>

      <!-- Angular 19: Message d'erreur avec nouveau control flow -->
      @if (hasError()) {
        <div class="alert alert-danger">
          {{ errorMessage() }}
        </div>
      }

      <!-- Angular 19: Bouton de connexion avec signals -->
      <button
        type="submit"
        class="btn btn-primary btn-login"
        [disabled]="!canSubmit()"
      >
        @if (isLoading()) {
          <span class="spinner"></span>
        }
        {{ isLoading() ? 'Connexion...' : 'Se connecter' }}
      </button>
    </form>

   

    <div class="login-footer">
      <p>Pas encore de compte ?
        <a routerLink="/register" class="register-link">Cr\xE9er un compte fournisseur</a>
      </p>
    </div>
  </div>
</div>
`, styles: ['/* src/app/components/auth/login/login.component.css */\n.login-container {\n  min-height: 100vh;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  background:\n    linear-gradient(\n      135deg,\n      #f8fafc 0%,\n      #e2e8f0 100%);\n  padding: 2rem;\n  position: relative;\n}\n.login-container::before {\n  content: "";\n  position: absolute;\n  top: 0;\n  left: 0;\n  right: 0;\n  bottom: 0;\n  background-image:\n    radial-gradient(\n      circle at 25% 25%,\n      rgba(59, 130, 246, 0.05) 0%,\n      transparent 50%),\n    radial-gradient(\n      circle at 75% 75%,\n      rgba(16, 185, 129, 0.05) 0%,\n      transparent 50%);\n  pointer-events: none;\n}\n.login-card {\n  background: rgba(255, 255, 255, 0.98);\n  -webkit-backdrop-filter: blur(10px);\n  backdrop-filter: blur(10px);\n  border: 1px solid rgba(255, 255, 255, 0.2);\n  border-radius: 16px;\n  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);\n  padding: 3rem;\n  width: 100%;\n  max-width: 480px;\n  position: relative;\n  transition: all 0.3s ease;\n}\n.login-card:hover {\n  box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.15), 0 20px 25px -5px rgba(0, 0, 0, 0.1);\n}\n.login-card::before {\n  content: "";\n  position: absolute;\n  top: 0;\n  left: 0;\n  right: 0;\n  height: 4px;\n  background:\n    linear-gradient(\n      90deg,\n      #3b82f6 0%,\n      #10b981 100%);\n  border-radius: 16px 16px 0 0;\n}\n.login-header {\n  text-align: center;\n  margin-bottom: 2.5rem;\n  position: relative;\n}\n.login-header::before {\n  content: "\\1f3e2";\n  display: block;\n  font-size: 2.5rem;\n  margin-bottom: 1rem;\n  opacity: 0.8;\n}\n.login-header h1 {\n  color: #1e293b;\n  margin-bottom: 0.5rem;\n  font-size: 2rem;\n  font-weight: 700;\n  letter-spacing: -0.025em;\n}\n.login-header p {\n  color: #64748b;\n  margin: 0;\n  font-size: 1rem;\n  font-weight: 500;\n}\n.login-form {\n  margin-bottom: 2rem;\n}\n.form-group {\n  margin-bottom: 2rem;\n  position: relative;\n}\n.form-group label {\n  display: block;\n  margin-bottom: 0.5rem;\n  color: #374151;\n  font-weight: 600;\n  font-size: 0.875rem;\n  letter-spacing: 0.025em;\n}\n.form-control {\n  width: 100%;\n  padding: 1rem 1.25rem;\n  border: 1px solid #d1d5db;\n  border-radius: 8px;\n  font-size: 1rem;\n  font-weight: 400;\n  background: #ffffff;\n  transition: all 0.2s ease;\n  box-sizing: border-box;\n}\n.form-control::placeholder {\n  color: #9ca3af;\n  font-weight: 400;\n}\n.form-control:focus {\n  outline: none;\n  border-color: #3b82f6;\n  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);\n}\n.form-control.is-invalid {\n  border-color: #ef4444;\n  box-shadow: 0 0 0 3px rgba(239, 68, 68, 0.1);\n}\n.invalid-feedback {\n  color: #ef4444;\n  font-size: 0.875rem;\n  font-weight: 500;\n  margin-top: 0.5rem;\n  display: flex;\n  align-items: center;\n  gap: 0.5rem;\n}\n.invalid-feedback::before {\n  content: "\\26a0\\fe0f";\n  font-size: 0.875rem;\n}\n.alert {\n  padding: 1.25rem 1.5rem;\n  border-radius: 16px;\n  margin-bottom: 1.5rem;\n  font-size: 0.95rem;\n  font-weight: 500;\n  position: relative;\n  overflow: hidden;\n  -webkit-backdrop-filter: blur(10px);\n  backdrop-filter: blur(10px);\n}\n.alert::before {\n  content: "";\n  position: absolute;\n  top: 0;\n  left: 0;\n  width: 4px;\n  height: 100%;\n  background: currentColor;\n}\n.alert-danger {\n  background: rgba(254, 242, 242, 0.9);\n  color: #dc2626;\n  border: 1px solid rgba(239, 68, 68, 0.2);\n}\n.alert-danger::after {\n  content: "\\274c";\n  position: absolute;\n  top: 1rem;\n  right: 1rem;\n  font-size: 1.25rem;\n}\n.btn {\n  padding: 1rem 2rem;\n  border: none;\n  border-radius: 8px;\n  font-size: 1rem;\n  font-weight: 600;\n  cursor: pointer;\n  transition: all 0.2s ease;\n  text-decoration: none;\n  display: inline-block;\n  text-align: center;\n  position: relative;\n}\n.btn-primary {\n  background: #3b82f6;\n  color: white;\n  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);\n}\n.btn-primary:hover:not(:disabled) {\n  background: #2563eb;\n  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);\n}\n.btn-primary:active:not(:disabled) {\n  background: #1d4ed8;\n}\n.btn-primary:disabled {\n  opacity: 0.6;\n  cursor: not-allowed;\n  background: #94a3b8;\n}\n.btn-login {\n  width: 100%;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  gap: 0.75rem;\n}\n.spinner {\n  width: 20px;\n  height: 20px;\n  border: 3px solid rgba(255, 255, 255, 0.3);\n  border-top: 3px solid white;\n  border-radius: 50%;\n  display: inline-block;\n}\n.login-footer {\n  text-align: center;\n  margin-top: 2.5rem;\n  padding-top: 2rem;\n  border-top: 1px solid rgba(226, 232, 240, 0.6);\n}\n.login-footer p {\n  color: #64748b;\n  font-size: 0.95rem;\n  margin: 0 0 1rem 0;\n}\n.register-link {\n  color: #3b82f6;\n  text-decoration: none;\n  font-size: 0.95rem;\n  font-weight: 600;\n  position: relative;\n  transition: all 0.3s ease;\n}\n.register-link::after {\n  content: "";\n  position: absolute;\n  bottom: -2px;\n  left: 0;\n  width: 0;\n  height: 2px;\n  background:\n    linear-gradient(\n      90deg,\n      #3b82f6,\n      #6366f1);\n  transition: width 0.3s ease;\n}\n.register-link:hover {\n  color: #6366f1;\n  transform: translateY(-1px);\n}\n.register-link:hover::after {\n  width: 100%;\n}\n.admin-link {\n  margin-top: 15px;\n  text-align: center;\n}\n.admin-link-btn {\n  display: inline-flex;\n  align-items: center;\n  gap: 10px;\n  padding: 12px 24px;\n  background:\n    linear-gradient(\n      135deg,\n      #3b82f6 0%,\n      #6366f1 50%,\n      #8b5cf6 100%);\n  color: white;\n  text-decoration: none;\n  border-radius: 14px;\n  font-weight: 700;\n  font-size: 0.95rem;\n  transition: all 0.3s ease;\n  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.4), 0 2px 6px rgba(99, 102, 241, 0.3);\n  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);\n}\n.admin-link-btn:hover {\n  background:\n    linear-gradient(\n      135deg,\n      #6366f1 0%,\n      #8b5cf6 50%,\n      #a855f7 100%);\n  transform: translateY(-3px) scale(1.05);\n  box-shadow: 0 8px 20px rgba(59, 130, 246, 0.5), 0 4px 12px rgba(99, 102, 241, 0.4);\n}\n@media (max-width: 640px) {\n  .login-container {\n    padding: 1rem;\n  }\n  .login-card {\n    padding: 2rem 1.5rem;\n    margin: 1rem;\n  }\n  .login-header h1 {\n    font-size: 1.75rem;\n  }\n  .login-header::before {\n    font-size: 2.5rem;\n  }\n  .form-control {\n    padding: 1rem 1.25rem;\n  }\n  .btn-login {\n    padding: 1rem 1.5rem;\n    font-size: 1rem;\n  }\n}\n@media (max-width: 480px) {\n  .login-card {\n    padding: 1.5rem 1rem;\n  }\n  .login-header h1 {\n    font-size: 1.5rem;\n  }\n  .login-header::before {\n    font-size: 2rem;\n  }\n}\n@media (prefers-color-scheme: dark) {\n  .login-card {\n    background:\n      linear-gradient(\n        135deg,\n        rgba(15, 23, 42, 0.95) 0%,\n        rgba(30, 41, 59, 0.92) 25%,\n        rgba(51, 65, 85, 0.90) 50%,\n        rgba(30, 41, 59, 0.92) 75%,\n        rgba(15, 23, 42, 0.95) 100%);\n    border: 1px solid rgba(255, 255, 255, 0.1);\n  }\n  .login-header h1 {\n    background:\n      linear-gradient(\n        135deg,\n        #60a5fa 0%,\n        #818cf8 25%,\n        #a78bfa 50%,\n        #c084fc 75%,\n        #f472b6 100%);\n    -webkit-background-clip: text;\n    -webkit-text-fill-color: transparent;\n    background-clip: text;\n  }\n  .login-header p {\n    color: #94a3b8;\n  }\n  .form-group label {\n    color: #e2e8f0;\n  }\n  .form-control {\n    background: rgba(30, 41, 59, 0.8);\n    border-color: rgba(71, 85, 105, 0.8);\n    color: #e2e8f0;\n  }\n  .form-control::placeholder {\n    color: #64748b;\n  }\n}\n/*# sourceMappingURL=login.component.css.map */\n'] }]
  }], () => [{ type: FormBuilder }, { type: AuthService }, { type: Router }, { type: ActivatedRoute }], null);
})();
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && \u0275setClassDebugInfo(LoginComponent, { className: "LoginComponent", filePath: "src/app/components/auth/login/login.component.ts", lineNumber: 15 });
})();
export {
  LoginComponent
};
//# sourceMappingURL=chunk-4QOETRKB.js.map

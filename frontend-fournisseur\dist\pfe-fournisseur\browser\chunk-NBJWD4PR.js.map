{"version": 3, "sources": ["src/app/components/admin/tools/workflow-engine/workflow-engine.component.ts"], "sourcesContent": ["import { Component } from '@angular/core';\nimport { CommonModule } from '@angular/common';\n\n@Component({\n  selector: 'app-workflow-engine',\n  standalone: true,\n  imports: [CommonModule],\n  template: `<div><h1>🔄 Moteur de workflow</h1><p>En développement...</p></div>`\n})\nexport class WorkflowEngineComponent {}\n"], "mappings": ";;;;;;;;;;;;AASM,IAAO,0BAAP,MAAO,yBAAuB;;qCAAvB,0BAAuB;EAAA;yEAAvB,0BAAuB,WAAA,CAAA,CAAA,qBAAA,CAAA,GAAA,OAAA,GAAA,MAAA,GAAA,UAAA,SAAA,iCAAA,IAAA,KAAA;AAAA,QAAA,KAAA,GAAA;AAFvB,MAAA,yBAAA,GAAA,KAAA,EAAK,GAAA,IAAA;AAAI,MAAA,iBAAA,GAAA,8BAAA;AAAqB,MAAA,uBAAA;AAAK,MAAA,yBAAA,GAAA,GAAA;AAAG,MAAA,iBAAA,GAAA,wBAAA;AAAmB,MAAA,uBAAA,EAAI;;oBAD9D,YAAY,GAAA,eAAA,EAAA,CAAA;;;sEAGX,yBAAuB,CAAA;UANnC;WAAU;MACT,UAAU;MACV,YAAY;MACZ,SAAS,CAAC,YAAY;MACtB,UAAU;KACX;;;;6EACY,yBAAuB,EAAA,WAAA,2BAAA,UAAA,+EAAA,YAAA,GAAA,CAAA;AAAA,GAAA;", "names": []}
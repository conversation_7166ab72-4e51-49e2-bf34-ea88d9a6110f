import {
  FormeService,
  MarqueService,
  TauxTVAService
} from "./chunk-KTQKKI3U.js";
import {
  ImageUrlService
} from "./chunk-CZWG52C5.js";
import {
  SousCategorieService
} from "./chunk-VGHANLZK.js";
import {
  CategorieService
} from "./chunk-LBLEENAN.js";
import {
  AuthService
} from "./chunk-ZSXXNL7U.js";
import {
  Router
} from "./chunk-6BVUYNW4.js";
import {
  CheckboxControlValueAccessor,
  DefaultValueAccessor,
  FormsModule,
  MaxValidator,
  MinValidator,
  NgControlStatus,
  NgControlStatusGroup,
  NgForm,
  NgModel,
  NgSelectOption,
  NumberValueAccessor,
  RequiredValidator,
  SelectControlValueAccessor,
  ɵNgNoValidate,
  ɵNgSelectMultipleOption
} from "./chunk-HQBVYEOO.js";
import "./chunk-GFHHPDQ6.js";
import "./chunk-7JDDWGD3.js";
import {
  CommonModule,
  Component,
  NgForOf,
  NgIf,
  NgSwitch,
  NgSwitchCase,
  setClassMetadata,
  signal,
  ɵsetClassDebugInfo,
  ɵɵadvance,
  ɵɵclassMap,
  ɵɵclassProp,
  ɵɵdefineComponent,
  ɵɵdirectiveInject,
  ɵɵelement,
  ɵɵelementContainerEnd,
  ɵɵelementContainerStart,
  ɵɵelementEnd,
  ɵɵelementStart,
  ɵɵgetCurrentView,
  ɵɵlistener,
  ɵɵnextContext,
  ɵɵproperty,
  ɵɵreference,
  ɵɵresetView,
  ɵɵrestoreView,
  ɵɵsanitizeUrl,
  ɵɵtemplate,
  ɵɵtext,
  ɵɵtextInterpolate,
  ɵɵtextInterpolate1,
  ɵɵtextInterpolate2
} from "./chunk-UBZQS7JS.js";

// src/app/components/referentiels/referentiels.component.ts
function ReferentielsComponent_button_9_Template(rf, ctx) {
  if (rf & 1) {
    const _r1 = \u0275\u0275getCurrentView();
    \u0275\u0275elementStart(0, "button", 9);
    \u0275\u0275listener("click", function ReferentielsComponent_button_9_Template_button_click_0_listener() {
      const tab_r2 = \u0275\u0275restoreView(_r1).$implicit;
      const ctx_r2 = \u0275\u0275nextContext();
      return \u0275\u0275resetView(ctx_r2.setActiveTab(tab_r2.id));
    });
    \u0275\u0275text(1);
    \u0275\u0275elementEnd();
  }
  if (rf & 2) {
    const tab_r2 = ctx.$implicit;
    const ctx_r2 = \u0275\u0275nextContext();
    \u0275\u0275classProp("active", ctx_r2.activeTab() === tab_r2.id);
    \u0275\u0275advance();
    \u0275\u0275textInterpolate2(" ", tab_r2.icon, " ", tab_r2.label, " ");
  }
}
function ReferentielsComponent_div_11_button_4_Template(rf, ctx) {
  if (rf & 1) {
    const _r4 = \u0275\u0275getCurrentView();
    \u0275\u0275elementStart(0, "button", 16);
    \u0275\u0275listener("click", function ReferentielsComponent_div_11_button_4_Template_button_click_0_listener() {
      \u0275\u0275restoreView(_r4);
      const ctx_r2 = \u0275\u0275nextContext(2);
      return \u0275\u0275resetView(ctx_r2.openCategorieForm());
    });
    \u0275\u0275text(1, " \u2795 Nouvelle Cat\xE9gorie ");
    \u0275\u0275elementEnd();
  }
}
function ReferentielsComponent_div_11_div_5_Template(rf, ctx) {
  if (rf & 1) {
    const _r5 = \u0275\u0275getCurrentView();
    \u0275\u0275elementStart(0, "div", 17)(1, "button", 18);
    \u0275\u0275listener("click", function ReferentielsComponent_div_11_div_5_Template_button_click_1_listener() {
      \u0275\u0275restoreView(_r5);
      const ctx_r2 = \u0275\u0275nextContext(2);
      return \u0275\u0275resetView(ctx_r2.requestNewCategorie());
    });
    \u0275\u0275text(2, " \u{1F4DD} Demander une nouvelle cat\xE9gorie ");
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(3, "span", 19);
    \u0275\u0275text(4, "\u{1F441}\uFE0F Mode consultation");
    \u0275\u0275elementEnd()();
  }
}
function ReferentielsComponent_div_11_div_7_p_6_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "p");
    \u0275\u0275text(1);
    \u0275\u0275elementEnd();
  }
  if (rf & 2) {
    const categorie_r6 = \u0275\u0275nextContext().$implicit;
    \u0275\u0275advance();
    \u0275\u0275textInterpolate(categorie_r6.description);
  }
}
function ReferentielsComponent_div_11_div_7_div_10_Template(rf, ctx) {
  if (rf & 1) {
    const _r7 = \u0275\u0275getCurrentView();
    \u0275\u0275elementStart(0, "div", 26)(1, "button", 27);
    \u0275\u0275listener("click", function ReferentielsComponent_div_11_div_7_div_10_Template_button_click_1_listener() {
      \u0275\u0275restoreView(_r7);
      const categorie_r6 = \u0275\u0275nextContext().$implicit;
      const ctx_r2 = \u0275\u0275nextContext(2);
      return \u0275\u0275resetView(ctx_r2.editCategorie(categorie_r6));
    });
    \u0275\u0275text(2, " \u270F\uFE0F Modifier ");
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(3, "button", 28);
    \u0275\u0275listener("click", function ReferentielsComponent_div_11_div_7_div_10_Template_button_click_3_listener() {
      \u0275\u0275restoreView(_r7);
      const categorie_r6 = \u0275\u0275nextContext().$implicit;
      const ctx_r2 = \u0275\u0275nextContext(2);
      return \u0275\u0275resetView(ctx_r2.deleteCategorie(categorie_r6));
    });
    \u0275\u0275text(4, " \u{1F5D1}\uFE0F Supprimer ");
    \u0275\u0275elementEnd()();
  }
}
function ReferentielsComponent_div_11_div_7_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "div", 20)(1, "div", 21)(2, "h4");
    \u0275\u0275text(3);
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(4, "span", 22);
    \u0275\u0275text(5);
    \u0275\u0275elementEnd()();
    \u0275\u0275template(6, ReferentielsComponent_div_11_div_7_p_6_Template, 2, 1, "p", 23);
    \u0275\u0275elementStart(7, "div", 24)(8, "span");
    \u0275\u0275text(9);
    \u0275\u0275elementEnd()();
    \u0275\u0275template(10, ReferentielsComponent_div_11_div_7_div_10_Template, 5, 0, "div", 25);
    \u0275\u0275elementEnd();
  }
  if (rf & 2) {
    const categorie_r6 = ctx.$implicit;
    const ctx_r2 = \u0275\u0275nextContext(2);
    \u0275\u0275advance(3);
    \u0275\u0275textInterpolate(categorie_r6.nom);
    \u0275\u0275advance();
    \u0275\u0275classMap(categorie_r6.estValidee ? "badge-success" : "badge-warning");
    \u0275\u0275advance();
    \u0275\u0275textInterpolate1(" ", categorie_r6.estValidee ? "Valid\xE9e" : "En attente", " ");
    \u0275\u0275advance();
    \u0275\u0275property("ngIf", categorie_r6.description);
    \u0275\u0275advance(3);
    \u0275\u0275textInterpolate1("", categorie_r6.sousCategoriesCount || 0, " sous-cat\xE9gories");
    \u0275\u0275advance();
    \u0275\u0275property("ngIf", ctx_r2.isAdmin());
  }
}
function ReferentielsComponent_div_11_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "div", 10)(1, "div", 11)(2, "h3");
    \u0275\u0275text(3, "\u{1F4C1} Cat\xE9gories");
    \u0275\u0275elementEnd();
    \u0275\u0275template(4, ReferentielsComponent_div_11_button_4_Template, 2, 0, "button", 12)(5, ReferentielsComponent_div_11_div_5_Template, 5, 0, "div", 13);
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(6, "div", 14);
    \u0275\u0275template(7, ReferentielsComponent_div_11_div_7_Template, 11, 7, "div", 15);
    \u0275\u0275elementEnd()();
  }
  if (rf & 2) {
    const ctx_r2 = \u0275\u0275nextContext();
    \u0275\u0275advance(4);
    \u0275\u0275property("ngIf", ctx_r2.isAdmin());
    \u0275\u0275advance();
    \u0275\u0275property("ngIf", !ctx_r2.isAdmin());
    \u0275\u0275advance(2);
    \u0275\u0275property("ngForOf", ctx_r2.categories());
  }
}
function ReferentielsComponent_div_12_button_4_Template(rf, ctx) {
  if (rf & 1) {
    const _r8 = \u0275\u0275getCurrentView();
    \u0275\u0275elementStart(0, "button", 16);
    \u0275\u0275listener("click", function ReferentielsComponent_div_12_button_4_Template_button_click_0_listener() {
      \u0275\u0275restoreView(_r8);
      const ctx_r2 = \u0275\u0275nextContext(2);
      return \u0275\u0275resetView(ctx_r2.openSousCategorieForm());
    });
    \u0275\u0275text(1, " \u2795 Nouvelle Sous-cat\xE9gorie ");
    \u0275\u0275elementEnd();
  }
}
function ReferentielsComponent_div_12_div_5_Template(rf, ctx) {
  if (rf & 1) {
    const _r9 = \u0275\u0275getCurrentView();
    \u0275\u0275elementStart(0, "div", 17)(1, "button", 18);
    \u0275\u0275listener("click", function ReferentielsComponent_div_12_div_5_Template_button_click_1_listener() {
      \u0275\u0275restoreView(_r9);
      const ctx_r2 = \u0275\u0275nextContext(2);
      return \u0275\u0275resetView(ctx_r2.requestNewSousCategorie());
    });
    \u0275\u0275text(2, " \u{1F4DD} Demander une nouvelle sous-cat\xE9gorie ");
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(3, "span", 19);
    \u0275\u0275text(4, "\u{1F441}\uFE0F Mode consultation");
    \u0275\u0275elementEnd()();
  }
}
function ReferentielsComponent_div_12_div_7_p_6_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "p");
    \u0275\u0275text(1);
    \u0275\u0275elementEnd();
  }
  if (rf & 2) {
    const sousCategorie_r10 = \u0275\u0275nextContext().$implicit;
    \u0275\u0275advance();
    \u0275\u0275textInterpolate(sousCategorie_r10.description);
  }
}
function ReferentielsComponent_div_12_div_7_div_13_Template(rf, ctx) {
  if (rf & 1) {
    const _r11 = \u0275\u0275getCurrentView();
    \u0275\u0275elementStart(0, "div", 26)(1, "button", 27);
    \u0275\u0275listener("click", function ReferentielsComponent_div_12_div_7_div_13_Template_button_click_1_listener() {
      \u0275\u0275restoreView(_r11);
      const sousCategorie_r10 = \u0275\u0275nextContext().$implicit;
      const ctx_r2 = \u0275\u0275nextContext(2);
      return \u0275\u0275resetView(ctx_r2.editSousCategorie(sousCategorie_r10));
    });
    \u0275\u0275text(2, " \u270F\uFE0F Modifier ");
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(3, "button", 28);
    \u0275\u0275listener("click", function ReferentielsComponent_div_12_div_7_div_13_Template_button_click_3_listener() {
      \u0275\u0275restoreView(_r11);
      const sousCategorie_r10 = \u0275\u0275nextContext().$implicit;
      const ctx_r2 = \u0275\u0275nextContext(2);
      return \u0275\u0275resetView(ctx_r2.deleteSousCategorie(sousCategorie_r10));
    });
    \u0275\u0275text(4, " \u{1F5D1}\uFE0F Supprimer ");
    \u0275\u0275elementEnd()();
  }
}
function ReferentielsComponent_div_12_div_7_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "div", 20)(1, "div", 21)(2, "h4");
    \u0275\u0275text(3);
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(4, "span", 22);
    \u0275\u0275text(5);
    \u0275\u0275elementEnd()();
    \u0275\u0275template(6, ReferentielsComponent_div_12_div_7_p_6_Template, 2, 1, "p", 23);
    \u0275\u0275elementStart(7, "div", 24)(8, "span");
    \u0275\u0275text(9);
    \u0275\u0275elementEnd();
    \u0275\u0275element(10, "br");
    \u0275\u0275elementStart(11, "span");
    \u0275\u0275text(12);
    \u0275\u0275elementEnd()();
    \u0275\u0275template(13, ReferentielsComponent_div_12_div_7_div_13_Template, 5, 0, "div", 25);
    \u0275\u0275elementEnd();
  }
  if (rf & 2) {
    const sousCategorie_r10 = ctx.$implicit;
    const ctx_r2 = \u0275\u0275nextContext(2);
    \u0275\u0275advance(3);
    \u0275\u0275textInterpolate(sousCategorie_r10.nom);
    \u0275\u0275advance();
    \u0275\u0275classMap(sousCategorie_r10.estValidee ? "badge-success" : "badge-warning");
    \u0275\u0275advance();
    \u0275\u0275textInterpolate1(" ", sousCategorie_r10.estValidee ? "Valid\xE9e" : "En attente", " ");
    \u0275\u0275advance();
    \u0275\u0275property("ngIf", sousCategorie_r10.description);
    \u0275\u0275advance(3);
    \u0275\u0275textInterpolate1("Cat\xE9gorie: ", sousCategorie_r10.categorieNom || "Non d\xE9finie", "");
    \u0275\u0275advance(3);
    \u0275\u0275textInterpolate1("", sousCategorie_r10.produitsCount || 0, " produits");
    \u0275\u0275advance();
    \u0275\u0275property("ngIf", ctx_r2.isAdmin());
  }
}
function ReferentielsComponent_div_12_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "div", 10)(1, "div", 11)(2, "h3");
    \u0275\u0275text(3, "\u{1F4C2} Sous-cat\xE9gories");
    \u0275\u0275elementEnd();
    \u0275\u0275template(4, ReferentielsComponent_div_12_button_4_Template, 2, 0, "button", 12)(5, ReferentielsComponent_div_12_div_5_Template, 5, 0, "div", 13);
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(6, "div", 14);
    \u0275\u0275template(7, ReferentielsComponent_div_12_div_7_Template, 14, 8, "div", 15);
    \u0275\u0275elementEnd()();
  }
  if (rf & 2) {
    const ctx_r2 = \u0275\u0275nextContext();
    \u0275\u0275advance(4);
    \u0275\u0275property("ngIf", ctx_r2.isAdmin());
    \u0275\u0275advance();
    \u0275\u0275property("ngIf", !ctx_r2.isAdmin());
    \u0275\u0275advance(2);
    \u0275\u0275property("ngForOf", ctx_r2.sousCategories());
  }
}
function ReferentielsComponent_div_13_button_4_Template(rf, ctx) {
  if (rf & 1) {
    const _r12 = \u0275\u0275getCurrentView();
    \u0275\u0275elementStart(0, "button", 16);
    \u0275\u0275listener("click", function ReferentielsComponent_div_13_button_4_Template_button_click_0_listener() {
      \u0275\u0275restoreView(_r12);
      const ctx_r2 = \u0275\u0275nextContext(2);
      return \u0275\u0275resetView(ctx_r2.openMarqueForm());
    });
    \u0275\u0275text(1, " \u2795 Nouvelle Marque ");
    \u0275\u0275elementEnd();
  }
}
function ReferentielsComponent_div_13_div_5_Template(rf, ctx) {
  if (rf & 1) {
    const _r13 = \u0275\u0275getCurrentView();
    \u0275\u0275elementStart(0, "div", 17)(1, "button", 16);
    \u0275\u0275listener("click", function ReferentielsComponent_div_13_div_5_Template_button_click_1_listener() {
      \u0275\u0275restoreView(_r13);
      const ctx_r2 = \u0275\u0275nextContext(2);
      return \u0275\u0275resetView(ctx_r2.openMarqueForm());
    });
    \u0275\u0275text(2, " \u2795 Ajouter une marque ");
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(3, "span", 29);
    \u0275\u0275text(4, "\u2705 Ajout autoris\xE9");
    \u0275\u0275elementEnd()();
  }
}
function ReferentielsComponent_div_13_div_7_div_6_Template(rf, ctx) {
  if (rf & 1) {
    const _r15 = \u0275\u0275getCurrentView();
    \u0275\u0275elementStart(0, "div", 26)(1, "button", 27);
    \u0275\u0275listener("click", function ReferentielsComponent_div_13_div_7_div_6_Template_button_click_1_listener() {
      \u0275\u0275restoreView(_r15);
      const marque_r16 = \u0275\u0275nextContext().$implicit;
      const ctx_r2 = \u0275\u0275nextContext(2);
      return \u0275\u0275resetView(ctx_r2.editMarque(marque_r16));
    });
    \u0275\u0275text(2, " \u270F\uFE0F Modifier ");
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(3, "button", 28);
    \u0275\u0275listener("click", function ReferentielsComponent_div_13_div_7_div_6_Template_button_click_3_listener() {
      \u0275\u0275restoreView(_r15);
      const marque_r16 = \u0275\u0275nextContext().$implicit;
      const ctx_r2 = \u0275\u0275nextContext(2);
      return \u0275\u0275resetView(ctx_r2.deleteMarque(marque_r16));
    });
    \u0275\u0275text(4, " \u{1F5D1}\uFE0F Supprimer ");
    \u0275\u0275elementEnd()();
  }
}
function ReferentielsComponent_div_13_div_7_Template(rf, ctx) {
  if (rf & 1) {
    const _r14 = \u0275\u0275getCurrentView();
    \u0275\u0275elementStart(0, "div", 20)(1, "div", 21)(2, "h4");
    \u0275\u0275text(3);
    \u0275\u0275elementEnd()();
    \u0275\u0275elementStart(4, "div", 30)(5, "img", 31);
    \u0275\u0275listener("error", function ReferentielsComponent_div_13_div_7_Template_img_error_5_listener($event) {
      \u0275\u0275restoreView(_r14);
      const ctx_r2 = \u0275\u0275nextContext(2);
      return \u0275\u0275resetView(ctx_r2.onImageError($event));
    });
    \u0275\u0275elementEnd()();
    \u0275\u0275template(6, ReferentielsComponent_div_13_div_7_div_6_Template, 5, 0, "div", 25);
    \u0275\u0275elementEnd();
  }
  if (rf & 2) {
    const marque_r16 = ctx.$implicit;
    const ctx_r2 = \u0275\u0275nextContext(2);
    \u0275\u0275advance(3);
    \u0275\u0275textInterpolate(marque_r16.name);
    \u0275\u0275advance(2);
    \u0275\u0275property("src", ctx_r2.getMarqueLogoUrl(marque_r16.logo), \u0275\u0275sanitizeUrl)("alt", marque_r16.name);
    \u0275\u0275advance();
    \u0275\u0275property("ngIf", ctx_r2.isAdmin());
  }
}
function ReferentielsComponent_div_13_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "div", 10)(1, "div", 11)(2, "h3");
    \u0275\u0275text(3, "\u{1F3F7}\uFE0F Marques");
    \u0275\u0275elementEnd();
    \u0275\u0275template(4, ReferentielsComponent_div_13_button_4_Template, 2, 0, "button", 12)(5, ReferentielsComponent_div_13_div_5_Template, 5, 0, "div", 13);
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(6, "div", 14);
    \u0275\u0275template(7, ReferentielsComponent_div_13_div_7_Template, 7, 4, "div", 15);
    \u0275\u0275elementEnd()();
  }
  if (rf & 2) {
    const ctx_r2 = \u0275\u0275nextContext();
    \u0275\u0275advance(4);
    \u0275\u0275property("ngIf", ctx_r2.isAdmin());
    \u0275\u0275advance();
    \u0275\u0275property("ngIf", !ctx_r2.isAdmin());
    \u0275\u0275advance(2);
    \u0275\u0275property("ngForOf", ctx_r2.marques());
  }
}
function ReferentielsComponent_div_14_button_4_Template(rf, ctx) {
  if (rf & 1) {
    const _r17 = \u0275\u0275getCurrentView();
    \u0275\u0275elementStart(0, "button", 16);
    \u0275\u0275listener("click", function ReferentielsComponent_div_14_button_4_Template_button_click_0_listener() {
      \u0275\u0275restoreView(_r17);
      const ctx_r2 = \u0275\u0275nextContext(2);
      return \u0275\u0275resetView(ctx_r2.openFormeForm());
    });
    \u0275\u0275text(1, " \u2795 Nouvelle Forme ");
    \u0275\u0275elementEnd();
  }
}
function ReferentielsComponent_div_14_div_5_Template(rf, ctx) {
  if (rf & 1) {
    const _r18 = \u0275\u0275getCurrentView();
    \u0275\u0275elementStart(0, "div", 17)(1, "button", 16);
    \u0275\u0275listener("click", function ReferentielsComponent_div_14_div_5_Template_button_click_1_listener() {
      \u0275\u0275restoreView(_r18);
      const ctx_r2 = \u0275\u0275nextContext(2);
      return \u0275\u0275resetView(ctx_r2.openFormeForm());
    });
    \u0275\u0275text(2, " \u2795 Ajouter une forme ");
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(3, "span", 29);
    \u0275\u0275text(4, "\u2705 Ajout autoris\xE9");
    \u0275\u0275elementEnd()();
  }
}
function ReferentielsComponent_div_14_div_7_div_9_Template(rf, ctx) {
  if (rf & 1) {
    const _r20 = \u0275\u0275getCurrentView();
    \u0275\u0275elementStart(0, "div", 26)(1, "button", 27);
    \u0275\u0275listener("click", function ReferentielsComponent_div_14_div_7_div_9_Template_button_click_1_listener() {
      \u0275\u0275restoreView(_r20);
      const forme_r21 = \u0275\u0275nextContext().$implicit;
      const ctx_r2 = \u0275\u0275nextContext(2);
      return \u0275\u0275resetView(ctx_r2.editForme(forme_r21));
    });
    \u0275\u0275text(2, " \u270F\uFE0F Modifier ");
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(3, "button", 28);
    \u0275\u0275listener("click", function ReferentielsComponent_div_14_div_7_div_9_Template_button_click_3_listener() {
      \u0275\u0275restoreView(_r20);
      const forme_r21 = \u0275\u0275nextContext().$implicit;
      const ctx_r2 = \u0275\u0275nextContext(2);
      return \u0275\u0275resetView(ctx_r2.deleteForme(forme_r21));
    });
    \u0275\u0275text(4, " \u{1F5D1}\uFE0F Supprimer ");
    \u0275\u0275elementEnd()();
  }
}
function ReferentielsComponent_div_14_div_7_Template(rf, ctx) {
  if (rf & 1) {
    const _r19 = \u0275\u0275getCurrentView();
    \u0275\u0275elementStart(0, "div", 20)(1, "div", 21)(2, "h4");
    \u0275\u0275text(3);
    \u0275\u0275elementEnd()();
    \u0275\u0275elementStart(4, "div", 32)(5, "img", 33);
    \u0275\u0275listener("error", function ReferentielsComponent_div_14_div_7_Template_img_error_5_listener($event) {
      \u0275\u0275restoreView(_r19);
      const ctx_r2 = \u0275\u0275nextContext(2);
      return \u0275\u0275resetView(ctx_r2.onImageError($event));
    });
    \u0275\u0275elementEnd()();
    \u0275\u0275elementStart(6, "div", 24)(7, "span");
    \u0275\u0275text(8);
    \u0275\u0275elementEnd()();
    \u0275\u0275template(9, ReferentielsComponent_div_14_div_7_div_9_Template, 5, 0, "div", 25);
    \u0275\u0275elementEnd();
  }
  if (rf & 2) {
    const forme_r21 = ctx.$implicit;
    const ctx_r2 = \u0275\u0275nextContext(2);
    \u0275\u0275advance(3);
    \u0275\u0275textInterpolate(forme_r21.nom);
    \u0275\u0275advance(2);
    \u0275\u0275property("src", ctx_r2.getFormeImageUrl(forme_r21.imageUrl), \u0275\u0275sanitizeUrl)("alt", forme_r21.nom);
    \u0275\u0275advance(3);
    \u0275\u0275textInterpolate1("Cat\xE9gorie: ", forme_r21.categorieNom || "Non d\xE9finie", "");
    \u0275\u0275advance();
    \u0275\u0275property("ngIf", ctx_r2.isAdmin());
  }
}
function ReferentielsComponent_div_14_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "div", 10)(1, "div", 11)(2, "h3");
    \u0275\u0275text(3, "\u{1F537} Formes");
    \u0275\u0275elementEnd();
    \u0275\u0275template(4, ReferentielsComponent_div_14_button_4_Template, 2, 0, "button", 12)(5, ReferentielsComponent_div_14_div_5_Template, 5, 0, "div", 13);
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(6, "div", 14);
    \u0275\u0275template(7, ReferentielsComponent_div_14_div_7_Template, 10, 5, "div", 15);
    \u0275\u0275elementEnd()();
  }
  if (rf & 2) {
    const ctx_r2 = \u0275\u0275nextContext();
    \u0275\u0275advance(4);
    \u0275\u0275property("ngIf", ctx_r2.isAdmin());
    \u0275\u0275advance();
    \u0275\u0275property("ngIf", !ctx_r2.isAdmin());
    \u0275\u0275advance(2);
    \u0275\u0275property("ngForOf", ctx_r2.formes());
  }
}
function ReferentielsComponent_div_15_button_4_Template(rf, ctx) {
  if (rf & 1) {
    const _r22 = \u0275\u0275getCurrentView();
    \u0275\u0275elementStart(0, "button", 16);
    \u0275\u0275listener("click", function ReferentielsComponent_div_15_button_4_Template_button_click_0_listener() {
      \u0275\u0275restoreView(_r22);
      const ctx_r2 = \u0275\u0275nextContext(2);
      return \u0275\u0275resetView(ctx_r2.openTauxTVAForm());
    });
    \u0275\u0275text(1, " \u2795 Nouveau Taux TVA ");
    \u0275\u0275elementEnd();
  }
}
function ReferentielsComponent_div_15_div_5_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "div", 35)(1, "span", 19);
    \u0275\u0275text(2, "\u{1F441}\uFE0F Mode consultation seule");
    \u0275\u0275elementEnd()();
  }
}
function ReferentielsComponent_div_15_div_7_p_9_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "p");
    \u0275\u0275text(1);
    \u0275\u0275elementEnd();
  }
  if (rf & 2) {
    const taux_r23 = \u0275\u0275nextContext().$implicit;
    \u0275\u0275advance();
    \u0275\u0275textInterpolate(taux_r23.description);
  }
}
function ReferentielsComponent_div_15_div_7_span_13_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "span");
    \u0275\u0275text(1);
    \u0275\u0275elementEnd();
  }
  if (rf & 2) {
    const taux_r23 = \u0275\u0275nextContext().$implicit;
    const ctx_r2 = \u0275\u0275nextContext(2);
    \u0275\u0275advance();
    \u0275\u0275textInterpolate1("Fin: ", ctx_r2.formatDate(taux_r23.dateFin), "");
  }
}
function ReferentielsComponent_div_15_div_7_div_14_Template(rf, ctx) {
  if (rf & 1) {
    const _r24 = \u0275\u0275getCurrentView();
    \u0275\u0275elementStart(0, "div", 26)(1, "button", 27);
    \u0275\u0275listener("click", function ReferentielsComponent_div_15_div_7_div_14_Template_button_click_1_listener() {
      \u0275\u0275restoreView(_r24);
      const taux_r23 = \u0275\u0275nextContext().$implicit;
      const ctx_r2 = \u0275\u0275nextContext(2);
      return \u0275\u0275resetView(ctx_r2.editTauxTVA(taux_r23));
    });
    \u0275\u0275text(2, " \u270F\uFE0F Modifier ");
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(3, "button", 28);
    \u0275\u0275listener("click", function ReferentielsComponent_div_15_div_7_div_14_Template_button_click_3_listener() {
      \u0275\u0275restoreView(_r24);
      const taux_r23 = \u0275\u0275nextContext().$implicit;
      const ctx_r2 = \u0275\u0275nextContext(2);
      return \u0275\u0275resetView(ctx_r2.deleteTauxTVA(taux_r23));
    });
    \u0275\u0275text(4, " \u{1F5D1}\uFE0F Supprimer ");
    \u0275\u0275elementEnd()();
  }
}
function ReferentielsComponent_div_15_div_7_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "div", 20)(1, "div", 21)(2, "h4");
    \u0275\u0275text(3);
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(4, "span", 22);
    \u0275\u0275text(5);
    \u0275\u0275elementEnd()();
    \u0275\u0275elementStart(6, "div", 36)(7, "span", 37);
    \u0275\u0275text(8);
    \u0275\u0275elementEnd()();
    \u0275\u0275template(9, ReferentielsComponent_div_15_div_7_p_9_Template, 2, 1, "p", 23);
    \u0275\u0275elementStart(10, "div", 24)(11, "span");
    \u0275\u0275text(12);
    \u0275\u0275elementEnd();
    \u0275\u0275template(13, ReferentielsComponent_div_15_div_7_span_13_Template, 2, 1, "span", 23);
    \u0275\u0275elementEnd();
    \u0275\u0275template(14, ReferentielsComponent_div_15_div_7_div_14_Template, 5, 0, "div", 25);
    \u0275\u0275elementEnd();
  }
  if (rf & 2) {
    const taux_r23 = ctx.$implicit;
    const ctx_r2 = \u0275\u0275nextContext(2);
    \u0275\u0275advance(3);
    \u0275\u0275textInterpolate(taux_r23.libelle);
    \u0275\u0275advance();
    \u0275\u0275classMap(taux_r23.estActif ? "badge-success" : "badge-danger");
    \u0275\u0275advance();
    \u0275\u0275textInterpolate1(" ", taux_r23.estActif ? "Actif" : "Inactif", " ");
    \u0275\u0275advance(3);
    \u0275\u0275textInterpolate1("", taux_r23.taux, "%");
    \u0275\u0275advance();
    \u0275\u0275property("ngIf", taux_r23.description);
    \u0275\u0275advance(3);
    \u0275\u0275textInterpolate1("Effet: ", ctx_r2.formatDate(taux_r23.dateEffet), "");
    \u0275\u0275advance();
    \u0275\u0275property("ngIf", taux_r23.dateFin);
    \u0275\u0275advance();
    \u0275\u0275property("ngIf", ctx_r2.isAdmin());
  }
}
function ReferentielsComponent_div_15_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "div", 10)(1, "div", 11)(2, "h3");
    \u0275\u0275text(3, "\u{1F4B0} Taux TVA");
    \u0275\u0275elementEnd();
    \u0275\u0275template(4, ReferentielsComponent_div_15_button_4_Template, 2, 0, "button", 12)(5, ReferentielsComponent_div_15_div_5_Template, 3, 0, "div", 34);
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(6, "div", 14);
    \u0275\u0275template(7, ReferentielsComponent_div_15_div_7_Template, 15, 9, "div", 15);
    \u0275\u0275elementEnd()();
  }
  if (rf & 2) {
    const ctx_r2 = \u0275\u0275nextContext();
    \u0275\u0275advance(4);
    \u0275\u0275property("ngIf", ctx_r2.isAdmin());
    \u0275\u0275advance();
    \u0275\u0275property("ngIf", !ctx_r2.isAdmin());
    \u0275\u0275advance(2);
    \u0275\u0275property("ngForOf", ctx_r2.tauxTVA());
  }
}
function ReferentielsComponent_div_16_div_10_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "div")(1, "div", 48)(2, "label");
    \u0275\u0275text(3, "Nom de la cat\xE9gorie *");
    \u0275\u0275elementEnd();
    \u0275\u0275element(4, "input", 49);
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(5, "div", 48)(6, "label");
    \u0275\u0275text(7, "Description");
    \u0275\u0275elementEnd();
    \u0275\u0275element(8, "textarea", 50);
    \u0275\u0275elementEnd()();
  }
}
function ReferentielsComponent_div_16_div_11_option_7_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "option", 57);
    \u0275\u0275text(1);
    \u0275\u0275elementEnd();
  }
  if (rf & 2) {
    const cat_r27 = ctx.$implicit;
    \u0275\u0275property("value", cat_r27.id);
    \u0275\u0275advance();
    \u0275\u0275textInterpolate1(" ", cat_r27.nom, " ");
  }
}
function ReferentielsComponent_div_16_div_11_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "div")(1, "div", 48)(2, "label");
    \u0275\u0275text(3, "Cat\xE9gorie parente *");
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(4, "select", 51)(5, "option", 52);
    \u0275\u0275text(6, "S\xE9lectionner une cat\xE9gorie");
    \u0275\u0275elementEnd();
    \u0275\u0275template(7, ReferentielsComponent_div_16_div_11_option_7_Template, 2, 2, "option", 53);
    \u0275\u0275elementEnd()();
    \u0275\u0275elementStart(8, "div", 48)(9, "label");
    \u0275\u0275text(10, "Nom de la sous-cat\xE9gorie *");
    \u0275\u0275elementEnd();
    \u0275\u0275element(11, "input", 54);
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(12, "div", 48)(13, "label");
    \u0275\u0275text(14, "Description");
    \u0275\u0275elementEnd();
    \u0275\u0275element(15, "textarea", 55);
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(16, "div", 48)(17, "label");
    \u0275\u0275element(18, "input", 56);
    \u0275\u0275text(19, " Valid\xE9e ");
    \u0275\u0275elementEnd()()();
  }
  if (rf & 2) {
    const ctx_r2 = \u0275\u0275nextContext(2);
    \u0275\u0275advance(7);
    \u0275\u0275property("ngForOf", ctx_r2.categories());
  }
}
function ReferentielsComponent_div_16_div_12_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "div")(1, "div", 48)(2, "label");
    \u0275\u0275text(3, "Nom de la marque *");
    \u0275\u0275elementEnd();
    \u0275\u0275element(4, "input", 58);
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(5, "div", 48)(6, "label");
    \u0275\u0275text(7, "URL du logo");
    \u0275\u0275elementEnd();
    \u0275\u0275element(8, "input", 59);
    \u0275\u0275elementEnd()();
  }
}
function ReferentielsComponent_div_16_div_13_option_7_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "option", 57);
    \u0275\u0275text(1);
    \u0275\u0275elementEnd();
  }
  if (rf & 2) {
    const cat_r28 = ctx.$implicit;
    \u0275\u0275property("value", cat_r28.id);
    \u0275\u0275advance();
    \u0275\u0275textInterpolate1(" ", cat_r28.nom, " ");
  }
}
function ReferentielsComponent_div_16_div_13_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "div")(1, "div", 48)(2, "label");
    \u0275\u0275text(3, "Cat\xE9gorie *");
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(4, "select", 51)(5, "option", 52);
    \u0275\u0275text(6, "S\xE9lectionner une cat\xE9gorie");
    \u0275\u0275elementEnd();
    \u0275\u0275template(7, ReferentielsComponent_div_16_div_13_option_7_Template, 2, 2, "option", 53);
    \u0275\u0275elementEnd()();
    \u0275\u0275elementStart(8, "div", 48)(9, "label");
    \u0275\u0275text(10, "Nom de la forme *");
    \u0275\u0275elementEnd();
    \u0275\u0275element(11, "input", 60);
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(12, "div", 48)(13, "label");
    \u0275\u0275text(14, "URL de l'image");
    \u0275\u0275elementEnd();
    \u0275\u0275element(15, "input", 61);
    \u0275\u0275elementEnd()();
  }
  if (rf & 2) {
    const ctx_r2 = \u0275\u0275nextContext(2);
    \u0275\u0275advance(7);
    \u0275\u0275property("ngForOf", ctx_r2.categories());
  }
}
function ReferentielsComponent_div_16_div_14_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "div")(1, "div", 48)(2, "label");
    \u0275\u0275text(3, "Libell\xE9 *");
    \u0275\u0275elementEnd();
    \u0275\u0275element(4, "input", 62);
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(5, "div", 48)(6, "label");
    \u0275\u0275text(7, "Taux (%) *");
    \u0275\u0275elementEnd();
    \u0275\u0275element(8, "input", 63);
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(9, "div", 48)(10, "label");
    \u0275\u0275text(11, "Description");
    \u0275\u0275elementEnd();
    \u0275\u0275element(12, "textarea", 64);
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(13, "div", 48)(14, "label");
    \u0275\u0275text(15, "Date d'effet *");
    \u0275\u0275elementEnd();
    \u0275\u0275element(16, "input", 65);
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(17, "div", 48)(18, "label");
    \u0275\u0275text(19, "Date de fin");
    \u0275\u0275elementEnd();
    \u0275\u0275element(20, "input", 66);
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(21, "div", 48)(22, "label");
    \u0275\u0275element(23, "input", 67);
    \u0275\u0275text(24, " Actif ");
    \u0275\u0275elementEnd()()();
  }
}
function ReferentielsComponent_div_16_Template(rf, ctx) {
  if (rf & 1) {
    const _r25 = \u0275\u0275getCurrentView();
    \u0275\u0275elementStart(0, "div", 38);
    \u0275\u0275listener("click", function ReferentielsComponent_div_16_Template_div_click_0_listener() {
      \u0275\u0275restoreView(_r25);
      const ctx_r2 = \u0275\u0275nextContext();
      return \u0275\u0275resetView(ctx_r2.closeModal());
    });
    \u0275\u0275elementStart(1, "div", 39);
    \u0275\u0275listener("click", function ReferentielsComponent_div_16_Template_div_click_1_listener($event) {
      \u0275\u0275restoreView(_r25);
      return \u0275\u0275resetView($event.stopPropagation());
    });
    \u0275\u0275elementStart(2, "div", 40)(3, "h3");
    \u0275\u0275text(4);
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(5, "button", 41);
    \u0275\u0275listener("click", function ReferentielsComponent_div_16_Template_button_click_5_listener() {
      \u0275\u0275restoreView(_r25);
      const ctx_r2 = \u0275\u0275nextContext();
      return \u0275\u0275resetView(ctx_r2.closeModal());
    });
    \u0275\u0275text(6, "\u2715");
    \u0275\u0275elementEnd()();
    \u0275\u0275elementStart(7, "form", 42, 0);
    \u0275\u0275listener("ngSubmit", function ReferentielsComponent_div_16_Template_form_ngSubmit_7_listener() {
      \u0275\u0275restoreView(_r25);
      const form_r26 = \u0275\u0275reference(8);
      const ctx_r2 = \u0275\u0275nextContext();
      return \u0275\u0275resetView(ctx_r2.onSubmit(form_r26));
    });
    \u0275\u0275elementContainerStart(9, 43);
    \u0275\u0275template(10, ReferentielsComponent_div_16_div_10_Template, 9, 0, "div", 44)(11, ReferentielsComponent_div_16_div_11_Template, 20, 1, "div", 44)(12, ReferentielsComponent_div_16_div_12_Template, 9, 0, "div", 44)(13, ReferentielsComponent_div_16_div_13_Template, 16, 1, "div", 44)(14, ReferentielsComponent_div_16_div_14_Template, 25, 0, "div", 44);
    \u0275\u0275elementContainerEnd();
    \u0275\u0275elementStart(15, "div", 45)(16, "button", 46);
    \u0275\u0275listener("click", function ReferentielsComponent_div_16_Template_button_click_16_listener() {
      \u0275\u0275restoreView(_r25);
      const ctx_r2 = \u0275\u0275nextContext();
      return \u0275\u0275resetView(ctx_r2.closeModal());
    });
    \u0275\u0275text(17, " Annuler ");
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(18, "button", 47);
    \u0275\u0275text(19);
    \u0275\u0275elementEnd()()()()();
  }
  if (rf & 2) {
    const form_r26 = \u0275\u0275reference(8);
    const ctx_r2 = \u0275\u0275nextContext();
    \u0275\u0275advance(4);
    \u0275\u0275textInterpolate(ctx_r2.modalTitle());
    \u0275\u0275advance(5);
    \u0275\u0275property("ngSwitch", ctx_r2.modalType());
    \u0275\u0275advance();
    \u0275\u0275property("ngSwitchCase", "categorie");
    \u0275\u0275advance();
    \u0275\u0275property("ngSwitchCase", "sous-categorie");
    \u0275\u0275advance();
    \u0275\u0275property("ngSwitchCase", "marque");
    \u0275\u0275advance();
    \u0275\u0275property("ngSwitchCase", "forme");
    \u0275\u0275advance();
    \u0275\u0275property("ngSwitchCase", "tva");
    \u0275\u0275advance(4);
    \u0275\u0275property("disabled", !form_r26.valid || ctx_r2.isLoading());
    \u0275\u0275advance();
    \u0275\u0275textInterpolate1(" ", ctx_r2.isEditMode() ? "Modifier" : "Cr\xE9er", " ");
  }
}
var ReferentielsComponent = class _ReferentielsComponent {
  categorieService;
  sousCategorieService;
  marqueService;
  formeService;
  tauxTVAService;
  authService;
  router;
  imageUrlService;
  // Signals pour les données
  categories = signal([]);
  sousCategories = signal([]);
  marques = signal([]);
  formes = signal([]);
  tauxTVA = signal([]);
  // Signals pour l'interface
  activeTab = signal("categories");
  showModal = signal(false);
  modalType = signal("");
  modalTitle = signal("");
  isEditMode = signal(false);
  isLoading = signal(false);
  selectedItem = signal(null);
  // Vérification des permissions
  isAdmin = signal(false);
  // Configuration des onglets
  tabs = [
    { id: "categories", label: "Cat\xE9gories", icon: "\u{1F4C1}" },
    { id: "sous-categories", label: "Sous-cat\xE9gories", icon: "\u{1F4C2}" },
    { id: "marques", label: "Marques", icon: "\u{1F3F7}\uFE0F" },
    { id: "formes", label: "Formes", icon: "\u{1F537}" },
    { id: "tva", label: "Taux TVA", icon: "\u{1F4B0}" }
  ];
  constructor(categorieService, sousCategorieService, marqueService, formeService, tauxTVAService, authService, router, imageUrlService) {
    this.categorieService = categorieService;
    this.sousCategorieService = sousCategorieService;
    this.marqueService = marqueService;
    this.formeService = formeService;
    this.tauxTVAService = tauxTVAService;
    this.authService = authService;
    this.router = router;
    this.imageUrlService = imageUrlService;
  }
  ngOnInit() {
    this.checkUserRole();
    this.loadAllData();
  }
  /**
   * Vérifier le rôle de l'utilisateur
   */
  checkUserRole() {
    const currentUser = this.authService.getCurrentUser();
    this.isAdmin.set(currentUser?.role === "Admin");
    console.log("\u{1F464} R\xF4le utilisateur:", currentUser?.role, "- Admin:", this.isAdmin());
  }
  /**
   * Charger toutes les données
   */
  loadAllData() {
    this.loadCategories();
    this.loadSousCategories();
    this.loadMarques();
    this.loadFormes();
    this.loadTauxTVA();
  }
  /**
   * Charger les catégories
   */
  loadCategories() {
    this.categorieService.getAll().subscribe({
      next: (data) => {
        console.log("\u{1F50D} Cat\xE9gories re\xE7ues du backend:", data);
        this.categories.set(Array.isArray(data) ? data : []);
        console.log("\u2705 Cat\xE9gories charg\xE9es:", this.categories().length);
      },
      error: (error) => {
        console.error("\u274C Erreur cat\xE9gories:", error);
        this.categories.set([]);
      }
    });
  }
  /**
   * Charger les sous-catégories
   */
  loadSousCategories() {
    const currentUser = this.authService.currentUser();
    const fournisseurId = currentUser?.role === "Fournisseur" ? currentUser.id : void 0;
    console.log("\u{1F50D} Chargement sous-cat\xE9gories...");
    console.log("\u{1F464} Utilisateur connect\xE9:", currentUser);
    console.log("\u{1F3E2} Fournisseur ID \xE0 utiliser:", fournisseurId);
    if (currentUser?.role === "Fournisseur") {
      console.log("\u{1F6A8} FOURNISSEUR CONNECT\xC9 - ID:", currentUser.id);
    }
    this.sousCategorieService.getAll(fournisseurId).subscribe({
      next: (data) => {
        console.log("\u{1F50D} Sous-cat\xE9gories re\xE7ues du backend:", data);
        console.log("\u{1F464} Fournisseur ID utilis\xE9:", fournisseurId);
        data.forEach((sc) => {
          console.log(`  \u{1F4CB} ${sc.nom}: ${sc.produitsCount} produits`);
        });
        this.sousCategories.set(Array.isArray(data) ? data : []);
        console.log("\u2705 Sous-cat\xE9gories charg\xE9es:", this.sousCategories().length);
      },
      error: (error) => {
        console.error("\u274C Erreur sous-cat\xE9gories:", error);
        this.sousCategories.set([]);
      }
    });
  }
  /**
   * Charger les marques
   */
  loadMarques() {
    this.marqueService.getAll().subscribe({
      next: (data) => {
        console.log("\u{1F50D} Marques re\xE7ues du backend:", data);
        this.marques.set(Array.isArray(data) ? data : []);
        console.log("\u2705 Marques charg\xE9es:", this.marques().length);
      },
      error: (error) => {
        console.error("\u274C Erreur marques:", error);
        this.marques.set([]);
      }
    });
  }
  /**
   * Charger les formes
   */
  loadFormes() {
    this.formeService.getAll().subscribe({
      next: (data) => {
        console.log("\u{1F50D} Formes re\xE7ues du backend:", data);
        this.formes.set(Array.isArray(data) ? data : []);
        console.log("\u2705 Formes charg\xE9es:", this.formes().length);
      },
      error: (error) => {
        console.error("\u274C Erreur formes:", error);
        this.formes.set([]);
      }
    });
  }
  /**
   * Charger les taux TVA
   */
  loadTauxTVA() {
    this.tauxTVAService.getAll().subscribe({
      next: (data) => {
        console.log("\u2705 Taux TVA charg\xE9s:", data);
        this.tauxTVA.set(Array.isArray(data) ? data : []);
      },
      error: (error) => {
        console.error("\u274C Erreur taux TVA:", error);
        this.tauxTVA.set([]);
      }
    });
  }
  /**
   * Changer d'onglet
   */
  setActiveTab(tabId) {
    this.activeTab.set(tabId);
  }
  /**
   * Ouvrir le formulaire de catégorie
   */
  openCategorieForm() {
    this.modalType.set("categorie");
    this.modalTitle.set("Nouvelle Cat\xE9gorie");
    this.isEditMode.set(false);
    this.selectedItem.set(null);
    this.showModal.set(true);
  }
  /**
   * Ouvrir le formulaire de sous-catégorie
   */
  openSousCategorieForm() {
    this.modalType.set("sous-categorie");
    this.modalTitle.set("Nouvelle Sous-cat\xE9gorie");
    this.isEditMode.set(false);
    this.selectedItem.set(null);
    this.showModal.set(true);
  }
  /**
   * Ouvrir le formulaire de marque
   */
  openMarqueForm() {
    this.modalType.set("marque");
    this.modalTitle.set("Nouvelle Marque");
    this.isEditMode.set(false);
    this.selectedItem.set(null);
    this.showModal.set(true);
  }
  /**
   * Ouvrir le formulaire de forme
   */
  openFormeForm() {
    this.modalType.set("forme");
    this.modalTitle.set("Nouvelle Forme");
    this.isEditMode.set(false);
    this.selectedItem.set(null);
    this.showModal.set(true);
  }
  /**
   * Ouvrir le formulaire de taux TVA
   */
  openTauxTVAForm() {
    this.modalType.set("tva");
    this.modalTitle.set("Nouveau Taux TVA");
    this.isEditMode.set(false);
    this.selectedItem.set(null);
    this.showModal.set(true);
  }
  /**
   * Éditer une catégorie
   */
  editCategorie(categorie) {
    this.modalType.set("categorie");
    this.modalTitle.set("Modifier Cat\xE9gorie");
    this.isEditMode.set(true);
    this.selectedItem.set(categorie);
    this.showModal.set(true);
  }
  /**
   * Éditer une sous-catégorie
   */
  editSousCategorie(sousCategorie) {
    this.modalType.set("sous-categorie");
    this.modalTitle.set("Modifier Sous-cat\xE9gorie");
    this.isEditMode.set(true);
    this.selectedItem.set(sousCategorie);
    this.showModal.set(true);
  }
  /**
   * Éditer une marque
   */
  editMarque(marque) {
    this.modalType.set("marque");
    this.modalTitle.set("Modifier Marque");
    this.isEditMode.set(true);
    this.selectedItem.set(marque);
    this.showModal.set(true);
  }
  /**
   * Éditer une forme
   */
  editForme(forme) {
    this.modalType.set("forme");
    this.modalTitle.set("Modifier Forme");
    this.isEditMode.set(true);
    this.selectedItem.set(forme);
    this.showModal.set(true);
  }
  /**
   * Éditer un taux TVA
   */
  editTauxTVA(taux) {
    this.modalType.set("tva");
    this.modalTitle.set("Modifier Taux TVA");
    this.isEditMode.set(true);
    this.selectedItem.set(taux);
    this.showModal.set(true);
  }
  /**
   * Fermer le modal
   */
  closeModal() {
    this.showModal.set(false);
    this.modalType.set("");
    this.modalTitle.set("");
    this.isEditMode.set(false);
    this.selectedItem.set(null);
  }
  /**
   * Soumettre le formulaire
   */
  onSubmit(form) {
    if (!form.valid)
      return;
    this.isLoading.set(true);
    const formData = form.value;
    const modalType = this.modalType();
    switch (modalType) {
      case "categorie":
        this.handleCategorieSubmit(formData);
        break;
      case "sous-categorie":
        this.handleSousCategorieSubmit(formData);
        break;
      case "marque":
        this.handleMarqueSubmit(formData);
        break;
      case "forme":
        this.handleFormeSubmit(formData);
        break;
      case "tva":
        this.handleTauxTVASubmit(formData);
        break;
    }
  }
  /**
   * Gérer la soumission de catégorie
   */
  handleCategorieSubmit(formData) {
    const categorieData = {
      nom: formData.nom,
      description: formData.description
    };
    if (this.isEditMode()) {
      const selectedItem = this.selectedItem();
      this.categorieService.update(selectedItem.id, categorieData).subscribe({
        next: () => {
          this.loadCategories();
          this.closeModal();
          this.isLoading.set(false);
          alert("Cat\xE9gorie mise \xE0 jour avec succ\xE8s");
        },
        error: (error) => {
          console.error("Erreur mise \xE0 jour cat\xE9gorie:", error);
          this.isLoading.set(false);
          alert("Erreur lors de la mise \xE0 jour");
        }
      });
    } else {
      this.categorieService.create(categorieData).subscribe({
        next: () => {
          this.loadCategories();
          this.closeModal();
          this.isLoading.set(false);
          alert("Cat\xE9gorie cr\xE9\xE9e avec succ\xE8s");
        },
        error: (error) => {
          console.error("Erreur cr\xE9ation cat\xE9gorie:", error);
          this.isLoading.set(false);
          alert("Erreur lors de la cr\xE9ation");
        }
      });
    }
  }
  /**
   * Gérer la soumission de sous-catégorie
   */
  handleSousCategorieSubmit(formData) {
    const sousCategorieData = {
      nom: formData.nom,
      categorieId: +formData.categorieId,
      description: formData.description,
      estValidee: formData.estValidee || false
    };
    if (this.isEditMode()) {
      const selectedItem = this.selectedItem();
      this.sousCategorieService.update(selectedItem.id, sousCategorieData).subscribe({
        next: () => {
          this.loadSousCategories();
          this.closeModal();
          this.isLoading.set(false);
          alert("Sous-cat\xE9gorie mise \xE0 jour avec succ\xE8s");
        },
        error: (error) => {
          console.error("Erreur mise \xE0 jour sous-cat\xE9gorie:", error);
          this.isLoading.set(false);
          alert("Erreur lors de la mise \xE0 jour");
        }
      });
    } else {
      this.sousCategorieService.create(sousCategorieData).subscribe({
        next: () => {
          this.loadSousCategories();
          this.closeModal();
          this.isLoading.set(false);
          alert("Sous-cat\xE9gorie cr\xE9\xE9e avec succ\xE8s");
        },
        error: (error) => {
          console.error("Erreur cr\xE9ation sous-cat\xE9gorie:", error);
          this.isLoading.set(false);
          alert("Erreur lors de la cr\xE9ation");
        }
      });
    }
  }
  /**
   * Gérer la soumission de marque
   */
  handleMarqueSubmit(formData) {
    const marqueData = {
      name: formData.name,
      logo: formData.logo
    };
    if (this.isEditMode()) {
      const selectedItem = this.selectedItem();
      this.marqueService.update(selectedItem.id, marqueData).subscribe({
        next: () => {
          this.loadMarques();
          this.closeModal();
          this.isLoading.set(false);
          alert("Marque mise \xE0 jour avec succ\xE8s");
        },
        error: (error) => {
          console.error("Erreur mise \xE0 jour marque:", error);
          this.isLoading.set(false);
          alert("Erreur lors de la mise \xE0 jour");
        }
      });
    } else {
      this.marqueService.create(marqueData).subscribe({
        next: () => {
          this.loadMarques();
          this.closeModal();
          this.isLoading.set(false);
          alert("Marque cr\xE9\xE9e avec succ\xE8s");
        },
        error: (error) => {
          console.error("Erreur cr\xE9ation marque:", error);
          this.isLoading.set(false);
          alert("Erreur lors de la cr\xE9ation");
        }
      });
    }
  }
  /**
   * Gérer la soumission de forme
   */
  handleFormeSubmit(formData) {
    const formeData = {
      nom: formData.nom,
      categorieId: +formData.categorieId,
      imageUrl: formData.imageUrl
    };
    if (this.isEditMode()) {
      const selectedItem = this.selectedItem();
      this.formeService.update(selectedItem.id, formeData).subscribe({
        next: () => {
          this.loadFormes();
          this.closeModal();
          this.isLoading.set(false);
          alert("Forme mise \xE0 jour avec succ\xE8s");
        },
        error: (error) => {
          console.error("Erreur mise \xE0 jour forme:", error);
          this.isLoading.set(false);
          alert("Erreur lors de la mise \xE0 jour");
        }
      });
    } else {
      this.formeService.create(formeData).subscribe({
        next: () => {
          this.loadFormes();
          this.closeModal();
          this.isLoading.set(false);
          alert("Forme cr\xE9\xE9e avec succ\xE8s");
        },
        error: (error) => {
          console.error("Erreur cr\xE9ation forme:", error);
          this.isLoading.set(false);
          alert("Erreur lors de la cr\xE9ation");
        }
      });
    }
  }
  /**
   * Gérer la soumission de taux TVA
   */
  handleTauxTVASubmit(formData) {
    const tauxTVAData = {
      libelle: formData.libelle,
      taux: +formData.taux,
      description: formData.description,
      estActif: formData.estActif || false,
      dateEffet: formData.dateEffet,
      dateFin: formData.dateFin
    };
    if (this.isEditMode()) {
      const selectedItem = this.selectedItem();
      this.tauxTVAService.update(selectedItem.id, tauxTVAData).subscribe({
        next: () => {
          this.loadTauxTVA();
          this.closeModal();
          this.isLoading.set(false);
          alert("Taux TVA mis \xE0 jour avec succ\xE8s");
        },
        error: (error) => {
          console.error("Erreur mise \xE0 jour taux TVA:", error);
          this.isLoading.set(false);
          alert("Erreur lors de la mise \xE0 jour");
        }
      });
    } else {
      this.tauxTVAService.create(tauxTVAData).subscribe({
        next: () => {
          this.loadTauxTVA();
          this.closeModal();
          this.isLoading.set(false);
          alert("Taux TVA cr\xE9\xE9 avec succ\xE8s");
        },
        error: (error) => {
          console.error("Erreur cr\xE9ation taux TVA:", error);
          this.isLoading.set(false);
          alert("Erreur lors de la cr\xE9ation");
        }
      });
    }
  }
  /**
   * Supprimer une catégorie
   */
  deleteCategorie(categorie) {
    if (confirm(`\xCAtes-vous s\xFBr de vouloir supprimer la cat\xE9gorie "${categorie.nom}" ?`)) {
      this.categorieService.delete(categorie.id).subscribe({
        next: () => {
          this.loadCategories();
          alert("Cat\xE9gorie supprim\xE9e avec succ\xE8s");
        },
        error: (error) => {
          console.error("Erreur suppression cat\xE9gorie:", error);
          alert("Erreur lors de la suppression");
        }
      });
    }
  }
  /**
   * Supprimer une sous-catégorie
   */
  deleteSousCategorie(sousCategorie) {
    if (confirm(`\xCAtes-vous s\xFBr de vouloir supprimer la sous-cat\xE9gorie "${sousCategorie.nom}" ?`)) {
      this.sousCategorieService.delete(sousCategorie.id).subscribe({
        next: () => {
          this.loadSousCategories();
          alert("Sous-cat\xE9gorie supprim\xE9e avec succ\xE8s");
        },
        error: (error) => {
          console.error("Erreur suppression sous-cat\xE9gorie:", error);
          alert("Erreur lors de la suppression");
        }
      });
    }
  }
  /**
   * Supprimer une marque
   */
  deleteMarque(marque) {
    if (confirm(`\xCAtes-vous s\xFBr de vouloir supprimer la marque "${marque.name}" ?`)) {
      this.marqueService.delete(marque.id).subscribe({
        next: () => {
          this.loadMarques();
          alert("Marque supprim\xE9e avec succ\xE8s");
        },
        error: (error) => {
          console.error("Erreur suppression marque:", error);
          alert("Erreur lors de la suppression");
        }
      });
    }
  }
  /**
   * Supprimer une forme
   */
  deleteForme(forme) {
    if (confirm(`\xCAtes-vous s\xFBr de vouloir supprimer la forme "${forme.nom}" ?`)) {
      this.formeService.delete(forme.id).subscribe({
        next: () => {
          this.loadFormes();
          alert("Forme supprim\xE9e avec succ\xE8s");
        },
        error: (error) => {
          console.error("Erreur suppression forme:", error);
          alert("Erreur lors de la suppression");
        }
      });
    }
  }
  /**
   * Supprimer un taux TVA
   */
  deleteTauxTVA(taux) {
    if (confirm(`\xCAtes-vous s\xFBr de vouloir supprimer le taux TVA "${taux.libelle}" ?`)) {
      this.tauxTVAService.delete(taux.id).subscribe({
        next: () => {
          this.loadTauxTVA();
          alert("Taux TVA supprim\xE9 avec succ\xE8s");
        },
        error: (error) => {
          console.error("Erreur suppression taux TVA:", error);
          alert("Erreur lors de la suppression");
        }
      });
    }
  }
  /**
   * Demander l'ajout d'une nouvelle catégorie (pour fournisseurs)
   */
  requestNewCategorie() {
    console.log("\u{1F504} Redirection vers la page de demande de cat\xE9gorie...");
    this.router.navigate(["/dashboard/demandes-categories"]);
  }
  /**
   * Demander l'ajout d'une nouvelle sous-catégorie (pour fournisseurs)
   */
  requestNewSousCategorie() {
    console.log("\u{1F504} Redirection vers la page de demande de cat\xE9gorie (onglet sous-cat\xE9gorie)...");
    this.router.navigate(["/dashboard/demandes-categories"]);
  }
  /**
   * Obtenir l'URL du logo d'une marque
   */
  getMarqueLogoUrl(logoPath) {
    return this.imageUrlService.getMarqueLogoUrl(logoPath);
  }
  /**
   * Obtenir l'URL de l'image d'une forme
   */
  getFormeImageUrl(imagePath) {
    return this.imageUrlService.getFullImageUrl(imagePath);
  }
  /**
   * Gérer les erreurs d'images
   */
  onImageError(event) {
    console.warn("Erreur de chargement d'image:", event.target.src);
    event.target.src = "data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNDAiIGhlaWdodD0iNDAiIHZpZXdCb3g9IjAgMCA0MCA0MCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHJlY3Qgd2lkdGg9IjQwIiBoZWlnaHQ9IjQwIiBmaWxsPSIjRjNGNEY2Ii8+CjxwYXRoIGQ9Ik0yMCAyOEMyNC40MTgzIDI4IDI4IDI0LjQxODMgMjggMjBDMjggMTUuNTgxNyAyNC40MTgzIDEyIDIwIDEyQzE1LjU4MTcgMTIgMTIgMTUuNTgxNyAxMiAyMEMxMiAyNC40MTgzIDE1LjU4MTcgMjggMjAgMjhaIiBmaWxsPSIjOUNBM0FGIi8+Cjwvc3ZnPgo=";
  }
  /**
   * Formater une date
   */
  formatDate(dateString) {
    return new Date(dateString).toLocaleDateString("fr-FR");
  }
  static \u0275fac = function ReferentielsComponent_Factory(__ngFactoryType__) {
    return new (__ngFactoryType__ || _ReferentielsComponent)(\u0275\u0275directiveInject(CategorieService), \u0275\u0275directiveInject(SousCategorieService), \u0275\u0275directiveInject(MarqueService), \u0275\u0275directiveInject(FormeService), \u0275\u0275directiveInject(TauxTVAService), \u0275\u0275directiveInject(AuthService), \u0275\u0275directiveInject(Router), \u0275\u0275directiveInject(ImageUrlService));
  };
  static \u0275cmp = /* @__PURE__ */ \u0275\u0275defineComponent({ type: _ReferentielsComponent, selectors: [["app-referentiels"]], decls: 17, vars: 9, consts: [["form", "ngForm"], [1, "referentiels-container"], [1, "referentiels-header"], [1, "referentiels-title"], [1, "tabs"], ["class", "tab-button", 3, "active", "click", 4, "ngFor", "ngForOf"], [1, "tab-content"], ["class", "tab-panel", 4, "ngIf"], ["class", "modal-overlay", 3, "click", 4, "ngIf"], [1, "tab-button", 3, "click"], [1, "tab-panel"], [1, "panel-header"], ["class", "btn btn-primary", 3, "click", 4, "ngIf"], ["class", "supplier-actions", 4, "ngIf"], [1, "items-grid"], ["class", "item-card", 4, "ngFor", "ngForOf"], [1, "btn", "btn-primary", 3, "click"], [1, "supplier-actions"], [1, "btn", "btn-outline-primary", 3, "click"], [1, "badge", "badge-info"], [1, "item-card"], [1, "item-header"], [1, "badge"], [4, "ngIf"], [1, "item-stats"], ["class", "item-actions", 4, "ngIf"], [1, "item-actions"], [1, "btn", "btn-sm", "btn-secondary", 3, "click"], [1, "btn", "btn-sm", "btn-danger", 3, "click"], [1, "badge", "badge-success"], [1, "marque-logo"], [1, "logo-img", 3, "error", "src", "alt"], [1, "forme-image"], [1, "forme-img", 3, "error", "src", "alt"], ["class", "read-only-notice", 4, "ngIf"], [1, "read-only-notice"], [1, "tva-rate"], [1, "rate"], [1, "modal-overlay", 3, "click"], [1, "modal-content", 3, "click"], [1, "modal-header"], [1, "close-btn", 3, "click"], [1, "modal-form", 3, "ngSubmit"], [3, "ngSwitch"], [4, "ngSwitchCase"], [1, "form-actions"], ["type", "button", 1, "btn", "btn-secondary", 3, "click"], ["type", "submit", 1, "btn", "btn-primary", 3, "disabled"], [1, "form-group"], ["type", "text", "name", "nom", "ngModel", "", "required", "", "placeholder", "Ex: Lunettes de Vue", 1, "form-control"], ["name", "description", "ngModel", "", "placeholder", "Description de la cat\xE9gorie...", 1, "form-control"], ["name", "categorieId", "ngModel", "", "required", "", 1, "form-control"], ["value", ""], [3, "value", 4, "ngFor", "ngForOf"], ["type", "text", "name", "nom", "ngModel", "", "required", "", "placeholder", "Ex: Montures Classiques", 1, "form-control"], ["name", "description", "ngModel", "", "placeholder", "Description de la sous-cat\xE9gorie...", 1, "form-control"], ["type", "checkbox", "name", "estValidee", "ngModel", ""], [3, "value"], ["type", "text", "name", "name", "ngModel", "", "required", "", "placeholder", "Ex: Ray-Ban", 1, "form-control"], ["type", "text", "name", "logo", "ngModel", "", "placeholder", "Ex: rayban-logo.png", 1, "form-control"], ["type", "text", "name", "nom", "ngModel", "", "required", "", "placeholder", "Ex: Rectangulaire", 1, "form-control"], ["type", "text", "name", "imageUrl", "ngModel", "", "placeholder", "Ex: forme-rectangulaire.png", 1, "form-control"], ["type", "text", "name", "libelle", "ngModel", "", "required", "", "placeholder", "Ex: TVA Standard", 1, "form-control"], ["type", "number", "name", "taux", "ngModel", "", "required", "", "min", "0", "max", "100", "step", "0.1", "placeholder", "Ex: 20", 1, "form-control"], ["name", "description", "ngModel", "", "placeholder", "Description du taux TVA...", 1, "form-control"], ["type", "date", "name", "dateEffet", "ngModel", "", "required", "", 1, "form-control"], ["type", "date", "name", "dateFin", "ngModel", "", 1, "form-control"], ["type", "checkbox", "name", "estActif", "ngModel", "", "checked", ""]], template: function ReferentielsComponent_Template(rf, ctx) {
    if (rf & 1) {
      \u0275\u0275elementStart(0, "div", 1)(1, "div", 2)(2, "h1", 3)(3, "span");
      \u0275\u0275text(4, "\u{1F5C2}\uFE0F");
      \u0275\u0275elementEnd();
      \u0275\u0275text(5);
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(6, "p");
      \u0275\u0275text(7);
      \u0275\u0275elementEnd()();
      \u0275\u0275elementStart(8, "div", 4);
      \u0275\u0275template(9, ReferentielsComponent_button_9_Template, 2, 4, "button", 5);
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(10, "div", 6);
      \u0275\u0275template(11, ReferentielsComponent_div_11_Template, 8, 3, "div", 7)(12, ReferentielsComponent_div_12_Template, 8, 3, "div", 7)(13, ReferentielsComponent_div_13_Template, 8, 3, "div", 7)(14, ReferentielsComponent_div_14_Template, 8, 3, "div", 7)(15, ReferentielsComponent_div_15_Template, 8, 3, "div", 7);
      \u0275\u0275elementEnd()();
      \u0275\u0275template(16, ReferentielsComponent_div_16_Template, 20, 9, "div", 8);
    }
    if (rf & 2) {
      \u0275\u0275advance(5);
      \u0275\u0275textInterpolate1(" ", ctx.isAdmin() ? "Gestion des R\xE9f\xE9rentiels" : "Consultation des R\xE9f\xE9rentiels", " ");
      \u0275\u0275advance(2);
      \u0275\u0275textInterpolate(ctx.isAdmin() ? "Configurez vos cat\xE9gories, marques et param\xE8tres" : "Consultez les cat\xE9gories, marques et param\xE8tres disponibles");
      \u0275\u0275advance(2);
      \u0275\u0275property("ngForOf", ctx.tabs);
      \u0275\u0275advance(2);
      \u0275\u0275property("ngIf", ctx.activeTab() === "categories");
      \u0275\u0275advance();
      \u0275\u0275property("ngIf", ctx.activeTab() === "sous-categories");
      \u0275\u0275advance();
      \u0275\u0275property("ngIf", ctx.activeTab() === "marques");
      \u0275\u0275advance();
      \u0275\u0275property("ngIf", ctx.activeTab() === "formes");
      \u0275\u0275advance();
      \u0275\u0275property("ngIf", ctx.activeTab() === "tva");
      \u0275\u0275advance();
      \u0275\u0275property("ngIf", ctx.showModal());
    }
  }, dependencies: [CommonModule, NgForOf, NgIf, NgSwitch, NgSwitchCase, FormsModule, \u0275NgNoValidate, NgSelectOption, \u0275NgSelectMultipleOption, DefaultValueAccessor, NumberValueAccessor, CheckboxControlValueAccessor, SelectControlValueAccessor, NgControlStatus, NgControlStatusGroup, RequiredValidator, MinValidator, MaxValidator, NgModel, NgForm], styles: [`

.referentiels-container[_ngcontent-%COMP%] {
  padding: var(--spacing-8);
  max-width: 1400px;
  margin: 0 auto;
  background: transparent;
}
.referentiels-header[_ngcontent-%COMP%] {
  background: var(--gradient-primary);
  border-radius: var(--border-radius-2xl);
  padding: var(--spacing-8);
  margin-bottom: var(--spacing-8);
  color: var(--white);
  box-shadow: var(--shadow-blue-lg);
  position: relative;
  overflow: hidden;
}
.referentiels-header[_ngcontent-%COMP%]::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="dots" width="20" height="20" patternUnits="userSpaceOnUse"><circle cx="10" cy="10" r="1" fill="rgba(255,255,255,0.1)"/></pattern></defs><rect width="100" height="100" fill="url(%23dots)"/></svg>');
  opacity: 0.3;
}
.referentiels-title[_ngcontent-%COMP%] {
  font-size: var(--font-size-3xl);
  font-weight: var(--font-weight-extrabold);
  margin: 0 0 var(--spacing-2) 0;
  display: flex;
  align-items: center;
  gap: var(--spacing-3);
}
.tabs[_ngcontent-%COMP%] {
  display: flex;
  background: var(--white);
  border-radius: var(--border-radius-xl);
  padding: var(--spacing-2);
  margin-bottom: var(--spacing-8);
  box-shadow: var(--shadow-base);
  overflow-x: auto;
}
.tab-button[_ngcontent-%COMP%] {
  padding: var(--spacing-3) var(--spacing-6);
  border: none;
  background: transparent;
  cursor: pointer;
  border-radius: var(--border-radius-lg);
  font-weight: var(--font-weight-medium);
  color: var(--gray-600);
  transition: all var(--transition-fast);
  white-space: nowrap;
  display: flex;
  align-items: center;
  gap: var(--spacing-2);
}
.tab-button.active[_ngcontent-%COMP%] {
  background: var(--primary-600);
  color: var(--white);
  box-shadow: var(--shadow-md);
}
.tab-button[_ngcontent-%COMP%]:hover:not(.active) {
  background: var(--primary-50);
  color: var(--primary-600);
}
.panel-header[_ngcontent-%COMP%] {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--spacing-8);
  background: var(--white);
  padding: var(--spacing-6);
  border-radius: var(--border-radius-xl);
  box-shadow: var(--shadow-base);
}
.panel-header[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%] {
  font-size: var(--font-size-xl);
  font-weight: var(--font-weight-bold);
  color: var(--gray-900);
  margin: 0;
  display: flex;
  align-items: center;
  gap: var(--spacing-2);
}
.items-grid[_ngcontent-%COMP%] {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
  gap: var(--spacing-6);
}
.item-card[_ngcontent-%COMP%] {
  background: var(--white);
  padding: var(--spacing-6);
  border-radius: var(--border-radius-xl);
  box-shadow: var(--shadow-base);
  transition: all var(--transition-base);
  position: relative;
  overflow: hidden;
}
.item-card[_ngcontent-%COMP%]::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: var(--gradient-primary);
}
.item-card[_ngcontent-%COMP%]:hover {
  transform: translateY(-4px);
  box-shadow: var(--shadow-lg);
}
.item-header[_ngcontent-%COMP%] {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--spacing-4);
}
.item-header[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%] {
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-bold);
  color: var(--gray-900);
  margin: 0;
}
.badge[_ngcontent-%COMP%] {
  padding: var(--spacing-1) var(--spacing-3);
  border-radius: var(--border-radius-full);
  font-size: var(--font-size-xs);
  font-weight: var(--font-weight-medium);
  text-transform: uppercase;
  letter-spacing: 0.025em;
}
.badge-success[_ngcontent-%COMP%] {
  background: var(--success-100);
  color: var(--success-800);
  border: 1px solid var(--success-200);
}
.badge-warning[_ngcontent-%COMP%] {
  background: var(--warning-100);
  color: var(--warning-800);
  border: 1px solid var(--warning-200);
}
.badge-danger[_ngcontent-%COMP%] {
  background: var(--error-100);
  color: var(--error-800);
  border: 1px solid var(--error-200);
}
.item-stats[_ngcontent-%COMP%] {
  margin: var(--spacing-4) 0;
  color: var(--gray-600);
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
}
.item-actions[_ngcontent-%COMP%] {
  display: flex;
  gap: var(--spacing-2);
  margin-top: var(--spacing-4);
}
.tva-rate[_ngcontent-%COMP%] {
  text-align: center;
  margin: var(--spacing-4) 0;
  padding: var(--spacing-4);
  background: var(--primary-50);
  border-radius: var(--border-radius-lg);
}
.rate[_ngcontent-%COMP%] {
  font-size: var(--font-size-4xl);
  font-weight: var(--font-weight-extrabold);
  color: var(--primary-600);
  display: block;
}
.marque-logo[_ngcontent-%COMP%], 
.forme-image[_ngcontent-%COMP%] {
  text-align: center;
  margin: var(--spacing-4) 0;
  padding: var(--spacing-3);
  background: var(--gray-50);
  border-radius: var(--border-radius-lg);
}
.logo-img[_ngcontent-%COMP%], 
.forme-img[_ngcontent-%COMP%] {
  max-width: 120px;
  max-height: 80px;
  object-fit: contain;
  border-radius: var(--border-radius-md);
}
.modal-overlay[_ngcontent-%COMP%] {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.6);
  -webkit-backdrop-filter: blur(4px);
  backdrop-filter: blur(4px);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: var(--z-modal);
  animation: fadeIn 0.2s ease-out;
}
.modal-content[_ngcontent-%COMP%] {
  background: var(--white);
  border-radius: var(--border-radius-2xl);
  box-shadow: var(--shadow-2xl);
  max-width: 600px;
  width: 90%;
  max-height: 90vh;
  overflow-y: auto;
  animation: slideUp 0.3s ease-out;
}
.modal-header[_ngcontent-%COMP%] {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--spacing-6);
  border-bottom: 1px solid var(--gray-200);
  background:
    linear-gradient(
      135deg,
      var(--gray-50) 0%,
      var(--white) 100%);
}
.modal-header[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%] {
  font-size: var(--font-size-xl);
  font-weight: var(--font-weight-bold);
  color: var(--gray-900);
  margin: 0;
}
.close-btn[_ngcontent-%COMP%] {
  background: none;
  border: none;
  font-size: var(--font-size-xl);
  cursor: pointer;
  color: var(--gray-400);
  padding: var(--spacing-2);
  border-radius: var(--border-radius-lg);
  transition: var(--transition-fast);
}
.close-btn[_ngcontent-%COMP%]:hover {
  background: var(--gray-100);
  color: var(--gray-600);
}
.modal-form[_ngcontent-%COMP%] {
  padding: var(--spacing-6);
}
.form-group[_ngcontent-%COMP%] {
  margin-bottom: var(--spacing-6);
}
.form-group[_ngcontent-%COMP%]   label[_ngcontent-%COMP%] {
  display: block;
  margin-bottom: var(--spacing-2);
  font-weight: var(--font-weight-semibold);
  color: var(--gray-700);
  font-size: var(--font-size-sm);
}
.form-control[_ngcontent-%COMP%] {
  width: 100%;
  padding: var(--spacing-3) var(--spacing-4);
  border: 2px solid var(--gray-300);
  border-radius: var(--border-radius-lg);
  font-size: var(--font-size-base);
  transition: all var(--transition-fast);
  background: var(--white);
}
.form-control[_ngcontent-%COMP%]:focus {
  outline: none;
  border-color: var(--primary-500);
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
  transform: translateY(-1px);
}
.form-actions[_ngcontent-%COMP%] {
  display: flex;
  gap: var(--spacing-3);
  justify-content: flex-end;
  padding: var(--spacing-6);
  border-top: 1px solid var(--gray-200);
  background: var(--gray-50);
}
.btn[_ngcontent-%COMP%] {
  padding: var(--spacing-3) var(--spacing-4);
  border: none;
  border-radius: var(--border-radius-lg);
  cursor: pointer;
  font-weight: var(--font-weight-medium);
  transition: all var(--transition-fast);
  display: inline-flex;
  align-items: center;
  gap: var(--spacing-2);
}
.btn-primary[_ngcontent-%COMP%] {
  background: var(--gradient-primary);
  color: var(--white);
  box-shadow: var(--shadow-blue);
}
.btn-primary[_ngcontent-%COMP%]:hover {
  background:
    linear-gradient(
      135deg,
      #5a67d8 0%,
      #667eea 100%);
  transform: translateY(-1px);
  box-shadow: var(--shadow-blue-lg);
}
.btn-secondary[_ngcontent-%COMP%] {
  background: var(--white);
  color: var(--gray-700);
  border: 2px solid var(--gray-300);
  box-shadow: var(--shadow-sm);
}
.btn-secondary[_ngcontent-%COMP%]:hover {
  background: var(--gray-50);
  border-color: var(--gray-400);
  transform: translateY(-1px);
}
.btn-danger[_ngcontent-%COMP%] {
  background:
    linear-gradient(
      135deg,
      var(--error-600),
      var(--error-500));
  color: var(--white);
  box-shadow: var(--shadow-sm);
}
.btn-danger[_ngcontent-%COMP%]:hover {
  background:
    linear-gradient(
      135deg,
      var(--error-700),
      var(--error-600));
  transform: translateY(-1px);
  box-shadow: var(--shadow-md);
}
.btn-sm[_ngcontent-%COMP%] {
  padding: var(--spacing-2) var(--spacing-3);
  font-size: var(--font-size-xs);
}
.read-only-notice[_ngcontent-%COMP%] {
  display: flex;
  align-items: center;
  gap: var(--spacing-2);
}
.badge-info[_ngcontent-%COMP%] {
  background:
    linear-gradient(
      135deg,
      #3b82f6,
      #1d4ed8);
  color: var(--white);
  padding: var(--spacing-2) var(--spacing-3);
  border-radius: var(--border-radius-full);
  font-size: var(--font-size-xs);
  font-weight: 600;
  display: inline-flex;
  align-items: center;
  gap: var(--spacing-1);
  box-shadow: var(--shadow-sm);
}
.badge-success[_ngcontent-%COMP%] {
  background:
    linear-gradient(
      135deg,
      #10b981,
      #059669);
  color: var(--white);
  padding: var(--spacing-2) var(--spacing-3);
  border-radius: var(--border-radius-full);
  font-size: var(--font-size-xs);
  font-weight: 600;
  display: inline-flex;
  align-items: center;
  gap: var(--spacing-1);
  box-shadow: var(--shadow-sm);
}
.supplier-actions[_ngcontent-%COMP%] {
  display: flex;
  align-items: center;
  gap: var(--spacing-3);
  flex-wrap: wrap;
}
.btn-outline-primary[_ngcontent-%COMP%] {
  background: transparent;
  color: var(--primary-600);
  border: 2px solid var(--primary-600);
  padding: var(--spacing-2) var(--spacing-4);
  border-radius: var(--border-radius-lg);
  font-weight: 600;
  transition: all 0.3s ease;
}
.btn-outline-primary[_ngcontent-%COMP%]:hover {
  background: var(--primary-600);
  color: var(--white);
  transform: translateY(-1px);
  box-shadow: var(--shadow-md);
}
@media (max-width: 768px) {
  .referentiels-container[_ngcontent-%COMP%] {
    padding: var(--spacing-4);
  }
  .items-grid[_ngcontent-%COMP%] {
    grid-template-columns: 1fr;
  }
  .panel-header[_ngcontent-%COMP%] {
    flex-direction: column;
    gap: var(--spacing-4);
    align-items: stretch;
  }
  .tabs[_ngcontent-%COMP%] {
    flex-wrap: wrap;
  }
  .modal-content[_ngcontent-%COMP%] {
    margin: var(--spacing-4);
    max-width: calc(100vw - 2rem);
  }
  .item-actions[_ngcontent-%COMP%] {
    flex-direction: column;
  }
}
/*# sourceMappingURL=referentiels.component.css.map */`] });
};
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && setClassMetadata(ReferentielsComponent, [{
    type: Component,
    args: [{ selector: "app-referentiels", standalone: true, imports: [CommonModule, FormsModule], template: `
    <div class="referentiels-container">
      <!-- Header moderne -->
      <div class="referentiels-header">
        <h1 class="referentiels-title">
          <span>\u{1F5C2}\uFE0F</span>
          {{ isAdmin() ? 'Gestion des R\xE9f\xE9rentiels' : 'Consultation des R\xE9f\xE9rentiels' }}
        </h1>
        <p>{{ isAdmin() ? 'Configurez vos cat\xE9gories, marques et param\xE8tres' : 'Consultez les cat\xE9gories, marques et param\xE8tres disponibles' }}</p>
      </div>

      <!-- Onglets -->
      <div class="tabs">
        <button
          *ngFor="let tab of tabs"
          class="tab-button"
          [class.active]="activeTab() === tab.id"
          (click)="setActiveTab(tab.id)"
        >
          {{ tab.icon }} {{ tab.label }}
        </button>
      </div>

      <!-- Contenu des onglets -->
      <div class="tab-content">

        <!-- Cat\xE9gories -->
        <div *ngIf="activeTab() === 'categories'" class="tab-panel">
          <div class="panel-header">
            <h3>\u{1F4C1} Cat\xE9gories</h3>
            <button
              *ngIf="isAdmin()"
              class="btn btn-primary"
              (click)="openCategorieForm()"
            >
              \u2795 Nouvelle Cat\xE9gorie
            </button>
            <div *ngIf="!isAdmin()" class="supplier-actions">
              <button
                class="btn btn-outline-primary"
                (click)="requestNewCategorie()"
              >
                \u{1F4DD} Demander une nouvelle cat\xE9gorie
              </button>
              <span class="badge badge-info">\u{1F441}\uFE0F Mode consultation</span>
            </div>
          </div>

          <div class="items-grid">
            <div *ngFor="let categorie of categories()" class="item-card">
              <div class="item-header">
                <h4>{{ categorie.nom }}</h4>
                <span class="badge" [class]="categorie.estValidee ? 'badge-success' : 'badge-warning'">
                  {{ categorie.estValidee ? 'Valid\xE9e' : 'En attente' }}
                </span>
              </div>
              <p *ngIf="categorie.description">{{ categorie.description }}</p>
              <div class="item-stats">
                <span>{{ categorie.sousCategoriesCount || 0 }} sous-cat\xE9gories</span>
              </div>
              <div class="item-actions" *ngIf="isAdmin()">
                <button class="btn btn-sm btn-secondary" (click)="editCategorie(categorie)">
                  \u270F\uFE0F Modifier
                </button>
                <button class="btn btn-sm btn-danger" (click)="deleteCategorie(categorie)">
                  \u{1F5D1}\uFE0F Supprimer
                </button>
              </div>
            </div>
          </div>
        </div>

        <!-- Sous-cat\xE9gories -->
        <div *ngIf="activeTab() === 'sous-categories'" class="tab-panel">
          <div class="panel-header">
            <h3>\u{1F4C2} Sous-cat\xE9gories</h3>
            <button
              *ngIf="isAdmin()"
              class="btn btn-primary"
              (click)="openSousCategorieForm()"
            >
              \u2795 Nouvelle Sous-cat\xE9gorie
            </button>
            <div *ngIf="!isAdmin()" class="supplier-actions">
              <button
                class="btn btn-outline-primary"
                (click)="requestNewSousCategorie()"
              >
                \u{1F4DD} Demander une nouvelle sous-cat\xE9gorie
              </button>
              <span class="badge badge-info">\u{1F441}\uFE0F Mode consultation</span>
            </div>
          </div>

          <div class="items-grid">
            <div *ngFor="let sousCategorie of sousCategories()" class="item-card">
              <div class="item-header">
                <h4>{{ sousCategorie.nom }}</h4>
                <span class="badge" [class]="sousCategorie.estValidee ? 'badge-success' : 'badge-warning'">
                  {{ sousCategorie.estValidee ? 'Valid\xE9e' : 'En attente' }}
                </span>
              </div>
              <p *ngIf="sousCategorie.description">{{ sousCategorie.description }}</p>
              <div class="item-stats">
                <span>Cat\xE9gorie: {{ sousCategorie.categorieNom || 'Non d\xE9finie' }}</span><br>
                <span>{{ sousCategorie.produitsCount || 0 }} produits</span>
              </div>
              <div class="item-actions" *ngIf="isAdmin()">
                <button class="btn btn-sm btn-secondary" (click)="editSousCategorie(sousCategorie)">
                  \u270F\uFE0F Modifier
                </button>
                <button class="btn btn-sm btn-danger" (click)="deleteSousCategorie(sousCategorie)">
                  \u{1F5D1}\uFE0F Supprimer
                </button>
              </div>
            </div>
          </div>
        </div>

        <!-- Marques -->
        <div *ngIf="activeTab() === 'marques'" class="tab-panel">
          <div class="panel-header">
            <h3>\u{1F3F7}\uFE0F Marques</h3>
            <button
              *ngIf="isAdmin()"
              class="btn btn-primary"
              (click)="openMarqueForm()"
            >
              \u2795 Nouvelle Marque
            </button>
            <div *ngIf="!isAdmin()" class="supplier-actions">
              <button
                class="btn btn-primary"
                (click)="openMarqueForm()"
              >
                \u2795 Ajouter une marque
              </button>
              <span class="badge badge-success">\u2705 Ajout autoris\xE9</span>
            </div>
          </div>

          <div class="items-grid">
            <div *ngFor="let marque of marques()" class="item-card">
              <div class="item-header">
                <h4>{{ marque.name }}</h4>
              </div>
              <div class="marque-logo">
                <img
                  [src]="getMarqueLogoUrl(marque.logo)"
                  [alt]="marque.name"
                  class="logo-img"
                  (error)="onImageError($event)"
                >
              </div>
              <div class="item-actions" *ngIf="isAdmin()">
                <button class="btn btn-sm btn-secondary" (click)="editMarque(marque)">
                  \u270F\uFE0F Modifier
                </button>
                <button class="btn btn-sm btn-danger" (click)="deleteMarque(marque)">
                  \u{1F5D1}\uFE0F Supprimer
                </button>
              </div>
            </div>
          </div>
        </div>

        <!-- Formes -->
        <div *ngIf="activeTab() === 'formes'" class="tab-panel">
          <div class="panel-header">
            <h3>\u{1F537} Formes</h3>
            <button
              *ngIf="isAdmin()"
              class="btn btn-primary"
              (click)="openFormeForm()"
            >
              \u2795 Nouvelle Forme
            </button>
            <div *ngIf="!isAdmin()" class="supplier-actions">
              <button
                class="btn btn-primary"
                (click)="openFormeForm()"
              >
                \u2795 Ajouter une forme
              </button>
              <span class="badge badge-success">\u2705 Ajout autoris\xE9</span>
            </div>
          </div>

          <div class="items-grid">
            <div *ngFor="let forme of formes()" class="item-card">
              <div class="item-header">
                <h4>{{ forme.nom }}</h4>
              </div>
              <div class="forme-image">
                <img
                  [src]="getFormeImageUrl(forme.imageUrl)"
                  [alt]="forme.nom"
                  class="forme-img"
                  (error)="onImageError($event)"
                >
              </div>
              <div class="item-stats">
                <span>Cat\xE9gorie: {{ forme.categorieNom || 'Non d\xE9finie' }}</span>
              </div>
              <div class="item-actions" *ngIf="isAdmin()">
                <button class="btn btn-sm btn-secondary" (click)="editForme(forme)">
                  \u270F\uFE0F Modifier
                </button>
                <button class="btn btn-sm btn-danger" (click)="deleteForme(forme)">
                  \u{1F5D1}\uFE0F Supprimer
                </button>
              </div>
            </div>
          </div>
        </div>

        <!-- Taux TVA -->
        <div *ngIf="activeTab() === 'tva'" class="tab-panel">
          <div class="panel-header">
            <h3>\u{1F4B0} Taux TVA</h3>
            <button
              *ngIf="isAdmin()"
              class="btn btn-primary"
              (click)="openTauxTVAForm()"
            >
              \u2795 Nouveau Taux TVA
            </button>
            <div *ngIf="!isAdmin()" class="read-only-notice">
              <span class="badge badge-info">\u{1F441}\uFE0F Mode consultation seule</span>
            </div>
          </div>

          <div class="items-grid">
            <div *ngFor="let taux of tauxTVA()" class="item-card">
              <div class="item-header">
                <h4>{{ taux.libelle }}</h4>
                <span class="badge" [class]="taux.estActif ? 'badge-success' : 'badge-danger'">
                  {{ taux.estActif ? 'Actif' : 'Inactif' }}
                </span>
              </div>
              <div class="tva-rate">
                <span class="rate">{{ taux.taux }}%</span>
              </div>
              <p *ngIf="taux.description">{{ taux.description }}</p>
              <div class="item-stats">
                <span>Effet: {{ formatDate(taux.dateEffet) }}</span>
                <span *ngIf="taux.dateFin">Fin: {{ formatDate(taux.dateFin) }}</span>
              </div>
              <div class="item-actions" *ngIf="isAdmin()">
                <button class="btn btn-sm btn-secondary" (click)="editTauxTVA(taux)">
                  \u270F\uFE0F Modifier
                </button>
                <button class="btn btn-sm btn-danger" (click)="deleteTauxTVA(taux)">
                  \u{1F5D1}\uFE0F Supprimer
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Modal pour les formulaires -->
    <div class="modal-overlay" *ngIf="showModal()" (click)="closeModal()">
      <div class="modal-content" (click)="$event.stopPropagation()">
        <div class="modal-header">
          <h3>{{ modalTitle() }}</h3>
          <button class="close-btn" (click)="closeModal()">\u2715</button>
        </div>

        <form #form="ngForm" (ngSubmit)="onSubmit(form)" class="modal-form">
          <!-- Formulaire dynamique selon le type -->
          <ng-container [ngSwitch]="modalType()">

            <!-- Formulaire Cat\xE9gorie -->
            <div *ngSwitchCase="'categorie'">
              <div class="form-group">
                <label>Nom de la cat\xE9gorie *</label>
                <input
                  type="text"
                  name="nom"
                  ngModel
                  required
                  class="form-control"
                  placeholder="Ex: Lunettes de Vue"
                />
              </div>
              <div class="form-group">
                <label>Description</label>
                <textarea
                  name="description"
                  ngModel
                  class="form-control"
                  placeholder="Description de la cat\xE9gorie..."
                ></textarea>
              </div>
            </div>

            <!-- Formulaire Sous-cat\xE9gorie -->
            <div *ngSwitchCase="'sous-categorie'">
              <div class="form-group">
                <label>Cat\xE9gorie parente *</label>
                <select name="categorieId" ngModel required class="form-control">
                  <option value="">S\xE9lectionner une cat\xE9gorie</option>
                  <option *ngFor="let cat of categories()" [value]="cat.id">
                    {{ cat.nom }}
                  </option>
                </select>
              </div>
              <div class="form-group">
                <label>Nom de la sous-cat\xE9gorie *</label>
                <input
                  type="text"
                  name="nom"
                  ngModel
                  required
                  class="form-control"
                  placeholder="Ex: Montures Classiques"
                />
              </div>
              <div class="form-group">
                <label>Description</label>
                <textarea
                  name="description"
                  ngModel
                  class="form-control"
                  placeholder="Description de la sous-cat\xE9gorie..."
                ></textarea>
              </div>
              <div class="form-group">
                <label>
                  <input type="checkbox" name="estValidee" ngModel>
                  Valid\xE9e
                </label>
              </div>
            </div>

            <!-- Formulaire Marque -->
            <div *ngSwitchCase="'marque'">
              <div class="form-group">
                <label>Nom de la marque *</label>
                <input
                  type="text"
                  name="name"
                  ngModel
                  required
                  class="form-control"
                  placeholder="Ex: Ray-Ban"
                />
              </div>
              <div class="form-group">
                <label>URL du logo</label>
                <input
                  type="text"
                  name="logo"
                  ngModel
                  class="form-control"
                  placeholder="Ex: rayban-logo.png"
                />
              </div>
            </div>

            <!-- Formulaire Forme -->
            <div *ngSwitchCase="'forme'">
              <div class="form-group">
                <label>Cat\xE9gorie *</label>
                <select name="categorieId" ngModel required class="form-control">
                  <option value="">S\xE9lectionner une cat\xE9gorie</option>
                  <option *ngFor="let cat of categories()" [value]="cat.id">
                    {{ cat.nom }}
                  </option>
                </select>
              </div>
              <div class="form-group">
                <label>Nom de la forme *</label>
                <input
                  type="text"
                  name="nom"
                  ngModel
                  required
                  class="form-control"
                  placeholder="Ex: Rectangulaire"
                />
              </div>
              <div class="form-group">
                <label>URL de l'image</label>
                <input
                  type="text"
                  name="imageUrl"
                  ngModel
                  class="form-control"
                  placeholder="Ex: forme-rectangulaire.png"
                />
              </div>
            </div>

            <!-- Formulaire Taux TVA -->
            <div *ngSwitchCase="'tva'">
              <div class="form-group">
                <label>Libell\xE9 *</label>
                <input
                  type="text"
                  name="libelle"
                  ngModel
                  required
                  class="form-control"
                  placeholder="Ex: TVA Standard"
                />
              </div>
              <div class="form-group">
                <label>Taux (%) *</label>
                <input
                  type="number"
                  name="taux"
                  ngModel
                  required
                  min="0"
                  max="100"
                  step="0.1"
                  class="form-control"
                  placeholder="Ex: 20"
                />
              </div>
              <div class="form-group">
                <label>Description</label>
                <textarea
                  name="description"
                  ngModel
                  class="form-control"
                  placeholder="Description du taux TVA..."
                ></textarea>
              </div>
              <div class="form-group">
                <label>Date d'effet *</label>
                <input
                  type="date"
                  name="dateEffet"
                  ngModel
                  required
                  class="form-control"
                />
              </div>
              <div class="form-group">
                <label>Date de fin</label>
                <input
                  type="date"
                  name="dateFin"
                  ngModel
                  class="form-control"
                />
              </div>
              <div class="form-group">
                <label>
                  <input type="checkbox" name="estActif" ngModel checked>
                  Actif
                </label>
              </div>
            </div>
          </ng-container>

          <div class="form-actions">
            <button type="button" class="btn btn-secondary" (click)="closeModal()">
              Annuler
            </button>
            <button type="submit" class="btn btn-primary" [disabled]="!form.valid || isLoading()">
              {{ isEditMode() ? 'Modifier' : 'Cr\xE9er' }}
            </button>
          </div>
        </form>
      </div>
    </div>
  `, styles: [`/* angular:styles/component:css;4e6c858038676ae794fa1d8527ce0003d19691fec32dbcd712431cf9eac44261;C:/Users/<USER>/Desktop/STE/PFE/PFE/frontend-fournisseur/src/app/components/referentiels/referentiels.component.ts */
.referentiels-container {
  padding: var(--spacing-8);
  max-width: 1400px;
  margin: 0 auto;
  background: transparent;
}
.referentiels-header {
  background: var(--gradient-primary);
  border-radius: var(--border-radius-2xl);
  padding: var(--spacing-8);
  margin-bottom: var(--spacing-8);
  color: var(--white);
  box-shadow: var(--shadow-blue-lg);
  position: relative;
  overflow: hidden;
}
.referentiels-header::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="dots" width="20" height="20" patternUnits="userSpaceOnUse"><circle cx="10" cy="10" r="1" fill="rgba(255,255,255,0.1)"/></pattern></defs><rect width="100" height="100" fill="url(%23dots)"/></svg>');
  opacity: 0.3;
}
.referentiels-title {
  font-size: var(--font-size-3xl);
  font-weight: var(--font-weight-extrabold);
  margin: 0 0 var(--spacing-2) 0;
  display: flex;
  align-items: center;
  gap: var(--spacing-3);
}
.tabs {
  display: flex;
  background: var(--white);
  border-radius: var(--border-radius-xl);
  padding: var(--spacing-2);
  margin-bottom: var(--spacing-8);
  box-shadow: var(--shadow-base);
  overflow-x: auto;
}
.tab-button {
  padding: var(--spacing-3) var(--spacing-6);
  border: none;
  background: transparent;
  cursor: pointer;
  border-radius: var(--border-radius-lg);
  font-weight: var(--font-weight-medium);
  color: var(--gray-600);
  transition: all var(--transition-fast);
  white-space: nowrap;
  display: flex;
  align-items: center;
  gap: var(--spacing-2);
}
.tab-button.active {
  background: var(--primary-600);
  color: var(--white);
  box-shadow: var(--shadow-md);
}
.tab-button:hover:not(.active) {
  background: var(--primary-50);
  color: var(--primary-600);
}
.panel-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--spacing-8);
  background: var(--white);
  padding: var(--spacing-6);
  border-radius: var(--border-radius-xl);
  box-shadow: var(--shadow-base);
}
.panel-header h3 {
  font-size: var(--font-size-xl);
  font-weight: var(--font-weight-bold);
  color: var(--gray-900);
  margin: 0;
  display: flex;
  align-items: center;
  gap: var(--spacing-2);
}
.items-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
  gap: var(--spacing-6);
}
.item-card {
  background: var(--white);
  padding: var(--spacing-6);
  border-radius: var(--border-radius-xl);
  box-shadow: var(--shadow-base);
  transition: all var(--transition-base);
  position: relative;
  overflow: hidden;
}
.item-card::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: var(--gradient-primary);
}
.item-card:hover {
  transform: translateY(-4px);
  box-shadow: var(--shadow-lg);
}
.item-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--spacing-4);
}
.item-header h4 {
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-bold);
  color: var(--gray-900);
  margin: 0;
}
.badge {
  padding: var(--spacing-1) var(--spacing-3);
  border-radius: var(--border-radius-full);
  font-size: var(--font-size-xs);
  font-weight: var(--font-weight-medium);
  text-transform: uppercase;
  letter-spacing: 0.025em;
}
.badge-success {
  background: var(--success-100);
  color: var(--success-800);
  border: 1px solid var(--success-200);
}
.badge-warning {
  background: var(--warning-100);
  color: var(--warning-800);
  border: 1px solid var(--warning-200);
}
.badge-danger {
  background: var(--error-100);
  color: var(--error-800);
  border: 1px solid var(--error-200);
}
.item-stats {
  margin: var(--spacing-4) 0;
  color: var(--gray-600);
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
}
.item-actions {
  display: flex;
  gap: var(--spacing-2);
  margin-top: var(--spacing-4);
}
.tva-rate {
  text-align: center;
  margin: var(--spacing-4) 0;
  padding: var(--spacing-4);
  background: var(--primary-50);
  border-radius: var(--border-radius-lg);
}
.rate {
  font-size: var(--font-size-4xl);
  font-weight: var(--font-weight-extrabold);
  color: var(--primary-600);
  display: block;
}
.marque-logo,
.forme-image {
  text-align: center;
  margin: var(--spacing-4) 0;
  padding: var(--spacing-3);
  background: var(--gray-50);
  border-radius: var(--border-radius-lg);
}
.logo-img,
.forme-img {
  max-width: 120px;
  max-height: 80px;
  object-fit: contain;
  border-radius: var(--border-radius-md);
}
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.6);
  -webkit-backdrop-filter: blur(4px);
  backdrop-filter: blur(4px);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: var(--z-modal);
  animation: fadeIn 0.2s ease-out;
}
.modal-content {
  background: var(--white);
  border-radius: var(--border-radius-2xl);
  box-shadow: var(--shadow-2xl);
  max-width: 600px;
  width: 90%;
  max-height: 90vh;
  overflow-y: auto;
  animation: slideUp 0.3s ease-out;
}
.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--spacing-6);
  border-bottom: 1px solid var(--gray-200);
  background:
    linear-gradient(
      135deg,
      var(--gray-50) 0%,
      var(--white) 100%);
}
.modal-header h3 {
  font-size: var(--font-size-xl);
  font-weight: var(--font-weight-bold);
  color: var(--gray-900);
  margin: 0;
}
.close-btn {
  background: none;
  border: none;
  font-size: var(--font-size-xl);
  cursor: pointer;
  color: var(--gray-400);
  padding: var(--spacing-2);
  border-radius: var(--border-radius-lg);
  transition: var(--transition-fast);
}
.close-btn:hover {
  background: var(--gray-100);
  color: var(--gray-600);
}
.modal-form {
  padding: var(--spacing-6);
}
.form-group {
  margin-bottom: var(--spacing-6);
}
.form-group label {
  display: block;
  margin-bottom: var(--spacing-2);
  font-weight: var(--font-weight-semibold);
  color: var(--gray-700);
  font-size: var(--font-size-sm);
}
.form-control {
  width: 100%;
  padding: var(--spacing-3) var(--spacing-4);
  border: 2px solid var(--gray-300);
  border-radius: var(--border-radius-lg);
  font-size: var(--font-size-base);
  transition: all var(--transition-fast);
  background: var(--white);
}
.form-control:focus {
  outline: none;
  border-color: var(--primary-500);
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
  transform: translateY(-1px);
}
.form-actions {
  display: flex;
  gap: var(--spacing-3);
  justify-content: flex-end;
  padding: var(--spacing-6);
  border-top: 1px solid var(--gray-200);
  background: var(--gray-50);
}
.btn {
  padding: var(--spacing-3) var(--spacing-4);
  border: none;
  border-radius: var(--border-radius-lg);
  cursor: pointer;
  font-weight: var(--font-weight-medium);
  transition: all var(--transition-fast);
  display: inline-flex;
  align-items: center;
  gap: var(--spacing-2);
}
.btn-primary {
  background: var(--gradient-primary);
  color: var(--white);
  box-shadow: var(--shadow-blue);
}
.btn-primary:hover {
  background:
    linear-gradient(
      135deg,
      #5a67d8 0%,
      #667eea 100%);
  transform: translateY(-1px);
  box-shadow: var(--shadow-blue-lg);
}
.btn-secondary {
  background: var(--white);
  color: var(--gray-700);
  border: 2px solid var(--gray-300);
  box-shadow: var(--shadow-sm);
}
.btn-secondary:hover {
  background: var(--gray-50);
  border-color: var(--gray-400);
  transform: translateY(-1px);
}
.btn-danger {
  background:
    linear-gradient(
      135deg,
      var(--error-600),
      var(--error-500));
  color: var(--white);
  box-shadow: var(--shadow-sm);
}
.btn-danger:hover {
  background:
    linear-gradient(
      135deg,
      var(--error-700),
      var(--error-600));
  transform: translateY(-1px);
  box-shadow: var(--shadow-md);
}
.btn-sm {
  padding: var(--spacing-2) var(--spacing-3);
  font-size: var(--font-size-xs);
}
.read-only-notice {
  display: flex;
  align-items: center;
  gap: var(--spacing-2);
}
.badge-info {
  background:
    linear-gradient(
      135deg,
      #3b82f6,
      #1d4ed8);
  color: var(--white);
  padding: var(--spacing-2) var(--spacing-3);
  border-radius: var(--border-radius-full);
  font-size: var(--font-size-xs);
  font-weight: 600;
  display: inline-flex;
  align-items: center;
  gap: var(--spacing-1);
  box-shadow: var(--shadow-sm);
}
.badge-success {
  background:
    linear-gradient(
      135deg,
      #10b981,
      #059669);
  color: var(--white);
  padding: var(--spacing-2) var(--spacing-3);
  border-radius: var(--border-radius-full);
  font-size: var(--font-size-xs);
  font-weight: 600;
  display: inline-flex;
  align-items: center;
  gap: var(--spacing-1);
  box-shadow: var(--shadow-sm);
}
.supplier-actions {
  display: flex;
  align-items: center;
  gap: var(--spacing-3);
  flex-wrap: wrap;
}
.btn-outline-primary {
  background: transparent;
  color: var(--primary-600);
  border: 2px solid var(--primary-600);
  padding: var(--spacing-2) var(--spacing-4);
  border-radius: var(--border-radius-lg);
  font-weight: 600;
  transition: all 0.3s ease;
}
.btn-outline-primary:hover {
  background: var(--primary-600);
  color: var(--white);
  transform: translateY(-1px);
  box-shadow: var(--shadow-md);
}
@media (max-width: 768px) {
  .referentiels-container {
    padding: var(--spacing-4);
  }
  .items-grid {
    grid-template-columns: 1fr;
  }
  .panel-header {
    flex-direction: column;
    gap: var(--spacing-4);
    align-items: stretch;
  }
  .tabs {
    flex-wrap: wrap;
  }
  .modal-content {
    margin: var(--spacing-4);
    max-width: calc(100vw - 2rem);
  }
  .item-actions {
    flex-direction: column;
  }
}
/*# sourceMappingURL=referentiels.component.css.map */
`] }]
  }], () => [{ type: CategorieService }, { type: SousCategorieService }, { type: MarqueService }, { type: FormeService }, { type: TauxTVAService }, { type: AuthService }, { type: Router }, { type: ImageUrlService }], null);
})();
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && \u0275setClassDebugInfo(ReferentielsComponent, { className: "ReferentielsComponent", filePath: "src/app/components/referentiels/referentiels.component.ts", lineNumber: 965 });
})();
export {
  ReferentielsComponent
};
//# sourceMappingURL=chunk-XV7JT4TE.js.map

import {
  AuthService
} from "./chunk-ZSXXNL7U.js";
import {
  Router
} from "./chunk-6BVUYNW4.js";
import {
  DefaultValueAccessor,
  FormBuilder,
  FormControlName,
  FormGroupDirective,
  MaxLengthValidator,
  MaxValidator,
  MinValidator,
  NgControlStatus,
  NgControlStatusGroup,
  NumberValueAccessor,
  ReactiveFormsModule,
  Validators,
  ɵNgNoValidate
} from "./chunk-HQBVYEOO.js";
import "./chunk-7JDDWGD3.js";
import {
  CommonModule,
  Component,
  NgIf,
  setClassMetadata,
  ɵsetClassDebugInfo,
  ɵɵadvance,
  ɵɵclassProp,
  ɵɵdefineComponent,
  ɵɵdirectiveInject,
  ɵɵelement,
  ɵɵelementEnd,
  ɵɵelementStart,
  ɵɵlistener,
  ɵɵnextContext,
  ɵɵproperty,
  ɵɵtemplate,
  ɵɵtext,
  ɵɵtextInterpolate,
  ɵɵtextInterpolate1
} from "./chunk-UBZQS7JS.js";

// src/app/components/auth/register/register.component.ts
function RegisterComponent_div_16_div_1_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "div");
    \u0275\u0275text(1, "Le pr\xE9nom est requis");
    \u0275\u0275elementEnd();
  }
}
function RegisterComponent_div_16_div_2_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "div");
    \u0275\u0275text(1, "Le pr\xE9nom doit contenir au moins 2 caract\xE8res");
    \u0275\u0275elementEnd();
  }
}
function RegisterComponent_div_16_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "div", 56);
    \u0275\u0275template(1, RegisterComponent_div_16_div_1_Template, 2, 0, "div", 57)(2, RegisterComponent_div_16_div_2_Template, 2, 0, "div", 57);
    \u0275\u0275elementEnd();
  }
  if (rf & 2) {
    const ctx_r0 = \u0275\u0275nextContext();
    \u0275\u0275advance();
    \u0275\u0275property("ngIf", ctx_r0.prenom == null ? null : ctx_r0.prenom.errors == null ? null : ctx_r0.prenom.errors["required"]);
    \u0275\u0275advance();
    \u0275\u0275property("ngIf", ctx_r0.prenom == null ? null : ctx_r0.prenom.errors == null ? null : ctx_r0.prenom.errors["minlength"]);
  }
}
function RegisterComponent_div_21_div_1_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "div");
    \u0275\u0275text(1, "Le nom est requis");
    \u0275\u0275elementEnd();
  }
}
function RegisterComponent_div_21_div_2_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "div");
    \u0275\u0275text(1, "Le nom doit contenir au moins 2 caract\xE8res");
    \u0275\u0275elementEnd();
  }
}
function RegisterComponent_div_21_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "div", 56);
    \u0275\u0275template(1, RegisterComponent_div_21_div_1_Template, 2, 0, "div", 57)(2, RegisterComponent_div_21_div_2_Template, 2, 0, "div", 57);
    \u0275\u0275elementEnd();
  }
  if (rf & 2) {
    const ctx_r0 = \u0275\u0275nextContext();
    \u0275\u0275advance();
    \u0275\u0275property("ngIf", ctx_r0.nom == null ? null : ctx_r0.nom.errors == null ? null : ctx_r0.nom.errors["required"]);
    \u0275\u0275advance();
    \u0275\u0275property("ngIf", ctx_r0.nom == null ? null : ctx_r0.nom.errors == null ? null : ctx_r0.nom.errors["minlength"]);
  }
}
function RegisterComponent_div_27_div_1_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "div");
    \u0275\u0275text(1, "L'email est requis");
    \u0275\u0275elementEnd();
  }
}
function RegisterComponent_div_27_div_2_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "div");
    \u0275\u0275text(1, "Format d'email invalide");
    \u0275\u0275elementEnd();
  }
}
function RegisterComponent_div_27_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "div", 56);
    \u0275\u0275template(1, RegisterComponent_div_27_div_1_Template, 2, 0, "div", 57)(2, RegisterComponent_div_27_div_2_Template, 2, 0, "div", 57);
    \u0275\u0275elementEnd();
  }
  if (rf & 2) {
    const ctx_r0 = \u0275\u0275nextContext();
    \u0275\u0275advance();
    \u0275\u0275property("ngIf", ctx_r0.email == null ? null : ctx_r0.email.errors == null ? null : ctx_r0.email.errors["required"]);
    \u0275\u0275advance();
    \u0275\u0275property("ngIf", ctx_r0.email == null ? null : ctx_r0.email.errors == null ? null : ctx_r0.email.errors["email"]);
  }
}
function RegisterComponent_div_32_div_1_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "div");
    \u0275\u0275text(1, "Le t\xE9l\xE9phone est requis");
    \u0275\u0275elementEnd();
  }
}
function RegisterComponent_div_32_div_2_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "div");
    \u0275\u0275text(1, "Le t\xE9l\xE9phone doit contenir entre 8 et 15 chiffres");
    \u0275\u0275elementEnd();
  }
}
function RegisterComponent_div_32_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "div", 56);
    \u0275\u0275template(1, RegisterComponent_div_32_div_1_Template, 2, 0, "div", 57)(2, RegisterComponent_div_32_div_2_Template, 2, 0, "div", 57);
    \u0275\u0275elementEnd();
  }
  if (rf & 2) {
    const ctx_r0 = \u0275\u0275nextContext();
    \u0275\u0275advance();
    \u0275\u0275property("ngIf", ctx_r0.phoneNumber == null ? null : ctx_r0.phoneNumber.errors == null ? null : ctx_r0.phoneNumber.errors["required"]);
    \u0275\u0275advance();
    \u0275\u0275property("ngIf", ctx_r0.phoneNumber == null ? null : ctx_r0.phoneNumber.errors == null ? null : ctx_r0.phoneNumber.errors["pattern"]);
  }
}
function RegisterComponent_div_37_div_1_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "div");
    \u0275\u0275text(1, "La date de naissance est requise");
    \u0275\u0275elementEnd();
  }
}
function RegisterComponent_div_37_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "div", 56);
    \u0275\u0275template(1, RegisterComponent_div_37_div_1_Template, 2, 0, "div", 57);
    \u0275\u0275elementEnd();
  }
  if (rf & 2) {
    const ctx_r0 = \u0275\u0275nextContext();
    \u0275\u0275advance();
    \u0275\u0275property("ngIf", ctx_r0.dateNaissance == null ? null : ctx_r0.dateNaissance.errors == null ? null : ctx_r0.dateNaissance.errors["required"]);
  }
}
function RegisterComponent_div_43_div_1_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "div");
    \u0275\u0275text(1, "Le mot de passe est requis");
    \u0275\u0275elementEnd();
  }
}
function RegisterComponent_div_43_div_2_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "div");
    \u0275\u0275text(1, "Le mot de passe doit contenir au moins 6 caract\xE8res");
    \u0275\u0275elementEnd();
  }
}
function RegisterComponent_div_43_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "div", 56);
    \u0275\u0275template(1, RegisterComponent_div_43_div_1_Template, 2, 0, "div", 57)(2, RegisterComponent_div_43_div_2_Template, 2, 0, "div", 57);
    \u0275\u0275elementEnd();
  }
  if (rf & 2) {
    const ctx_r0 = \u0275\u0275nextContext();
    \u0275\u0275advance();
    \u0275\u0275property("ngIf", ctx_r0.password == null ? null : ctx_r0.password.errors == null ? null : ctx_r0.password.errors["required"]);
    \u0275\u0275advance();
    \u0275\u0275property("ngIf", ctx_r0.password == null ? null : ctx_r0.password.errors == null ? null : ctx_r0.password.errors["minlength"]);
  }
}
function RegisterComponent_div_48_div_1_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "div");
    \u0275\u0275text(1, "La confirmation est requise");
    \u0275\u0275elementEnd();
  }
}
function RegisterComponent_div_48_div_2_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "div");
    \u0275\u0275text(1, "Les mots de passe ne correspondent pas");
    \u0275\u0275elementEnd();
  }
}
function RegisterComponent_div_48_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "div", 56);
    \u0275\u0275template(1, RegisterComponent_div_48_div_1_Template, 2, 0, "div", 57)(2, RegisterComponent_div_48_div_2_Template, 2, 0, "div", 57);
    \u0275\u0275elementEnd();
  }
  if (rf & 2) {
    const ctx_r0 = \u0275\u0275nextContext();
    \u0275\u0275advance();
    \u0275\u0275property("ngIf", ctx_r0.confirmPassword == null ? null : ctx_r0.confirmPassword.errors == null ? null : ctx_r0.confirmPassword.errors["required"]);
    \u0275\u0275advance();
    \u0275\u0275property("ngIf", ctx_r0.confirmPassword == null ? null : ctx_r0.confirmPassword.errors == null ? null : ctx_r0.confirmPassword.errors["passwordMismatch"]);
  }
}
function RegisterComponent_div_57_div_1_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "div");
    \u0275\u0275text(1, "Le matricule fiscale est requis");
    \u0275\u0275elementEnd();
  }
}
function RegisterComponent_div_57_div_2_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "div");
    \u0275\u0275text(1, "Le matricule fiscale doit contenir exactement 8 caract\xE8res");
    \u0275\u0275elementEnd();
  }
}
function RegisterComponent_div_57_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "div", 56);
    \u0275\u0275template(1, RegisterComponent_div_57_div_1_Template, 2, 0, "div", 57)(2, RegisterComponent_div_57_div_2_Template, 2, 0, "div", 57);
    \u0275\u0275elementEnd();
  }
  if (rf & 2) {
    const ctx_r0 = \u0275\u0275nextContext();
    \u0275\u0275advance();
    \u0275\u0275property("ngIf", ctx_r0.matriculeFiscale == null ? null : ctx_r0.matriculeFiscale.errors == null ? null : ctx_r0.matriculeFiscale.errors["required"]);
    \u0275\u0275advance();
    \u0275\u0275property("ngIf", (ctx_r0.matriculeFiscale == null ? null : ctx_r0.matriculeFiscale.errors == null ? null : ctx_r0.matriculeFiscale.errors["minlength"]) || (ctx_r0.matriculeFiscale == null ? null : ctx_r0.matriculeFiscale.errors == null ? null : ctx_r0.matriculeFiscale.errors["maxlength"]));
  }
}
function RegisterComponent_div_62_div_1_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "div");
    \u0275\u0275text(1, "La raison sociale est requise");
    \u0275\u0275elementEnd();
  }
}
function RegisterComponent_div_62_div_2_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "div");
    \u0275\u0275text(1, "La raison sociale doit contenir au moins 2 caract\xE8res");
    \u0275\u0275elementEnd();
  }
}
function RegisterComponent_div_62_div_3_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "div");
    \u0275\u0275text(1, "La raison sociale ne peut pas d\xE9passer 200 caract\xE8res");
    \u0275\u0275elementEnd();
  }
}
function RegisterComponent_div_62_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "div", 56);
    \u0275\u0275template(1, RegisterComponent_div_62_div_1_Template, 2, 0, "div", 57)(2, RegisterComponent_div_62_div_2_Template, 2, 0, "div", 57)(3, RegisterComponent_div_62_div_3_Template, 2, 0, "div", 57);
    \u0275\u0275elementEnd();
  }
  if (rf & 2) {
    const ctx_r0 = \u0275\u0275nextContext();
    \u0275\u0275advance();
    \u0275\u0275property("ngIf", ctx_r0.raisonSociale == null ? null : ctx_r0.raisonSociale.errors == null ? null : ctx_r0.raisonSociale.errors["required"]);
    \u0275\u0275advance();
    \u0275\u0275property("ngIf", ctx_r0.raisonSociale == null ? null : ctx_r0.raisonSociale.errors == null ? null : ctx_r0.raisonSociale.errors["minlength"]);
    \u0275\u0275advance();
    \u0275\u0275property("ngIf", ctx_r0.raisonSociale == null ? null : ctx_r0.raisonSociale.errors == null ? null : ctx_r0.raisonSociale.errors["maxlength"]);
  }
}
function RegisterComponent_div_72_div_1_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "div");
    \u0275\u0275text(1, "Le RIB est requis");
    \u0275\u0275elementEnd();
  }
}
function RegisterComponent_div_72_div_2_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "div");
    \u0275\u0275text(1, "Le RIB doit contenir exactement 20 caract\xE8res");
    \u0275\u0275elementEnd();
  }
}
function RegisterComponent_div_72_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "div", 56);
    \u0275\u0275template(1, RegisterComponent_div_72_div_1_Template, 2, 0, "div", 57)(2, RegisterComponent_div_72_div_2_Template, 2, 0, "div", 57);
    \u0275\u0275elementEnd();
  }
  if (rf & 2) {
    const ctx_r0 = \u0275\u0275nextContext();
    \u0275\u0275advance();
    \u0275\u0275property("ngIf", ctx_r0.rib == null ? null : ctx_r0.rib.errors == null ? null : ctx_r0.rib.errors["required"]);
    \u0275\u0275advance();
    \u0275\u0275property("ngIf", (ctx_r0.rib == null ? null : ctx_r0.rib.errors == null ? null : ctx_r0.rib.errors["minlength"]) || (ctx_r0.rib == null ? null : ctx_r0.rib.errors == null ? null : ctx_r0.rib.errors["maxlength"]));
  }
}
function RegisterComponent_div_77_div_1_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "div");
    \u0275\u0275text(1, "Le code banque est requis");
    \u0275\u0275elementEnd();
  }
}
function RegisterComponent_div_77_div_2_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "div");
    \u0275\u0275text(1, "Le code banque doit contenir exactement 3 caract\xE8res");
    \u0275\u0275elementEnd();
  }
}
function RegisterComponent_div_77_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "div", 56);
    \u0275\u0275template(1, RegisterComponent_div_77_div_1_Template, 2, 0, "div", 57)(2, RegisterComponent_div_77_div_2_Template, 2, 0, "div", 57);
    \u0275\u0275elementEnd();
  }
  if (rf & 2) {
    const ctx_r0 = \u0275\u0275nextContext();
    \u0275\u0275advance();
    \u0275\u0275property("ngIf", ctx_r0.codeBanque == null ? null : ctx_r0.codeBanque.errors == null ? null : ctx_r0.codeBanque.errors["required"]);
    \u0275\u0275advance();
    \u0275\u0275property("ngIf", (ctx_r0.codeBanque == null ? null : ctx_r0.codeBanque.errors == null ? null : ctx_r0.codeBanque.errors["minlength"]) || (ctx_r0.codeBanque == null ? null : ctx_r0.codeBanque.errors == null ? null : ctx_r0.codeBanque.errors["maxlength"]));
  }
}
function RegisterComponent_div_83_div_1_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "div");
    \u0275\u0275text(1, "La commission est requise");
    \u0275\u0275elementEnd();
  }
}
function RegisterComponent_div_83_div_2_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "div");
    \u0275\u0275text(1, "La commission doit \xEAtre au minimum 50% (0.5)");
    \u0275\u0275elementEnd();
  }
}
function RegisterComponent_div_83_div_3_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "div");
    \u0275\u0275text(1, "La commission doit \xEAtre au maximum 100% (1.0)");
    \u0275\u0275elementEnd();
  }
}
function RegisterComponent_div_83_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "div", 56);
    \u0275\u0275template(1, RegisterComponent_div_83_div_1_Template, 2, 0, "div", 57)(2, RegisterComponent_div_83_div_2_Template, 2, 0, "div", 57)(3, RegisterComponent_div_83_div_3_Template, 2, 0, "div", 57);
    \u0275\u0275elementEnd();
  }
  if (rf & 2) {
    const ctx_r0 = \u0275\u0275nextContext();
    \u0275\u0275advance();
    \u0275\u0275property("ngIf", ctx_r0.commission == null ? null : ctx_r0.commission.errors == null ? null : ctx_r0.commission.errors["required"]);
    \u0275\u0275advance();
    \u0275\u0275property("ngIf", ctx_r0.commission == null ? null : ctx_r0.commission.errors == null ? null : ctx_r0.commission.errors["min"]);
    \u0275\u0275advance();
    \u0275\u0275property("ngIf", ctx_r0.commission == null ? null : ctx_r0.commission.errors == null ? null : ctx_r0.commission.errors["max"]);
  }
}
function RegisterComponent_div_90_div_1_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "div");
    \u0275\u0275text(1, "Le d\xE9lai de pr\xE9paration est requis");
    \u0275\u0275elementEnd();
  }
}
function RegisterComponent_div_90_div_2_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "div");
    \u0275\u0275text(1, "Le d\xE9lai doit \xEAtre d'au moins 1 jour");
    \u0275\u0275elementEnd();
  }
}
function RegisterComponent_div_90_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "div", 56);
    \u0275\u0275template(1, RegisterComponent_div_90_div_1_Template, 2, 0, "div", 57)(2, RegisterComponent_div_90_div_2_Template, 2, 0, "div", 57);
    \u0275\u0275elementEnd();
  }
  if (rf & 2) {
    const ctx_r0 = \u0275\u0275nextContext();
    \u0275\u0275advance();
    \u0275\u0275property("ngIf", ctx_r0.delaiPreparationJours == null ? null : ctx_r0.delaiPreparationJours.errors == null ? null : ctx_r0.delaiPreparationJours.errors["required"]);
    \u0275\u0275advance();
    \u0275\u0275property("ngIf", ctx_r0.delaiPreparationJours == null ? null : ctx_r0.delaiPreparationJours.errors == null ? null : ctx_r0.delaiPreparationJours.errors["min"]);
  }
}
function RegisterComponent_div_97_div_1_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "div");
    \u0275\u0275text(1, "Les frais de livraison sont requis");
    \u0275\u0275elementEnd();
  }
}
function RegisterComponent_div_97_div_2_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "div");
    \u0275\u0275text(1, "Les frais de livraison doivent \xEAtre positifs");
    \u0275\u0275elementEnd();
  }
}
function RegisterComponent_div_97_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "div", 56);
    \u0275\u0275template(1, RegisterComponent_div_97_div_1_Template, 2, 0, "div", 57)(2, RegisterComponent_div_97_div_2_Template, 2, 0, "div", 57);
    \u0275\u0275elementEnd();
  }
  if (rf & 2) {
    const ctx_r0 = \u0275\u0275nextContext();
    \u0275\u0275advance();
    \u0275\u0275property("ngIf", ctx_r0.fraisLivraisonBase == null ? null : ctx_r0.fraisLivraisonBase.errors == null ? null : ctx_r0.fraisLivraisonBase.errors["required"]);
    \u0275\u0275advance();
    \u0275\u0275property("ngIf", ctx_r0.fraisLivraisonBase == null ? null : ctx_r0.fraisLivraisonBase.errors == null ? null : ctx_r0.fraisLivraisonBase.errors["min"]);
  }
}
function RegisterComponent_div_106_div_1_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "div");
    \u0275\u0275text(1, "La rue est requise");
    \u0275\u0275elementEnd();
  }
}
function RegisterComponent_div_106_div_2_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "div");
    \u0275\u0275text(1, "La rue doit contenir au moins 5 caract\xE8res");
    \u0275\u0275elementEnd();
  }
}
function RegisterComponent_div_106_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "div", 56);
    \u0275\u0275template(1, RegisterComponent_div_106_div_1_Template, 2, 0, "div", 57)(2, RegisterComponent_div_106_div_2_Template, 2, 0, "div", 57);
    \u0275\u0275elementEnd();
  }
  if (rf & 2) {
    const ctx_r0 = \u0275\u0275nextContext();
    \u0275\u0275advance();
    \u0275\u0275property("ngIf", ctx_r0.rue == null ? null : ctx_r0.rue.errors == null ? null : ctx_r0.rue.errors["required"]);
    \u0275\u0275advance();
    \u0275\u0275property("ngIf", ctx_r0.rue == null ? null : ctx_r0.rue.errors == null ? null : ctx_r0.rue.errors["minlength"]);
  }
}
function RegisterComponent_div_112_div_1_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "div");
    \u0275\u0275text(1, "Le code postal est requis");
    \u0275\u0275elementEnd();
  }
}
function RegisterComponent_div_112_div_2_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "div");
    \u0275\u0275text(1, "Le code postal doit contenir 4 chiffres");
    \u0275\u0275elementEnd();
  }
}
function RegisterComponent_div_112_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "div", 56);
    \u0275\u0275template(1, RegisterComponent_div_112_div_1_Template, 2, 0, "div", 57)(2, RegisterComponent_div_112_div_2_Template, 2, 0, "div", 57);
    \u0275\u0275elementEnd();
  }
  if (rf & 2) {
    const ctx_r0 = \u0275\u0275nextContext();
    \u0275\u0275advance();
    \u0275\u0275property("ngIf", ctx_r0.codePostal == null ? null : ctx_r0.codePostal.errors == null ? null : ctx_r0.codePostal.errors["required"]);
    \u0275\u0275advance();
    \u0275\u0275property("ngIf", ctx_r0.codePostal == null ? null : ctx_r0.codePostal.errors == null ? null : ctx_r0.codePostal.errors["pattern"]);
  }
}
function RegisterComponent_div_117_div_1_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "div");
    \u0275\u0275text(1, "La ville est requise");
    \u0275\u0275elementEnd();
  }
}
function RegisterComponent_div_117_div_2_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "div");
    \u0275\u0275text(1, "La ville doit contenir au moins 2 caract\xE8res");
    \u0275\u0275elementEnd();
  }
}
function RegisterComponent_div_117_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "div", 56);
    \u0275\u0275template(1, RegisterComponent_div_117_div_1_Template, 2, 0, "div", 57)(2, RegisterComponent_div_117_div_2_Template, 2, 0, "div", 57);
    \u0275\u0275elementEnd();
  }
  if (rf & 2) {
    const ctx_r0 = \u0275\u0275nextContext();
    \u0275\u0275advance();
    \u0275\u0275property("ngIf", ctx_r0.ville == null ? null : ctx_r0.ville.errors == null ? null : ctx_r0.ville.errors["required"]);
    \u0275\u0275advance();
    \u0275\u0275property("ngIf", ctx_r0.ville == null ? null : ctx_r0.ville.errors == null ? null : ctx_r0.ville.errors["minlength"]);
  }
}
function RegisterComponent_div_130_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "div", 58)(1, "div", 59);
    \u0275\u0275text(2);
    \u0275\u0275elementEnd()();
  }
  if (rf & 2) {
    const ctx_r0 = \u0275\u0275nextContext();
    \u0275\u0275advance(2);
    \u0275\u0275textInterpolate(ctx_r0.errorMessage);
  }
}
function RegisterComponent_div_131_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "div", 60);
    \u0275\u0275text(1);
    \u0275\u0275elementEnd();
  }
  if (rf & 2) {
    const ctx_r0 = \u0275\u0275nextContext();
    \u0275\u0275advance();
    \u0275\u0275textInterpolate1(" ", ctx_r0.successMessage, " ");
  }
}
function RegisterComponent_span_136_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275element(0, "span", 61);
  }
}
var RegisterComponent = class _RegisterComponent {
  formBuilder;
  authService;
  router;
  registerForm;
  isLoading = false;
  errorMessage = "";
  successMessage = "";
  constructor(formBuilder, authService, router) {
    this.formBuilder = formBuilder;
    this.authService = authService;
    this.router = router;
    this.registerForm = this.formBuilder.group({
      // Informations personnelles
      email: ["", [Validators.required, Validators.email]],
      password: ["", [Validators.required, Validators.minLength(8), Validators.maxLength(100)]],
      confirmPassword: ["", [Validators.required]],
      nom: ["", [Validators.required, Validators.minLength(2), Validators.maxLength(50)]],
      prenom: ["", [Validators.required, Validators.minLength(2), Validators.maxLength(50)]],
      phoneNumber: ["", [Validators.required, Validators.pattern(/^\d{8,15}$/)]],
      dateNaissance: ["", [Validators.required]],
      // Informations entreprise
      matriculeFiscale: ["", [Validators.required, Validators.pattern(/^\d{8}$/)]],
      raisonSociale: ["", [Validators.required, Validators.minLength(2), Validators.maxLength(200)]],
      description: [""],
      rib: ["", [Validators.required, Validators.pattern(/^\d{20}$/)]],
      codeBanque: ["", [Validators.required, Validators.pattern(/^\d{3}$/)]],
      commission: [0.75, [Validators.required, Validators.min(0.5), Validators.max(1)]],
      delaiPreparationJours: [2, [Validators.required, Validators.min(1), Validators.max(30)]],
      fraisLivraisonBase: [9.99, [Validators.required, Validators.min(0)]],
      // Adresse de l'entreprise (selon AdresseCreateDto backend)
      rue: ["", [Validators.required, Validators.minLength(5)]],
      ville: ["", [Validators.required, Validators.minLength(2)]],
      codePostal: ["", [Validators.required, Validators.pattern(/^\d{4}$/)]],
      pays: ["Tunisie", [Validators.required]],
      logoFile: [null]
      // Rendu optionnel car on ajoute un logo par défaut
    }, { validators: this.passwordMatchValidator });
  }
  passwordMatchValidator(form) {
    const password = form.get("password");
    const confirmPassword = form.get("confirmPassword");
    if (password && confirmPassword && password.value !== confirmPassword.value) {
      confirmPassword.setErrors({ passwordMismatch: true });
      return { passwordMismatch: true };
    }
    return null;
  }
  validateFormData(formValue) {
    if (!/^\d{8}$/.test(formValue.matriculeFiscale)) {
      this.errorMessage = "Le matricule fiscal doit contenir exactement 8 chiffres.";
      return false;
    }
    if (!/^\d{3}$/.test(formValue.codeBanque)) {
      this.errorMessage = "Le code banque doit contenir exactement 3 chiffres.";
      return false;
    }
    if (!/^\d{20}$/.test(formValue.rib)) {
      this.errorMessage = "Le RIB doit contenir exactement 20 chiffres.";
      return false;
    }
    if (!formValue.rib.startsWith(formValue.codeBanque)) {
      this.errorMessage = "Le RIB doit commencer par le code banque.";
      return false;
    }
    if (!/^\d{8,15}$/.test(formValue.phoneNumber)) {
      this.errorMessage = "Le num\xE9ro de t\xE9l\xE9phone doit contenir entre 8 et 15 chiffres.";
      return false;
    }
    return true;
  }
  getFieldDisplayName(fieldName) {
    const fieldNames = {
      "email": "Email",
      "password": "Mot de passe",
      "nom": "Nom",
      "prenom": "Pr\xE9nom",
      "phoneNumber": "T\xE9l\xE9phone",
      "dateNaissance": "Date de naissance",
      "matriculeFiscale": "Matricule fiscal",
      "raisonSociale": "Raison sociale",
      "rib": "RIB",
      "codeBanque": "Code banque",
      "description": "Description",
      "commission": "Commission",
      "delaiPreparationJours": "D\xE9lai de pr\xE9paration",
      "fraisLivraisonBase": "Frais de livraison",
      "logoFile": "Logo"
    };
    return fieldNames[fieldName] || fieldName;
  }
  onSubmit() {
    if (this.registerForm.valid) {
      this.isLoading = true;
      this.errorMessage = "";
      this.successMessage = "";
      const formValue = this.registerForm.value;
      if (!this.validateFormData(formValue)) {
        this.isLoading = false;
        return;
      }
      const formData = new FormData();
      formData.append("email", formValue.email);
      formData.append("password", formValue.password);
      formData.append("nom", formValue.nom);
      formData.append("prenom", formValue.prenom);
      formData.append("phoneNumber", formValue.phoneNumber);
      if (formValue.dateNaissance) {
        const date = new Date(formValue.dateNaissance);
        formData.append("dateNaissance", date.toISOString());
      }
      formData.append("matriculeFiscale", formValue.matriculeFiscale);
      formData.append("raisonSociale", formValue.raisonSociale);
      formData.append("rib", formValue.rib);
      formData.append("codeBanque", formValue.codeBanque);
      if (formValue.description) {
        formData.append("description", formValue.description);
      }
      formData.append("commission", formValue.commission?.toString() || "0.75");
      formData.append("delaiPreparationJours", formValue.delaiPreparationJours?.toString() || "2");
      formData.append("fraisLivraisonBase", formValue.fraisLivraisonBase?.toString() || "9.99");
      formData.append("estActif", "true");
      if (formValue.rue) {
        formData.append("adresseRue", formValue.rue);
      }
      if (formValue.ville) {
        formData.append("adresseVille", formValue.ville);
      }
      if (formValue.codePostal) {
        formData.append("adresseCodePostal", formValue.codePostal);
      }
      if (formValue.pays) {
        formData.append("adressePays", formValue.pays);
      }
      formData.append("adresseEstPrincipale", "true");
      if (formValue.logoFile) {
        formData.append("logoFile", formValue.logoFile);
      } else {
        const defaultLogoBlob = new Blob([
          new Uint8Array([
            137,
            80,
            78,
            71,
            13,
            10,
            26,
            10,
            0,
            0,
            0,
            13,
            73,
            72,
            68,
            82,
            0,
            0,
            0,
            1,
            0,
            0,
            0,
            1,
            8,
            6,
            0,
            0,
            0,
            31,
            21,
            196,
            137,
            0,
            0,
            0,
            10,
            73,
            68,
            65,
            84,
            120,
            156,
            99,
            0,
            1,
            0,
            0,
            5,
            0,
            1,
            13,
            10,
            45,
            180,
            0,
            0,
            0,
            0,
            73,
            69,
            78,
            68,
            174,
            66,
            96,
            130
          ])
        ], { type: "image/png" });
        const defaultLogoFile = new File([defaultLogoBlob], "default-logo.png", { type: "image/png" });
        formData.append("logoFile", defaultLogoFile);
        console.log("\u{1F4DD} Logo par d\xE9faut ajout\xE9 car aucun logo n'a \xE9t\xE9 s\xE9lectionn\xE9");
      }
      console.log("\u{1F4CB} FormData final:");
      for (let pair of formData.entries()) {
        const key = pair[0];
        const value = pair[1];
        if (value instanceof File) {
          console.log(`${key}: [File] ${value.name} (${value.size} bytes, ${value.type})`);
        } else {
          console.log(`${key}: ${value}`);
        }
      }
      this.authService.registerFournisseur(formData).subscribe({
        next: () => {
          this.isLoading = false;
          this.successMessage = "Inscription r\xE9ussie ! Vous pouvez maintenant vous connecter.";
          setTimeout(() => {
            this.router.navigate(["/login"]);
          }, 2e3);
        },
        error: (error) => {
          this.isLoading = false;
          if (error.status === 400) {
            console.error("\u274C Erreur 400 - D\xE9tails:", error.error);
            if (error.error && typeof error.error === "object") {
              if (error.error.errors) {
                const validationErrors = error.error.errors;
                let errorMessages = [];
                Object.keys(validationErrors).forEach((key) => {
                  const fieldErrors = validationErrors[key];
                  if (Array.isArray(fieldErrors)) {
                    errorMessages.push(`${this.getFieldDisplayName(key)}: ${fieldErrors.join(", ")}`);
                  } else if (typeof fieldErrors === "string") {
                    errorMessages.push(`${this.getFieldDisplayName(key)}: ${fieldErrors}`);
                  }
                });
                if (errorMessages.length > 0) {
                  this.errorMessage = "Erreurs de validation:\n" + errorMessages.join("\n");
                } else {
                  this.errorMessage = error.error.message || "Erreur de validation. V\xE9rifiez vos donn\xE9es.";
                }
              } else {
                const validationErrors = error.error;
                let errorMessages = [];
                Object.keys(validationErrors).forEach((key) => {
                  const fieldErrors = validationErrors[key];
                  if (Array.isArray(fieldErrors)) {
                    errorMessages.push(`${this.getFieldDisplayName(key)}: ${fieldErrors.join(", ")}`);
                  } else if (typeof fieldErrors === "string") {
                    errorMessages.push(`${this.getFieldDisplayName(key)}: ${fieldErrors}`);
                  }
                });
                if (errorMessages.length > 0) {
                  this.errorMessage = "Erreurs de validation:\n" + errorMessages.join("\n");
                } else {
                  this.errorMessage = error.error?.message || "Erreur de validation. V\xE9rifiez vos donn\xE9es.";
                }
              }
            } else {
              this.errorMessage = error.error?.message || "Erreur lors de l'inscription. Veuillez r\xE9essayer.";
            }
          } else {
            this.errorMessage = error.error?.message || "Erreur lors de l'inscription. Veuillez r\xE9essayer.";
          }
        }
      });
    } else {
      this.markFormGroupTouched();
    }
  }
  markFormGroupTouched() {
    Object.keys(this.registerForm.controls).forEach((key) => {
      const control = this.registerForm.get(key);
      control?.markAsTouched();
    });
  }
  goToLogin() {
    this.router.navigate(["/login"]);
  }
  onFileSelected(event) {
    const file = event.target.files[0];
    if (file) {
      this.registerForm.patchValue({
        logoFile: file
      });
    }
  }
  testBackendConnection() {
    console.log("\u{1F9EA} Test de connectivit\xE9 backend...");
    const formValue = this.registerForm.value;
    const testData = {
      email: formValue.email || "<EMAIL>",
      password: formValue.password || "Test123!",
      nom: formValue.nom || "TestNom",
      prenom: formValue.prenom || "TestPrenom",
      phoneNumber: formValue.phoneNumber || "1234567890",
      dateNaissance: formValue.dateNaissance || "2025-06-17T06:50:12.478Z",
      matriculeFiscale: formValue.matriculeFiscale || "12345678",
      raisonSociale: formValue.raisonSociale || "Test Company",
      rib: formValue.rib || "12312345678901234567",
      // RIB qui commence par le code banque 123
      codeBanque: formValue.codeBanque || "123",
      description: formValue.description || "Test description",
      commission: 0.75,
      delaiPreparationJours: 2,
      fraisLivraisonBase: 9.99,
      estActif: true
    };
    console.log("\u{1F9EA} Test data:", testData);
    console.log("\u{1F9EA} Test 1: FormData avec logo par d\xE9faut...");
    this.authService.registerFournisseurJSON(testData).subscribe({
      next: (response) => {
        console.log("\u2705 Test FormData r\xE9ussi:", response);
        alert("\u2705 Test FormData r\xE9ussi ! Le backend fonctionne.\nLe probl\xE8me vient probablement du fichier logo ou des donn\xE9es.");
        this.testWithRealFormData();
      },
      error: (error) => {
        console.error("\u274C Test FormData \xE9chou\xE9:", error);
        if (error.status === 415) {
          alert("\u274C Erreur 415 avec FormData.\nLe backend ne supporte peut-\xEAtre pas cet endpoint ou il manque des headers.");
        } else if (error.status === 400) {
          alert("\u2705 Le backend r\xE9pond (erreur 400).\nLe probl\xE8me vient probablement des donn\xE9es ou de la validation.");
        } else {
          alert(`\u274C Test FormData \xE9chou\xE9: ${error.status} - ${error.statusText}
Voir la console pour plus de d\xE9tails.`);
        }
      }
    });
  }
  testWithRealFormData() {
    console.log("\u{1F9EA} Test 2: Avec les vraies donn\xE9es du formulaire...");
    if (!this.registerForm.valid) {
      alert("\u274C Le formulaire n'est pas valide. Veuillez remplir tous les champs requis.");
      return;
    }
    const formValue = this.registerForm.value;
    const formData = new FormData();
    formData.append("Email", formValue.email);
    formData.append("Password", formValue.password);
    formData.append("Nom", formValue.nom);
    formData.append("Prenom", formValue.prenom);
    formData.append("Telephone", formValue.phoneNumber);
    if (formValue.dateNaissance) {
      formData.append("DateNaissance", formValue.dateNaissance);
    }
    formData.append("MatriculeFiscale", formValue.matriculeFiscale);
    formData.append("RaisonSociale", formValue.raisonSociale);
    formData.append("Rib", formValue.rib);
    formData.append("CodeBanque", formValue.codeBanque);
    if (formValue.description) {
      formData.append("Description", formValue.description);
    }
    formData.append("Commission", formValue.commission?.toString() || "0.75");
    formData.append("DelaiPreparationJours", formValue.delaiPreparationJours?.toString() || "2");
    formData.append("FraisLivraisonBase", formValue.fraisLivraisonBase?.toString() || "9.99");
    formData.append("EstActif", "true");
    if (formValue.logoFile) {
      formData.append("LogoFile", formValue.logoFile);
    } else {
      const defaultLogoBlob = new Blob([
        new Uint8Array([
          137,
          80,
          78,
          71,
          13,
          10,
          26,
          10,
          0,
          0,
          0,
          13,
          73,
          72,
          68,
          82,
          0,
          0,
          0,
          1,
          0,
          0,
          0,
          1,
          8,
          6,
          0,
          0,
          0,
          31,
          21,
          196,
          137,
          0,
          0,
          0,
          10,
          73,
          68,
          65,
          84,
          120,
          156,
          99,
          0,
          1,
          0,
          0,
          5,
          0,
          1,
          13,
          10,
          45,
          180,
          0,
          0,
          0,
          0,
          73,
          69,
          78,
          68,
          174,
          66,
          96,
          130
        ])
      ], { type: "image/png" });
      const defaultLogoFile = new File([defaultLogoBlob], "default-logo.png", { type: "image/png" });
      formData.append("LogoFile", defaultLogoFile);
    }
    console.log("\u{1F9EA} FormData avec vraies donn\xE9es:");
    for (let pair of formData.entries()) {
      const key = pair[0];
      const value = pair[1];
      if (value instanceof File) {
        console.log(`${key}: [File] ${value.name} (${value.size} bytes, ${value.type})`);
      } else {
        console.log(`${key}: ${value}`);
      }
    }
    this.authService.registerFournisseur(formData).subscribe({
      next: (response) => {
        console.log("\u2705 Test avec vraies donn\xE9es r\xE9ussi:", response);
        alert("\u2705 Test avec vraies donn\xE9es r\xE9ussi !\nL'inscription devrait fonctionner maintenant.");
      },
      error: (error) => {
        console.error("\u274C Test avec vraies donn\xE9es \xE9chou\xE9:", error);
        alert(`\u274C Test avec vraies donn\xE9es \xE9chou\xE9: ${error.status} - ${error.statusText}
Voir la console pour plus de d\xE9tails.`);
      }
    });
  }
  testMinimalData() {
    console.log("\u{1F9EA} Test avec donn\xE9es minimales...");
    this.authService.testEndpointWithMinimalData().subscribe({
      next: (response) => {
        console.log("\u2705 Test minimal r\xE9ussi:", response);
        alert("\u2705 Test minimal r\xE9ussi !\nLe backend fonctionne avec FormData et logo par d\xE9faut.");
      },
      error: (error) => {
        console.error("\u274C Test minimal \xE9chou\xE9:", error);
        alert(`\u274C Test minimal \xE9chou\xE9: ${error.status} - ${error.statusText}
Voir la console pour plus de d\xE9tails.`);
      }
    });
  }
  // Getters pour faciliter l'accès aux contrôles dans le template
  get email() {
    return this.registerForm.get("email");
  }
  get password() {
    return this.registerForm.get("password");
  }
  get confirmPassword() {
    return this.registerForm.get("confirmPassword");
  }
  get nom() {
    return this.registerForm.get("nom");
  }
  get prenom() {
    return this.registerForm.get("prenom");
  }
  get phoneNumber() {
    return this.registerForm.get("phoneNumber");
  }
  get dateNaissance() {
    return this.registerForm.get("dateNaissance");
  }
  get matriculeFiscale() {
    return this.registerForm.get("matriculeFiscale");
  }
  get raisonSociale() {
    return this.registerForm.get("raisonSociale");
  }
  get description() {
    return this.registerForm.get("description");
  }
  get rib() {
    return this.registerForm.get("rib");
  }
  get codeBanque() {
    return this.registerForm.get("codeBanque");
  }
  get commission() {
    return this.registerForm.get("commission");
  }
  get delaiPreparationJours() {
    return this.registerForm.get("delaiPreparationJours");
  }
  get fraisLivraisonBase() {
    return this.registerForm.get("fraisLivraisonBase");
  }
  // Getters pour les champs d'adresse (selon AdresseCreateDto backend)
  get rue() {
    return this.registerForm.get("rue");
  }
  get ville() {
    return this.registerForm.get("ville");
  }
  get codePostal() {
    return this.registerForm.get("codePostal");
  }
  get pays() {
    return this.registerForm.get("pays");
  }
  get logoFile() {
    return this.registerForm.get("logoFile");
  }
  static \u0275fac = function RegisterComponent_Factory(__ngFactoryType__) {
    return new (__ngFactoryType__ || _RegisterComponent)(\u0275\u0275directiveInject(FormBuilder), \u0275\u0275directiveInject(AuthService), \u0275\u0275directiveInject(Router));
  };
  static \u0275cmp = /* @__PURE__ */ \u0275\u0275defineComponent({ type: _RegisterComponent, selectors: [["app-register"]], decls: 138, vars: 62, consts: [[1, "register-container"], [1, "register-card"], [1, "register-header"], [1, "register-form", 3, "ngSubmit", "formGroup"], [1, "form-section"], [1, "form-row"], [1, "form-group"], ["for", "prenom"], ["type", "text", "id", "prenom", "formControlName", "prenom", "placeholder", "Votre pr\xE9nom", 1, "form-control"], ["class", "invalid-feedback", 4, "ngIf"], ["for", "nom"], ["type", "text", "id", "nom", "formControlName", "nom", "placeholder", "Votre nom", 1, "form-control"], ["for", "email"], ["type", "email", "id", "email", "formControlName", "email", "placeholder", "<EMAIL>", 1, "form-control"], ["for", "phoneNumber"], ["type", "tel", "id", "phoneNumber", "formControlName", "phoneNumber", "placeholder", "0123456789", 1, "form-control"], ["for", "dateNaissance"], ["type", "date", "id", "dateNaissance", "formControlName", "dateNaissance", 1, "form-control"], ["for", "password"], ["type", "password", "id", "password", "formControlName", "password", "placeholder", "Votre mot de passe", 1, "form-control"], ["for", "confirmPassword"], ["type", "password", "id", "confirmPassword", "formControlName", "confirmPassword", "placeholder", "Confirmez votre mot de passe", 1, "form-control"], ["for", "matriculeFiscale"], ["type", "text", "id", "matriculeFiscale", "formControlName", "matriculeFiscale", "placeholder", "12345678", "maxlength", "8", 1, "form-control"], ["for", "raisonSociale"], ["type", "text", "id", "raisonSociale", "formControlName", "raisonSociale", "placeholder", "Nom de votre entreprise", "maxlength", "200", 1, "form-control"], ["for", "description"], ["id", "description", "formControlName", "description", "placeholder", "Description de votre entreprise", "rows", "3", 1, "form-control"], ["for", "rib"], ["type", "text", "id", "rib", "formControlName", "rib", "placeholder", "12345678901234567890", "maxlength", "20", 1, "form-control"], ["for", "codeBanque"], ["type", "text", "id", "codeBanque", "formControlName", "codeBanque", "placeholder", "123", "maxlength", "3", 1, "form-control"], ["for", "commission"], ["type", "number", "id", "commission", "formControlName", "commission", "placeholder", "0.75", "min", "0.5", "max", "1", "step", "0.01", 1, "form-control"], [1, "form-text", "text-muted"], ["for", "delaiPreparationJours"], ["type", "number", "id", "delaiPreparationJours", "formControlName", "delaiPreparationJours", "placeholder", "2", "min", "1", "max", "30", 1, "form-control"], ["for", "fraisLivraisonBase"], ["type", "number", "id", "fraisLivraisonBase", "formControlName", "fraisLivraisonBase", "placeholder", "9.99", "min", "0", "step", "0.01", 1, "form-control"], [1, "section-title"], ["for", "rue"], ["type", "text", "id", "rue", "formControlName", "rue", "placeholder", "123 Rue de la R\xE9publique", 1, "form-control"], ["for", "codePostal"], ["type", "text", "id", "codePostal", "formControlName", "codePostal", "placeholder", "1000", "maxlength", "4", 1, "form-control"], ["for", "ville"], ["type", "text", "id", "ville", "formControlName", "ville", "placeholder", "Paris", 1, "form-control"], ["for", "pays"], ["type", "text", "id", "pays", "formControlName", "pays", "value", "Tunisie", "readonly", "", 1, "form-control", 2, "background-color", "#f8f9fa", "cursor", "not-allowed"], ["for", "logoFile"], ["type", "file", "id", "logoFile", "accept", "image/*", 1, "form-control", 3, "change"], ["class", "alert alert-danger", 4, "ngIf"], ["class", "alert alert-success", 4, "ngIf"], [1, "form-actions"], ["type", "button", 1, "btn", "btn-secondary", 3, "click", "disabled"], ["type", "submit", 1, "btn", "btn-primary", 3, "disabled"], ["class", "spinner", 4, "ngIf"], [1, "invalid-feedback"], [4, "ngIf"], [1, "alert", "alert-danger"], [2, "white-space", "pre-line"], [1, "alert", "alert-success"], [1, "spinner"]], template: function RegisterComponent_Template(rf, ctx) {
    if (rf & 1) {
      \u0275\u0275elementStart(0, "div", 0)(1, "div", 1)(2, "div", 2)(3, "h1");
      \u0275\u0275text(4, "Inscription Fournisseur");
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(5, "p");
      \u0275\u0275text(6, "Cr\xE9ez votre compte fournisseur");
      \u0275\u0275elementEnd()();
      \u0275\u0275elementStart(7, "form", 3);
      \u0275\u0275listener("ngSubmit", function RegisterComponent_Template_form_ngSubmit_7_listener() {
        return ctx.onSubmit();
      });
      \u0275\u0275elementStart(8, "div", 4)(9, "h3");
      \u0275\u0275text(10, "Informations personnelles");
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(11, "div", 5)(12, "div", 6)(13, "label", 7);
      \u0275\u0275text(14, "Pr\xE9nom *");
      \u0275\u0275elementEnd();
      \u0275\u0275element(15, "input", 8);
      \u0275\u0275template(16, RegisterComponent_div_16_Template, 3, 2, "div", 9);
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(17, "div", 6)(18, "label", 10);
      \u0275\u0275text(19, "Nom *");
      \u0275\u0275elementEnd();
      \u0275\u0275element(20, "input", 11);
      \u0275\u0275template(21, RegisterComponent_div_21_Template, 3, 2, "div", 9);
      \u0275\u0275elementEnd()();
      \u0275\u0275elementStart(22, "div", 5)(23, "div", 6)(24, "label", 12);
      \u0275\u0275text(25, "Email *");
      \u0275\u0275elementEnd();
      \u0275\u0275element(26, "input", 13);
      \u0275\u0275template(27, RegisterComponent_div_27_Template, 3, 2, "div", 9);
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(28, "div", 6)(29, "label", 14);
      \u0275\u0275text(30, "T\xE9l\xE9phone *");
      \u0275\u0275elementEnd();
      \u0275\u0275element(31, "input", 15);
      \u0275\u0275template(32, RegisterComponent_div_32_Template, 3, 2, "div", 9);
      \u0275\u0275elementEnd()();
      \u0275\u0275elementStart(33, "div", 6)(34, "label", 16);
      \u0275\u0275text(35, "Date de naissance *");
      \u0275\u0275elementEnd();
      \u0275\u0275element(36, "input", 17);
      \u0275\u0275template(37, RegisterComponent_div_37_Template, 2, 1, "div", 9);
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(38, "div", 5)(39, "div", 6)(40, "label", 18);
      \u0275\u0275text(41, "Mot de passe *");
      \u0275\u0275elementEnd();
      \u0275\u0275element(42, "input", 19);
      \u0275\u0275template(43, RegisterComponent_div_43_Template, 3, 2, "div", 9);
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(44, "div", 6)(45, "label", 20);
      \u0275\u0275text(46, "Confirmer le mot de passe *");
      \u0275\u0275elementEnd();
      \u0275\u0275element(47, "input", 21);
      \u0275\u0275template(48, RegisterComponent_div_48_Template, 3, 2, "div", 9);
      \u0275\u0275elementEnd()()();
      \u0275\u0275elementStart(49, "div", 4)(50, "h3");
      \u0275\u0275text(51, "Informations entreprise");
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(52, "div", 5)(53, "div", 6)(54, "label", 22);
      \u0275\u0275text(55, "Matricule Fiscale *");
      \u0275\u0275elementEnd();
      \u0275\u0275element(56, "input", 23);
      \u0275\u0275template(57, RegisterComponent_div_57_Template, 3, 2, "div", 9);
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(58, "div", 6)(59, "label", 24);
      \u0275\u0275text(60, "Raison Sociale *");
      \u0275\u0275elementEnd();
      \u0275\u0275element(61, "input", 25);
      \u0275\u0275template(62, RegisterComponent_div_62_Template, 4, 3, "div", 9);
      \u0275\u0275elementEnd()();
      \u0275\u0275elementStart(63, "div", 6)(64, "label", 26);
      \u0275\u0275text(65, "Description (optionnel)");
      \u0275\u0275elementEnd();
      \u0275\u0275element(66, "textarea", 27);
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(67, "div", 5)(68, "div", 6)(69, "label", 28);
      \u0275\u0275text(70, "RIB *");
      \u0275\u0275elementEnd();
      \u0275\u0275element(71, "input", 29);
      \u0275\u0275template(72, RegisterComponent_div_72_Template, 3, 2, "div", 9);
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(73, "div", 6)(74, "label", 30);
      \u0275\u0275text(75, "Code Banque *");
      \u0275\u0275elementEnd();
      \u0275\u0275element(76, "input", 31);
      \u0275\u0275template(77, RegisterComponent_div_77_Template, 3, 2, "div", 9);
      \u0275\u0275elementEnd()();
      \u0275\u0275elementStart(78, "div", 5)(79, "div", 6)(80, "label", 32);
      \u0275\u0275text(81, "Commission (%) *");
      \u0275\u0275elementEnd();
      \u0275\u0275element(82, "input", 33);
      \u0275\u0275template(83, RegisterComponent_div_83_Template, 4, 3, "div", 9);
      \u0275\u0275elementStart(84, "small", 34);
      \u0275\u0275text(85, "Commission sur les ventes (entre 50% et 100%)");
      \u0275\u0275elementEnd()();
      \u0275\u0275elementStart(86, "div", 6)(87, "label", 35);
      \u0275\u0275text(88, "D\xE9lai de pr\xE9paration (jours) *");
      \u0275\u0275elementEnd();
      \u0275\u0275element(89, "input", 36);
      \u0275\u0275template(90, RegisterComponent_div_90_Template, 3, 2, "div", 9);
      \u0275\u0275elementStart(91, "small", 34);
      \u0275\u0275text(92, "Nombre de jours pour pr\xE9parer une commande");
      \u0275\u0275elementEnd()()();
      \u0275\u0275elementStart(93, "div", 6)(94, "label", 37);
      \u0275\u0275text(95, "Frais de livraison de base (\u20AC) *");
      \u0275\u0275elementEnd();
      \u0275\u0275element(96, "input", 38);
      \u0275\u0275template(97, RegisterComponent_div_97_Template, 3, 2, "div", 9);
      \u0275\u0275elementStart(98, "small", 34);
      \u0275\u0275text(99, "Frais de livraison de base pour vos produits");
      \u0275\u0275elementEnd()();
      \u0275\u0275elementStart(100, "h3", 39);
      \u0275\u0275text(101, "\u{1F4CD} Adresse de l'entreprise");
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(102, "div", 6)(103, "label", 40);
      \u0275\u0275text(104, "Rue *");
      \u0275\u0275elementEnd();
      \u0275\u0275element(105, "input", 41);
      \u0275\u0275template(106, RegisterComponent_div_106_Template, 3, 2, "div", 9);
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(107, "div", 5)(108, "div", 6)(109, "label", 42);
      \u0275\u0275text(110, "Code postal *");
      \u0275\u0275elementEnd();
      \u0275\u0275element(111, "input", 43);
      \u0275\u0275template(112, RegisterComponent_div_112_Template, 3, 2, "div", 9);
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(113, "div", 6)(114, "label", 44);
      \u0275\u0275text(115, "Ville *");
      \u0275\u0275elementEnd();
      \u0275\u0275element(116, "input", 45);
      \u0275\u0275template(117, RegisterComponent_div_117_Template, 3, 2, "div", 9);
      \u0275\u0275elementEnd()();
      \u0275\u0275elementStart(118, "div", 6)(119, "label", 46);
      \u0275\u0275text(120, "Pays *");
      \u0275\u0275elementEnd();
      \u0275\u0275element(121, "input", 47);
      \u0275\u0275elementStart(122, "small", 34);
      \u0275\u0275text(123, "Le pays est automatiquement d\xE9fini sur Tunisie");
      \u0275\u0275elementEnd()();
      \u0275\u0275elementStart(124, "div", 6)(125, "label", 48);
      \u0275\u0275text(126, "Logo de l'entreprise (optionnel)");
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(127, "input", 49);
      \u0275\u0275listener("change", function RegisterComponent_Template_input_change_127_listener($event) {
        return ctx.onFileSelected($event);
      });
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(128, "small", 34);
      \u0275\u0275text(129, "Si aucun logo n'est s\xE9lectionn\xE9, un logo par d\xE9faut sera utilis\xE9.");
      \u0275\u0275elementEnd()()();
      \u0275\u0275template(130, RegisterComponent_div_130_Template, 3, 1, "div", 50)(131, RegisterComponent_div_131_Template, 2, 1, "div", 51);
      \u0275\u0275elementStart(132, "div", 52)(133, "button", 53);
      \u0275\u0275listener("click", function RegisterComponent_Template_button_click_133_listener() {
        return ctx.goToLogin();
      });
      \u0275\u0275text(134, " Retour \xE0 la connexion ");
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(135, "button", 54);
      \u0275\u0275template(136, RegisterComponent_span_136_Template, 1, 0, "span", 55);
      \u0275\u0275text(137);
      \u0275\u0275elementEnd()()()()();
    }
    if (rf & 2) {
      \u0275\u0275advance(7);
      \u0275\u0275property("formGroup", ctx.registerForm);
      \u0275\u0275advance(8);
      \u0275\u0275classProp("is-invalid", (ctx.prenom == null ? null : ctx.prenom.invalid) && (ctx.prenom == null ? null : ctx.prenom.touched));
      \u0275\u0275advance();
      \u0275\u0275property("ngIf", (ctx.prenom == null ? null : ctx.prenom.invalid) && (ctx.prenom == null ? null : ctx.prenom.touched));
      \u0275\u0275advance(4);
      \u0275\u0275classProp("is-invalid", (ctx.nom == null ? null : ctx.nom.invalid) && (ctx.nom == null ? null : ctx.nom.touched));
      \u0275\u0275advance();
      \u0275\u0275property("ngIf", (ctx.nom == null ? null : ctx.nom.invalid) && (ctx.nom == null ? null : ctx.nom.touched));
      \u0275\u0275advance(5);
      \u0275\u0275classProp("is-invalid", (ctx.email == null ? null : ctx.email.invalid) && (ctx.email == null ? null : ctx.email.touched));
      \u0275\u0275advance();
      \u0275\u0275property("ngIf", (ctx.email == null ? null : ctx.email.invalid) && (ctx.email == null ? null : ctx.email.touched));
      \u0275\u0275advance(4);
      \u0275\u0275classProp("is-invalid", (ctx.phoneNumber == null ? null : ctx.phoneNumber.invalid) && (ctx.phoneNumber == null ? null : ctx.phoneNumber.touched));
      \u0275\u0275advance();
      \u0275\u0275property("ngIf", (ctx.phoneNumber == null ? null : ctx.phoneNumber.invalid) && (ctx.phoneNumber == null ? null : ctx.phoneNumber.touched));
      \u0275\u0275advance(4);
      \u0275\u0275classProp("is-invalid", (ctx.dateNaissance == null ? null : ctx.dateNaissance.invalid) && (ctx.dateNaissance == null ? null : ctx.dateNaissance.touched));
      \u0275\u0275advance();
      \u0275\u0275property("ngIf", (ctx.dateNaissance == null ? null : ctx.dateNaissance.invalid) && (ctx.dateNaissance == null ? null : ctx.dateNaissance.touched));
      \u0275\u0275advance(5);
      \u0275\u0275classProp("is-invalid", (ctx.password == null ? null : ctx.password.invalid) && (ctx.password == null ? null : ctx.password.touched));
      \u0275\u0275advance();
      \u0275\u0275property("ngIf", (ctx.password == null ? null : ctx.password.invalid) && (ctx.password == null ? null : ctx.password.touched));
      \u0275\u0275advance(4);
      \u0275\u0275classProp("is-invalid", (ctx.confirmPassword == null ? null : ctx.confirmPassword.invalid) && (ctx.confirmPassword == null ? null : ctx.confirmPassword.touched));
      \u0275\u0275advance();
      \u0275\u0275property("ngIf", (ctx.confirmPassword == null ? null : ctx.confirmPassword.invalid) && (ctx.confirmPassword == null ? null : ctx.confirmPassword.touched));
      \u0275\u0275advance(8);
      \u0275\u0275classProp("is-invalid", (ctx.matriculeFiscale == null ? null : ctx.matriculeFiscale.invalid) && (ctx.matriculeFiscale == null ? null : ctx.matriculeFiscale.touched));
      \u0275\u0275advance();
      \u0275\u0275property("ngIf", (ctx.matriculeFiscale == null ? null : ctx.matriculeFiscale.invalid) && (ctx.matriculeFiscale == null ? null : ctx.matriculeFiscale.touched));
      \u0275\u0275advance(4);
      \u0275\u0275classProp("is-invalid", (ctx.raisonSociale == null ? null : ctx.raisonSociale.invalid) && (ctx.raisonSociale == null ? null : ctx.raisonSociale.touched));
      \u0275\u0275advance();
      \u0275\u0275property("ngIf", (ctx.raisonSociale == null ? null : ctx.raisonSociale.invalid) && (ctx.raisonSociale == null ? null : ctx.raisonSociale.touched));
      \u0275\u0275advance(4);
      \u0275\u0275classProp("is-invalid", (ctx.description == null ? null : ctx.description.invalid) && (ctx.description == null ? null : ctx.description.touched));
      \u0275\u0275advance(5);
      \u0275\u0275classProp("is-invalid", (ctx.rib == null ? null : ctx.rib.invalid) && (ctx.rib == null ? null : ctx.rib.touched));
      \u0275\u0275advance();
      \u0275\u0275property("ngIf", (ctx.rib == null ? null : ctx.rib.invalid) && (ctx.rib == null ? null : ctx.rib.touched));
      \u0275\u0275advance(4);
      \u0275\u0275classProp("is-invalid", (ctx.codeBanque == null ? null : ctx.codeBanque.invalid) && (ctx.codeBanque == null ? null : ctx.codeBanque.touched));
      \u0275\u0275advance();
      \u0275\u0275property("ngIf", (ctx.codeBanque == null ? null : ctx.codeBanque.invalid) && (ctx.codeBanque == null ? null : ctx.codeBanque.touched));
      \u0275\u0275advance(5);
      \u0275\u0275classProp("is-invalid", (ctx.commission == null ? null : ctx.commission.invalid) && (ctx.commission == null ? null : ctx.commission.touched));
      \u0275\u0275advance();
      \u0275\u0275property("ngIf", (ctx.commission == null ? null : ctx.commission.invalid) && (ctx.commission == null ? null : ctx.commission.touched));
      \u0275\u0275advance(6);
      \u0275\u0275classProp("is-invalid", (ctx.delaiPreparationJours == null ? null : ctx.delaiPreparationJours.invalid) && (ctx.delaiPreparationJours == null ? null : ctx.delaiPreparationJours.touched));
      \u0275\u0275advance();
      \u0275\u0275property("ngIf", (ctx.delaiPreparationJours == null ? null : ctx.delaiPreparationJours.invalid) && (ctx.delaiPreparationJours == null ? null : ctx.delaiPreparationJours.touched));
      \u0275\u0275advance(6);
      \u0275\u0275classProp("is-invalid", (ctx.fraisLivraisonBase == null ? null : ctx.fraisLivraisonBase.invalid) && (ctx.fraisLivraisonBase == null ? null : ctx.fraisLivraisonBase.touched));
      \u0275\u0275advance();
      \u0275\u0275property("ngIf", (ctx.fraisLivraisonBase == null ? null : ctx.fraisLivraisonBase.invalid) && (ctx.fraisLivraisonBase == null ? null : ctx.fraisLivraisonBase.touched));
      \u0275\u0275advance(8);
      \u0275\u0275classProp("is-invalid", (ctx.rue == null ? null : ctx.rue.invalid) && (ctx.rue == null ? null : ctx.rue.touched));
      \u0275\u0275advance();
      \u0275\u0275property("ngIf", (ctx.rue == null ? null : ctx.rue.invalid) && (ctx.rue == null ? null : ctx.rue.touched));
      \u0275\u0275advance(5);
      \u0275\u0275classProp("is-invalid", (ctx.codePostal == null ? null : ctx.codePostal.invalid) && (ctx.codePostal == null ? null : ctx.codePostal.touched));
      \u0275\u0275advance();
      \u0275\u0275property("ngIf", (ctx.codePostal == null ? null : ctx.codePostal.invalid) && (ctx.codePostal == null ? null : ctx.codePostal.touched));
      \u0275\u0275advance(4);
      \u0275\u0275classProp("is-invalid", (ctx.ville == null ? null : ctx.ville.invalid) && (ctx.ville == null ? null : ctx.ville.touched));
      \u0275\u0275advance();
      \u0275\u0275property("ngIf", (ctx.ville == null ? null : ctx.ville.invalid) && (ctx.ville == null ? null : ctx.ville.touched));
      \u0275\u0275advance(10);
      \u0275\u0275classProp("is-invalid", (ctx.logoFile == null ? null : ctx.logoFile.invalid) && (ctx.logoFile == null ? null : ctx.logoFile.touched));
      \u0275\u0275advance(3);
      \u0275\u0275property("ngIf", ctx.errorMessage);
      \u0275\u0275advance();
      \u0275\u0275property("ngIf", ctx.successMessage);
      \u0275\u0275advance(2);
      \u0275\u0275property("disabled", ctx.isLoading);
      \u0275\u0275advance(2);
      \u0275\u0275property("disabled", ctx.isLoading);
      \u0275\u0275advance();
      \u0275\u0275property("ngIf", ctx.isLoading);
      \u0275\u0275advance();
      \u0275\u0275textInterpolate1(" ", ctx.isLoading ? "Inscription..." : "S'inscrire", " ");
    }
  }, dependencies: [CommonModule, NgIf, ReactiveFormsModule, \u0275NgNoValidate, DefaultValueAccessor, NumberValueAccessor, NgControlStatus, NgControlStatusGroup, MaxLengthValidator, MinValidator, MaxValidator, FormGroupDirective, FormControlName], styles: ["\n\n.register-container[_ngcontent-%COMP%] {\n  min-height: 100vh;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  background:\n    linear-gradient(\n      135deg,\n      #667eea 0%,\n      #764ba2 100%);\n  padding: 20px;\n}\n.register-card[_ngcontent-%COMP%] {\n  background: white;\n  border-radius: 12px;\n  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);\n  padding: 40px;\n  width: 100%;\n  max-width: 800px;\n  max-height: 90vh;\n  overflow-y: auto;\n}\n.register-header[_ngcontent-%COMP%] {\n  text-align: center;\n  margin-bottom: 30px;\n}\n.register-header[_ngcontent-%COMP%]   h1[_ngcontent-%COMP%] {\n  color: #333;\n  font-size: 2rem;\n  font-weight: 600;\n  margin-bottom: 8px;\n}\n.register-header[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\n  color: #666;\n  font-size: 1rem;\n  margin: 0;\n}\n.register-form[_ngcontent-%COMP%] {\n  display: flex;\n  flex-direction: column;\n  gap: 30px;\n}\n.form-section[_ngcontent-%COMP%] {\n  border: 1px solid #e9ecef;\n  border-radius: 8px;\n  padding: 20px;\n  background-color: #f8f9fa;\n}\n.form-section[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%] {\n  color: #495057;\n  font-size: 1.2rem;\n  font-weight: 600;\n  margin-bottom: 20px;\n  padding-bottom: 10px;\n  border-bottom: 2px solid #dee2e6;\n}\n.form-row[_ngcontent-%COMP%] {\n  display: grid;\n  grid-template-columns: 1fr 1fr;\n  gap: 20px;\n  margin-bottom: 20px;\n}\n.form-group[_ngcontent-%COMP%] {\n  display: flex;\n  flex-direction: column;\n}\n.form-group[_ngcontent-%COMP%]:last-child {\n  margin-bottom: 0;\n}\n.form-group[_ngcontent-%COMP%]   label[_ngcontent-%COMP%] {\n  font-weight: 500;\n  color: #495057;\n  margin-bottom: 8px;\n  font-size: 0.9rem;\n}\n.form-control[_ngcontent-%COMP%] {\n  padding: 12px 16px;\n  border: 2px solid #e9ecef;\n  border-radius: 8px;\n  font-size: 1rem;\n  transition: all 0.3s ease;\n  background-color: white;\n}\n.form-control[_ngcontent-%COMP%]:focus {\n  outline: none;\n  border-color: #667eea;\n  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);\n}\n.form-control.is-invalid[_ngcontent-%COMP%] {\n  border-color: #dc3545;\n}\n.form-control.is-invalid[_ngcontent-%COMP%]:focus {\n  border-color: #dc3545;\n  box-shadow: 0 0 0 3px rgba(220, 53, 69, 0.1);\n}\n.invalid-feedback[_ngcontent-%COMP%] {\n  color: #dc3545;\n  font-size: 0.875rem;\n  margin-top: 5px;\n}\n.alert[_ngcontent-%COMP%] {\n  padding: 12px 16px;\n  border-radius: 8px;\n  margin-bottom: 20px;\n  font-size: 0.9rem;\n}\n.alert-danger[_ngcontent-%COMP%] {\n  background-color: #f8d7da;\n  border: 1px solid #f5c6cb;\n  color: #721c24;\n}\n.alert-success[_ngcontent-%COMP%] {\n  background-color: #d4edda;\n  border: 1px solid #c3e6cb;\n  color: #155724;\n}\n.form-actions[_ngcontent-%COMP%] {\n  display: flex;\n  gap: 15px;\n  justify-content: space-between;\n  margin-top: 20px;\n}\n.btn[_ngcontent-%COMP%] {\n  padding: 12px 24px;\n  border: none;\n  border-radius: 8px;\n  font-size: 1rem;\n  font-weight: 500;\n  cursor: pointer;\n  transition: all 0.3s ease;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  gap: 8px;\n  min-width: 140px;\n}\n.btn[_ngcontent-%COMP%]:disabled {\n  opacity: 0.6;\n  cursor: not-allowed;\n}\n.btn-primary[_ngcontent-%COMP%] {\n  background:\n    linear-gradient(\n      135deg,\n      #667eea 0%,\n      #764ba2 100%);\n  color: white;\n}\n.btn-primary[_ngcontent-%COMP%]:hover:not(:disabled) {\n  transform: translateY(-2px);\n  box-shadow: 0 8px 20px rgba(102, 126, 234, 0.3);\n}\n.btn-secondary[_ngcontent-%COMP%] {\n  background-color: #6c757d;\n  color: white;\n}\n.btn-secondary[_ngcontent-%COMP%]:hover:not(:disabled) {\n  background-color: #5a6268;\n  transform: translateY(-2px);\n}\n.spinner[_ngcontent-%COMP%] {\n  width: 16px;\n  height: 16px;\n  border: 2px solid transparent;\n  border-top: 2px solid currentColor;\n  border-radius: 50%;\n  animation: _ngcontent-%COMP%_spin 1s linear infinite;\n}\n@keyframes _ngcontent-%COMP%_spin {\n  0% {\n    transform: rotate(0deg);\n  }\n  100% {\n    transform: rotate(360deg);\n  }\n}\n@media (max-width: 768px) {\n  .register-container[_ngcontent-%COMP%] {\n    padding: 10px;\n  }\n  .register-card[_ngcontent-%COMP%] {\n    padding: 20px;\n    max-height: 95vh;\n  }\n  .form-row[_ngcontent-%COMP%] {\n    grid-template-columns: 1fr;\n    gap: 15px;\n  }\n  .form-actions[_ngcontent-%COMP%] {\n    flex-direction: column;\n  }\n  .register-header[_ngcontent-%COMP%]   h1[_ngcontent-%COMP%] {\n    font-size: 1.5rem;\n  }\n}\n@media (max-width: 480px) {\n  .register-card[_ngcontent-%COMP%] {\n    padding: 15px;\n  }\n  .form-section[_ngcontent-%COMP%] {\n    padding: 15px;\n  }\n}\n/*# sourceMappingURL=register.component.css.map */"] });
};
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && setClassMetadata(RegisterComponent, [{
    type: Component,
    args: [{ selector: "app-register", standalone: true, imports: [CommonModule, ReactiveFormsModule], template: `<div class="register-container">
  <div class="register-card">
    <div class="register-header">
      <h1>Inscription Fournisseur</h1>
      <p>Cr\xE9ez votre compte fournisseur</p>
    </div>

    <form [formGroup]="registerForm" (ngSubmit)="onSubmit()" class="register-form">
      
      <!-- Informations personnelles -->
      <div class="form-section">
        <h3>Informations personnelles</h3>
        
        <div class="form-row">
          <div class="form-group">
            <label for="prenom">Pr\xE9nom *</label>
            <input
              type="text"
              id="prenom"
              formControlName="prenom"
              class="form-control"
              [class.is-invalid]="prenom?.invalid && prenom?.touched"
              placeholder="Votre pr\xE9nom"
            />
            <div class="invalid-feedback" *ngIf="prenom?.invalid && prenom?.touched">
              <div *ngIf="prenom?.errors?.['required']">Le pr\xE9nom est requis</div>
              <div *ngIf="prenom?.errors?.['minlength']">Le pr\xE9nom doit contenir au moins 2 caract\xE8res</div>
            </div>
          </div>

          <div class="form-group">
            <label for="nom">Nom *</label>
            <input
              type="text"
              id="nom"
              formControlName="nom"
              class="form-control"
              [class.is-invalid]="nom?.invalid && nom?.touched"
              placeholder="Votre nom"
            />
            <div class="invalid-feedback" *ngIf="nom?.invalid && nom?.touched">
              <div *ngIf="nom?.errors?.['required']">Le nom est requis</div>
              <div *ngIf="nom?.errors?.['minlength']">Le nom doit contenir au moins 2 caract\xE8res</div>
            </div>
          </div>
        </div>

        <div class="form-row">
          <div class="form-group">
            <label for="email">Email *</label>
            <input
              type="email"
              id="email"
              formControlName="email"
              class="form-control"
              [class.is-invalid]="email?.invalid && email?.touched"
              placeholder="<EMAIL>"
            />
            <div class="invalid-feedback" *ngIf="email?.invalid && email?.touched">
              <div *ngIf="email?.errors?.['required']">L'email est requis</div>
              <div *ngIf="email?.errors?.['email']">Format d'email invalide</div>
            </div>
          </div>

          <div class="form-group">
            <label for="phoneNumber">T\xE9l\xE9phone *</label>
            <input
              type="tel"
              id="phoneNumber"
              formControlName="phoneNumber"
              class="form-control"
              [class.is-invalid]="phoneNumber?.invalid && phoneNumber?.touched"
              placeholder="0123456789"
            />
            <div class="invalid-feedback" *ngIf="phoneNumber?.invalid && phoneNumber?.touched">
              <div *ngIf="phoneNumber?.errors?.['required']">Le t\xE9l\xE9phone est requis</div>
              <div *ngIf="phoneNumber?.errors?.['pattern']">Le t\xE9l\xE9phone doit contenir entre 8 et 15 chiffres</div>
            </div>
          </div>
        </div>

        <div class="form-group">
          <label for="dateNaissance">Date de naissance *</label>
          <input
            type="date"
            id="dateNaissance"
            formControlName="dateNaissance"
            class="form-control"
            [class.is-invalid]="dateNaissance?.invalid && dateNaissance?.touched"
          />
          <div class="invalid-feedback" *ngIf="dateNaissance?.invalid && dateNaissance?.touched">
            <div *ngIf="dateNaissance?.errors?.['required']">La date de naissance est requise</div>
          </div>
        </div>

        <div class="form-row">
          <div class="form-group">
            <label for="password">Mot de passe *</label>
            <input
              type="password"
              id="password"
              formControlName="password"
              class="form-control"
              [class.is-invalid]="password?.invalid && password?.touched"
              placeholder="Votre mot de passe"
            />
            <div class="invalid-feedback" *ngIf="password?.invalid && password?.touched">
              <div *ngIf="password?.errors?.['required']">Le mot de passe est requis</div>
              <div *ngIf="password?.errors?.['minlength']">Le mot de passe doit contenir au moins 6 caract\xE8res</div>
            </div>
          </div>

          <div class="form-group">
            <label for="confirmPassword">Confirmer le mot de passe *</label>
            <input
              type="password"
              id="confirmPassword"
              formControlName="confirmPassword"
              class="form-control"
              [class.is-invalid]="confirmPassword?.invalid && confirmPassword?.touched"
              placeholder="Confirmez votre mot de passe"
            />
            <div class="invalid-feedback" *ngIf="confirmPassword?.invalid && confirmPassword?.touched">
              <div *ngIf="confirmPassword?.errors?.['required']">La confirmation est requise</div>
              <div *ngIf="confirmPassword?.errors?.['passwordMismatch']">Les mots de passe ne correspondent pas</div>
            </div>
          </div>
        </div>
      </div>

      <!-- Informations entreprise -->
      <div class="form-section">
        <h3>Informations entreprise</h3>
        
        <div class="form-row">
          <div class="form-group">
            <label for="matriculeFiscale">Matricule Fiscale *</label>
            <input
              type="text"
              id="matriculeFiscale"
              formControlName="matriculeFiscale"
              class="form-control"
              [class.is-invalid]="matriculeFiscale?.invalid && matriculeFiscale?.touched"
              placeholder="12345678"
              maxlength="8"
            />
            <div class="invalid-feedback" *ngIf="matriculeFiscale?.invalid && matriculeFiscale?.touched">
              <div *ngIf="matriculeFiscale?.errors?.['required']">Le matricule fiscale est requis</div>
              <div *ngIf="matriculeFiscale?.errors?.['minlength'] || matriculeFiscale?.errors?.['maxlength']">Le matricule fiscale doit contenir exactement 8 caract\xE8res</div>
            </div>
          </div>

          <div class="form-group">
            <label for="raisonSociale">Raison Sociale *</label>
            <input
              type="text"
              id="raisonSociale"
              formControlName="raisonSociale"
              class="form-control"
              [class.is-invalid]="raisonSociale?.invalid && raisonSociale?.touched"
              placeholder="Nom de votre entreprise"
              maxlength="200"
            />
            <div class="invalid-feedback" *ngIf="raisonSociale?.invalid && raisonSociale?.touched">
              <div *ngIf="raisonSociale?.errors?.['required']">La raison sociale est requise</div>
              <div *ngIf="raisonSociale?.errors?.['minlength']">La raison sociale doit contenir au moins 2 caract\xE8res</div>
              <div *ngIf="raisonSociale?.errors?.['maxlength']">La raison sociale ne peut pas d\xE9passer 200 caract\xE8res</div>
            </div>
          </div>
        </div>

        <div class="form-group">
          <label for="description">Description (optionnel)</label>
          <textarea
            id="description"
            formControlName="description"
            class="form-control"
            [class.is-invalid]="description?.invalid && description?.touched"
            placeholder="Description de votre entreprise"
            rows="3"
          ></textarea>
        </div>

        <div class="form-row">
          <div class="form-group">
            <label for="rib">RIB *</label>
            <input
              type="text"
              id="rib"
              formControlName="rib"
              class="form-control"
              [class.is-invalid]="rib?.invalid && rib?.touched"
              placeholder="12345678901234567890"
              maxlength="20"
            />
            <div class="invalid-feedback" *ngIf="rib?.invalid && rib?.touched">
              <div *ngIf="rib?.errors?.['required']">Le RIB est requis</div>
              <div *ngIf="rib?.errors?.['minlength'] || rib?.errors?.['maxlength']">Le RIB doit contenir exactement 20 caract\xE8res</div>
            </div>
          </div>

          <div class="form-group">
            <label for="codeBanque">Code Banque *</label>
            <input
              type="text"
              id="codeBanque"
              formControlName="codeBanque"
              class="form-control"
              [class.is-invalid]="codeBanque?.invalid && codeBanque?.touched"
              placeholder="123"
              maxlength="3"
            />
            <div class="invalid-feedback" *ngIf="codeBanque?.invalid && codeBanque?.touched">
              <div *ngIf="codeBanque?.errors?.['required']">Le code banque est requis</div>
              <div *ngIf="codeBanque?.errors?.['minlength'] || codeBanque?.errors?.['maxlength']">Le code banque doit contenir exactement 3 caract\xE8res</div>
            </div>
          </div>
        </div>

        <!-- Param\xE8tres commerciaux -->
        <div class="form-row">
          <div class="form-group">
            <label for="commission">Commission (%) *</label>
            <input
              type="number"
              id="commission"
              formControlName="commission"
              class="form-control"
              [class.is-invalid]="commission?.invalid && commission?.touched"
              placeholder="0.75"
              min="0.5"
              max="1"
              step="0.01"
            />
            <div class="invalid-feedback" *ngIf="commission?.invalid && commission?.touched">
              <div *ngIf="commission?.errors?.['required']">La commission est requise</div>
              <div *ngIf="commission?.errors?.['min']">La commission doit \xEAtre au minimum 50% (0.5)</div>
              <div *ngIf="commission?.errors?.['max']">La commission doit \xEAtre au maximum 100% (1.0)</div>
            </div>
            <small class="form-text text-muted">Commission sur les ventes (entre 50% et 100%)</small>
          </div>

          <div class="form-group">
            <label for="delaiPreparationJours">D\xE9lai de pr\xE9paration (jours) *</label>
            <input
              type="number"
              id="delaiPreparationJours"
              formControlName="delaiPreparationJours"
              class="form-control"
              [class.is-invalid]="delaiPreparationJours?.invalid && delaiPreparationJours?.touched"
              placeholder="2"
              min="1"
              max="30"
            />
            <div class="invalid-feedback" *ngIf="delaiPreparationJours?.invalid && delaiPreparationJours?.touched">
              <div *ngIf="delaiPreparationJours?.errors?.['required']">Le d\xE9lai de pr\xE9paration est requis</div>
              <div *ngIf="delaiPreparationJours?.errors?.['min']">Le d\xE9lai doit \xEAtre d'au moins 1 jour</div>
            </div>
            <small class="form-text text-muted">Nombre de jours pour pr\xE9parer une commande</small>
          </div>
        </div>

        <div class="form-group">
          <label for="fraisLivraisonBase">Frais de livraison de base (\u20AC) *</label>
          <input
            type="number"
            id="fraisLivraisonBase"
            formControlName="fraisLivraisonBase"
            class="form-control"
            [class.is-invalid]="fraisLivraisonBase?.invalid && fraisLivraisonBase?.touched"
            placeholder="9.99"
            min="0"
            step="0.01"
          />
          <div class="invalid-feedback" *ngIf="fraisLivraisonBase?.invalid && fraisLivraisonBase?.touched">
            <div *ngIf="fraisLivraisonBase?.errors?.['required']">Les frais de livraison sont requis</div>
            <div *ngIf="fraisLivraisonBase?.errors?.['min']">Les frais de livraison doivent \xEAtre positifs</div>
          </div>
          <small class="form-text text-muted">Frais de livraison de base pour vos produits</small>
        </div>

        <!-- Adresse de l'entreprise -->
        <h3 class="section-title">\u{1F4CD} Adresse de l'entreprise</h3>

        <div class="form-group">
          <label for="rue">Rue *</label>
          <input
            type="text"
            id="rue"
            formControlName="rue"
            class="form-control"
            [class.is-invalid]="rue?.invalid && rue?.touched"
            placeholder="123 Rue de la R\xE9publique"
          />
          <div class="invalid-feedback" *ngIf="rue?.invalid && rue?.touched">
            <div *ngIf="rue?.errors?.['required']">La rue est requise</div>
            <div *ngIf="rue?.errors?.['minlength']">La rue doit contenir au moins 5 caract\xE8res</div>
          </div>
        </div>

        <div class="form-row">
          <div class="form-group">
            <label for="codePostal">Code postal *</label>
            <input
              type="text"
              id="codePostal"
              formControlName="codePostal"
              class="form-control"
              [class.is-invalid]="codePostal?.invalid && codePostal?.touched"
              placeholder="1000"
              maxlength="4"
            />
            <div class="invalid-feedback" *ngIf="codePostal?.invalid && codePostal?.touched">
              <div *ngIf="codePostal?.errors?.['required']">Le code postal est requis</div>
              <div *ngIf="codePostal?.errors?.['pattern']">Le code postal doit contenir 4 chiffres</div>
            </div>
          </div>

          <div class="form-group">
            <label for="ville">Ville *</label>
            <input
              type="text"
              id="ville"
              formControlName="ville"
              class="form-control"
              [class.is-invalid]="ville?.invalid && ville?.touched"
              placeholder="Paris"
            />
            <div class="invalid-feedback" *ngIf="ville?.invalid && ville?.touched">
              <div *ngIf="ville?.errors?.['required']">La ville est requise</div>
              <div *ngIf="ville?.errors?.['minlength']">La ville doit contenir au moins 2 caract\xE8res</div>
            </div>
          </div>
        </div>

        <div class="form-group">
          <label for="pays">Pays *</label>
          <input
            type="text"
            id="pays"
            formControlName="pays"
            class="form-control"
            value="Tunisie"
            readonly
            style="background-color: #f8f9fa; cursor: not-allowed;"
          />
          <small class="form-text text-muted">Le pays est automatiquement d\xE9fini sur Tunisie</small>
        </div>

        <div class="form-group">
          <label for="logoFile">Logo de l'entreprise (optionnel)</label>
          <input
            type="file"
            id="logoFile"
            class="form-control"
            [class.is-invalid]="logoFile?.invalid && logoFile?.touched"
            accept="image/*"
            (change)="onFileSelected($event)"
          />
          <small class="form-text text-muted">Si aucun logo n'est s\xE9lectionn\xE9, un logo par d\xE9faut sera utilis\xE9.</small>
        </div>
      </div>

      <!-- Messages -->
      <div class="alert alert-danger" *ngIf="errorMessage">
        <div style="white-space: pre-line;">{{ errorMessage }}</div>
      </div>

      <div class="alert alert-success" *ngIf="successMessage">
        {{ successMessage }}
      </div>

      <!-- Boutons -->
      <div class="form-actions">
        <button
          type="button"
          class="btn btn-secondary"
          (click)="goToLogin()"
          [disabled]="isLoading"
        >
          Retour \xE0 la connexion
        </button>

        <button
          type="submit"
          class="btn btn-primary"
          [disabled]="isLoading"
        >
          <span *ngIf="isLoading" class="spinner"></span>
          {{ isLoading ? 'Inscription...' : 'S\\'inscrire' }}
        </button>
      </div>
    </form>
  </div>
</div>
`, styles: ["/* src/app/components/auth/register/register.component.css */\n.register-container {\n  min-height: 100vh;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  background:\n    linear-gradient(\n      135deg,\n      #667eea 0%,\n      #764ba2 100%);\n  padding: 20px;\n}\n.register-card {\n  background: white;\n  border-radius: 12px;\n  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);\n  padding: 40px;\n  width: 100%;\n  max-width: 800px;\n  max-height: 90vh;\n  overflow-y: auto;\n}\n.register-header {\n  text-align: center;\n  margin-bottom: 30px;\n}\n.register-header h1 {\n  color: #333;\n  font-size: 2rem;\n  font-weight: 600;\n  margin-bottom: 8px;\n}\n.register-header p {\n  color: #666;\n  font-size: 1rem;\n  margin: 0;\n}\n.register-form {\n  display: flex;\n  flex-direction: column;\n  gap: 30px;\n}\n.form-section {\n  border: 1px solid #e9ecef;\n  border-radius: 8px;\n  padding: 20px;\n  background-color: #f8f9fa;\n}\n.form-section h3 {\n  color: #495057;\n  font-size: 1.2rem;\n  font-weight: 600;\n  margin-bottom: 20px;\n  padding-bottom: 10px;\n  border-bottom: 2px solid #dee2e6;\n}\n.form-row {\n  display: grid;\n  grid-template-columns: 1fr 1fr;\n  gap: 20px;\n  margin-bottom: 20px;\n}\n.form-group {\n  display: flex;\n  flex-direction: column;\n}\n.form-group:last-child {\n  margin-bottom: 0;\n}\n.form-group label {\n  font-weight: 500;\n  color: #495057;\n  margin-bottom: 8px;\n  font-size: 0.9rem;\n}\n.form-control {\n  padding: 12px 16px;\n  border: 2px solid #e9ecef;\n  border-radius: 8px;\n  font-size: 1rem;\n  transition: all 0.3s ease;\n  background-color: white;\n}\n.form-control:focus {\n  outline: none;\n  border-color: #667eea;\n  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);\n}\n.form-control.is-invalid {\n  border-color: #dc3545;\n}\n.form-control.is-invalid:focus {\n  border-color: #dc3545;\n  box-shadow: 0 0 0 3px rgba(220, 53, 69, 0.1);\n}\n.invalid-feedback {\n  color: #dc3545;\n  font-size: 0.875rem;\n  margin-top: 5px;\n}\n.alert {\n  padding: 12px 16px;\n  border-radius: 8px;\n  margin-bottom: 20px;\n  font-size: 0.9rem;\n}\n.alert-danger {\n  background-color: #f8d7da;\n  border: 1px solid #f5c6cb;\n  color: #721c24;\n}\n.alert-success {\n  background-color: #d4edda;\n  border: 1px solid #c3e6cb;\n  color: #155724;\n}\n.form-actions {\n  display: flex;\n  gap: 15px;\n  justify-content: space-between;\n  margin-top: 20px;\n}\n.btn {\n  padding: 12px 24px;\n  border: none;\n  border-radius: 8px;\n  font-size: 1rem;\n  font-weight: 500;\n  cursor: pointer;\n  transition: all 0.3s ease;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  gap: 8px;\n  min-width: 140px;\n}\n.btn:disabled {\n  opacity: 0.6;\n  cursor: not-allowed;\n}\n.btn-primary {\n  background:\n    linear-gradient(\n      135deg,\n      #667eea 0%,\n      #764ba2 100%);\n  color: white;\n}\n.btn-primary:hover:not(:disabled) {\n  transform: translateY(-2px);\n  box-shadow: 0 8px 20px rgba(102, 126, 234, 0.3);\n}\n.btn-secondary {\n  background-color: #6c757d;\n  color: white;\n}\n.btn-secondary:hover:not(:disabled) {\n  background-color: #5a6268;\n  transform: translateY(-2px);\n}\n.spinner {\n  width: 16px;\n  height: 16px;\n  border: 2px solid transparent;\n  border-top: 2px solid currentColor;\n  border-radius: 50%;\n  animation: spin 1s linear infinite;\n}\n@keyframes spin {\n  0% {\n    transform: rotate(0deg);\n  }\n  100% {\n    transform: rotate(360deg);\n  }\n}\n@media (max-width: 768px) {\n  .register-container {\n    padding: 10px;\n  }\n  .register-card {\n    padding: 20px;\n    max-height: 95vh;\n  }\n  .form-row {\n    grid-template-columns: 1fr;\n    gap: 15px;\n  }\n  .form-actions {\n    flex-direction: column;\n  }\n  .register-header h1 {\n    font-size: 1.5rem;\n  }\n}\n@media (max-width: 480px) {\n  .register-card {\n    padding: 15px;\n  }\n  .form-section {\n    padding: 15px;\n  }\n}\n/*# sourceMappingURL=register.component.css.map */\n"] }]
  }], () => [{ type: FormBuilder }, { type: AuthService }, { type: Router }], null);
})();
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && \u0275setClassDebugInfo(RegisterComponent, { className: "RegisterComponent", filePath: "src/app/components/auth/register/register.component.ts", lineNumber: 43 });
})();
export {
  RegisterComponent
};
//# sourceMappingURL=chunk-MKVKK6BG.js.map

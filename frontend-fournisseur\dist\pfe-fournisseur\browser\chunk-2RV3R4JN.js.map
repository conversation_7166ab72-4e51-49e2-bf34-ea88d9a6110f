{"version": 3, "sources": ["src/app/models/admin.model.ts", "src/app/config/admin.config.ts", "src/app/services/admin-auth.service.ts"], "sourcesContent": ["export interface AdminUser {\n  id: number;\n  email: string;\n  nom: string;\n  prenom: string;\n  role: AdminRole;\n  permissions: Permission[];\n  isActive: boolean;\n  lastLogin?: Date;\n  createdAt: Date;\n  updatedAt: Date;\n  profileImage?: string;\n  phoneNumber?: string;\n  department?: string;\n  twoFactorEnabled: boolean;\n  sessionTimeout: number;\n}\n\nexport enum AdminRole {\n  ADMIN = 'ADMIN',\n  SUPER_ADMIN = 'SUPER_ADMIN',\n  MODERATOR = 'MODERATOR',\n  ANALYST = 'ANALYST'\n}\n\nexport interface Permission {\n  id: number;\n  name: string;\n  resource: string;\n  action: PermissionAction;\n  description: string;\n}\n\nexport enum PermissionAction {\n  CREATE = 'CREATE',\n  READ = 'READ',\n  UPDATE = 'UPDATE',\n  DELETE = 'DELETE',\n  APPROVE = 'APPROVE',\n  MODERATE = 'MODERATE',\n  EXPORT = 'EXPORT',\n  AUDIT = 'AUDIT'\n}\n\nexport interface LoginRequest {\n  email: string;\n  password: string;\n  rememberMe?: boolean;\n}\n\nexport interface OTPRequest {\n  email: string;\n  otpCode: string;\n  sessionToken: string;\n}\n\nexport interface LoginResponse {\n  success: boolean;\n  requiresOTP: boolean;\n  sessionToken?: string;\n  accessToken?: string;\n  refreshToken?: string;\n  user?: AdminUser;\n  expiresIn?: number;\n  message?: string;\n}\n\nexport interface AuditLog {\n  id: number;\n  userId: number;\n  userEmail: string;\n  action: string;\n  resource: string;\n  resourceId?: number;\n  oldValues?: any;\n  newValues?: any;\n  ipAddress: string;\n  userAgent: string;\n  timestamp: Date;\n  severity: AuditSeverity;\n  description: string;\n}\n\nexport enum AuditSeverity {\n  LOW = 'LOW',\n  MEDIUM = 'MEDIUM',\n  HIGH = 'HIGH',\n  CRITICAL = 'CRITICAL'\n}\n\nexport interface SystemAlert {\n  id: number;\n  type: AlertType;\n  title: string;\n  message: string;\n  severity: AlertSeverity;\n  isRead: boolean;\n  createdAt: Date;\n  expiresAt?: Date;\n  actionUrl?: string;\n  metadata?: any;\n}\n\nexport enum AlertType {\n  STOCK_CRITICAL = 'STOCK_CRITICAL',\n  PAYMENT_OVERDUE = 'PAYMENT_OVERDUE',\n  SECURITY_BREACH = 'SECURITY_BREACH',\n  SYSTEM_ERROR = 'SYSTEM_ERROR',\n  USER_ACTIVITY = 'USER_ACTIVITY',\n  PERFORMANCE = 'PERFORMANCE'\n}\n\nexport enum AlertSeverity {\n  INFO = 'INFO',\n  WARNING = 'WARNING',\n  ERROR = 'ERROR',\n  CRITICAL = 'CRITICAL'\n}\n\nexport interface DashboardStats {\n  totalRevenue: number;\n  totalOrders: number;\n  activeSuppliers: number;\n  pendingApprovals: number;\n  criticalAlerts: number;\n  systemHealth: number;\n  revenueGrowth: number;\n  orderGrowth: number;\n  supplierSatisfaction: number;\n  disputeRate: number;\n}\n\nexport interface CommissionRule {\n  id: number;\n  supplierId?: number;\n  categoryId?: number;\n  productId?: number;\n  commissionRate: number;\n  minAmount?: number;\n  maxAmount?: number;\n  isActive: boolean;\n  validFrom: Date;\n  validTo?: Date;\n  createdBy: number;\n  createdAt: Date;\n  description?: string;\n}\n\nexport interface PromotionCampaign {\n  id: number;\n  name: string;\n  description: string;\n  type: PromotionType;\n  discountValue: number;\n  discountType: DiscountType;\n  startDate: Date;\n  endDate: Date;\n  isActive: boolean;\n  targetAudience: string[];\n  conditions: PromotionCondition[];\n  usage: PromotionUsage;\n  createdBy: number;\n  createdAt: Date;\n}\n\nexport enum PromotionType {\n  PERCENTAGE = 'PERCENTAGE',\n  FIXED_AMOUNT = 'FIXED_AMOUNT',\n  FREE_SHIPPING = 'FREE_SHIPPING',\n  BUY_X_GET_Y = 'BUY_X_GET_Y'\n}\n\nexport enum DiscountType {\n  PERCENTAGE = 'PERCENTAGE',\n  FIXED = 'FIXED'\n}\n\nexport interface PromotionCondition {\n  type: string;\n  value: any;\n  operator: string;\n}\n\nexport interface PromotionUsage {\n  totalUses: number;\n  maxUses?: number;\n  usesPerCustomer?: number;\n  currentUses: number;\n}\n\nexport interface BatchOperation {\n  id: number;\n  type: BatchOperationType;\n  status: BatchStatus;\n  totalItems: number;\n  processedItems: number;\n  successItems: number;\n  failedItems: number;\n  startedAt: Date;\n  completedAt?: Date;\n  createdBy: number;\n  parameters: any;\n  results?: BatchResult[];\n  errorLog?: string[];\n}\n\nexport enum BatchOperationType {\n  PRICE_UPDATE = 'PRICE_UPDATE',\n  STOCK_UPDATE = 'STOCK_UPDATE',\n  CATEGORY_ASSIGNMENT = 'CATEGORY_ASSIGNMENT',\n  USER_IMPORT = 'USER_IMPORT',\n  PRODUCT_EXPORT = 'PRODUCT_EXPORT'\n}\n\nexport enum BatchStatus {\n  PENDING = 'PENDING',\n  RUNNING = 'RUNNING',\n  COMPLETED = 'COMPLETED',\n  FAILED = 'FAILED',\n  CANCELLED = 'CANCELLED'\n}\n\nexport interface BatchResult {\n  itemId: string;\n  status: 'SUCCESS' | 'FAILED';\n  message?: string;\n  oldValue?: any;\n  newValue?: any;\n}\n\nexport interface WorkflowStep {\n  id: number;\n  name: string;\n  description: string;\n  assignedRole: AdminRole;\n  requiredPermissions: string[];\n  isCompleted: boolean;\n  completedBy?: number;\n  completedAt?: Date;\n  comments?: string;\n  order: number;\n}\n\nexport interface WorkflowInstance {\n  id: number;\n  type: WorkflowType;\n  resourceId: number;\n  resourceType: string;\n  status: WorkflowStatus;\n  currentStep: number;\n  steps: WorkflowStep[];\n  initiatedBy: number;\n  initiatedAt: Date;\n  completedAt?: Date;\n  metadata?: any;\n}\n\nexport enum WorkflowType {\n  PRODUCT_APPROVAL = 'PRODUCT_APPROVAL',\n  SUPPLIER_ONBOARDING = 'SUPPLIER_ONBOARDING',\n  PRICE_CHANGE = 'PRICE_CHANGE',\n  CATEGORY_CREATION = 'CATEGORY_CREATION'\n}\n\nexport enum WorkflowStatus {\n  PENDING = 'PENDING',\n  IN_PROGRESS = 'IN_PROGRESS',\n  APPROVED = 'APPROVED',\n  REJECTED = 'REJECTED',\n  CANCELLED = 'CANCELLED'\n}\n", "export interface AdminCredentials {\n  username: string;\n  password: string;\n  role: string;\n  permissions: string[];\n}\n\nexport const ADMIN_CONFIG: AdminCredentials = {\n  username: 'adminOptiLet',\n  password: 'adminOptiLet!2025',\n  role: 'ADMIN',\n  permissions: [\n    'MANAGE_USERS',\n    'MANAGE_PRODUCTS',\n    'MANAGE_ORDERS',\n    'MANAGE_CATEGORIES',\n    'MANAGE_PROMOTIONS',\n    'VIEW_STATISTICS',\n    'MANAGE_SETTINGS',\n    'MODERATE_CONTENT',\n    'MANAGE_REFUNDS',\n    'SYSTEM_ADMIN'\n  ]\n};\n\nexport const ADMIN_USER_INFO = {\n  id: 1,\n  username: ADMIN_CONFIG.username,\n  email: '<EMAIL>',\n  nom: 'Administrateur',\n  prenom: 'OptiLet',\n  role: ADMIN_CONFIG.role,\n  permissions: ADMIN_CONFIG.permissions,\n  isActive: true,\n  lastLogin: new Date(),\n  createdAt: new Date('2024-01-01'),\n  updatedAt: new Date(),\n  profileImage: 'https://via.placeholder.com/150x150?text=ADMIN',\n  phoneNumber: '+33123456789',\n  department: 'Administration Système',\n  twoFactorEnabled: false, \n  sessionTimeout: 3600\n};\n", "/*  */import { Injectable } from '@angular/core';\nimport { BehaviorSubject, Observable, of, throwError } from 'rxjs';\nimport { delay, map, tap, catchError } from 'rxjs/operators';\nimport { HttpClient } from '@angular/common/http';\nimport { environment } from '../../environments/environment';\nimport { ADMIN_CONFIG, ADMIN_USER_INFO } from '../config/admin.config';\nimport { AdminUser, AdminRole, LoginRequest, OTPRequest, LoginResponse, Permission, PermissionAction } from '../models/admin.model';\n\n@Injectable({\n  providedIn: 'root'\n})\nexport class AdminAuthService {\n  private currentUserSubject = new BehaviorSubject<AdminUser | null>(null);\n  private isAuthenticatedSubject = new BehaviorSubject<boolean>(false);\n  private sessionTokenSubject = new BehaviorSubject<string | null>(null);\n  private apiUrl = environment.apiUrl;\n\n  public currentUser$ = this.currentUserSubject.asObservable();\n  public isAuthenticated$ = this.isAuthenticatedSubject.asObservable();\n\n  constructor(private http: HttpClient) {\n    // Vérifier si l'utilisateur est déjà connecté\n    this.checkStoredAuth();\n  }\n\n  /**\n   * Forcer la déconnexion (pour debug)\n   */\n  forceLogout(): void {\n    console.log('🔒 Déconnexion forcée admin');\n    this.clearAuthData();\n    this.currentUserSubject.next(null);\n    this.isAuthenticatedSubject.next(false);\n    this.sessionTokenSubject.next(null);\n  }\n\n  /**\n   * Connexion avec email et mot de passe\n   */\n  login(credentials: LoginRequest): Observable<LoginResponse> {\n    console.log('🔐 Tentative de connexion admin:', credentials.email);\n\n    // Utiliser l'API backend pour l'authentification\n    const loginData = {\n      email: credentials.email,\n      motDePasse: credentials.password\n    };\n\n    return this.http.post<any>(`${this.apiUrl}/Auth/login`, loginData).pipe(\n      map((response) => {\n        console.log('✅ Réponse API login:', response);\n\n        // Vérifier si l'utilisateur est admin\n        if (response.user && response.user.role === 'Admin') {\n          const adminUser: AdminUser = {\n            id: response.user.id,\n            email: response.user.email,\n            nom: response.user.nom || 'Admin',\n            prenom: response.user.prenom || 'OptiLet',\n            role: AdminRole.SUPER_ADMIN,\n            permissions: this.getAllPermissions(),\n            isActive: true,\n            lastLogin: new Date(),\n            createdAt: new Date(),\n            updatedAt: new Date(),\n            twoFactorEnabled: false,\n            sessionTimeout: 8 * 60 * 60 * 1000 // 8 heures\n          };\n\n          // Stocker le token JWT réel\n          const realToken = response.token;\n          this.storeAuthData(adminUser, realToken, realToken, realToken);\n\n          this.currentUserSubject.next(adminUser);\n          this.isAuthenticatedSubject.next(true);\n\n          return {\n            success: true,\n            requiresOTP: false,\n            accessToken: realToken,\n            refreshToken: realToken,\n            user: adminUser,\n            expiresIn: 8 * 60 * 60 * 1000,\n            message: 'Connexion admin réussie'\n          };\n        } else {\n          throw new Error('Accès refusé : droits administrateur requis');\n        }\n      }),\n      catchError((error) => {\n        console.error('❌ Erreur connexion admin:', error);\n        throw new Error(error.error?.Message || 'Erreur de connexion');\n      })\n    );\n  }\n\n  /**\n   * Vérification du code OTP\n   */\n  verifyOTP(otpRequest: OTPRequest): Observable<LoginResponse> {\n    return of(null).pipe(\n      delay(500),\n      map(() => {\n        // Simulation validation OTP (code: 123456)\n        if (otpRequest.otpCode !== '123456') {\n          throw new Error('Code OTP invalide');\n        }\n\n        const user = this.findUserByEmail(otpRequest.email);\n        if (!user) {\n          throw new Error('Utilisateur non trouvé');\n        }\n\n        return this.completeLogin(user, otpRequest.sessionToken);\n      })\n    );\n  }\n\n  /**\n   * Finaliser la connexion\n   */\n  private completeLogin(user: AdminUser, sessionToken: string): LoginResponse {\n    const accessToken = this.generateAccessToken();\n    const refreshToken = this.generateRefreshToken();\n\n    // Mettre à jour la dernière connexion\n    user.lastLogin = new Date();\n\n    // Stocker les informations de session\n    this.storeAuthData(user, accessToken, refreshToken, sessionToken);\n\n    // Mettre à jour les subjects\n    this.currentUserSubject.next(user);\n    this.isAuthenticatedSubject.next(true);\n\n    return {\n      success: true,\n      requiresOTP: false,\n      accessToken: accessToken,\n      refreshToken: refreshToken,\n      user: user,\n      expiresIn: user.sessionTimeout,\n      message: 'Connexion réussie'\n    };\n  }\n\n  /**\n   * Déconnexion\n   */\n  logout(): Observable<boolean> {\n    return of(true).pipe(\n      delay(300),\n      tap(() => {\n        this.clearAuthData();\n        this.currentUserSubject.next(null);\n        this.isAuthenticatedSubject.next(false);\n        this.sessionTokenSubject.next(null);\n      })\n    );\n  }\n\n  /**\n   * Rafraîchir le token\n   */\n  refreshToken(): Observable<string> {\n    const refreshToken = localStorage.getItem('admin_refresh_token');\n    if (!refreshToken) {\n      return throwError(() => new Error('Aucun refresh token'));\n    }\n\n    return of(this.generateAccessToken()).pipe(\n      delay(300),\n      tap(newToken => {\n        localStorage.setItem('admin_access_token', newToken);\n      })\n    );\n  }\n\n  /**\n   * Vérifier les permissions\n   */\n  hasPermission(resource: string, action: PermissionAction): boolean {\n    const user = this.currentUserSubject.value;\n    if (!user) return false;\n\n    return user.permissions.some(p => \n      p.resource === resource && p.action === action\n    );\n  }\n\n  /**\n   * Vérifier le rôle\n   */\n  hasRole(role: AdminRole): boolean {\n    const user = this.currentUserSubject.value;\n    return user?.role === role || false;\n  }\n\n  /**\n   * Vérifier si super admin\n   */\n  isSuperAdmin(): boolean {\n    return this.hasRole(AdminRole.SUPER_ADMIN);\n  }\n\n  /**\n   * Obtenir l'utilisateur actuel\n   */\n  getCurrentUser(): AdminUser | null {\n    return this.currentUserSubject.value;\n  }\n\n  /**\n   * Vérifier l'authentification stockée\n   */\n  private checkStoredAuth(): void {\n    const token = localStorage.getItem('admin_access_token');\n    const userStr = localStorage.getItem('admin_user');\n\n    console.log('🔍 Vérification auth stockée:', { hasToken: !!token, hasUser: !!userStr });\n\n    if (token && userStr) {\n      try {\n        const user = JSON.parse(userStr) as AdminUser;\n\n        // Vérifier si le token n'est pas expiré (simple vérification)\n        const tokenExpiry = localStorage.getItem('admin_token_expiry');\n        const now = Date.now();\n\n        if (tokenExpiry && now > parseInt(tokenExpiry)) {\n          console.log('🔒 Token admin expiré, nettoyage...');\n          this.clearAuthData();\n          return;\n        }\n\n        console.log('✅ Utilisateur admin restauré:', user.email);\n        this.currentUserSubject.next(user);\n        this.isAuthenticatedSubject.next(true);\n      } catch (error) {\n        console.log('❌ Erreur lors de la restauration auth admin:', error);\n        this.clearAuthData();\n      }\n    } else {\n      console.log('❌ Pas d\\'authentification admin stockée');\n      this.isAuthenticatedSubject.next(false);\n    }\n  }\n\n  /**\n   * Stocker les données d'authentification\n   */\n  private storeAuthData(user: AdminUser, accessToken: string, refreshToken: string, sessionToken: string): void {\n    // Définir une expiration de 8 heures\n    const expiryTime = Date.now() + (8 * 60 * 60 * 1000);\n\n    localStorage.setItem('admin_access_token', accessToken);\n    localStorage.setItem('admin_refresh_token', refreshToken);\n    localStorage.setItem('admin_session_token', sessionToken);\n    localStorage.setItem('admin_user', JSON.stringify(user));\n    localStorage.setItem('admin_token_expiry', expiryTime.toString());\n  }\n\n  /**\n   * Effacer les données d'authentification\n   */\n  private clearAuthData(): void {\n    localStorage.removeItem('admin_access_token');\n    localStorage.removeItem('admin_refresh_token');\n    localStorage.removeItem('admin_session_token');\n    localStorage.removeItem('admin_user');\n    localStorage.removeItem('admin_token_expiry');\n  }\n\n  /**\n   * Générer un token de session\n   */\n  private generateSessionToken(): string {\n    return 'session_' + Math.random().toString(36).substr(2, 9) + Date.now().toString(36);\n  }\n\n  /**\n   * Générer un access token\n   */\n  private generateAccessToken(): string {\n    return 'access_' + Math.random().toString(36).substr(2, 9) + Date.now().toString(36);\n  }\n\n  /**\n   * Générer un refresh token\n   */\n  private generateRefreshToken(): string {\n    return 'refresh_' + Math.random().toString(36).substr(2, 9) + Date.now().toString(36);\n  }\n\n  /**\n   * Obtenir toutes les permissions (Super Admin)\n   */\n  private getAllPermissions(): Permission[] {\n    return [\n      { id: 1, name: 'Créer utilisateur', resource: 'users', action: PermissionAction.CREATE, description: 'Créer de nouveaux utilisateurs' },\n      { id: 2, name: 'Lire utilisateur', resource: 'users', action: PermissionAction.READ, description: 'Consulter les utilisateurs' },\n      { id: 3, name: 'Modifier utilisateur', resource: 'users', action: PermissionAction.UPDATE, description: 'Modifier les utilisateurs' },\n      { id: 4, name: 'Supprimer utilisateur', resource: 'users', action: PermissionAction.DELETE, description: 'Supprimer les utilisateurs' },\n      { id: 5, name: 'Approuver produit', resource: 'products', action: PermissionAction.APPROVE, description: 'Approuver les produits' },\n      { id: 6, name: 'Lire produit', resource: 'products', action: PermissionAction.READ, description: 'Consulter les produits' },\n      { id: 7, name: 'Créer produit', resource: 'products', action: PermissionAction.CREATE, description: 'Créer des produits' },\n      { id: 8, name: 'Modifier produit', resource: 'products', action: PermissionAction.UPDATE, description: 'Modifier les produits' },\n      { id: 9, name: 'Supprimer produit', resource: 'products', action: PermissionAction.DELETE, description: 'Supprimer les produits' },\n      { id: 10, name: 'Modérer avis', resource: 'reviews', action: PermissionAction.MODERATE, description: 'Modérer les avis clients' },\n      { id: 11, name: 'Lire avis', resource: 'reviews', action: PermissionAction.READ, description: 'Consulter les avis clients' },\n      { id: 12, name: 'Créer promotion', resource: 'promotions', action: PermissionAction.CREATE, description: 'Créer des promotions' },\n      { id: 13, name: 'Approuver catégorie', resource: 'categories', action: PermissionAction.APPROVE, description: 'Approuver les demandes de catégories' },\n      { id: 14, name: 'Exporter données', resource: 'data', action: PermissionAction.EXPORT, description: 'Exporter les données' },\n      { id: 15, name: 'Consulter audit', resource: 'audit', action: PermissionAction.AUDIT, description: 'Consulter les logs d\\'audit' }\n    ];\n  }\n\n  /**\n   * Obtenir les permissions modérateur\n   */\n  private getModeratorPermissions(): Permission[] {\n    return [\n      { id: 2, name: 'Lire utilisateur', resource: 'users', action: PermissionAction.READ, description: 'Consulter les utilisateurs' },\n      { id: 5, name: 'Approuver produit', resource: 'products', action: PermissionAction.APPROVE, description: 'Approuver les produits' },\n      { id: 6, name: 'Lire produit', resource: 'products', action: PermissionAction.READ, description: 'Consulter les produits' },\n      { id: 10, name: 'Modérer avis', resource: 'reviews', action: PermissionAction.MODERATE, description: 'Modérer les avis clients' },\n      { id: 11, name: 'Lire avis', resource: 'reviews', action: PermissionAction.READ, description: 'Consulter les avis clients' },\n      { id: 14, name: 'Exporter données', resource: 'data', action: PermissionAction.EXPORT, description: 'Exporter les données' }\n    ];\n  }\n\n  /**\n   * Vérifier les credentials admin (username/password depuis la configuration)\n   */\n  private validateAdminCredentials(email: string, password: string): AdminUser | undefined {\n    // Vérifier si c'est l'admin OptiLet avec les credentials de configuration\n    // Accepter soit l'email soit le username pour la connexion\n    if ((email === ADMIN_CONFIG.username || email === ADMIN_USER_INFO.email) && password === ADMIN_CONFIG.password) {\n      return {\n        id: ADMIN_USER_INFO.id,\n        email: ADMIN_USER_INFO.email,\n        nom: ADMIN_USER_INFO.nom,\n        prenom: ADMIN_USER_INFO.prenom,\n        role: AdminRole.SUPER_ADMIN,\n        permissions: this.getAllPermissions(),\n        isActive: ADMIN_USER_INFO.isActive,\n        lastLogin: new Date(),\n        createdAt: ADMIN_USER_INFO.createdAt,\n        updatedAt: new Date(),\n        profileImage: ADMIN_USER_INFO.profileImage,\n        phoneNumber: ADMIN_USER_INFO.phoneNumber,\n        department: ADMIN_USER_INFO.department,\n        twoFactorEnabled: ADMIN_USER_INFO.twoFactorEnabled,\n        sessionTimeout: ADMIN_USER_INFO.sessionTimeout\n      };\n    }\n\n    return undefined;\n  }\n\n  /**\n   * Trouver un utilisateur par son email (pour compatibilité avec OTP)\n   */\n  private findUserByEmail(email: string): AdminUser | undefined {\n    // Pour l'admin OptiLet, on utilise l'email de configuration\n    if (email === ADMIN_USER_INFO.email) {\n      return {\n        id: ADMIN_USER_INFO.id,\n        email: ADMIN_USER_INFO.email,\n        nom: ADMIN_USER_INFO.nom,\n        prenom: ADMIN_USER_INFO.prenom,\n        role: AdminRole.SUPER_ADMIN,\n        permissions: this.getAllPermissions(),\n        isActive: ADMIN_USER_INFO.isActive,\n        lastLogin: new Date(),\n        createdAt: ADMIN_USER_INFO.createdAt,\n        updatedAt: new Date(),\n        profileImage: ADMIN_USER_INFO.profileImage,\n        phoneNumber: ADMIN_USER_INFO.phoneNumber,\n        department: ADMIN_USER_INFO.department,\n        twoFactorEnabled: ADMIN_USER_INFO.twoFactorEnabled,\n        sessionTimeout: ADMIN_USER_INFO.sessionTimeout\n      };\n    }\n\n    return undefined;\n  }\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;AAkBA,IAAY;CAAZ,SAAYA,YAAS;AACnB,EAAAA,WAAA,OAAA,IAAA;AACA,EAAAA,WAAA,aAAA,IAAA;AACA,EAAAA,WAAA,WAAA,IAAA;AACA,EAAAA,WAAA,SAAA,IAAA;AACF,GALY,cAAA,YAAS,CAAA,EAAA;AAerB,IAAY;CAAZ,SAAYC,mBAAgB;AAC1B,EAAAA,kBAAA,QAAA,IAAA;AACA,EAAAA,kBAAA,MAAA,IAAA;AACA,EAAAA,kBAAA,QAAA,IAAA;AACA,EAAAA,kBAAA,QAAA,IAAA;AACA,EAAAA,kBAAA,SAAA,IAAA;AACA,EAAAA,kBAAA,UAAA,IAAA;AACA,EAAAA,kBAAA,QAAA,IAAA;AACA,EAAAA,kBAAA,OAAA,IAAA;AACF,GATY,qBAAA,mBAAgB,CAAA,EAAA;AAkD5B,IAAY;CAAZ,SAAYC,gBAAa;AACvB,EAAAA,eAAA,KAAA,IAAA;AACA,EAAAA,eAAA,QAAA,IAAA;AACA,EAAAA,eAAA,MAAA,IAAA;AACA,EAAAA,eAAA,UAAA,IAAA;AACF,GALY,kBAAA,gBAAa,CAAA,EAAA;AAoBzB,IAAY;CAAZ,SAAYC,YAAS;AACnB,EAAAA,WAAA,gBAAA,IAAA;AACA,EAAAA,WAAA,iBAAA,IAAA;AACA,EAAAA,WAAA,iBAAA,IAAA;AACA,EAAAA,WAAA,cAAA,IAAA;AACA,EAAAA,WAAA,eAAA,IAAA;AACA,EAAAA,WAAA,aAAA,IAAA;AACF,GAPY,cAAA,YAAS,CAAA,EAAA;AASrB,IAAY;CAAZ,SAAYC,gBAAa;AACvB,EAAAA,eAAA,MAAA,IAAA;AACA,EAAAA,eAAA,SAAA,IAAA;AACA,EAAAA,eAAA,OAAA,IAAA;AACA,EAAAA,eAAA,UAAA,IAAA;AACF,GALY,kBAAA,gBAAa,CAAA,EAAA;AAqDzB,IAAY;CAAZ,SAAYC,gBAAa;AACvB,EAAAA,eAAA,YAAA,IAAA;AACA,EAAAA,eAAA,cAAA,IAAA;AACA,EAAAA,eAAA,eAAA,IAAA;AACA,EAAAA,eAAA,aAAA,IAAA;AACF,GALY,kBAAA,gBAAa,CAAA,EAAA;AAOzB,IAAY;CAAZ,SAAYC,eAAY;AACtB,EAAAA,cAAA,YAAA,IAAA;AACA,EAAAA,cAAA,OAAA,IAAA;AACF,GAHY,iBAAA,eAAY,CAAA,EAAA;AAkCxB,IAAY;CAAZ,SAAYC,qBAAkB;AAC5B,EAAAA,oBAAA,cAAA,IAAA;AACA,EAAAA,oBAAA,cAAA,IAAA;AACA,EAAAA,oBAAA,qBAAA,IAAA;AACA,EAAAA,oBAAA,aAAA,IAAA;AACA,EAAAA,oBAAA,gBAAA,IAAA;AACF,GANY,uBAAA,qBAAkB,CAAA,EAAA;AAQ9B,IAAY;CAAZ,SAAYC,cAAW;AACrB,EAAAA,aAAA,SAAA,IAAA;AACA,EAAAA,aAAA,SAAA,IAAA;AACA,EAAAA,aAAA,WAAA,IAAA;AACA,EAAAA,aAAA,QAAA,IAAA;AACA,EAAAA,aAAA,WAAA,IAAA;AACF,GANY,gBAAA,cAAW,CAAA,EAAA;AA2CvB,IAAY;CAAZ,SAAYC,eAAY;AACtB,EAAAA,cAAA,kBAAA,IAAA;AACA,EAAAA,cAAA,qBAAA,IAAA;AACA,EAAAA,cAAA,cAAA,IAAA;AACA,EAAAA,cAAA,mBAAA,IAAA;AACF,GALY,iBAAA,eAAY,CAAA,EAAA;AAOxB,IAAY;CAAZ,SAAYC,iBAAc;AACxB,EAAAA,gBAAA,SAAA,IAAA;AACA,EAAAA,gBAAA,aAAA,IAAA;AACA,EAAAA,gBAAA,UAAA,IAAA;AACA,EAAAA,gBAAA,UAAA,IAAA;AACA,EAAAA,gBAAA,WAAA,IAAA;AACF,GANY,mBAAA,iBAAc,CAAA,EAAA;;;ACjQnB,IAAM,eAAiC;EAC5C,UAAU;EACV,UAAU;EACV,MAAM;EACN,aAAa;IACX;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;;;AAIG,IAAM,kBAAkB;EAC7B,IAAI;EACJ,UAAU,aAAa;EACvB,OAAO;EACP,KAAK;EACL,QAAQ;EACR,MAAM,aAAa;EACnB,aAAa,aAAa;EAC1B,UAAU;EACV,WAAW,oBAAI,KAAI;EACnB,WAAW,oBAAI,KAAK,YAAY;EAChC,WAAW,oBAAI,KAAI;EACnB,cAAc;EACd,aAAa;EACb,YAAY;EACZ,kBAAkB;EAClB,gBAAgB;;;;AC9BZ,IAAO,mBAAP,MAAO,kBAAgB;EASP;EARZ,qBAAqB,IAAI,gBAAkC,IAAI;EAC/D,yBAAyB,IAAI,gBAAyB,KAAK;EAC3D,sBAAsB,IAAI,gBAA+B,IAAI;EAC7D,SAAS,YAAY;EAEtB,eAAe,KAAK,mBAAmB,aAAY;EACnD,mBAAmB,KAAK,uBAAuB,aAAY;EAElE,YAAoB,MAAgB;AAAhB,SAAA,OAAA;AAElB,SAAK,gBAAe;EACtB;;;;EAKA,cAAW;AACT,YAAQ,IAAI,0CAA6B;AACzC,SAAK,cAAa;AAClB,SAAK,mBAAmB,KAAK,IAAI;AACjC,SAAK,uBAAuB,KAAK,KAAK;AACtC,SAAK,oBAAoB,KAAK,IAAI;EACpC;;;;EAKA,MAAM,aAAyB;AAC7B,YAAQ,IAAI,2CAAoC,YAAY,KAAK;AAGjE,UAAM,YAAY;MAChB,OAAO,YAAY;MACnB,YAAY,YAAY;;AAG1B,WAAO,KAAK,KAAK,KAAU,GAAG,KAAK,MAAM,eAAe,SAAS,EAAE,KACjE,IAAI,CAAC,aAAY;AACf,cAAQ,IAAI,gCAAwB,QAAQ;AAG5C,UAAI,SAAS,QAAQ,SAAS,KAAK,SAAS,SAAS;AACnD,cAAM,YAAuB;UAC3B,IAAI,SAAS,KAAK;UAClB,OAAO,SAAS,KAAK;UACrB,KAAK,SAAS,KAAK,OAAO;UAC1B,QAAQ,SAAS,KAAK,UAAU;UAChC,MAAM,UAAU;UAChB,aAAa,KAAK,kBAAiB;UACnC,UAAU;UACV,WAAW,oBAAI,KAAI;UACnB,WAAW,oBAAI,KAAI;UACnB,WAAW,oBAAI,KAAI;UACnB,kBAAkB;UAClB,gBAAgB,IAAI,KAAK,KAAK;;;AAIhC,cAAM,YAAY,SAAS;AAC3B,aAAK,cAAc,WAAW,WAAW,WAAW,SAAS;AAE7D,aAAK,mBAAmB,KAAK,SAAS;AACtC,aAAK,uBAAuB,KAAK,IAAI;AAErC,eAAO;UACL,SAAS;UACT,aAAa;UACb,aAAa;UACb,cAAc;UACd,MAAM;UACN,WAAW,IAAI,KAAK,KAAK;UACzB,SAAS;;MAEb,OAAO;AACL,cAAM,IAAI,MAAM,mDAA6C;MAC/D;IACF,CAAC,GACD,WAAW,CAAC,UAAS;AACnB,cAAQ,MAAM,kCAA6B,KAAK;AAChD,YAAM,IAAI,MAAM,MAAM,OAAO,WAAW,qBAAqB;IAC/D,CAAC,CAAC;EAEN;;;;EAKA,UAAU,YAAsB;AAC9B,WAAO,GAAG,IAAI,EAAE,KACd,MAAM,GAAG,GACT,IAAI,MAAK;AAEP,UAAI,WAAW,YAAY,UAAU;AACnC,cAAM,IAAI,MAAM,mBAAmB;MACrC;AAEA,YAAM,OAAO,KAAK,gBAAgB,WAAW,KAAK;AAClD,UAAI,CAAC,MAAM;AACT,cAAM,IAAI,MAAM,2BAAwB;MAC1C;AAEA,aAAO,KAAK,cAAc,MAAM,WAAW,YAAY;IACzD,CAAC,CAAC;EAEN;;;;EAKQ,cAAc,MAAiB,cAAoB;AACzD,UAAM,cAAc,KAAK,oBAAmB;AAC5C,UAAM,eAAe,KAAK,qBAAoB;AAG9C,SAAK,YAAY,oBAAI,KAAI;AAGzB,SAAK,cAAc,MAAM,aAAa,cAAc,YAAY;AAGhE,SAAK,mBAAmB,KAAK,IAAI;AACjC,SAAK,uBAAuB,KAAK,IAAI;AAErC,WAAO;MACL,SAAS;MACT,aAAa;MACb;MACA;MACA;MACA,WAAW,KAAK;MAChB,SAAS;;EAEb;;;;EAKA,SAAM;AACJ,WAAO,GAAG,IAAI,EAAE,KACd,MAAM,GAAG,GACT,IAAI,MAAK;AACP,WAAK,cAAa;AAClB,WAAK,mBAAmB,KAAK,IAAI;AACjC,WAAK,uBAAuB,KAAK,KAAK;AACtC,WAAK,oBAAoB,KAAK,IAAI;IACpC,CAAC,CAAC;EAEN;;;;EAKA,eAAY;AACV,UAAM,eAAe,aAAa,QAAQ,qBAAqB;AAC/D,QAAI,CAAC,cAAc;AACjB,aAAO,WAAW,MAAM,IAAI,MAAM,qBAAqB,CAAC;IAC1D;AAEA,WAAO,GAAG,KAAK,oBAAmB,CAAE,EAAE,KACpC,MAAM,GAAG,GACT,IAAI,cAAW;AACb,mBAAa,QAAQ,sBAAsB,QAAQ;IACrD,CAAC,CAAC;EAEN;;;;EAKA,cAAc,UAAkB,QAAwB;AACtD,UAAM,OAAO,KAAK,mBAAmB;AACrC,QAAI,CAAC;AAAM,aAAO;AAElB,WAAO,KAAK,YAAY,KAAK,OAC3B,EAAE,aAAa,YAAY,EAAE,WAAW,MAAM;EAElD;;;;EAKA,QAAQ,MAAe;AACrB,UAAM,OAAO,KAAK,mBAAmB;AACrC,WAAO,MAAM,SAAS,QAAQ;EAChC;;;;EAKA,eAAY;AACV,WAAO,KAAK,QAAQ,UAAU,WAAW;EAC3C;;;;EAKA,iBAAc;AACZ,WAAO,KAAK,mBAAmB;EACjC;;;;EAKQ,kBAAe;AACrB,UAAM,QAAQ,aAAa,QAAQ,oBAAoB;AACvD,UAAM,UAAU,aAAa,QAAQ,YAAY;AAEjD,YAAQ,IAAI,8CAAiC,EAAE,UAAU,CAAC,CAAC,OAAO,SAAS,CAAC,CAAC,QAAO,CAAE;AAEtF,QAAI,SAAS,SAAS;AACpB,UAAI;AACF,cAAM,OAAO,KAAK,MAAM,OAAO;AAG/B,cAAM,cAAc,aAAa,QAAQ,oBAAoB;AAC7D,cAAM,MAAM,KAAK,IAAG;AAEpB,YAAI,eAAe,MAAM,SAAS,WAAW,GAAG;AAC9C,kBAAQ,IAAI,+CAAqC;AACjD,eAAK,cAAa;AAClB;QACF;AAEA,gBAAQ,IAAI,yCAAiC,KAAK,KAAK;AACvD,aAAK,mBAAmB,KAAK,IAAI;AACjC,aAAK,uBAAuB,KAAK,IAAI;MACvC,SAAS,OAAO;AACd,gBAAQ,IAAI,qDAAgD,KAAK;AACjE,aAAK,cAAa;MACpB;IACF,OAAO;AACL,cAAQ,IAAI,gDAAyC;AACrD,WAAK,uBAAuB,KAAK,KAAK;IACxC;EACF;;;;EAKQ,cAAc,MAAiB,aAAqB,cAAsB,cAAoB;AAEpG,UAAM,aAAa,KAAK,IAAG,IAAM,IAAI,KAAK,KAAK;AAE/C,iBAAa,QAAQ,sBAAsB,WAAW;AACtD,iBAAa,QAAQ,uBAAuB,YAAY;AACxD,iBAAa,QAAQ,uBAAuB,YAAY;AACxD,iBAAa,QAAQ,cAAc,KAAK,UAAU,IAAI,CAAC;AACvD,iBAAa,QAAQ,sBAAsB,WAAW,SAAQ,CAAE;EAClE;;;;EAKQ,gBAAa;AACnB,iBAAa,WAAW,oBAAoB;AAC5C,iBAAa,WAAW,qBAAqB;AAC7C,iBAAa,WAAW,qBAAqB;AAC7C,iBAAa,WAAW,YAAY;AACpC,iBAAa,WAAW,oBAAoB;EAC9C;;;;EAKQ,uBAAoB;AAC1B,WAAO,aAAa,KAAK,OAAM,EAAG,SAAS,EAAE,EAAE,OAAO,GAAG,CAAC,IAAI,KAAK,IAAG,EAAG,SAAS,EAAE;EACtF;;;;EAKQ,sBAAmB;AACzB,WAAO,YAAY,KAAK,OAAM,EAAG,SAAS,EAAE,EAAE,OAAO,GAAG,CAAC,IAAI,KAAK,IAAG,EAAG,SAAS,EAAE;EACrF;;;;EAKQ,uBAAoB;AAC1B,WAAO,aAAa,KAAK,OAAM,EAAG,SAAS,EAAE,EAAE,OAAO,GAAG,CAAC,IAAI,KAAK,IAAG,EAAG,SAAS,EAAE;EACtF;;;;EAKQ,oBAAiB;AACvB,WAAO;MACL,EAAE,IAAI,GAAG,MAAM,wBAAqB,UAAU,SAAS,QAAQ,iBAAiB,QAAQ,aAAa,oCAAgC;MACrI,EAAE,IAAI,GAAG,MAAM,oBAAoB,UAAU,SAAS,QAAQ,iBAAiB,MAAM,aAAa,6BAA4B;MAC9H,EAAE,IAAI,GAAG,MAAM,wBAAwB,UAAU,SAAS,QAAQ,iBAAiB,QAAQ,aAAa,4BAA2B;MACnI,EAAE,IAAI,GAAG,MAAM,yBAAyB,UAAU,SAAS,QAAQ,iBAAiB,QAAQ,aAAa,6BAA4B;MACrI,EAAE,IAAI,GAAG,MAAM,qBAAqB,UAAU,YAAY,QAAQ,iBAAiB,SAAS,aAAa,yBAAwB;MACjI,EAAE,IAAI,GAAG,MAAM,gBAAgB,UAAU,YAAY,QAAQ,iBAAiB,MAAM,aAAa,yBAAwB;MACzH,EAAE,IAAI,GAAG,MAAM,oBAAiB,UAAU,YAAY,QAAQ,iBAAiB,QAAQ,aAAa,wBAAoB;MACxH,EAAE,IAAI,GAAG,MAAM,oBAAoB,UAAU,YAAY,QAAQ,iBAAiB,QAAQ,aAAa,wBAAuB;MAC9H,EAAE,IAAI,GAAG,MAAM,qBAAqB,UAAU,YAAY,QAAQ,iBAAiB,QAAQ,aAAa,yBAAwB;MAChI,EAAE,IAAI,IAAI,MAAM,mBAAgB,UAAU,WAAW,QAAQ,iBAAiB,UAAU,aAAa,8BAA0B;MAC/H,EAAE,IAAI,IAAI,MAAM,aAAa,UAAU,WAAW,QAAQ,iBAAiB,MAAM,aAAa,6BAA4B;MAC1H,EAAE,IAAI,IAAI,MAAM,sBAAmB,UAAU,cAAc,QAAQ,iBAAiB,QAAQ,aAAa,0BAAsB;MAC/H,EAAE,IAAI,IAAI,MAAM,0BAAuB,UAAU,cAAc,QAAQ,iBAAiB,SAAS,aAAa,0CAAsC;MACpJ,EAAE,IAAI,IAAI,MAAM,uBAAoB,UAAU,QAAQ,QAAQ,iBAAiB,QAAQ,aAAa,0BAAsB;MAC1H,EAAE,IAAI,IAAI,MAAM,mBAAmB,UAAU,SAAS,QAAQ,iBAAiB,OAAO,aAAa,6BAA6B;;EAEpI;;;;EAKQ,0BAAuB;AAC7B,WAAO;MACL,EAAE,IAAI,GAAG,MAAM,oBAAoB,UAAU,SAAS,QAAQ,iBAAiB,MAAM,aAAa,6BAA4B;MAC9H,EAAE,IAAI,GAAG,MAAM,qBAAqB,UAAU,YAAY,QAAQ,iBAAiB,SAAS,aAAa,yBAAwB;MACjI,EAAE,IAAI,GAAG,MAAM,gBAAgB,UAAU,YAAY,QAAQ,iBAAiB,MAAM,aAAa,yBAAwB;MACzH,EAAE,IAAI,IAAI,MAAM,mBAAgB,UAAU,WAAW,QAAQ,iBAAiB,UAAU,aAAa,8BAA0B;MAC/H,EAAE,IAAI,IAAI,MAAM,aAAa,UAAU,WAAW,QAAQ,iBAAiB,MAAM,aAAa,6BAA4B;MAC1H,EAAE,IAAI,IAAI,MAAM,uBAAoB,UAAU,QAAQ,QAAQ,iBAAiB,QAAQ,aAAa,0BAAsB;;EAE9H;;;;EAKQ,yBAAyB,OAAe,UAAgB;AAG9D,SAAK,UAAU,aAAa,YAAY,UAAU,gBAAgB,UAAU,aAAa,aAAa,UAAU;AAC9G,aAAO;QACL,IAAI,gBAAgB;QACpB,OAAO,gBAAgB;QACvB,KAAK,gBAAgB;QACrB,QAAQ,gBAAgB;QACxB,MAAM,UAAU;QAChB,aAAa,KAAK,kBAAiB;QACnC,UAAU,gBAAgB;QAC1B,WAAW,oBAAI,KAAI;QACnB,WAAW,gBAAgB;QAC3B,WAAW,oBAAI,KAAI;QACnB,cAAc,gBAAgB;QAC9B,aAAa,gBAAgB;QAC7B,YAAY,gBAAgB;QAC5B,kBAAkB,gBAAgB;QAClC,gBAAgB,gBAAgB;;IAEpC;AAEA,WAAO;EACT;;;;EAKQ,gBAAgB,OAAa;AAEnC,QAAI,UAAU,gBAAgB,OAAO;AACnC,aAAO;QACL,IAAI,gBAAgB;QACpB,OAAO,gBAAgB;QACvB,KAAK,gBAAgB;QACrB,QAAQ,gBAAgB;QACxB,MAAM,UAAU;QAChB,aAAa,KAAK,kBAAiB;QACnC,UAAU,gBAAgB;QAC1B,WAAW,oBAAI,KAAI;QACnB,WAAW,gBAAgB;QAC3B,WAAW,oBAAI,KAAI;QACnB,cAAc,gBAAgB;QAC9B,aAAa,gBAAgB;QAC7B,YAAY,gBAAgB;QAC5B,kBAAkB,gBAAgB;QAClC,gBAAgB,gBAAgB;;IAEpC;AAEA,WAAO;EACT;;qCAvXW,mBAAgB,mBAAA,UAAA,CAAA;EAAA;4EAAhB,mBAAgB,SAAhB,kBAAgB,WAAA,YAFf,OAAM,CAAA;;;sEAEP,kBAAgB,CAAA;UAH5B;WAAW;MACV,YAAY;KACb;;;", "names": ["AdminRole", "PermissionAction", "AuditSeverity", "AlertType", "Alert<PERSON>everity", "PromotionType", "DiscountType", "BatchOperationType", "BatchStatus", "WorkflowType", "WorkflowStatus"]}
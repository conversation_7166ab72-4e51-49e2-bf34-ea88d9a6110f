import {
  AdminAuthService,
  AdminRole,
  PermissionAction
} from "./chunk-2RV3R4JN.js";
import {
  Router
} from "./chunk-6BVUYNW4.js";
import "./chunk-GFHHPDQ6.js";
import "./chunk-7JDDWGD3.js";
import {
  Injectable,
  map,
  setClassMetadata,
  take,
  ɵɵdefineInjectable,
  ɵɵinject
} from "./chunk-UBZQS7JS.js";

// src/app/guards/admin-auth.guard.ts
var AdminAuthGuard = class _AdminAuthGuard {
  adminAuthService;
  router;
  constructor(adminAuthService, router) {
    this.adminAuthService = adminAuthService;
    this.router = router;
  }
  canActivate(route, state) {
    return this.checkAuth(state.url);
  }
  canActivateChild(childRoute, state) {
    return this.checkAuth(state.url);
  }
  checkAuth(url) {
    return this.adminAuthService.isAuthenticated$.pipe(take(1), map((isAuthenticated) => {
      if (isAuthenticated) {
        return true;
      } else {
        localStorage.setItem("admin_redirect_url", url);
        this.router.navigate(["/adminOptiLet"]);
        return false;
      }
    }));
  }
  static \u0275fac = function AdminAuthGuard_Factory(__ngFactoryType__) {
    return new (__ngFactoryType__ || _AdminAuthGuard)(\u0275\u0275inject(AdminAuthService), \u0275\u0275inject(Router));
  };
  static \u0275prov = /* @__PURE__ */ \u0275\u0275defineInjectable({ token: _AdminAuthGuard, factory: _AdminAuthGuard.\u0275fac, providedIn: "root" });
};
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && setClassMetadata(AdminAuthGuard, [{
    type: Injectable,
    args: [{
      providedIn: "root"
    }]
  }], () => [{ type: AdminAuthService }, { type: Router }], null);
})();

// src/app/guards/super-admin.guard.ts
var SuperAdminGuard = class _SuperAdminGuard {
  adminAuthService;
  router;
  constructor(adminAuthService, router) {
    this.adminAuthService = adminAuthService;
    this.router = router;
  }
  canActivate(route, state) {
    return this.adminAuthService.currentUser$.pipe(take(1), map((user) => {
      if (user && user.role === AdminRole.SUPER_ADMIN) {
        return true;
      } else {
        this.router.navigate(["/admin/dashboard"], {
          queryParams: { error: "access_denied", message: "Acc\xE8s r\xE9serv\xE9 aux Super Administrateurs" }
        });
        return false;
      }
    }));
  }
  static \u0275fac = function SuperAdminGuard_Factory(__ngFactoryType__) {
    return new (__ngFactoryType__ || _SuperAdminGuard)(\u0275\u0275inject(AdminAuthService), \u0275\u0275inject(Router));
  };
  static \u0275prov = /* @__PURE__ */ \u0275\u0275defineInjectable({ token: _SuperAdminGuard, factory: _SuperAdminGuard.\u0275fac, providedIn: "root" });
};
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && setClassMetadata(SuperAdminGuard, [{
    type: Injectable,
    args: [{
      providedIn: "root"
    }]
  }], () => [{ type: AdminAuthService }, { type: Router }], null);
})();

// src/app/guards/role.guard.ts
var RoleGuard = class _RoleGuard {
  adminAuthService;
  router;
  constructor(adminAuthService, router) {
    this.adminAuthService = adminAuthService;
    this.router = router;
  }
  canActivate(route, state) {
    return this.adminAuthService.currentUser$.pipe(take(1), map((user) => {
      if (!user) {
        this.router.navigate(["/admin/login"]);
        return false;
      }
      const requiredRoles = route.data["roles"];
      if (requiredRoles && requiredRoles.length > 0) {
        const hasRole = requiredRoles.includes(user.role);
        if (!hasRole) {
          this.handleAccessDenied("R\xF4le insuffisant pour acc\xE9der \xE0 cette page");
          return false;
        }
      }
      const requiredPermissions = route.data["permissions"];
      if (requiredPermissions && requiredPermissions.length > 0) {
        const hasAllPermissions = requiredPermissions.every((perm) => this.adminAuthService.hasPermission(perm.resource, perm.action));
        if (!hasAllPermissions) {
          this.handleAccessDenied("Permissions insuffisantes pour acc\xE9der \xE0 cette page");
          return false;
        }
      }
      return true;
    }));
  }
  handleAccessDenied(message) {
    this.router.navigate(["/admin/dashboard"], {
      queryParams: {
        error: "access_denied",
        message
      }
    });
  }
  static \u0275fac = function RoleGuard_Factory(__ngFactoryType__) {
    return new (__ngFactoryType__ || _RoleGuard)(\u0275\u0275inject(AdminAuthService), \u0275\u0275inject(Router));
  };
  static \u0275prov = /* @__PURE__ */ \u0275\u0275defineInjectable({ token: _RoleGuard, factory: _RoleGuard.\u0275fac, providedIn: "root" });
};
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && setClassMetadata(RoleGuard, [{
    type: Injectable,
    args: [{
      providedIn: "root"
    }]
  }], () => [{ type: AdminAuthService }, { type: Router }], null);
})();

// src/app/routes/admin.routes.ts
var adminRoutes = [
  {
    path: "",
    redirectTo: "dashboard",
    pathMatch: "full"
  },
  {
    path: "login",
    loadComponent: () => import("./chunk-CEJLOO4X.js").then((m) => m.AdminLoginComponent),
    title: "Connexion Admin - Optique Vision"
  },
  {
    path: "dashboard",
    canActivate: [AdminAuthGuard],
    loadComponent: () => import("./chunk-W2UMNSEZ.js").then((m) => m.AdminLayoutComponent),
    title: "Dashboard Admin - Optique Vision",
    children: [
      {
        path: "",
        loadComponent: () => import("./chunk-WAO4F4AD.js").then((m) => m.AdminDashboardComponent),
        title: "Tableau de bord - Admin"
      },
      {
        path: "users",
        canActivate: [RoleGuard],
        data: {
          roles: [AdminRole.ADMIN, AdminRole.SUPER_ADMIN],
          permissions: [{ resource: "users", action: PermissionAction.READ }]
        },
        loadComponent: () => import("./chunk-VJGGIZOP.js").then((m) => m.UserManagementComponent),
        title: "Gestion des utilisateurs - Admin"
      },
      {
        path: "users/roles",
        canActivate: [SuperAdminGuard],
        loadComponent: () => import("./chunk-P7SHLRZE.js").then((m) => m.RoleAssignmentComponent),
        title: "Attribution des r\xF4les - Admin"
      },
      {
        path: "products",
        canActivate: [RoleGuard],
        data: {
          permissions: [{ resource: "products", action: PermissionAction.READ }]
        },
        loadComponent: () => import("./chunk-YPTXIRSN.js").then((m) => m.ProductManagementComponent),
        title: "Gestion des produits - Admin"
      },
      {
        path: "products/validation",
        canActivate: [RoleGuard],
        data: {
          permissions: [{ resource: "products", action: PermissionAction.APPROVE }]
        },
        loadComponent: () => import("./chunk-5ZQT6ZXA.js").then((m) => m.ProductValidationComponent),
        title: "Validation des produits - Admin"
      },
      {
        path: "categories",
        canActivate: [AdminAuthGuard],
        loadComponent: () => import("./chunk-SRX6LHHQ.js").then((m) => m.CategoryManagementComponent),
        title: "Gestion des cat\xE9gories - Admin"
      },
      {
        path: "demandes",
        canActivate: [RoleGuard],
        data: {
          roles: [AdminRole.ADMIN, AdminRole.SUPER_ADMIN],
          permissions: [{ resource: "categories", action: PermissionAction.APPROVE }]
        },
        loadComponent: () => import("./chunk-M7E4P5MN.js").then((m) => m.DemandesManagementComponent),
        title: "Gestion des demandes - Admin"
      },
      {
        path: "commissions",
        canActivate: [SuperAdminGuard],
        loadComponent: () => import("./chunk-7PVY5IDH.js").then((m) => m.CommissionRulesComponent),
        title: "R\xE8gles de commission - Admin"
      },
      {
        path: "promotions",
        canActivate: [RoleGuard],
        data: {
          roles: [AdminRole.ADMIN, AdminRole.SUPER_ADMIN],
          permissions: [{ resource: "promotions", action: PermissionAction.CREATE }]
        },
        loadComponent: () => import("./chunk-WUG5RC2A.js").then((m) => m.PromotionEngineComponent),
        title: "Moteur de promotions - Admin"
      },
      {
        path: "avis-moderation",
        canActivate: [AdminAuthGuard],
        loadComponent: () => import("./chunk-VJ2XUHC3.js").then((m) => m.AvisModerationComponent),
        title: "Mod\xE9ration des avis - Admin"
      },
      {
        path: "batch",
        canActivate: [SuperAdminGuard],
        loadComponent: () => import("./chunk-U3TE3X4Q.js").then((m) => m.BatchOperationsComponent),
        title: "Op\xE9rations par lots - Admin"
      },
      {
        path: "workflow",
        canActivate: [RoleGuard],
        data: {
          roles: [AdminRole.ADMIN, AdminRole.SUPER_ADMIN]
        },
        loadComponent: () => import("./chunk-NBJWD4PR.js").then((m) => m.WorkflowEngineComponent),
        title: "Moteur de workflow - Admin"
      },
      {
        path: "profile",
        canActivate: [AdminAuthGuard],
        loadComponent: () => import("./chunk-D3C4HQQV.js").then((m) => m.AdminProfileComponent),
        title: "Mon profil - Admin"
      },
      {
        path: "reports",
        canActivate: [AdminAuthGuard],
        loadComponent: () => import("./chunk-ZPIYOOVG.js").then((m) => m.ReportsComponent),
        title: "Rapports - Admin"
      },
      {
        path: "orders",
        canActivate: [AdminAuthGuard],
        loadComponent: () => import("./chunk-B5ZEQJLI.js").then((m) => m.OrderManagementComponent),
        title: "Gestion des commandes - Admin"
      }
    ]
  },
  {
    path: "unauthorized",
    loadComponent: () => import("./chunk-YVRZDTXZ.js").then((m) => m.UnauthorizedComponent),
    title: "Acc\xE8s non autoris\xE9 - Admin"
  },
  {
    path: "**",
    redirectTo: "login"
  }
];
export {
  adminRoutes
};
//# sourceMappingURL=chunk-723FXVA7.js.map

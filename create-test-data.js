// Script pour créer des données de test (fournisseur, produits, clients, avis)
const API_URL = 'http://localhost:5014/api';

async function createTestData() {
    try {
        console.log('🚀 Création des données de test...');
        
        // 1. Créer un compte fournisseur de test
        const fournisseurData = await createTestFournisseur();
        if (!fournisseurData) return;
        
        // 2. Se connecter en tant que fournisseur
        const fournisseurToken = await loginUser('<EMAIL>', 'TestFournisseur123!');
        if (!fournisseurToken) return;
        
        // 3. Créer des clients de test
        const clients = await createTestClients();
        
        // 4. Créer des produits de test pour le fournisseur
        const produits = await createTestProduits(fournisseurToken.token);
        
        // 5. Créer des avis de test
        await createTestAvis(clients, produits);
        
        console.log('✅ Données de test créées avec succès !');
        console.log('🔄 Rechargez la page pour voir les avis !');
        
        // Stocker le token fournisseur pour l'interface
        localStorage.setItem('auth_token', fournisseurToken.token);
        localStorage.setItem('current_user', JSON.stringify(fournisseurToken.utilisateur));
        
        setTimeout(() => location.reload(), 2000);
        
    } catch (error) {
        console.error('❌ Erreur lors de la création des données de test:', error);
    }
}

async function createTestFournisseur() {
    try {
        const formData = new FormData();
        formData.append('Email', '<EMAIL>');
        formData.append('Password', 'TestFournisseur123!');
        formData.append('Nom', 'TestFournisseur');
        formData.append('Prenom', 'Société');
        formData.append('PhoneNumber', '12345678');
        formData.append('DateNaissance', '1990-01-01');
        formData.append('MatriculeFiscale', '12345678');
        formData.append('RaisonSociale', 'Test Fournisseur SARL');
        formData.append('Description', 'Fournisseur de test pour les avis');
        formData.append('RIB', '12345678901234567890');
        formData.append('CodeBanque', '123');
        formData.append('Commission', '0.75');
        formData.append('DelaiPreparationJours', '2');
        formData.append('FraisLivraisonBase', '9.99');
        formData.append('Rue', '123 Rue de Test');
        formData.append('Ville', 'Tunis');
        formData.append('CodePostal', '1000');
        formData.append('Pays', 'Tunisie');
        
        const logoBlob = new Blob([''], { type: 'image/png' });
        formData.append('LogoFile', logoBlob, 'test-logo.png');
        
        const response = await fetch(`${API_URL}/Auth/register/fournisseur`, {
            method: 'POST',
            body: formData
        });
        
        if (response.ok) {
            const result = await response.json();
            console.log('✅ Fournisseur créé:', result);
            return result;
        } else {
            console.log('⚠️ Fournisseur existe peut-être déjà');
            return { success: true }; // Continuer même si existe déjà
        }
    } catch (error) {
        console.error('❌ Erreur création fournisseur:', error);
        return null;
    }
}

async function loginUser(email, password) {
    try {
        const response = await fetch(`${API_URL}/Auth/login`, {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({ email, password })
        });
        
        if (response.ok) {
            const result = await response.json();
            console.log('✅ Connexion réussie:', email);
            return result;
        } else {
            console.error('❌ Erreur de connexion:', email);
            return null;
        }
    } catch (error) {
        console.error('❌ Erreur login:', error);
        return null;
    }
}

async function createTestClients() {
    const clients = [];
    const clientsData = [
        { email: '<EMAIL>', nom: 'Dupont', prenom: 'Jean' },
        { email: '<EMAIL>', nom: 'Martin', prenom: 'Marie' },
        { email: '<EMAIL>', nom: 'Bernard', prenom: 'Pierre' }
    ];
    
    for (const clientData of clientsData) {
        try {
            const formData = new FormData();
            formData.append('Email', clientData.email);
            formData.append('Password', 'TestClient123!');
            formData.append('Nom', clientData.nom);
            formData.append('Prenom', clientData.prenom);
            formData.append('PhoneNumber', '12345678');
            formData.append('DateNaissance', '1990-01-01');
            formData.append('Rue', '123 Rue Client');
            formData.append('Ville', 'Tunis');
            formData.append('CodePostal', '1000');
            formData.append('Pays', 'Tunisie');
            
            const response = await fetch(`${API_URL}/Auth/register/client`, {
                method: 'POST',
                body: formData
            });
            
            if (response.ok) {
                const result = await response.json();
                const loginResult = await loginUser(clientData.email, 'TestClient123!');
                if (loginResult) {
                    clients.push(loginResult);
                    console.log('✅ Client créé:', clientData.email);
                }
            }
        } catch (error) {
            console.error('❌ Erreur création client:', clientData.email, error);
        }
    }
    
    return clients;
}

async function createTestProduits(fournisseurToken) {
    // Cette fonction nécessiterait l'endpoint de création de produits
    // Pour l'instant, on va supposer qu'il y a déjà des produits
    console.log('⚠️ Création de produits non implémentée - utilisation des produits existants');
    return [{ id: 1, nom: 'Produit Test 1' }, { id: 2, nom: 'Produit Test 2' }];
}

async function createTestAvis(clients, produits) {
    const avisData = [
        { note: 5, commentaire: 'Excellent produit, très satisfait !', produitId: 1 },
        { note: 4, commentaire: 'Bon produit, livraison rapide.', produitId: 1 },
        { note: 3, commentaire: 'Correct mais peut mieux faire.', produitId: 2 },
        { note: 5, commentaire: 'Parfait, je recommande !', produitId: 2 },
        { note: 2, commentaire: 'Pas terrible, déçu de mon achat.', produitId: 1 }
    ];
    
    for (let i = 0; i < avisData.length && i < clients.length; i++) {
        try {
            const client = clients[i % clients.length];
            const avis = avisData[i];
            
            const response = await fetch(`${API_URL}/Avis/${avis.produitId}`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': `Bearer ${client.token}`
                },
                body: JSON.stringify({
                    note: avis.note,
                    commentaire: avis.commentaire,
                    produitId: avis.produitId
                })
            });
            
            if (response.ok) {
                console.log('✅ Avis créé:', avis.commentaire.substring(0, 30) + '...');
            } else {
                console.log('⚠️ Erreur création avis:', await response.text());
            }
        } catch (error) {
            console.error('❌ Erreur création avis:', error);
        }
    }
}

// Exécuter le script
console.log('🎯 Démarrage de la création des données de test...');
createTestData();

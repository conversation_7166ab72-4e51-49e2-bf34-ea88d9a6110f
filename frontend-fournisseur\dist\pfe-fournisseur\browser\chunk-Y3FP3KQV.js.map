{"version": 3, "sources": ["src/app/services/dashboard.service.ts"], "sourcesContent": ["import { Injectable } from '@angular/core';\r\nimport { HttpClient, HttpHeaders } from '@angular/common/http';\r\nimport { Observable, forkJoin, map, catchError, of } from 'rxjs';\r\nimport { environment } from '../../environments/environment';\r\nimport { AuthService } from './auth.service';\r\nimport { ProduitService } from './produit.service';\r\nimport { Produit } from '../models/produit.model';\r\n\r\nexport interface DashboardStats {\r\n  totalProducts: number;\r\n  activeOrders: number;\r\n  pendingDeliveries: number;\r\n  monthlyRevenue: number;\r\n}\r\n\r\nexport interface RecentOrder {\r\n  id: number;\r\n  reference: string;\r\n  client: string;\r\n  date: Date;\r\n  amount: number;\r\n  status: string;\r\n}\r\n\r\nexport interface RecentActivity {\r\n  icon: string;\r\n  title: string;\r\n  time: string;\r\n}\r\n\r\nexport interface AdminStats {\r\n  nombreUtilisateurs: number;\r\n  nombreVentes: number;\r\n  nombreProduits: number;\r\n  nombreCommandesAnnulees: number;\r\n  nombreCommandes: number;\r\n  nombreFournisseurs: number;\r\n  nombreClients: number;\r\n}\r\n\r\nexport interface Fournisseur {\r\n  id: number;\r\n  email: string;\r\n  nom: string;\r\n  prenom: string;\r\n  phoneNumber: string;\r\n  role: string;\r\n  dateNaissance: string;\r\n  dateInscription: string;\r\n  derniereConnexion: string | null;\r\n  estActif: boolean;\r\n  matriculeFiscale: string;\r\n  raisonSociale: string;\r\n  description: string;\r\n  ribMasque: string;\r\n  codeBanque: string;\r\n  commission: number;\r\n  delaiPreparationJours: number;\r\n  fraisLivraisonBase: number;\r\n  logoFile: string;\r\n  adresses: any[];\r\n}\r\n\r\n// Interface Produit supprimée - utilise celle du modèle importé\r\n\r\nexport interface CommandeFournisseur {\r\n  id: number;\r\n  reference: string;\r\n  fournisseurId: number;\r\n  nomFournisseur: string;\r\n  dateCommande: string;\r\n  dateLivraison: string;\r\n  statut: string;\r\n  montantTotal: number;\r\n  lignes: any[];\r\n}\r\n\r\n@Injectable({\r\n  providedIn: 'root'\r\n})\r\nexport class DashboardService {\r\n  private apiUrl = environment.apiUrl;\r\n\r\n  constructor(\r\n    private http: HttpClient,\r\n    private authService: AuthService,\r\n    private produitService: ProduitService\r\n  ) {}\r\n\r\n  /**\r\n   * Récupérer l'ID du fournisseur connecté (utilise la même logique que la page produits)\r\n   */\r\n  private getFournisseurId(): number | null {\r\n    // Utiliser directement le service d'authentification comme dans la page produits\r\n    const currentUser = this.authService.getCurrentUser();\r\n    return currentUser?.id || null;\r\n  }\r\n\r\n  /**\r\n   * Headers avec token d'authentification\r\n   */\r\n  private getAuthHeaders(): HttpHeaders {\r\n    const token = this.authService.getToken();\r\n    return new HttpHeaders({\r\n      'Authorization': `Bearer ${token}`,\r\n      'Content-Type': 'application/json'\r\n    });\r\n  }\r\n\r\n  /**\r\n   * Récupérer les statistiques admin (globales)\r\n   */\r\n  getAdminStats(): Observable<AdminStats> {\r\n    return this.http.get<AdminStats>(`${this.apiUrl}/api/Admin/statistiques`, {\r\n      headers: this.getAuthHeaders()\r\n    });\r\n  }\r\n\r\n  /**\r\n   * Récupérer les informations du fournisseur connecté (priorité localStorage)\r\n   */\r\n  getFournisseurInfo(): Observable<Fournisseur | null> {\r\n    console.log('🔄 Dashboard: Récupération des infos fournisseur...');\r\n\r\n    // Essayer d'abord localStorage\r\n    const storedUser = localStorage.getItem('user');\r\n    const currentUser = this.authService.getCurrentUser();\r\n\r\n    if (storedUser) {\r\n      try {\r\n        const userData = JSON.parse(storedUser);\r\n        console.log('📦 Dashboard: Données depuis localStorage:', userData);\r\n\r\n        // Créer un objet Fournisseur depuis localStorage\r\n        const fournisseur: Fournisseur = {\r\n          id: userData.id || currentUser?.id || 0,\r\n          email: userData.email || currentUser?.email || '',\r\n          nom: userData.nom || currentUser?.nom || '',\r\n          prenom: userData.prenom || currentUser?.prenom || '',\r\n          phoneNumber: userData.phoneNumber || userData.telephone || '',\r\n          role: userData.role || 'Fournisseur',\r\n          dateNaissance: userData.dateNaissance || '',\r\n          dateInscription: userData.dateInscription || new Date().toISOString(),\r\n          derniereConnexion: userData.derniereConnexion || null,\r\n          estActif: userData.estActif !== undefined ? userData.estActif : true,\r\n          matriculeFiscale: userData.matriculeFiscale || '',\r\n          raisonSociale: userData.raisonSociale || '',\r\n          description: userData.description || '',\r\n          ribMasque: userData.ribMasque || userData.rib || '',\r\n          codeBanque: userData.codeBanque || '',\r\n          commission: userData.commission || 0,\r\n          delaiPreparationJours: userData.delaiPreparationJours || 3,\r\n          fraisLivraisonBase: userData.fraisLivraisonBase || 5.00,\r\n          logoFile: userData.logoFile || '',\r\n          adresses: userData.adresses || []\r\n        };\r\n\r\n        console.log('✅ Dashboard: Fournisseur créé depuis localStorage:', fournisseur);\r\n        return of(fournisseur);\r\n      } catch (error) {\r\n        console.warn('⚠️ Dashboard: Erreur parsing localStorage:', error);\r\n      }\r\n    }\r\n\r\n    // Fallback: créer depuis currentUser si disponible\r\n    if (currentUser) {\r\n      const fournisseur: Fournisseur = {\r\n        id: currentUser.id,\r\n        email: currentUser.email,\r\n        nom: currentUser.nom,\r\n        prenom: currentUser.prenom,\r\n        phoneNumber: (currentUser as any).phoneNumber || (currentUser as any).telephone || '',\r\n        role: currentUser.role || 'Fournisseur',\r\n        dateNaissance: '',\r\n        dateInscription: new Date().toISOString(),\r\n        derniereConnexion: null,\r\n        estActif: true,\r\n        matriculeFiscale: '',\r\n        raisonSociale: '',\r\n        description: '',\r\n        ribMasque: '',\r\n        codeBanque: '',\r\n        commission: 0,\r\n        delaiPreparationJours: 3,\r\n        fraisLivraisonBase: 5.00,\r\n        logoFile: '',\r\n        adresses: []\r\n      };\r\n\r\n      console.log('✅ Dashboard: Fournisseur créé depuis currentUser:', fournisseur);\r\n      return of(fournisseur);\r\n    }\r\n\r\n    console.warn('⚠️ Dashboard: Aucune donnée utilisateur disponible');\r\n    return of(null);\r\n  }\r\n\r\n  /**\r\n   * Récupérer les produits du fournisseur (utilise le même service que la page produits)\r\n   */\r\n  getFournisseurProducts(): Observable<Produit[]> {\r\n    const currentUser = this.authService.getCurrentUser();\r\n    if (!currentUser?.id) {\r\n      console.warn('Aucun utilisateur connecté trouvé');\r\n      return of([]);\r\n    }\r\n\r\n    console.log(`🔄 Dashboard: Récupération des produits pour le fournisseur ${currentUser.id}`);\r\n    return this.produitService.getByFournisseur(currentUser.id).pipe(\r\n      map(products => {\r\n        console.log(`✅ Dashboard: ${products.length} produits récupérés:`, products);\r\n        return products || [];\r\n      }),\r\n      catchError(error => {\r\n        console.error('❌ Dashboard: Erreur lors de la récupération des produits:', error);\r\n        return of([]);\r\n      })\r\n    );\r\n  }\r\n\r\n  /**\r\n   * Récupérer les commandes fournisseur\r\n   */\r\n  getFournisseurOrders(): Observable<CommandeFournisseur[]> {\r\n    const fournisseurId = this.getFournisseurId();\r\n    console.log('Récupération des commandes...');\r\n\r\n    return this.http.get<CommandeFournisseur[]>(`${this.apiUrl}/api/CommandeFournisseur`, {\r\n      headers: this.getAuthHeaders()\r\n    }).pipe(\r\n      map(orders => {\r\n        console.log(`${orders?.length || 0} commandes récupérées:`, orders);\r\n        // Filtrer les commandes du fournisseur si nécessaire\r\n        if (fournisseurId && orders) {\r\n          return orders.filter(order => order.fournisseurId === fournisseurId);\r\n        }\r\n        return orders || [];\r\n      }),\r\n      catchError(error => {\r\n        console.error('Erreur lors de la récupération des commandes:', error);\r\n        console.log('Aucune commande disponible - retour d\\'un tableau vide');\r\n        // Retourner un tableau vide en cas d'erreur\r\n        return of([]);\r\n      })\r\n    );\r\n  }\r\n\r\n\r\n\r\n  /**\r\n   * Calculer les statistiques du dashboard à partir des données réelles\r\n   */\r\n  getDashboardStats(): Observable<DashboardStats> {\r\n    const fournisseurId = this.getFournisseurId();\r\n    if (!fournisseurId) {\r\n      return of({\r\n        totalProducts: 0,\r\n        activeOrders: 0,\r\n        pendingDeliveries: 0,\r\n        monthlyRevenue: 0\r\n      });\r\n    }\r\n\r\n    return forkJoin({\r\n      products: this.getFournisseurProducts(),\r\n      orders: this.getFournisseurOrders()\r\n    }).pipe(\r\n      map(({ products, orders }) => {\r\n        // Calculer les statistiques\r\n        const totalProducts = products.length;\r\n        const activeOrders = orders.filter(order =>\r\n          order.statut !== 'Livré' && order.statut !== 'Annulé'\r\n        ).length;\r\n\r\n        // Calculer le chiffre d'affaires du mois (approximation)\r\n        const currentMonth = new Date().getMonth();\r\n        const monthlyRevenue = orders\r\n          .filter(order => {\r\n            const orderDate = new Date(order.dateCommande);\r\n            return orderDate.getMonth() === currentMonth;\r\n          })\r\n          .reduce((total, order) => total + order.montantTotal, 0);\r\n\r\n        // Livraisons en cours (approximation basée sur les commandes)\r\n        const pendingDeliveries = orders.filter(order =>\r\n          order.statut === 'En préparation' || order.statut === 'Expédié'\r\n        ).length;\r\n\r\n        return {\r\n          totalProducts,\r\n          activeOrders,\r\n          pendingDeliveries,\r\n          monthlyRevenue\r\n        };\r\n      }),\r\n      catchError(error => {\r\n        console.error('Erreur lors du calcul des statistiques:', error);\r\n        return of({\r\n          totalProducts: 0,\r\n          activeOrders: 0,\r\n          pendingDeliveries: 0,\r\n          monthlyRevenue: 0\r\n        });\r\n      })\r\n    );\r\n  }\r\n\r\n  /**\r\n   * Récupérer les commandes récentes\r\n   */\r\n  getRecentOrders(): Observable<RecentOrder[]> {\r\n    return this.getFournisseurOrders().pipe(\r\n      map(orders => {\r\n        return orders\r\n          .sort((a, b) => new Date(b.dateCommande).getTime() - new Date(a.dateCommande).getTime())\r\n          .slice(0, 5) // Les 5 plus récentes\r\n          .map(order => ({\r\n            id: order.id,\r\n            reference: order.reference,\r\n            client: order.nomFournisseur, // Nom du client (à adapter selon votre modèle)\r\n            date: new Date(order.dateCommande),\r\n            amount: order.montantTotal,\r\n            status: order.statut\r\n          }));\r\n      })\r\n    );\r\n  }\r\n\r\n  /**\r\n   * Récupérer l'activité récente\r\n   */\r\n  getRecentActivity(): Observable<RecentActivity[]> {\r\n    return forkJoin({\r\n      products: this.getFournisseurProducts(),\r\n      orders: this.getFournisseurOrders()\r\n    }).pipe(\r\n      map(({ products, orders }) => {\r\n        const activities: RecentActivity[] = [];\r\n\r\n        // Derniers produits ajoutés\r\n        const recentProducts = products\r\n          .sort((a, b) => new Date(b.dateAjout).getTime() - new Date(a.dateAjout).getTime())\r\n          .slice(0, 2);\r\n\r\n        recentProducts.forEach(product => {\r\n          activities.push({\r\n            icon: '📦',\r\n            title: `Produit \"${product.nom}\" ajouté`,\r\n            time: this.getRelativeTime(new Date(product.dateAjout))\r\n          });\r\n        });\r\n\r\n        // Dernières commandes\r\n        const recentOrders = orders\r\n          .sort((a, b) => new Date(b.dateCommande).getTime() - new Date(a.dateCommande).getTime())\r\n          .slice(0, 2);\r\n\r\n        recentOrders.forEach(order => {\r\n          activities.push({\r\n            icon: '📋',\r\n            title: `Commande ${order.reference} reçue`,\r\n            time: this.getRelativeTime(new Date(order.dateCommande))\r\n          });\r\n        });\r\n\r\n        return activities.slice(0, 3); // Limiter à 3 activités\r\n      })\r\n    );\r\n  }\r\n\r\n  /**\r\n   * Calculer le temps relatif (il y a X heures/jours)\r\n   */\r\n  private getRelativeTime(date: Date): string {\r\n    const now = new Date();\r\n    const diffMs = now.getTime() - date.getTime();\r\n    const diffHours = Math.floor(diffMs / (1000 * 60 * 60));\r\n    const diffDays = Math.floor(diffHours / 24);\r\n\r\n    if (diffDays > 0) {\r\n      return diffDays === 1 ? 'Hier' : `Il y a ${diffDays} jours`;\r\n    } else if (diffHours > 0) {\r\n      return `Il y a ${diffHours} heure${diffHours > 1 ? 's' : ''}`;\r\n    } else {\r\n      return 'À l\\'instant';\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Formater le prix en dinars tunisiens\r\n   */\r\n  formatCurrency(amount: number): string {\r\n    return new Intl.NumberFormat('fr-TN', {\r\n      style: 'currency',\r\n      currency: 'TND'\r\n    }).format(amount);\r\n  }\r\n}"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;AAgFM,IAAO,mBAAP,MAAO,kBAAgB;EAIjB;EACA;EACA;EALF,SAAS,YAAY;EAE7B,YACU,MACA,aACA,gBAA8B;AAF9B,SAAA,OAAA;AACA,SAAA,cAAA;AACA,SAAA,iBAAA;EACP;;;;EAKK,mBAAgB;AAEtB,UAAM,cAAc,KAAK,YAAY,eAAc;AACnD,WAAO,aAAa,MAAM;EAC5B;;;;EAKQ,iBAAc;AACpB,UAAM,QAAQ,KAAK,YAAY,SAAQ;AACvC,WAAO,IAAI,YAAY;MACrB,iBAAiB,UAAU,KAAK;MAChC,gBAAgB;KACjB;EACH;;;;EAKA,gBAAa;AACX,WAAO,KAAK,KAAK,IAAgB,GAAG,KAAK,MAAM,2BAA2B;MACxE,SAAS,KAAK,eAAc;KAC7B;EACH;;;;EAKA,qBAAkB;AAChB,YAAQ,IAAI,kEAAqD;AAGjE,UAAM,aAAa,aAAa,QAAQ,MAAM;AAC9C,UAAM,cAAc,KAAK,YAAY,eAAc;AAEnD,QAAI,YAAY;AACd,UAAI;AACF,cAAM,WAAW,KAAK,MAAM,UAAU;AACtC,gBAAQ,IAAI,wDAA8C,QAAQ;AAGlE,cAAM,cAA2B;UAC/B,IAAI,SAAS,MAAM,aAAa,MAAM;UACtC,OAAO,SAAS,SAAS,aAAa,SAAS;UAC/C,KAAK,SAAS,OAAO,aAAa,OAAO;UACzC,QAAQ,SAAS,UAAU,aAAa,UAAU;UAClD,aAAa,SAAS,eAAe,SAAS,aAAa;UAC3D,MAAM,SAAS,QAAQ;UACvB,eAAe,SAAS,iBAAiB;UACzC,iBAAiB,SAAS,oBAAmB,oBAAI,KAAI,GAAG,YAAW;UACnE,mBAAmB,SAAS,qBAAqB;UACjD,UAAU,SAAS,aAAa,SAAY,SAAS,WAAW;UAChE,kBAAkB,SAAS,oBAAoB;UAC/C,eAAe,SAAS,iBAAiB;UACzC,aAAa,SAAS,eAAe;UACrC,WAAW,SAAS,aAAa,SAAS,OAAO;UACjD,YAAY,SAAS,cAAc;UACnC,YAAY,SAAS,cAAc;UACnC,uBAAuB,SAAS,yBAAyB;UACzD,oBAAoB,SAAS,sBAAsB;UACnD,UAAU,SAAS,YAAY;UAC/B,UAAU,SAAS,YAAY,CAAA;;AAGjC,gBAAQ,IAAI,iEAAsD,WAAW;AAC7E,eAAO,GAAG,WAAW;MACvB,SAAS,OAAO;AACd,gBAAQ,KAAK,wDAA8C,KAAK;MAClE;IACF;AAGA,QAAI,aAAa;AACf,YAAM,cAA2B;QAC/B,IAAI,YAAY;QAChB,OAAO,YAAY;QACnB,KAAK,YAAY;QACjB,QAAQ,YAAY;QACpB,aAAc,YAAoB,eAAgB,YAAoB,aAAa;QACnF,MAAM,YAAY,QAAQ;QAC1B,eAAe;QACf,kBAAiB,oBAAI,KAAI,GAAG,YAAW;QACvC,mBAAmB;QACnB,UAAU;QACV,kBAAkB;QAClB,eAAe;QACf,aAAa;QACb,WAAW;QACX,YAAY;QACZ,YAAY;QACZ,uBAAuB;QACvB,oBAAoB;QACpB,UAAU;QACV,UAAU,CAAA;;AAGZ,cAAQ,IAAI,gEAAqD,WAAW;AAC5E,aAAO,GAAG,WAAW;IACvB;AAEA,YAAQ,KAAK,iEAAoD;AACjE,WAAO,GAAG,IAAI;EAChB;;;;EAKA,yBAAsB;AACpB,UAAM,cAAc,KAAK,YAAY,eAAc;AACnD,QAAI,CAAC,aAAa,IAAI;AACpB,cAAQ,KAAK,yCAAmC;AAChD,aAAO,GAAG,CAAA,CAAE;IACd;AAEA,YAAQ,IAAI,4EAA+D,YAAY,EAAE,EAAE;AAC3F,WAAO,KAAK,eAAe,iBAAiB,YAAY,EAAE,EAAE,KAC1D,IAAI,cAAW;AACb,cAAQ,IAAI,qBAAgB,SAAS,MAAM,iCAAwB,QAAQ;AAC3E,aAAO,YAAY,CAAA;IACrB,CAAC,GACD,WAAW,WAAQ;AACjB,cAAQ,MAAM,wEAA6D,KAAK;AAChF,aAAO,GAAG,CAAA,CAAE;IACd,CAAC,CAAC;EAEN;;;;EAKA,uBAAoB;AAClB,UAAM,gBAAgB,KAAK,iBAAgB;AAC3C,YAAQ,IAAI,qCAA+B;AAE3C,WAAO,KAAK,KAAK,IAA2B,GAAG,KAAK,MAAM,4BAA4B;MACpF,SAAS,KAAK,eAAc;KAC7B,EAAE,KACD,IAAI,YAAS;AACX,cAAQ,IAAI,GAAG,QAAQ,UAAU,CAAC,mCAA0B,MAAM;AAElE,UAAI,iBAAiB,QAAQ;AAC3B,eAAO,OAAO,OAAO,WAAS,MAAM,kBAAkB,aAAa;MACrE;AACA,aAAO,UAAU,CAAA;IACnB,CAAC,GACD,WAAW,WAAQ;AACjB,cAAQ,MAAM,uDAAiD,KAAK;AACpE,cAAQ,IAAI,uDAAwD;AAEpE,aAAO,GAAG,CAAA,CAAE;IACd,CAAC,CAAC;EAEN;;;;EAOA,oBAAiB;AACf,UAAM,gBAAgB,KAAK,iBAAgB;AAC3C,QAAI,CAAC,eAAe;AAClB,aAAO,GAAG;QACR,eAAe;QACf,cAAc;QACd,mBAAmB;QACnB,gBAAgB;OACjB;IACH;AAEA,WAAO,SAAS;MACd,UAAU,KAAK,uBAAsB;MACrC,QAAQ,KAAK,qBAAoB;KAClC,EAAE,KACD,IAAI,CAAC,EAAE,UAAU,OAAM,MAAM;AAE3B,YAAM,gBAAgB,SAAS;AAC/B,YAAM,eAAe,OAAO,OAAO,WACjC,MAAM,WAAW,cAAW,MAAM,WAAW,WAAQ,EACrD;AAGF,YAAM,gBAAe,oBAAI,KAAI,GAAG,SAAQ;AACxC,YAAM,iBAAiB,OACpB,OAAO,WAAQ;AACd,cAAM,YAAY,IAAI,KAAK,MAAM,YAAY;AAC7C,eAAO,UAAU,SAAQ,MAAO;MAClC,CAAC,EACA,OAAO,CAAC,OAAO,UAAU,QAAQ,MAAM,cAAc,CAAC;AAGzD,YAAM,oBAAoB,OAAO,OAAO,WACtC,MAAM,WAAW,uBAAoB,MAAM,WAAW,eAAS,EAC/D;AAEF,aAAO;QACL;QACA;QACA;QACA;;IAEJ,CAAC,GACD,WAAW,WAAQ;AACjB,cAAQ,MAAM,2CAA2C,KAAK;AAC9D,aAAO,GAAG;QACR,eAAe;QACf,cAAc;QACd,mBAAmB;QACnB,gBAAgB;OACjB;IACH,CAAC,CAAC;EAEN;;;;EAKA,kBAAe;AACb,WAAO,KAAK,qBAAoB,EAAG,KACjC,IAAI,YAAS;AACX,aAAO,OACJ,KAAK,CAAC,GAAG,MAAM,IAAI,KAAK,EAAE,YAAY,EAAE,QAAO,IAAK,IAAI,KAAK,EAAE,YAAY,EAAE,QAAO,CAAE,EACtF,MAAM,GAAG,CAAC,EACV,IAAI,YAAU;QACb,IAAI,MAAM;QACV,WAAW,MAAM;QACjB,QAAQ,MAAM;;QACd,MAAM,IAAI,KAAK,MAAM,YAAY;QACjC,QAAQ,MAAM;QACd,QAAQ,MAAM;QACd;IACN,CAAC,CAAC;EAEN;;;;EAKA,oBAAiB;AACf,WAAO,SAAS;MACd,UAAU,KAAK,uBAAsB;MACrC,QAAQ,KAAK,qBAAoB;KAClC,EAAE,KACD,IAAI,CAAC,EAAE,UAAU,OAAM,MAAM;AAC3B,YAAM,aAA+B,CAAA;AAGrC,YAAM,iBAAiB,SACpB,KAAK,CAAC,GAAG,MAAM,IAAI,KAAK,EAAE,SAAS,EAAE,QAAO,IAAK,IAAI,KAAK,EAAE,SAAS,EAAE,QAAO,CAAE,EAChF,MAAM,GAAG,CAAC;AAEb,qBAAe,QAAQ,aAAU;AAC/B,mBAAW,KAAK;UACd,MAAM;UACN,OAAO,YAAY,QAAQ,GAAG;UAC9B,MAAM,KAAK,gBAAgB,IAAI,KAAK,QAAQ,SAAS,CAAC;SACvD;MACH,CAAC;AAGD,YAAM,eAAe,OAClB,KAAK,CAAC,GAAG,MAAM,IAAI,KAAK,EAAE,YAAY,EAAE,QAAO,IAAK,IAAI,KAAK,EAAE,YAAY,EAAE,QAAO,CAAE,EACtF,MAAM,GAAG,CAAC;AAEb,mBAAa,QAAQ,WAAQ;AAC3B,mBAAW,KAAK;UACd,MAAM;UACN,OAAO,YAAY,MAAM,SAAS;UAClC,MAAM,KAAK,gBAAgB,IAAI,KAAK,MAAM,YAAY,CAAC;SACxD;MACH,CAAC;AAED,aAAO,WAAW,MAAM,GAAG,CAAC;IAC9B,CAAC,CAAC;EAEN;;;;EAKQ,gBAAgB,MAAU;AAChC,UAAM,MAAM,oBAAI,KAAI;AACpB,UAAM,SAAS,IAAI,QAAO,IAAK,KAAK,QAAO;AAC3C,UAAM,YAAY,KAAK,MAAM,UAAU,MAAO,KAAK,GAAG;AACtD,UAAM,WAAW,KAAK,MAAM,YAAY,EAAE;AAE1C,QAAI,WAAW,GAAG;AAChB,aAAO,aAAa,IAAI,SAAS,UAAU,QAAQ;IACrD,WAAW,YAAY,GAAG;AACxB,aAAO,UAAU,SAAS,SAAS,YAAY,IAAI,MAAM,EAAE;IAC7D,OAAO;AACL,aAAO;IACT;EACF;;;;EAKA,eAAe,QAAc;AAC3B,WAAO,IAAI,KAAK,aAAa,SAAS;MACpC,OAAO;MACP,UAAU;KACX,EAAE,OAAO,MAAM;EAClB;;qCA5TW,mBAAgB,mBAAA,UAAA,GAAA,mBAAA,WAAA,GAAA,mBAAA,cAAA,CAAA;EAAA;4EAAhB,mBAAgB,SAAhB,kBAAgB,WAAA,YAFf,OAAM,CAAA;;;sEAEP,kBAAgB,CAAA;UAH5B;WAAW;MACV,YAAY;KACb;;;", "names": []}
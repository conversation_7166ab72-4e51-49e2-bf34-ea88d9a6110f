{"version": 3, "sources": ["src/app/components/layout/sidebar/sidebar.component.css"], "sourcesContent": [".sidebar {\n  width: 280px;\n  height: 100vh;\n  background: var(--gradient-primary);\n  color: var(--white);\n  display: flex;\n  flex-direction: column;\n  position: fixed;\n  left: 0;\n  top: 0;\n  z-index: 1000; /* Valeur fixe au lieu de var() */\n  transition: width 0.3s ease; /* Valeur fixe au lieu de var() */\n  overflow-y: auto; /* Permettre le scroll dans la sidebar si nécessaire */\n  box-shadow: 0 4px 20px rgba(59, 130, 246, 0.3); /* Valeur fixe */\n}\n\n.sidebar.collapsed {\n  width: 70px;\n}\n\n.sidebar-header {\n  padding: var(--spacing-6);\n  border-bottom: 1px solid rgba(255, 255, 255, 0.1);\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  position: relative;\n}\n\n.sidebar-header::before {\n  content: '';\n  position: absolute;\n  top: 0;\n  left: 0;\n  right: 0;\n  bottom: 0;\n  background: url('data:image/svg+xml,<svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 100 100\"><defs><pattern id=\"dots\" width=\"20\" height=\"20\" patternUnits=\"userSpaceOnUse\"><circle cx=\"10\" cy=\"10\" r=\"1\" fill=\"rgba(255,255,255,0.05)\"/></pattern></defs><rect width=\"100\" height=\"100\" fill=\"url(%23dots)\"/></svg>');\n  opacity: 0.3;\n}\n\n.logo {\n  display: flex;\n  align-items: center;\n  gap: var(--spacing-3);\n  position: relative;\n  z-index: 1;\n}\n\n.logo-icon {\n  width: 48px;\n  height: 48px;\n  background: linear-gradient(135deg, var(--primary-400) 0%, var(--primary-300) 100%);\n  border-radius: var(--border-radius-xl);\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  flex-shrink: 0;\n  box-shadow: var(--shadow-lg);\n  border: 2px solid rgba(255, 255, 255, 0.2);\n  animation: float 3s ease-in-out infinite;\n}\n\n@keyframes float {\n  0%, 100% { transform: translateY(0px); }\n  50% { transform: translateY(-3px); }\n}\n\n.logo-icon svg {\n  color: var(--white);\n  font-size: var(--font-size-xl);\n}\n\n.logo-text {\n  display: flex;\n  flex-direction: column;\n  min-width: 0;\n  position: relative;\n  z-index: 1;\n}\n\n.logo-title {\n  font-size: var(--font-size-lg);\n  font-weight: var(--font-weight-extrabold);\n  color: var(--white);\n  line-height: var(--line-height-tight);\n  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);\n}\n\n.logo-subtitle {\n  font-size: var(--font-size-xs);\n  color: rgba(255, 255, 255, 0.8);\n  line-height: var(--line-height-tight);\n  font-weight: var(--font-weight-medium);\n}\n\n/* Bouton de fermeture mobile */\n.close-btn {\n  background: none;\n  border: none;\n  color: rgba(255, 255, 255, 0.8);\n  cursor: pointer;\n  padding: 8px;\n  border-radius: 6px;\n  transition: all 0.2s ease;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n}\n\n.close-btn:hover {\n  background: rgba(255, 255, 255, 0.1);\n  color: white;\n}\n\n/* Overlay mobile */\n.sidebar-overlay {\n  position: fixed;\n  top: 0;\n  left: 0;\n  width: 100vw;\n  height: 100vh;\n  background: rgba(0, 0, 0, 0.5);\n  z-index: 999;\n  backdrop-filter: blur(2px);\n}\n\n.sidebar-nav {\n  flex: 1;\n  padding: 20px 0;\n  overflow-y: auto;\n}\n\n.nav-list {\n  list-style: none;\n  margin: 0;\n  padding: 0;\n}\n\n.nav-item {\n  margin-bottom: 4px;\n}\n\n.nav-item-wrapper {\n  display: flex;\n  flex-direction: column;\n}\n\n.nav-link {\n  display: flex;\n  align-items: center;\n  padding: var(--spacing-3) var(--spacing-5);\n  color: rgba(255, 255, 255, 0.9);\n  text-decoration: none;\n  transition: all var(--transition-base);\n  border: none;\n  background: none;\n  width: 100%;\n  text-align: left;\n  cursor: pointer;\n  position: relative;\n  border-radius: var(--border-radius-lg);\n  margin: var(--spacing-1) var(--spacing-3);\n  font-weight: var(--font-weight-medium);\n  overflow: hidden;\n}\n\n.nav-link::before {\n  content: '';\n  position: absolute;\n  top: 0;\n  left: -100%;\n  width: 100%;\n  height: 100%;\n  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);\n  transition: var(--transition-slow);\n}\n\n.nav-link:hover::before {\n  left: 100%;\n}\n\n.nav-link:hover {\n  background: rgba(255, 255, 255, 0.15);\n  color: var(--white);\n  transform: translateX(4px);\n  box-shadow: var(--shadow-md);\n}\n\n.nav-link.active {\n  background: rgba(255, 255, 255, 0.2);\n  color: var(--white);\n  font-weight: var(--font-weight-semibold);\n  box-shadow: var(--shadow-lg);\n}\n\n.nav-link.active::after {\n  content: '';\n  position: absolute;\n  left: 0;\n  top: 0;\n  bottom: 0;\n  width: 4px;\n  background: var(--primary-200);\n}\n\n.parent-link {\n  justify-content: space-between;\n}\n\n.parent-link.expanded {\n  background: rgba(255, 255, 255, 0.05);\n}\n\n.nav-icon {\n  width: 24px;\n  height: 24px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  flex-shrink: 0;\n  font-size: var(--font-size-lg);\n  transition: var(--transition-fast);\n}\n\n.nav-link:hover .nav-icon {\n  transform: scale(1.1);\n}\n\n.nav-text {\n  margin-left: var(--spacing-3);\n  font-size: var(--font-size-sm);\n  font-weight: var(--font-weight-medium);\n  flex: 1;\n  white-space: nowrap;\n  overflow: hidden;\n  text-overflow: ellipsis;\n}\n\n.nav-arrow {\n  width: 20px;\n  height: 20px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  transition: transform var(--transition-base);\n  flex-shrink: 0;\n  opacity: 0.7;\n}\n\n.parent-link.expanded .nav-arrow {\n  transform: rotate(180deg);\n  opacity: 1;\n}\n\n.parent-link.expanded .nav-arrow {\n  transform: rotate(180deg);\n}\n\n.submenu {\n  list-style: none;\n  margin: 0;\n  padding: 0;\n  max-height: 0;\n  overflow: hidden;\n  transition: max-height 0.3s ease;\n  background: rgba(0, 0, 0, 0.2);\n}\n\n.submenu.expanded {\n  max-height: 300px;\n}\n\n.submenu-item {\n  margin: 0;\n}\n\n.submenu-link {\n  display: flex;\n  align-items: center;\n  padding: var(--spacing-3) var(--spacing-5) var(--spacing-3) var(--spacing-12);\n  color: rgba(255, 255, 255, 0.95);\n  text-decoration: none;\n  transition: all var(--transition-base);\n  font-size: var(--font-size-sm);\n  position: relative;\n  border-radius: var(--border-radius-lg);\n  margin: var(--spacing-1) var(--spacing-3);\n  font-weight: var(--font-weight-medium);\n  background: rgba(255, 255, 255, 0.08);\n  border-left: 3px solid rgba(255, 255, 255, 0.2);\n}\n\n.submenu-link::before {\n  content: '';\n  position: absolute;\n  left: var(--spacing-6);\n  top: 50%;\n  transform: translateY(-50%);\n  width: 4px;\n  height: 4px;\n  background: rgba(255, 255, 255, 0.4);\n  border-radius: var(--border-radius-full);\n  transition: var(--transition-fast);\n}\n\n.submenu-link:hover::before {\n  background: var(--primary-200);\n  transform: translateY(-50%) scale(1.5);\n}\n\n.submenu-link:hover {\n  background: rgba(255, 255, 255, 0.2);\n  color: var(--white);\n  transform: translateX(6px);\n  border-left-color: rgba(255, 255, 255, 0.6);\n  box-shadow: var(--shadow-md);\n}\n\n.submenu-link.active {\n  background: rgba(255, 255, 255, 0.25);\n  color: var(--white);\n  font-weight: var(--font-weight-semibold);\n  border-left-color: var(--primary-200);\n  box-shadow: var(--shadow-lg);\n}\n\n.submenu-link.active::after {\n  content: '';\n  position: absolute;\n  left: 0;\n  top: 0;\n  bottom: 0;\n  width: 3px;\n  background: var(--primary-200);\n}\n\n.submenu-link.active::before {\n  background: var(--primary-200);\n  transform: translateY(-50%) scale(1.5);\n}\n\n.submenu-icon {\n  width: 16px;\n  height: 16px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  flex-shrink: 0;\n}\n\n.submenu-text {\n  margin-left: 8px;\n  white-space: nowrap;\n  overflow: hidden;\n  text-overflow: ellipsis;\n}\n\n.sidebar-footer {\n  padding: var(--spacing-5);\n  border-top: 1px solid rgba(255, 255, 255, 0.1);\n  margin-top: auto;\n  background: rgba(0, 0, 0, 0.2);\n  backdrop-filter: blur(10px);\n}\n\n.footer-content {\n  text-align: center;\n  position: relative;\n  z-index: 1;\n}\n\n.footer-text {\n  font-size: var(--font-size-xs);\n  color: rgba(255, 255, 255, 0.7);\n  margin: 0 0 var(--spacing-1) 0;\n  font-weight: var(--font-weight-medium);\n}\n\n.footer-copyright {\n  font-size: var(--font-size-xs);\n  color: rgba(255, 255, 255, 0.5);\n  margin: 0;\n  font-weight: var(--font-weight-normal);\n}\n\n/* Collapsed state adjustments */\n.sidebar.collapsed .nav-link {\n  padding: 12px 25px;\n  justify-content: center;\n}\n\n.sidebar.collapsed .submenu {\n  display: none;\n}\n\n.sidebar.collapsed .nav-arrow {\n  display: none;\n}\n\n/* Scrollbar styling */\n.sidebar-nav::-webkit-scrollbar {\n  width: 4px;\n}\n\n.sidebar-nav::-webkit-scrollbar-track {\n  background: rgba(255, 255, 255, 0.1);\n}\n\n.sidebar-nav::-webkit-scrollbar-thumb {\n  background: rgba(255, 255, 255, 0.3);\n  border-radius: 2px;\n}\n\n.sidebar-nav::-webkit-scrollbar-thumb:hover {\n  background: rgba(255, 255, 255, 0.5);\n}\n\n/* Responsive */\n\n/* Mobile (< 768px) */\n@media (max-width: 767px) {\n  .sidebar {\n    width: 280px;\n    transform: translateX(-100%);\n    transition: transform 0.3s ease, width 0.3s ease;\n    box-shadow: 2px 0 10px rgba(0, 0, 0, 0.3);\n  }\n\n  .sidebar:not(.collapsed) {\n    transform: translateX(0);\n  }\n\n  .sidebar.collapsed {\n    transform: translateX(-100%);\n    width: 280px;\n  }\n\n  .sidebar-header {\n    padding: 16px 20px;\n  }\n\n  .logo-title {\n    font-size: 16px;\n  }\n\n  .nav-link {\n    padding: 14px 20px;\n    font-size: 15px;\n  }\n\n  .nav-text {\n    font-size: 15px;\n  }\n\n  .submenu-link {\n    padding: 12px 20px 12px 52px;\n    font-size: 14px;\n  }\n}\n\n/* Tablet (768px - 1023px) */\n@media (min-width: 768px) and (max-width: 1023px) {\n  .sidebar {\n    width: 240px;\n  }\n\n  .sidebar.collapsed {\n    width: 60px;\n  }\n\n  .sidebar-header {\n    padding: 16px;\n  }\n\n  .logo-title {\n    font-size: 16px;\n  }\n\n  .nav-link {\n    padding: 10px 16px;\n  }\n\n  .nav-text {\n    font-size: 13px;\n  }\n\n  .submenu-link {\n    padding: 8px 16px 8px 44px;\n    font-size: 12px;\n  }\n\n  .sidebar.collapsed .nav-link {\n    padding: 10px 20px;\n  }\n}\n\n/* Desktop (> 1024px) */\n@media (min-width: 1024px) {\n  .sidebar {\n    width: 280px;\n  }\n\n  .sidebar.collapsed {\n    width: 70px;\n  }\n}\n\n/* Améliorations pour les boutons sur mobile */\n@media (max-width: 767px) {\n  .nav-link,\n  .submenu-link {\n    border: none;\n    background: none;\n    width: 100%;\n    text-align: left;\n    cursor: pointer;\n    font-family: inherit;\n  }\n\n  .nav-link:focus,\n  .submenu-link:focus {\n    outline: 2px solid rgba(255, 255, 255, 0.3);\n    outline-offset: -2px;\n  }\n}\n\n/* Animations améliorées */\n@media (prefers-reduced-motion: no-preference) {\n  .sidebar {\n    transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1),\n                width 0.3s cubic-bezier(0.4, 0, 0.2, 1);\n  }\n\n  .submenu {\n    transition: max-height 0.3s cubic-bezier(0.4, 0, 0.2, 1);\n  }\n\n  .nav-arrow {\n    transition: transform 0.2s cubic-bezier(0.4, 0, 0.2, 1);\n  }\n}\n"], "mappings": ";AAAA,CAAC;AACC,SAAO;AACP,UAAQ;AACR,cAAY,IAAI;AAChB,SAAO,IAAI;AACX,WAAS;AACT,kBAAgB;AAChB,YAAU;AACV,QAAM;AACN,OAAK;AACL,WAAS;AACT,cAAY,MAAM,KAAK;AACvB,cAAY;AACZ,cAAY,EAAE,IAAI,KAAK,KAAK,EAAE,EAAE,GAAG,EAAE,GAAG,EAAE;AAC5C;AAEA,CAhBC,OAgBO,CAAC;AACP,SAAO;AACT;AAEA,CAAC;AACC,WAAS,IAAI;AACb,iBAAe,IAAI,MAAM,KAAK,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE;AAC7C,WAAS;AACT,mBAAiB;AACjB,eAAa;AACb,YAAU;AACZ;AAEA,CATC,cASc;AACb,WAAS;AACT,YAAU;AACV,OAAK;AACL,QAAM;AACN,SAAO;AACP,UAAQ;AACR,cAAY;AACZ,WAAS;AACX;AAEA,CAAC;AACC,WAAS;AACT,eAAa;AACb,OAAK,IAAI;AACT,YAAU;AACV,WAAS;AACX;AAEA,CAAC;AACC,SAAO;AACP,UAAQ;AACR;AAAA,IAAY;AAAA,MAAgB,MAAM;AAAA,MAAE,IAAI,eAAe,EAAE;AAAA,MAAE,IAAI,eAAe;AAC9E,iBAAe,IAAI;AACnB,WAAS;AACT,eAAa;AACb,mBAAiB;AACjB,eAAa;AACb,cAAY,IAAI;AAChB,UAAQ,IAAI,MAAM,KAAK,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE;AACtC,aAAW,MAAM,GAAG,YAAY;AAClC;AAEA,WAHa;AAIX;AAAW,eAAW,WAAW;AAAM;AACvC;AAAM,eAAW,WAAW;AAAO;AACrC;AAEA,CAnBC,UAmBU;AACT,SAAO,IAAI;AACX,aAAW,IAAI;AACjB;AAEA,CAAC;AACC,WAAS;AACT,kBAAgB;AAChB,aAAW;AACX,YAAU;AACV,WAAS;AACX;AAEA,CAAC;AACC,aAAW,IAAI;AACf,eAAa,IAAI;AACjB,SAAO,IAAI;AACX,eAAa,IAAI;AACjB,eAAa,EAAE,IAAI,IAAI,KAAK,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE;AACvC;AAEA,CAAC;AACC,aAAW,IAAI;AACf,SAAO,KAAK,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE;AAC3B,eAAa,IAAI;AACjB,eAAa,IAAI;AACnB;AAGA,CAAC;AACC,cAAY;AACZ,UAAQ;AACR,SAAO,KAAK,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE;AAC3B,UAAQ;AACR,WAAS;AACT,iBAAe;AACf,cAAY,IAAI,KAAK;AACrB,WAAS;AACT,eAAa;AACb,mBAAiB;AACnB;AAEA,CAbC,SAaS;AACR,cAAY,KAAK,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE;AAChC,SAAO;AACT;AAGA,CAAC;AACC,YAAU;AACV,OAAK;AACL,QAAM;AACN,SAAO;AACP,UAAQ;AACR,cAAY,KAAK,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE;AAC1B,WAAS;AACT,2BAAiB,KAAK;AAAtB,mBAAiB,KAAK;AACxB;AAEA,CAAC;AACC,QAAM;AACN,WAAS,KAAK;AACd,cAAY;AACd;AAEA,CAAC;AACC,cAAY;AACZ,UAAQ;AACR,WAAS;AACX;AAEA,CAAC;AACC,iBAAe;AACjB;AAEA,CAAC;AACC,WAAS;AACT,kBAAgB;AAClB;AAEA,CAAC;AACC,WAAS;AACT,eAAa;AACb,WAAS,IAAI,aAAa,IAAI;AAC9B,SAAO,KAAK,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE;AAC3B,mBAAiB;AACjB,cAAY,IAAI,IAAI;AACpB,UAAQ;AACR,cAAY;AACZ,SAAO;AACP,cAAY;AACZ,UAAQ;AACR,YAAU;AACV,iBAAe,IAAI;AACnB,UAAQ,IAAI,aAAa,IAAI;AAC7B,eAAa,IAAI;AACjB,YAAU;AACZ;AAEA,CAnBC,QAmBQ;AACP,WAAS;AACT,YAAU;AACV,OAAK;AACL,QAAM;AACN,SAAO;AACP,UAAQ;AACR;AAAA,IAAY;AAAA,MAAgB,KAAK;AAAA,MAAE,WAAW;AAAA,MAAE,KAAK,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,IAAI;AAAA,MAAE;AAC1E,cAAY,IAAI;AAClB;AAEA,CA9BC,QA8BQ,MAAM;AACb,QAAM;AACR;AAEA,CAlCC,QAkCQ;AACP,cAAY,KAAK,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE;AAChC,SAAO,IAAI;AACX,aAAW,WAAW;AACtB,cAAY,IAAI;AAClB;AAEA,CAzCC,QAyCQ,CAAC;AACR,cAAY,KAAK,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE;AAChC,SAAO,IAAI;AACX,eAAa,IAAI;AACjB,cAAY,IAAI;AAClB;AAEA,CAhDC,QAgDQ,CAPC,MAOM;AACd,WAAS;AACT,YAAU;AACV,QAAM;AACN,OAAK;AACL,UAAQ;AACR,SAAO;AACP,cAAY,IAAI;AAClB;AAEA,CAAC;AACC,mBAAiB;AACnB;AAEA,CAJC,WAIW,CAAC;AACX,cAAY,KAAK,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE;AAClC;AAEA,CAAC;AACC,SAAO;AACP,UAAQ;AACR,WAAS;AACT,eAAa;AACb,mBAAiB;AACjB,eAAa;AACb,aAAW,IAAI;AACf,cAAY,IAAI;AAClB;AAEA,CA7EC,QA6EQ,OAAO,CAXf;AAYC,aAAW,MAAM;AACnB;AAEA,CAAC;AACC,eAAa,IAAI;AACjB,aAAW,IAAI;AACf,eAAa,IAAI;AACjB,QAAM;AACN,eAAa;AACb,YAAU;AACV,iBAAe;AACjB;AAEA,CAAC;AACC,SAAO;AACP,UAAQ;AACR,WAAS;AACT,eAAa;AACb,mBAAiB;AACjB,cAAY,UAAU,IAAI;AAC1B,eAAa;AACb,WAAS;AACX;AAEA,CA5CC,WA4CW,CAxCC,SAwCS,CAXrB;AAYC,aAAW,OAAO;AAClB,WAAS;AACX;AAEA,CAjDC,WAiDW,CA7CC,SA6CS,CAhBrB;AAiBC,aAAW,OAAO;AACpB;AAEA,CAAC;AACC,cAAY;AACZ,UAAQ;AACR,WAAS;AACT,cAAY;AACZ,YAAU;AACV,cAAY,WAAW,KAAK;AAC5B,cAAY,KAAK,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE;AAC5B;AAEA,CAVC,OAUO,CA3DK;AA4DX,cAAY;AACd;AAEA,CAAC;AACC,UAAQ;AACV;AAEA,CAAC;AACC,WAAS;AACT,eAAa;AACb,WAAS,IAAI,aAAa,IAAI,aAAa,IAAI,aAAa,IAAI;AAChE,SAAO,KAAK,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE;AAC3B,mBAAiB;AACjB,cAAY,IAAI,IAAI;AACpB,aAAW,IAAI;AACf,YAAU;AACV,iBAAe,IAAI;AACnB,UAAQ,IAAI,aAAa,IAAI;AAC7B,eAAa,IAAI;AACjB,cAAY,KAAK,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE;AAChC,eAAa,IAAI,MAAM,KAAK,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE;AAC7C;AAEA,CAhBC,YAgBY;AACX,WAAS;AACT,YAAU;AACV,QAAM,IAAI;AACV,OAAK;AACL,aAAW,WAAW;AACtB,SAAO;AACP,UAAQ;AACR,cAAY,KAAK,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE;AAChC,iBAAe,IAAI;AACnB,cAAY,IAAI;AAClB;AAEA,CA7BC,YA6BY,MAAM;AACjB,cAAY,IAAI;AAChB,aAAW,WAAW,MAAM,MAAM;AACpC;AAEA,CAlCC,YAkCY;AACX,cAAY,KAAK,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE;AAChC,SAAO,IAAI;AACX,aAAW,WAAW;AACtB,qBAAmB,KAAK,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE;AACvC,cAAY,IAAI;AAClB;AAEA,CA1CC,YA0CY,CAlIH;AAmIR,cAAY,KAAK,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE;AAChC,SAAO,IAAI;AACX,eAAa,IAAI;AACjB,qBAAmB,IAAI;AACvB,cAAY,IAAI;AAClB;AAEA,CAlDC,YAkDY,CA1IH,MA0IU;AAClB,WAAS;AACT,YAAU;AACV,QAAM;AACN,OAAK;AACL,UAAQ;AACR,SAAO;AACP,cAAY,IAAI;AAClB;AAEA,CA5DC,YA4DY,CApJH,MAoJU;AAClB,cAAY,IAAI;AAChB,aAAW,WAAW,MAAM,MAAM;AACpC;AAEA,CAAC;AACC,SAAO;AACP,UAAQ;AACR,WAAS;AACT,eAAa;AACb,mBAAiB;AACjB,eAAa;AACf;AAEA,CAAC;AACC,eAAa;AACb,eAAa;AACb,YAAU;AACV,iBAAe;AACjB;AAEA,CAAC;AACC,WAAS,IAAI;AACb,cAAY,IAAI,MAAM,KAAK,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE;AAC1C,cAAY;AACZ,cAAY,KAAK,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE;AAC1B,2BAAiB,KAAK;AAAtB,mBAAiB,KAAK;AACxB;AAEA,CAAC;AACC,cAAY;AACZ,YAAU;AACV,WAAS;AACX;AAEA,CAAC;AACC,aAAW,IAAI;AACf,SAAO,KAAK,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE;AAC3B,UAAQ,EAAE,EAAE,IAAI,aAAa;AAC7B,eAAa,IAAI;AACnB;AAEA,CAAC;AACC,aAAW,IAAI;AACf,SAAO,KAAK,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE;AAC3B,UAAQ;AACR,eAAa,IAAI;AACnB;AAGA,CAlYC,OAkYO,CAlXC,UAkXU,CA/OlB;AAgPC,WAAS,KAAK;AACd,mBAAiB;AACnB;AAEA,CAvYC,OAuYO,CAvXC,UAuXU,CArIlB;AAsIC,WAAS;AACX;AAEA,CA3YC,OA2YO,CA3XC,UA2XU,CA7JlB;AA8JC,WAAS;AACX;AAGA,CAlRC,WAkRW;AACV,SAAO;AACT;AAEA,CAtRC,WAsRW;AACV,cAAY,KAAK,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE;AAClC;AAEA,CA1RC,WA0RW;AACV,cAAY,KAAK,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE;AAChC,iBAAe;AACjB;AAEA,CA/RC,WA+RW,yBAAyB;AACnC,cAAY,KAAK,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE;AAClC;AAKA,OAAO,CAAC,SAAS,EAAE;AACjB,GAraD;AAsaG,WAAO;AACP,eAAW,WAAW;AACtB,gBAAY,UAAU,KAAK,IAAI,EAAE,MAAM,KAAK;AAC5C,gBAAY,IAAI,EAAE,KAAK,KAAK,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE;AACvC;AAEA,GA5aD,OA4aS,KAAK,CA5ZN;AA6ZL,eAAW,WAAW;AACxB;AAEA,GAhbD,OAgbS,CAhaD;AAiaL,eAAW,WAAW;AACtB,WAAO;AACT;AAEA,GAjaD;AAkaG,aAAS,KAAK;AAChB;AAEA,GAzWD;AA0WG,eAAW;AACb;AAEA,GA1SD;AA2SG,aAAS,KAAK;AACd,eAAW;AACb;AAEA,GA9ND;AA+NG,eAAW;AACb;AAEA,GAlLD;AAmLG,aAAS,KAAK,KAAK,KAAK;AACxB,eAAW;AACb;AACF;AAGA,OAAO,CAAC,SAAS,EAAE,OAAO,IAAI,CAAC,SAAS,EAAE;AACxC,GA9cD;AA+cG,WAAO;AACT;AAEA,GAldD,OAkdS,CAlcD;AAmcL,WAAO;AACT;AAEA,GAlcD;AAmcG,aAAS;AACX;AAEA,GA1YD;AA2YG,eAAW;AACb;AAEA,GA3UD;AA4UG,aAAS,KAAK;AAChB;AAEA,GA9PD;AA+PG,eAAW;AACb;AAEA,GAlND;AAmNG,aAAS,IAAI,KAAK,IAAI;AACtB,eAAW;AACb;AAEA,GA3eD,OA2eS,CA3dD,UA2dY,CAxVpB;AAyVG,aAAS,KAAK;AAChB;AACF;AAGA,OAAO,CAAC,SAAS,EAAE;AACjB,GAlfD;AAmfG,WAAO;AACT;AAEA,GAtfD,OAsfS,CAteD;AAueL,WAAO;AACT;AACF;AAGA,OAAO,CAAC,SAAS,EAAE;AACjB,GA1WD;AAAA,EA2WC,CA1OD;AA2OG,YAAQ;AACR,gBAAY;AACZ,WAAO;AACP,gBAAY;AACZ,YAAQ;AACR,iBAAa;AACf;AAEA,GApXD,QAoXU;AAAA,EACT,CApPD,YAoPc;AACX,aAAS,IAAI,MAAM,KAAK,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE;AACvC,oBAAgB;AAClB;AACF;AAGA,OAAO,CAAC,sBAAsB,EAAE;AAC9B,GAhhBD;AAihBG,gBAAY,UAAU,KAAK,aAAa,GAAG,EAAE,CAAC,EAAE,GAAG,EAAE,EAAE,EAC3C,MAAM,KAAK,aAAa,GAAG,EAAE,CAAC,EAAE,GAAG,EAAE;AACnD;AAEA,GAnRD;AAoRG,gBAAY,WAAW,KAAK,aAAa,GAAG,EAAE,CAAC,EAAE,GAAG,EAAE;AACxD;AAEA,GA3SD;AA4SG,gBAAY,UAAU,KAAK,aAAa,GAAG,EAAE,CAAC,EAAE,GAAG,EAAE;AACvD;AACF;", "names": []}
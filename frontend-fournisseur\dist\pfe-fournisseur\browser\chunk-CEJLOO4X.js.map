{"version": 3, "sources": ["src/app/components/admin/auth/admin-login/admin-login.component.ts", "src/app/components/admin/auth/admin-login/admin-login.component.html"], "sourcesContent": ["import { Component, OnInit } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { FormBuilder, FormGroup, Validators, ReactiveFormsModule } from '@angular/forms';\nimport { Router } from '@angular/router';\nimport { AdminAuthService } from '../../../../services/admin-auth.service';\nimport { LoginRequest, OTPRequest } from '../../../../models/admin.model';\n\n@Component({\n  selector: 'app-admin-login',\n  standalone: true,\n  imports: [CommonModule, ReactiveFormsModule],\n  templateUrl: './admin-login.component.html',\n  styleUrls: ['./admin-login.component.css']\n})\nexport class AdminLoginComponent implements OnInit {\n  loginForm: FormGroup;\n  otpForm: FormGroup;\n  \n  isLoading = false;\n  showOTPForm = false;\n  sessionToken = '';\n  userEmail = '';\n  \n  errorMessage = '';\n  successMessage = '';\n  \n  // Animation states\n  isFormVisible = false;\n  \n  constructor(\n    private formBuilder: FormBuilder,\n    private adminAuthService: AdminAuthService,\n    private router: Router\n  ) {\n    this.loginForm = this.createLoginForm();\n    this.otpForm = this.createOTPForm();\n  }\n\n  ngOnInit(): void {\n    // Vérifier l'état d'authentification actuel\n    const currentUser = this.adminAuthService.getCurrentUser();\n    console.log('🔍 État auth admin au chargement:', currentUser);\n\n    // Si déjà connecté, rediriger (mais seulement si vraiment connecté)\n    if (currentUser) {\n      console.log('Admin déjà connecté, redirection vers dashboard');\n      this.redirectAfterLogin();\n      return;\n    }\n\n    // Animation d'entrée\n    setTimeout(() => {\n      this.isFormVisible = true;\n    }, 100);\n  }\n\n  // Méthode temporaire pour debug\n  forceLogout(): void {\n    this.adminAuthService.forceLogout();\n    console.log('🔒 Déconnexion forcée effectuée');\n    location.reload(); // Recharger la page\n  }\n\n  /**\n   * Créer le formulaire de connexion\n   */\n  private createLoginForm(): FormGroup {\n    return this.formBuilder.group({\n      email: ['', [Validators.required, Validators.minLength(3)]], // Changé pour username\n      password: ['', [Validators.required, Validators.minLength(6)]],\n      rememberMe: [false]\n    });\n  }\n\n  /**\n   * Créer le formulaire OTP\n   */\n  private createOTPForm(): FormGroup {\n    return this.formBuilder.group({\n      otpCode: ['', [Validators.required, Validators.pattern(/^\\d{6}$/)]]\n    });\n  }\n\n  /**\n   * Connexion avec email/mot de passe\n   */\n  onLogin(): void {\n    if (this.loginForm.invalid) {\n      this.markFormGroupTouched(this.loginForm);\n      return;\n    }\n\n    this.isLoading = true;\n    this.errorMessage = '';\n    this.successMessage = '';\n\n    const credentials: LoginRequest = this.loginForm.value;\n\n    this.adminAuthService.login(credentials).subscribe({\n      next: (response) => {\n        this.isLoading = false;\n        \n        if (response.requiresOTP) {\n          this.showOTPForm = true;\n          this.sessionToken = response.sessionToken!;\n          this.userEmail = credentials.email;\n          this.successMessage = response.message || 'Code OTP envoyé';\n        } else {\n          this.successMessage = 'Connexion réussie';\n          // Attendre que l'état d'authentification soit mis à jour\n          setTimeout(() => {\n            this.adminAuthService.isAuthenticated$.subscribe(isAuth => {\n              if (isAuth) {\n                this.redirectAfterLogin();\n              }\n            });\n          }, 500);\n        }\n      },\n      error: (error) => {\n        this.isLoading = false;\n        this.errorMessage = error.message || 'Erreur de connexion';\n      }\n    });\n  }\n\n  /**\n   * Vérification du code OTP\n   */\n  onVerifyOTP(): void {\n    if (this.otpForm.invalid) {\n      this.markFormGroupTouched(this.otpForm);\n      return;\n    }\n\n    this.isLoading = true;\n    this.errorMessage = '';\n\n    const otpRequest: OTPRequest = {\n      email: this.userEmail,\n      otpCode: this.otpForm.value.otpCode,\n      sessionToken: this.sessionToken\n    };\n\n    this.adminAuthService.verifyOTP(otpRequest).subscribe({\n      next: (response) => {\n        this.isLoading = false;\n        this.successMessage = 'Authentification réussie';\n        setTimeout(() => this.redirectAfterLogin(), 1000);\n      },\n      error: (error) => {\n        this.isLoading = false;\n        this.errorMessage = error.message || 'Code OTP invalide';\n      }\n    });\n  }\n\n  /**\n   * Retour au formulaire de connexion\n   */\n  backToLogin(): void {\n    this.showOTPForm = false;\n    this.sessionToken = '';\n    this.userEmail = '';\n    this.otpForm.reset();\n    this.errorMessage = '';\n    this.successMessage = '';\n  }\n\n  /**\n   * Redirection après connexion\n   */\n  private redirectAfterLogin(): void {\n    // Rediriger vers le dashboard admin\n    const redirectUrl = localStorage.getItem('admin_redirect_url') || '/admin/dashboard';\n    localStorage.removeItem('admin_redirect_url');\n    console.log('Redirection admin vers:', redirectUrl);\n    this.router.navigate([redirectUrl]);\n  }\n\n  /**\n   * Marquer tous les champs comme touchés\n   */\n  private markFormGroupTouched(formGroup: FormGroup): void {\n    Object.keys(formGroup.controls).forEach(key => {\n      const control = formGroup.get(key);\n      control?.markAsTouched();\n    });\n  }\n\n  /**\n   * Vérifier si un champ a une erreur\n   */\n  hasFieldError(form: FormGroup, fieldName: string): boolean {\n    const field = form.get(fieldName);\n    return !!(field && field.invalid && field.touched);\n  }\n\n  /**\n   * Obtenir le message d'erreur d'un champ\n   */\n  getFieldError(form: FormGroup, fieldName: string): string {\n    const field = form.get(fieldName);\n    if (field && field.errors && field.touched) {\n      if (field.errors['required']) {\n        return 'Ce champ est obligatoire';\n      }\n      if (field.errors['minlength']) {\n        return `Minimum ${field.errors['minlength'].requiredLength} caractères`;\n      }\n      if (field.errors['pattern']) {\n        if (fieldName === 'otpCode') {\n          return 'Le code doit contenir 6 chiffres';\n        }\n        return 'Format invalide';\n      }\n    }\n    return '';\n  }\n\n\n}\n", "<div class=\"admin-login-page\">\n  <!-- Background avec animations -->\n  <div class=\"login-background\">\n    <div class=\"animated-shapes\">\n      <div class=\"shape shape-1\"></div>\n      <div class=\"shape shape-2\"></div>\n      <div class=\"shape shape-3\"></div>\n      <div class=\"shape shape-4\"></div>\n    </div>\n  </div>\n\n  <!-- Container principal -->\n  <div class=\"login-container\" [class.visible]=\"isFormVisible\">\n    \n    <!-- Logo et titre -->\n    <div class=\"login-header\">\n      <div class=\"logo-container\">\n        <div class=\"logo-icon\">\n          <svg width=\"48\" height=\"48\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" stroke-width=\"2\">\n            <path d=\"M12 2L2 7l10 5 10-5-10-5z\"></path>\n            <path d=\"M2 17l10 5 10-5\"></path>\n            <path d=\"M2 12l10 5 10-5\"></path>\n          </svg>\n        </div>\n        <h1 class=\"logo-title\">Admin Panel</h1>\n      </div>\n      <p class=\"login-subtitle\">Accès sécurisé à l'administration</p>\n    </div>\n\n    <!-- Formulaire de connexion -->\n    <div class=\"login-card\" *ngIf=\"!showOTPForm\">\n      <div class=\"card-header\">\n        <h2>Connexion Administrateur</h2>\n        <p>Connectez-vous avec vos identifiants sécurisés</p>\n      </div>\n\n      <form [formGroup]=\"loginForm\" (ngSubmit)=\"onLogin()\" class=\"login-form\">\n        <!-- Username -->\n        <div class=\"form-group\">\n          <label for=\"email\" class=\"form-label\">\n            <span class=\"label-icon\">👤</span>\n            Nom d'utilisateur\n          </label>\n          <input\n            type=\"text\"\n            id=\"email\"\n            formControlName=\"email\"\n            class=\"form-control\"\n            [class.error]=\"hasFieldError(loginForm, 'email')\"\n            placeholder=\"adminOptiLet\"\n            autocomplete=\"username\"\n          />\n          <div class=\"error-message\" *ngIf=\"hasFieldError(loginForm, 'email')\">\n            {{ getFieldError(loginForm, 'email') }}\n          </div>\n        </div>\n\n        <!-- Mot de passe -->\n        <div class=\"form-group\">\n          <label for=\"password\" class=\"form-label\">\n            <span class=\"label-icon\">🔒</span>\n            Mot de passe\n          </label>\n          <input\n            type=\"password\"\n            id=\"password\"\n            formControlName=\"password\"\n            class=\"form-control\"\n            [class.error]=\"hasFieldError(loginForm, 'password')\"\n            placeholder=\"••••••••\"\n            autocomplete=\"current-password\"\n          />\n          <div class=\"error-message\" *ngIf=\"hasFieldError(loginForm, 'password')\">\n            {{ getFieldError(loginForm, 'password') }}\n          </div>\n        </div>\n\n        <!-- Messages -->\n        <div class=\"message-container\">\n          <div class=\"error-alert\" *ngIf=\"errorMessage\">\n            <span class=\"alert-icon\">⚠️</span>\n            {{ errorMessage }}\n          </div>\n          <div class=\"success-alert\" *ngIf=\"successMessage\">\n            <span class=\"alert-icon\">✅</span>\n            {{ successMessage }}\n          </div>\n        </div>\n\n        <!-- Bouton de connexion -->\n        <button\n          type=\"submit\"\n          class=\"login-button\"\n          [disabled]=\"isLoading\"\n          [class.loading]=\"isLoading\"\n        >\n          <span class=\"button-content\" *ngIf=\"!isLoading\">\n            <span class=\"button-icon\">🚀</span>\n            Se connecter\n          </span>\n          <span class=\"loading-spinner\" *ngIf=\"isLoading\"></span>\n        </button>\n      </form>\n    </div>\n\n    <!-- Formulaire OTP -->\n    <div class=\"login-card otp-card\" *ngIf=\"showOTPForm\">\n      <div class=\"card-header\">\n        <h2>Authentification à deux facteurs</h2>\n        <p>Saisissez le code à 6 chiffres envoyé à <strong>{{ userEmail }}</strong></p>\n      </div>\n\n      <form [formGroup]=\"otpForm\" (ngSubmit)=\"onVerifyOTP()\" class=\"otp-form\">\n        <!-- Code OTP -->\n        <div class=\"form-group\">\n          <label for=\"otpCode\" class=\"form-label\">\n            <span class=\"label-icon\">🔐</span>\n            Code de vérification\n          </label>\n          <input\n            type=\"text\"\n            id=\"otpCode\"\n            formControlName=\"otpCode\"\n            class=\"form-control otp-input\"\n            [class.error]=\"hasFieldError(otpForm, 'otpCode')\"\n            placeholder=\"123456\"\n            maxlength=\"6\"\n            autocomplete=\"one-time-code\"\n          />\n          <div class=\"error-message\" *ngIf=\"hasFieldError(otpForm, 'otpCode')\">\n            {{ getFieldError(otpForm, 'otpCode') }}\n          </div>\n        </div>\n\n        <!-- Messages -->\n        <div class=\"message-container\">\n          <div class=\"error-alert\" *ngIf=\"errorMessage\">\n            <span class=\"alert-icon\">⚠️</span>\n            {{ errorMessage }}\n          </div>\n          <div class=\"success-alert\" *ngIf=\"successMessage\">\n            <span class=\"alert-icon\">✅</span>\n            {{ successMessage }}\n          </div>\n        </div>\n\n        <!-- Boutons -->\n        <div class=\"otp-buttons\">\n          <button\n            type=\"button\"\n            class=\"back-button\"\n            (click)=\"backToLogin()\"\n            [disabled]=\"isLoading\"\n          >\n            ← Retour\n          </button>\n          \n          <button\n            type=\"submit\"\n            class=\"verify-button\"\n            [disabled]=\"isLoading\"\n            [class.loading]=\"isLoading\"\n          >\n            <span class=\"button-content\" *ngIf=\"!isLoading\">\n              <span class=\"button-icon\">🔓</span>\n              Vérifier\n            </span>\n            <span class=\"loading-spinner\" *ngIf=\"isLoading\"></span>\n          </button>\n        </div>\n\n\n      </form>\n    </div>\n\n    <!-- Footer -->\n    <div class=\"login-footer\">\n      <p class=\"footer-text\">\n        © 2024 Optique Vision - Administration sécurisée\n      </p>\n      <div class=\"security-badges\">\n        <span class=\"security-badge\">🔒 SSL</span>\n        <span class=\"security-badge\">🛡️ 2FA</span>\n        <span class=\"security-badge\">📊 Audit</span>\n      </div>\n    </div>\n  </div>\n</div>\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACoDU,IAAA,yBAAA,GAAA,OAAA,EAAA;AACE,IAAA,iBAAA,CAAA;AACF,IAAA,uBAAA;;;;AADE,IAAA,oBAAA;AAAA,IAAA,6BAAA,KAAA,OAAA,cAAA,OAAA,WAAA,OAAA,GAAA,GAAA;;;;;AAmBF,IAAA,yBAAA,GAAA,OAAA,EAAA;AACE,IAAA,iBAAA,CAAA;AACF,IAAA,uBAAA;;;;AADE,IAAA,oBAAA;AAAA,IAAA,6BAAA,KAAA,OAAA,cAAA,OAAA,WAAA,UAAA,GAAA,GAAA;;;;;AAMF,IAAA,yBAAA,GAAA,OAAA,EAAA,EAA8C,GAAA,QAAA,EAAA;AACnB,IAAA,iBAAA,GAAA,cAAA;AAAE,IAAA,uBAAA;AAC3B,IAAA,iBAAA,CAAA;AACF,IAAA,uBAAA;;;;AADE,IAAA,oBAAA,CAAA;AAAA,IAAA,6BAAA,KAAA,OAAA,cAAA,GAAA;;;;;AAEF,IAAA,yBAAA,GAAA,OAAA,EAAA,EAAkD,GAAA,QAAA,EAAA;AACvB,IAAA,iBAAA,GAAA,QAAA;AAAC,IAAA,uBAAA;AAC1B,IAAA,iBAAA,CAAA;AACF,IAAA,uBAAA;;;;AADE,IAAA,oBAAA,CAAA;AAAA,IAAA,6BAAA,KAAA,OAAA,gBAAA,GAAA;;;;;AAWF,IAAA,yBAAA,GAAA,QAAA,EAAA,EAAgD,GAAA,QAAA,EAAA;AACpB,IAAA,iBAAA,GAAA,WAAA;AAAE,IAAA,uBAAA;AAC5B,IAAA,iBAAA,GAAA,gBAAA;AACF,IAAA,uBAAA;;;;;AACA,IAAA,oBAAA,GAAA,QAAA,EAAA;;;;;;AAtEN,IAAA,yBAAA,GAAA,OAAA,EAAA,EAA6C,GAAA,OAAA,EAAA,EAClB,GAAA,IAAA;AACnB,IAAA,iBAAA,GAAA,0BAAA;AAAwB,IAAA,uBAAA;AAC5B,IAAA,yBAAA,GAAA,GAAA;AAAG,IAAA,iBAAA,GAAA,sDAAA;AAA8C,IAAA,uBAAA,EAAI;AAGvD,IAAA,yBAAA,GAAA,QAAA,EAAA;AAA8B,IAAA,qBAAA,YAAA,SAAA,+DAAA;AAAA,MAAA,wBAAA,GAAA;AAAA,YAAA,SAAA,wBAAA;AAAA,aAAA,sBAAY,OAAA,QAAA,CAAS;IAAA,CAAA;AAEjD,IAAA,yBAAA,GAAA,OAAA,EAAA,EAAwB,GAAA,SAAA,EAAA,EACgB,GAAA,QAAA,EAAA;AACX,IAAA,iBAAA,IAAA,WAAA;AAAE,IAAA,uBAAA;AAC3B,IAAA,iBAAA,IAAA,qBAAA;AACF,IAAA,uBAAA;AACA,IAAA,oBAAA,IAAA,SAAA,EAAA;AASA,IAAA,qBAAA,IAAA,4CAAA,GAAA,GAAA,OAAA,EAAA;AAGF,IAAA,uBAAA;AAGA,IAAA,yBAAA,IAAA,OAAA,EAAA,EAAwB,IAAA,SAAA,EAAA,EACmB,IAAA,QAAA,EAAA;AACd,IAAA,iBAAA,IAAA,WAAA;AAAE,IAAA,uBAAA;AAC3B,IAAA,iBAAA,IAAA,gBAAA;AACF,IAAA,uBAAA;AACA,IAAA,oBAAA,IAAA,SAAA,EAAA;AASA,IAAA,qBAAA,IAAA,4CAAA,GAAA,GAAA,OAAA,EAAA;AAGF,IAAA,uBAAA;AAGA,IAAA,yBAAA,IAAA,OAAA,EAAA;AACE,IAAA,qBAAA,IAAA,4CAAA,GAAA,GAAA,OAAA,EAAA,EAA8C,IAAA,4CAAA,GAAA,GAAA,OAAA,EAAA;AAQhD,IAAA,uBAAA;AAGA,IAAA,yBAAA,IAAA,UAAA,EAAA;AAME,IAAA,qBAAA,IAAA,6CAAA,GAAA,GAAA,QAAA,EAAA,EAAgD,IAAA,6CAAA,GAAA,GAAA,QAAA,EAAA;AAKlD,IAAA,uBAAA,EAAS,EACJ;;;;AAlED,IAAA,oBAAA,CAAA;AAAA,IAAA,qBAAA,aAAA,OAAA,SAAA;AAYA,IAAA,oBAAA,CAAA;AAAA,IAAA,sBAAA,SAAA,OAAA,cAAA,OAAA,WAAA,OAAA,CAAA;AAI0B,IAAA,oBAAA;AAAA,IAAA,qBAAA,QAAA,OAAA,cAAA,OAAA,WAAA,OAAA,CAAA;AAgB1B,IAAA,oBAAA,CAAA;AAAA,IAAA,sBAAA,SAAA,OAAA,cAAA,OAAA,WAAA,UAAA,CAAA;AAI0B,IAAA,oBAAA;AAAA,IAAA,qBAAA,QAAA,OAAA,cAAA,OAAA,WAAA,UAAA,CAAA;AAOF,IAAA,oBAAA,CAAA;AAAA,IAAA,qBAAA,QAAA,OAAA,YAAA;AAIE,IAAA,oBAAA;AAAA,IAAA,qBAAA,QAAA,OAAA,cAAA;AAW5B,IAAA,oBAAA;AAAA,IAAA,sBAAA,WAAA,OAAA,SAAA;AADA,IAAA,qBAAA,YAAA,OAAA,SAAA;AAG8B,IAAA,oBAAA;AAAA,IAAA,qBAAA,QAAA,CAAA,OAAA,SAAA;AAIC,IAAA,oBAAA;AAAA,IAAA,qBAAA,QAAA,OAAA,SAAA;;;;;AA6B/B,IAAA,yBAAA,GAAA,OAAA,EAAA;AACE,IAAA,iBAAA,CAAA;AACF,IAAA,uBAAA;;;;AADE,IAAA,oBAAA;AAAA,IAAA,6BAAA,KAAA,OAAA,cAAA,OAAA,SAAA,SAAA,GAAA,GAAA;;;;;AAMF,IAAA,yBAAA,GAAA,OAAA,EAAA,EAA8C,GAAA,QAAA,EAAA;AACnB,IAAA,iBAAA,GAAA,cAAA;AAAE,IAAA,uBAAA;AAC3B,IAAA,iBAAA,CAAA;AACF,IAAA,uBAAA;;;;AADE,IAAA,oBAAA,CAAA;AAAA,IAAA,6BAAA,KAAA,OAAA,cAAA,GAAA;;;;;AAEF,IAAA,yBAAA,GAAA,OAAA,EAAA,EAAkD,GAAA,QAAA,EAAA;AACvB,IAAA,iBAAA,GAAA,QAAA;AAAC,IAAA,uBAAA;AAC1B,IAAA,iBAAA,CAAA;AACF,IAAA,uBAAA;;;;AADE,IAAA,oBAAA,CAAA;AAAA,IAAA,6BAAA,KAAA,OAAA,gBAAA,GAAA;;;;;AAqBA,IAAA,yBAAA,GAAA,QAAA,EAAA,EAAgD,GAAA,QAAA,EAAA;AACpB,IAAA,iBAAA,GAAA,WAAA;AAAE,IAAA,uBAAA;AAC5B,IAAA,iBAAA,GAAA,eAAA;AACF,IAAA,uBAAA;;;;;AACA,IAAA,oBAAA,GAAA,QAAA,EAAA;;;;;;AA7DR,IAAA,yBAAA,GAAA,OAAA,EAAA,EAAqD,GAAA,OAAA,EAAA,EAC1B,GAAA,IAAA;AACnB,IAAA,iBAAA,GAAA,qCAAA;AAAgC,IAAA,uBAAA;AACpC,IAAA,yBAAA,GAAA,GAAA;AAAG,IAAA,iBAAA,GAAA,mDAAA;AAAwC,IAAA,yBAAA,GAAA,QAAA;AAAQ,IAAA,iBAAA,CAAA;AAAe,IAAA,uBAAA,EAAS,EAAI;AAGjF,IAAA,yBAAA,GAAA,QAAA,EAAA;AAA4B,IAAA,qBAAA,YAAA,SAAA,+DAAA;AAAA,MAAA,wBAAA,GAAA;AAAA,YAAA,SAAA,wBAAA;AAAA,aAAA,sBAAY,OAAA,YAAA,CAAa;IAAA,CAAA;AAEnD,IAAA,yBAAA,GAAA,OAAA,EAAA,EAAwB,IAAA,SAAA,EAAA,EACkB,IAAA,QAAA,EAAA;AACb,IAAA,iBAAA,IAAA,WAAA;AAAE,IAAA,uBAAA;AAC3B,IAAA,iBAAA,IAAA,2BAAA;AACF,IAAA,uBAAA;AACA,IAAA,oBAAA,IAAA,SAAA,EAAA;AAUA,IAAA,qBAAA,IAAA,4CAAA,GAAA,GAAA,OAAA,EAAA;AAGF,IAAA,uBAAA;AAGA,IAAA,yBAAA,IAAA,OAAA,EAAA;AACE,IAAA,qBAAA,IAAA,4CAAA,GAAA,GAAA,OAAA,EAAA,EAA8C,IAAA,4CAAA,GAAA,GAAA,OAAA,EAAA;AAQhD,IAAA,uBAAA;AAGA,IAAA,yBAAA,IAAA,OAAA,EAAA,EAAyB,IAAA,UAAA,EAAA;AAIrB,IAAA,qBAAA,SAAA,SAAA,+DAAA;AAAA,MAAA,wBAAA,GAAA;AAAA,YAAA,SAAA,wBAAA;AAAA,aAAA,sBAAS,OAAA,YAAA,CAAa;IAAA,CAAA;AAGtB,IAAA,iBAAA,IAAA,iBAAA;AACF,IAAA,uBAAA;AAEA,IAAA,yBAAA,IAAA,UAAA,EAAA;AAME,IAAA,qBAAA,IAAA,6CAAA,GAAA,GAAA,QAAA,EAAA,EAAgD,IAAA,6CAAA,GAAA,GAAA,QAAA,EAAA;AAKlD,IAAA,uBAAA,EAAS,EACL,EAGD;;;;AA/D8C,IAAA,oBAAA,CAAA;AAAA,IAAA,4BAAA,OAAA,SAAA;AAG/C,IAAA,oBAAA;AAAA,IAAA,qBAAA,aAAA,OAAA,OAAA;AAYA,IAAA,oBAAA,CAAA;AAAA,IAAA,sBAAA,SAAA,OAAA,cAAA,OAAA,SAAA,SAAA,CAAA;AAK0B,IAAA,oBAAA;AAAA,IAAA,qBAAA,QAAA,OAAA,cAAA,OAAA,SAAA,SAAA,CAAA;AAOF,IAAA,oBAAA,CAAA;AAAA,IAAA,qBAAA,QAAA,OAAA,YAAA;AAIE,IAAA,oBAAA;AAAA,IAAA,qBAAA,QAAA,OAAA,cAAA;AAY1B,IAAA,oBAAA,CAAA;AAAA,IAAA,qBAAA,YAAA,OAAA,SAAA;AASA,IAAA,oBAAA,CAAA;AAAA,IAAA,sBAAA,WAAA,OAAA,SAAA;AADA,IAAA,qBAAA,YAAA,OAAA,SAAA;AAG8B,IAAA,oBAAA;AAAA,IAAA,qBAAA,QAAA,CAAA,OAAA,SAAA;AAIC,IAAA,oBAAA;AAAA,IAAA,qBAAA,QAAA,OAAA,SAAA;;;ADzJrC,IAAO,sBAAP,MAAO,qBAAmB;EAgBpB;EACA;EACA;EAjBV;EACA;EAEA,YAAY;EACZ,cAAc;EACd,eAAe;EACf,YAAY;EAEZ,eAAe;EACf,iBAAiB;;EAGjB,gBAAgB;EAEhB,YACU,aACA,kBACA,QAAc;AAFd,SAAA,cAAA;AACA,SAAA,mBAAA;AACA,SAAA,SAAA;AAER,SAAK,YAAY,KAAK,gBAAe;AACrC,SAAK,UAAU,KAAK,cAAa;EACnC;EAEA,WAAQ;AAEN,UAAM,cAAc,KAAK,iBAAiB,eAAc;AACxD,YAAQ,IAAI,+CAAqC,WAAW;AAG5D,QAAI,aAAa;AACf,cAAQ,IAAI,0DAAiD;AAC7D,WAAK,mBAAkB;AACvB;IACF;AAGA,eAAW,MAAK;AACd,WAAK,gBAAgB;IACvB,GAAG,GAAG;EACR;;EAGA,cAAW;AACT,SAAK,iBAAiB,YAAW;AACjC,YAAQ,IAAI,iDAAiC;AAC7C,aAAS,OAAM;EACjB;;;;EAKQ,kBAAe;AACrB,WAAO,KAAK,YAAY,MAAM;MAC5B,OAAO,CAAC,IAAI,CAAC,WAAW,UAAU,WAAW,UAAU,CAAC,CAAC,CAAC;;MAC1D,UAAU,CAAC,IAAI,CAAC,WAAW,UAAU,WAAW,UAAU,CAAC,CAAC,CAAC;MAC7D,YAAY,CAAC,KAAK;KACnB;EACH;;;;EAKQ,gBAAa;AACnB,WAAO,KAAK,YAAY,MAAM;MAC5B,SAAS,CAAC,IAAI,CAAC,WAAW,UAAU,WAAW,QAAQ,SAAS,CAAC,CAAC;KACnE;EACH;;;;EAKA,UAAO;AACL,QAAI,KAAK,UAAU,SAAS;AAC1B,WAAK,qBAAqB,KAAK,SAAS;AACxC;IACF;AAEA,SAAK,YAAY;AACjB,SAAK,eAAe;AACpB,SAAK,iBAAiB;AAEtB,UAAM,cAA4B,KAAK,UAAU;AAEjD,SAAK,iBAAiB,MAAM,WAAW,EAAE,UAAU;MACjD,MAAM,CAAC,aAAY;AACjB,aAAK,YAAY;AAEjB,YAAI,SAAS,aAAa;AACxB,eAAK,cAAc;AACnB,eAAK,eAAe,SAAS;AAC7B,eAAK,YAAY,YAAY;AAC7B,eAAK,iBAAiB,SAAS,WAAW;QAC5C,OAAO;AACL,eAAK,iBAAiB;AAEtB,qBAAW,MAAK;AACd,iBAAK,iBAAiB,iBAAiB,UAAU,YAAS;AACxD,kBAAI,QAAQ;AACV,qBAAK,mBAAkB;cACzB;YACF,CAAC;UACH,GAAG,GAAG;QACR;MACF;MACA,OAAO,CAAC,UAAS;AACf,aAAK,YAAY;AACjB,aAAK,eAAe,MAAM,WAAW;MACvC;KACD;EACH;;;;EAKA,cAAW;AACT,QAAI,KAAK,QAAQ,SAAS;AACxB,WAAK,qBAAqB,KAAK,OAAO;AACtC;IACF;AAEA,SAAK,YAAY;AACjB,SAAK,eAAe;AAEpB,UAAM,aAAyB;MAC7B,OAAO,KAAK;MACZ,SAAS,KAAK,QAAQ,MAAM;MAC5B,cAAc,KAAK;;AAGrB,SAAK,iBAAiB,UAAU,UAAU,EAAE,UAAU;MACpD,MAAM,CAAC,aAAY;AACjB,aAAK,YAAY;AACjB,aAAK,iBAAiB;AACtB,mBAAW,MAAM,KAAK,mBAAkB,GAAI,GAAI;MAClD;MACA,OAAO,CAAC,UAAS;AACf,aAAK,YAAY;AACjB,aAAK,eAAe,MAAM,WAAW;MACvC;KACD;EACH;;;;EAKA,cAAW;AACT,SAAK,cAAc;AACnB,SAAK,eAAe;AACpB,SAAK,YAAY;AACjB,SAAK,QAAQ,MAAK;AAClB,SAAK,eAAe;AACpB,SAAK,iBAAiB;EACxB;;;;EAKQ,qBAAkB;AAExB,UAAM,cAAc,aAAa,QAAQ,oBAAoB,KAAK;AAClE,iBAAa,WAAW,oBAAoB;AAC5C,YAAQ,IAAI,2BAA2B,WAAW;AAClD,SAAK,OAAO,SAAS,CAAC,WAAW,CAAC;EACpC;;;;EAKQ,qBAAqB,WAAoB;AAC/C,WAAO,KAAK,UAAU,QAAQ,EAAE,QAAQ,SAAM;AAC5C,YAAM,UAAU,UAAU,IAAI,GAAG;AACjC,eAAS,cAAa;IACxB,CAAC;EACH;;;;EAKA,cAAc,MAAiB,WAAiB;AAC9C,UAAM,QAAQ,KAAK,IAAI,SAAS;AAChC,WAAO,CAAC,EAAE,SAAS,MAAM,WAAW,MAAM;EAC5C;;;;EAKA,cAAc,MAAiB,WAAiB;AAC9C,UAAM,QAAQ,KAAK,IAAI,SAAS;AAChC,QAAI,SAAS,MAAM,UAAU,MAAM,SAAS;AAC1C,UAAI,MAAM,OAAO,UAAU,GAAG;AAC5B,eAAO;MACT;AACA,UAAI,MAAM,OAAO,WAAW,GAAG;AAC7B,eAAO,WAAW,MAAM,OAAO,WAAW,EAAE,cAAc;MAC5D;AACA,UAAI,MAAM,OAAO,SAAS,GAAG;AAC3B,YAAI,cAAc,WAAW;AAC3B,iBAAO;QACT;AACA,eAAO;MACT;IACF;AACA,WAAO;EACT;;qCA5MW,sBAAmB,4BAAA,WAAA,GAAA,4BAAA,gBAAA,GAAA,4BAAA,MAAA,CAAA;EAAA;yEAAnB,sBAAmB,WAAA,CAAA,CAAA,iBAAA,CAAA,GAAA,OAAA,IAAA,MAAA,GAAA,QAAA,CAAA,CAAA,GAAA,kBAAA,GAAA,CAAA,GAAA,kBAAA,GAAA,CAAA,GAAA,iBAAA,GAAA,CAAA,GAAA,SAAA,SAAA,GAAA,CAAA,GAAA,SAAA,SAAA,GAAA,CAAA,GAAA,SAAA,SAAA,GAAA,CAAA,GAAA,SAAA,SAAA,GAAA,CAAA,GAAA,iBAAA,GAAA,CAAA,GAAA,cAAA,GAAA,CAAA,GAAA,gBAAA,GAAA,CAAA,GAAA,WAAA,GAAA,CAAA,SAAA,MAAA,UAAA,MAAA,WAAA,aAAA,QAAA,QAAA,UAAA,gBAAA,gBAAA,GAAA,GAAA,CAAA,KAAA,2BAAA,GAAA,CAAA,KAAA,iBAAA,GAAA,CAAA,KAAA,iBAAA,GAAA,CAAA,GAAA,YAAA,GAAA,CAAA,GAAA,gBAAA,GAAA,CAAA,SAAA,cAAA,GAAA,MAAA,GAAA,CAAA,SAAA,uBAAA,GAAA,MAAA,GAAA,CAAA,GAAA,cAAA,GAAA,CAAA,GAAA,aAAA,GAAA,CAAA,GAAA,iBAAA,GAAA,CAAA,GAAA,gBAAA,GAAA,CAAA,GAAA,YAAA,GAAA,CAAA,GAAA,aAAA,GAAA,CAAA,GAAA,cAAA,GAAA,YAAA,WAAA,GAAA,CAAA,GAAA,YAAA,GAAA,CAAA,OAAA,SAAA,GAAA,YAAA,GAAA,CAAA,GAAA,YAAA,GAAA,CAAA,QAAA,QAAA,MAAA,SAAA,mBAAA,SAAA,eAAA,gBAAA,gBAAA,YAAA,GAAA,cAAA,GAAA,CAAA,SAAA,iBAAA,GAAA,MAAA,GAAA,CAAA,OAAA,YAAA,GAAA,YAAA,GAAA,CAAA,QAAA,YAAA,MAAA,YAAA,mBAAA,YAAA,eAAA,oDAAA,gBAAA,oBAAA,GAAA,cAAA,GAAA,CAAA,GAAA,mBAAA,GAAA,CAAA,SAAA,eAAA,GAAA,MAAA,GAAA,CAAA,SAAA,iBAAA,GAAA,MAAA,GAAA,CAAA,QAAA,UAAA,GAAA,gBAAA,GAAA,UAAA,GAAA,CAAA,SAAA,kBAAA,GAAA,MAAA,GAAA,CAAA,SAAA,mBAAA,GAAA,MAAA,GAAA,CAAA,GAAA,eAAA,GAAA,CAAA,GAAA,aAAA,GAAA,CAAA,GAAA,YAAA,GAAA,CAAA,GAAA,eAAA,GAAA,CAAA,GAAA,gBAAA,GAAA,CAAA,GAAA,aAAA,GAAA,CAAA,GAAA,iBAAA,GAAA,CAAA,GAAA,cAAA,UAAA,GAAA,CAAA,GAAA,YAAA,GAAA,YAAA,WAAA,GAAA,CAAA,OAAA,WAAA,GAAA,YAAA,GAAA,CAAA,QAAA,QAAA,MAAA,WAAA,mBAAA,WAAA,eAAA,UAAA,aAAA,KAAA,gBAAA,iBAAA,GAAA,gBAAA,WAAA,GAAA,CAAA,GAAA,aAAA,GAAA,CAAA,QAAA,UAAA,GAAA,eAAA,GAAA,SAAA,UAAA,GAAA,CAAA,QAAA,UAAA,GAAA,iBAAA,GAAA,UAAA,CAAA,GAAA,UAAA,SAAA,6BAAA,IAAA,KAAA;AAAA,QAAA,KAAA,GAAA;ACdhC,MAAA,yBAAA,GAAA,OAAA,CAAA,EAA8B,GAAA,OAAA,CAAA,EAEE,GAAA,OAAA,CAAA;AAE1B,MAAA,oBAAA,GAAA,OAAA,CAAA,EAAiC,GAAA,OAAA,CAAA,EACA,GAAA,OAAA,CAAA,EACA,GAAA,OAAA,CAAA;AAEnC,MAAA,uBAAA,EAAM;AAIR,MAAA,yBAAA,GAAA,OAAA,CAAA,EAA6D,GAAA,OAAA,CAAA,EAGjC,GAAA,OAAA,CAAA,EACI,IAAA,OAAA,EAAA;;AAExB,MAAA,yBAAA,IAAA,OAAA,EAAA;AACE,MAAA,oBAAA,IAAA,QAAA,EAAA,EAA2C,IAAA,QAAA,EAAA,EACV,IAAA,QAAA,EAAA;AAEnC,MAAA,uBAAA,EAAM;;AAER,MAAA,yBAAA,IAAA,MAAA,EAAA;AAAuB,MAAA,iBAAA,IAAA,aAAA;AAAW,MAAA,uBAAA,EAAK;AAEzC,MAAA,yBAAA,IAAA,KAAA,EAAA;AAA0B,MAAA,iBAAA,IAAA,+CAAA;AAAiC,MAAA,uBAAA,EAAI;AAIjE,MAAA,qBAAA,IAAA,qCAAA,IAAA,IAAA,OAAA,EAAA,EAA6C,IAAA,qCAAA,IAAA,IAAA,OAAA,EAAA;AAkJ7C,MAAA,yBAAA,IAAA,OAAA,EAAA,EAA0B,IAAA,KAAA,EAAA;AAEtB,MAAA,iBAAA,IAAA,6DAAA;AACF,MAAA,uBAAA;AACA,MAAA,yBAAA,IAAA,OAAA,EAAA,EAA6B,IAAA,QAAA,EAAA;AACE,MAAA,iBAAA,IAAA,eAAA;AAAM,MAAA,uBAAA;AACnC,MAAA,yBAAA,IAAA,QAAA,EAAA;AAA6B,MAAA,iBAAA,IAAA,qBAAA;AAAO,MAAA,uBAAA;AACpC,MAAA,yBAAA,IAAA,QAAA,EAAA;AAA6B,MAAA,iBAAA,IAAA,iBAAA;AAAQ,MAAA,uBAAA,EAAO,EACxC,EACF,EACF;;;AA9KuB,MAAA,oBAAA,CAAA;AAAA,MAAA,sBAAA,WAAA,IAAA,aAAA;AAkBF,MAAA,oBAAA,EAAA;AAAA,MAAA,qBAAA,QAAA,CAAA,IAAA,WAAA;AA4ES,MAAA,oBAAA;AAAA,MAAA,qBAAA,QAAA,IAAA,WAAA;;oBDhG1B,cAAY,MAAE,qBAAmB,oBAAA,sBAAA,iBAAA,sBAAA,oBAAA,oBAAA,eAAA,GAAA,QAAA,CAAA,84OAAA,EAAA,CAAA;;;sEAIhC,qBAAmB,CAAA;UAP/B;uBACW,mBAAiB,YACf,MAAI,SACP,CAAC,cAAc,mBAAmB,GAAC,UAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;GAAA,QAAA,CAAA,ivMAAA,EAAA,CAAA;;;;6EAIjC,qBAAmB,EAAA,WAAA,uBAAA,UAAA,sEAAA,YAAA,GAAA,CAAA;AAAA,GAAA;", "names": []}
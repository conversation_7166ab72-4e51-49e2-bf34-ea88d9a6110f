{"version": 3, "sources": ["angular:styles/component:css;4e6c858038676ae794fa1d8527ce0003d19691fec32dbcd712431cf9eac44261;C:/Users/<USER>/Desktop/STE/PFE/PFE/frontend-fournisseur/src/app/components/referentiels/referentiels.component.ts"], "sourcesContent": ["\n    /* ===== RÉFÉRENTIELS MODERNE - DESIGN SYSTEM ===== */\n    .referentiels-container {\n      padding: var(--spacing-8);\n      max-width: 1400px;\n      margin: 0 auto;\n      background: transparent;\n    }\n\n    /* === HEADER === */\n    .referentiels-header {\n      background: var(--gradient-primary);\n      border-radius: var(--border-radius-2xl);\n      padding: var(--spacing-8);\n      margin-bottom: var(--spacing-8);\n      color: var(--white);\n      box-shadow: var(--shadow-blue-lg);\n      position: relative;\n      overflow: hidden;\n    }\n\n    .referentiels-header::before {\n      content: '';\n      position: absolute;\n      top: 0;\n      left: 0;\n      right: 0;\n      bottom: 0;\n      background: url('data:image/svg+xml,<svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 100 100\"><defs><pattern id=\"dots\" width=\"20\" height=\"20\" patternUnits=\"userSpaceOnUse\"><circle cx=\"10\" cy=\"10\" r=\"1\" fill=\"rgba(255,255,255,0.1)\"/></pattern></defs><rect width=\"100\" height=\"100\" fill=\"url(%23dots)\"/></svg>');\n      opacity: 0.3;\n    }\n\n    .referentiels-title {\n      font-size: var(--font-size-3xl);\n      font-weight: var(--font-weight-extrabold);\n      margin: 0 0 var(--spacing-2) 0;\n      display: flex;\n      align-items: center;\n      gap: var(--spacing-3);\n    }\n\n    /* === ONGLETS MODERNES === */\n    .tabs {\n      display: flex;\n      background: var(--white);\n      border-radius: var(--border-radius-xl);\n      padding: var(--spacing-2);\n      margin-bottom: var(--spacing-8);\n      box-shadow: var(--shadow-base);\n      overflow-x: auto;\n    }\n\n    .tab-button {\n      padding: var(--spacing-3) var(--spacing-6);\n      border: none;\n      background: transparent;\n      cursor: pointer;\n      border-radius: var(--border-radius-lg);\n      font-weight: var(--font-weight-medium);\n      color: var(--gray-600);\n      transition: all var(--transition-fast);\n      white-space: nowrap;\n      display: flex;\n      align-items: center;\n      gap: var(--spacing-2);\n    }\n\n    .tab-button.active {\n      background: var(--primary-600);\n      color: var(--white);\n      box-shadow: var(--shadow-md);\n    }\n\n    .tab-button:hover:not(.active) {\n      background: var(--primary-50);\n      color: var(--primary-600);\n    }\n\n    /* === PANEL HEADER === */\n    .panel-header {\n      display: flex;\n      justify-content: space-between;\n      align-items: center;\n      margin-bottom: var(--spacing-8);\n      background: var(--white);\n      padding: var(--spacing-6);\n      border-radius: var(--border-radius-xl);\n      box-shadow: var(--shadow-base);\n    }\n\n    .panel-header h3 {\n      font-size: var(--font-size-xl);\n      font-weight: var(--font-weight-bold);\n      color: var(--gray-900);\n      margin: 0;\n      display: flex;\n      align-items: center;\n      gap: var(--spacing-2);\n    }\n\n    /* === GRILLE D'ÉLÉMENTS === */\n    .items-grid {\n      display: grid;\n      grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));\n      gap: var(--spacing-6);\n    }\n\n    .item-card {\n      background: var(--white);\n      padding: var(--spacing-6);\n      border-radius: var(--border-radius-xl);\n      box-shadow: var(--shadow-base);\n      transition: all var(--transition-base);\n      position: relative;\n      overflow: hidden;\n    }\n\n    .item-card::before {\n      content: '';\n      position: absolute;\n      top: 0;\n      left: 0;\n      right: 0;\n      height: 4px;\n      background: var(--gradient-primary);\n    }\n\n    .item-card:hover {\n      transform: translateY(-4px);\n      box-shadow: var(--shadow-lg);\n    }\n\n    .item-header {\n      display: flex;\n      justify-content: space-between;\n      align-items: center;\n      margin-bottom: var(--spacing-4);\n    }\n\n    .item-header h4 {\n      font-size: var(--font-size-lg);\n      font-weight: var(--font-weight-bold);\n      color: var(--gray-900);\n      margin: 0;\n    }\n\n    /* === BADGES MODERNES === */\n    .badge {\n      padding: var(--spacing-1) var(--spacing-3);\n      border-radius: var(--border-radius-full);\n      font-size: var(--font-size-xs);\n      font-weight: var(--font-weight-medium);\n      text-transform: uppercase;\n      letter-spacing: 0.025em;\n    }\n\n    .badge-success {\n      background: var(--success-100);\n      color: var(--success-800);\n      border: 1px solid var(--success-200);\n    }\n\n    .badge-warning {\n      background: var(--warning-100);\n      color: var(--warning-800);\n      border: 1px solid var(--warning-200);\n    }\n\n    .badge-danger {\n      background: var(--error-100);\n      color: var(--error-800);\n      border: 1px solid var(--error-200);\n    }\n\n    .item-stats {\n      margin: var(--spacing-4) 0;\n      color: var(--gray-600);\n      font-size: var(--font-size-sm);\n      font-weight: var(--font-weight-medium);\n    }\n\n    .item-actions {\n      display: flex;\n      gap: var(--spacing-2);\n      margin-top: var(--spacing-4);\n    }\n\n    /* === TAUX TVA === */\n    .tva-rate {\n      text-align: center;\n      margin: var(--spacing-4) 0;\n      padding: var(--spacing-4);\n      background: var(--primary-50);\n      border-radius: var(--border-radius-lg);\n    }\n\n    .rate {\n      font-size: var(--font-size-4xl);\n      font-weight: var(--font-weight-extrabold);\n      color: var(--primary-600);\n      display: block;\n    }\n\n    /* === IMAGES === */\n    .marque-logo, .forme-image {\n      text-align: center;\n      margin: var(--spacing-4) 0;\n      padding: var(--spacing-3);\n      background: var(--gray-50);\n      border-radius: var(--border-radius-lg);\n    }\n\n    .logo-img, .forme-img {\n      max-width: 120px;\n      max-height: 80px;\n      object-fit: contain;\n      border-radius: var(--border-radius-md);\n    }\n\n    /* === MODAL MODERNE === */\n    .modal-overlay {\n      position: fixed;\n      top: 0;\n      left: 0;\n      width: 100%;\n      height: 100%;\n      background: rgba(0, 0, 0, 0.6);\n      backdrop-filter: blur(4px);\n      display: flex;\n      align-items: center;\n      justify-content: center;\n      z-index: var(--z-modal);\n      animation: fadeIn 0.2s ease-out;\n    }\n\n    .modal-content {\n      background: var(--white);\n      border-radius: var(--border-radius-2xl);\n      box-shadow: var(--shadow-2xl);\n      max-width: 600px;\n      width: 90%;\n      max-height: 90vh;\n      overflow-y: auto;\n      animation: slideUp 0.3s ease-out;\n    }\n\n    .modal-header {\n      display: flex;\n      justify-content: space-between;\n      align-items: center;\n      padding: var(--spacing-6);\n      border-bottom: 1px solid var(--gray-200);\n      background: linear-gradient(135deg, var(--gray-50) 0%, var(--white) 100%);\n    }\n\n    .modal-header h3 {\n      font-size: var(--font-size-xl);\n      font-weight: var(--font-weight-bold);\n      color: var(--gray-900);\n      margin: 0;\n    }\n\n    .close-btn {\n      background: none;\n      border: none;\n      font-size: var(--font-size-xl);\n      cursor: pointer;\n      color: var(--gray-400);\n      padding: var(--spacing-2);\n      border-radius: var(--border-radius-lg);\n      transition: var(--transition-fast);\n    }\n\n    .close-btn:hover {\n      background: var(--gray-100);\n      color: var(--gray-600);\n    }\n\n    .modal-form {\n      padding: var(--spacing-6);\n    }\n\n    /* === FORMULAIRES === */\n    .form-group {\n      margin-bottom: var(--spacing-6);\n    }\n\n    .form-group label {\n      display: block;\n      margin-bottom: var(--spacing-2);\n      font-weight: var(--font-weight-semibold);\n      color: var(--gray-700);\n      font-size: var(--font-size-sm);\n    }\n\n    .form-control {\n      width: 100%;\n      padding: var(--spacing-3) var(--spacing-4);\n      border: 2px solid var(--gray-300);\n      border-radius: var(--border-radius-lg);\n      font-size: var(--font-size-base);\n      transition: all var(--transition-fast);\n      background: var(--white);\n    }\n\n    .form-control:focus {\n      outline: none;\n      border-color: var(--primary-500);\n      box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);\n      transform: translateY(-1px);\n    }\n\n    .form-actions {\n      display: flex;\n      gap: var(--spacing-3);\n      justify-content: flex-end;\n      padding: var(--spacing-6);\n      border-top: 1px solid var(--gray-200);\n      background: var(--gray-50);\n    }\n\n    /* === BOUTONS === */\n    .btn {\n      padding: var(--spacing-3) var(--spacing-4);\n      border: none;\n      border-radius: var(--border-radius-lg);\n      cursor: pointer;\n      font-weight: var(--font-weight-medium);\n      transition: all var(--transition-fast);\n      display: inline-flex;\n      align-items: center;\n      gap: var(--spacing-2);\n    }\n\n    .btn-primary {\n      background: var(--gradient-primary);\n      color: var(--white);\n      box-shadow: var(--shadow-blue);\n    }\n\n    .btn-primary:hover {\n      background: linear-gradient(135deg, #5a67d8 0%, #667eea 100%);\n      transform: translateY(-1px);\n      box-shadow: var(--shadow-blue-lg);\n    }\n\n    .btn-secondary {\n      background: var(--white);\n      color: var(--gray-700);\n      border: 2px solid var(--gray-300);\n      box-shadow: var(--shadow-sm);\n    }\n\n    .btn-secondary:hover {\n      background: var(--gray-50);\n      border-color: var(--gray-400);\n      transform: translateY(-1px);\n    }\n\n    .btn-danger {\n      background: linear-gradient(135deg, var(--error-600), var(--error-500));\n      color: var(--white);\n      box-shadow: var(--shadow-sm);\n    }\n\n    .btn-danger:hover {\n      background: linear-gradient(135deg, var(--error-700), var(--error-600));\n      transform: translateY(-1px);\n      box-shadow: var(--shadow-md);\n    }\n\n    .btn-sm {\n      padding: var(--spacing-2) var(--spacing-3);\n      font-size: var(--font-size-xs);\n    }\n\n    /* === MODE CONSULTATION === */\n    .read-only-notice {\n      display: flex;\n      align-items: center;\n      gap: var(--spacing-2);\n    }\n\n    .badge-info {\n      background: linear-gradient(135deg, #3b82f6, #1d4ed8);\n      color: var(--white);\n      padding: var(--spacing-2) var(--spacing-3);\n      border-radius: var(--border-radius-full);\n      font-size: var(--font-size-xs);\n      font-weight: 600;\n      display: inline-flex;\n      align-items: center;\n      gap: var(--spacing-1);\n      box-shadow: var(--shadow-sm);\n    }\n\n    .badge-success {\n      background: linear-gradient(135deg, #10b981, #059669);\n      color: var(--white);\n      padding: var(--spacing-2) var(--spacing-3);\n      border-radius: var(--border-radius-full);\n      font-size: var(--font-size-xs);\n      font-weight: 600;\n      display: inline-flex;\n      align-items: center;\n      gap: var(--spacing-1);\n      box-shadow: var(--shadow-sm);\n    }\n\n    .supplier-actions {\n      display: flex;\n      align-items: center;\n      gap: var(--spacing-3);\n      flex-wrap: wrap;\n    }\n\n    .btn-outline-primary {\n      background: transparent;\n      color: var(--primary-600);\n      border: 2px solid var(--primary-600);\n      padding: var(--spacing-2) var(--spacing-4);\n      border-radius: var(--border-radius-lg);\n      font-weight: 600;\n      transition: all 0.3s ease;\n    }\n\n    .btn-outline-primary:hover {\n      background: var(--primary-600);\n      color: var(--white);\n      transform: translateY(-1px);\n      box-shadow: var(--shadow-md);\n    }\n\n    /* === RESPONSIVE === */\n    @media (max-width: 768px) {\n      .referentiels-container {\n        padding: var(--spacing-4);\n      }\n\n      .items-grid {\n        grid-template-columns: 1fr;\n      }\n\n      .panel-header {\n        flex-direction: column;\n        gap: var(--spacing-4);\n        align-items: stretch;\n      }\n\n      .tabs {\n        flex-wrap: wrap;\n      }\n\n      .modal-content {\n        margin: var(--spacing-4);\n        max-width: calc(100vw - 2rem);\n      }\n\n      .item-actions {\n        flex-direction: column;\n      }\n    }\n  "], "mappings": ";AAEI,CAAC;AACC,WAAS,IAAI;AACb,aAAW;AACX,UAAQ,EAAE;AACV,cAAY;AACd;AAGA,CAAC;AACC,cAAY,IAAI;AAChB,iBAAe,IAAI;AACnB,WAAS,IAAI;AACb,iBAAe,IAAI;AACnB,SAAO,IAAI;AACX,cAAY,IAAI;AAChB,YAAU;AACV,YAAU;AACZ;AAEA,CAXC,mBAWmB;AAClB,WAAS;AACT,YAAU;AACV,OAAK;AACL,QAAM;AACN,SAAO;AACP,UAAQ;AACR,cAAY;AACZ,WAAS;AACX;AAEA,CAAC;AACC,aAAW,IAAI;AACf,eAAa,IAAI;AACjB,UAAQ,EAAE,EAAE,IAAI,aAAa;AAC7B,WAAS;AACT,eAAa;AACb,OAAK,IAAI;AACX;AAGA,CAAC;AACC,WAAS;AACT,cAAY,IAAI;AAChB,iBAAe,IAAI;AACnB,WAAS,IAAI;AACb,iBAAe,IAAI;AACnB,cAAY,IAAI;AAChB,cAAY;AACd;AAEA,CAAC;AACC,WAAS,IAAI,aAAa,IAAI;AAC9B,UAAQ;AACR,cAAY;AACZ,UAAQ;AACR,iBAAe,IAAI;AACnB,eAAa,IAAI;AACjB,SAAO,IAAI;AACX,cAAY,IAAI,IAAI;AACpB,eAAa;AACb,WAAS;AACT,eAAa;AACb,OAAK,IAAI;AACX;AAEA,CAfC,UAeU,CAAC;AACV,cAAY,IAAI;AAChB,SAAO,IAAI;AACX,cAAY,IAAI;AAClB;AAEA,CArBC,UAqBU,MAAM,KAAK,CANV;AAOV,cAAY,IAAI;AAChB,SAAO,IAAI;AACb;AAGA,CAAC;AACC,WAAS;AACT,mBAAiB;AACjB,eAAa;AACb,iBAAe,IAAI;AACnB,cAAY,IAAI;AAChB,WAAS,IAAI;AACb,iBAAe,IAAI;AACnB,cAAY,IAAI;AAClB;AAEA,CAXC,aAWa;AACZ,aAAW,IAAI;AACf,eAAa,IAAI;AACjB,SAAO,IAAI;AACX,UAAQ;AACR,WAAS;AACT,eAAa;AACb,OAAK,IAAI;AACX;AAGA,CAAC;AACC,WAAS;AACT,yBAAuB,OAAO,SAAS,EAAE,OAAO,KAAK,EAAE;AACvD,OAAK,IAAI;AACX;AAEA,CAAC;AACC,cAAY,IAAI;AAChB,WAAS,IAAI;AACb,iBAAe,IAAI;AACnB,cAAY,IAAI;AAChB,cAAY,IAAI,IAAI;AACpB,YAAU;AACV,YAAU;AACZ;AAEA,CAVC,SAUS;AACR,WAAS;AACT,YAAU;AACV,OAAK;AACL,QAAM;AACN,SAAO;AACP,UAAQ;AACR,cAAY,IAAI;AAClB;AAEA,CApBC,SAoBS;AACR,aAAW,WAAW;AACtB,cAAY,IAAI;AAClB;AAEA,CAAC;AACC,WAAS;AACT,mBAAiB;AACjB,eAAa;AACb,iBAAe,IAAI;AACrB;AAEA,CAPC,YAOY;AACX,aAAW,IAAI;AACf,eAAa,IAAI;AACjB,SAAO,IAAI;AACX,UAAQ;AACV;AAGA,CAAC;AACC,WAAS,IAAI,aAAa,IAAI;AAC9B,iBAAe,IAAI;AACnB,aAAW,IAAI;AACf,eAAa,IAAI;AACjB,kBAAgB;AAChB,kBAAgB;AAClB;AAEA,CAAC;AACC,cAAY,IAAI;AAChB,SAAO,IAAI;AACX,UAAQ,IAAI,MAAM,IAAI;AACxB;AAEA,CAAC;AACC,cAAY,IAAI;AAChB,SAAO,IAAI;AACX,UAAQ,IAAI,MAAM,IAAI;AACxB;AAEA,CAAC;AACC,cAAY,IAAI;AAChB,SAAO,IAAI;AACX,UAAQ,IAAI,MAAM,IAAI;AACxB;AAEA,CAAC;AACC,UAAQ,IAAI,aAAa;AACzB,SAAO,IAAI;AACX,aAAW,IAAI;AACf,eAAa,IAAI;AACnB;AAEA,CAAC;AACC,WAAS;AACT,OAAK,IAAI;AACT,cAAY,IAAI;AAClB;AAGA,CAAC;AACC,cAAY;AACZ,UAAQ,IAAI,aAAa;AACzB,WAAS,IAAI;AACb,cAAY,IAAI;AAChB,iBAAe,IAAI;AACrB;AAEA,CAAC;AACC,aAAW,IAAI;AACf,eAAa,IAAI;AACjB,SAAO,IAAI;AACX,WAAS;AACX;AAGA,CAAC;AAAa,CAAC;AACb,cAAY;AACZ,UAAQ,IAAI,aAAa;AACzB,WAAS,IAAI;AACb,cAAY,IAAI;AAChB,iBAAe,IAAI;AACrB;AAEA,CAAC;AAAU,CAAC;AACV,aAAW;AACX,cAAY;AACZ,cAAY;AACZ,iBAAe,IAAI;AACrB;AAGA,CAAC;AACC,YAAU;AACV,OAAK;AACL,QAAM;AACN,SAAO;AACP,UAAQ;AACR,cAAY,KAAK,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE;AAC1B,2BAAiB,KAAK;AAAtB,mBAAiB,KAAK;AACtB,WAAS;AACT,eAAa;AACb,mBAAiB;AACjB,WAAS,IAAI;AACb,aAAW,OAAO,KAAK;AACzB;AAEA,CAAC;AACC,cAAY,IAAI;AAChB,iBAAe,IAAI;AACnB,cAAY,IAAI;AAChB,aAAW;AACX,SAAO;AACP,cAAY;AACZ,cAAY;AACZ,aAAW,QAAQ,KAAK;AAC1B;AAEA,CAAC;AACC,WAAS;AACT,mBAAiB;AACjB,eAAa;AACb,WAAS,IAAI;AACb,iBAAe,IAAI,MAAM,IAAI;AAC7B;AAAA,IAAY;AAAA,MAAgB,MAAM;AAAA,MAAE,IAAI,WAAW,EAAE;AAAA,MAAE,IAAI,SAAS;AACtE;AAEA,CATC,aASa;AACZ,aAAW,IAAI;AACf,eAAa,IAAI;AACjB,SAAO,IAAI;AACX,UAAQ;AACV;AAEA,CAAC;AACC,cAAY;AACZ,UAAQ;AACR,aAAW,IAAI;AACf,UAAQ;AACR,SAAO,IAAI;AACX,WAAS,IAAI;AACb,iBAAe,IAAI;AACnB,cAAY,IAAI;AAClB;AAEA,CAXC,SAWS;AACR,cAAY,IAAI;AAChB,SAAO,IAAI;AACb;AAEA,CAAC;AACC,WAAS,IAAI;AACf;AAGA,CAAC;AACC,iBAAe,IAAI;AACrB;AAEA,CAJC,WAIW;AACV,WAAS;AACT,iBAAe,IAAI;AACnB,eAAa,IAAI;AACjB,SAAO,IAAI;AACX,aAAW,IAAI;AACjB;AAEA,CAAC;AACC,SAAO;AACP,WAAS,IAAI,aAAa,IAAI;AAC9B,UAAQ,IAAI,MAAM,IAAI;AACtB,iBAAe,IAAI;AACnB,aAAW,IAAI;AACf,cAAY,IAAI,IAAI;AACpB,cAAY,IAAI;AAClB;AAEA,CAVC,YAUY;AACX,WAAS;AACT,gBAAc,IAAI;AAClB,cAAY,EAAE,EAAE,EAAE,IAAI,KAAK,EAAE,EAAE,GAAG,EAAE,GAAG,EAAE;AACzC,aAAW,WAAW;AACxB;AAEA,CAAC;AACC,WAAS;AACT,OAAK,IAAI;AACT,mBAAiB;AACjB,WAAS,IAAI;AACb,cAAY,IAAI,MAAM,IAAI;AAC1B,cAAY,IAAI;AAClB;AAGA,CAAC;AACC,WAAS,IAAI,aAAa,IAAI;AAC9B,UAAQ;AACR,iBAAe,IAAI;AACnB,UAAQ;AACR,eAAa,IAAI;AACjB,cAAY,IAAI,IAAI;AACpB,WAAS;AACT,eAAa;AACb,OAAK,IAAI;AACX;AAEA,CAAC;AACC,cAAY,IAAI;AAChB,SAAO,IAAI;AACX,cAAY,IAAI;AAClB;AAEA,CANC,WAMW;AACV;AAAA,IAAY;AAAA,MAAgB,MAAhB;AAAA,MAAwB,QAAQ,EAAhC;AAAA,MAAoC,QAAQ;AACxD,aAAW,WAAW;AACtB,cAAY,IAAI;AAClB;AAEA,CAAC;AACC,cAAY,IAAI;AAChB,SAAO,IAAI;AACX,UAAQ,IAAI,MAAM,IAAI;AACtB,cAAY,IAAI;AAClB;AAEA,CAPC,aAOa;AACZ,cAAY,IAAI;AAChB,gBAAc,IAAI;AAClB,aAAW,WAAW;AACxB;AAEA,CAAC;AACC;AAAA,IAAY;AAAA,MAAgB,MAAM;AAAA,MAAE,IAAI,YAAY;AAAA,MAAE,IAAI;AAC1D,SAAO,IAAI;AACX,cAAY,IAAI;AAClB;AAEA,CANC,UAMU;AACT;AAAA,IAAY;AAAA,MAAgB,MAAM;AAAA,MAAE,IAAI,YAAY;AAAA,MAAE,IAAI;AAC1D,aAAW,WAAW;AACtB,cAAY,IAAI;AAClB;AAEA,CAAC;AACC,WAAS,IAAI,aAAa,IAAI;AAC9B,aAAW,IAAI;AACjB;AAGA,CAAC;AACC,WAAS;AACT,eAAa;AACb,OAAK,IAAI;AACX;AAEA,CAAC;AACC;AAAA,IAAY;AAAA,MAAgB,MAAhB;AAAA,MAAwB,OAAxB;AAAA,MAAiC;AAC7C,SAAO,IAAI;AACX,WAAS,IAAI,aAAa,IAAI;AAC9B,iBAAe,IAAI;AACnB,aAAW,IAAI;AACf,eAAa;AACb,WAAS;AACT,eAAa;AACb,OAAK,IAAI;AACT,cAAY,IAAI;AAClB;AAEA,CAhPC;AAiPC;AAAA,IAAY;AAAA,MAAgB,MAAhB;AAAA,MAAwB,OAAxB;AAAA,MAAiC;AAC7C,SAAO,IAAI;AACX,WAAS,IAAI,aAAa,IAAI;AAC9B,iBAAe,IAAI;AACnB,aAAW,IAAI;AACf,eAAa;AACb,WAAS;AACT,eAAa;AACb,OAAK,IAAI;AACT,cAAY,IAAI;AAClB;AAEA,CAAC;AACC,WAAS;AACT,eAAa;AACb,OAAK,IAAI;AACT,aAAW;AACb;AAEA,CAAC;AACC,cAAY;AACZ,SAAO,IAAI;AACX,UAAQ,IAAI,MAAM,IAAI;AACtB,WAAS,IAAI,aAAa,IAAI;AAC9B,iBAAe,IAAI;AACnB,eAAa;AACb,cAAY,IAAI,KAAK;AACvB;AAEA,CAVC,mBAUmB;AAClB,cAAY,IAAI;AAChB,SAAO,IAAI;AACX,aAAW,WAAW;AACtB,cAAY,IAAI;AAClB;AAGA,OAAO,CAAC,SAAS,EAAE;AACjB,GAjbD;AAkbG,aAAS,IAAI;AACf;AAEA,GAlVD;AAmVG,2BAAuB;AACzB;AAEA,GA5WD;AA6WG,oBAAgB;AAChB,SAAK,IAAI;AACT,iBAAa;AACf;AAEA,GAvZD;AAwZG,eAAW;AACb;AAEA,GA1ND;AA2NG,YAAQ,IAAI;AACZ,eAAW,KAAK,MAAM,EAAE;AAC1B;AAEA,GArRD;AAsRG,oBAAgB;AAClB;AACF;", "names": []}
{"version": 3, "sources": ["src/app/components/layout/header/header.component.css"], "sourcesContent": [".header {\n  background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);\n  border-bottom: 1px solid #e2e8f0;\n  padding: 0 24px;\n  height: 64px;\n  display: flex;\n  align-items: center;\n  justify-content: space-between;\n  position: sticky;\n  top: 0;\n  z-index: 1000;\n  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);\n  backdrop-filter: blur(8px);\n  transition: all 0.3s ease;\n}\n\n.header-left {\n  display: flex;\n  align-items: center;\n  gap: 16px;\n}\n\n.sidebar-toggle {\n  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);\n  border: 1px solid #e2e8f0;\n  padding: 8px;\n  cursor: pointer;\n  border-radius: 12px;\n  transition: all 0.3s ease;\n  display: flex;\n  flex-direction: column;\n  gap: 3px;\n  width: 40px;\n  height: 40px;\n  justify-content: center;\n  align-items: center;\n  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);\n}\n\n.sidebar-toggle:hover {\n  background: linear-gradient(135deg, #e2e8f0 0%, #cbd5e1 100%);\n  transform: translateY(-1px);\n  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);\n}\n\n.sidebar-toggle:active {\n  transform: translateY(0);\n  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);\n}\n\n.hamburger-line {\n  width: 18px;\n  height: 2px;\n  background-color: #374151;\n  border-radius: 1px;\n  transition: all 0.3s ease;\n}\n\n.header-title h1 {\n  font-size: 20px;\n  font-weight: 600;\n  color: #111827;\n  margin: 0;\n}\n\n.mock-indicator {\n  background: linear-gradient(45deg, #ff6b35, #f7931e);\n  color: white;\n  padding: 4px 8px;\n  border-radius: 12px;\n  font-size: 0.75rem;\n  font-weight: 600;\n  margin-left: 12px;\n  animation: pulse 2s infinite;\n  box-shadow: 0 2px 4px rgba(255, 107, 53, 0.3);\n}\n\n@keyframes pulse {\n  0% { opacity: 1; }\n  50% { opacity: 0.7; }\n  100% { opacity: 1; }\n}\n\n.header-right {\n  display: flex;\n  align-items: center;\n  gap: 16px;\n}\n\n.header-notifications {\n  position: relative;\n}\n\n.notification-btn {\n  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);\n  border: 1px solid #e2e8f0;\n  padding: 10px;\n  cursor: pointer;\n  border-radius: 12px;\n  transition: all 0.3s ease;\n  color: #6b7280;\n  position: relative;\n  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);\n  width: 40px;\n  height: 40px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n}\n\n.notification-btn:hover {\n  background: linear-gradient(135deg, #e2e8f0 0%, #cbd5e1 100%);\n  color: #374151;\n  transform: translateY(-1px);\n  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);\n}\n\n.notification-btn:active {\n  transform: translateY(0);\n  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);\n}\n\n.notification-badge {\n  position: absolute;\n  top: 4px;\n  right: 4px;\n  background: #ef4444;\n  color: white;\n  font-size: 10px;\n  font-weight: 600;\n  padding: 2px 5px;\n  border-radius: 10px;\n  min-width: 16px;\n  text-align: center;\n  line-height: 1;\n}\n\n.user-menu {\n  position: relative;\n}\n\n.user-menu-trigger {\n  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);\n  border: 1px solid #e2e8f0;\n  padding: 8px 12px;\n  cursor: pointer;\n  border-radius: 12px;\n  transition: all 0.3s ease;\n  display: flex;\n  align-items: center;\n  gap: 12px;\n  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);\n}\n\n.user-menu-trigger:hover {\n  background: linear-gradient(135deg, #e2e8f0 0%, #cbd5e1 100%);\n  transform: translateY(-1px);\n  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);\n}\n\n.user-menu-trigger:active {\n  transform: translateY(0);\n  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);\n}\n\n.user-avatar {\n  width: 36px;\n  height: 36px;\n  border-radius: 50%;\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n  color: white;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  font-weight: 600;\n  font-size: 14px;\n}\n\n.user-info {\n  display: flex;\n  flex-direction: column;\n  align-items: flex-start;\n  text-align: left;\n}\n\n.user-name {\n  font-weight: 500;\n  color: #111827;\n  font-size: 14px;\n  line-height: 1.2;\n}\n\n.user-role {\n  font-size: 12px;\n  color: #6b7280;\n  line-height: 1.2;\n}\n\n.dropdown-arrow {\n  color: #6b7280;\n  transition: transform 0.2s;\n}\n\n.dropdown-arrow.rotated {\n  transform: rotate(180deg);\n}\n\n.user-dropdown {\n  position: absolute;\n  top: 100%;\n  right: 0;\n  background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);\n  border: 1px solid #e2e8f0;\n  border-radius: 16px;\n  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);\n  min-width: 280px;\n  opacity: 0;\n  visibility: hidden;\n  transform: translateY(-10px) scale(0.95);\n  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);\n  z-index: 1001;\n  margin-top: 8px;\n  backdrop-filter: blur(8px);\n}\n\n.user-dropdown.show {\n  opacity: 1;\n  visibility: visible;\n  transform: translateY(0) scale(1);\n}\n\n.dropdown-header {\n  padding: 16px;\n  display: flex;\n  align-items: center;\n  gap: 12px;\n}\n\n.user-avatar-large {\n  width: 48px;\n  height: 48px;\n  border-radius: 50%;\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n  color: white;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  font-weight: 600;\n  font-size: 16px;\n}\n\n.user-details {\n  flex: 1;\n}\n\n.user-details .user-name {\n  font-weight: 600;\n  color: #111827;\n  font-size: 16px;\n  margin-bottom: 2px;\n}\n\n.user-email {\n  font-size: 13px;\n  color: #6b7280;\n}\n\n.dropdown-divider {\n  height: 1px;\n  background: #e5e7eb;\n  margin: 0;\n}\n\n.dropdown-menu {\n  padding: 8px 0;\n}\n\n.dropdown-item {\n  width: 100%;\n  background: none;\n  border: none;\n  padding: 12px 16px;\n  text-align: left;\n  cursor: pointer;\n  transition: all 0.2s ease;\n  display: flex;\n  align-items: center;\n  gap: 12px;\n  font-size: 14px;\n  color: #374151;\n  border-radius: 8px;\n  margin: 2px 8px;\n  width: calc(100% - 16px);\n}\n\n.dropdown-item:hover {\n  background: linear-gradient(135deg, #f1f5f9 0%, #e2e8f0 100%);\n  transform: translateX(4px);\n  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);\n}\n\n.dropdown-item svg {\n  color: #6b7280;\n}\n\n.logout-item {\n  color: #dc2626;\n}\n\n.logout-item:hover {\n  background-color: #fef2f2;\n}\n\n.logout-item svg {\n  color: #dc2626;\n}\n\n/* Responsive */\n\n/* Mobile (< 768px) */\n@media (max-width: 767px) {\n  .header {\n    padding: 0 16px;\n    height: 56px;\n  }\n\n  .header-title h1 {\n    font-size: 18px;\n  }\n\n  .mock-indicator {\n    font-size: 0.7rem;\n    padding: 3px 6px;\n    margin-left: 8px;\n  }\n\n  .user-info {\n    display: none;\n  }\n\n  .user-dropdown {\n    min-width: 240px;\n    right: -8px;\n  }\n\n  .sidebar-toggle {\n    width: 28px;\n    height: 28px;\n  }\n\n  .hamburger-line {\n    width: 16px;\n  }\n\n  .notification-btn,\n  .user-menu-trigger {\n    padding: 6px;\n  }\n\n  .user-avatar {\n    width: 32px;\n    height: 32px;\n    font-size: 12px;\n  }\n}\n\n/* Tablet (768px - 1023px) */\n@media (min-width: 768px) and (max-width: 1023px) {\n  .header {\n    padding: 0 20px;\n    height: 60px;\n  }\n\n  .header-title h1 {\n    font-size: 19px;\n  }\n\n  .mock-indicator {\n    font-size: 0.7rem;\n    margin-left: 10px;\n  }\n\n  .user-dropdown {\n    min-width: 260px;\n  }\n\n  .user-name {\n    font-size: 13px;\n  }\n\n  .user-role {\n    font-size: 11px;\n  }\n}\n\n/* Desktop (> 1024px) */\n@media (min-width: 1024px) {\n  .header {\n    padding: 0 24px;\n    height: 64px;\n  }\n\n  .user-dropdown {\n    min-width: 280px;\n  }\n}\n\n/* Améliorations pour les interactions tactiles */\n@media (max-width: 1023px) {\n  .sidebar-toggle,\n  .notification-btn,\n  .user-menu-trigger,\n  .dropdown-item {\n    min-height: 44px;\n    min-width: 44px;\n  }\n\n  .dropdown-item {\n    padding: 14px 16px;\n    min-width: auto;\n  }\n}\n\n/* Masquer certains éléments sur très petits écrans */\n@media (max-width: 480px) {\n  .header-title h1 {\n    font-size: 16px;\n  }\n\n  .mock-indicator {\n    display: none;\n  }\n\n  .header-notifications {\n    margin-right: 4px;\n  }\n}\n"], "mappings": ";AAAA,CAAC;AACC;AAAA,IAAY;AAAA,MAAgB,MAAhB;AAAA,MAAwB,QAAQ,EAAhC;AAAA,MAAoC,QAAQ;AACxD,iBAAe,IAAI,MAAM;AACzB,WAAS,EAAE;AACX,UAAQ;AACR,WAAS;AACT,eAAa;AACb,mBAAiB;AACjB,YAAU;AACV,OAAK;AACL,WAAS;AACT,cAAY,EAAE,IAAI,IAAI,KAAK,KAAK,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,IAAI,EAAE,EAAE,IAAI,IAAI,KAAK,KAAK,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE;AAC5E,2BAAiB,KAAK;AAAtB,mBAAiB,KAAK;AACtB,cAAY,IAAI,KAAK;AACvB;AAEA,CAAC;AACC,WAAS;AACT,eAAa;AACb,OAAK;AACP;AAEA,CAAC;AACC;AAAA,IAAY;AAAA,MAAgB,MAAhB;AAAA,MAAwB,QAAQ,EAAhC;AAAA,MAAoC,QAAQ;AACxD,UAAQ,IAAI,MAAM;AAClB,WAAS;AACT,UAAQ;AACR,iBAAe;AACf,cAAY,IAAI,KAAK;AACrB,WAAS;AACT,kBAAgB;AAChB,OAAK;AACL,SAAO;AACP,UAAQ;AACR,mBAAiB;AACjB,eAAa;AACb,cAAY,EAAE,IAAI,IAAI,KAAK,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE;AACtC;AAEA,CAjBC,cAiBc;AACb;AAAA,IAAY;AAAA,MAAgB,MAAhB;AAAA,MAAwB,QAAQ,EAAhC;AAAA,MAAoC,QAAQ;AACxD,aAAW,WAAW;AACtB,cAAY,EAAE,IAAI,IAAI,KAAK,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE;AACtC;AAEA,CAvBC,cAuBc;AACb,aAAW,WAAW;AACtB,cAAY,EAAE,IAAI,IAAI,KAAK,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE;AACtC;AAEA,CAAC;AACC,SAAO;AACP,UAAQ;AACR,oBAAkB;AAClB,iBAAe;AACf,cAAY,IAAI,KAAK;AACvB;AAEA,CAAC,aAAa;AACZ,aAAW;AACX,eAAa;AACb,SAAO;AACP,UAAQ;AACV;AAEA,CAAC;AACC;AAAA,IAAY;AAAA,MAAgB,KAAhB;AAAA,MAAuB,OAAvB;AAAA,MAAgC;AAC5C,SAAO;AACP,WAAS,IAAI;AACb,iBAAe;AACf,aAAW;AACX,eAAa;AACb,eAAa;AACb,aAAW,MAAM,GAAG;AACpB,cAAY,EAAE,IAAI,IAAI,KAAK,GAAG,EAAE,GAAG,EAAE,EAAE,EAAE;AAC3C;AAEA,WAJa;AAKX;AAAK,aAAS;AAAG;AACjB;AAAM,aAAS;AAAK;AACpB;AAAO,aAAS;AAAG;AACrB;AAEA,CAAC;AACC,WAAS;AACT,eAAa;AACb,OAAK;AACP;AAEA,CAAC;AACC,YAAU;AACZ;AAEA,CAAC;AACC;AAAA,IAAY;AAAA,MAAgB,MAAhB;AAAA,MAAwB,QAAQ,EAAhC;AAAA,MAAoC,QAAQ;AACxD,UAAQ,IAAI,MAAM;AAClB,WAAS;AACT,UAAQ;AACR,iBAAe;AACf,cAAY,IAAI,KAAK;AACrB,SAAO;AACP,YAAU;AACV,cAAY,EAAE,IAAI,IAAI,KAAK,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE;AACpC,SAAO;AACP,UAAQ;AACR,WAAS;AACT,eAAa;AACb,mBAAiB;AACnB;AAEA,CAjBC,gBAiBgB;AACf;AAAA,IAAY;AAAA,MAAgB,MAAhB;AAAA,MAAwB,QAAQ,EAAhC;AAAA,MAAoC,QAAQ;AACxD,SAAO;AACP,aAAW,WAAW;AACtB,cAAY,EAAE,IAAI,IAAI,KAAK,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE;AACtC;AAEA,CAxBC,gBAwBgB;AACf,aAAW,WAAW;AACtB,cAAY,EAAE,IAAI,IAAI,KAAK,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE;AACtC;AAEA,CAAC;AACC,YAAU;AACV,OAAK;AACL,SAAO;AACP,cAAY;AACZ,SAAO;AACP,aAAW;AACX,eAAa;AACb,WAAS,IAAI;AACb,iBAAe;AACf,aAAW;AACX,cAAY;AACZ,eAAa;AACf;AAEA,CAAC;AACC,YAAU;AACZ;AAEA,CAAC;AACC;AAAA,IAAY;AAAA,MAAgB,MAAhB;AAAA,MAAwB,QAAQ,EAAhC;AAAA,MAAoC,QAAQ;AACxD,UAAQ,IAAI,MAAM;AAClB,WAAS,IAAI;AACb,UAAQ;AACR,iBAAe;AACf,cAAY,IAAI,KAAK;AACrB,WAAS;AACT,eAAa;AACb,OAAK;AACL,cAAY,EAAE,IAAI,IAAI,KAAK,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE;AACtC;AAEA,CAbC,iBAaiB;AAChB;AAAA,IAAY;AAAA,MAAgB,MAAhB;AAAA,MAAwB,QAAQ,EAAhC;AAAA,MAAoC,QAAQ;AACxD,aAAW,WAAW;AACtB,cAAY,EAAE,IAAI,IAAI,KAAK,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE;AACtC;AAEA,CAnBC,iBAmBiB;AAChB,aAAW,WAAW;AACtB,cAAY,EAAE,IAAI,IAAI,KAAK,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE;AACtC;AAEA,CAAC;AACC,SAAO;AACP,UAAQ;AACR,iBAAe;AACf;AAAA,IAAY;AAAA,MAAgB,MAAhB;AAAA,MAAwB,QAAQ,EAAhC;AAAA,MAAoC,QAAQ;AACxD,SAAO;AACP,WAAS;AACT,eAAa;AACb,mBAAiB;AACjB,eAAa;AACb,aAAW;AACb;AAEA,CAAC;AACC,WAAS;AACT,kBAAgB;AAChB,eAAa;AACb,cAAY;AACd;AAEA,CAAC;AACC,eAAa;AACb,SAAO;AACP,aAAW;AACX,eAAa;AACf;AAEA,CAAC;AACC,aAAW;AACX,SAAO;AACP,eAAa;AACf;AAEA,CAAC;AACC,SAAO;AACP,cAAY,UAAU;AACxB;AAEA,CALC,cAKc,CAAC;AACd,aAAW,OAAO;AACpB;AAEA,CAAC;AACC,YAAU;AACV,OAAK;AACL,SAAO;AACP;AAAA,IAAY;AAAA,MAAgB,MAAhB;AAAA,MAAwB,QAAQ,EAAhC;AAAA,MAAoC,QAAQ;AACxD,UAAQ,IAAI,MAAM;AAClB,iBAAe;AACf,cAAY,EAAE,KAAK,KAAK,KAAK,KAAK,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,IAAI,EAAE,EAAE,KAAK,KAAK,KAAK,KAAK,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE;AAChF,aAAW;AACX,WAAS;AACT,cAAY;AACZ,aAAW,WAAW,OAAO,MAAM;AACnC,cAAY,IAAI,KAAK,aAAa,GAAG,EAAE,CAAC,EAAE,GAAG,EAAE;AAC/C,WAAS;AACT,cAAY;AACZ,2BAAiB,KAAK;AAAtB,mBAAiB,KAAK;AACxB;AAEA,CAlBC,aAkBa,CAAC;AACb,WAAS;AACT,cAAY;AACZ,aAAW,WAAW,GAAG,MAAM;AACjC;AAEA,CAAC;AACC,WAAS;AACT,WAAS;AACT,eAAa;AACb,OAAK;AACP;AAEA,CAAC;AACC,SAAO;AACP,UAAQ;AACR,iBAAe;AACf;AAAA,IAAY;AAAA,MAAgB,MAAhB;AAAA,MAAwB,QAAQ,EAAhC;AAAA,MAAoC,QAAQ;AACxD,SAAO;AACP,WAAS;AACT,eAAa;AACb,mBAAiB;AACjB,eAAa;AACb,aAAW;AACb;AAEA,CAAC;AACC,QAAM;AACR;AAEA,CAJC,aAIa,CAtEb;AAuEC,eAAa;AACb,SAAO;AACP,aAAW;AACX,iBAAe;AACjB;AAEA,CAAC;AACC,aAAW;AACX,SAAO;AACT;AAEA,CAAC;AACC,UAAQ;AACR,cAAY;AACZ,UAAQ;AACV;AAEA,CAAC;AACC,WAAS,IAAI;AACf;AAEA,CAAC;AACC,SAAO;AACP,cAAY;AACZ,UAAQ;AACR,WAAS,KAAK;AACd,cAAY;AACZ,UAAQ;AACR,cAAY,IAAI,KAAK;AACrB,WAAS;AACT,eAAa;AACb,OAAK;AACL,aAAW;AACX,SAAO;AACP,iBAAe;AACf,UAAQ,IAAI;AACZ,SAAO,KAAK,KAAK,EAAE;AACrB;AAEA,CAlBC,aAkBa;AACZ;AAAA,IAAY;AAAA,MAAgB,MAAhB;AAAA,MAAwB,QAAQ,EAAhC;AAAA,MAAoC,QAAQ;AACxD,aAAW,WAAW;AACtB,cAAY,EAAE,IAAI,IAAI,KAAK,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE;AACtC;AAEA,CAxBC,cAwBc;AACb,SAAO;AACT;AAEA,CAAC;AACC,SAAO;AACT;AAEA,CAJC,WAIW;AACV,oBAAkB;AACpB;AAEA,CARC,YAQY;AACX,SAAO;AACT;AAKA,OAAO,CAAC,SAAS,EAAE;AACjB,GAjUD;AAkUG,aAAS,EAAE;AACX,YAAQ;AACV;AAEA,GA5QD,aA4Qe;AACZ,eAAW;AACb;AAEA,GAzQD;AA0QG,eAAW;AACX,aAAS,IAAI;AACb,iBAAa;AACf;AAEA,GA9JD;AA+JG,aAAS;AACX;AAEA,GArID;AAsIG,eAAW;AACX,WAAO;AACT;AAEA,GAnUD;AAoUG,WAAO;AACP,YAAQ;AACV;AAEA,GA5SD;AA6SG,WAAO;AACT;AAEA,GArQD;AAAA,EAsQC,CAtND;AAuNG,aAAS;AACX;AAEA,GAlMD;AAmMG,WAAO;AACP,YAAQ;AACR,eAAW;AACb;AACF;AAGA,OAAO,CAAC,SAAS,EAAE,OAAO,IAAI,CAAC,SAAS,EAAE;AACxC,GAhXD;AAiXG,aAAS,EAAE;AACX,YAAQ;AACV;AAEA,GA3TD,aA2Te;AACZ,eAAW;AACb;AAEA,GAxTD;AAyTG,eAAW;AACX,iBAAa;AACf;AAEA,GA/KD;AAgLG,eAAW;AACb;AAEA,GAzMD;AA0MG,eAAW;AACb;AAEA,GAtMD;AAuMG,eAAW;AACb;AACF;AAGA,OAAO,CAAC,SAAS,EAAE;AACjB,GA7YD;AA8YG,aAAS,EAAE;AACX,YAAQ;AACV;AAEA,GAnMD;AAoMG,eAAW;AACb;AACF;AAGA,OAAO,CAAC,SAAS,EAAE;AACjB,GAnYD;AAAA,EAoYC,CA7TD;AAAA,EA8TC,CA9QD;AAAA,EA+QC,CAvID;AAwIG,gBAAY;AACZ,eAAW;AACb;AAEA,GA5ID;AA6IG,aAAS,KAAK;AACd,eAAW;AACb;AACF;AAGA,OAAO,CAAC,SAAS,EAAE;AACjB,GA/WD,aA+We;AACZ,eAAW;AACb;AAEA,GA5WD;AA6WG,aAAS;AACX;AAEA,GAxVD;AAyVG,kBAAc;AAChB;AACF;", "names": []}
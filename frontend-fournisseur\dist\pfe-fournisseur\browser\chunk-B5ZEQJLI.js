import {
  FormsModule,
  NgSelectOption,
  ɵNgSelectMultipleOption
} from "./chunk-HQBVYEOO.js";
import {
  AdminService
} from "./chunk-EFJVWLOV.js";
import "./chunk-GFHHPDQ6.js";
import "./chunk-7JDDWGD3.js";
import {
  CommonModule,
  Component,
  NgClass,
  NgForOf,
  NgIf,
  __spreadProps,
  __spreadValues,
  computed,
  setClassMetadata,
  signal,
  ɵsetClassDebugInfo,
  ɵɵadvance,
  ɵɵclassProp,
  ɵɵdefineComponent,
  ɵɵdirectiveInject,
  ɵɵelement,
  ɵɵelementEnd,
  ɵɵelementStart,
  ɵɵgetCurrentView,
  ɵɵlistener,
  ɵɵnextContext,
  ɵɵproperty,
  ɵɵresetView,
  ɵɵrestoreView,
  ɵɵtemplate,
  ɵɵtext,
  ɵɵtextInterpolate,
  ɵɵtextInterpolate1,
  ɵɵtextInterpolate2,
  ɵɵtextInterpolate3
} from "./chunk-UBZQS7JS.js";

// src/app/components/admin/order-management/order-management.component.ts
function OrderManagementComponent_option_21_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "option", 28);
    \u0275\u0275text(1);
    \u0275\u0275elementEnd();
  }
  if (rf & 2) {
    const statut_r1 = ctx.$implicit;
    const ctx_r1 = \u0275\u0275nextContext();
    \u0275\u0275property("value", statut_r1);
    \u0275\u0275advance();
    \u0275\u0275textInterpolate(ctx_r1.getStatusText(statut_r1));
  }
}
function OrderManagementComponent_div_24_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "div", 29);
    \u0275\u0275element(1, "i", 30);
    \u0275\u0275text(2);
    \u0275\u0275elementEnd();
  }
  if (rf & 2) {
    const ctx_r1 = \u0275\u0275nextContext();
    \u0275\u0275advance(2);
    \u0275\u0275textInterpolate1(" ", ctx_r1.error(), " ");
  }
}
function OrderManagementComponent_div_25_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "div", 31);
    \u0275\u0275element(1, "div", 32);
    \u0275\u0275elementStart(2, "p");
    \u0275\u0275text(3, "Chargement des commandes...");
    \u0275\u0275elementEnd()();
  }
}
function OrderManagementComponent_div_26_div_1_div_20_div_4_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "div", 54)(1, "p");
    \u0275\u0275text(2, "Aucune commande fournisseur associ\xE9e");
    \u0275\u0275elementEnd()();
  }
}
function OrderManagementComponent_div_26_div_1_div_20_div_5_div_14_div_1_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "div", 68);
    \u0275\u0275element(1, "i", 69);
    \u0275\u0275text(2);
    \u0275\u0275elementEnd();
  }
  if (rf & 2) {
    const cmdFournisseur_r5 = \u0275\u0275nextContext(2).$implicit;
    const ctx_r1 = \u0275\u0275nextContext(4);
    \u0275\u0275advance(2);
    \u0275\u0275textInterpolate1(" Livraison pr\xE9vue: ", ctx_r1.formatDate(cmdFournisseur_r5.dateLivraison), " ");
  }
}
function OrderManagementComponent_div_26_div_1_div_20_div_5_div_14_div_2_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "div", 70);
    \u0275\u0275element(1, "i", 71);
    \u0275\u0275text(2);
    \u0275\u0275elementEnd();
  }
  if (rf & 2) {
    const cmdFournisseur_r5 = \u0275\u0275nextContext(2).$implicit;
    \u0275\u0275advance(2);
    \u0275\u0275textInterpolate1(" Bon de livraison: ", cmdFournisseur_r5.numeroBonLivraison, " ");
  }
}
function OrderManagementComponent_div_26_div_1_div_20_div_5_div_14_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "div", 65);
    \u0275\u0275template(1, OrderManagementComponent_div_26_div_1_div_20_div_5_div_14_div_1_Template, 3, 1, "div", 66)(2, OrderManagementComponent_div_26_div_1_div_20_div_5_div_14_div_2_Template, 3, 1, "div", 67);
    \u0275\u0275elementEnd();
  }
  if (rf & 2) {
    const cmdFournisseur_r5 = \u0275\u0275nextContext().$implicit;
    \u0275\u0275advance();
    \u0275\u0275property("ngIf", cmdFournisseur_r5.dateLivraison);
    \u0275\u0275advance();
    \u0275\u0275property("ngIf", cmdFournisseur_r5.numeroBonLivraison);
  }
}
function OrderManagementComponent_div_26_div_1_div_20_div_5_div_16_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "div", 72)(1, "div", 73)(2, "span", 74);
    \u0275\u0275text(3);
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(4, "span", 75);
    \u0275\u0275text(5);
    \u0275\u0275elementEnd()();
    \u0275\u0275elementStart(6, "div", 76)(7, "span", 77);
    \u0275\u0275text(8);
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(9, "span", 78);
    \u0275\u0275text(10);
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(11, "span", 79);
    \u0275\u0275text(12);
    \u0275\u0275elementEnd()()();
  }
  if (rf & 2) {
    const ligne_r6 = ctx.$implicit;
    const ctx_r1 = \u0275\u0275nextContext(5);
    \u0275\u0275advance(3);
    \u0275\u0275textInterpolate(ligne_r6.produitNom);
    \u0275\u0275advance(2);
    \u0275\u0275textInterpolate(ligne_r6.referenceProduit);
    \u0275\u0275advance(3);
    \u0275\u0275textInterpolate1("", ligne_r6.quantite, "x");
    \u0275\u0275advance(2);
    \u0275\u0275textInterpolate(ctx_r1.formatPrice(ligne_r6.prixUnitaire));
    \u0275\u0275advance(2);
    \u0275\u0275textInterpolate(ctx_r1.formatPrice(ligne_r6.montantLigne));
  }
}
function OrderManagementComponent_div_26_div_1_div_20_div_5_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "div", 55)(1, "div", 56)(2, "div", 57)(3, "strong");
    \u0275\u0275text(4);
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(5, "span", 58);
    \u0275\u0275text(6);
    \u0275\u0275elementEnd()();
    \u0275\u0275elementStart(7, "div", 59)(8, "span", 60);
    \u0275\u0275text(9);
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(10, "span", 43);
    \u0275\u0275text(11);
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(12, "span", 61);
    \u0275\u0275text(13);
    \u0275\u0275elementEnd()()();
    \u0275\u0275template(14, OrderManagementComponent_div_26_div_1_div_20_div_5_div_14_Template, 3, 2, "div", 62);
    \u0275\u0275elementStart(15, "div", 63);
    \u0275\u0275template(16, OrderManagementComponent_div_26_div_1_div_20_div_5_div_16_Template, 13, 5, "div", 64);
    \u0275\u0275elementEnd()();
  }
  if (rf & 2) {
    const cmdFournisseur_r5 = ctx.$implicit;
    const ctx_r1 = \u0275\u0275nextContext(4);
    \u0275\u0275advance(4);
    \u0275\u0275textInterpolate(cmdFournisseur_r5.fournisseurNom);
    \u0275\u0275advance(2);
    \u0275\u0275textInterpolate(cmdFournisseur_r5.fournisseurEmail);
    \u0275\u0275advance(3);
    \u0275\u0275textInterpolate(cmdFournisseur_r5.reference);
    \u0275\u0275advance();
    \u0275\u0275property("ngClass", ctx_r1.getCommandeFournisseurStatusClass(cmdFournisseur_r5.statut));
    \u0275\u0275advance();
    \u0275\u0275textInterpolate1(" ", ctx_r1.getCommandeFournisseurStatusText(cmdFournisseur_r5.statut), " ");
    \u0275\u0275advance(2);
    \u0275\u0275textInterpolate(ctx_r1.formatPrice(cmdFournisseur_r5.montantTotal));
    \u0275\u0275advance();
    \u0275\u0275property("ngIf", cmdFournisseur_r5.dateLivraison || cmdFournisseur_r5.numeroBonLivraison);
    \u0275\u0275advance(2);
    \u0275\u0275property("ngForOf", cmdFournisseur_r5.lignes);
  }
}
function OrderManagementComponent_div_26_div_1_div_20_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "div", 49)(1, "h4", 50);
    \u0275\u0275element(2, "i", 51);
    \u0275\u0275text(3);
    \u0275\u0275elementEnd();
    \u0275\u0275template(4, OrderManagementComponent_div_26_div_1_div_20_div_4_Template, 3, 0, "div", 52)(5, OrderManagementComponent_div_26_div_1_div_20_div_5_Template, 17, 8, "div", 53);
    \u0275\u0275elementEnd();
  }
  if (rf & 2) {
    const commande_r4 = \u0275\u0275nextContext().$implicit;
    \u0275\u0275advance(3);
    \u0275\u0275textInterpolate1(" Commandes Fournisseur (", commande_r4.commandesFournisseur.length, ") ");
    \u0275\u0275advance();
    \u0275\u0275property("ngIf", commande_r4.commandesFournisseur.length === 0);
    \u0275\u0275advance();
    \u0275\u0275property("ngForOf", commande_r4.commandesFournisseur);
  }
}
function OrderManagementComponent_div_26_div_1_Template(rf, ctx) {
  if (rf & 1) {
    const _r3 = \u0275\u0275getCurrentView();
    \u0275\u0275elementStart(0, "div", 36)(1, "div", 37)(2, "div", 38)(3, "h3", 39);
    \u0275\u0275text(4);
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(5, "div", 40)(6, "strong");
    \u0275\u0275text(7);
    \u0275\u0275elementEnd();
    \u0275\u0275text(8);
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(9, "div", 41)(10, "span", 42);
    \u0275\u0275text(11);
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(12, "span", 43);
    \u0275\u0275text(13);
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(14, "span", 44);
    \u0275\u0275text(15);
    \u0275\u0275elementEnd()()();
    \u0275\u0275elementStart(16, "div", 45)(17, "button", 46);
    \u0275\u0275listener("click", function OrderManagementComponent_div_26_div_1_Template_button_click_17_listener() {
      const commande_r4 = \u0275\u0275restoreView(_r3).$implicit;
      const ctx_r1 = \u0275\u0275nextContext(2);
      return \u0275\u0275resetView(ctx_r1.viewOrderDetails(commande_r4));
    });
    \u0275\u0275element(18, "i", 47);
    \u0275\u0275text(19);
    \u0275\u0275elementEnd()()();
    \u0275\u0275template(20, OrderManagementComponent_div_26_div_1_div_20_Template, 6, 3, "div", 48);
    \u0275\u0275elementEnd();
  }
  if (rf & 2) {
    const commande_r4 = ctx.$implicit;
    const ctx_r1 = \u0275\u0275nextContext(2);
    \u0275\u0275advance(4);
    \u0275\u0275textInterpolate(commande_r4.numeroCommande);
    \u0275\u0275advance(3);
    \u0275\u0275textInterpolate(commande_r4.clientNom);
    \u0275\u0275advance();
    \u0275\u0275textInterpolate1(" - ", commande_r4.clientEmail, " ");
    \u0275\u0275advance(3);
    \u0275\u0275textInterpolate(ctx_r1.formatDate(commande_r4.dateCommande));
    \u0275\u0275advance();
    \u0275\u0275property("ngClass", ctx_r1.getStatusClass(commande_r4.statut));
    \u0275\u0275advance();
    \u0275\u0275textInterpolate1(" ", ctx_r1.getStatusText(commande_r4.statut), " ");
    \u0275\u0275advance(2);
    \u0275\u0275textInterpolate(ctx_r1.formatPrice(commande_r4.montantTotal));
    \u0275\u0275advance(2);
    \u0275\u0275classProp("active", ctx_r1.isOrderExpanded(commande_r4.id));
    \u0275\u0275advance();
    \u0275\u0275classProp("rotated", ctx_r1.isOrderExpanded(commande_r4.id));
    \u0275\u0275advance();
    \u0275\u0275textInterpolate1(" ", ctx_r1.isOrderExpanded(commande_r4.id) ? "Masquer" : "Voir", " d\xE9tails ");
    \u0275\u0275advance();
    \u0275\u0275property("ngIf", ctx_r1.isOrderExpanded(commande_r4.id));
  }
}
function OrderManagementComponent_div_26_div_2_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "div", 80);
    \u0275\u0275element(1, "i", 4);
    \u0275\u0275elementStart(2, "h3");
    \u0275\u0275text(3, "Aucune commande trouv\xE9e");
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(4, "p");
    \u0275\u0275text(5, "Aucune commande ne correspond aux crit\xE8res de recherche.");
    \u0275\u0275elementEnd()();
  }
}
function OrderManagementComponent_div_26_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "div", 33);
    \u0275\u0275template(1, OrderManagementComponent_div_26_div_1_Template, 21, 13, "div", 34)(2, OrderManagementComponent_div_26_div_2_Template, 6, 0, "div", 35);
    \u0275\u0275elementEnd();
  }
  if (rf & 2) {
    const ctx_r1 = \u0275\u0275nextContext();
    \u0275\u0275advance();
    \u0275\u0275property("ngForOf", ctx_r1.filteredCommandesAvecFournisseur());
    \u0275\u0275advance();
    \u0275\u0275property("ngIf", ctx_r1.commandesAvecFournisseur().length === 0);
  }
}
function OrderManagementComponent_div_27_Template(rf, ctx) {
  if (rf & 1) {
    const _r7 = \u0275\u0275getCurrentView();
    \u0275\u0275elementStart(0, "div", 81)(1, "div", 82);
    \u0275\u0275text(2);
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(3, "div", 83)(4, "button", 84);
    \u0275\u0275listener("click", function OrderManagementComponent_div_27_Template_button_click_4_listener() {
      \u0275\u0275restoreView(_r7);
      const ctx_r1 = \u0275\u0275nextContext();
      return \u0275\u0275resetView(ctx_r1.onPageChange(ctx_r1.currentPage() - 1));
    });
    \u0275\u0275element(5, "i", 85);
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(6, "span", 86);
    \u0275\u0275text(7);
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(8, "button", 84);
    \u0275\u0275listener("click", function OrderManagementComponent_div_27_Template_button_click_8_listener() {
      \u0275\u0275restoreView(_r7);
      const ctx_r1 = \u0275\u0275nextContext();
      return \u0275\u0275resetView(ctx_r1.onPageChange(ctx_r1.currentPage() + 1));
    });
    \u0275\u0275element(9, "i", 87);
    \u0275\u0275elementEnd()()();
  }
  if (rf & 2) {
    const ctx_r1 = \u0275\u0275nextContext();
    \u0275\u0275advance(2);
    \u0275\u0275textInterpolate3(" Affichage de ", (ctx_r1.currentPage() - 1) * ctx_r1.pageSize() + 1, " \xE0 ", ctx_r1.Math.min(ctx_r1.currentPage() * ctx_r1.pageSize(), ctx_r1.totalItems()), " sur ", ctx_r1.totalItems(), " commandes ");
    \u0275\u0275advance(2);
    \u0275\u0275property("disabled", ctx_r1.currentPage() === 1);
    \u0275\u0275advance(3);
    \u0275\u0275textInterpolate2(" Page ", ctx_r1.currentPage(), " sur ", ctx_r1.totalPages(), " ");
    \u0275\u0275advance();
    \u0275\u0275property("disabled", ctx_r1.currentPage() === ctx_r1.totalPages());
  }
}
var OrderManagementComponent = class _OrderManagementComponent {
  adminService;
  // Angular 19: Signals
  commandesAvecFournisseur = signal([]);
  isLoading = signal(false);
  error = signal("");
  searchTerm = signal("");
  selectedStatus = signal("");
  dateDebut = signal("");
  dateFin = signal("");
  currentPage = signal(1);
  pageSize = signal(10);
  totalItems = signal(0);
  expandedOrderId = signal(null);
  // Computed signals pour les commandes filtrées
  filteredCommandesAvecFournisseur = computed(() => {
    const orders = this.commandesAvecFournisseur();
    const search = this.searchTerm().toLowerCase();
    const status = this.selectedStatus();
    const dateDebut = this.dateDebut();
    const dateFin = this.dateFin();
    return orders.filter((order) => {
      const matchesSearch = !search || order.numeroCommande.toLowerCase().includes(search) || order.clientNom.toLowerCase().includes(search) || order.clientEmail.toLowerCase().includes(search);
      const matchesStatus = !status || order.statut === status;
      let matchesDate = true;
      if (dateDebut || dateFin) {
        const orderDate = new Date(order.dateCommande);
        if (dateDebut) {
          const startDate = new Date(dateDebut);
          matchesDate = matchesDate && orderDate >= startDate;
        }
        if (dateFin) {
          const endDate = new Date(dateFin);
          endDate.setHours(23, 59, 59, 999);
          matchesDate = matchesDate && orderDate <= endDate;
        }
      }
      return matchesSearch && matchesStatus && matchesDate;
    });
  });
  statuts = ["EnAttente", "Confirmee", "EnPreparation", "Expediee", "Livree", "Annulee"];
  constructor(adminService) {
    this.adminService = adminService;
  }
  ngOnInit() {
    this.loadCommandes();
  }
  loadCommandes() {
    this.isLoading.set(true);
    this.error.set("");
    const params = {
      page: this.currentPage(),
      pageSize: this.pageSize(),
      statut: this.selectedStatus() || void 0,
      dateDebut: this.dateDebut() ? new Date(this.dateDebut()) : void 0,
      dateFin: this.dateFin() ? new Date(this.dateFin()) : void 0
    };
    this.adminService.getCommandes(params).subscribe({
      next: (response) => {
        if (response.success && response.data) {
          const commandesAvecFournisseur = response.data.map((commande) => __spreadProps(__spreadValues({}, commande), {
            fournisseurNom: commande.fournisseurNom || "Non sp\xE9cifi\xE9",
            commandesFournisseur: commande.commandesFournisseur || []
          }));
          this.commandesAvecFournisseur.set(commandesAvecFournisseur);
          this.totalItems.set(response.total || response.data.length);
        } else {
          this.commandesAvecFournisseur.set([]);
          this.totalItems.set(0);
        }
        this.isLoading.set(false);
      },
      error: (error) => {
        console.error("Erreur lors du chargement des commandes:", error);
        this.error.set("Erreur lors du chargement des commandes");
        this.isLoading.set(false);
      }
    });
  }
  // Méthodes simplifiées - plus besoin de basculer entre les vues
  toggleDetailedView() {
  }
  // Method called by template - alias for toggleDetailedView
  toggleView() {
  }
  onSearch() {
  }
  onFilterChange() {
  }
  onDateFilterChange() {
  }
  clearFilters() {
    this.searchTerm.set("");
    this.selectedStatus.set("");
    this.dateDebut.set("");
    this.dateFin.set("");
  }
  refresh() {
    this.currentPage.set(1);
    this.loadCommandes();
  }
  viewOrderDetails(commande) {
    const currentExpanded = this.expandedOrderId();
    this.expandedOrderId.set(currentExpanded === commande.id ? null : commande.id);
  }
  isOrderExpanded(orderId) {
    return this.expandedOrderId() === orderId;
  }
  getTotalCommandes() {
    return this.filteredCommandesAvecFournisseur().length;
  }
  getTotalAmount() {
    const total = this.filteredCommandesAvecFournisseur().reduce((sum, c) => sum + c.montantTotal, 0);
    return this.formatPrice(total);
  }
  exportOrders() {
    alert("Fonctionnalit\xE9 d'export en cours de d\xE9veloppement");
  }
  formatPrice(price) {
    return new Intl.NumberFormat("fr-TN", {
      style: "currency",
      currency: "TND"
    }).format(price);
  }
  formatDate(date) {
    return new Date(date).toLocaleDateString("fr-FR", {
      year: "numeric",
      month: "short",
      day: "numeric",
      hour: "2-digit",
      minute: "2-digit"
    });
  }
  getStatusText(statut) {
    switch (statut) {
      case "EnAttente":
        return "En attente";
      case "Confirmee":
        return "Confirm\xE9e";
      case "EnPreparation":
        return "En pr\xE9paration";
      case "Expediee":
        return "Exp\xE9di\xE9e";
      case "Livree":
        return "Livr\xE9e";
      case "Annulee":
        return "Annul\xE9e";
      default:
        return statut;
    }
  }
  getStatusClass(statut) {
    switch (statut) {
      case "EnAttente":
        return "status-pending";
      case "Confirmee":
        return "status-confirmed";
      case "EnPreparation":
        return "status-preparing";
      case "Expediee":
        return "status-shipped";
      case "Livree":
        return "status-delivered";
      case "Annulee":
        return "status-cancelled";
      default:
        return "status-unknown";
    }
  }
  getCommandeFournisseurStatusText(statut) {
    switch (statut) {
      case "EnAttente":
        return "En attente";
      case "Confirmee":
        return "Confirm\xE9e";
      case "EnPreparation":
        return "En pr\xE9paration";
      case "Expediee":
        return "Exp\xE9di\xE9e";
      case "Livree":
        return "Livr\xE9e";
      case "Annulee":
        return "Annul\xE9e";
      default:
        return statut;
    }
  }
  getCommandeFournisseurStatusClass(statut) {
    switch (statut) {
      case "EnAttente":
        return "status-pending";
      case "Confirmee":
        return "status-confirmed";
      case "EnPreparation":
        return "status-preparing";
      case "Expediee":
        return "status-shipped";
      case "Livree":
        return "status-delivered";
      case "Annulee":
        return "status-cancelled";
      default:
        return "status-unknown";
    }
  }
  canCancelOrder(statut) {
    return ["EnAttente", "Confirmee"].includes(statut);
  }
  annulerCommande(commande) {
    console.log("Annuler commande ID:", commande.id);
  }
  // Pagination methods
  totalPages = computed(() => {
    return Math.ceil(this.totalItems() / this.pageSize());
  });
  onPageChange(page) {
    if (page >= 1 && page <= this.totalPages()) {
      this.currentPage.set(page);
      this.loadCommandes();
    }
  }
  // Math object for template
  Math = Math;
  static \u0275fac = function OrderManagementComponent_Factory(__ngFactoryType__) {
    return new (__ngFactoryType__ || _OrderManagementComponent)(\u0275\u0275directiveInject(AdminService));
  };
  static \u0275cmp = /* @__PURE__ */ \u0275\u0275defineComponent({ type: _OrderManagementComponent, selectors: [["app-order-management"]], decls: 34, vars: 11, consts: [[1, "order-management-container"], [1, "page-header"], [1, "header-content"], [1, "page-title"], [1, "icon-shopping-cart"], [1, "header-actions"], [1, "btn", "btn-secondary", 3, "click"], [1, "icon-filter-x"], [1, "btn", "btn-secondary", 3, "click", "disabled"], [1, "icon-refresh"], [1, "filters-section"], [1, "search-box"], [1, "icon-search"], ["type", "text", "placeholder", "Rechercher par num\xE9ro, client...", 1, "search-input", 3, "input", "value"], [1, "filters"], [1, "filter-select", 3, "change", "value"], ["value", ""], [3, "value", 4, "ngFor", "ngForOf"], ["type", "date", "placeholder", "Date d\xE9but", 1, "filter-date", 3, "change", "value"], ["type", "date", "placeholder", "Date fin", 1, "filter-date", 3, "change", "value"], ["class", "error-message", 4, "ngIf"], ["class", "loading-container", 4, "ngIf"], ["class", "detailed-orders-container", 4, "ngIf"], ["class", "pagination-container", 4, "ngIf"], [1, "stats-summary"], [1, "stat-card"], [1, "stat-value"], [1, "stat-label"], [3, "value"], [1, "error-message"], [1, "icon-alert"], [1, "loading-container"], [1, "loading-spinner"], [1, "detailed-orders-container"], ["class", "order-card", 4, "ngFor", "ngForOf"], ["class", "no-data", 4, "ngIf"], [1, "order-card"], [1, "order-header"], [1, "order-info"], [1, "order-title"], [1, "client-info"], [1, "order-meta"], [1, "order-date"], [1, "status-badge", 3, "ngClass"], [1, "order-amount"], [1, "order-actions"], [1, "btn", "btn-sm", "btn-outline", 3, "click"], [1, "icon-chevron-down"], ["class", "supplier-orders", 4, "ngIf"], [1, "supplier-orders"], [1, "supplier-orders-title"], [1, "icon-truck"], ["class", "no-supplier-orders", 4, "ngIf"], ["class", "supplier-order-card", 4, "ngFor", "ngForOf"], [1, "no-supplier-orders"], [1, "supplier-order-card"], [1, "supplier-order-header"], [1, "supplier-info"], [1, "supplier-email"], [1, "supplier-order-meta"], [1, "supplier-reference"], [1, "supplier-amount"], ["class", "delivery-info", 4, "ngIf"], [1, "order-lines"], ["class", "order-line", 4, "ngFor", "ngForOf"], [1, "delivery-info"], ["class", "delivery-date", 4, "ngIf"], ["class", "delivery-number", 4, "ngIf"], [1, "delivery-date"], [1, "icon-calendar"], [1, "delivery-number"], [1, "icon-package"], [1, "order-line"], [1, "product-info"], [1, "product-name"], [1, "product-ref"], [1, "quantity-price"], [1, "quantity"], [1, "unit-price"], [1, "line-total"], [1, "no-data"], [1, "pagination-container"], [1, "pagination-info"], [1, "pagination"], [1, "btn-page", 3, "click", "disabled"], [1, "icon-chevron-left"], [1, "page-info"], [1, "icon-chevron-right"]], template: function OrderManagementComponent_Template(rf, ctx) {
    if (rf & 1) {
      \u0275\u0275elementStart(0, "div", 0)(1, "div", 1)(2, "div", 2)(3, "h1", 3);
      \u0275\u0275element(4, "i", 4);
      \u0275\u0275text(5, " Gestion des Commandes ");
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(6, "div", 5)(7, "button", 6);
      \u0275\u0275listener("click", function OrderManagementComponent_Template_button_click_7_listener() {
        return ctx.clearFilters();
      });
      \u0275\u0275element(8, "i", 7);
      \u0275\u0275text(9, " Effacer filtres ");
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(10, "button", 8);
      \u0275\u0275listener("click", function OrderManagementComponent_Template_button_click_10_listener() {
        return ctx.refresh();
      });
      \u0275\u0275element(11, "i", 9);
      \u0275\u0275text(12, " Actualiser ");
      \u0275\u0275elementEnd()()()();
      \u0275\u0275elementStart(13, "div", 10)(14, "div", 11);
      \u0275\u0275element(15, "i", 12);
      \u0275\u0275elementStart(16, "input", 13);
      \u0275\u0275listener("input", function OrderManagementComponent_Template_input_input_16_listener($event) {
        return ctx.searchTerm.set($event.target.value);
      });
      \u0275\u0275elementEnd()();
      \u0275\u0275elementStart(17, "div", 14)(18, "select", 15);
      \u0275\u0275listener("change", function OrderManagementComponent_Template_select_change_18_listener($event) {
        return ctx.selectedStatus.set($event.target.value);
      });
      \u0275\u0275elementStart(19, "option", 16);
      \u0275\u0275text(20, "Tous les statuts");
      \u0275\u0275elementEnd();
      \u0275\u0275template(21, OrderManagementComponent_option_21_Template, 2, 2, "option", 17);
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(22, "input", 18);
      \u0275\u0275listener("change", function OrderManagementComponent_Template_input_change_22_listener($event) {
        return ctx.dateDebut.set($event.target.value);
      });
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(23, "input", 19);
      \u0275\u0275listener("change", function OrderManagementComponent_Template_input_change_23_listener($event) {
        return ctx.dateFin.set($event.target.value);
      });
      \u0275\u0275elementEnd()()();
      \u0275\u0275template(24, OrderManagementComponent_div_24_Template, 3, 1, "div", 20)(25, OrderManagementComponent_div_25_Template, 4, 0, "div", 21)(26, OrderManagementComponent_div_26_Template, 3, 2, "div", 22)(27, OrderManagementComponent_div_27_Template, 10, 7, "div", 23);
      \u0275\u0275elementStart(28, "div", 24)(29, "div", 25)(30, "div", 26);
      \u0275\u0275text(31);
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(32, "div", 27);
      \u0275\u0275text(33, "Commandes");
      \u0275\u0275elementEnd()()()();
    }
    if (rf & 2) {
      \u0275\u0275advance(10);
      \u0275\u0275property("disabled", ctx.isLoading());
      \u0275\u0275advance(6);
      \u0275\u0275property("value", ctx.searchTerm());
      \u0275\u0275advance(2);
      \u0275\u0275property("value", ctx.selectedStatus());
      \u0275\u0275advance(3);
      \u0275\u0275property("ngForOf", ctx.statuts);
      \u0275\u0275advance();
      \u0275\u0275property("value", ctx.dateDebut());
      \u0275\u0275advance();
      \u0275\u0275property("value", ctx.dateFin());
      \u0275\u0275advance();
      \u0275\u0275property("ngIf", ctx.error());
      \u0275\u0275advance();
      \u0275\u0275property("ngIf", ctx.isLoading());
      \u0275\u0275advance();
      \u0275\u0275property("ngIf", !ctx.isLoading() && !ctx.error());
      \u0275\u0275advance();
      \u0275\u0275property("ngIf", ctx.totalPages() > 1);
      \u0275\u0275advance(4);
      \u0275\u0275textInterpolate(ctx.getTotalCommandes());
    }
  }, dependencies: [CommonModule, NgClass, NgForOf, NgIf, FormsModule, NgSelectOption, \u0275NgSelectMultipleOption], styles: ['\n\n.order-management-container[_ngcontent-%COMP%] {\n  padding: 2rem;\n  max-width: 1400px;\n  margin: 0 auto;\n  background:\n    linear-gradient(\n      135deg,\n      #f8fafc 0%,\n      #e2e8f0 100%);\n  min-height: 100vh;\n}\n.page-header[_ngcontent-%COMP%] {\n  margin-bottom: 2rem;\n  background: white;\n  border-radius: 16px;\n  padding: 2rem;\n  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);\n  border: 1px solid rgba(255, 255, 255, 0.2);\n}\n.header-content[_ngcontent-%COMP%] {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  flex-wrap: wrap;\n  gap: 1rem;\n}\n.page-title[_ngcontent-%COMP%] {\n  display: flex;\n  align-items: center;\n  gap: 0.75rem;\n  font-size: 2.25rem;\n  font-weight: 700;\n  background:\n    linear-gradient(\n      135deg,\n      #667eea 0%,\n      #764ba2 100%);\n  -webkit-background-clip: text;\n  -webkit-text-fill-color: transparent;\n  background-clip: text;\n  margin: 0;\n}\n.header-actions[_ngcontent-%COMP%] {\n  display: flex;\n  gap: 1rem;\n  flex-wrap: wrap;\n}\n.filters-section[_ngcontent-%COMP%] {\n  background: white;\n  border-radius: 16px;\n  padding: 2rem;\n  margin-bottom: 2rem;\n  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);\n  border: 1px solid rgba(255, 255, 255, 0.2);\n  display: flex;\n  gap: 1.5rem;\n  flex-wrap: wrap;\n  align-items: center;\n  -webkit-backdrop-filter: blur(10px);\n  backdrop-filter: blur(10px);\n}\n.search-box[_ngcontent-%COMP%] {\n  position: relative;\n  flex: 1;\n  min-width: 300px;\n}\n.search-box[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\n  position: absolute;\n  left: 1rem;\n  top: 50%;\n  transform: translateY(-50%);\n  color: #64748b;\n}\n.search-input[_ngcontent-%COMP%] {\n  width: 100%;\n  padding: 0.75rem 1rem 0.75rem 2.5rem;\n  border: 2px solid #e2e8f0;\n  border-radius: 8px;\n  font-size: 1rem;\n  transition: border-color 0.2s;\n}\n.search-input[_ngcontent-%COMP%]:focus {\n  outline: none;\n  border-color: #3b82f6;\n}\n.filters[_ngcontent-%COMP%] {\n  display: flex;\n  gap: 1rem;\n  flex-wrap: wrap;\n}\n.filter-select[_ngcontent-%COMP%], \n.filter-date[_ngcontent-%COMP%] {\n  padding: 0.75rem 1rem;\n  border: 2px solid #e2e8f0;\n  border-radius: 8px;\n  font-size: 1rem;\n  background: white;\n  min-width: 150px;\n}\n.table-container[_ngcontent-%COMP%] {\n  background: white;\n  border-radius: 12px;\n  overflow: hidden;\n  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);\n  margin-bottom: 2rem;\n}\n.orders-table[_ngcontent-%COMP%] {\n  width: 100%;\n  border-collapse: collapse;\n  table-layout: fixed;\n}\n.orders-table[_ngcontent-%COMP%]   th[_ngcontent-%COMP%] {\n  background: #f8fafc;\n  padding: 1rem;\n  text-align: left;\n  font-weight: 600;\n  color: #374151;\n  border-bottom: 1px solid #e5e7eb;\n}\n.orders-table[_ngcontent-%COMP%]   th[_ngcontent-%COMP%]:nth-child(1) {\n  width: 15%;\n}\n.orders-table[_ngcontent-%COMP%]   th[_ngcontent-%COMP%]:nth-child(2) {\n  width: 25%;\n}\n.orders-table[_ngcontent-%COMP%]   th[_ngcontent-%COMP%]:nth-child(3) {\n  width: 20%;\n}\n.orders-table[_ngcontent-%COMP%]   th[_ngcontent-%COMP%]:nth-child(4) {\n  width: 15%;\n}\n.orders-table[_ngcontent-%COMP%]   th[_ngcontent-%COMP%]:nth-child(5) {\n  width: 12%;\n}\n.orders-table[_ngcontent-%COMP%]   th[_ngcontent-%COMP%]:nth-child(6) {\n  width: 13%;\n}\n.orders-table[_ngcontent-%COMP%]   th[_ngcontent-%COMP%]:nth-child(7) {\n  width: 10%;\n}\n.order-row[_ngcontent-%COMP%] {\n  border-bottom: 1px solid #f3f4f6;\n  transition: background-color 0.2s;\n}\n.order-row[_ngcontent-%COMP%]:hover {\n  background: #f9fafb;\n}\n.orders-table[_ngcontent-%COMP%]   td[_ngcontent-%COMP%] {\n  padding: 1rem;\n  vertical-align: middle;\n  word-wrap: break-word;\n  overflow: hidden;\n}\n.order-number[_ngcontent-%COMP%] {\n  display: flex;\n  flex-direction: column;\n  gap: 0.25rem;\n}\n.number-main[_ngcontent-%COMP%] {\n  font-weight: 600;\n  color: #1a202c;\n  font-family: monospace;\n}\n.order-id[_ngcontent-%COMP%] {\n  font-size: 0.875rem;\n  color: #64748b;\n}\n.client-info[_ngcontent-%COMP%] {\n  display: flex;\n  flex-direction: column;\n  gap: 0.25rem;\n}\n.client-name[_ngcontent-%COMP%] {\n  font-weight: 600;\n  color: #1a202c;\n  font-size: 0.95rem;\n}\n.client-email[_ngcontent-%COMP%] {\n  font-size: 0.8rem;\n  color: #4f46e5;\n  font-family: monospace;\n}\n.supplier-name[_ngcontent-%COMP%] {\n  color: #059669;\n  font-weight: 500;\n  font-size: 0.9rem;\n  line-height: 1.4;\n  max-height: 2.8rem;\n  overflow: hidden;\n  text-overflow: ellipsis;\n}\n.amount[_ngcontent-%COMP%] {\n  font-weight: 700;\n  color: #059669;\n  font-size: 1.125rem;\n}\n.date-cell[_ngcontent-%COMP%] {\n  color: #64748b;\n  font-size: 0.875rem;\n}\n.status-badge[_ngcontent-%COMP%] {\n  padding: 0.5rem 1rem;\n  border-radius: 20px;\n  font-size: 0.8rem;\n  font-weight: 600;\n  text-transform: uppercase;\n  letter-spacing: 0.5px;\n  position: relative;\n  overflow: hidden;\n  -webkit-backdrop-filter: blur(10px);\n  backdrop-filter: blur(10px);\n  border: 1px solid rgba(255, 255, 255, 0.2);\n}\n.status-pending[_ngcontent-%COMP%] {\n  background:\n    linear-gradient(\n      135deg,\n      #fef3c7 0%,\n      #fde68a 100%);\n  color: #92400e;\n  box-shadow: 0 2px 8px rgba(251, 191, 36, 0.3);\n}\n.status-confirmed[_ngcontent-%COMP%] {\n  background:\n    linear-gradient(\n      135deg,\n      #dbeafe 0%,\n      #bfdbfe 100%);\n  color: #1e40af;\n  box-shadow: 0 2px 8px rgba(59, 130, 246, 0.3);\n}\n.status-preparing[_ngcontent-%COMP%] {\n  background:\n    linear-gradient(\n      135deg,\n      #e0e7ff 0%,\n      #c7d2fe 100%);\n  color: #4338ca;\n  box-shadow: 0 2px 8px rgba(99, 102, 241, 0.3);\n}\n.status-shipped[_ngcontent-%COMP%] {\n  background:\n    linear-gradient(\n      135deg,\n      #f3e8ff 0%,\n      #e9d5ff 100%);\n  color: #7c2d12;\n  box-shadow: 0 2px 8px rgba(147, 51, 234, 0.3);\n}\n.status-delivered[_ngcontent-%COMP%] {\n  background:\n    linear-gradient(\n      135deg,\n      #d1fae5 0%,\n      #a7f3d0 100%);\n  color: #065f46;\n  box-shadow: 0 2px 8px rgba(5, 150, 105, 0.3);\n}\n.status-cancelled[_ngcontent-%COMP%] {\n  background:\n    linear-gradient(\n      135deg,\n      #fee2e2 0%,\n      #fecaca 100%);\n  color: #991b1b;\n  box-shadow: 0 2px 8px rgba(220, 38, 38, 0.3);\n}\n.status-unknown[_ngcontent-%COMP%] {\n  background:\n    linear-gradient(\n      135deg,\n      #f3f4f6 0%,\n      #e5e7eb 100%);\n  color: #4b5563;\n  box-shadow: 0 2px 8px rgba(107, 114, 128, 0.3);\n}\n.actions-cell[_ngcontent-%COMP%] {\n  width: 100px;\n}\n.action-buttons[_ngcontent-%COMP%] {\n  display: flex;\n  gap: 0.5rem;\n}\n.btn-action[_ngcontent-%COMP%] {\n  width: 32px;\n  height: 32px;\n  border: none;\n  border-radius: 6px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  cursor: pointer;\n  transition: all 0.2s;\n}\n.btn-view[_ngcontent-%COMP%] {\n  background: #dbeafe;\n  color: #2563eb;\n}\n.btn-view[_ngcontent-%COMP%]:hover {\n  background: #bfdbfe;\n}\n.btn-cancel[_ngcontent-%COMP%] {\n  background: #fee2e2;\n  color: #dc2626;\n}\n.btn-cancel[_ngcontent-%COMP%]:hover {\n  background: #fecaca;\n}\n.btn[_ngcontent-%COMP%] {\n  padding: 0.875rem 1.75rem;\n  border: none;\n  border-radius: 12px;\n  font-weight: 600;\n  cursor: pointer;\n  transition: all 0.3s ease;\n  display: flex;\n  align-items: center;\n  gap: 0.5rem;\n  position: relative;\n  overflow: hidden;\n  text-transform: uppercase;\n  letter-spacing: 0.5px;\n  font-size: 0.875rem;\n}\n.btn[_ngcontent-%COMP%]::before {\n  content: "";\n  position: absolute;\n  top: 0;\n  left: -100%;\n  width: 100%;\n  height: 100%;\n  background:\n    linear-gradient(\n      90deg,\n      transparent,\n      rgba(255, 255, 255, 0.2),\n      transparent);\n  transition: left 0.5s;\n}\n.btn[_ngcontent-%COMP%]:hover::before {\n  left: 100%;\n}\n.btn-primary[_ngcontent-%COMP%] {\n  background:\n    linear-gradient(\n      135deg,\n      #667eea 0%,\n      #764ba2 100%);\n  color: white;\n  box-shadow: 0 4px 15px rgba(102, 126, 234, 0.4);\n}\n.btn-primary[_ngcontent-%COMP%]:hover {\n  transform: translateY(-2px);\n  box-shadow: 0 8px 25px rgba(102, 126, 234, 0.6);\n}\n.btn-secondary[_ngcontent-%COMP%] {\n  background:\n    linear-gradient(\n      135deg,\n      #f8fafc 0%,\n      #e2e8f0 100%);\n  color: #4a5568;\n  border: 1px solid rgba(226, 232, 240, 0.8);\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\n}\n.btn-secondary[_ngcontent-%COMP%]:hover {\n  transform: translateY(-2px);\n  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.15);\n  background:\n    linear-gradient(\n      135deg,\n      #e2e8f0 0%,\n      #cbd5e0 100%);\n}\n.btn[_ngcontent-%COMP%]:disabled {\n  opacity: 0.5;\n  cursor: not-allowed;\n}\n.pagination-container[_ngcontent-%COMP%] {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  padding: 1.5rem;\n  background: white;\n  border-radius: 16px;\n  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);\n  border: 1px solid rgba(255, 255, 255, 0.2);\n  -webkit-backdrop-filter: blur(10px);\n  backdrop-filter: blur(10px);\n  margin-bottom: 2rem;\n}\n.pagination-info[_ngcontent-%COMP%] {\n  color: #64748b;\n  font-size: 0.875rem;\n}\n.pagination[_ngcontent-%COMP%] {\n  display: flex;\n  align-items: center;\n  gap: 1rem;\n}\n.btn-page[_ngcontent-%COMP%] {\n  width: 36px;\n  height: 36px;\n  border: 1px solid #e5e7eb;\n  background: white;\n  border-radius: 6px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  cursor: pointer;\n  transition: all 0.2s;\n}\n.btn-page[_ngcontent-%COMP%]:hover:not(:disabled) {\n  background: #f3f4f6;\n}\n.btn-page[_ngcontent-%COMP%]:disabled {\n  opacity: 0.5;\n  cursor: not-allowed;\n}\n.page-info[_ngcontent-%COMP%] {\n  font-weight: 500;\n  color: #374151;\n}\n.stats-summary[_ngcontent-%COMP%] {\n  display: grid;\n  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));\n  gap: 1rem;\n  margin-top: 2rem;\n}\n.stat-card[_ngcontent-%COMP%] {\n  background: white;\n  border-radius: 12px;\n  padding: 1.5rem;\n  text-align: center;\n  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);\n}\n.stat-value[_ngcontent-%COMP%] {\n  font-size: 1.5rem;\n  font-weight: 700;\n  color: #1a202c;\n  margin-bottom: 0.5rem;\n}\n.stat-label[_ngcontent-%COMP%] {\n  color: #64748b;\n  font-size: 0.875rem;\n}\n.error-message[_ngcontent-%COMP%] {\n  background: #fef2f2;\n  border: 1px solid #fecaca;\n  color: #dc2626;\n  padding: 1rem;\n  border-radius: 8px;\n  margin-bottom: 2rem;\n  display: flex;\n  align-items: center;\n  gap: 0.5rem;\n}\n.loading-container[_ngcontent-%COMP%] {\n  text-align: center;\n  padding: 3rem;\n}\n.loading-spinner[_ngcontent-%COMP%] {\n  width: 40px;\n  height: 40px;\n  border: 4px solid #e2e8f0;\n  border-top: 4px solid #3b82f6;\n  border-radius: 50%;\n  animation: _ngcontent-%COMP%_spin 1s linear infinite;\n  margin: 0 auto 1rem;\n}\n@keyframes _ngcontent-%COMP%_spin {\n  0% {\n    transform: rotate(0deg);\n  }\n  100% {\n    transform: rotate(360deg);\n  }\n}\n.no-data[_ngcontent-%COMP%] {\n  text-align: center;\n  padding: 3rem;\n  color: #64748b;\n}\n.no-data[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\n  font-size: 3rem;\n  margin-bottom: 1rem;\n  opacity: 0.5;\n}\n.no-data[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%] {\n  margin: 0 0 0.5rem 0;\n  color: #374151;\n}\n.icon-shopping-cart[_ngcontent-%COMP%]::before {\n  content: "\\1f6cd\\fe0f";\n  font-size: 1.2em;\n}\n.icon-filter-x[_ngcontent-%COMP%]::before {\n  content: "\\1f5c2\\fe0f";\n  font-size: 1.1em;\n}\n.icon-refresh[_ngcontent-%COMP%]::before {\n  content: "\\1f504";\n  font-size: 1.1em;\n}\n.icon-download[_ngcontent-%COMP%]::before {\n  content: "\\1f4be";\n  font-size: 1.1em;\n}\n.icon-search[_ngcontent-%COMP%]::before {\n  content: "\\1f50d";\n  font-size: 1.1em;\n}\n.icon-alert[_ngcontent-%COMP%]::before {\n  content: "\\26a0\\fe0f";\n  font-size: 1.1em;\n}\n.icon-eye[_ngcontent-%COMP%]::before {\n  content: "\\1f440";\n  font-size: 1.1em;\n}\n.icon-x[_ngcontent-%COMP%]::before {\n  content: "\\274c";\n  font-size: 1.1em;\n}\n.icon-chevron-left[_ngcontent-%COMP%]::before {\n  content: "\\2b05\\fe0f";\n  font-size: 1.1em;\n}\n.icon-chevron-right[_ngcontent-%COMP%]::before {\n  content: "\\27a1\\fe0f";\n  font-size: 1.1em;\n}\n.icon-chevron-down[_ngcontent-%COMP%]::before {\n  content: "\\1f53d";\n  font-size: 0.9em;\n}\n.icon-layers[_ngcontent-%COMP%]::before {\n  content: "\\1f4ca";\n  font-size: 1.1em;\n}\n.icon-truck[_ngcontent-%COMP%]::before {\n  content: "\\1f69b";\n  font-size: 1.1em;\n}\n.icon-calendar[_ngcontent-%COMP%]::before {\n  content: "\\1f4c5";\n  font-size: 1.1em;\n}\n.icon-package[_ngcontent-%COMP%]::before {\n  content: "\\1f4e6";\n  font-size: 1.1em;\n}\n@media (max-width: 768px) {\n  .order-management-container[_ngcontent-%COMP%] {\n    padding: 1rem;\n  }\n  .header-content[_ngcontent-%COMP%] {\n    flex-direction: column;\n    align-items: stretch;\n  }\n  .filters-section[_ngcontent-%COMP%] {\n    flex-direction: column;\n  }\n  .search-box[_ngcontent-%COMP%] {\n    min-width: auto;\n  }\n  .filters[_ngcontent-%COMP%] {\n    justify-content: stretch;\n  }\n  .filter-select[_ngcontent-%COMP%], \n   .filter-date[_ngcontent-%COMP%] {\n    min-width: auto;\n    flex: 1;\n  }\n  .orders-table[_ngcontent-%COMP%] {\n    font-size: 0.875rem;\n  }\n  .orders-table[_ngcontent-%COMP%]   th[_ngcontent-%COMP%], \n   .orders-table[_ngcontent-%COMP%]   td[_ngcontent-%COMP%] {\n    padding: 0.75rem 0.5rem;\n  }\n  .pagination-container[_ngcontent-%COMP%] {\n    flex-direction: column;\n    gap: 1rem;\n  }\n  .stats-summary[_ngcontent-%COMP%] {\n    grid-template-columns: 1fr;\n  }\n}\n.detailed-orders-container[_ngcontent-%COMP%] {\n  display: flex;\n  flex-direction: column;\n  gap: 1.5rem;\n}\n.order-card[_ngcontent-%COMP%] {\n  background: white;\n  border-radius: 16px;\n  border: 1px solid rgba(255, 255, 255, 0.2);\n  overflow: hidden;\n  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);\n  -webkit-backdrop-filter: blur(10px);\n  backdrop-filter: blur(10px);\n  transition: all 0.3s ease;\n  position: relative;\n}\n.order-card[_ngcontent-%COMP%]:hover {\n  transform: translateY(-4px);\n  box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15);\n}\n.order-card[_ngcontent-%COMP%]::before {\n  content: "";\n  position: absolute;\n  top: 0;\n  left: 0;\n  right: 0;\n  height: 4px;\n  background:\n    linear-gradient(\n      90deg,\n      #667eea 0%,\n      #764ba2 100%);\n}\n.order-header[_ngcontent-%COMP%] {\n  background:\n    linear-gradient(\n      135deg,\n      #f8fafc 0%,\n      #e2e8f0 100%);\n  padding: 2rem;\n  border-bottom: 1px solid rgba(229, 231, 235, 0.5);\n  display: flex;\n  justify-content: space-between;\n  align-items: flex-start;\n  gap: 1rem;\n}\n.order-title[_ngcontent-%COMP%] {\n  font-size: 1.25rem;\n  font-weight: bold;\n  color: #1f2937;\n  margin: 0 0 0.5rem 0;\n}\n.client-info[_ngcontent-%COMP%] {\n  color: #4b5563;\n  margin-bottom: 0.75rem;\n}\n.order-meta[_ngcontent-%COMP%] {\n  display: flex;\n  gap: 1rem;\n  align-items: center;\n  flex-wrap: wrap;\n}\n.order-date[_ngcontent-%COMP%] {\n  color: #6b7280;\n  font-size: 0.875rem;\n}\n.order-amount[_ngcontent-%COMP%] {\n  font-weight: bold;\n  color: #059669;\n  font-size: 1.1rem;\n}\n.supplier-orders[_ngcontent-%COMP%] {\n  padding: 1.5rem;\n}\n.supplier-orders-title[_ngcontent-%COMP%] {\n  display: flex;\n  align-items: center;\n  gap: 0.5rem;\n  font-size: 1.1rem;\n  font-weight: 600;\n  color: #374151;\n  margin: 0 0 1rem 0;\n}\n.no-supplier-orders[_ngcontent-%COMP%] {\n  text-align: center;\n  color: #6b7280;\n  font-style: italic;\n  padding: 2rem;\n}\n.supplier-order-card[_ngcontent-%COMP%] {\n  background: #f9fafb;\n  border: 1px solid #e5e7eb;\n  border-radius: 8px;\n  padding: 1rem;\n  margin-bottom: 1rem;\n}\n.supplier-order-card[_ngcontent-%COMP%]:last-child {\n  margin-bottom: 0;\n}\n.supplier-order-header[_ngcontent-%COMP%] {\n  display: flex;\n  justify-content: space-between;\n  align-items: flex-start;\n  margin-bottom: 1rem;\n}\n.supplier-info[_ngcontent-%COMP%]   strong[_ngcontent-%COMP%] {\n  color: #1f2937;\n  font-size: 1rem;\n}\n.supplier-email[_ngcontent-%COMP%] {\n  color: #6b7280;\n  font-size: 0.875rem;\n  display: block;\n  margin-top: 0.25rem;\n}\n.supplier-order-meta[_ngcontent-%COMP%] {\n  display: flex;\n  flex-direction: column;\n  align-items: flex-end;\n  gap: 0.5rem;\n}\n.supplier-reference[_ngcontent-%COMP%] {\n  font-family: monospace;\n  background: #e5e7eb;\n  padding: 0.25rem 0.5rem;\n  border-radius: 4px;\n  font-size: 0.875rem;\n}\n.supplier-amount[_ngcontent-%COMP%] {\n  font-weight: bold;\n  color: #059669;\n}\n.delivery-info[_ngcontent-%COMP%] {\n  background: #eff6ff;\n  border: 1px solid #dbeafe;\n  border-radius: 6px;\n  padding: 0.75rem;\n  margin-bottom: 1rem;\n  display: flex;\n  gap: 1rem;\n  flex-wrap: wrap;\n}\n.delivery-date[_ngcontent-%COMP%], \n.delivery-number[_ngcontent-%COMP%] {\n  display: flex;\n  align-items: center;\n  gap: 0.5rem;\n  color: #1e40af;\n  font-size: 0.875rem;\n}\n.order-lines[_ngcontent-%COMP%] {\n  border-top: 1px solid #e5e7eb;\n  padding-top: 1rem;\n}\n.order-line[_ngcontent-%COMP%] {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  padding: 0.5rem 0;\n  border-bottom: 1px solid #f3f4f6;\n}\n.order-line[_ngcontent-%COMP%]:last-child {\n  border-bottom: none;\n}\n.product-info[_ngcontent-%COMP%] {\n  flex: 1;\n}\n.product-name[_ngcontent-%COMP%] {\n  font-weight: 500;\n  color: #1f2937;\n  display: block;\n}\n.product-ref[_ngcontent-%COMP%] {\n  color: #6b7280;\n  font-size: 0.875rem;\n  font-family: monospace;\n}\n.quantity-price[_ngcontent-%COMP%] {\n  display: flex;\n  align-items: center;\n  gap: 1rem;\n  font-size: 0.875rem;\n}\n.quantity[_ngcontent-%COMP%] {\n  color: #6b7280;\n}\n.unit-price[_ngcontent-%COMP%] {\n  color: #4b5563;\n}\n.line-total[_ngcontent-%COMP%] {\n  font-weight: 600;\n  color: #1f2937;\n  min-width: 80px;\n  text-align: right;\n}\n@media (max-width: 768px) {\n  .order-meta[_ngcontent-%COMP%] {\n    flex-direction: column;\n    align-items: flex-start;\n    gap: 0.5rem;\n  }\n  .supplier-order-header[_ngcontent-%COMP%] {\n    flex-direction: column;\n    gap: 1rem;\n  }\n  .supplier-order-meta[_ngcontent-%COMP%] {\n    align-items: flex-start;\n  }\n  .delivery-info[_ngcontent-%COMP%] {\n    flex-direction: column;\n    gap: 0.5rem;\n  }\n  .order-line[_ngcontent-%COMP%] {\n    flex-direction: column;\n    align-items: flex-start;\n    gap: 0.5rem;\n  }\n  .quantity-price[_ngcontent-%COMP%] {\n    justify-content: space-between;\n    width: 100%;\n  }\n}\n.order-actions[_ngcontent-%COMP%] {\n  display: flex;\n  align-items: center;\n  gap: 0.5rem;\n}\n.order-actions[_ngcontent-%COMP%]   .btn[_ngcontent-%COMP%] {\n  display: flex;\n  align-items: center;\n  gap: 0.5rem;\n  transition: all 0.2s ease;\n}\n.order-actions[_ngcontent-%COMP%]   .btn.active[_ngcontent-%COMP%] {\n  background-color: #3182ce;\n  color: white;\n}\n.icon-chevron-down[_ngcontent-%COMP%] {\n  transition: transform 0.2s ease;\n}\n.icon-chevron-down.rotated[_ngcontent-%COMP%] {\n  transform: rotate(180deg);\n}\n.action-buttons[_ngcontent-%COMP%] {\n  display: flex;\n  gap: 0.5rem;\n  align-items: center;\n}\n.btn-sm[_ngcontent-%COMP%] {\n  padding: 0.375rem 0.75rem;\n  font-size: 0.875rem;\n  border-radius: 6px;\n  border: 1px solid #e2e8f0;\n  background: white;\n  color: #4a5568;\n  cursor: pointer;\n  transition: all 0.2s ease;\n  display: flex;\n  align-items: center;\n  gap: 0.375rem;\n}\n.btn-sm[_ngcontent-%COMP%]:hover {\n  background-color: #f7fafc;\n  border-color: #cbd5e0;\n}\n.btn-sm.btn-primary[_ngcontent-%COMP%] {\n  background-color: #3182ce;\n  border-color: #3182ce;\n  color: white;\n}\n.btn-sm.btn-primary[_ngcontent-%COMP%]:hover {\n  background-color: #2c5aa0;\n  border-color: #2c5aa0;\n}\n.btn-sm.btn-outline[_ngcontent-%COMP%] {\n  background: transparent;\n  border-color: #e2e8f0;\n  color: #4a5568;\n}\n.btn-sm.btn-outline[_ngcontent-%COMP%]:hover {\n  background-color: #f7fafc;\n  border-color: #cbd5e0;\n}\n.stats-summary[_ngcontent-%COMP%] {\n  display: flex;\n  gap: 1rem;\n  margin-bottom: 1rem;\n}\n.stat-card[_ngcontent-%COMP%] {\n  background: white;\n  border-radius: 8px;\n  padding: 1rem;\n  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);\n  min-width: 120px;\n  text-align: center;\n}\n.stat-value[_ngcontent-%COMP%] {\n  font-size: 1.5rem;\n  font-weight: 600;\n  color: #2d3748;\n  margin-bottom: 0.25rem;\n}\n.stat-label[_ngcontent-%COMP%] {\n  font-size: 0.875rem;\n  color: #718096;\n  text-transform: uppercase;\n  letter-spacing: 0.05em;\n}\n/*# sourceMappingURL=order-management.component.css.map */'] });
};
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && setClassMetadata(OrderManagementComponent, [{
    type: Component,
    args: [{ selector: "app-order-management", standalone: true, imports: [CommonModule, FormsModule], template: `<div class="order-management-container">
  <!-- Header -->
  <div class="page-header">
    <div class="header-content">
      <h1 class="page-title">
        <i class="icon-shopping-cart"></i>
        Gestion des Commandes
      </h1>
      <div class="header-actions">
        <button class="btn btn-secondary" (click)="clearFilters()">
          <i class="icon-filter-x"></i>
          Effacer filtres
        </button>
        <button class="btn btn-secondary" (click)="refresh()" [disabled]="isLoading()">
          <i class="icon-refresh"></i>
          Actualiser
        </button>
      </div>
    </div>
  </div>

  <!-- Filtres et Recherche -->
  <div class="filters-section">
    <div class="search-box">
      <i class="icon-search"></i>
      <input
        type="text"
        placeholder="Rechercher par num\xE9ro, client..."
        [value]="searchTerm()"
        (input)="searchTerm.set($any($event.target).value)"
        class="search-input">
    </div>

    <div class="filters">
      <select [value]="selectedStatus()" (change)="selectedStatus.set($any($event.target).value)" class="filter-select">
        <option value="">Tous les statuts</option>
        <option *ngFor="let statut of statuts" [value]="statut">{{ getStatusText(statut) }}</option>
      </select>

      <input
        type="date"
        [value]="dateDebut()"
        (change)="dateDebut.set($any($event.target).value)"
        class="filter-date"
        placeholder="Date d\xE9but">

      <input
        type="date"
        [value]="dateFin()"
        (change)="dateFin.set($any($event.target).value)"
        class="filter-date"
        placeholder="Date fin">
    </div>
  </div>

  <!-- Message d'erreur -->
  <div *ngIf="error()" class="error-message">
    <i class="icon-alert"></i>
    {{ error() }}
  </div>

  <!-- Loading -->
  <div *ngIf="isLoading()" class="loading-container">
    <div class="loading-spinner"></div>
    <p>Chargement des commandes...</p>
  </div>



  <!-- Vue d\xE9taill\xE9e des commandes avec fournisseurs -->
  <div *ngIf="!isLoading() && !error()" class="detailed-orders-container">
    <div *ngFor="let commande of filteredCommandesAvecFournisseur()" class="order-card">
      <!-- En-t\xEAte de la commande client -->
      <div class="order-header">
        <div class="order-info">
          <h3 class="order-title">{{ commande.numeroCommande }}</h3>
          <div class="client-info">
            <strong>{{ commande.clientNom }}</strong> - {{ commande.clientEmail }}
          </div>
          <div class="order-meta">
            <span class="order-date">{{ formatDate(commande.dateCommande) }}</span>
            <span class="status-badge" [ngClass]="getStatusClass(commande.statut)">
              {{ getStatusText(commande.statut) }}
            </span>
            <span class="order-amount">{{ formatPrice(commande.montantTotal) }}</span>
          </div>
        </div>
        <div class="order-actions">
          <button
            class="btn btn-sm btn-outline"
            (click)="viewOrderDetails(commande)"
            [class.active]="isOrderExpanded(commande.id)">
            <i class="icon-chevron-down" [class.rotated]="isOrderExpanded(commande.id)"></i>
            {{ isOrderExpanded(commande.id) ? 'Masquer' : 'Voir' }} d\xE9tails
          </button>
        </div>
      </div>

      <!-- Commandes fournisseur associ\xE9es (affich\xE9es seulement si expand\xE9e) -->
      <div *ngIf="isOrderExpanded(commande.id)" class="supplier-orders">
        <h4 class="supplier-orders-title">
          <i class="icon-truck"></i>
          Commandes Fournisseur ({{ commande.commandesFournisseur.length }})
        </h4>

        <div *ngIf="commande.commandesFournisseur.length === 0" class="no-supplier-orders">
          <p>Aucune commande fournisseur associ\xE9e</p>
        </div>

        <div *ngFor="let cmdFournisseur of commande.commandesFournisseur" class="supplier-order-card">
          <div class="supplier-order-header">
            <div class="supplier-info">
              <strong>{{ cmdFournisseur.fournisseurNom }}</strong>
              <span class="supplier-email">{{ cmdFournisseur.fournisseurEmail }}</span>
            </div>
            <div class="supplier-order-meta">
              <span class="supplier-reference">{{ cmdFournisseur.reference }}</span>
              <span class="status-badge" [ngClass]="getCommandeFournisseurStatusClass(cmdFournisseur.statut)">
                {{ getCommandeFournisseurStatusText(cmdFournisseur.statut) }}
              </span>
              <span class="supplier-amount">{{ formatPrice(cmdFournisseur.montantTotal) }}</span>
            </div>
          </div>

          <!-- D\xE9tails de livraison -->
          <div *ngIf="cmdFournisseur.dateLivraison || cmdFournisseur.numeroBonLivraison" class="delivery-info">
            <div *ngIf="cmdFournisseur.dateLivraison" class="delivery-date">
              <i class="icon-calendar"></i>
              Livraison pr\xE9vue: {{ formatDate(cmdFournisseur.dateLivraison) }}
            </div>
            <div *ngIf="cmdFournisseur.numeroBonLivraison" class="delivery-number">
              <i class="icon-package"></i>
              Bon de livraison: {{ cmdFournisseur.numeroBonLivraison }}
            </div>
          </div>

          <!-- Lignes de commande -->
          <div class="order-lines">
            <div *ngFor="let ligne of cmdFournisseur.lignes" class="order-line">
              <div class="product-info">
                <span class="product-name">{{ ligne.produitNom }}</span>
                <span class="product-ref">{{ ligne.referenceProduit }}</span>
              </div>
              <div class="quantity-price">
                <span class="quantity">{{ ligne.quantite }}x</span>
                <span class="unit-price">{{ formatPrice(ligne.prixUnitaire) }}</span>
                <span class="line-total">{{ formatPrice(ligne.montantLigne) }}</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Message si aucune commande -->
    <div *ngIf="commandesAvecFournisseur().length === 0" class="no-data">
      <i class="icon-shopping-cart"></i>
      <h3>Aucune commande trouv\xE9e</h3>
      <p>Aucune commande ne correspond aux crit\xE8res de recherche.</p>
    </div>
  </div>

  <!-- Pagination -->
  <div *ngIf="totalPages() > 1" class="pagination-container">
    <div class="pagination-info">
      Affichage de {{ (currentPage() - 1) * pageSize() + 1 }} \xE0 
      {{ Math.min(currentPage() * pageSize(), totalItems()) }} sur {{ totalItems() }} commandes
    </div>
    
    <div class="pagination">
      <button 
        class="btn-page"
        [disabled]="currentPage() === 1"
        (click)="onPageChange(currentPage() - 1)">
        <i class="icon-chevron-left"></i>
      </button>
      
      <span class="page-info">
        Page {{ currentPage() }} sur {{ totalPages() }}
      </span>
      
      <button 
        class="btn-page"
        [disabled]="currentPage() === totalPages()"
        (click)="onPageChange(currentPage() + 1)">
        <i class="icon-chevron-right"></i>
      </button>
    </div>
  </div>

  <!-- Statistiques rapides -->
  <div class="stats-summary">
    <div class="stat-card">
      <div class="stat-value">{{ getTotalCommandes() }}</div>
      <div class="stat-label">Commandes</div>
    </div>
</div>
`, styles: ['/* src/app/components/admin/order-management/order-management.component.css */\n.order-management-container {\n  padding: 2rem;\n  max-width: 1400px;\n  margin: 0 auto;\n  background:\n    linear-gradient(\n      135deg,\n      #f8fafc 0%,\n      #e2e8f0 100%);\n  min-height: 100vh;\n}\n.page-header {\n  margin-bottom: 2rem;\n  background: white;\n  border-radius: 16px;\n  padding: 2rem;\n  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);\n  border: 1px solid rgba(255, 255, 255, 0.2);\n}\n.header-content {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  flex-wrap: wrap;\n  gap: 1rem;\n}\n.page-title {\n  display: flex;\n  align-items: center;\n  gap: 0.75rem;\n  font-size: 2.25rem;\n  font-weight: 700;\n  background:\n    linear-gradient(\n      135deg,\n      #667eea 0%,\n      #764ba2 100%);\n  -webkit-background-clip: text;\n  -webkit-text-fill-color: transparent;\n  background-clip: text;\n  margin: 0;\n}\n.header-actions {\n  display: flex;\n  gap: 1rem;\n  flex-wrap: wrap;\n}\n.filters-section {\n  background: white;\n  border-radius: 16px;\n  padding: 2rem;\n  margin-bottom: 2rem;\n  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);\n  border: 1px solid rgba(255, 255, 255, 0.2);\n  display: flex;\n  gap: 1.5rem;\n  flex-wrap: wrap;\n  align-items: center;\n  -webkit-backdrop-filter: blur(10px);\n  backdrop-filter: blur(10px);\n}\n.search-box {\n  position: relative;\n  flex: 1;\n  min-width: 300px;\n}\n.search-box i {\n  position: absolute;\n  left: 1rem;\n  top: 50%;\n  transform: translateY(-50%);\n  color: #64748b;\n}\n.search-input {\n  width: 100%;\n  padding: 0.75rem 1rem 0.75rem 2.5rem;\n  border: 2px solid #e2e8f0;\n  border-radius: 8px;\n  font-size: 1rem;\n  transition: border-color 0.2s;\n}\n.search-input:focus {\n  outline: none;\n  border-color: #3b82f6;\n}\n.filters {\n  display: flex;\n  gap: 1rem;\n  flex-wrap: wrap;\n}\n.filter-select,\n.filter-date {\n  padding: 0.75rem 1rem;\n  border: 2px solid #e2e8f0;\n  border-radius: 8px;\n  font-size: 1rem;\n  background: white;\n  min-width: 150px;\n}\n.table-container {\n  background: white;\n  border-radius: 12px;\n  overflow: hidden;\n  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);\n  margin-bottom: 2rem;\n}\n.orders-table {\n  width: 100%;\n  border-collapse: collapse;\n  table-layout: fixed;\n}\n.orders-table th {\n  background: #f8fafc;\n  padding: 1rem;\n  text-align: left;\n  font-weight: 600;\n  color: #374151;\n  border-bottom: 1px solid #e5e7eb;\n}\n.orders-table th:nth-child(1) {\n  width: 15%;\n}\n.orders-table th:nth-child(2) {\n  width: 25%;\n}\n.orders-table th:nth-child(3) {\n  width: 20%;\n}\n.orders-table th:nth-child(4) {\n  width: 15%;\n}\n.orders-table th:nth-child(5) {\n  width: 12%;\n}\n.orders-table th:nth-child(6) {\n  width: 13%;\n}\n.orders-table th:nth-child(7) {\n  width: 10%;\n}\n.order-row {\n  border-bottom: 1px solid #f3f4f6;\n  transition: background-color 0.2s;\n}\n.order-row:hover {\n  background: #f9fafb;\n}\n.orders-table td {\n  padding: 1rem;\n  vertical-align: middle;\n  word-wrap: break-word;\n  overflow: hidden;\n}\n.order-number {\n  display: flex;\n  flex-direction: column;\n  gap: 0.25rem;\n}\n.number-main {\n  font-weight: 600;\n  color: #1a202c;\n  font-family: monospace;\n}\n.order-id {\n  font-size: 0.875rem;\n  color: #64748b;\n}\n.client-info {\n  display: flex;\n  flex-direction: column;\n  gap: 0.25rem;\n}\n.client-name {\n  font-weight: 600;\n  color: #1a202c;\n  font-size: 0.95rem;\n}\n.client-email {\n  font-size: 0.8rem;\n  color: #4f46e5;\n  font-family: monospace;\n}\n.supplier-name {\n  color: #059669;\n  font-weight: 500;\n  font-size: 0.9rem;\n  line-height: 1.4;\n  max-height: 2.8rem;\n  overflow: hidden;\n  text-overflow: ellipsis;\n}\n.amount {\n  font-weight: 700;\n  color: #059669;\n  font-size: 1.125rem;\n}\n.date-cell {\n  color: #64748b;\n  font-size: 0.875rem;\n}\n.status-badge {\n  padding: 0.5rem 1rem;\n  border-radius: 20px;\n  font-size: 0.8rem;\n  font-weight: 600;\n  text-transform: uppercase;\n  letter-spacing: 0.5px;\n  position: relative;\n  overflow: hidden;\n  -webkit-backdrop-filter: blur(10px);\n  backdrop-filter: blur(10px);\n  border: 1px solid rgba(255, 255, 255, 0.2);\n}\n.status-pending {\n  background:\n    linear-gradient(\n      135deg,\n      #fef3c7 0%,\n      #fde68a 100%);\n  color: #92400e;\n  box-shadow: 0 2px 8px rgba(251, 191, 36, 0.3);\n}\n.status-confirmed {\n  background:\n    linear-gradient(\n      135deg,\n      #dbeafe 0%,\n      #bfdbfe 100%);\n  color: #1e40af;\n  box-shadow: 0 2px 8px rgba(59, 130, 246, 0.3);\n}\n.status-preparing {\n  background:\n    linear-gradient(\n      135deg,\n      #e0e7ff 0%,\n      #c7d2fe 100%);\n  color: #4338ca;\n  box-shadow: 0 2px 8px rgba(99, 102, 241, 0.3);\n}\n.status-shipped {\n  background:\n    linear-gradient(\n      135deg,\n      #f3e8ff 0%,\n      #e9d5ff 100%);\n  color: #7c2d12;\n  box-shadow: 0 2px 8px rgba(147, 51, 234, 0.3);\n}\n.status-delivered {\n  background:\n    linear-gradient(\n      135deg,\n      #d1fae5 0%,\n      #a7f3d0 100%);\n  color: #065f46;\n  box-shadow: 0 2px 8px rgba(5, 150, 105, 0.3);\n}\n.status-cancelled {\n  background:\n    linear-gradient(\n      135deg,\n      #fee2e2 0%,\n      #fecaca 100%);\n  color: #991b1b;\n  box-shadow: 0 2px 8px rgba(220, 38, 38, 0.3);\n}\n.status-unknown {\n  background:\n    linear-gradient(\n      135deg,\n      #f3f4f6 0%,\n      #e5e7eb 100%);\n  color: #4b5563;\n  box-shadow: 0 2px 8px rgba(107, 114, 128, 0.3);\n}\n.actions-cell {\n  width: 100px;\n}\n.action-buttons {\n  display: flex;\n  gap: 0.5rem;\n}\n.btn-action {\n  width: 32px;\n  height: 32px;\n  border: none;\n  border-radius: 6px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  cursor: pointer;\n  transition: all 0.2s;\n}\n.btn-view {\n  background: #dbeafe;\n  color: #2563eb;\n}\n.btn-view:hover {\n  background: #bfdbfe;\n}\n.btn-cancel {\n  background: #fee2e2;\n  color: #dc2626;\n}\n.btn-cancel:hover {\n  background: #fecaca;\n}\n.btn {\n  padding: 0.875rem 1.75rem;\n  border: none;\n  border-radius: 12px;\n  font-weight: 600;\n  cursor: pointer;\n  transition: all 0.3s ease;\n  display: flex;\n  align-items: center;\n  gap: 0.5rem;\n  position: relative;\n  overflow: hidden;\n  text-transform: uppercase;\n  letter-spacing: 0.5px;\n  font-size: 0.875rem;\n}\n.btn::before {\n  content: "";\n  position: absolute;\n  top: 0;\n  left: -100%;\n  width: 100%;\n  height: 100%;\n  background:\n    linear-gradient(\n      90deg,\n      transparent,\n      rgba(255, 255, 255, 0.2),\n      transparent);\n  transition: left 0.5s;\n}\n.btn:hover::before {\n  left: 100%;\n}\n.btn-primary {\n  background:\n    linear-gradient(\n      135deg,\n      #667eea 0%,\n      #764ba2 100%);\n  color: white;\n  box-shadow: 0 4px 15px rgba(102, 126, 234, 0.4);\n}\n.btn-primary:hover {\n  transform: translateY(-2px);\n  box-shadow: 0 8px 25px rgba(102, 126, 234, 0.6);\n}\n.btn-secondary {\n  background:\n    linear-gradient(\n      135deg,\n      #f8fafc 0%,\n      #e2e8f0 100%);\n  color: #4a5568;\n  border: 1px solid rgba(226, 232, 240, 0.8);\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\n}\n.btn-secondary:hover {\n  transform: translateY(-2px);\n  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.15);\n  background:\n    linear-gradient(\n      135deg,\n      #e2e8f0 0%,\n      #cbd5e0 100%);\n}\n.btn:disabled {\n  opacity: 0.5;\n  cursor: not-allowed;\n}\n.pagination-container {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  padding: 1.5rem;\n  background: white;\n  border-radius: 16px;\n  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);\n  border: 1px solid rgba(255, 255, 255, 0.2);\n  -webkit-backdrop-filter: blur(10px);\n  backdrop-filter: blur(10px);\n  margin-bottom: 2rem;\n}\n.pagination-info {\n  color: #64748b;\n  font-size: 0.875rem;\n}\n.pagination {\n  display: flex;\n  align-items: center;\n  gap: 1rem;\n}\n.btn-page {\n  width: 36px;\n  height: 36px;\n  border: 1px solid #e5e7eb;\n  background: white;\n  border-radius: 6px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  cursor: pointer;\n  transition: all 0.2s;\n}\n.btn-page:hover:not(:disabled) {\n  background: #f3f4f6;\n}\n.btn-page:disabled {\n  opacity: 0.5;\n  cursor: not-allowed;\n}\n.page-info {\n  font-weight: 500;\n  color: #374151;\n}\n.stats-summary {\n  display: grid;\n  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));\n  gap: 1rem;\n  margin-top: 2rem;\n}\n.stat-card {\n  background: white;\n  border-radius: 12px;\n  padding: 1.5rem;\n  text-align: center;\n  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);\n}\n.stat-value {\n  font-size: 1.5rem;\n  font-weight: 700;\n  color: #1a202c;\n  margin-bottom: 0.5rem;\n}\n.stat-label {\n  color: #64748b;\n  font-size: 0.875rem;\n}\n.error-message {\n  background: #fef2f2;\n  border: 1px solid #fecaca;\n  color: #dc2626;\n  padding: 1rem;\n  border-radius: 8px;\n  margin-bottom: 2rem;\n  display: flex;\n  align-items: center;\n  gap: 0.5rem;\n}\n.loading-container {\n  text-align: center;\n  padding: 3rem;\n}\n.loading-spinner {\n  width: 40px;\n  height: 40px;\n  border: 4px solid #e2e8f0;\n  border-top: 4px solid #3b82f6;\n  border-radius: 50%;\n  animation: spin 1s linear infinite;\n  margin: 0 auto 1rem;\n}\n@keyframes spin {\n  0% {\n    transform: rotate(0deg);\n  }\n  100% {\n    transform: rotate(360deg);\n  }\n}\n.no-data {\n  text-align: center;\n  padding: 3rem;\n  color: #64748b;\n}\n.no-data i {\n  font-size: 3rem;\n  margin-bottom: 1rem;\n  opacity: 0.5;\n}\n.no-data h3 {\n  margin: 0 0 0.5rem 0;\n  color: #374151;\n}\n.icon-shopping-cart::before {\n  content: "\\1f6cd\\fe0f";\n  font-size: 1.2em;\n}\n.icon-filter-x::before {\n  content: "\\1f5c2\\fe0f";\n  font-size: 1.1em;\n}\n.icon-refresh::before {\n  content: "\\1f504";\n  font-size: 1.1em;\n}\n.icon-download::before {\n  content: "\\1f4be";\n  font-size: 1.1em;\n}\n.icon-search::before {\n  content: "\\1f50d";\n  font-size: 1.1em;\n}\n.icon-alert::before {\n  content: "\\26a0\\fe0f";\n  font-size: 1.1em;\n}\n.icon-eye::before {\n  content: "\\1f440";\n  font-size: 1.1em;\n}\n.icon-x::before {\n  content: "\\274c";\n  font-size: 1.1em;\n}\n.icon-chevron-left::before {\n  content: "\\2b05\\fe0f";\n  font-size: 1.1em;\n}\n.icon-chevron-right::before {\n  content: "\\27a1\\fe0f";\n  font-size: 1.1em;\n}\n.icon-chevron-down::before {\n  content: "\\1f53d";\n  font-size: 0.9em;\n}\n.icon-layers::before {\n  content: "\\1f4ca";\n  font-size: 1.1em;\n}\n.icon-truck::before {\n  content: "\\1f69b";\n  font-size: 1.1em;\n}\n.icon-calendar::before {\n  content: "\\1f4c5";\n  font-size: 1.1em;\n}\n.icon-package::before {\n  content: "\\1f4e6";\n  font-size: 1.1em;\n}\n@media (max-width: 768px) {\n  .order-management-container {\n    padding: 1rem;\n  }\n  .header-content {\n    flex-direction: column;\n    align-items: stretch;\n  }\n  .filters-section {\n    flex-direction: column;\n  }\n  .search-box {\n    min-width: auto;\n  }\n  .filters {\n    justify-content: stretch;\n  }\n  .filter-select,\n  .filter-date {\n    min-width: auto;\n    flex: 1;\n  }\n  .orders-table {\n    font-size: 0.875rem;\n  }\n  .orders-table th,\n  .orders-table td {\n    padding: 0.75rem 0.5rem;\n  }\n  .pagination-container {\n    flex-direction: column;\n    gap: 1rem;\n  }\n  .stats-summary {\n    grid-template-columns: 1fr;\n  }\n}\n.detailed-orders-container {\n  display: flex;\n  flex-direction: column;\n  gap: 1.5rem;\n}\n.order-card {\n  background: white;\n  border-radius: 16px;\n  border: 1px solid rgba(255, 255, 255, 0.2);\n  overflow: hidden;\n  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);\n  -webkit-backdrop-filter: blur(10px);\n  backdrop-filter: blur(10px);\n  transition: all 0.3s ease;\n  position: relative;\n}\n.order-card:hover {\n  transform: translateY(-4px);\n  box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15);\n}\n.order-card::before {\n  content: "";\n  position: absolute;\n  top: 0;\n  left: 0;\n  right: 0;\n  height: 4px;\n  background:\n    linear-gradient(\n      90deg,\n      #667eea 0%,\n      #764ba2 100%);\n}\n.order-header {\n  background:\n    linear-gradient(\n      135deg,\n      #f8fafc 0%,\n      #e2e8f0 100%);\n  padding: 2rem;\n  border-bottom: 1px solid rgba(229, 231, 235, 0.5);\n  display: flex;\n  justify-content: space-between;\n  align-items: flex-start;\n  gap: 1rem;\n}\n.order-title {\n  font-size: 1.25rem;\n  font-weight: bold;\n  color: #1f2937;\n  margin: 0 0 0.5rem 0;\n}\n.client-info {\n  color: #4b5563;\n  margin-bottom: 0.75rem;\n}\n.order-meta {\n  display: flex;\n  gap: 1rem;\n  align-items: center;\n  flex-wrap: wrap;\n}\n.order-date {\n  color: #6b7280;\n  font-size: 0.875rem;\n}\n.order-amount {\n  font-weight: bold;\n  color: #059669;\n  font-size: 1.1rem;\n}\n.supplier-orders {\n  padding: 1.5rem;\n}\n.supplier-orders-title {\n  display: flex;\n  align-items: center;\n  gap: 0.5rem;\n  font-size: 1.1rem;\n  font-weight: 600;\n  color: #374151;\n  margin: 0 0 1rem 0;\n}\n.no-supplier-orders {\n  text-align: center;\n  color: #6b7280;\n  font-style: italic;\n  padding: 2rem;\n}\n.supplier-order-card {\n  background: #f9fafb;\n  border: 1px solid #e5e7eb;\n  border-radius: 8px;\n  padding: 1rem;\n  margin-bottom: 1rem;\n}\n.supplier-order-card:last-child {\n  margin-bottom: 0;\n}\n.supplier-order-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: flex-start;\n  margin-bottom: 1rem;\n}\n.supplier-info strong {\n  color: #1f2937;\n  font-size: 1rem;\n}\n.supplier-email {\n  color: #6b7280;\n  font-size: 0.875rem;\n  display: block;\n  margin-top: 0.25rem;\n}\n.supplier-order-meta {\n  display: flex;\n  flex-direction: column;\n  align-items: flex-end;\n  gap: 0.5rem;\n}\n.supplier-reference {\n  font-family: monospace;\n  background: #e5e7eb;\n  padding: 0.25rem 0.5rem;\n  border-radius: 4px;\n  font-size: 0.875rem;\n}\n.supplier-amount {\n  font-weight: bold;\n  color: #059669;\n}\n.delivery-info {\n  background: #eff6ff;\n  border: 1px solid #dbeafe;\n  border-radius: 6px;\n  padding: 0.75rem;\n  margin-bottom: 1rem;\n  display: flex;\n  gap: 1rem;\n  flex-wrap: wrap;\n}\n.delivery-date,\n.delivery-number {\n  display: flex;\n  align-items: center;\n  gap: 0.5rem;\n  color: #1e40af;\n  font-size: 0.875rem;\n}\n.order-lines {\n  border-top: 1px solid #e5e7eb;\n  padding-top: 1rem;\n}\n.order-line {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  padding: 0.5rem 0;\n  border-bottom: 1px solid #f3f4f6;\n}\n.order-line:last-child {\n  border-bottom: none;\n}\n.product-info {\n  flex: 1;\n}\n.product-name {\n  font-weight: 500;\n  color: #1f2937;\n  display: block;\n}\n.product-ref {\n  color: #6b7280;\n  font-size: 0.875rem;\n  font-family: monospace;\n}\n.quantity-price {\n  display: flex;\n  align-items: center;\n  gap: 1rem;\n  font-size: 0.875rem;\n}\n.quantity {\n  color: #6b7280;\n}\n.unit-price {\n  color: #4b5563;\n}\n.line-total {\n  font-weight: 600;\n  color: #1f2937;\n  min-width: 80px;\n  text-align: right;\n}\n@media (max-width: 768px) {\n  .order-meta {\n    flex-direction: column;\n    align-items: flex-start;\n    gap: 0.5rem;\n  }\n  .supplier-order-header {\n    flex-direction: column;\n    gap: 1rem;\n  }\n  .supplier-order-meta {\n    align-items: flex-start;\n  }\n  .delivery-info {\n    flex-direction: column;\n    gap: 0.5rem;\n  }\n  .order-line {\n    flex-direction: column;\n    align-items: flex-start;\n    gap: 0.5rem;\n  }\n  .quantity-price {\n    justify-content: space-between;\n    width: 100%;\n  }\n}\n.order-actions {\n  display: flex;\n  align-items: center;\n  gap: 0.5rem;\n}\n.order-actions .btn {\n  display: flex;\n  align-items: center;\n  gap: 0.5rem;\n  transition: all 0.2s ease;\n}\n.order-actions .btn.active {\n  background-color: #3182ce;\n  color: white;\n}\n.icon-chevron-down {\n  transition: transform 0.2s ease;\n}\n.icon-chevron-down.rotated {\n  transform: rotate(180deg);\n}\n.action-buttons {\n  display: flex;\n  gap: 0.5rem;\n  align-items: center;\n}\n.btn-sm {\n  padding: 0.375rem 0.75rem;\n  font-size: 0.875rem;\n  border-radius: 6px;\n  border: 1px solid #e2e8f0;\n  background: white;\n  color: #4a5568;\n  cursor: pointer;\n  transition: all 0.2s ease;\n  display: flex;\n  align-items: center;\n  gap: 0.375rem;\n}\n.btn-sm:hover {\n  background-color: #f7fafc;\n  border-color: #cbd5e0;\n}\n.btn-sm.btn-primary {\n  background-color: #3182ce;\n  border-color: #3182ce;\n  color: white;\n}\n.btn-sm.btn-primary:hover {\n  background-color: #2c5aa0;\n  border-color: #2c5aa0;\n}\n.btn-sm.btn-outline {\n  background: transparent;\n  border-color: #e2e8f0;\n  color: #4a5568;\n}\n.btn-sm.btn-outline:hover {\n  background-color: #f7fafc;\n  border-color: #cbd5e0;\n}\n.stats-summary {\n  display: flex;\n  gap: 1rem;\n  margin-bottom: 1rem;\n}\n.stat-card {\n  background: white;\n  border-radius: 8px;\n  padding: 1rem;\n  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);\n  min-width: 120px;\n  text-align: center;\n}\n.stat-value {\n  font-size: 1.5rem;\n  font-weight: 600;\n  color: #2d3748;\n  margin-bottom: 0.25rem;\n}\n.stat-label {\n  font-size: 0.875rem;\n  color: #718096;\n  text-transform: uppercase;\n  letter-spacing: 0.05em;\n}\n/*# sourceMappingURL=order-management.component.css.map */\n'] }]
  }], () => [{ type: AdminService }], null);
})();
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && \u0275setClassDebugInfo(OrderManagementComponent, { className: "OrderManagementComponent", filePath: "src/app/components/admin/order-management/order-management.component.ts", lineNumber: 52 });
})();
export {
  OrderManagementComponent
};
//# sourceMappingURL=chunk-B5ZEQJLI.js.map

import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { Produit, StockUpdate } from '../../../models';
import { ProduitService } from '../../../services/produit.service';
import { ImageUrlService } from '../../../services/image-url.service';
import { AuthService } from '../../../services/auth.service';
import { StockMovementService, StockMovement, CreateStockMovementDto } from '../../../services/stock-movement.service';

interface StockAlert {
  product: Produit;
  type: 'low' | 'empty';
  message: string;
}

// Interface déplacée vers le service

@Component({
  selector: 'app-stock-management',
  standalone: true,
  imports: [CommonModule, FormsModule],
  templateUrl: './stock-management.component.html',
  styleUrls: ['./stock-management.component.css']
})
export class StockManagementComponent implements OnInit {
  products: Produit[] = [];
  stockAlerts: StockAlert[] = [];
  stockMovements: StockMovement[] = [];

  // Filtres
  selectedFilter = 'all'; // all, low, empty, active
  searchQuery = '';

  // Gestion du stock
  selectedProduct: Produit | null = null;
  showStockModal = false;
  stockOperation = {
    type: 'in' as 'in' | 'out',
    quantity: 0,
    reason: ''
  };
  
  // États
  isLoading = false;
  error = '';

  // Options de raisons
  stockReasons = {
    in: [
      'Réapprovisionnement',
      'Retour client',
      'Correction inventaire',
      'Transfert entrepôt',
      'Autre'
    ],
    out: [
      'Vente',
      'Produit défectueux',
      'Échantillon',
      'Perte/Vol',
      'Correction inventaire',
      'Autre'
    ]
  };

  constructor(
    private produitService: ProduitService,
    private imageUrlService: ImageUrlService,
    private authService: AuthService,
    private stockMovementService: StockMovementService
  ) {}

  ngOnInit(): void {
    this.loadData();
    this.loadStockMovements();
  }

  /**
   * Charger les données depuis l'API backend
   */
  loadData(): void {
    this.isLoading = true;
    this.error = '';

    // Récupérer l'ID du fournisseur connecté
    const fournisseurId = this.authService.getCurrentUserId();
    if (!fournisseurId) {
      this.error = 'Utilisateur non connecté';
      this.isLoading = false;
      return;
    }

    this.produitService.getByFournisseur(fournisseurId).subscribe({
      next: (products) => {
        this.products = products;
        this.generateStockAlerts();
        this.isLoading = false;
      },
      error: (error) => {
        this.error = 'Erreur lors du chargement des produits';
        console.error('Error loading products:', error);
        this.isLoading = false;
      }
    });
  }

  /**
   * Générer les alertes de stock selon le modèle backend
   */
  generateStockAlerts(): void {
    this.stockAlerts = [];

    this.products.forEach(product => {
      const seuilAlerte = 5; // Seuil par défaut, pourrait être configuré

      if (product.stock === 0) {
        this.stockAlerts.push({
          product,
          type: 'empty',
          message: `Rupture de stock`
        });
      } else if (product.stock <= seuilAlerte) {
        this.stockAlerts.push({
          product,
          type: 'low',
          message: `Stock faible (${product.stock} restant)`
        });
      }
    });
  }

  /**
   * Charger les mouvements de stock depuis le service
   */
  loadStockMovements(): void {
    this.stockMovementService.getAllMovements().subscribe({
      next: (movements) => {
        this.stockMovements = movements;
      },
      error: (error) => {
        console.error('Erreur lors du chargement des mouvements:', error);
        this.stockMovements = [];
      }
    });
  }

  /**
   * Filtrer les produits
   */
  getFilteredProducts(): Produit[] {
    let filtered = [...this.products];

    // Filtre par recherche
    if (this.searchQuery.trim()) {
      const query = this.searchQuery.toLowerCase();
      filtered = filtered.filter(product =>
        product.nom.toLowerCase().includes(query) ||
        (product.description && product.description.toLowerCase().includes(query))
      );
    }

    // Filtre par statut
    switch (this.selectedFilter) {
      case 'low':
        filtered = filtered.filter(p => p.stock <= 5 && p.stock > 0);
        break;
      case 'empty':
        filtered = filtered.filter(p => p.stock === 0);
        break;
      case 'active':
        // Tous les produits sont considérés comme actifs
        break;
    }

    return filtered;
  }

  /**
   * Ouvrir le modal de gestion du stock
   */
  openStockModal(product: Produit): void {
    this.selectedProduct = product;
    this.stockOperation = {
      type: 'in',
      quantity: 0,
      reason: ''
    };
    this.showStockModal = true;
  }

  /**
   * Fermer le modal
   */
  closeStockModal(): void {
    this.showStockModal = false;
    this.selectedProduct = null;
  }

  /**
   * Appliquer l'opération de stock
   */
  applyStockOperation(): void {
    if (!this.selectedProduct || this.stockOperation.quantity <= 0) {
      alert('Veuillez saisir une quantité valide');
      return;
    }

    if (!this.stockOperation.reason.trim()) {
      alert('Veuillez sélectionner une raison');
      return;
    }

    const newStock = this.stockOperation.type === 'in'
      ? this.selectedProduct.stock + this.stockOperation.quantity
      : this.selectedProduct.stock - this.stockOperation.quantity;

    if (newStock < 0) {
      alert('Stock insuffisant pour cette opération');
      return;
    }

    // Mettre à jour le stock via l'API backend
    const stockUpdate: StockUpdate = {
      id: this.selectedProduct.id,
      stock: newStock
    };

    this.produitService.updateStock(this.selectedProduct.id, stockUpdate).subscribe({
      next: () => {
        const oldStock = this.selectedProduct!.stock;

        // Mettre à jour le produit dans la liste locale
        const index = this.products.findIndex(p => p.id === this.selectedProduct!.id);
        if (index !== -1) {
          this.products[index].stock = newStock;
        }

        // Enregistrer le mouvement de stock via le service
        const movementDto: CreateStockMovementDto = {
          productId: this.selectedProduct!.id,
          productName: this.selectedProduct!.nom,
          type: this.stockOperation.type,
          quantity: this.stockOperation.quantity,
          reason: this.stockOperation.reason,
          oldStock: oldStock,
          newStock: newStock
        };

        this.stockMovementService.addMovement(movementDto).subscribe({
          next: () => {
            console.log('Mouvement de stock enregistré');
          },
          error: (error) => {
            console.error('Erreur lors de l\'enregistrement du mouvement:', error);
          }
        });

        // Régénérer les alertes
        this.generateStockAlerts();

        this.closeStockModal();
        alert('Stock mis à jour avec succès');
      },
      error: (error) => {
        alert('Erreur lors de la mise à jour du stock');
        console.error('Error updating stock:', error);
      }
    });
  }

  /**
   * Obtenir la classe CSS pour le niveau de stock
   */
  getStockLevelClass(product: Produit): string {
    if (product.stock === 0) return 'stock-empty';
    if (product.stock <= 5) return 'stock-low';
    return 'stock-ok';
  }

  /**
   * Obtenir le texte du niveau de stock
   */
  getStockLevelText(product: Produit): string {
    if (product.stock === 0) return 'Rupture';
    if (product.stock <= 5) return 'Faible';
    return 'Suffisant';
  }

  /**
   * Obtenir la classe CSS pour l'alerte
   */
  getAlertClass(alert: StockAlert): string {
    return alert.type === 'empty' ? 'alert-danger' : 'alert-warning';
  }

  /**
   * Formater la date
   */
  formatDate(date: string): string {
    return new Date(date).toLocaleDateString('fr-FR', {
      day: '2-digit',
      month: '2-digit',
      year: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  }

  /**
   * Formater le prix
   */
  formatPrice(price: number): string {
    return new Intl.NumberFormat('fr-TN', {
      style: 'currency',
      currency: 'TND'
    }).format(price);
  }

  /**
   * Obtenir l'icône pour le type de mouvement
   */
  getMovementIcon(type: 'in' | 'out'): string {
    return type === 'in' ? '🔼' : '🔽';
  }

  /**
   * Obtenir la classe CSS pour le type de mouvement
   */
  getMovementClass(type: 'in' | 'out'): string {
    return type === 'in' ? 'movement-in' : 'movement-out';
  }

  /**
   * Calculer la valeur du stock
   */
  getStockValue(product: Produit): number {
    return product.stock * product.prixAchatHT;
  }

  /**
   * Obtenir les statistiques globales
   */
  getGlobalStats() {
    const totalProducts = this.products.length;
    const lowStockProducts = this.products.filter(p => p.stock <= 5 && p.stock > 0).length;
    const emptyStockProducts = this.products.filter(p => p.stock === 0).length;
    const totalStockValue = this.products.reduce((sum, p) => sum + this.getStockValue(p), 0);

    return {
      totalProducts,
      lowStockProducts,
      emptyStockProducts,
      totalStockValue
    };
  }

  /**
   * Obtenir l'URL de l'image d'un produit
   */
  getProductImageUrl(product: Produit): string {
    // Vérifier d'abord imagePrincipaleUrl
    if (product.imagePrincipaleUrl) {
      return this.imageUrlService.getProduitImageUrl(product.imagePrincipaleUrl);
    }

    // Ensuite vérifier le tableau images
    if (product.images && product.images.length > 0) {
      const mainImage = product.images.find(img => img.isMain);
      const imageUrl = mainImage?.imageUrl || product.images[0].imageUrl;
      return this.imageUrlService.getProduitImageUrl(imageUrl);
    }

    // Retourner le placeholder par défaut
    return this.imageUrlService.getPlaceholderUrl();
  }

  /**
   * Obtenir le nom de la catégorie d'un produit
   */
  getProductCategory(product: Produit): string {
    return product.sousCategorie?.nom || 'Non définie';
  }
}

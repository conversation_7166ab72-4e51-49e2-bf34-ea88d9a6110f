import {
  ImageUrlService
} from "./chunk-CZWG52C5.js";
import {
  CheckboxControlValueAccessor,
  DefaultValueAccessor,
  FormsModule,
  MinValidator,
  NgControlStatus,
  NgModel,
  NgSelectOption,
  NumberValueAccessor,
  SelectControlValueAccessor,
  ɵNgSelectMultipleOption
} from "./chunk-HQBVYEOO.js";
import {
  AdminService
} from "./chunk-EFJVWLOV.js";
import "./chunk-GFHHPDQ6.js";
import "./chunk-7JDDWGD3.js";
import {
  CommonModule,
  Component,
  NgClass,
  NgForOf,
  NgIf,
  computed,
  setClassMetadata,
  signal,
  ɵsetClassDebugInfo,
  ɵɵadvance,
  ɵɵclassProp,
  ɵɵdefineComponent,
  ɵɵdirectiveInject,
  ɵɵelement,
  ɵɵelementEnd,
  ɵɵelementStart,
  ɵɵgetCurrentView,
  ɵɵlistener,
  ɵɵnextContext,
  ɵɵproperty,
  ɵɵpureFunction1,
  ɵɵresetView,
  ɵɵrestoreView,
  ɵɵsanitizeUrl,
  ɵɵtemplate,
  ɵɵtext,
  ɵɵtextInterpolate,
  ɵɵtextInterpolate1,
  ɵɵtextInterpolate3,
  ɵɵtwoWayBindingSet,
  ɵɵtwoWayListener,
  ɵɵtwoWayProperty
} from "./chunk-UBZQS7JS.js";

// src/app/components/admin/product-management/product-management.component.ts
var _c0 = (a0) => ({ "stock-critique": a0 });
var _c1 = (a0) => ({ "active": a0 });
function ProductManagementComponent_option_14_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "option", 23);
    \u0275\u0275text(1);
    \u0275\u0275elementEnd();
  }
  if (rf & 2) {
    const option_r1 = ctx.$implicit;
    \u0275\u0275property("value", option_r1.value);
    \u0275\u0275advance();
    \u0275\u0275textInterpolate1(" ", option_r1.label, " ");
  }
}
function ProductManagementComponent_div_45_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "div", 24);
    \u0275\u0275text(1);
    \u0275\u0275elementEnd();
  }
  if (rf & 2) {
    const ctx_r1 = \u0275\u0275nextContext();
    \u0275\u0275advance();
    \u0275\u0275textInterpolate1(" \u274C ", ctx_r1.error(), " ");
  }
}
function ProductManagementComponent_div_46_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "div", 25);
    \u0275\u0275element(1, "div", 26);
    \u0275\u0275elementStart(2, "p");
    \u0275\u0275text(3, "Chargement des produits...");
    \u0275\u0275elementEnd()();
  }
}
function ProductManagementComponent_div_47_tr_23_span_9_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "span", 69);
    \u0275\u0275text(1, "\u2B50 En avant");
    \u0275\u0275elementEnd();
  }
}
function ProductManagementComponent_div_47_tr_23_div_13_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "div", 70);
    \u0275\u0275text(1);
    \u0275\u0275elementEnd();
  }
  if (rf & 2) {
    const produit_r4 = \u0275\u0275nextContext().$implicit;
    \u0275\u0275advance();
    \u0275\u0275textInterpolate1(" Fournisseur: ", produit_r4.referenceFournisseur, " ");
  }
}
function ProductManagementComponent_div_47_tr_23_div_21_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "div", 71)(1, "span", 45);
    \u0275\u0275text(2, "Apr\xE8s outlet:");
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(3, "span", 72);
    \u0275\u0275text(4);
    \u0275\u0275elementEnd()();
  }
  if (rf & 2) {
    const produit_r4 = \u0275\u0275nextContext().$implicit;
    const ctx_r1 = \u0275\u0275nextContext(2);
    \u0275\u0275advance(4);
    \u0275\u0275textInterpolate(ctx_r1.formatPrice(produit_r4.prixApresOutletTTC));
  }
}
function ProductManagementComponent_div_47_tr_23_div_22_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "div", 73)(1, "span", 45);
    \u0275\u0275text(2, "Prix final:");
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(3, "span", 74);
    \u0275\u0275text(4);
    \u0275\u0275elementEnd()();
  }
  if (rf & 2) {
    const produit_r4 = \u0275\u0275nextContext().$implicit;
    const ctx_r1 = \u0275\u0275nextContext(2);
    \u0275\u0275advance(4);
    \u0275\u0275textInterpolate(ctx_r1.formatPrice(produit_r4.prixFinalTTC));
  }
}
function ProductManagementComponent_div_47_tr_23_div_23_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "div", 75);
    \u0275\u0275text(1);
    \u0275\u0275elementEnd();
  }
  if (rf & 2) {
    const produit_r4 = \u0275\u0275nextContext().$implicit;
    \u0275\u0275advance();
    \u0275\u0275textInterpolate1(" -", produit_r4.pourcentageRemiseTotale, "% ");
  }
}
function ProductManagementComponent_div_47_tr_23_div_28_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "div", 76);
    \u0275\u0275text(1, " \u26A0\uFE0F Stock critique (\u226410) ");
    \u0275\u0275elementEnd();
  }
}
function ProductManagementComponent_div_47_tr_23_span_41_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "span", 77);
    \u0275\u0275text(1, "\u26A0\uFE0F Stock critique");
    \u0275\u0275elementEnd();
  }
}
function ProductManagementComponent_div_47_tr_23_button_44_Template(rf, ctx) {
  if (rf & 1) {
    const _r5 = \u0275\u0275getCurrentView();
    \u0275\u0275elementStart(0, "button", 78);
    \u0275\u0275listener("click", function ProductManagementComponent_div_47_tr_23_button_44_Template_button_click_0_listener() {
      \u0275\u0275restoreView(_r5);
      const produit_r4 = \u0275\u0275nextContext().$implicit;
      const ctx_r1 = \u0275\u0275nextContext(2);
      return \u0275\u0275resetView(ctx_r1.validerProduit(produit_r4));
    });
    \u0275\u0275text(1, " \u2705 ");
    \u0275\u0275elementEnd();
  }
}
function ProductManagementComponent_div_47_tr_23_button_45_Template(rf, ctx) {
  if (rf & 1) {
    const _r6 = \u0275\u0275getCurrentView();
    \u0275\u0275elementStart(0, "button", 79);
    \u0275\u0275listener("click", function ProductManagementComponent_div_47_tr_23_button_45_Template_button_click_0_listener() {
      \u0275\u0275restoreView(_r6);
      const produit_r4 = \u0275\u0275nextContext().$implicit;
      const ctx_r1 = \u0275\u0275nextContext(2);
      return \u0275\u0275resetView(ctx_r1.refuserProduit(produit_r4));
    });
    \u0275\u0275text(1, " \u274C ");
    \u0275\u0275elementEnd();
  }
}
function ProductManagementComponent_div_47_tr_23_Template(rf, ctx) {
  if (rf & 1) {
    const _r3 = \u0275\u0275getCurrentView();
    \u0275\u0275elementStart(0, "tr", 31)(1, "td", 32)(2, "img", 33);
    \u0275\u0275listener("error", function ProductManagementComponent_div_47_tr_23_Template_img_error_2_listener($event) {
      \u0275\u0275restoreView(_r3);
      const ctx_r1 = \u0275\u0275nextContext(2);
      return \u0275\u0275resetView(ctx_r1.onImageError($event));
    });
    \u0275\u0275elementEnd()();
    \u0275\u0275elementStart(3, "td", 34)(4, "div", 35);
    \u0275\u0275text(5);
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(6, "div", 36)(7, "span", 37);
    \u0275\u0275text(8);
    \u0275\u0275elementEnd();
    \u0275\u0275template(9, ProductManagementComponent_div_47_tr_23_span_9_Template, 2, 0, "span", 38);
    \u0275\u0275elementEnd()();
    \u0275\u0275elementStart(10, "td", 39)(11, "div", 40);
    \u0275\u0275text(12);
    \u0275\u0275elementEnd();
    \u0275\u0275template(13, ProductManagementComponent_div_47_tr_23_div_13_Template, 2, 1, "div", 41);
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(14, "td", 42)(15, "div", 43)(16, "div", 44)(17, "span", 45);
    \u0275\u0275text(18, "Prix initial:");
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(19, "span", 46);
    \u0275\u0275text(20);
    \u0275\u0275elementEnd()();
    \u0275\u0275template(21, ProductManagementComponent_div_47_tr_23_div_21_Template, 5, 1, "div", 47)(22, ProductManagementComponent_div_47_tr_23_div_22_Template, 5, 1, "div", 48)(23, ProductManagementComponent_div_47_tr_23_div_23_Template, 2, 1, "div", 49);
    \u0275\u0275elementEnd()();
    \u0275\u0275elementStart(24, "td", 50)(25, "div", 51)(26, "span", 52);
    \u0275\u0275text(27);
    \u0275\u0275elementEnd();
    \u0275\u0275template(28, ProductManagementComponent_div_47_tr_23_div_28_Template, 2, 0, "div", 53);
    \u0275\u0275elementStart(29, "button", 54);
    \u0275\u0275listener("click", function ProductManagementComponent_div_47_tr_23_Template_button_click_29_listener() {
      const produit_r4 = \u0275\u0275restoreView(_r3).$implicit;
      const ctx_r1 = \u0275\u0275nextContext(2);
      return \u0275\u0275resetView(ctx_r1.openStockModal(produit_r4));
    });
    \u0275\u0275text(30, " \u{1F4E6} ");
    \u0275\u0275elementEnd()()();
    \u0275\u0275elementStart(31, "td", 55);
    \u0275\u0275text(32);
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(33, "td", 56)(34, "div", 57);
    \u0275\u0275text(35);
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(36, "div", 58);
    \u0275\u0275text(37);
    \u0275\u0275elementEnd()();
    \u0275\u0275elementStart(38, "td", 59)(39, "span", 60);
    \u0275\u0275text(40);
    \u0275\u0275elementEnd();
    \u0275\u0275template(41, ProductManagementComponent_div_47_tr_23_span_41_Template, 2, 0, "span", 61);
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(42, "td", 62)(43, "div", 63);
    \u0275\u0275template(44, ProductManagementComponent_div_47_tr_23_button_44_Template, 2, 0, "button", 64)(45, ProductManagementComponent_div_47_tr_23_button_45_Template, 2, 0, "button", 65);
    \u0275\u0275elementStart(46, "button", 66);
    \u0275\u0275listener("click", function ProductManagementComponent_div_47_tr_23_Template_button_click_46_listener() {
      const produit_r4 = \u0275\u0275restoreView(_r3).$implicit;
      const ctx_r1 = \u0275\u0275nextContext(2);
      return \u0275\u0275resetView(ctx_r1.toggleMiseEnAvant(produit_r4));
    });
    \u0275\u0275text(47);
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(48, "button", 67);
    \u0275\u0275listener("click", function ProductManagementComponent_div_47_tr_23_Template_button_click_48_listener() {
      const produit_r4 = \u0275\u0275restoreView(_r3).$implicit;
      const ctx_r1 = \u0275\u0275nextContext(2);
      return \u0275\u0275resetView(ctx_r1.openModerationModal(produit_r4));
    });
    \u0275\u0275text(49, " \u{1F50D} ");
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(50, "button", 68);
    \u0275\u0275listener("click", function ProductManagementComponent_div_47_tr_23_Template_button_click_50_listener() {
      const produit_r4 = \u0275\u0275restoreView(_r3).$implicit;
      const ctx_r1 = \u0275\u0275nextContext(2);
      return \u0275\u0275resetView(ctx_r1.supprimerProduit(produit_r4));
    });
    \u0275\u0275text(51, " \u{1F5D1}\uFE0F ");
    \u0275\u0275elementEnd()()()();
  }
  if (rf & 2) {
    const produit_r4 = ctx.$implicit;
    const ctx_r1 = \u0275\u0275nextContext(2);
    \u0275\u0275property("ngClass", ctx_r1.getStatusClass(produit_r4));
    \u0275\u0275advance(2);
    \u0275\u0275property("src", ctx_r1.imageUrlService.getProduitImageUrl(produit_r4.imagePrincipale || (produit_r4.images && produit_r4.images.length > 0 ? produit_r4.images[0] : null)), \u0275\u0275sanitizeUrl)("alt", produit_r4.nom);
    \u0275\u0275advance(3);
    \u0275\u0275textInterpolate(produit_r4.nom);
    \u0275\u0275advance(3);
    \u0275\u0275textInterpolate1("Ajout\xE9 le ", ctx_r1.formatDate(produit_r4.dateCreation), "");
    \u0275\u0275advance();
    \u0275\u0275property("ngIf", produit_r4.estEnAvant);
    \u0275\u0275advance(3);
    \u0275\u0275textInterpolate(produit_r4.referenceOriginal);
    \u0275\u0275advance();
    \u0275\u0275property("ngIf", produit_r4.referenceFournisseur);
    \u0275\u0275advance(6);
    \u0275\u0275classProp("crossed", produit_r4.pourcentageRemiseTotale > 0);
    \u0275\u0275advance();
    \u0275\u0275textInterpolate1(" ", ctx_r1.formatPrice(produit_r4.prixVenteTTC), " ");
    \u0275\u0275advance();
    \u0275\u0275property("ngIf", produit_r4.prixApresOutletTTC !== produit_r4.prixVenteTTC);
    \u0275\u0275advance();
    \u0275\u0275property("ngIf", produit_r4.prixFinalTTC !== produit_r4.prixApresOutletTTC);
    \u0275\u0275advance();
    \u0275\u0275property("ngIf", produit_r4.pourcentageRemiseTotale > 0);
    \u0275\u0275advance(3);
    \u0275\u0275property("ngClass", \u0275\u0275pureFunction1(28, _c0, produit_r4.estStockCritique));
    \u0275\u0275advance();
    \u0275\u0275textInterpolate1(" ", produit_r4.stock, " ");
    \u0275\u0275advance();
    \u0275\u0275property("ngIf", produit_r4.estStockCritique);
    \u0275\u0275advance(4);
    \u0275\u0275textInterpolate1(" ", produit_r4.fournisseurNom, " ");
    \u0275\u0275advance(3);
    \u0275\u0275textInterpolate(produit_r4.categorieNom);
    \u0275\u0275advance(2);
    \u0275\u0275textInterpolate(produit_r4.sousCategorieNom);
    \u0275\u0275advance(2);
    \u0275\u0275property("ngClass", ctx_r1.getStatusClass(produit_r4));
    \u0275\u0275advance();
    \u0275\u0275textInterpolate1(" ", produit_r4.statutValidation, " ");
    \u0275\u0275advance();
    \u0275\u0275property("ngIf", produit_r4.estStockCritique);
    \u0275\u0275advance(3);
    \u0275\u0275property("ngIf", !produit_r4.estValide);
    \u0275\u0275advance();
    \u0275\u0275property("ngIf", !produit_r4.estValide);
    \u0275\u0275advance();
    \u0275\u0275property("ngClass", produit_r4.estEnAvant ? "btn-unfeature" : "btn-feature")("title", produit_r4.estEnAvant ? "Retirer la mise en avant" : "Mettre en avant");
    \u0275\u0275advance();
    \u0275\u0275textInterpolate1(" ", produit_r4.estEnAvant ? "\u2B50" : "\u2606", " ");
  }
}
function ProductManagementComponent_div_47_div_24_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "div", 80)(1, "p");
    \u0275\u0275text(2, "Aucun produit trouv\xE9 avec les crit\xE8res s\xE9lectionn\xE9s.");
    \u0275\u0275elementEnd()();
  }
}
function ProductManagementComponent_div_47_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "div", 27)(1, "table", 28)(2, "thead")(3, "tr")(4, "th");
    \u0275\u0275text(5, "Image");
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(6, "th");
    \u0275\u0275text(7, "Produit");
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(8, "th");
    \u0275\u0275text(9, "R\xE9f\xE9rence");
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(10, "th");
    \u0275\u0275text(11, "Prix");
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(12, "th");
    \u0275\u0275text(13, "Stock");
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(14, "th");
    \u0275\u0275text(15, "Fournisseur");
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(16, "th");
    \u0275\u0275text(17, "Cat\xE9gorie");
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(18, "th");
    \u0275\u0275text(19, "Statut");
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(20, "th");
    \u0275\u0275text(21, "Actions");
    \u0275\u0275elementEnd()()();
    \u0275\u0275elementStart(22, "tbody");
    \u0275\u0275template(23, ProductManagementComponent_div_47_tr_23_Template, 52, 30, "tr", 29);
    \u0275\u0275elementEnd()();
    \u0275\u0275template(24, ProductManagementComponent_div_47_div_24_Template, 3, 0, "div", 30);
    \u0275\u0275elementEnd();
  }
  if (rf & 2) {
    const ctx_r1 = \u0275\u0275nextContext();
    \u0275\u0275advance(23);
    \u0275\u0275property("ngForOf", ctx_r1.produits());
    \u0275\u0275advance();
    \u0275\u0275property("ngIf", ctx_r1.produits().length === 0);
  }
}
function ProductManagementComponent_div_48_button_8_Template(rf, ctx) {
  if (rf & 1) {
    const _r8 = \u0275\u0275getCurrentView();
    \u0275\u0275elementStart(0, "button", 87);
    \u0275\u0275listener("click", function ProductManagementComponent_div_48_button_8_Template_button_click_0_listener() {
      const page_r9 = \u0275\u0275restoreView(_r8).$implicit;
      const ctx_r1 = \u0275\u0275nextContext(2);
      return \u0275\u0275resetView(ctx_r1.onPageChange(page_r9));
    });
    \u0275\u0275text(1);
    \u0275\u0275elementEnd();
  }
  if (rf & 2) {
    const page_r9 = ctx.$implicit;
    const ctx_r1 = \u0275\u0275nextContext(2);
    \u0275\u0275property("ngClass", \u0275\u0275pureFunction1(2, _c1, ctx_r1.currentPage() === page_r9));
    \u0275\u0275advance();
    \u0275\u0275textInterpolate1(" ", page_r9, " ");
  }
}
function ProductManagementComponent_div_48_Template(rf, ctx) {
  if (rf & 1) {
    const _r7 = \u0275\u0275getCurrentView();
    \u0275\u0275elementStart(0, "div", 81)(1, "div", 82)(2, "p");
    \u0275\u0275text(3);
    \u0275\u0275elementEnd()();
    \u0275\u0275elementStart(4, "div", 83)(5, "button", 84);
    \u0275\u0275listener("click", function ProductManagementComponent_div_48_Template_button_click_5_listener() {
      \u0275\u0275restoreView(_r7);
      const ctx_r1 = \u0275\u0275nextContext();
      return \u0275\u0275resetView(ctx_r1.onPageChange(ctx_r1.currentPage() - 1));
    });
    \u0275\u0275text(6, " \u2190 Pr\xE9c\xE9dent ");
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(7, "span", 85);
    \u0275\u0275template(8, ProductManagementComponent_div_48_button_8_Template, 2, 4, "button", 86);
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(9, "button", 84);
    \u0275\u0275listener("click", function ProductManagementComponent_div_48_Template_button_click_9_listener() {
      \u0275\u0275restoreView(_r7);
      const ctx_r1 = \u0275\u0275nextContext();
      return \u0275\u0275resetView(ctx_r1.onPageChange(ctx_r1.currentPage() + 1));
    });
    \u0275\u0275text(10, " Suivant \u2192 ");
    \u0275\u0275elementEnd()()();
  }
  if (rf & 2) {
    const ctx_r1 = \u0275\u0275nextContext();
    \u0275\u0275advance(3);
    \u0275\u0275textInterpolate3("Page ", ctx_r1.currentPage(), " sur ", ctx_r1.totalPages(), " (", ctx_r1.totalItems(), " produits au total)");
    \u0275\u0275advance(2);
    \u0275\u0275property("disabled", ctx_r1.currentPage() === 1);
    \u0275\u0275advance(3);
    \u0275\u0275property("ngForOf", ctx_r1.getPageNumbers());
    \u0275\u0275advance();
    \u0275\u0275property("disabled", ctx_r1.currentPage() === ctx_r1.totalPages());
  }
}
function ProductManagementComponent_div_49_Template(rf, ctx) {
  if (rf & 1) {
    const _r10 = \u0275\u0275getCurrentView();
    \u0275\u0275elementStart(0, "div", 88);
    \u0275\u0275listener("click", function ProductManagementComponent_div_49_Template_div_click_0_listener() {
      \u0275\u0275restoreView(_r10);
      const ctx_r1 = \u0275\u0275nextContext();
      return \u0275\u0275resetView(ctx_r1.closeModerationModal());
    });
    \u0275\u0275elementStart(1, "div", 89);
    \u0275\u0275listener("click", function ProductManagementComponent_div_49_Template_div_click_1_listener($event) {
      \u0275\u0275restoreView(_r10);
      return \u0275\u0275resetView($event.stopPropagation());
    });
    \u0275\u0275elementStart(2, "div", 90)(3, "h3");
    \u0275\u0275text(4, "\u{1F50D} Mod\xE9ration du contenu");
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(5, "button", 91);
    \u0275\u0275listener("click", function ProductManagementComponent_div_49_Template_button_click_5_listener() {
      \u0275\u0275restoreView(_r10);
      const ctx_r1 = \u0275\u0275nextContext();
      return \u0275\u0275resetView(ctx_r1.closeModerationModal());
    });
    \u0275\u0275text(6, "\xD7");
    \u0275\u0275elementEnd()();
    \u0275\u0275elementStart(7, "div", 92)(8, "div", 93)(9, "label");
    \u0275\u0275text(10, "Nouveau nom :");
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(11, "input", 94);
    \u0275\u0275twoWayListener("ngModelChange", function ProductManagementComponent_div_49_Template_input_ngModelChange_11_listener($event) {
      \u0275\u0275restoreView(_r10);
      const ctx_r1 = \u0275\u0275nextContext();
      \u0275\u0275twoWayBindingSet(ctx_r1.moderationData().nouveauNom, $event) || (ctx_r1.moderationData().nouveauNom = $event);
      return \u0275\u0275resetView($event);
    });
    \u0275\u0275elementEnd()();
    \u0275\u0275elementStart(12, "div", 93)(13, "label");
    \u0275\u0275text(14, "Nouvelle description :");
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(15, "textarea", 95);
    \u0275\u0275twoWayListener("ngModelChange", function ProductManagementComponent_div_49_Template_textarea_ngModelChange_15_listener($event) {
      \u0275\u0275restoreView(_r10);
      const ctx_r1 = \u0275\u0275nextContext();
      \u0275\u0275twoWayBindingSet(ctx_r1.moderationData().nouvelleDescription, $event) || (ctx_r1.moderationData().nouvelleDescription = $event);
      return \u0275\u0275resetView($event);
    });
    \u0275\u0275elementEnd()();
    \u0275\u0275elementStart(16, "div", 93)(17, "label");
    \u0275\u0275text(18, "Raison de la mod\xE9ration :");
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(19, "textarea", 96);
    \u0275\u0275twoWayListener("ngModelChange", function ProductManagementComponent_div_49_Template_textarea_ngModelChange_19_listener($event) {
      \u0275\u0275restoreView(_r10);
      const ctx_r1 = \u0275\u0275nextContext();
      \u0275\u0275twoWayBindingSet(ctx_r1.moderationData().raison, $event) || (ctx_r1.moderationData().raison = $event);
      return \u0275\u0275resetView($event);
    });
    \u0275\u0275elementEnd()()();
    \u0275\u0275elementStart(20, "div", 97)(21, "button", 98);
    \u0275\u0275listener("click", function ProductManagementComponent_div_49_Template_button_click_21_listener() {
      \u0275\u0275restoreView(_r10);
      const ctx_r1 = \u0275\u0275nextContext();
      return \u0275\u0275resetView(ctx_r1.closeModerationModal());
    });
    \u0275\u0275text(22, "Annuler");
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(23, "button", 99);
    \u0275\u0275listener("click", function ProductManagementComponent_div_49_Template_button_click_23_listener() {
      \u0275\u0275restoreView(_r10);
      const ctx_r1 = \u0275\u0275nextContext();
      return \u0275\u0275resetView(ctx_r1.submitModeration());
    });
    \u0275\u0275text(24, "Appliquer");
    \u0275\u0275elementEnd()()()();
  }
  if (rf & 2) {
    const ctx_r1 = \u0275\u0275nextContext();
    \u0275\u0275advance(11);
    \u0275\u0275twoWayProperty("ngModel", ctx_r1.moderationData().nouveauNom);
    \u0275\u0275advance(4);
    \u0275\u0275twoWayProperty("ngModel", ctx_r1.moderationData().nouvelleDescription);
    \u0275\u0275advance(4);
    \u0275\u0275twoWayProperty("ngModel", ctx_r1.moderationData().raison);
  }
}
function ProductManagementComponent_div_50_p_17_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "p", 103);
    \u0275\u0275text(1, " \u26A0\uFE0F Ce produit est actuellement en stock critique ");
    \u0275\u0275elementEnd();
  }
}
function ProductManagementComponent_div_50_Template(rf, ctx) {
  if (rf & 1) {
    const _r11 = \u0275\u0275getCurrentView();
    \u0275\u0275elementStart(0, "div", 88);
    \u0275\u0275listener("click", function ProductManagementComponent_div_50_Template_div_click_0_listener() {
      \u0275\u0275restoreView(_r11);
      const ctx_r1 = \u0275\u0275nextContext();
      return \u0275\u0275resetView(ctx_r1.closeStockModal());
    });
    \u0275\u0275elementStart(1, "div", 89);
    \u0275\u0275listener("click", function ProductManagementComponent_div_50_Template_div_click_1_listener($event) {
      \u0275\u0275restoreView(_r11);
      return \u0275\u0275resetView($event.stopPropagation());
    });
    \u0275\u0275elementStart(2, "div", 90)(3, "h3");
    \u0275\u0275text(4, "\u{1F4E6} Gestion du stock");
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(5, "button", 91);
    \u0275\u0275listener("click", function ProductManagementComponent_div_50_Template_button_click_5_listener() {
      \u0275\u0275restoreView(_r11);
      const ctx_r1 = \u0275\u0275nextContext();
      return \u0275\u0275resetView(ctx_r1.closeStockModal());
    });
    \u0275\u0275text(6, "\xD7");
    \u0275\u0275elementEnd()();
    \u0275\u0275elementStart(7, "div", 92)(8, "div", 93)(9, "label");
    \u0275\u0275text(10, "Nouveau stock :");
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(11, "input", 100);
    \u0275\u0275twoWayListener("ngModelChange", function ProductManagementComponent_div_50_Template_input_ngModelChange_11_listener($event) {
      \u0275\u0275restoreView(_r11);
      const ctx_r1 = \u0275\u0275nextContext();
      \u0275\u0275twoWayBindingSet(ctx_r1.nouveauStock, $event) || (ctx_r1.nouveauStock = $event);
      return \u0275\u0275resetView($event);
    });
    \u0275\u0275elementEnd()();
    \u0275\u0275elementStart(12, "div", 101)(13, "p")(14, "strong");
    \u0275\u0275text(15, "Stock actuel :");
    \u0275\u0275elementEnd();
    \u0275\u0275text(16);
    \u0275\u0275elementEnd();
    \u0275\u0275template(17, ProductManagementComponent_div_50_p_17_Template, 2, 0, "p", 102);
    \u0275\u0275elementEnd()();
    \u0275\u0275elementStart(18, "div", 97)(19, "button", 98);
    \u0275\u0275listener("click", function ProductManagementComponent_div_50_Template_button_click_19_listener() {
      \u0275\u0275restoreView(_r11);
      const ctx_r1 = \u0275\u0275nextContext();
      return \u0275\u0275resetView(ctx_r1.closeStockModal());
    });
    \u0275\u0275text(20, "Annuler");
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(21, "button", 99);
    \u0275\u0275listener("click", function ProductManagementComponent_div_50_Template_button_click_21_listener() {
      \u0275\u0275restoreView(_r11);
      const ctx_r1 = \u0275\u0275nextContext();
      return \u0275\u0275resetView(ctx_r1.submitStockUpdate());
    });
    \u0275\u0275text(22, "Mettre \xE0 jour");
    \u0275\u0275elementEnd()()()();
  }
  if (rf & 2) {
    let tmp_2_0;
    let tmp_3_0;
    const ctx_r1 = \u0275\u0275nextContext();
    \u0275\u0275advance(11);
    \u0275\u0275twoWayProperty("ngModel", ctx_r1.nouveauStock);
    \u0275\u0275advance(5);
    \u0275\u0275textInterpolate1(" ", (tmp_2_0 = ctx_r1.selectedProduit()) == null ? null : tmp_2_0.stock, "");
    \u0275\u0275advance();
    \u0275\u0275property("ngIf", (tmp_3_0 = ctx_r1.selectedProduit()) == null ? null : tmp_3_0.estStockCritique);
  }
}
var ProductManagementComponent = class _ProductManagementComponent {
  adminService;
  imageUrlService;
  // Signals pour la gestion d'état
  produits = signal([]);
  isLoading = signal(false);
  error = signal("");
  // Pagination
  currentPage = signal(1);
  pageSize = signal(10);
  totalItems = signal(0);
  totalPages = computed(() => Math.ceil(this.totalItems() / this.pageSize()));
  // Filtres
  searchTerm = signal("");
  selectedStatut = signal("");
  selectedFournisseur = signal("");
  showStockCritique = signal(false);
  // Options pour les filtres
  statutOptions = [
    { value: "", label: "Tous les statuts" },
    { value: "valide", label: "Valid\xE9s" },
    { value: "attente", label: "En attente" }
  ];
  // Modals et actions
  showModerationModal = signal(false);
  showStockModal = signal(false);
  selectedProduit = signal(null);
  moderationData = signal({
    nouveauNom: "",
    nouvelleDescription: "",
    raison: ""
  });
  nouveauStock = signal(0);
  constructor(adminService, imageUrlService) {
    this.adminService = adminService;
    this.imageUrlService = imageUrlService;
  }
  ngOnInit() {
    this.loadProduits();
  }
  loadProduits() {
    this.isLoading.set(true);
    this.error.set("");
    const params = {
      page: this.currentPage(),
      pageSize: this.pageSize(),
      search: this.searchTerm() || void 0,
      statut: this.selectedStatut() || void 0,
      fournisseur: this.selectedFournisseur() || void 0,
      stockCritique: this.showStockCritique() || void 0
    };
    this.adminService.getProduits(params).subscribe({
      next: (response) => {
        console.log("\u2705 Produits re\xE7us:", response);
        this.produits.set(response.produits || []);
        this.totalItems.set(response.totalCount || 0);
        this.isLoading.set(false);
      },
      error: (error) => {
        console.error("\u274C Erreur lors du chargement des produits:", error);
        this.error.set(`Erreur lors du chargement des produits: ${error.status || "Erreur r\xE9seau"}`);
        this.isLoading.set(false);
      }
    });
  }
  // Méthodes de filtrage
  onSearchChange() {
    this.currentPage.set(1);
    this.loadProduits();
  }
  onStatutChange() {
    this.currentPage.set(1);
    this.loadProduits();
  }
  onFournisseurChange() {
    this.currentPage.set(1);
    this.loadProduits();
  }
  onStockCritiqueToggle() {
    this.currentPage.set(1);
    this.loadProduits();
  }
  // Méthodes de pagination
  onPageChange(page) {
    if (page >= 1 && page <= this.totalPages()) {
      this.currentPage.set(page);
      this.loadProduits();
    }
  }
  // Actions sur les produits
  validerProduit(produit) {
    if (confirm(`\xCAtes-vous s\xFBr de vouloir valider le produit "${produit.nom}" ?`)) {
      this.adminService.validerProduit(produit.id).subscribe({
        next: (success) => {
          if (success) {
            console.log("\u2705 Produit valid\xE9 avec succ\xE8s");
            this.loadProduits();
          }
        },
        error: (error) => {
          console.error("\u274C Erreur validation produit:", error);
          this.error.set("Erreur lors de la validation du produit");
        }
      });
    }
  }
  refuserProduit(produit) {
    const raison = prompt(`Raison du refus pour "${produit.nom}" :`);
    if (raison !== null) {
      this.adminService.refuserProduit(produit.id, raison).subscribe({
        next: (success) => {
          if (success) {
            console.log("\u2705 Produit refus\xE9 avec succ\xE8s");
            this.loadProduits();
          }
        },
        error: (error) => {
          console.error("\u274C Erreur refus produit:", error);
          this.error.set("Erreur lors du refus du produit");
        }
      });
    }
  }
  toggleMiseEnAvant(produit) {
    const action = produit.estEnAvant ? "retirer la mise en avant" : "mettre en avant";
    if (confirm(`\xCAtes-vous s\xFBr de vouloir ${action} le produit "${produit.nom}" ?`)) {
      if (produit.estEnAvant) {
        this.adminService.retirerMiseEnAvantProduit(produit.id).subscribe({
          next: (success) => {
            if (success) {
              console.log("\u2705 Mise en avant retir\xE9e avec succ\xE8s");
              this.loadProduits();
            }
          },
          error: (error) => {
            console.error("\u274C Erreur retrait mise en avant:", error);
            this.error.set("Erreur lors du retrait de la mise en avant");
          }
        });
      } else {
        this.adminService.mettreEnAvantProduit(produit.id).subscribe({
          next: (response) => {
            console.log("\u2705 Produit mis en avant avec succ\xE8s");
            this.loadProduits();
          },
          error: (error) => {
            console.error("\u274C Erreur mise en avant:", error);
            this.error.set("Erreur lors de la mise en avant");
          }
        });
      }
    }
  }
  supprimerProduit(produit) {
    if (confirm(`\xCAtes-vous s\xFBr de vouloir supprimer d\xE9finitivement le produit "${produit.nom}" ?`)) {
      this.adminService.supprimerProduit(produit.id).subscribe({
        next: (success) => {
          if (success) {
            console.log("\u2705 Produit supprim\xE9 avec succ\xE8s");
            this.loadProduits();
          }
        },
        error: (error) => {
          console.error("\u274C Erreur suppression produit:", error);
          this.error.set("Erreur lors de la suppression du produit");
        }
      });
    }
  }
  // Modals
  openModerationModal(produit) {
    this.selectedProduit.set(produit);
    this.moderationData.set({
      nouveauNom: produit.nom,
      nouvelleDescription: "",
      // À récupérer si nécessaire
      raison: ""
    });
    this.showModerationModal.set(true);
  }
  closeModerationModal() {
    this.showModerationModal.set(false);
    this.selectedProduit.set(null);
  }
  submitModeration() {
    const produit = this.selectedProduit();
    const data = this.moderationData();
    if (produit) {
      this.adminService.modererContenuProduit(produit.id, {
        nouveauNom: data.nouveauNom !== produit.nom ? data.nouveauNom : void 0,
        nouvelleDescription: data.nouvelleDescription || void 0,
        raison: data.raison || void 0
      }).subscribe({
        next: (success) => {
          if (success) {
            console.log("\u2705 Contenu mod\xE9r\xE9 avec succ\xE8s");
            this.closeModerationModal();
            this.loadProduits();
          }
        },
        error: (error) => {
          console.error("\u274C Erreur mod\xE9ration:", error);
          this.error.set("Erreur lors de la mod\xE9ration du contenu");
        }
      });
    }
  }
  openStockModal(produit) {
    this.selectedProduit.set(produit);
    this.nouveauStock.set(produit.stock);
    this.showStockModal.set(true);
  }
  closeStockModal() {
    this.showStockModal.set(false);
    this.selectedProduit.set(null);
  }
  submitStockUpdate() {
    const produit = this.selectedProduit();
    if (produit) {
      this.adminService.updateStockProduit(produit.id, this.nouveauStock()).subscribe({
        next: (success) => {
          if (success) {
            console.log("\u2705 Stock mis \xE0 jour avec succ\xE8s");
            this.closeStockModal();
            this.loadProduits();
          }
        },
        error: (error) => {
          console.error("\u274C Erreur mise \xE0 jour stock:", error);
          this.error.set("Erreur lors de la mise \xE0 jour du stock");
        }
      });
    }
  }
  // Utilitaires
  getStatusClass(produit) {
    if (produit.estStockCritique)
      return "status-critique";
    if (produit.estValide)
      return "status-valide";
    return "status-attente";
  }
  formatPrice(price) {
    return new Intl.NumberFormat("fr-FR", {
      style: "currency",
      currency: "EUR"
    }).format(price);
  }
  formatDate(dateString) {
    return new Date(dateString).toLocaleDateString("fr-FR");
  }
  onImageError(event) {
    console.log("\u274C Erreur de chargement d'image:", event.target.src);
    event.target.src = this.imageUrlService.getPlaceholderUrl();
  }
  // Méthodes pour les statistiques
  getProduitsStockCritique() {
    return this.produits().filter((p) => p.estStockCritique).length;
  }
  getProduitsEnAvant() {
    return this.produits().filter((p) => p.estEnAvant).length;
  }
  getProduitsEnAttente() {
    return this.produits().filter((p) => !p.estValide).length;
  }
  // Méthode pour la pagination
  getPageNumbers() {
    const maxPages = Math.min(5, this.totalPages());
    const pages = [];
    let startPage = Math.max(1, this.currentPage() - 2);
    let endPage = Math.min(this.totalPages(), startPage + maxPages - 1);
    if (endPage - startPage + 1 < maxPages) {
      startPage = Math.max(1, endPage - maxPages + 1);
    }
    for (let i = startPage; i <= endPage; i++) {
      pages.push(i);
    }
    return pages;
  }
  static \u0275fac = function ProductManagementComponent_Factory(__ngFactoryType__) {
    return new (__ngFactoryType__ || _ProductManagementComponent)(\u0275\u0275directiveInject(AdminService), \u0275\u0275directiveInject(ImageUrlService));
  };
  static \u0275cmp = /* @__PURE__ */ \u0275\u0275defineComponent({ type: _ProductManagementComponent, selectors: [["app-product-management"]], decls: 51, vars: 15, consts: [[1, "product-management-container"], [1, "header"], [1, "subtitle"], [1, "filters-section"], [1, "search-bar"], ["type", "text", "placeholder", "\u{1F50D} Rechercher par nom, r\xE9f\xE9rence...", 1, "search-input", 3, "ngModelChange", "input", "ngModel"], [1, "filters-row"], [1, "filter-group"], [1, "filter-select", 3, "ngModelChange", "change", "ngModel"], [3, "value", 4, "ngFor", "ngForOf"], ["type", "text", "placeholder", "Nom du fournisseur", 1, "filter-input", 3, "ngModelChange", "input", "ngModel"], [1, "checkbox-label"], ["type", "checkbox", 1, "filter-checkbox", 3, "ngModelChange", "change", "ngModel"], [1, "checkmark"], [1, "stats-row"], [1, "stat-card"], [1, "stat-number"], [1, "stat-label"], ["class", "error-message", 4, "ngIf"], ["class", "loading-spinner", 4, "ngIf"], ["class", "table-container", 4, "ngIf"], ["class", "pagination-container", 4, "ngIf"], ["class", "modal-overlay", 3, "click", 4, "ngIf"], [3, "value"], [1, "error-message"], [1, "loading-spinner"], [1, "spinner"], [1, "table-container"], [1, "products-table"], ["class", "product-row", 3, "ngClass", 4, "ngFor", "ngForOf"], ["class", "no-data", 4, "ngIf"], [1, "product-row", 3, "ngClass"], [1, "image-cell"], [1, "product-image", 3, "error", "src", "alt"], [1, "product-info"], [1, "product-name"], [1, "product-meta"], [1, "date"], ["class", "featured-badge", 4, "ngIf"], [1, "reference-cell"], [1, "ref-original"], ["class", "ref-fournisseur", 4, "ngIf"], [1, "price-cell"], [1, "price-container"], [1, "price-initial"], [1, "price-label"], [1, "price-value", "initial"], ["class", "price-outlet", 4, "ngIf"], ["class", "price-final", 4, "ngIf"], ["class", "discount-badge", 4, "ngIf"], [1, "stock-cell"], [1, "stock-container"], [1, "stock-value", 3, "ngClass"], ["class", "stock-alert", 4, "ngIf"], ["title", "Modifier le stock", 1, "btn-stock", 3, "click"], [1, "supplier-cell"], [1, "category-cell"], [1, "category"], [1, "subcategory"], [1, "status-cell"], [1, "status-badge", 3, "ngClass"], ["class", "stock-warning", 4, "ngIf"], [1, "actions-cell"], [1, "action-buttons"], ["class", "btn-action btn-validate", "title", "Valider le produit", 3, "click", 4, "ngIf"], ["class", "btn-action btn-reject", "title", "Refuser le produit", 3, "click", 4, "ngIf"], [1, "btn-action", 3, "click", "ngClass", "title"], ["title", "Mod\xE9rer le contenu", 1, "btn-action", "btn-moderate", 3, "click"], ["title", "Supprimer le produit", 1, "btn-action", "btn-delete", 3, "click"], [1, "featured-badge"], [1, "ref-fournisseur"], [1, "price-outlet"], [1, "price-value", "outlet"], [1, "price-final"], [1, "price-value", "final"], [1, "discount-badge"], [1, "stock-alert"], [1, "stock-warning"], ["title", "Valider le produit", 1, "btn-action", "btn-validate", 3, "click"], ["title", "Refuser le produit", 1, "btn-action", "btn-reject", 3, "click"], [1, "no-data"], [1, "pagination-container"], [1, "pagination-info"], [1, "pagination-controls"], [1, "btn-page", 3, "click", "disabled"], [1, "page-numbers"], ["class", "btn-page-number", 3, "ngClass", "click", 4, "ngFor", "ngForOf"], [1, "btn-page-number", 3, "click", "ngClass"], [1, "modal-overlay", 3, "click"], [1, "modal-content", 3, "click"], [1, "modal-header"], [1, "btn-close", 3, "click"], [1, "modal-body"], [1, "form-group"], ["type", "text", 1, "form-input", 3, "ngModelChange", "ngModel"], ["rows", "4", 1, "form-textarea", 3, "ngModelChange", "ngModel"], ["rows", "3", 1, "form-textarea", 3, "ngModelChange", "ngModel"], [1, "modal-footer"], [1, "btn-secondary", 3, "click"], [1, "btn-primary", 3, "click"], ["type", "number", "min", "0", 1, "form-input", 3, "ngModelChange", "ngModel"], [1, "stock-info"], ["class", "warning", 4, "ngIf"], [1, "warning"]], template: function ProductManagementComponent_Template(rf, ctx) {
    if (rf & 1) {
      \u0275\u0275elementStart(0, "div", 0)(1, "div", 1)(2, "h1");
      \u0275\u0275text(3, "\u{1F6CD}\uFE0F Gestion des Produits");
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(4, "p", 2);
      \u0275\u0275text(5, "Validez, mod\xE9rez et g\xE9rez les produits de la plateforme");
      \u0275\u0275elementEnd()();
      \u0275\u0275elementStart(6, "div", 3)(7, "div", 4)(8, "input", 5);
      \u0275\u0275twoWayListener("ngModelChange", function ProductManagementComponent_Template_input_ngModelChange_8_listener($event) {
        \u0275\u0275twoWayBindingSet(ctx.searchTerm, $event) || (ctx.searchTerm = $event);
        return $event;
      });
      \u0275\u0275listener("input", function ProductManagementComponent_Template_input_input_8_listener() {
        return ctx.onSearchChange();
      });
      \u0275\u0275elementEnd()();
      \u0275\u0275elementStart(9, "div", 6)(10, "div", 7)(11, "label");
      \u0275\u0275text(12, "Statut :");
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(13, "select", 8);
      \u0275\u0275twoWayListener("ngModelChange", function ProductManagementComponent_Template_select_ngModelChange_13_listener($event) {
        \u0275\u0275twoWayBindingSet(ctx.selectedStatut, $event) || (ctx.selectedStatut = $event);
        return $event;
      });
      \u0275\u0275listener("change", function ProductManagementComponent_Template_select_change_13_listener() {
        return ctx.onStatutChange();
      });
      \u0275\u0275template(14, ProductManagementComponent_option_14_Template, 2, 2, "option", 9);
      \u0275\u0275elementEnd()();
      \u0275\u0275elementStart(15, "div", 7)(16, "label");
      \u0275\u0275text(17, "Fournisseur :");
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(18, "input", 10);
      \u0275\u0275twoWayListener("ngModelChange", function ProductManagementComponent_Template_input_ngModelChange_18_listener($event) {
        \u0275\u0275twoWayBindingSet(ctx.selectedFournisseur, $event) || (ctx.selectedFournisseur = $event);
        return $event;
      });
      \u0275\u0275listener("input", function ProductManagementComponent_Template_input_input_18_listener() {
        return ctx.onFournisseurChange();
      });
      \u0275\u0275elementEnd()();
      \u0275\u0275elementStart(19, "div", 7)(20, "label", 11)(21, "input", 12);
      \u0275\u0275twoWayListener("ngModelChange", function ProductManagementComponent_Template_input_ngModelChange_21_listener($event) {
        \u0275\u0275twoWayBindingSet(ctx.showStockCritique, $event) || (ctx.showStockCritique = $event);
        return $event;
      });
      \u0275\u0275listener("change", function ProductManagementComponent_Template_input_change_21_listener() {
        return ctx.onStockCritiqueToggle();
      });
      \u0275\u0275elementEnd();
      \u0275\u0275element(22, "span", 13);
      \u0275\u0275text(23, " Stock critique uniquement ");
      \u0275\u0275elementEnd()()()();
      \u0275\u0275elementStart(24, "div", 14)(25, "div", 15)(26, "span", 16);
      \u0275\u0275text(27);
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(28, "span", 17);
      \u0275\u0275text(29, "Produits total");
      \u0275\u0275elementEnd()();
      \u0275\u0275elementStart(30, "div", 15)(31, "span", 16);
      \u0275\u0275text(32);
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(33, "span", 17);
      \u0275\u0275text(34, "Stock critique");
      \u0275\u0275elementEnd()();
      \u0275\u0275elementStart(35, "div", 15)(36, "span", 16);
      \u0275\u0275text(37);
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(38, "span", 17);
      \u0275\u0275text(39, "Mis en avant");
      \u0275\u0275elementEnd()();
      \u0275\u0275elementStart(40, "div", 15)(41, "span", 16);
      \u0275\u0275text(42);
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(43, "span", 17);
      \u0275\u0275text(44, "En attente");
      \u0275\u0275elementEnd()()();
      \u0275\u0275template(45, ProductManagementComponent_div_45_Template, 2, 1, "div", 18)(46, ProductManagementComponent_div_46_Template, 4, 0, "div", 19)(47, ProductManagementComponent_div_47_Template, 25, 2, "div", 20)(48, ProductManagementComponent_div_48_Template, 11, 6, "div", 21);
      \u0275\u0275elementEnd();
      \u0275\u0275template(49, ProductManagementComponent_div_49_Template, 25, 3, "div", 22)(50, ProductManagementComponent_div_50_Template, 23, 3, "div", 22);
    }
    if (rf & 2) {
      \u0275\u0275advance(8);
      \u0275\u0275twoWayProperty("ngModel", ctx.searchTerm);
      \u0275\u0275advance(5);
      \u0275\u0275twoWayProperty("ngModel", ctx.selectedStatut);
      \u0275\u0275advance();
      \u0275\u0275property("ngForOf", ctx.statutOptions);
      \u0275\u0275advance(4);
      \u0275\u0275twoWayProperty("ngModel", ctx.selectedFournisseur);
      \u0275\u0275advance(3);
      \u0275\u0275twoWayProperty("ngModel", ctx.showStockCritique);
      \u0275\u0275advance(6);
      \u0275\u0275textInterpolate(ctx.totalItems());
      \u0275\u0275advance(5);
      \u0275\u0275textInterpolate(ctx.getProduitsStockCritique());
      \u0275\u0275advance(5);
      \u0275\u0275textInterpolate(ctx.getProduitsEnAvant());
      \u0275\u0275advance(5);
      \u0275\u0275textInterpolate(ctx.getProduitsEnAttente());
      \u0275\u0275advance(3);
      \u0275\u0275property("ngIf", ctx.error());
      \u0275\u0275advance();
      \u0275\u0275property("ngIf", ctx.isLoading());
      \u0275\u0275advance();
      \u0275\u0275property("ngIf", !ctx.isLoading());
      \u0275\u0275advance();
      \u0275\u0275property("ngIf", ctx.totalPages() > 1);
      \u0275\u0275advance();
      \u0275\u0275property("ngIf", ctx.showModerationModal());
      \u0275\u0275advance();
      \u0275\u0275property("ngIf", ctx.showStockModal());
    }
  }, dependencies: [CommonModule, NgClass, NgForOf, NgIf, FormsModule, NgSelectOption, \u0275NgSelectMultipleOption, DefaultValueAccessor, NumberValueAccessor, CheckboxControlValueAccessor, SelectControlValueAccessor, NgControlStatus, MinValidator, NgModel], styles: ["\n\n.product-management-container[_ngcontent-%COMP%] {\n  padding: 2rem;\n  max-width: 1400px;\n  margin: 0 auto;\n}\n.product-management-container[_ngcontent-%COMP%]   .header[_ngcontent-%COMP%] {\n  text-align: center;\n  margin-bottom: 2rem;\n}\n.product-management-container[_ngcontent-%COMP%]   .header[_ngcontent-%COMP%]   h1[_ngcontent-%COMP%] {\n  color: #2563eb;\n  font-size: 2.5rem;\n  margin-bottom: 0.5rem;\n}\n.product-management-container[_ngcontent-%COMP%]   .header[_ngcontent-%COMP%]   .subtitle[_ngcontent-%COMP%] {\n  color: #64748b;\n  font-size: 1.1rem;\n}\n.product-management-container[_ngcontent-%COMP%]   .filters-section[_ngcontent-%COMP%] {\n  background: white;\n  padding: 1.5rem;\n  border-radius: 12px;\n  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);\n  margin-bottom: 2rem;\n}\n.product-management-container[_ngcontent-%COMP%]   .filters-section[_ngcontent-%COMP%]   .search-bar[_ngcontent-%COMP%] {\n  margin-bottom: 1rem;\n}\n.product-management-container[_ngcontent-%COMP%]   .filters-section[_ngcontent-%COMP%]   .search-bar[_ngcontent-%COMP%]   .search-input[_ngcontent-%COMP%] {\n  width: 100%;\n  padding: 0.75rem 1rem;\n  border: 2px solid #e2e8f0;\n  border-radius: 8px;\n  font-size: 1rem;\n  transition: border-color 0.3s ease;\n}\n.product-management-container[_ngcontent-%COMP%]   .filters-section[_ngcontent-%COMP%]   .search-bar[_ngcontent-%COMP%]   .search-input[_ngcontent-%COMP%]:focus {\n  outline: none;\n  border-color: #2563eb;\n}\n.product-management-container[_ngcontent-%COMP%]   .filters-section[_ngcontent-%COMP%]   .filters-row[_ngcontent-%COMP%] {\n  display: flex;\n  gap: 1.5rem;\n  flex-wrap: wrap;\n  align-items: end;\n}\n.product-management-container[_ngcontent-%COMP%]   .filters-section[_ngcontent-%COMP%]   .filters-row[_ngcontent-%COMP%]   .filter-group[_ngcontent-%COMP%] {\n  display: flex;\n  flex-direction: column;\n  gap: 0.5rem;\n}\n.product-management-container[_ngcontent-%COMP%]   .filters-section[_ngcontent-%COMP%]   .filters-row[_ngcontent-%COMP%]   .filter-group[_ngcontent-%COMP%]   label[_ngcontent-%COMP%] {\n  font-weight: 600;\n  color: #1e293b;\n  font-size: 0.9rem;\n}\n.product-management-container[_ngcontent-%COMP%]   .filters-section[_ngcontent-%COMP%]   .filters-row[_ngcontent-%COMP%]   .filter-group[_ngcontent-%COMP%]   .filter-select[_ngcontent-%COMP%], \n.product-management-container[_ngcontent-%COMP%]   .filters-section[_ngcontent-%COMP%]   .filters-row[_ngcontent-%COMP%]   .filter-group[_ngcontent-%COMP%]   .filter-input[_ngcontent-%COMP%] {\n  padding: 0.5rem;\n  border: 1px solid #e2e8f0;\n  border-radius: 6px;\n  font-size: 0.9rem;\n  min-width: 150px;\n}\n.product-management-container[_ngcontent-%COMP%]   .filters-section[_ngcontent-%COMP%]   .filters-row[_ngcontent-%COMP%]   .filter-group[_ngcontent-%COMP%]   .filter-select[_ngcontent-%COMP%]:focus, \n.product-management-container[_ngcontent-%COMP%]   .filters-section[_ngcontent-%COMP%]   .filters-row[_ngcontent-%COMP%]   .filter-group[_ngcontent-%COMP%]   .filter-input[_ngcontent-%COMP%]:focus {\n  outline: none;\n  border-color: #2563eb;\n}\n.product-management-container[_ngcontent-%COMP%]   .filters-section[_ngcontent-%COMP%]   .filters-row[_ngcontent-%COMP%]   .filter-group[_ngcontent-%COMP%]   .checkbox-label[_ngcontent-%COMP%] {\n  display: flex;\n  align-items: center;\n  gap: 0.5rem;\n  cursor: pointer;\n  font-weight: normal;\n}\n.product-management-container[_ngcontent-%COMP%]   .filters-section[_ngcontent-%COMP%]   .filters-row[_ngcontent-%COMP%]   .filter-group[_ngcontent-%COMP%]   .checkbox-label[_ngcontent-%COMP%]   .filter-checkbox[_ngcontent-%COMP%] {\n  width: 18px;\n  height: 18px;\n}\n.product-management-container[_ngcontent-%COMP%]   .stats-row[_ngcontent-%COMP%] {\n  display: grid;\n  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));\n  gap: 1rem;\n  margin-bottom: 2rem;\n}\n.product-management-container[_ngcontent-%COMP%]   .stats-row[_ngcontent-%COMP%]   .stat-card[_ngcontent-%COMP%] {\n  background: white;\n  padding: 1.5rem;\n  border-radius: 12px;\n  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);\n  text-align: center;\n  border-left: 4px solid #2563eb;\n}\n.product-management-container[_ngcontent-%COMP%]   .stats-row[_ngcontent-%COMP%]   .stat-card[_ngcontent-%COMP%]   .stat-number[_ngcontent-%COMP%] {\n  display: block;\n  font-size: 2rem;\n  font-weight: bold;\n  color: #2563eb;\n  margin-bottom: 0.5rem;\n}\n.product-management-container[_ngcontent-%COMP%]   .stats-row[_ngcontent-%COMP%]   .stat-card[_ngcontent-%COMP%]   .stat-label[_ngcontent-%COMP%] {\n  color: #64748b;\n  font-size: 0.9rem;\n  text-transform: uppercase;\n  letter-spacing: 0.5px;\n}\n.product-management-container[_ngcontent-%COMP%]   .error-message[_ngcontent-%COMP%] {\n  background: #fee;\n  color: #c33;\n  padding: 1rem;\n  border-radius: 8px;\n  margin-bottom: 1rem;\n  border-left: 4px solid #c33;\n}\n.product-management-container[_ngcontent-%COMP%]   .loading-spinner[_ngcontent-%COMP%] {\n  text-align: center;\n  padding: 3rem;\n}\n.product-management-container[_ngcontent-%COMP%]   .loading-spinner[_ngcontent-%COMP%]   .spinner[_ngcontent-%COMP%] {\n  width: 40px;\n  height: 40px;\n  border: 4px solid #f3f3f3;\n  border-top: 4px solid #2563eb;\n  border-radius: 50%;\n  animation: _ngcontent-%COMP%_spin 1s linear infinite;\n  margin: 0 auto 1rem;\n}\n.product-management-container[_ngcontent-%COMP%]   .table-container[_ngcontent-%COMP%] {\n  background: white;\n  border-radius: 12px;\n  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);\n  overflow: hidden;\n  margin-bottom: 2rem;\n}\n.product-management-container[_ngcontent-%COMP%]   .table-container[_ngcontent-%COMP%]   .products-table[_ngcontent-%COMP%] {\n  width: 100%;\n  border-collapse: collapse;\n}\n.product-management-container[_ngcontent-%COMP%]   .table-container[_ngcontent-%COMP%]   .products-table[_ngcontent-%COMP%]   thead[_ngcontent-%COMP%] {\n  background: #2563eb;\n  color: white;\n}\n.product-management-container[_ngcontent-%COMP%]   .table-container[_ngcontent-%COMP%]   .products-table[_ngcontent-%COMP%]   thead[_ngcontent-%COMP%]   th[_ngcontent-%COMP%] {\n  padding: 1rem 0.75rem;\n  text-align: left;\n  font-weight: 600;\n  font-size: 0.9rem;\n  text-transform: uppercase;\n  letter-spacing: 0.5px;\n}\n.product-management-container[_ngcontent-%COMP%]   .table-container[_ngcontent-%COMP%]   .products-table[_ngcontent-%COMP%]   tbody[_ngcontent-%COMP%]   .product-row[_ngcontent-%COMP%] {\n  border-bottom: 1px solid #e2e8f0;\n  transition: background-color 0.2s ease;\n}\n.product-management-container[_ngcontent-%COMP%]   .table-container[_ngcontent-%COMP%]   .products-table[_ngcontent-%COMP%]   tbody[_ngcontent-%COMP%]   .product-row[_ngcontent-%COMP%]:hover {\n  background-color: #f8f9fa;\n}\n.product-management-container[_ngcontent-%COMP%]   .table-container[_ngcontent-%COMP%]   .products-table[_ngcontent-%COMP%]   tbody[_ngcontent-%COMP%]   .product-row.status-critique[_ngcontent-%COMP%] {\n  background-color: #fff5f5;\n  border-left: 4px solid #e53e3e;\n}\n.product-management-container[_ngcontent-%COMP%]   .table-container[_ngcontent-%COMP%]   .products-table[_ngcontent-%COMP%]   tbody[_ngcontent-%COMP%]   .product-row.status-attente[_ngcontent-%COMP%] {\n  background-color: #fffbf0;\n  border-left: 4px solid #f6ad55;\n}\n.product-management-container[_ngcontent-%COMP%]   .table-container[_ngcontent-%COMP%]   .products-table[_ngcontent-%COMP%]   tbody[_ngcontent-%COMP%]   .product-row.status-valide[_ngcontent-%COMP%] {\n  background-color: #f0fff4;\n  border-left: 4px solid #38a169;\n}\n.product-management-container[_ngcontent-%COMP%]   .table-container[_ngcontent-%COMP%]   .products-table[_ngcontent-%COMP%]   tbody[_ngcontent-%COMP%]   .product-row[_ngcontent-%COMP%]   td[_ngcontent-%COMP%] {\n  padding: 1rem 0.75rem;\n  vertical-align: top;\n}\n.product-management-container[_ngcontent-%COMP%]   .table-container[_ngcontent-%COMP%]   .products-table[_ngcontent-%COMP%]   tbody[_ngcontent-%COMP%]   .product-row[_ngcontent-%COMP%]   .image-cell[_ngcontent-%COMP%] {\n  width: 80px;\n}\n.product-management-container[_ngcontent-%COMP%]   .table-container[_ngcontent-%COMP%]   .products-table[_ngcontent-%COMP%]   tbody[_ngcontent-%COMP%]   .product-row[_ngcontent-%COMP%]   .image-cell[_ngcontent-%COMP%]   .product-image[_ngcontent-%COMP%] {\n  width: 60px;\n  height: 60px;\n  object-fit: cover;\n  border-radius: 8px;\n  border: 1px solid #e2e8f0;\n}\n.product-management-container[_ngcontent-%COMP%]   .table-container[_ngcontent-%COMP%]   .products-table[_ngcontent-%COMP%]   tbody[_ngcontent-%COMP%]   .product-row[_ngcontent-%COMP%]   .product-info[_ngcontent-%COMP%] {\n  min-width: 200px;\n}\n.product-management-container[_ngcontent-%COMP%]   .table-container[_ngcontent-%COMP%]   .products-table[_ngcontent-%COMP%]   tbody[_ngcontent-%COMP%]   .product-row[_ngcontent-%COMP%]   .product-info[_ngcontent-%COMP%]   .product-name[_ngcontent-%COMP%] {\n  font-weight: 600;\n  color: #1e293b;\n  margin-bottom: 0.25rem;\n  line-height: 1.3;\n}\n.product-management-container[_ngcontent-%COMP%]   .table-container[_ngcontent-%COMP%]   .products-table[_ngcontent-%COMP%]   tbody[_ngcontent-%COMP%]   .product-row[_ngcontent-%COMP%]   .product-info[_ngcontent-%COMP%]   .product-meta[_ngcontent-%COMP%] {\n  display: flex;\n  gap: 0.5rem;\n  align-items: center;\n  flex-wrap: wrap;\n}\n.product-management-container[_ngcontent-%COMP%]   .table-container[_ngcontent-%COMP%]   .products-table[_ngcontent-%COMP%]   tbody[_ngcontent-%COMP%]   .product-row[_ngcontent-%COMP%]   .product-info[_ngcontent-%COMP%]   .product-meta[_ngcontent-%COMP%]   .date[_ngcontent-%COMP%] {\n  font-size: 0.8rem;\n  color: #64748b;\n}\n.product-management-container[_ngcontent-%COMP%]   .table-container[_ngcontent-%COMP%]   .products-table[_ngcontent-%COMP%]   tbody[_ngcontent-%COMP%]   .product-row[_ngcontent-%COMP%]   .product-info[_ngcontent-%COMP%]   .product-meta[_ngcontent-%COMP%]   .featured-badge[_ngcontent-%COMP%] {\n  background: #f59e0b;\n  color: white;\n  padding: 0.2rem 0.5rem;\n  border-radius: 12px;\n  font-size: 0.7rem;\n  font-weight: 600;\n}\n.product-management-container[_ngcontent-%COMP%]   .table-container[_ngcontent-%COMP%]   .products-table[_ngcontent-%COMP%]   tbody[_ngcontent-%COMP%]   .product-row[_ngcontent-%COMP%]   .reference-cell[_ngcontent-%COMP%]   .ref-original[_ngcontent-%COMP%] {\n  font-weight: 600;\n  color: #1e293b;\n}\n.product-management-container[_ngcontent-%COMP%]   .table-container[_ngcontent-%COMP%]   .products-table[_ngcontent-%COMP%]   tbody[_ngcontent-%COMP%]   .product-row[_ngcontent-%COMP%]   .reference-cell[_ngcontent-%COMP%]   .ref-fournisseur[_ngcontent-%COMP%] {\n  font-size: 0.8rem;\n  color: #64748b;\n  margin-top: 0.25rem;\n}\n.product-management-container[_ngcontent-%COMP%]   .table-container[_ngcontent-%COMP%]   .products-table[_ngcontent-%COMP%]   tbody[_ngcontent-%COMP%]   .product-row[_ngcontent-%COMP%]   .price-cell[_ngcontent-%COMP%] {\n  text-align: right;\n}\n.product-management-container[_ngcontent-%COMP%]   .table-container[_ngcontent-%COMP%]   .products-table[_ngcontent-%COMP%]   tbody[_ngcontent-%COMP%]   .product-row[_ngcontent-%COMP%]   .price-cell[_ngcontent-%COMP%]   .price-container[_ngcontent-%COMP%] {\n  display: flex;\n  flex-direction: column;\n  gap: 4px;\n  position: relative;\n}\n.product-management-container[_ngcontent-%COMP%]   .table-container[_ngcontent-%COMP%]   .products-table[_ngcontent-%COMP%]   tbody[_ngcontent-%COMP%]   .product-row[_ngcontent-%COMP%]   .price-cell[_ngcontent-%COMP%]   .price-container[_ngcontent-%COMP%]   .price-initial[_ngcontent-%COMP%]   .price-label[_ngcontent-%COMP%] {\n  font-size: 0.75rem;\n  color: #64748b;\n  margin-right: 4px;\n  font-weight: 500;\n}\n.product-management-container[_ngcontent-%COMP%]   .table-container[_ngcontent-%COMP%]   .products-table[_ngcontent-%COMP%]   tbody[_ngcontent-%COMP%]   .product-row[_ngcontent-%COMP%]   .price-cell[_ngcontent-%COMP%]   .price-container[_ngcontent-%COMP%]   .price-initial[_ngcontent-%COMP%]   .price-value.initial[_ngcontent-%COMP%] {\n  color: #1e293b;\n  font-size: 0.95rem;\n  font-weight: 500;\n}\n.product-management-container[_ngcontent-%COMP%]   .table-container[_ngcontent-%COMP%]   .products-table[_ngcontent-%COMP%]   tbody[_ngcontent-%COMP%]   .product-row[_ngcontent-%COMP%]   .price-cell[_ngcontent-%COMP%]   .price-container[_ngcontent-%COMP%]   .price-initial[_ngcontent-%COMP%]   .price-value.initial.crossed[_ngcontent-%COMP%] {\n  text-decoration: line-through;\n  color: #64748b;\n}\n.product-management-container[_ngcontent-%COMP%]   .table-container[_ngcontent-%COMP%]   .products-table[_ngcontent-%COMP%]   tbody[_ngcontent-%COMP%]   .product-row[_ngcontent-%COMP%]   .price-cell[_ngcontent-%COMP%]   .price-container[_ngcontent-%COMP%]   .price-outlet[_ngcontent-%COMP%]   .price-label[_ngcontent-%COMP%] {\n  font-size: 0.8rem;\n  color: #f59e0b;\n  margin-right: 4px;\n  font-weight: 500;\n}\n.product-management-container[_ngcontent-%COMP%]   .table-container[_ngcontent-%COMP%]   .products-table[_ngcontent-%COMP%]   tbody[_ngcontent-%COMP%]   .product-row[_ngcontent-%COMP%]   .price-cell[_ngcontent-%COMP%]   .price-container[_ngcontent-%COMP%]   .price-outlet[_ngcontent-%COMP%]   .price-value.outlet[_ngcontent-%COMP%] {\n  color: #f59e0b;\n  font-weight: 600;\n  font-size: 1rem;\n}\n.product-management-container[_ngcontent-%COMP%]   .table-container[_ngcontent-%COMP%]   .products-table[_ngcontent-%COMP%]   tbody[_ngcontent-%COMP%]   .product-row[_ngcontent-%COMP%]   .price-cell[_ngcontent-%COMP%]   .price-container[_ngcontent-%COMP%]   .price-final[_ngcontent-%COMP%]   .price-label[_ngcontent-%COMP%] {\n  font-size: 0.8rem;\n  color: #2563eb;\n  margin-right: 4px;\n  font-weight: 500;\n}\n.product-management-container[_ngcontent-%COMP%]   .table-container[_ngcontent-%COMP%]   .products-table[_ngcontent-%COMP%]   tbody[_ngcontent-%COMP%]   .product-row[_ngcontent-%COMP%]   .price-cell[_ngcontent-%COMP%]   .price-container[_ngcontent-%COMP%]   .price-final[_ngcontent-%COMP%]   .price-value.final[_ngcontent-%COMP%] {\n  color: #2563eb;\n  font-weight: 700;\n  font-size: 1.1rem;\n}\n.product-management-container[_ngcontent-%COMP%]   .table-container[_ngcontent-%COMP%]   .products-table[_ngcontent-%COMP%]   tbody[_ngcontent-%COMP%]   .product-row[_ngcontent-%COMP%]   .price-cell[_ngcontent-%COMP%]   .price-container[_ngcontent-%COMP%]   .discount-badge[_ngcontent-%COMP%] {\n  position: absolute;\n  top: -8px;\n  right: -8px;\n  background:\n    linear-gradient(\n      135deg,\n      #f59e0b,\n      #f7931e);\n  color: white;\n  font-size: 0.7rem;\n  padding: 3px 8px;\n  border-radius: 12px;\n  font-weight: 700;\n  box-shadow: 0 2px 4px rgba(255, 107, 53, 0.3);\n}\n.product-management-container[_ngcontent-%COMP%]   .table-container[_ngcontent-%COMP%]   .products-table[_ngcontent-%COMP%]   tbody[_ngcontent-%COMP%]   .product-row[_ngcontent-%COMP%]   .price-cell[_ngcontent-%COMP%]   .price[_ngcontent-%COMP%] {\n  font-weight: 600;\n  color: #2563eb;\n  font-size: 1.1rem;\n}\n.product-management-container[_ngcontent-%COMP%]   .table-container[_ngcontent-%COMP%]   .products-table[_ngcontent-%COMP%]   tbody[_ngcontent-%COMP%]   .product-row[_ngcontent-%COMP%]   .stock-cell[_ngcontent-%COMP%]   .stock-container[_ngcontent-%COMP%] {\n  display: flex;\n  flex-direction: column;\n  align-items: flex-start;\n  gap: 0.25rem;\n}\n.product-management-container[_ngcontent-%COMP%]   .table-container[_ngcontent-%COMP%]   .products-table[_ngcontent-%COMP%]   tbody[_ngcontent-%COMP%]   .product-row[_ngcontent-%COMP%]   .stock-cell[_ngcontent-%COMP%]   .stock-container[_ngcontent-%COMP%]   .stock-value[_ngcontent-%COMP%] {\n  font-weight: 600;\n  padding: 0.25rem 0.5rem;\n  border-radius: 4px;\n  background: #e6fffa;\n  color: #234e52;\n  font-size: 1.1rem;\n}\n.product-management-container[_ngcontent-%COMP%]   .table-container[_ngcontent-%COMP%]   .products-table[_ngcontent-%COMP%]   tbody[_ngcontent-%COMP%]   .product-row[_ngcontent-%COMP%]   .stock-cell[_ngcontent-%COMP%]   .stock-container[_ngcontent-%COMP%]   .stock-value.stock-critique[_ngcontent-%COMP%] {\n  background: #fed7d7;\n  color: #742a2a;\n  border: 2px solid #e53e3e;\n  animation: _ngcontent-%COMP%_pulse-warning 2s infinite;\n}\n.product-management-container[_ngcontent-%COMP%]   .table-container[_ngcontent-%COMP%]   .products-table[_ngcontent-%COMP%]   tbody[_ngcontent-%COMP%]   .product-row[_ngcontent-%COMP%]   .stock-cell[_ngcontent-%COMP%]   .stock-container[_ngcontent-%COMP%]   .stock-alert[_ngcontent-%COMP%] {\n  font-size: 0.75rem;\n  color: #e53e3e;\n  font-weight: 600;\n  background: #fed7d7;\n  padding: 2px 6px;\n  border-radius: 10px;\n  border: 1px solid #e53e3e;\n}\n.product-management-container[_ngcontent-%COMP%]   .table-container[_ngcontent-%COMP%]   .products-table[_ngcontent-%COMP%]   tbody[_ngcontent-%COMP%]   .product-row[_ngcontent-%COMP%]   .stock-cell[_ngcontent-%COMP%]   .stock-container[_ngcontent-%COMP%]   .btn-stock[_ngcontent-%COMP%] {\n  background: none;\n  border: none;\n  cursor: pointer;\n  font-size: 1.2rem;\n  padding: 0.25rem;\n  border-radius: 4px;\n  transition: background-color 0.2s ease;\n  margin-top: 0.25rem;\n}\n.product-management-container[_ngcontent-%COMP%]   .table-container[_ngcontent-%COMP%]   .products-table[_ngcontent-%COMP%]   tbody[_ngcontent-%COMP%]   .product-row[_ngcontent-%COMP%]   .stock-cell[_ngcontent-%COMP%]   .stock-container[_ngcontent-%COMP%]   .btn-stock[_ngcontent-%COMP%]:hover {\n  background: #f0f0f0;\n}\n@keyframes _ngcontent-%COMP%_pulse-warning {\n  0%, 100% {\n    opacity: 1;\n  }\n  50% {\n    opacity: 0.7;\n  }\n}\n.product-management-container[_ngcontent-%COMP%]   .table-container[_ngcontent-%COMP%]   .products-table[_ngcontent-%COMP%]   tbody[_ngcontent-%COMP%]   .product-row[_ngcontent-%COMP%]   .category-cell[_ngcontent-%COMP%]   .category[_ngcontent-%COMP%] {\n  font-weight: 600;\n  color: #1e293b;\n}\n.product-management-container[_ngcontent-%COMP%]   .table-container[_ngcontent-%COMP%]   .products-table[_ngcontent-%COMP%]   tbody[_ngcontent-%COMP%]   .product-row[_ngcontent-%COMP%]   .category-cell[_ngcontent-%COMP%]   .subcategory[_ngcontent-%COMP%] {\n  font-size: 0.8rem;\n  color: #64748b;\n  margin-top: 0.25rem;\n}\n.product-management-container[_ngcontent-%COMP%]   .table-container[_ngcontent-%COMP%]   .products-table[_ngcontent-%COMP%]   tbody[_ngcontent-%COMP%]   .product-row[_ngcontent-%COMP%]   .status-cell[_ngcontent-%COMP%]   .status-badge[_ngcontent-%COMP%] {\n  padding: 0.25rem 0.75rem;\n  border-radius: 12px;\n  font-size: 0.8rem;\n  font-weight: 600;\n  text-transform: uppercase;\n  letter-spacing: 0.5px;\n}\n.product-management-container[_ngcontent-%COMP%]   .table-container[_ngcontent-%COMP%]   .products-table[_ngcontent-%COMP%]   tbody[_ngcontent-%COMP%]   .product-row[_ngcontent-%COMP%]   .status-cell[_ngcontent-%COMP%]   .status-badge.status-valide[_ngcontent-%COMP%] {\n  background: #c6f6d5;\n  color: #22543d;\n}\n.product-management-container[_ngcontent-%COMP%]   .table-container[_ngcontent-%COMP%]   .products-table[_ngcontent-%COMP%]   tbody[_ngcontent-%COMP%]   .product-row[_ngcontent-%COMP%]   .status-cell[_ngcontent-%COMP%]   .status-badge.status-attente[_ngcontent-%COMP%] {\n  background: #feebc8;\n  color: #744210;\n}\n.product-management-container[_ngcontent-%COMP%]   .table-container[_ngcontent-%COMP%]   .products-table[_ngcontent-%COMP%]   tbody[_ngcontent-%COMP%]   .product-row[_ngcontent-%COMP%]   .status-cell[_ngcontent-%COMP%]   .status-badge.status-critique[_ngcontent-%COMP%] {\n  background: #fed7d7;\n  color: #742a2a;\n}\n.product-management-container[_ngcontent-%COMP%]   .table-container[_ngcontent-%COMP%]   .products-table[_ngcontent-%COMP%]   tbody[_ngcontent-%COMP%]   .product-row[_ngcontent-%COMP%]   .status-cell[_ngcontent-%COMP%]   .stock-warning[_ngcontent-%COMP%] {\n  display: block;\n  font-size: 0.7rem;\n  color: #e53e3e;\n  margin-top: 0.25rem;\n}\n.product-management-container[_ngcontent-%COMP%]   .table-container[_ngcontent-%COMP%]   .products-table[_ngcontent-%COMP%]   tbody[_ngcontent-%COMP%]   .product-row[_ngcontent-%COMP%]   .actions-cell[_ngcontent-%COMP%]   .action-buttons[_ngcontent-%COMP%] {\n  display: flex;\n  gap: 0.25rem;\n  flex-wrap: wrap;\n}\n.product-management-container[_ngcontent-%COMP%]   .table-container[_ngcontent-%COMP%]   .products-table[_ngcontent-%COMP%]   tbody[_ngcontent-%COMP%]   .product-row[_ngcontent-%COMP%]   .actions-cell[_ngcontent-%COMP%]   .action-buttons[_ngcontent-%COMP%]   .btn-action[_ngcontent-%COMP%] {\n  background: none;\n  border: 1px solid transparent;\n  padding: 0.5rem;\n  border-radius: 6px;\n  cursor: pointer;\n  font-size: 1rem;\n  transition: all 0.2s ease;\n  min-width: 36px;\n  height: 36px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n}\n.product-management-container[_ngcontent-%COMP%]   .table-container[_ngcontent-%COMP%]   .products-table[_ngcontent-%COMP%]   tbody[_ngcontent-%COMP%]   .product-row[_ngcontent-%COMP%]   .actions-cell[_ngcontent-%COMP%]   .action-buttons[_ngcontent-%COMP%]   .btn-action[_ngcontent-%COMP%]:hover {\n  transform: translateY(-1px);\n}\n.product-management-container[_ngcontent-%COMP%]   .table-container[_ngcontent-%COMP%]   .products-table[_ngcontent-%COMP%]   tbody[_ngcontent-%COMP%]   .product-row[_ngcontent-%COMP%]   .actions-cell[_ngcontent-%COMP%]   .action-buttons[_ngcontent-%COMP%]   .btn-action.btn-validate[_ngcontent-%COMP%] {\n  background: #c6f6d5;\n  color: #22543d;\n}\n.product-management-container[_ngcontent-%COMP%]   .table-container[_ngcontent-%COMP%]   .products-table[_ngcontent-%COMP%]   tbody[_ngcontent-%COMP%]   .product-row[_ngcontent-%COMP%]   .actions-cell[_ngcontent-%COMP%]   .action-buttons[_ngcontent-%COMP%]   .btn-action.btn-validate[_ngcontent-%COMP%]:hover {\n  background: #9ae6b4;\n}\n.product-management-container[_ngcontent-%COMP%]   .table-container[_ngcontent-%COMP%]   .products-table[_ngcontent-%COMP%]   tbody[_ngcontent-%COMP%]   .product-row[_ngcontent-%COMP%]   .actions-cell[_ngcontent-%COMP%]   .action-buttons[_ngcontent-%COMP%]   .btn-action.btn-reject[_ngcontent-%COMP%] {\n  background: #fed7d7;\n  color: #742a2a;\n}\n.product-management-container[_ngcontent-%COMP%]   .table-container[_ngcontent-%COMP%]   .products-table[_ngcontent-%COMP%]   tbody[_ngcontent-%COMP%]   .product-row[_ngcontent-%COMP%]   .actions-cell[_ngcontent-%COMP%]   .action-buttons[_ngcontent-%COMP%]   .btn-action.btn-reject[_ngcontent-%COMP%]:hover {\n  background: #feb2b2;\n}\n.product-management-container[_ngcontent-%COMP%]   .table-container[_ngcontent-%COMP%]   .products-table[_ngcontent-%COMP%]   tbody[_ngcontent-%COMP%]   .product-row[_ngcontent-%COMP%]   .actions-cell[_ngcontent-%COMP%]   .action-buttons[_ngcontent-%COMP%]   .btn-action.btn-feature[_ngcontent-%COMP%] {\n  background: #feebc8;\n  color: #744210;\n}\n.product-management-container[_ngcontent-%COMP%]   .table-container[_ngcontent-%COMP%]   .products-table[_ngcontent-%COMP%]   tbody[_ngcontent-%COMP%]   .product-row[_ngcontent-%COMP%]   .actions-cell[_ngcontent-%COMP%]   .action-buttons[_ngcontent-%COMP%]   .btn-action.btn-feature[_ngcontent-%COMP%]:hover {\n  background: #fbd38d;\n}\n.product-management-container[_ngcontent-%COMP%]   .table-container[_ngcontent-%COMP%]   .products-table[_ngcontent-%COMP%]   tbody[_ngcontent-%COMP%]   .product-row[_ngcontent-%COMP%]   .actions-cell[_ngcontent-%COMP%]   .action-buttons[_ngcontent-%COMP%]   .btn-action.btn-unfeature[_ngcontent-%COMP%] {\n  background: #f59e0b;\n  color: white;\n}\n.product-management-container[_ngcontent-%COMP%]   .table-container[_ngcontent-%COMP%]   .products-table[_ngcontent-%COMP%]   tbody[_ngcontent-%COMP%]   .product-row[_ngcontent-%COMP%]   .actions-cell[_ngcontent-%COMP%]   .action-buttons[_ngcontent-%COMP%]   .btn-action.btn-unfeature[_ngcontent-%COMP%]:hover {\n  background: rgb(196.9291338583, 126.7125984252, 8.0708661417);\n}\n.product-management-container[_ngcontent-%COMP%]   .table-container[_ngcontent-%COMP%]   .products-table[_ngcontent-%COMP%]   tbody[_ngcontent-%COMP%]   .product-row[_ngcontent-%COMP%]   .actions-cell[_ngcontent-%COMP%]   .action-buttons[_ngcontent-%COMP%]   .btn-action.btn-moderate[_ngcontent-%COMP%] {\n  background: #e6fffa;\n  color: #234e52;\n}\n.product-management-container[_ngcontent-%COMP%]   .table-container[_ngcontent-%COMP%]   .products-table[_ngcontent-%COMP%]   tbody[_ngcontent-%COMP%]   .product-row[_ngcontent-%COMP%]   .actions-cell[_ngcontent-%COMP%]   .action-buttons[_ngcontent-%COMP%]   .btn-action.btn-moderate[_ngcontent-%COMP%]:hover {\n  background: #b2f5ea;\n}\n.product-management-container[_ngcontent-%COMP%]   .table-container[_ngcontent-%COMP%]   .products-table[_ngcontent-%COMP%]   tbody[_ngcontent-%COMP%]   .product-row[_ngcontent-%COMP%]   .actions-cell[_ngcontent-%COMP%]   .action-buttons[_ngcontent-%COMP%]   .btn-action.btn-delete[_ngcontent-%COMP%] {\n  background: #fed7d7;\n  color: #742a2a;\n}\n.product-management-container[_ngcontent-%COMP%]   .table-container[_ngcontent-%COMP%]   .products-table[_ngcontent-%COMP%]   tbody[_ngcontent-%COMP%]   .product-row[_ngcontent-%COMP%]   .actions-cell[_ngcontent-%COMP%]   .action-buttons[_ngcontent-%COMP%]   .btn-action.btn-delete[_ngcontent-%COMP%]:hover {\n  background: #feb2b2;\n}\n.product-management-container[_ngcontent-%COMP%]   .table-container[_ngcontent-%COMP%]   .no-data[_ngcontent-%COMP%] {\n  text-align: center;\n  padding: 3rem;\n  color: #64748b;\n}\n.product-management-container[_ngcontent-%COMP%]   .pagination-container[_ngcontent-%COMP%] {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  background: white;\n  padding: 1rem 1.5rem;\n  border-radius: 12px;\n  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);\n}\n.product-management-container[_ngcontent-%COMP%]   .pagination-container[_ngcontent-%COMP%]   .pagination-info[_ngcontent-%COMP%] {\n  color: #64748b;\n  font-size: 0.9rem;\n}\n.product-management-container[_ngcontent-%COMP%]   .pagination-container[_ngcontent-%COMP%]   .pagination-controls[_ngcontent-%COMP%] {\n  display: flex;\n  align-items: center;\n  gap: 0.5rem;\n}\n.product-management-container[_ngcontent-%COMP%]   .pagination-container[_ngcontent-%COMP%]   .pagination-controls[_ngcontent-%COMP%]   .btn-page[_ngcontent-%COMP%] {\n  padding: 0.5rem 1rem;\n  border: 1px solid #e2e8f0;\n  background: white;\n  color: #1e293b;\n  border-radius: 6px;\n  cursor: pointer;\n  transition: all 0.2s ease;\n}\n.product-management-container[_ngcontent-%COMP%]   .pagination-container[_ngcontent-%COMP%]   .pagination-controls[_ngcontent-%COMP%]   .btn-page[_ngcontent-%COMP%]:hover:not(:disabled) {\n  background: #2563eb;\n  color: white;\n  border-color: #2563eb;\n}\n.product-management-container[_ngcontent-%COMP%]   .pagination-container[_ngcontent-%COMP%]   .pagination-controls[_ngcontent-%COMP%]   .btn-page[_ngcontent-%COMP%]:disabled {\n  opacity: 0.5;\n  cursor: not-allowed;\n}\n.product-management-container[_ngcontent-%COMP%]   .pagination-container[_ngcontent-%COMP%]   .pagination-controls[_ngcontent-%COMP%]   .page-numbers[_ngcontent-%COMP%] {\n  display: flex;\n  gap: 0.25rem;\n}\n.product-management-container[_ngcontent-%COMP%]   .pagination-container[_ngcontent-%COMP%]   .pagination-controls[_ngcontent-%COMP%]   .page-numbers[_ngcontent-%COMP%]   .btn-page-number[_ngcontent-%COMP%] {\n  width: 36px;\n  height: 36px;\n  border: 1px solid #e2e8f0;\n  background: white;\n  color: #1e293b;\n  border-radius: 6px;\n  cursor: pointer;\n  transition: all 0.2s ease;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n}\n.product-management-container[_ngcontent-%COMP%]   .pagination-container[_ngcontent-%COMP%]   .pagination-controls[_ngcontent-%COMP%]   .page-numbers[_ngcontent-%COMP%]   .btn-page-number[_ngcontent-%COMP%]:hover {\n  background: #2563eb;\n  color: white;\n  border-color: #2563eb;\n}\n.product-management-container[_ngcontent-%COMP%]   .pagination-container[_ngcontent-%COMP%]   .pagination-controls[_ngcontent-%COMP%]   .page-numbers[_ngcontent-%COMP%]   .btn-page-number.active[_ngcontent-%COMP%] {\n  background: #2563eb;\n  color: white;\n  border-color: #2563eb;\n}\n.product-management-container[_ngcontent-%COMP%]   .modal-overlay[_ngcontent-%COMP%] {\n  position: fixed;\n  top: 0;\n  left: 0;\n  right: 0;\n  bottom: 0;\n  background: rgba(0, 0, 0, 0.5);\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  z-index: 1000;\n}\n.product-management-container[_ngcontent-%COMP%]   .modal-overlay[_ngcontent-%COMP%]   .modal-content[_ngcontent-%COMP%] {\n  background: white;\n  border-radius: 12px;\n  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);\n  max-width: 500px;\n  width: 90%;\n  max-height: 80vh;\n  overflow-y: auto;\n}\n.product-management-container[_ngcontent-%COMP%]   .modal-overlay[_ngcontent-%COMP%]   .modal-content[_ngcontent-%COMP%]   .modal-header[_ngcontent-%COMP%] {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  padding: 1.5rem;\n  border-bottom: 1px solid #e2e8f0;\n}\n.product-management-container[_ngcontent-%COMP%]   .modal-overlay[_ngcontent-%COMP%]   .modal-content[_ngcontent-%COMP%]   .modal-header[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%] {\n  margin: 0;\n  color: #2563eb;\n  font-size: 1.3rem;\n}\n.product-management-container[_ngcontent-%COMP%]   .modal-overlay[_ngcontent-%COMP%]   .modal-content[_ngcontent-%COMP%]   .modal-header[_ngcontent-%COMP%]   .btn-close[_ngcontent-%COMP%] {\n  background: none;\n  border: none;\n  font-size: 1.5rem;\n  cursor: pointer;\n  color: #64748b;\n  width: 32px;\n  height: 32px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  border-radius: 50%;\n  transition: background-color 0.2s ease;\n}\n.product-management-container[_ngcontent-%COMP%]   .modal-overlay[_ngcontent-%COMP%]   .modal-content[_ngcontent-%COMP%]   .modal-header[_ngcontent-%COMP%]   .btn-close[_ngcontent-%COMP%]:hover {\n  background: #f0f0f0;\n}\n.product-management-container[_ngcontent-%COMP%]   .modal-overlay[_ngcontent-%COMP%]   .modal-content[_ngcontent-%COMP%]   .modal-body[_ngcontent-%COMP%] {\n  padding: 1.5rem;\n}\n.product-management-container[_ngcontent-%COMP%]   .modal-overlay[_ngcontent-%COMP%]   .modal-content[_ngcontent-%COMP%]   .modal-body[_ngcontent-%COMP%]   .form-group[_ngcontent-%COMP%] {\n  margin-bottom: 1.5rem;\n}\n.product-management-container[_ngcontent-%COMP%]   .modal-overlay[_ngcontent-%COMP%]   .modal-content[_ngcontent-%COMP%]   .modal-body[_ngcontent-%COMP%]   .form-group[_ngcontent-%COMP%]   label[_ngcontent-%COMP%] {\n  display: block;\n  margin-bottom: 0.5rem;\n  font-weight: 600;\n  color: #1e293b;\n}\n.product-management-container[_ngcontent-%COMP%]   .modal-overlay[_ngcontent-%COMP%]   .modal-content[_ngcontent-%COMP%]   .modal-body[_ngcontent-%COMP%]   .form-group[_ngcontent-%COMP%]   .form-input[_ngcontent-%COMP%], \n.product-management-container[_ngcontent-%COMP%]   .modal-overlay[_ngcontent-%COMP%]   .modal-content[_ngcontent-%COMP%]   .modal-body[_ngcontent-%COMP%]   .form-group[_ngcontent-%COMP%]   .form-textarea[_ngcontent-%COMP%] {\n  width: 100%;\n  padding: 0.75rem;\n  border: 1px solid #e2e8f0;\n  border-radius: 6px;\n  font-size: 1rem;\n  transition: border-color 0.3s ease;\n}\n.product-management-container[_ngcontent-%COMP%]   .modal-overlay[_ngcontent-%COMP%]   .modal-content[_ngcontent-%COMP%]   .modal-body[_ngcontent-%COMP%]   .form-group[_ngcontent-%COMP%]   .form-input[_ngcontent-%COMP%]:focus, \n.product-management-container[_ngcontent-%COMP%]   .modal-overlay[_ngcontent-%COMP%]   .modal-content[_ngcontent-%COMP%]   .modal-body[_ngcontent-%COMP%]   .form-group[_ngcontent-%COMP%]   .form-textarea[_ngcontent-%COMP%]:focus {\n  outline: none;\n  border-color: #2563eb;\n}\n.product-management-container[_ngcontent-%COMP%]   .modal-overlay[_ngcontent-%COMP%]   .modal-content[_ngcontent-%COMP%]   .modal-body[_ngcontent-%COMP%]   .form-group[_ngcontent-%COMP%]   .form-textarea[_ngcontent-%COMP%] {\n  resize: vertical;\n  min-height: 80px;\n}\n.product-management-container[_ngcontent-%COMP%]   .modal-overlay[_ngcontent-%COMP%]   .modal-content[_ngcontent-%COMP%]   .modal-body[_ngcontent-%COMP%]   .stock-info[_ngcontent-%COMP%] {\n  background: #f8f9fa;\n  padding: 1rem;\n  border-radius: 6px;\n  margin-top: 1rem;\n}\n.product-management-container[_ngcontent-%COMP%]   .modal-overlay[_ngcontent-%COMP%]   .modal-content[_ngcontent-%COMP%]   .modal-body[_ngcontent-%COMP%]   .stock-info[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\n  margin: 0.5rem 0;\n}\n.product-management-container[_ngcontent-%COMP%]   .modal-overlay[_ngcontent-%COMP%]   .modal-content[_ngcontent-%COMP%]   .modal-body[_ngcontent-%COMP%]   .stock-info[_ngcontent-%COMP%]   p.warning[_ngcontent-%COMP%] {\n  color: #e53e3e;\n  font-weight: 600;\n}\n.product-management-container[_ngcontent-%COMP%]   .modal-overlay[_ngcontent-%COMP%]   .modal-content[_ngcontent-%COMP%]   .modal-footer[_ngcontent-%COMP%] {\n  display: flex;\n  justify-content: flex-end;\n  gap: 1rem;\n  padding: 1.5rem;\n  border-top: 1px solid #e2e8f0;\n}\n.product-management-container[_ngcontent-%COMP%]   .modal-overlay[_ngcontent-%COMP%]   .modal-content[_ngcontent-%COMP%]   .modal-footer[_ngcontent-%COMP%]   .btn-secondary[_ngcontent-%COMP%], \n.product-management-container[_ngcontent-%COMP%]   .modal-overlay[_ngcontent-%COMP%]   .modal-content[_ngcontent-%COMP%]   .modal-footer[_ngcontent-%COMP%]   .btn-primary[_ngcontent-%COMP%] {\n  padding: 0.75rem 1.5rem;\n  border: none;\n  border-radius: 6px;\n  cursor: pointer;\n  font-weight: 600;\n  transition: all 0.2s ease;\n}\n.product-management-container[_ngcontent-%COMP%]   .modal-overlay[_ngcontent-%COMP%]   .modal-content[_ngcontent-%COMP%]   .modal-footer[_ngcontent-%COMP%]   .btn-secondary[_ngcontent-%COMP%] {\n  background: #f8f9fa;\n  color: #1e293b;\n}\n.product-management-container[_ngcontent-%COMP%]   .modal-overlay[_ngcontent-%COMP%]   .modal-content[_ngcontent-%COMP%]   .modal-footer[_ngcontent-%COMP%]   .btn-secondary[_ngcontent-%COMP%]:hover {\n  background: #e9ecef;\n}\n.product-management-container[_ngcontent-%COMP%]   .modal-overlay[_ngcontent-%COMP%]   .modal-content[_ngcontent-%COMP%]   .modal-footer[_ngcontent-%COMP%]   .btn-primary[_ngcontent-%COMP%] {\n  background: #2563eb;\n  color: white;\n}\n.product-management-container[_ngcontent-%COMP%]   .modal-overlay[_ngcontent-%COMP%]   .modal-content[_ngcontent-%COMP%]   .modal-footer[_ngcontent-%COMP%]   .btn-primary[_ngcontent-%COMP%]:hover {\n  background: rgb(18.5714285714, 76.1428571429, 202.4285714286);\n}\n@keyframes _ngcontent-%COMP%_spin {\n  0% {\n    transform: rotate(0deg);\n  }\n  100% {\n    transform: rotate(360deg);\n  }\n}\n@media (max-width: 768px) {\n  .product-management-container[_ngcontent-%COMP%] {\n    padding: 1rem;\n  }\n  .product-management-container[_ngcontent-%COMP%]   .filters-section[_ngcontent-%COMP%]   .filters-row[_ngcontent-%COMP%] {\n    flex-direction: column;\n    align-items: stretch;\n  }\n  .product-management-container[_ngcontent-%COMP%]   .filters-section[_ngcontent-%COMP%]   .filters-row[_ngcontent-%COMP%]   .filter-group[_ngcontent-%COMP%] {\n    width: 100%;\n  }\n  .product-management-container[_ngcontent-%COMP%]   .filters-section[_ngcontent-%COMP%]   .filters-row[_ngcontent-%COMP%]   .filter-group[_ngcontent-%COMP%]   .filter-select[_ngcontent-%COMP%], \n   .product-management-container[_ngcontent-%COMP%]   .filters-section[_ngcontent-%COMP%]   .filters-row[_ngcontent-%COMP%]   .filter-group[_ngcontent-%COMP%]   .filter-input[_ngcontent-%COMP%] {\n    min-width: auto;\n    width: 100%;\n  }\n  .product-management-container[_ngcontent-%COMP%]   .stats-row[_ngcontent-%COMP%] {\n    grid-template-columns: repeat(2, 1fr);\n  }\n  .product-management-container[_ngcontent-%COMP%]   .table-container[_ngcontent-%COMP%] {\n    overflow-x: auto;\n  }\n  .product-management-container[_ngcontent-%COMP%]   .table-container[_ngcontent-%COMP%]   .products-table[_ngcontent-%COMP%] {\n    min-width: 1000px;\n  }\n  .product-management-container[_ngcontent-%COMP%]   .pagination-container[_ngcontent-%COMP%] {\n    flex-direction: column;\n    gap: 1rem;\n    text-align: center;\n  }\n}\n/*# sourceMappingURL=product-management.component.css.map */"] });
};
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && setClassMetadata(ProductManagementComponent, [{
    type: Component,
    args: [{ selector: "app-product-management", standalone: true, imports: [CommonModule, FormsModule], template: `<div class="product-management-container">
  <div class="header">
    <h1>\u{1F6CD}\uFE0F Gestion des Produits</h1>
    <p class="subtitle">Validez, mod\xE9rez et g\xE9rez les produits de la plateforme</p>
  </div>

  <!-- Filtres et recherche -->
  <div class="filters-section">
    <div class="search-bar">
      <input 
        type="text" 
        [(ngModel)]="searchTerm" 
        (input)="onSearchChange()"
        placeholder="\u{1F50D} Rechercher par nom, r\xE9f\xE9rence..."
        class="search-input">
    </div>

    <div class="filters-row">
      <div class="filter-group">
        <label>Statut :</label>
        <select [(ngModel)]="selectedStatut" (change)="onStatutChange()" class="filter-select">
          <option *ngFor="let option of statutOptions" [value]="option.value">
            {{ option.label }}
          </option>
        </select>
      </div>

      <div class="filter-group">
        <label>Fournisseur :</label>
        <input 
          type="text" 
          [(ngModel)]="selectedFournisseur" 
          (input)="onFournisseurChange()"
          placeholder="Nom du fournisseur"
          class="filter-input">
      </div>

      <div class="filter-group">
        <label class="checkbox-label">
          <input 
            type="checkbox" 
            [(ngModel)]="showStockCritique" 
            (change)="onStockCritiqueToggle()"
            class="filter-checkbox">
          <span class="checkmark"></span>
          Stock critique uniquement
        </label>
      </div>
    </div>
  </div>

  <!-- Statistiques rapides -->
  <div class="stats-row">
    <div class="stat-card">
      <span class="stat-number">{{ totalItems() }}</span>
      <span class="stat-label">Produits total</span>
    </div>
    <div class="stat-card">
      <span class="stat-number">{{ getProduitsStockCritique() }}</span>
      <span class="stat-label">Stock critique</span>
    </div>
    <div class="stat-card">
      <span class="stat-number">{{ getProduitsEnAvant() }}</span>
      <span class="stat-label">Mis en avant</span>
    </div>
    <div class="stat-card">
      <span class="stat-number">{{ getProduitsEnAttente() }}</span>
      <span class="stat-label">En attente</span>
    </div>
  </div>

  <!-- Message d'erreur -->
  <div *ngIf="error()" class="error-message">
    \u274C {{ error() }}
  </div>

  <!-- Indicateur de chargement -->
  <div *ngIf="isLoading()" class="loading-spinner">
    <div class="spinner"></div>
    <p>Chargement des produits...</p>
  </div>

  <!-- Tableau des produits -->
  <div class="table-container" *ngIf="!isLoading()">
    <table class="products-table">
      <thead>
        <tr>
          <th>Image</th>
          <th>Produit</th>
          <th>R\xE9f\xE9rence</th>
          <th>Prix</th>
          <th>Stock</th>
          <th>Fournisseur</th>
          <th>Cat\xE9gorie</th>
          <th>Statut</th>
          <th>Actions</th>
        </tr>
      </thead>
      <tbody>
        <tr *ngFor="let produit of produits()" class="product-row" [ngClass]="getStatusClass(produit)">
          <td class="image-cell">
            <img
              [src]="imageUrlService.getProduitImageUrl(produit.imagePrincipale || (produit.images && produit.images.length > 0 ? produit.images[0] : null))"
              [alt]="produit.nom"
              class="product-image"
              (error)="onImageError($event)">
          </td>
          
          <td class="product-info">
            <div class="product-name">{{ produit.nom }}</div>
            <div class="product-meta">
              <span class="date">Ajout\xE9 le {{ formatDate(produit.dateCreation) }}</span>
              <span *ngIf="produit.estEnAvant" class="featured-badge">\u2B50 En avant</span>
            </div>
          </td>
          
          <td class="reference-cell">
            <div class="ref-original">{{ produit.referenceOriginal }}</div>
            <div *ngIf="produit.referenceFournisseur" class="ref-fournisseur">
              Fournisseur: {{ produit.referenceFournisseur }}
            </div>
          </td>
          
          <td class="price-cell">
            <div class="price-container">
              <!-- Prix initial (toujours affich\xE9) -->
              <div class="price-initial">
                <span class="price-label">Prix initial:</span>
                <span class="price-value initial" [class.crossed]="produit.pourcentageRemiseTotale > 0">
                  {{ formatPrice(produit.prixVenteTTC) }}
                </span>
              </div>

              <!-- Prix apr\xE8s promotion outlet (si diff\xE9rent du prix initial) -->
              <div class="price-outlet" *ngIf="produit.prixApresOutletTTC !== produit.prixVenteTTC">
                <span class="price-label">Apr\xE8s outlet:</span>
                <span class="price-value outlet">{{ formatPrice(produit.prixApresOutletTTC) }}</span>
              </div>

              <!-- Prix final (si diff\xE9rent du prix outlet) -->
              <div class="price-final" *ngIf="produit.prixFinalTTC !== produit.prixApresOutletTTC">
                <span class="price-label">Prix final:</span>
                <span class="price-value final">{{ formatPrice(produit.prixFinalTTC) }}</span>
              </div>

              <!-- Badge de remise totale -->
              <div class="discount-badge" *ngIf="produit.pourcentageRemiseTotale > 0">
                -{{ produit.pourcentageRemiseTotale }}%
              </div>
            </div>
          </td>
          
          <td class="stock-cell">
            <div class="stock-container">
              <span class="stock-value" [ngClass]="{'stock-critique': produit.estStockCritique}">
                {{ produit.stock }}
              </span>
              <div class="stock-alert" *ngIf="produit.estStockCritique">
                \u26A0\uFE0F Stock critique (\u226410)
              </div>
              <button
                class="btn-stock"
                (click)="openStockModal(produit)"
                title="Modifier le stock">
                \u{1F4E6}
              </button>
            </div>
          </td>
          
          <td class="supplier-cell">
            {{ produit.fournisseurNom }}
          </td>
          
          <td class="category-cell">
            <div class="category">{{ produit.categorieNom }}</div>
            <div class="subcategory">{{ produit.sousCategorieNom }}</div>
          </td>
          
          <td class="status-cell">
            <span class="status-badge" [ngClass]="getStatusClass(produit)">
              {{ produit.statutValidation }}
            </span>
            <span *ngIf="produit.estStockCritique" class="stock-warning">\u26A0\uFE0F Stock critique</span>
          </td>
          
          <td class="actions-cell">
            <div class="action-buttons">
              <!-- Validation -->
              <button 
                *ngIf="!produit.estValide"
                class="btn-action btn-validate" 
                (click)="validerProduit(produit)"
                title="Valider le produit">
                \u2705
              </button>
              
              <button 
                *ngIf="!produit.estValide"
                class="btn-action btn-reject" 
                (click)="refuserProduit(produit)"
                title="Refuser le produit">
                \u274C
              </button>
              
              <!-- Mise en avant -->
              <button 
                class="btn-action" 
                [ngClass]="produit.estEnAvant ? 'btn-unfeature' : 'btn-feature'"
                (click)="toggleMiseEnAvant(produit)"
                [title]="produit.estEnAvant ? 'Retirer la mise en avant' : 'Mettre en avant'">
                {{ produit.estEnAvant ? '\u2B50' : '\u2606' }}
              </button>
              
              <!-- Mod\xE9ration -->
              <button 
                class="btn-action btn-moderate" 
                (click)="openModerationModal(produit)"
                title="Mod\xE9rer le contenu">
                \u{1F50D}
              </button>
              
              <!-- Suppression -->
              <button 
                class="btn-action btn-delete" 
                (click)="supprimerProduit(produit)"
                title="Supprimer le produit">
                \u{1F5D1}\uFE0F
              </button>
            </div>
          </td>
        </tr>
      </tbody>
    </table>

    <!-- Message si aucun produit -->
    <div *ngIf="produits().length === 0" class="no-data">
      <p>Aucun produit trouv\xE9 avec les crit\xE8res s\xE9lectionn\xE9s.</p>
    </div>
  </div>

  <!-- Pagination -->
  <div class="pagination-container" *ngIf="totalPages() > 1">
    <div class="pagination-info">
      <p>Page {{ currentPage() }} sur {{ totalPages() }} ({{ totalItems() }} produits au total)</p>
    </div>
    
    <div class="pagination-controls">
      <button 
        class="btn-page" 
        [disabled]="currentPage() === 1"
        (click)="onPageChange(currentPage() - 1)">
        \u2190 Pr\xE9c\xE9dent
      </button>
      
      <span class="page-numbers">
        <button
          *ngFor="let page of getPageNumbers(); let i = index"
          class="btn-page-number"
          [ngClass]="{'active': currentPage() === page}"
          (click)="onPageChange(page)">
          {{ page }}
        </button>
      </span>
      
      <button 
        class="btn-page" 
        [disabled]="currentPage() === totalPages()"
        (click)="onPageChange(currentPage() + 1)">
        Suivant \u2192
      </button>
    </div>
  </div>
</div>

<!-- Modal de mod\xE9ration -->
<div *ngIf="showModerationModal()" class="modal-overlay" (click)="closeModerationModal()">
  <div class="modal-content" (click)="$event.stopPropagation()">
    <div class="modal-header">
      <h3>\u{1F50D} Mod\xE9ration du contenu</h3>
      <button class="btn-close" (click)="closeModerationModal()">\xD7</button>
    </div>
    
    <div class="modal-body">
      <div class="form-group">
        <label>Nouveau nom :</label>
        <input 
          type="text" 
          [(ngModel)]="moderationData().nouveauNom"
          class="form-input">
      </div>
      
      <div class="form-group">
        <label>Nouvelle description :</label>
        <textarea 
          [(ngModel)]="moderationData().nouvelleDescription"
          class="form-textarea"
          rows="4"></textarea>
      </div>
      
      <div class="form-group">
        <label>Raison de la mod\xE9ration :</label>
        <textarea 
          [(ngModel)]="moderationData().raison"
          class="form-textarea"
          rows="3"></textarea>
      </div>
    </div>
    
    <div class="modal-footer">
      <button class="btn-secondary" (click)="closeModerationModal()">Annuler</button>
      <button class="btn-primary" (click)="submitModeration()">Appliquer</button>
    </div>
  </div>
</div>

<!-- Modal de gestion du stock -->
<div *ngIf="showStockModal()" class="modal-overlay" (click)="closeStockModal()">
  <div class="modal-content" (click)="$event.stopPropagation()">
    <div class="modal-header">
      <h3>\u{1F4E6} Gestion du stock</h3>
      <button class="btn-close" (click)="closeStockModal()">\xD7</button>
    </div>
    
    <div class="modal-body">
      <div class="form-group">
        <label>Nouveau stock :</label>
        <input 
          type="number" 
          [(ngModel)]="nouveauStock"
          min="0"
          class="form-input">
      </div>
      
      <div class="stock-info">
        <p><strong>Stock actuel :</strong> {{ selectedProduit()?.stock }}</p>
        <p *ngIf="selectedProduit()?.estStockCritique" class="warning">
          \u26A0\uFE0F Ce produit est actuellement en stock critique
        </p>
      </div>
    </div>
    
    <div class="modal-footer">
      <button class="btn-secondary" (click)="closeStockModal()">Annuler</button>
      <button class="btn-primary" (click)="submitStockUpdate()">Mettre \xE0 jour</button>
    </div>
  </div>
</div>
`, styles: ["/* src/app/components/admin/product-management/product-management.component.scss */\n.product-management-container {\n  padding: 2rem;\n  max-width: 1400px;\n  margin: 0 auto;\n}\n.product-management-container .header {\n  text-align: center;\n  margin-bottom: 2rem;\n}\n.product-management-container .header h1 {\n  color: #2563eb;\n  font-size: 2.5rem;\n  margin-bottom: 0.5rem;\n}\n.product-management-container .header .subtitle {\n  color: #64748b;\n  font-size: 1.1rem;\n}\n.product-management-container .filters-section {\n  background: white;\n  padding: 1.5rem;\n  border-radius: 12px;\n  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);\n  margin-bottom: 2rem;\n}\n.product-management-container .filters-section .search-bar {\n  margin-bottom: 1rem;\n}\n.product-management-container .filters-section .search-bar .search-input {\n  width: 100%;\n  padding: 0.75rem 1rem;\n  border: 2px solid #e2e8f0;\n  border-radius: 8px;\n  font-size: 1rem;\n  transition: border-color 0.3s ease;\n}\n.product-management-container .filters-section .search-bar .search-input:focus {\n  outline: none;\n  border-color: #2563eb;\n}\n.product-management-container .filters-section .filters-row {\n  display: flex;\n  gap: 1.5rem;\n  flex-wrap: wrap;\n  align-items: end;\n}\n.product-management-container .filters-section .filters-row .filter-group {\n  display: flex;\n  flex-direction: column;\n  gap: 0.5rem;\n}\n.product-management-container .filters-section .filters-row .filter-group label {\n  font-weight: 600;\n  color: #1e293b;\n  font-size: 0.9rem;\n}\n.product-management-container .filters-section .filters-row .filter-group .filter-select,\n.product-management-container .filters-section .filters-row .filter-group .filter-input {\n  padding: 0.5rem;\n  border: 1px solid #e2e8f0;\n  border-radius: 6px;\n  font-size: 0.9rem;\n  min-width: 150px;\n}\n.product-management-container .filters-section .filters-row .filter-group .filter-select:focus,\n.product-management-container .filters-section .filters-row .filter-group .filter-input:focus {\n  outline: none;\n  border-color: #2563eb;\n}\n.product-management-container .filters-section .filters-row .filter-group .checkbox-label {\n  display: flex;\n  align-items: center;\n  gap: 0.5rem;\n  cursor: pointer;\n  font-weight: normal;\n}\n.product-management-container .filters-section .filters-row .filter-group .checkbox-label .filter-checkbox {\n  width: 18px;\n  height: 18px;\n}\n.product-management-container .stats-row {\n  display: grid;\n  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));\n  gap: 1rem;\n  margin-bottom: 2rem;\n}\n.product-management-container .stats-row .stat-card {\n  background: white;\n  padding: 1.5rem;\n  border-radius: 12px;\n  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);\n  text-align: center;\n  border-left: 4px solid #2563eb;\n}\n.product-management-container .stats-row .stat-card .stat-number {\n  display: block;\n  font-size: 2rem;\n  font-weight: bold;\n  color: #2563eb;\n  margin-bottom: 0.5rem;\n}\n.product-management-container .stats-row .stat-card .stat-label {\n  color: #64748b;\n  font-size: 0.9rem;\n  text-transform: uppercase;\n  letter-spacing: 0.5px;\n}\n.product-management-container .error-message {\n  background: #fee;\n  color: #c33;\n  padding: 1rem;\n  border-radius: 8px;\n  margin-bottom: 1rem;\n  border-left: 4px solid #c33;\n}\n.product-management-container .loading-spinner {\n  text-align: center;\n  padding: 3rem;\n}\n.product-management-container .loading-spinner .spinner {\n  width: 40px;\n  height: 40px;\n  border: 4px solid #f3f3f3;\n  border-top: 4px solid #2563eb;\n  border-radius: 50%;\n  animation: spin 1s linear infinite;\n  margin: 0 auto 1rem;\n}\n.product-management-container .table-container {\n  background: white;\n  border-radius: 12px;\n  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);\n  overflow: hidden;\n  margin-bottom: 2rem;\n}\n.product-management-container .table-container .products-table {\n  width: 100%;\n  border-collapse: collapse;\n}\n.product-management-container .table-container .products-table thead {\n  background: #2563eb;\n  color: white;\n}\n.product-management-container .table-container .products-table thead th {\n  padding: 1rem 0.75rem;\n  text-align: left;\n  font-weight: 600;\n  font-size: 0.9rem;\n  text-transform: uppercase;\n  letter-spacing: 0.5px;\n}\n.product-management-container .table-container .products-table tbody .product-row {\n  border-bottom: 1px solid #e2e8f0;\n  transition: background-color 0.2s ease;\n}\n.product-management-container .table-container .products-table tbody .product-row:hover {\n  background-color: #f8f9fa;\n}\n.product-management-container .table-container .products-table tbody .product-row.status-critique {\n  background-color: #fff5f5;\n  border-left: 4px solid #e53e3e;\n}\n.product-management-container .table-container .products-table tbody .product-row.status-attente {\n  background-color: #fffbf0;\n  border-left: 4px solid #f6ad55;\n}\n.product-management-container .table-container .products-table tbody .product-row.status-valide {\n  background-color: #f0fff4;\n  border-left: 4px solid #38a169;\n}\n.product-management-container .table-container .products-table tbody .product-row td {\n  padding: 1rem 0.75rem;\n  vertical-align: top;\n}\n.product-management-container .table-container .products-table tbody .product-row .image-cell {\n  width: 80px;\n}\n.product-management-container .table-container .products-table tbody .product-row .image-cell .product-image {\n  width: 60px;\n  height: 60px;\n  object-fit: cover;\n  border-radius: 8px;\n  border: 1px solid #e2e8f0;\n}\n.product-management-container .table-container .products-table tbody .product-row .product-info {\n  min-width: 200px;\n}\n.product-management-container .table-container .products-table tbody .product-row .product-info .product-name {\n  font-weight: 600;\n  color: #1e293b;\n  margin-bottom: 0.25rem;\n  line-height: 1.3;\n}\n.product-management-container .table-container .products-table tbody .product-row .product-info .product-meta {\n  display: flex;\n  gap: 0.5rem;\n  align-items: center;\n  flex-wrap: wrap;\n}\n.product-management-container .table-container .products-table tbody .product-row .product-info .product-meta .date {\n  font-size: 0.8rem;\n  color: #64748b;\n}\n.product-management-container .table-container .products-table tbody .product-row .product-info .product-meta .featured-badge {\n  background: #f59e0b;\n  color: white;\n  padding: 0.2rem 0.5rem;\n  border-radius: 12px;\n  font-size: 0.7rem;\n  font-weight: 600;\n}\n.product-management-container .table-container .products-table tbody .product-row .reference-cell .ref-original {\n  font-weight: 600;\n  color: #1e293b;\n}\n.product-management-container .table-container .products-table tbody .product-row .reference-cell .ref-fournisseur {\n  font-size: 0.8rem;\n  color: #64748b;\n  margin-top: 0.25rem;\n}\n.product-management-container .table-container .products-table tbody .product-row .price-cell {\n  text-align: right;\n}\n.product-management-container .table-container .products-table tbody .product-row .price-cell .price-container {\n  display: flex;\n  flex-direction: column;\n  gap: 4px;\n  position: relative;\n}\n.product-management-container .table-container .products-table tbody .product-row .price-cell .price-container .price-initial .price-label {\n  font-size: 0.75rem;\n  color: #64748b;\n  margin-right: 4px;\n  font-weight: 500;\n}\n.product-management-container .table-container .products-table tbody .product-row .price-cell .price-container .price-initial .price-value.initial {\n  color: #1e293b;\n  font-size: 0.95rem;\n  font-weight: 500;\n}\n.product-management-container .table-container .products-table tbody .product-row .price-cell .price-container .price-initial .price-value.initial.crossed {\n  text-decoration: line-through;\n  color: #64748b;\n}\n.product-management-container .table-container .products-table tbody .product-row .price-cell .price-container .price-outlet .price-label {\n  font-size: 0.8rem;\n  color: #f59e0b;\n  margin-right: 4px;\n  font-weight: 500;\n}\n.product-management-container .table-container .products-table tbody .product-row .price-cell .price-container .price-outlet .price-value.outlet {\n  color: #f59e0b;\n  font-weight: 600;\n  font-size: 1rem;\n}\n.product-management-container .table-container .products-table tbody .product-row .price-cell .price-container .price-final .price-label {\n  font-size: 0.8rem;\n  color: #2563eb;\n  margin-right: 4px;\n  font-weight: 500;\n}\n.product-management-container .table-container .products-table tbody .product-row .price-cell .price-container .price-final .price-value.final {\n  color: #2563eb;\n  font-weight: 700;\n  font-size: 1.1rem;\n}\n.product-management-container .table-container .products-table tbody .product-row .price-cell .price-container .discount-badge {\n  position: absolute;\n  top: -8px;\n  right: -8px;\n  background:\n    linear-gradient(\n      135deg,\n      #f59e0b,\n      #f7931e);\n  color: white;\n  font-size: 0.7rem;\n  padding: 3px 8px;\n  border-radius: 12px;\n  font-weight: 700;\n  box-shadow: 0 2px 4px rgba(255, 107, 53, 0.3);\n}\n.product-management-container .table-container .products-table tbody .product-row .price-cell .price {\n  font-weight: 600;\n  color: #2563eb;\n  font-size: 1.1rem;\n}\n.product-management-container .table-container .products-table tbody .product-row .stock-cell .stock-container {\n  display: flex;\n  flex-direction: column;\n  align-items: flex-start;\n  gap: 0.25rem;\n}\n.product-management-container .table-container .products-table tbody .product-row .stock-cell .stock-container .stock-value {\n  font-weight: 600;\n  padding: 0.25rem 0.5rem;\n  border-radius: 4px;\n  background: #e6fffa;\n  color: #234e52;\n  font-size: 1.1rem;\n}\n.product-management-container .table-container .products-table tbody .product-row .stock-cell .stock-container .stock-value.stock-critique {\n  background: #fed7d7;\n  color: #742a2a;\n  border: 2px solid #e53e3e;\n  animation: pulse-warning 2s infinite;\n}\n.product-management-container .table-container .products-table tbody .product-row .stock-cell .stock-container .stock-alert {\n  font-size: 0.75rem;\n  color: #e53e3e;\n  font-weight: 600;\n  background: #fed7d7;\n  padding: 2px 6px;\n  border-radius: 10px;\n  border: 1px solid #e53e3e;\n}\n.product-management-container .table-container .products-table tbody .product-row .stock-cell .stock-container .btn-stock {\n  background: none;\n  border: none;\n  cursor: pointer;\n  font-size: 1.2rem;\n  padding: 0.25rem;\n  border-radius: 4px;\n  transition: background-color 0.2s ease;\n  margin-top: 0.25rem;\n}\n.product-management-container .table-container .products-table tbody .product-row .stock-cell .stock-container .btn-stock:hover {\n  background: #f0f0f0;\n}\n@keyframes pulse-warning {\n  0%, 100% {\n    opacity: 1;\n  }\n  50% {\n    opacity: 0.7;\n  }\n}\n.product-management-container .table-container .products-table tbody .product-row .category-cell .category {\n  font-weight: 600;\n  color: #1e293b;\n}\n.product-management-container .table-container .products-table tbody .product-row .category-cell .subcategory {\n  font-size: 0.8rem;\n  color: #64748b;\n  margin-top: 0.25rem;\n}\n.product-management-container .table-container .products-table tbody .product-row .status-cell .status-badge {\n  padding: 0.25rem 0.75rem;\n  border-radius: 12px;\n  font-size: 0.8rem;\n  font-weight: 600;\n  text-transform: uppercase;\n  letter-spacing: 0.5px;\n}\n.product-management-container .table-container .products-table tbody .product-row .status-cell .status-badge.status-valide {\n  background: #c6f6d5;\n  color: #22543d;\n}\n.product-management-container .table-container .products-table tbody .product-row .status-cell .status-badge.status-attente {\n  background: #feebc8;\n  color: #744210;\n}\n.product-management-container .table-container .products-table tbody .product-row .status-cell .status-badge.status-critique {\n  background: #fed7d7;\n  color: #742a2a;\n}\n.product-management-container .table-container .products-table tbody .product-row .status-cell .stock-warning {\n  display: block;\n  font-size: 0.7rem;\n  color: #e53e3e;\n  margin-top: 0.25rem;\n}\n.product-management-container .table-container .products-table tbody .product-row .actions-cell .action-buttons {\n  display: flex;\n  gap: 0.25rem;\n  flex-wrap: wrap;\n}\n.product-management-container .table-container .products-table tbody .product-row .actions-cell .action-buttons .btn-action {\n  background: none;\n  border: 1px solid transparent;\n  padding: 0.5rem;\n  border-radius: 6px;\n  cursor: pointer;\n  font-size: 1rem;\n  transition: all 0.2s ease;\n  min-width: 36px;\n  height: 36px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n}\n.product-management-container .table-container .products-table tbody .product-row .actions-cell .action-buttons .btn-action:hover {\n  transform: translateY(-1px);\n}\n.product-management-container .table-container .products-table tbody .product-row .actions-cell .action-buttons .btn-action.btn-validate {\n  background: #c6f6d5;\n  color: #22543d;\n}\n.product-management-container .table-container .products-table tbody .product-row .actions-cell .action-buttons .btn-action.btn-validate:hover {\n  background: #9ae6b4;\n}\n.product-management-container .table-container .products-table tbody .product-row .actions-cell .action-buttons .btn-action.btn-reject {\n  background: #fed7d7;\n  color: #742a2a;\n}\n.product-management-container .table-container .products-table tbody .product-row .actions-cell .action-buttons .btn-action.btn-reject:hover {\n  background: #feb2b2;\n}\n.product-management-container .table-container .products-table tbody .product-row .actions-cell .action-buttons .btn-action.btn-feature {\n  background: #feebc8;\n  color: #744210;\n}\n.product-management-container .table-container .products-table tbody .product-row .actions-cell .action-buttons .btn-action.btn-feature:hover {\n  background: #fbd38d;\n}\n.product-management-container .table-container .products-table tbody .product-row .actions-cell .action-buttons .btn-action.btn-unfeature {\n  background: #f59e0b;\n  color: white;\n}\n.product-management-container .table-container .products-table tbody .product-row .actions-cell .action-buttons .btn-action.btn-unfeature:hover {\n  background: rgb(196.9291338583, 126.7125984252, 8.0708661417);\n}\n.product-management-container .table-container .products-table tbody .product-row .actions-cell .action-buttons .btn-action.btn-moderate {\n  background: #e6fffa;\n  color: #234e52;\n}\n.product-management-container .table-container .products-table tbody .product-row .actions-cell .action-buttons .btn-action.btn-moderate:hover {\n  background: #b2f5ea;\n}\n.product-management-container .table-container .products-table tbody .product-row .actions-cell .action-buttons .btn-action.btn-delete {\n  background: #fed7d7;\n  color: #742a2a;\n}\n.product-management-container .table-container .products-table tbody .product-row .actions-cell .action-buttons .btn-action.btn-delete:hover {\n  background: #feb2b2;\n}\n.product-management-container .table-container .no-data {\n  text-align: center;\n  padding: 3rem;\n  color: #64748b;\n}\n.product-management-container .pagination-container {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  background: white;\n  padding: 1rem 1.5rem;\n  border-radius: 12px;\n  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);\n}\n.product-management-container .pagination-container .pagination-info {\n  color: #64748b;\n  font-size: 0.9rem;\n}\n.product-management-container .pagination-container .pagination-controls {\n  display: flex;\n  align-items: center;\n  gap: 0.5rem;\n}\n.product-management-container .pagination-container .pagination-controls .btn-page {\n  padding: 0.5rem 1rem;\n  border: 1px solid #e2e8f0;\n  background: white;\n  color: #1e293b;\n  border-radius: 6px;\n  cursor: pointer;\n  transition: all 0.2s ease;\n}\n.product-management-container .pagination-container .pagination-controls .btn-page:hover:not(:disabled) {\n  background: #2563eb;\n  color: white;\n  border-color: #2563eb;\n}\n.product-management-container .pagination-container .pagination-controls .btn-page:disabled {\n  opacity: 0.5;\n  cursor: not-allowed;\n}\n.product-management-container .pagination-container .pagination-controls .page-numbers {\n  display: flex;\n  gap: 0.25rem;\n}\n.product-management-container .pagination-container .pagination-controls .page-numbers .btn-page-number {\n  width: 36px;\n  height: 36px;\n  border: 1px solid #e2e8f0;\n  background: white;\n  color: #1e293b;\n  border-radius: 6px;\n  cursor: pointer;\n  transition: all 0.2s ease;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n}\n.product-management-container .pagination-container .pagination-controls .page-numbers .btn-page-number:hover {\n  background: #2563eb;\n  color: white;\n  border-color: #2563eb;\n}\n.product-management-container .pagination-container .pagination-controls .page-numbers .btn-page-number.active {\n  background: #2563eb;\n  color: white;\n  border-color: #2563eb;\n}\n.product-management-container .modal-overlay {\n  position: fixed;\n  top: 0;\n  left: 0;\n  right: 0;\n  bottom: 0;\n  background: rgba(0, 0, 0, 0.5);\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  z-index: 1000;\n}\n.product-management-container .modal-overlay .modal-content {\n  background: white;\n  border-radius: 12px;\n  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);\n  max-width: 500px;\n  width: 90%;\n  max-height: 80vh;\n  overflow-y: auto;\n}\n.product-management-container .modal-overlay .modal-content .modal-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  padding: 1.5rem;\n  border-bottom: 1px solid #e2e8f0;\n}\n.product-management-container .modal-overlay .modal-content .modal-header h3 {\n  margin: 0;\n  color: #2563eb;\n  font-size: 1.3rem;\n}\n.product-management-container .modal-overlay .modal-content .modal-header .btn-close {\n  background: none;\n  border: none;\n  font-size: 1.5rem;\n  cursor: pointer;\n  color: #64748b;\n  width: 32px;\n  height: 32px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  border-radius: 50%;\n  transition: background-color 0.2s ease;\n}\n.product-management-container .modal-overlay .modal-content .modal-header .btn-close:hover {\n  background: #f0f0f0;\n}\n.product-management-container .modal-overlay .modal-content .modal-body {\n  padding: 1.5rem;\n}\n.product-management-container .modal-overlay .modal-content .modal-body .form-group {\n  margin-bottom: 1.5rem;\n}\n.product-management-container .modal-overlay .modal-content .modal-body .form-group label {\n  display: block;\n  margin-bottom: 0.5rem;\n  font-weight: 600;\n  color: #1e293b;\n}\n.product-management-container .modal-overlay .modal-content .modal-body .form-group .form-input,\n.product-management-container .modal-overlay .modal-content .modal-body .form-group .form-textarea {\n  width: 100%;\n  padding: 0.75rem;\n  border: 1px solid #e2e8f0;\n  border-radius: 6px;\n  font-size: 1rem;\n  transition: border-color 0.3s ease;\n}\n.product-management-container .modal-overlay .modal-content .modal-body .form-group .form-input:focus,\n.product-management-container .modal-overlay .modal-content .modal-body .form-group .form-textarea:focus {\n  outline: none;\n  border-color: #2563eb;\n}\n.product-management-container .modal-overlay .modal-content .modal-body .form-group .form-textarea {\n  resize: vertical;\n  min-height: 80px;\n}\n.product-management-container .modal-overlay .modal-content .modal-body .stock-info {\n  background: #f8f9fa;\n  padding: 1rem;\n  border-radius: 6px;\n  margin-top: 1rem;\n}\n.product-management-container .modal-overlay .modal-content .modal-body .stock-info p {\n  margin: 0.5rem 0;\n}\n.product-management-container .modal-overlay .modal-content .modal-body .stock-info p.warning {\n  color: #e53e3e;\n  font-weight: 600;\n}\n.product-management-container .modal-overlay .modal-content .modal-footer {\n  display: flex;\n  justify-content: flex-end;\n  gap: 1rem;\n  padding: 1.5rem;\n  border-top: 1px solid #e2e8f0;\n}\n.product-management-container .modal-overlay .modal-content .modal-footer .btn-secondary,\n.product-management-container .modal-overlay .modal-content .modal-footer .btn-primary {\n  padding: 0.75rem 1.5rem;\n  border: none;\n  border-radius: 6px;\n  cursor: pointer;\n  font-weight: 600;\n  transition: all 0.2s ease;\n}\n.product-management-container .modal-overlay .modal-content .modal-footer .btn-secondary {\n  background: #f8f9fa;\n  color: #1e293b;\n}\n.product-management-container .modal-overlay .modal-content .modal-footer .btn-secondary:hover {\n  background: #e9ecef;\n}\n.product-management-container .modal-overlay .modal-content .modal-footer .btn-primary {\n  background: #2563eb;\n  color: white;\n}\n.product-management-container .modal-overlay .modal-content .modal-footer .btn-primary:hover {\n  background: rgb(18.5714285714, 76.1428571429, 202.4285714286);\n}\n@keyframes spin {\n  0% {\n    transform: rotate(0deg);\n  }\n  100% {\n    transform: rotate(360deg);\n  }\n}\n@media (max-width: 768px) {\n  .product-management-container {\n    padding: 1rem;\n  }\n  .product-management-container .filters-section .filters-row {\n    flex-direction: column;\n    align-items: stretch;\n  }\n  .product-management-container .filters-section .filters-row .filter-group {\n    width: 100%;\n  }\n  .product-management-container .filters-section .filters-row .filter-group .filter-select,\n  .product-management-container .filters-section .filters-row .filter-group .filter-input {\n    min-width: auto;\n    width: 100%;\n  }\n  .product-management-container .stats-row {\n    grid-template-columns: repeat(2, 1fr);\n  }\n  .product-management-container .table-container {\n    overflow-x: auto;\n  }\n  .product-management-container .table-container .products-table {\n    min-width: 1000px;\n  }\n  .product-management-container .pagination-container {\n    flex-direction: column;\n    gap: 1rem;\n    text-align: center;\n  }\n}\n/*# sourceMappingURL=product-management.component.css.map */\n"] }]
  }], () => [{ type: AdminService }, { type: ImageUrlService }], null);
})();
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && \u0275setClassDebugInfo(ProductManagementComponent, { className: "ProductManagementComponent", filePath: "src/app/components/admin/product-management/product-management.component.ts", lineNumber: 14 });
})();
export {
  ProductManagementComponent
};
//# sourceMappingURL=chunk-YPTXIRSN.js.map

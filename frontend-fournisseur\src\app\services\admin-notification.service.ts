import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Observable, BehaviorSubject, interval } from 'rxjs';
import { environment } from '../../environments/environment';

export interface AdminNotification {
  id: number;
  type: 'DEMANDE_CATEGORIE' | 'DEMANDE_SOUS_CATEGORIE' | 'NOUVEAU_PRODUIT' | 'COMMANDE' | 'AUTRE';
  titre: string;
  message: string;
  fournisseurNom?: string;
  fournisseurId?: number;
  referenceId?: number; // ID de la demande/produit/commande
  dateCreation: Date;
  estLue: boolean;
  priority: 'HIGH' | 'MEDIUM' | 'LOW';
  actionUrl?: string; // URL pour rediriger vers l'action
}

export interface DemandeNotificationDetail {
  demandeId: number;
  type: 'CATEGORIE' | 'SOUS_CATEGORIE';
  nomDemande: string;
  fournisseurRaisonSociale: string;
  fournisseurId: number;
  dateCreation: Date;
  statut: 'EN_ATTENTE' | 'APPROUVEE' | 'REJETEE';
}

@Injectable({
  providedIn: 'root'
})
export class AdminNotificationService {
  private apiUrl = `${environment.apiUrl}`;
  
  private notificationsSubject = new BehaviorSubject<AdminNotification[]>([]);
  public notifications$ = this.notificationsSubject.asObservable();
  
  private unreadCountSubject = new BehaviorSubject<number>(0);
  public unreadCount$ = this.unreadCountSubject.asObservable();

  constructor(private http: HttpClient) {
    // Actualiser les notifications toutes les 2 minutes au lieu de 30 secondes
    // et seulement si l'utilisateur est actif
    interval(120000).subscribe(() => {
      if (document.visibilityState === 'visible') {
        this.loadNotifications();
      }
    });
  }

  loadNotifications(): void {
    this.getAdminNotifications().subscribe({
      next: (notifications) => {
        this.notificationsSubject.next(notifications);
        const unreadCount = notifications.filter(n => !n.estLue).length;
        this.unreadCountSubject.next(unreadCount);
      },
      error: (error) => {
        console.error('Erreur lors du chargement des notifications admin:', error);
      }
    });
  }

  getAdminNotifications(): Observable<AdminNotification[]> {
    return this.http.get<AdminNotification[]>(`${this.apiUrl}/admin/notifications`);
  }

  getDemandesNotifications(): Observable<DemandeNotificationDetail[]> {
    return this.http.get<DemandeNotificationDetail[]>(`${this.apiUrl}/admin/notifications/demandes`);
  }

  markAsRead(notificationId: number): Observable<void> {
    return this.http.put<void>(`${this.apiUrl}/admin/notifications/${notificationId}/read`, {});
  }

  markAllAsRead(): Observable<void> {
    return this.http.put<void>(`${this.apiUrl}/admin/notifications/mark-all-read`, {});
  }

  deleteNotification(notificationId: number): Observable<void> {
    return this.http.delete<void>(`${this.apiUrl}/admin/notifications/${notificationId}`);
  }

  // Méthodes pour créer des notifications spécifiques
  createDemandeNotification(demande: any, type: 'CATEGORIE' | 'SOUS_CATEGORIE'): AdminNotification {
    const typeText = type === 'CATEGORIE' ? 'catégorie' : 'sous-catégorie';
    return {
      id: Date.now(), // Temporaire
      type: type === 'CATEGORIE' ? 'DEMANDE_CATEGORIE' : 'DEMANDE_SOUS_CATEGORIE',
      titre: `Nouvelle demande de ${typeText}`,
      message: `Le fournisseur ${demande.fournisseurRaisonSociale} souhaite créer une nouvelle ${typeText} nommée "${demande.nom}"`,
      fournisseurNom: demande.fournisseurRaisonSociale,
      fournisseurId: demande.fournisseurId,
      referenceId: demande.id,
      dateCreation: new Date(demande.dateCreation),
      estLue: false,
      priority: 'HIGH',
      actionUrl: `/admin/dashboard/demandes`
    };
  }

  // Simuler des notifications pour test (à supprimer en production)
  getMockNotifications(): AdminNotification[] {
    return [
      {
        id: 1,
        type: 'DEMANDE_CATEGORIE',
        titre: 'Nouvelle demande de catégorie',
        message: 'Le fournisseur Optique Vision Plus souhaite créer une nouvelle catégorie nommée "Lunettes de Sport"',
        fournisseurNom: 'Optique Vision Plus',
        fournisseurId: 11,
        referenceId: 1,
        dateCreation: new Date(),
        estLue: false,
        priority: 'HIGH',
        actionUrl: '/admin/dashboard/demandes'
      },
      {
        id: 2,
        type: 'DEMANDE_SOUS_CATEGORIE',
        titre: 'Nouvelle demande de sous-catégorie',
        message: 'Le fournisseur Optique El Manar souhaite créer une nouvelle sous-catégorie nommée "Lunettes de Natation"',
        fournisseurNom: 'Optique El Manar',
        fournisseurId: 12,
        referenceId: 2,
        dateCreation: new Date(Date.now() - 3600000), // Il y a 1 heure
        estLue: false,
        priority: 'HIGH',
        actionUrl: '/admin/dashboard/demandes'
      },
      {
        id: 3,
        type: 'NOUVEAU_PRODUIT',
        titre: 'Nouveau produit en attente',
        message: 'Le fournisseur Optique Centrale a ajouté un nouveau produit "Ray-Ban Aviator" en attente de validation',
        fournisseurNom: 'Optique Centrale',
        fournisseurId: 13,
        referenceId: 101,
        dateCreation: new Date(Date.now() - 7200000), // Il y a 2 heures
        estLue: true,
        priority: 'MEDIUM',
        actionUrl: '/admin/dashboard/products'
      }
    ];
  }
}

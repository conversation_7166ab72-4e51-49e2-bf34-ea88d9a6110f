﻿// <auto-generated />
using System;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Infrastructure;
using Microsoft.EntityFrameworkCore.Metadata;
using Microsoft.EntityFrameworkCore.Migrations;
using Microsoft.EntityFrameworkCore.Storage.ValueConversion;
using WebApiPfe;

#nullable disable

namespace WebApiPfe.Migrations
{
    [DbContext(typeof(AppDbContext))]
    [Migration("20250719204938_AjoutCommentaireSupprime")]
    partial class AjoutCommentaireSupprime
    {
        /// <inheritdoc />
        protected override void BuildTargetModel(ModelBuilder modelBuilder)
        {
#pragma warning disable 612, 618
            modelBuilder
                .HasAnnotation("ProductVersion", "8.0.10")
                .HasAnnotation("Relational:MaxIdentifierLength", 128);

            SqlServerModelBuilderExtensions.UseIdentityColumns(modelBuilder);

            modelBuilder.Entity("Microsoft.AspNetCore.Identity.IdentityRole<int>", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<string>("ConcurrencyStamp")
                        .IsConcurrencyToken()
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Name")
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.Property<string>("NormalizedName")
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.HasKey("Id");

                    b.HasIndex("NormalizedName")
                        .IsUnique()
                        .HasDatabaseName("RoleNameIndex")
                        .HasFilter("[NormalizedName] IS NOT NULL");

                    b.ToTable("AspNetRoles", (string)null);
                });

            modelBuilder.Entity("Microsoft.AspNetCore.Identity.IdentityRoleClaim<int>", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<string>("ClaimType")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("ClaimValue")
                        .HasColumnType("nvarchar(max)");

                    b.Property<int>("RoleId")
                        .HasColumnType("int");

                    b.HasKey("Id");

                    b.HasIndex("RoleId");

                    b.ToTable("AspNetRoleClaims", (string)null);
                });

            modelBuilder.Entity("Microsoft.AspNetCore.Identity.IdentityUserClaim<int>", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<string>("ClaimType")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("ClaimValue")
                        .HasColumnType("nvarchar(max)");

                    b.Property<int>("UserId")
                        .HasColumnType("int");

                    b.HasKey("Id");

                    b.HasIndex("UserId");

                    b.ToTable("AspNetUserClaims", (string)null);
                });

            modelBuilder.Entity("Microsoft.AspNetCore.Identity.IdentityUserLogin<int>", b =>
                {
                    b.Property<string>("LoginProvider")
                        .HasColumnType("nvarchar(450)");

                    b.Property<string>("ProviderKey")
                        .HasColumnType("nvarchar(450)");

                    b.Property<string>("ProviderDisplayName")
                        .HasColumnType("nvarchar(max)");

                    b.Property<int>("UserId")
                        .HasColumnType("int");

                    b.HasKey("LoginProvider", "ProviderKey");

                    b.HasIndex("UserId");

                    b.ToTable("AspNetUserLogins", (string)null);
                });

            modelBuilder.Entity("Microsoft.AspNetCore.Identity.IdentityUserRole<int>", b =>
                {
                    b.Property<int>("UserId")
                        .HasColumnType("int");

                    b.Property<int>("RoleId")
                        .HasColumnType("int");

                    b.HasKey("UserId", "RoleId");

                    b.HasIndex("RoleId");

                    b.ToTable("AspNetUserRoles", (string)null);
                });

            modelBuilder.Entity("Microsoft.AspNetCore.Identity.IdentityUserToken<int>", b =>
                {
                    b.Property<int>("UserId")
                        .HasColumnType("int");

                    b.Property<string>("LoginProvider")
                        .HasColumnType("nvarchar(450)");

                    b.Property<string>("Name")
                        .HasColumnType("nvarchar(450)");

                    b.Property<string>("Value")
                        .HasColumnType("nvarchar(max)");

                    b.HasKey("UserId", "LoginProvider", "Name");

                    b.ToTable("AspNetUserTokens", (string)null);
                });

            modelBuilder.Entity("ProduitPromotion", b =>
                {
                    b.Property<int>("ProduitId")
                        .HasColumnType("int");

                    b.Property<int>("PromotionId")
                        .HasColumnType("int");

                    b.HasKey("ProduitId", "PromotionId");

                    b.HasIndex("PromotionId");

                    b.ToTable("ProduitPromotion", (string)null);
                });

            modelBuilder.Entity("WebApiPfe.Models.Entity.Adresse", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<string>("CodePostal")
                        .IsRequired()
                        .HasMaxLength(20)
                        .HasColumnType("nvarchar(20)");

                    b.Property<bool>("EstPrincipale")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bit")
                        .HasDefaultValue(false);

                    b.Property<string>("Pays")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<string>("Rue")
                        .IsRequired()
                        .HasMaxLength(200)
                        .HasColumnType("nvarchar(200)");

                    b.Property<int>("UtilisateurId")
                        .HasColumnType("int");

                    b.Property<string>("Ville")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.HasKey("Id");

                    b.HasIndex("UtilisateurId", "EstPrincipale");

                    b.ToTable("Adresses");
                });

            modelBuilder.Entity("WebApiPfe.Models.Entity.Avis", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<int>("ClientId")
                        .HasColumnType("int");

                    b.Property<string>("Commentaire")
                        .IsRequired()
                        .HasMaxLength(1000)
                        .HasColumnType("nvarchar(1000)");

                    b.Property<string>("CommentaireModeration")
                        .HasMaxLength(500)
                        .HasColumnType("nvarchar(500)");

                    b.Property<bool>("CommentaireSupprime")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bit")
                        .HasDefaultValue(false);

                    b.Property<DateTime?>("DateModeration")
                        .HasColumnType("datetime2");

                    b.Property<DateTime>("DatePublication")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("datetime2")
                        .HasDefaultValueSql("GETUTCDATE()");

                    b.Property<int?>("ModerePar")
                        .HasColumnType("int");

                    b.Property<int>("Note")
                        .HasColumnType("int");

                    b.Property<int>("ProduitId")
                        .HasColumnType("int");

                    b.Property<int>("Statut")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasDefaultValue(1);

                    b.HasKey("Id");

                    b.HasIndex("ModerePar");

                    b.HasIndex("ProduitId");

                    b.HasIndex("ClientId", "ProduitId")
                        .IsUnique();

                    b.ToTable("Avis");
                });

            modelBuilder.Entity("WebApiPfe.Models.Entity.Categorie", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<string>("Description")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<bool>("EstValidee")
                        .HasColumnType("bit");

                    b.Property<string>("Nom")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.HasKey("Id");

                    b.HasIndex("Nom")
                        .IsUnique();

                    b.ToTable("Categories");
                });

            modelBuilder.Entity("WebApiPfe.Models.Entity.Commande", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<int>("ClientId")
                        .HasColumnType("int");

                    b.Property<DateTime>("DateCreation")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("datetime2")
                        .HasDefaultValueSql("GETUTCDATE()");

                    b.Property<decimal>("FraisLivraison")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("decimal(18,2)")
                        .HasDefaultValue(0m);

                    b.Property<decimal>("MontantTotal")
                        .HasColumnType("decimal(18,2)");

                    b.Property<string>("Statut")
                        .IsRequired()
                        .HasColumnType("nvarchar(450)");

                    b.HasKey("Id");

                    b.HasIndex("ClientId", "Statut");

                    b.ToTable("Commandes");
                });

            modelBuilder.Entity("WebApiPfe.Models.Entity.CommandeFournisseur", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<int>("CommandeClientId")
                        .HasColumnType("int");

                    b.Property<DateTime>("DateCreation")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("datetime2")
                        .HasDefaultValueSql("GETUTCDATE()");

                    b.Property<DateTime?>("DateLivraison")
                        .HasColumnType("datetime2");

                    b.Property<int>("FournisseurId")
                        .HasColumnType("int");

                    b.Property<decimal>("FraisLivraison")
                        .HasColumnType("decimal(18,2)");

                    b.Property<decimal>("MontantTotal")
                        .HasColumnType("decimal(18,2)");

                    b.Property<string>("NumeroBonLivraison")
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<string>("Reference")
                        .IsRequired()
                        .HasMaxLength(20)
                        .HasColumnType("nvarchar(20)");

                    b.Property<string>("Statut")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.HasKey("Id");

                    b.HasIndex("CommandeClientId");

                    b.HasIndex("FournisseurId");

                    b.HasIndex("Reference")
                        .IsUnique();

                    b.ToTable("CommandesFournisseurs");
                });

            modelBuilder.Entity("WebApiPfe.Models.Entity.DemandeCategorie", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<int?>("AdminTraitantId")
                        .HasColumnType("int");

                    b.Property<int?>("CategorieCreeeId")
                        .HasColumnType("int");

                    b.Property<string>("CommentaireAdmin")
                        .HasMaxLength(500)
                        .HasColumnType("nvarchar(500)");

                    b.Property<DateTime>("DateDemande")
                        .HasColumnType("datetime2");

                    b.Property<DateTime?>("DateTraitement")
                        .HasColumnType("datetime2");

                    b.Property<string>("Description")
                        .HasMaxLength(500)
                        .HasColumnType("nvarchar(500)");

                    b.Property<int>("FournisseurId")
                        .HasColumnType("int");

                    b.Property<string>("Nom")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<int>("Statut")
                        .HasColumnType("int");

                    b.HasKey("Id");

                    b.HasIndex("AdminTraitantId");

                    b.HasIndex("CategorieCreeeId");

                    b.HasIndex("FournisseurId");

                    b.ToTable("DemandesCategories");
                });

            modelBuilder.Entity("WebApiPfe.Models.Entity.DemandeSousCategorie", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<int?>("AdminTraitantId")
                        .HasColumnType("int");

                    b.Property<int>("CategorieId")
                        .HasColumnType("int");

                    b.Property<string>("CommentaireAdmin")
                        .HasMaxLength(500)
                        .HasColumnType("nvarchar(500)");

                    b.Property<DateTime>("DateDemande")
                        .HasColumnType("datetime2");

                    b.Property<DateTime?>("DateTraitement")
                        .HasColumnType("datetime2");

                    b.Property<string>("Description")
                        .HasMaxLength(500)
                        .HasColumnType("nvarchar(500)");

                    b.Property<int>("FournisseurId")
                        .HasColumnType("int");

                    b.Property<string>("Nom")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<int?>("SousCategorieCreeeId")
                        .HasColumnType("int");

                    b.Property<int>("Statut")
                        .HasColumnType("int");

                    b.HasKey("Id");

                    b.HasIndex("AdminTraitantId");

                    b.HasIndex("CategorieId");

                    b.HasIndex("FournisseurId");

                    b.HasIndex("SousCategorieCreeeId");

                    b.ToTable("DemandesSousCategories");
                });

            modelBuilder.Entity("WebApiPfe.Models.Entity.DetailsCommande", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<int>("CommandeId")
                        .HasColumnType("int");

                    b.Property<decimal>("PrixUnitaireHT")
                        .HasColumnType("decimal(18,2)");

                    b.Property<int>("ProduitId")
                        .HasColumnType("int");

                    b.Property<int>("Quantite")
                        .HasColumnType("int");

                    b.Property<decimal>("TauxTVAValue")
                        .HasColumnType("decimal(5,2)");

                    b.HasKey("Id");

                    b.HasIndex("ProduitId");

                    b.HasIndex("CommandeId", "ProduitId")
                        .IsUnique();

                    b.ToTable("DetailsCommandes");
                });

            modelBuilder.Entity("WebApiPfe.Models.Entity.Favori", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<int>("ClientId")
                        .HasColumnType("int");

                    b.Property<int>("ProduitId")
                        .HasColumnType("int");

                    b.Property<bool>("StockStatus")
                        .HasColumnType("bit");

                    b.HasKey("Id");

                    b.HasIndex("ProduitId");

                    b.HasIndex("ClientId", "ProduitId")
                        .IsUnique();

                    b.ToTable("Favoris");
                });

            modelBuilder.Entity("WebApiPfe.Models.Entity.Forme", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<int>("CategorieId")
                        .HasColumnType("int");

                    b.Property<string>("ImageUrl")
                        .IsRequired()
                        .HasMaxLength(500)
                        .HasColumnType("nvarchar(500)");

                    b.Property<string>("Nom")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.HasKey("Id");

                    b.HasIndex("CategorieId");

                    b.HasIndex("Nom", "CategorieId")
                        .IsUnique();

                    b.ToTable("Formes");
                });

            modelBuilder.Entity("WebApiPfe.Models.Entity.ImagesProduit", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<string>("ImageUrl")
                        .IsRequired()
                        .HasMaxLength(500)
                        .HasColumnType("nvarchar(500)");

                    b.Property<bool>("IsMain")
                        .HasColumnType("bit");

                    b.Property<int>("Ordre")
                        .HasColumnType("int");

                    b.Property<int>("ProduitId")
                        .HasColumnType("int");

                    b.HasKey("Id");

                    b.HasIndex("ProduitId", "Ordre")
                        .IsUnique();

                    b.ToTable("ImagesProduit");
                });

            modelBuilder.Entity("WebApiPfe.Models.Entity.ItemPanier", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<DateTime>("DateAjout")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("datetime2")
                        .HasDefaultValueSql("GETUTCDATE()");

                    b.Property<int>("PanierId")
                        .HasColumnType("int");

                    b.Property<decimal?>("PrixApresPromotion")
                        .HasColumnType("decimal(18,2)");

                    b.Property<decimal>("PrixUnitaire")
                        .HasColumnType("decimal(18,2)");

                    b.Property<int>("ProduitId")
                        .HasColumnType("int");

                    b.Property<int>("Quantite")
                        .HasColumnType("int");

                    b.HasKey("Id");

                    b.HasIndex("ProduitId");

                    b.HasIndex("PanierId", "ProduitId")
                        .IsUnique();

                    b.ToTable("ItemsPanier");
                });

            modelBuilder.Entity("WebApiPfe.Models.Entity.LigneCommandeFournisseur", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<int>("CommandeId")
                        .HasColumnType("int");

                    b.Property<decimal>("MontantLigne")
                        .HasColumnType("decimal(18,2)");

                    b.Property<decimal>("PrixUnitaire")
                        .HasColumnType("decimal(18,2)");

                    b.Property<int>("ProduitId")
                        .HasColumnType("int");

                    b.Property<int>("Quantite")
                        .HasColumnType("int");

                    b.HasKey("Id");

                    b.HasIndex("CommandeId");

                    b.HasIndex("ProduitId");

                    b.ToTable("LignesCommandeFournisseur");
                });

            modelBuilder.Entity("WebApiPfe.Models.Entity.Livraison", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<int>("AdresseLivraisonId")
                        .HasColumnType("int");

                    b.Property<int>("CommandeId")
                        .HasColumnType("int");

                    b.Property<DateTime?>("DateChangementStatut")
                        .HasColumnType("datetime2");

                    b.Property<int?>("FournisseurId")
                        .HasColumnType("int");

                    b.Property<string>("NumeroSuivi")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<decimal>("PoidsTotal")
                        .HasColumnType("decimal(10,3)");

                    b.Property<int>("StatutLivraisonId")
                        .HasColumnType("int");

                    b.Property<string>("Transporteur")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.HasKey("Id");

                    b.HasIndex("AdresseLivraisonId");

                    b.HasIndex("CommandeId")
                        .IsUnique();

                    b.HasIndex("FournisseurId");

                    b.HasIndex("NumeroSuivi")
                        .IsUnique();

                    b.HasIndex("StatutLivraisonId");

                    b.ToTable("Livraisons");
                });

            modelBuilder.Entity("WebApiPfe.Models.Entity.Marque", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<string>("Logo")
                        .IsRequired()
                        .HasMaxLength(500)
                        .HasColumnType("nvarchar(500)");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.HasKey("Id");

                    b.HasIndex("Name")
                        .IsUnique();

                    b.ToTable("Marques");
                });

            modelBuilder.Entity("WebApiPfe.Models.Entity.Notification", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<string>("Contenu")
                        .IsRequired()
                        .HasMaxLength(1000)
                        .HasColumnType("nvarchar(1000)");

                    b.Property<DateTime>("DateEnvoi")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("datetime2")
                        .HasDefaultValueSql("GETUTCDATE()");

                    b.Property<bool>("EstLue")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bit")
                        .HasDefaultValue(false);

                    b.Property<string>("Message")
                        .IsRequired()
                        .HasMaxLength(1000)
                        .HasColumnType("nvarchar(1000)");

                    b.Property<string>("Titre")
                        .IsRequired()
                        .HasMaxLength(200)
                        .HasColumnType("nvarchar(200)");

                    b.Property<int>("Type")
                        .HasColumnType("int");

                    b.Property<int>("UtilisateurId")
                        .HasColumnType("int");

                    b.HasKey("Id");

                    b.HasIndex("UtilisateurId");

                    b.ToTable("Notifications");
                });

            modelBuilder.Entity("WebApiPfe.Models.Entity.Paiement", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<int>("CommandeId")
                        .HasColumnType("int");

                    b.Property<DateTime>("DateCreation")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("datetime2")
                        .HasDefaultValueSql("GETUTCDATE()");

                    b.Property<DateTime>("DateMiseAJour")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("datetime2")
                        .HasDefaultValueSql("GETUTCDATE()");

                    b.Property<string>("Methode")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<decimal>("Montant")
                        .HasColumnType("decimal(18,2)");

                    b.Property<decimal?>("MontantRembourse")
                        .HasColumnType("decimal(18,2)");

                    b.Property<string>("Statut")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("TransactionId")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.HasKey("Id");

                    b.HasIndex("CommandeId")
                        .IsUnique();

                    b.HasIndex("TransactionId")
                        .IsUnique();

                    b.ToTable("Paiements");
                });

            modelBuilder.Entity("WebApiPfe.Models.Entity.Panier", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<int>("ClientId")
                        .HasColumnType("int");

                    b.Property<string>("CodePromoApplique")
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<DateTime>("DateCreation")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("datetime2")
                        .HasDefaultValueSql("GETUTCDATE()");

                    b.Property<bool>("EstActif")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bit")
                        .HasDefaultValue(true);

                    b.Property<decimal>("Total")
                        .HasColumnType("decimal(18,2)");

                    b.HasKey("Id");

                    b.HasIndex("ClientId")
                        .IsUnique()
                        .HasFilter("[EstActif] = 1");

                    b.ToTable("Paniers");
                });

            modelBuilder.Entity("WebApiPfe.Models.Entity.Produit", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<string>("CodeABarre")
                        .IsRequired()
                        .HasMaxLength(13)
                        .HasColumnType("nchar(13)")
                        .IsFixedLength();

                    b.Property<DateTime>("DateAjout")
                        .HasColumnType("datetime2");

                    b.Property<string>("Description")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<bool>("EstMisEnAvant")
                        .HasColumnType("bit");

                    b.Property<int>("FormeId")
                        .HasColumnType("int");

                    b.Property<int>("FournisseurId")
                        .HasColumnType("int");

                    b.Property<int>("MarqueId")
                        .HasColumnType("int");

                    b.Property<string>("Nom")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<decimal>("PrixAchatHT")
                        .HasColumnType("decimal(18,2)");

                    b.Property<decimal>("PrixVenteHT")
                        .HasColumnType("decimal(18,2)");

                    b.Property<string>("ReferenceFournisseur")
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<string>("ReferenceOriginal")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<int>("SousCategorieId")
                        .HasColumnType("int");

                    b.Property<int>("Stock")
                        .HasColumnType("int");

                    b.Property<int>("TauxTVAId")
                        .HasColumnType("int");

                    b.HasKey("Id");

                    b.HasIndex("CodeABarre")
                        .IsUnique();

                    b.HasIndex("FormeId");

                    b.HasIndex("FournisseurId");

                    b.HasIndex("MarqueId");

                    b.HasIndex("ReferenceOriginal")
                        .IsUnique();

                    b.HasIndex("SousCategorieId");

                    b.HasIndex("TauxTVAId");

                    b.ToTable("Produits");
                });

            modelBuilder.Entity("WebApiPfe.Models.Entity.Promotion", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<bool>("AppliquerSurHT")
                        .HasColumnType("bit");

                    b.Property<int?>("CategorieId")
                        .HasColumnType("int");

                    b.Property<string>("CodePromo")
                        .HasMaxLength(20)
                        .HasColumnType("nvarchar(20)");

                    b.Property<DateTime>("DateDebut")
                        .HasColumnType("datetime2");

                    b.Property<DateTime?>("DateFin")
                        .HasColumnType("datetime2");

                    b.Property<int?>("FormeId")
                        .HasColumnType("int");

                    b.Property<int?>("FournisseurId")
                        .HasColumnType("int");

                    b.Property<int?>("MarqueId")
                        .HasColumnType("int");

                    b.Property<decimal>("PourcentageRemise")
                        .HasColumnType("decimal(5,2)");

                    b.Property<int?>("SousCategorieId")
                        .HasColumnType("int");

                    b.Property<string>("Type")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.HasKey("Id");

                    b.HasIndex("CategorieId");

                    b.HasIndex("CodePromo")
                        .IsUnique()
                        .HasFilter("[CodePromo] IS NOT NULL");

                    b.HasIndex("FormeId");

                    b.HasIndex("FournisseurId");

                    b.HasIndex("MarqueId");

                    b.HasIndex("SousCategorieId");

                    b.ToTable("Promotions");
                });

            modelBuilder.Entity("WebApiPfe.Models.Entity.PromotionUtilisee", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<string>("CodePromoUtilise")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<int>("CommandeId")
                        .HasColumnType("int");

                    b.Property<DateTime>("DateUtilisation")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("datetime2")
                        .HasDefaultValueSql("GETUTCDATE()");

                    b.Property<decimal>("MontantEconomise")
                        .HasColumnType("decimal(18,2)");

                    b.Property<int>("PromotionId")
                        .HasColumnType("int");

                    b.HasKey("Id");

                    b.HasIndex("CommandeId");

                    b.HasIndex("PromotionId");

                    b.ToTable("PromotionsUtilisees");
                });

            modelBuilder.Entity("WebApiPfe.Models.Entity.Remboursement", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<int>("CommandeId")
                        .HasColumnType("int");

                    b.Property<DateTime>("DateDemande")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("datetime2")
                        .HasDefaultValueSql("GETUTCDATE()");

                    b.Property<DateTime?>("DateTraitement")
                        .HasColumnType("datetime2");

                    b.Property<int>("InitiateurId")
                        .HasColumnType("int");

                    b.Property<decimal>("Montant")
                        .HasColumnType("decimal(18,2)");

                    b.Property<int>("PaiementId")
                        .HasColumnType("int");

                    b.Property<string>("Raison")
                        .IsRequired()
                        .HasMaxLength(500)
                        .HasColumnType("nvarchar(500)");

                    b.Property<string>("RemboursementId")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<string>("Statut")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.HasKey("Id");

                    b.HasIndex("CommandeId");

                    b.HasIndex("InitiateurId");

                    b.HasIndex("PaiementId")
                        .IsUnique();

                    b.ToTable("Remboursements");
                });

            modelBuilder.Entity("WebApiPfe.Models.Entity.SousCategorie", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<int>("CategorieId")
                        .HasColumnType("int");

                    b.Property<string>("Description")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<bool>("EstValidee")
                        .HasColumnType("bit");

                    b.Property<string>("Nom")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.HasKey("Id");

                    b.HasIndex("CategorieId");

                    b.HasIndex("Nom", "CategorieId")
                        .IsUnique();

                    b.ToTable("SousCategories");
                });

            modelBuilder.Entity("WebApiPfe.Models.Entity.StatutLivraison", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<string>("Code")
                        .IsRequired()
                        .HasColumnType("nvarchar(450)");

                    b.Property<DateTime>("DateCreation")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("datetime2")
                        .HasDefaultValueSql("GETUTCDATE()");

                    b.Property<string>("Description")
                        .IsRequired()
                        .HasMaxLength(200)
                        .HasColumnType("nvarchar(200)");

                    b.Property<bool>("EstTerminal")
                        .HasColumnType("bit");

                    b.Property<string>("Libelle")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.HasKey("Id");

                    b.HasIndex("Code")
                        .IsUnique();

                    b.ToTable("StatutsLivraison");
                });

            modelBuilder.Entity("WebApiPfe.Models.Entity.StockFournisseur", b =>
                {
                    b.Property<int>("FournisseurId")
                        .HasColumnType("int")
                        .HasColumnOrder(0);

                    b.Property<int>("ProduitId")
                        .HasColumnType("int")
                        .HasColumnOrder(1);

                    b.Property<int>("DelaiLivraison")
                        .HasColumnType("int");

                    b.Property<decimal>("PrixUnitaire")
                        .HasColumnType("decimal(18,2)");

                    b.Property<int>("Quantite")
                        .HasColumnType("int");

                    b.HasKey("FournisseurId", "ProduitId");

                    b.HasIndex("ProduitId");

                    b.ToTable("StocksFournisseurs");
                });

            modelBuilder.Entity("WebApiPfe.Models.Entity.TauxTVA", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<int?>("CategorieId")
                        .HasColumnType("int");

                    b.Property<DateTime>("DateEffet")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("datetime2")
                        .HasDefaultValueSql("GETUTCDATE()");

                    b.Property<DateTime?>("DateFin")
                        .HasColumnType("datetime2");

                    b.Property<string>("Description")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<bool>("EstActif")
                        .HasColumnType("bit");

                    b.Property<string>("Libelle")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<decimal>("Taux")
                        .HasColumnType("decimal(5,2)");

                    b.HasKey("Id");

                    b.HasIndex("CategorieId");

                    b.HasIndex("Libelle")
                        .IsUnique();

                    b.ToTable("TauxTVA");
                });

            modelBuilder.Entity("WebApiPfe.Models.Entity.Utilisateur", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<int>("AccessFailedCount")
                        .HasColumnType("int");

                    b.Property<string>("ConcurrencyStamp")
                        .IsConcurrencyToken()
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTime>("DateInscription")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("datetime2")
                        .HasDefaultValueSql("GETUTCDATE()");

                    b.Property<DateTime>("DateNaissance")
                        .HasColumnType("date");

                    b.Property<DateTime?>("DerniereConnexion")
                        .HasColumnType("datetime2");

                    b.Property<string>("Email")
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.Property<bool>("EmailConfirmed")
                        .HasColumnType("bit");

                    b.Property<bool>("EstActif")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bit")
                        .HasDefaultValue(true);

                    b.Property<bool>("LockoutEnabled")
                        .HasColumnType("bit");

                    b.Property<DateTimeOffset?>("LockoutEnd")
                        .HasColumnType("datetimeoffset");

                    b.Property<string>("Nom")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<string>("NormalizedEmail")
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.Property<string>("NormalizedUserName")
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.Property<string>("PasswordHash")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("PhoneNumber")
                        .HasColumnType("nvarchar(max)");

                    b.Property<bool>("PhoneNumberConfirmed")
                        .HasColumnType("bit");

                    b.Property<string>("Prenom")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<int>("RoleDiscriminator")
                        .HasColumnType("int");

                    b.Property<string>("SecurityStamp")
                        .HasColumnType("nvarchar(max)");

                    b.Property<bool>("TwoFactorEnabled")
                        .HasColumnType("bit");

                    b.Property<string>("UserName")
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.HasKey("Id");

                    b.HasIndex("NormalizedEmail")
                        .HasDatabaseName("EmailIndex");

                    b.HasIndex("NormalizedUserName")
                        .IsUnique()
                        .HasDatabaseName("UserNameIndex")
                        .HasFilter("[NormalizedUserName] IS NOT NULL");

                    b.ToTable("AspNetUsers", (string)null);

                    b.HasDiscriminator<int>("RoleDiscriminator");

                    b.UseTphMappingStrategy();
                });

            modelBuilder.Entity("WebApiPfe.Models.Entity.Admin", b =>
                {
                    b.HasBaseType("WebApiPfe.Models.Entity.Utilisateur");

                    b.Property<string>("AdminToken")
                        .IsRequired()
                        .HasMaxLength(20)
                        .HasColumnType("nvarchar(20)");

                    b.HasIndex("AdminToken")
                        .IsUnique()
                        .HasFilter("[AdminToken] IS NOT NULL");

                    b.HasDiscriminator().HasValue(0);
                });

            modelBuilder.Entity("WebApiPfe.Models.Entity.Client", b =>
                {
                    b.HasBaseType("WebApiPfe.Models.Entity.Utilisateur");

                    b.HasDiscriminator().HasValue(1);
                });

            modelBuilder.Entity("WebApiPfe.Models.Entity.Fournisseur", b =>
                {
                    b.HasBaseType("WebApiPfe.Models.Entity.Utilisateur");

                    b.Property<string>("CodeBanque")
                        .IsRequired()
                        .HasMaxLength(3)
                        .HasColumnType("nchar(3)")
                        .IsFixedLength();

                    b.Property<decimal>("Commission")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("decimal(3,2)")
                        .HasDefaultValue(0.15m);

                    b.Property<int>("DelaiPreparationJours")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasDefaultValue(2);

                    b.Property<string>("Description")
                        .HasColumnType("nvarchar(max)");

                    b.Property<decimal>("FraisLivraisonBase")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("decimal(10,2)")
                        .HasDefaultValue(9.99m);

                    b.Property<string>("LogoFile")
                        .IsRequired()
                        .HasMaxLength(500)
                        .HasColumnType("nvarchar(500)");

                    b.Property<string>("MatriculeFiscale")
                        .IsRequired()
                        .HasMaxLength(8)
                        .HasColumnType("nchar(8)")
                        .IsFixedLength();

                    b.Property<string>("RIB")
                        .IsRequired()
                        .HasMaxLength(20)
                        .HasColumnType("nchar(20)")
                        .IsFixedLength();

                    b.Property<string>("RaisonSociale")
                        .IsRequired()
                        .HasMaxLength(200)
                        .HasColumnType("nvarchar(200)");

                    b.HasIndex("MatriculeFiscale")
                        .IsUnique()
                        .HasFilter("[MatriculeFiscale] IS NOT NULL");

                    b.HasIndex("RIB")
                        .IsUnique()
                        .HasFilter("[RIB] IS NOT NULL");

                    b.HasDiscriminator().HasValue(2);
                });

            modelBuilder.Entity("Microsoft.AspNetCore.Identity.IdentityRoleClaim<int>", b =>
                {
                    b.HasOne("Microsoft.AspNetCore.Identity.IdentityRole<int>", null)
                        .WithMany()
                        .HasForeignKey("RoleId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();
                });

            modelBuilder.Entity("Microsoft.AspNetCore.Identity.IdentityUserClaim<int>", b =>
                {
                    b.HasOne("WebApiPfe.Models.Entity.Utilisateur", null)
                        .WithMany()
                        .HasForeignKey("UserId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();
                });

            modelBuilder.Entity("Microsoft.AspNetCore.Identity.IdentityUserLogin<int>", b =>
                {
                    b.HasOne("WebApiPfe.Models.Entity.Utilisateur", null)
                        .WithMany()
                        .HasForeignKey("UserId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();
                });

            modelBuilder.Entity("Microsoft.AspNetCore.Identity.IdentityUserRole<int>", b =>
                {
                    b.HasOne("Microsoft.AspNetCore.Identity.IdentityRole<int>", null)
                        .WithMany()
                        .HasForeignKey("RoleId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("WebApiPfe.Models.Entity.Utilisateur", null)
                        .WithMany()
                        .HasForeignKey("UserId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();
                });

            modelBuilder.Entity("Microsoft.AspNetCore.Identity.IdentityUserToken<int>", b =>
                {
                    b.HasOne("WebApiPfe.Models.Entity.Utilisateur", null)
                        .WithMany()
                        .HasForeignKey("UserId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();
                });

            modelBuilder.Entity("ProduitPromotion", b =>
                {
                    b.HasOne("WebApiPfe.Models.Entity.Produit", null)
                        .WithMany()
                        .HasForeignKey("ProduitId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("WebApiPfe.Models.Entity.Promotion", null)
                        .WithMany()
                        .HasForeignKey("PromotionId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();
                });

            modelBuilder.Entity("WebApiPfe.Models.Entity.Adresse", b =>
                {
                    b.HasOne("WebApiPfe.Models.Entity.Utilisateur", "Utilisateur")
                        .WithMany("Adresses")
                        .HasForeignKey("UtilisateurId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Utilisateur");
                });

            modelBuilder.Entity("WebApiPfe.Models.Entity.Avis", b =>
                {
                    b.HasOne("WebApiPfe.Models.Entity.Client", "Client")
                        .WithMany("Avis")
                        .HasForeignKey("ClientId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.HasOne("WebApiPfe.Models.Entity.Utilisateur", "Moderateur")
                        .WithMany()
                        .HasForeignKey("ModerePar")
                        .OnDelete(DeleteBehavior.SetNull);

                    b.HasOne("WebApiPfe.Models.Entity.Produit", "Produit")
                        .WithMany("Avis")
                        .HasForeignKey("ProduitId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Client");

                    b.Navigation("Moderateur");

                    b.Navigation("Produit");
                });

            modelBuilder.Entity("WebApiPfe.Models.Entity.Commande", b =>
                {
                    b.HasOne("WebApiPfe.Models.Entity.Client", "Client")
                        .WithMany("Commandes")
                        .HasForeignKey("ClientId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.Navigation("Client");
                });

            modelBuilder.Entity("WebApiPfe.Models.Entity.CommandeFournisseur", b =>
                {
                    b.HasOne("WebApiPfe.Models.Entity.Commande", "CommandeClient")
                        .WithMany("CommandesFournisseurs")
                        .HasForeignKey("CommandeClientId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("WebApiPfe.Models.Entity.Fournisseur", "Fournisseur")
                        .WithMany("CommandesFournisseurs")
                        .HasForeignKey("FournisseurId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.Navigation("CommandeClient");

                    b.Navigation("Fournisseur");
                });

            modelBuilder.Entity("WebApiPfe.Models.Entity.DemandeCategorie", b =>
                {
                    b.HasOne("WebApiPfe.Models.Entity.Admin", "AdminTraitant")
                        .WithMany()
                        .HasForeignKey("AdminTraitantId");

                    b.HasOne("WebApiPfe.Models.Entity.Categorie", "CategorieCreee")
                        .WithMany()
                        .HasForeignKey("CategorieCreeeId");

                    b.HasOne("WebApiPfe.Models.Entity.Fournisseur", "Fournisseur")
                        .WithMany()
                        .HasForeignKey("FournisseurId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("AdminTraitant");

                    b.Navigation("CategorieCreee");

                    b.Navigation("Fournisseur");
                });

            modelBuilder.Entity("WebApiPfe.Models.Entity.DemandeSousCategorie", b =>
                {
                    b.HasOne("WebApiPfe.Models.Entity.Admin", "AdminTraitant")
                        .WithMany()
                        .HasForeignKey("AdminTraitantId");

                    b.HasOne("WebApiPfe.Models.Entity.Categorie", "Categorie")
                        .WithMany()
                        .HasForeignKey("CategorieId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("WebApiPfe.Models.Entity.Fournisseur", "Fournisseur")
                        .WithMany()
                        .HasForeignKey("FournisseurId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("WebApiPfe.Models.Entity.SousCategorie", "SousCategorieCreee")
                        .WithMany()
                        .HasForeignKey("SousCategorieCreeeId");

                    b.Navigation("AdminTraitant");

                    b.Navigation("Categorie");

                    b.Navigation("Fournisseur");

                    b.Navigation("SousCategorieCreee");
                });

            modelBuilder.Entity("WebApiPfe.Models.Entity.DetailsCommande", b =>
                {
                    b.HasOne("WebApiPfe.Models.Entity.Commande", "Commande")
                        .WithMany("DetailsCommandes")
                        .HasForeignKey("CommandeId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("WebApiPfe.Models.Entity.Produit", "Produit")
                        .WithMany("DetailsCommandes")
                        .HasForeignKey("ProduitId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.Navigation("Commande");

                    b.Navigation("Produit");
                });

            modelBuilder.Entity("WebApiPfe.Models.Entity.Favori", b =>
                {
                    b.HasOne("WebApiPfe.Models.Entity.Client", "Client")
                        .WithMany("Favoris")
                        .HasForeignKey("ClientId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("WebApiPfe.Models.Entity.Produit", "Produit")
                        .WithMany("Favoris")
                        .HasForeignKey("ProduitId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Client");

                    b.Navigation("Produit");
                });

            modelBuilder.Entity("WebApiPfe.Models.Entity.Forme", b =>
                {
                    b.HasOne("WebApiPfe.Models.Entity.Categorie", "Categorie")
                        .WithMany("Formes")
                        .HasForeignKey("CategorieId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.Navigation("Categorie");
                });

            modelBuilder.Entity("WebApiPfe.Models.Entity.ImagesProduit", b =>
                {
                    b.HasOne("WebApiPfe.Models.Entity.Produit", "Produit")
                        .WithMany("Images")
                        .HasForeignKey("ProduitId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Produit");
                });

            modelBuilder.Entity("WebApiPfe.Models.Entity.ItemPanier", b =>
                {
                    b.HasOne("WebApiPfe.Models.Entity.Panier", "Panier")
                        .WithMany("Items")
                        .HasForeignKey("PanierId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("WebApiPfe.Models.Entity.Produit", "Produit")
                        .WithMany()
                        .HasForeignKey("ProduitId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.Navigation("Panier");

                    b.Navigation("Produit");
                });

            modelBuilder.Entity("WebApiPfe.Models.Entity.LigneCommandeFournisseur", b =>
                {
                    b.HasOne("WebApiPfe.Models.Entity.CommandeFournisseur", "Commande")
                        .WithMany("LignesCommande")
                        .HasForeignKey("CommandeId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("WebApiPfe.Models.Entity.Produit", "Produit")
                        .WithMany("LignesCommandeFournisseur")
                        .HasForeignKey("ProduitId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.Navigation("Commande");

                    b.Navigation("Produit");
                });

            modelBuilder.Entity("WebApiPfe.Models.Entity.Livraison", b =>
                {
                    b.HasOne("WebApiPfe.Models.Entity.Adresse", "AdresseLivraison")
                        .WithMany()
                        .HasForeignKey("AdresseLivraisonId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.HasOne("WebApiPfe.Models.Entity.Commande", "Commande")
                        .WithOne("Livraison")
                        .HasForeignKey("WebApiPfe.Models.Entity.Livraison", "CommandeId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.HasOne("WebApiPfe.Models.Entity.Fournisseur", "Fournisseur")
                        .WithMany()
                        .HasForeignKey("FournisseurId")
                        .OnDelete(DeleteBehavior.SetNull);

                    b.HasOne("WebApiPfe.Models.Entity.StatutLivraison", "StatutLivraison")
                        .WithMany("Livraisons")
                        .HasForeignKey("StatutLivraisonId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.Navigation("AdresseLivraison");

                    b.Navigation("Commande");

                    b.Navigation("Fournisseur");

                    b.Navigation("StatutLivraison");
                });

            modelBuilder.Entity("WebApiPfe.Models.Entity.Notification", b =>
                {
                    b.HasOne("WebApiPfe.Models.Entity.Utilisateur", "Utilisateur")
                        .WithMany("Notifications")
                        .HasForeignKey("UtilisateurId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Utilisateur");
                });

            modelBuilder.Entity("WebApiPfe.Models.Entity.Paiement", b =>
                {
                    b.HasOne("WebApiPfe.Models.Entity.Commande", "Commande")
                        .WithOne("Paiement")
                        .HasForeignKey("WebApiPfe.Models.Entity.Paiement", "CommandeId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.Navigation("Commande");
                });

            modelBuilder.Entity("WebApiPfe.Models.Entity.Panier", b =>
                {
                    b.HasOne("WebApiPfe.Models.Entity.Client", "Client")
                        .WithOne("Panier")
                        .HasForeignKey("WebApiPfe.Models.Entity.Panier", "ClientId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Client");
                });

            modelBuilder.Entity("WebApiPfe.Models.Entity.Produit", b =>
                {
                    b.HasOne("WebApiPfe.Models.Entity.Forme", "Forme")
                        .WithMany("Produits")
                        .HasForeignKey("FormeId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.HasOne("WebApiPfe.Models.Entity.Fournisseur", "Fournisseur")
                        .WithMany("Produits")
                        .HasForeignKey("FournisseurId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.HasOne("WebApiPfe.Models.Entity.Marque", "Marque")
                        .WithMany("Produits")
                        .HasForeignKey("MarqueId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.HasOne("WebApiPfe.Models.Entity.SousCategorie", "SousCategorie")
                        .WithMany("Produits")
                        .HasForeignKey("SousCategorieId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.HasOne("WebApiPfe.Models.Entity.TauxTVA", "TauxTVA")
                        .WithMany("Produits")
                        .HasForeignKey("TauxTVAId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.Navigation("Forme");

                    b.Navigation("Fournisseur");

                    b.Navigation("Marque");

                    b.Navigation("SousCategorie");

                    b.Navigation("TauxTVA");
                });

            modelBuilder.Entity("WebApiPfe.Models.Entity.Promotion", b =>
                {
                    b.HasOne("WebApiPfe.Models.Entity.Categorie", "Categorie")
                        .WithMany()
                        .HasForeignKey("CategorieId")
                        .OnDelete(DeleteBehavior.Restrict);

                    b.HasOne("WebApiPfe.Models.Entity.Forme", "Forme")
                        .WithMany()
                        .HasForeignKey("FormeId")
                        .OnDelete(DeleteBehavior.Restrict);

                    b.HasOne("WebApiPfe.Models.Entity.Fournisseur", "Fournisseur")
                        .WithMany()
                        .HasForeignKey("FournisseurId")
                        .OnDelete(DeleteBehavior.Restrict);

                    b.HasOne("WebApiPfe.Models.Entity.Marque", "Marque")
                        .WithMany()
                        .HasForeignKey("MarqueId")
                        .OnDelete(DeleteBehavior.Restrict);

                    b.HasOne("WebApiPfe.Models.Entity.SousCategorie", "SousCategorie")
                        .WithMany()
                        .HasForeignKey("SousCategorieId")
                        .OnDelete(DeleteBehavior.Restrict);

                    b.Navigation("Categorie");

                    b.Navigation("Forme");

                    b.Navigation("Fournisseur");

                    b.Navigation("Marque");

                    b.Navigation("SousCategorie");
                });

            modelBuilder.Entity("WebApiPfe.Models.Entity.PromotionUtilisee", b =>
                {
                    b.HasOne("WebApiPfe.Models.Entity.Commande", "Commande")
                        .WithMany("PromotionsUtilisees")
                        .HasForeignKey("CommandeId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("WebApiPfe.Models.Entity.Promotion", "Promotion")
                        .WithMany()
                        .HasForeignKey("PromotionId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.Navigation("Commande");

                    b.Navigation("Promotion");
                });

            modelBuilder.Entity("WebApiPfe.Models.Entity.Remboursement", b =>
                {
                    b.HasOne("WebApiPfe.Models.Entity.Commande", "Commande")
                        .WithMany("Remboursements")
                        .HasForeignKey("CommandeId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.HasOne("WebApiPfe.Models.Entity.Utilisateur", "Initiateur")
                        .WithMany("RemboursementsInities")
                        .HasForeignKey("InitiateurId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.HasOne("WebApiPfe.Models.Entity.Paiement", "Paiement")
                        .WithOne("Remboursement")
                        .HasForeignKey("WebApiPfe.Models.Entity.Remboursement", "PaiementId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.Navigation("Commande");

                    b.Navigation("Initiateur");

                    b.Navigation("Paiement");
                });

            modelBuilder.Entity("WebApiPfe.Models.Entity.SousCategorie", b =>
                {
                    b.HasOne("WebApiPfe.Models.Entity.Categorie", "Categorie")
                        .WithMany("SousCategories")
                        .HasForeignKey("CategorieId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.Navigation("Categorie");
                });

            modelBuilder.Entity("WebApiPfe.Models.Entity.StockFournisseur", b =>
                {
                    b.HasOne("WebApiPfe.Models.Entity.Fournisseur", "Fournisseur")
                        .WithMany("Stocks")
                        .HasForeignKey("FournisseurId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("WebApiPfe.Models.Entity.Produit", "Produit")
                        .WithMany("StocksFournisseurs")
                        .HasForeignKey("ProduitId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Fournisseur");

                    b.Navigation("Produit");
                });

            modelBuilder.Entity("WebApiPfe.Models.Entity.TauxTVA", b =>
                {
                    b.HasOne("WebApiPfe.Models.Entity.Categorie", "Categorie")
                        .WithMany()
                        .HasForeignKey("CategorieId")
                        .OnDelete(DeleteBehavior.SetNull);

                    b.Navigation("Categorie");
                });

            modelBuilder.Entity("WebApiPfe.Models.Entity.Categorie", b =>
                {
                    b.Navigation("Formes");

                    b.Navigation("SousCategories");
                });

            modelBuilder.Entity("WebApiPfe.Models.Entity.Commande", b =>
                {
                    b.Navigation("CommandesFournisseurs");

                    b.Navigation("DetailsCommandes");

                    b.Navigation("Livraison")
                        .IsRequired();

                    b.Navigation("Paiement")
                        .IsRequired();

                    b.Navigation("PromotionsUtilisees");

                    b.Navigation("Remboursements");
                });

            modelBuilder.Entity("WebApiPfe.Models.Entity.CommandeFournisseur", b =>
                {
                    b.Navigation("LignesCommande");
                });

            modelBuilder.Entity("WebApiPfe.Models.Entity.Forme", b =>
                {
                    b.Navigation("Produits");
                });

            modelBuilder.Entity("WebApiPfe.Models.Entity.Marque", b =>
                {
                    b.Navigation("Produits");
                });

            modelBuilder.Entity("WebApiPfe.Models.Entity.Paiement", b =>
                {
                    b.Navigation("Remboursement")
                        .IsRequired();
                });

            modelBuilder.Entity("WebApiPfe.Models.Entity.Panier", b =>
                {
                    b.Navigation("Items");
                });

            modelBuilder.Entity("WebApiPfe.Models.Entity.Produit", b =>
                {
                    b.Navigation("Avis");

                    b.Navigation("DetailsCommandes");

                    b.Navigation("Favoris");

                    b.Navigation("Images");

                    b.Navigation("LignesCommandeFournisseur");

                    b.Navigation("StocksFournisseurs");
                });

            modelBuilder.Entity("WebApiPfe.Models.Entity.SousCategorie", b =>
                {
                    b.Navigation("Produits");
                });

            modelBuilder.Entity("WebApiPfe.Models.Entity.StatutLivraison", b =>
                {
                    b.Navigation("Livraisons");
                });

            modelBuilder.Entity("WebApiPfe.Models.Entity.TauxTVA", b =>
                {
                    b.Navigation("Produits");
                });

            modelBuilder.Entity("WebApiPfe.Models.Entity.Utilisateur", b =>
                {
                    b.Navigation("Adresses");

                    b.Navigation("Notifications");

                    b.Navigation("RemboursementsInities");
                });

            modelBuilder.Entity("WebApiPfe.Models.Entity.Client", b =>
                {
                    b.Navigation("Avis");

                    b.Navigation("Commandes");

                    b.Navigation("Favoris");

                    b.Navigation("Panier");
                });

            modelBuilder.Entity("WebApiPfe.Models.Entity.Fournisseur", b =>
                {
                    b.Navigation("CommandesFournisseurs");

                    b.Navigation("Produits");

                    b.Navigation("Stocks");
                });
#pragma warning restore 612, 618
        }
    }
}

import {
  environment
} from "./chunk-GFHHPDQ6.js";
import {
  HttpClient
} from "./chunk-7JDDWGD3.js";
import {
  Injectable,
  setClassMetadata,
  tap,
  ɵɵdefineInjectable,
  ɵɵinject
} from "./chunk-UBZQS7JS.js";

// src/app/services/sous-categorie.service.ts
var SousCategorieService = class _SousCategorieService {
  http;
  API_URL = `${environment.apiUrl || "https://localhost:7264/api"}/SousCategories`;
  constructor(http) {
    this.http = http;
  }
  /**
   * GET /api/SousCategories - Obtenir toutes les sous-catégories
   */
  getAll(fournisseurId) {
    console.log("\u{1F4E6} R\xE9cup\xE9ration des sous-cat\xE9gories");
    console.log("\u{1F50D} FournisseurId re\xE7u:", fournisseurId);
    let url = `${this.API_URL}/enriched`;
    if (fournisseurId) {
      url += `?fournisseurId=${fournisseurId}`;
    }
    console.log("\u{1F310} URL finale:", url);
    return this.http.get(url).pipe(tap((response) => console.log("\u2705 Sous-cat\xE9gories r\xE9cup\xE9r\xE9es:", response)));
  }
  /**
   * GET /api/SousCategories/{id} - Obtenir une sous-catégorie par ID
   */
  getById(id) {
    console.log("\u{1F50D} R\xE9cup\xE9ration de la sous-cat\xE9gorie ID:", id);
    return this.http.get(`${this.API_URL}/${id}`).pipe(tap((response) => console.log("\u2705 Sous-cat\xE9gorie r\xE9cup\xE9r\xE9e:", response)));
  }
  /**
   * POST /api/SousCategories - Créer une nouvelle sous-catégorie
   */
  create(sousCategorie) {
    console.log("\u2795 Cr\xE9ation d'une nouvelle sous-cat\xE9gorie:", sousCategorie);
    return this.http.post(this.API_URL, sousCategorie).pipe(tap((response) => console.log("\u2705 Sous-cat\xE9gorie cr\xE9\xE9e:", response)));
  }
  /**
   * PUT /api/SousCategories/{id} - Mettre à jour une sous-catégorie
   */
  update(id, sousCategorie) {
    console.log("\u270F\uFE0F Mise \xE0 jour de la sous-cat\xE9gorie ID:", id, sousCategorie);
    return this.http.put(`${this.API_URL}/${id}`, sousCategorie).pipe(tap((response) => console.log("\u2705 Sous-cat\xE9gorie mise \xE0 jour:", response)));
  }
  /**
   * DELETE /api/SousCategories/{id} - Supprimer une sous-catégorie
   */
  delete(id) {
    console.log("\u{1F5D1}\uFE0F Suppression de la sous-cat\xE9gorie ID:", id);
    return this.http.delete(`${this.API_URL}/${id}`).pipe(tap(() => console.log("\u2705 Sous-cat\xE9gorie supprim\xE9e:", id)));
  }
  /**
   * GET /api/SousCategories/by-categorie/{categorieId} - Obtenir les sous-catégories d'une catégorie
   */
  getByCategorie(categorieId) {
    console.log("\u{1F4C2} R\xE9cup\xE9ration des sous-cat\xE9gories pour la cat\xE9gorie:", categorieId);
    return this.http.get(`${this.API_URL}/by-categorie/${categorieId}`).pipe(tap((response) => console.log("\u2705 Sous-cat\xE9gories par cat\xE9gorie r\xE9cup\xE9r\xE9es:", response)));
  }
  /**
   * GET /api/SousCategories/{id}/produits-count - Obtenir le nombre de produits d'une sous-catégorie
   */
  getProduitsCount(id) {
    console.log("\u{1F522} R\xE9cup\xE9ration du nombre de produits pour la sous-cat\xE9gorie:", id);
    return this.http.get(`${this.API_URL}/${id}/produits-count`).pipe(tap((response) => console.log("\u2705 Nombre de produits r\xE9cup\xE9r\xE9:", response)));
  }
  /**
   * GET /api/SousCategories/dropdown/{categorieId} - Obtenir les sous-catégories pour dropdown
   */
  getDropdown(categorieId) {
    console.log("\u{1F4CB} R\xE9cup\xE9ration des sous-cat\xE9gories pour dropdown, cat\xE9gorie:", categorieId);
    return this.http.get(`${this.API_URL}/dropdown/${categorieId}`).pipe(tap((response) => console.log("\u2705 Dropdown sous-cat\xE9gories r\xE9cup\xE9r\xE9:", response)));
  }
  static \u0275fac = function SousCategorieService_Factory(__ngFactoryType__) {
    return new (__ngFactoryType__ || _SousCategorieService)(\u0275\u0275inject(HttpClient));
  };
  static \u0275prov = /* @__PURE__ */ \u0275\u0275defineInjectable({ token: _SousCategorieService, factory: _SousCategorieService.\u0275fac, providedIn: "root" });
};
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && setClassMetadata(SousCategorieService, [{
    type: Injectable,
    args: [{
      providedIn: "root"
    }]
  }], () => [{ type: HttpClient }], null);
})();

export {
  SousCategorieService
};
//# sourceMappingURL=chunk-VGHANLZK.js.map

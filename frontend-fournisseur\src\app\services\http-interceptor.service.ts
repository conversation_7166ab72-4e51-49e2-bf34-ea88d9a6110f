import { Injectable } from '@angular/core';
import { 
  HttpInterceptor, 
  HttpRequest, 
  HttpHandler, 
  HttpEvent, 
  HttpErrorResponse 
} from '@angular/common/http';
import { Observable, throwError } from 'rxjs';
import { catchError } from 'rxjs/operators';
import { AuthService } from './auth.service';

@Injectable()
export class HttpInterceptorService implements HttpInterceptor {

  constructor(private authService: AuthService) {}

  intercept(req: HttpRequest<any>, next: HttpHandler): Observable<HttpEvent<any>> {
    // Ajouter le token d'authentification si disponible
    const token = this.authService.getToken();
    let authReq = req;

    // Debug pour les requêtes vers l'API avis
    if (req.url.includes('/AvisModeration/')) {
      console.log('🔍 Requête AvisModeration:', {
        url: req.url,
        method: req.method,
        hasToken: !!token,
        token: token ? token.substring(0, 20) + '...' : 'Aucun',
        localStorage_token: localStorage.getItem('auth_token') ? localStorage.getItem('auth_token')!.substring(0, 20) + '...' : 'Aucun',
        currentUser: this.authService.getCurrentUser()
      });
    }

    if (token) {
      authReq = req.clone({
        headers: req.headers.set('Authorization', `Bearer ${token}`)
      });

      // Debug pour vérifier que le header est bien ajouté
      if (req.url.includes('/AvisModeration/')) {
        console.log('✅ Header Authorization ajouté:', authReq.headers.get('Authorization')?.substring(0, 30) + '...');
      }
    } else if (req.url.includes('/AvisModeration/')) {
      console.warn('⚠️ Requête AvisModeration sans token d\'authentification');
      console.warn('⚠️ localStorage auth_token:', localStorage.getItem('auth_token') ? 'Présent' : 'Absent');
      console.warn('⚠️ authService.getToken():', this.authService.getToken() ? 'Présent' : 'Absent');
    }

    // Ajouter les headers par défaut (sauf pour FormData)
    if (!(req.body instanceof FormData)) {
      authReq = authReq.clone({
        headers: authReq.headers
          .set('Content-Type', 'application/json')
          .set('Accept', 'application/json')
      });
    }

    return next.handle(authReq).pipe(
      catchError((error: HttpErrorResponse) => {
        // Debug spécial pour les erreurs d'authentification sur AvisModeration
        if (req.url.includes('/AvisModeration/') && error.status === 401) {
          console.error('❌ Erreur 401 sur AvisModeration:', {
            url: req.url,
            hasToken: !!token,
            tokenValue: token ? token.substring(0, 30) + '...' : 'Aucun',
            currentUser: this.authService.getCurrentUser(),
            isAuthenticated: this.authService.isAuthenticatedLegacy(),
            headers: Object.fromEntries(authReq.headers.keys().map(key => [key, authReq.headers.get(key)]))
          });
        }
        return this.handleError(error);
      })
    );
  }

  private handleError(error: HttpErrorResponse): Observable<never> {
    let errorMessage = 'Une erreur est survenue';

    if (error.error instanceof ErrorEvent) {
      // Erreur côté client
      errorMessage = `Erreur: ${error.error.message}`;
    } else {
      // Erreur côté serveur
      switch (error.status) {
        case 400:
          errorMessage = 'Requête invalide';
          break;
        case 401:
          errorMessage = 'Non autorisé - Veuillez vous reconnecter';
          this.authService.logout();
          break;
        case 403:
          errorMessage = 'Accès interdit';
          break;
        case 404:
          errorMessage = 'Ressource non trouvée';
          break;
        case 500:
          errorMessage = 'Erreur serveur interne';
          break;
        default:
          errorMessage = `Erreur ${error.status}: ${error.message}`;
      }

      // Essayer d'extraire le message d'erreur du serveur
      if (error.error && typeof error.error === 'object') {
        if (error.error.message) {
          errorMessage = error.error.message;
        } else if (error.error.title) {
          errorMessage = error.error.title;
        }
      } else if (typeof error.error === 'string') {
        errorMessage = error.error;
      }
    }

    console.error('Erreur HTTP:', error);
    return throwError(() => new Error(errorMessage));
  }
}

{"version": 3, "sources": ["src/app/components/products/products.component.ts", "src/app/components/products/products.component.html"], "sourcesContent": ["import { Component, OnInit, signal, computed } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { FormsModule } from '@angular/forms';\nimport { forkJoin } from 'rxjs';\nimport { ProduitService } from '../../services/produit.service';\nimport { CategorieService } from '../../services/categorie.service';\nimport { SousCategorieService } from '../../services/sous-categorie.service';\nimport { MarqueService } from '../../services/marque.service';\nimport { FormeService } from '../../services/forme.service';\nimport { TauxTVAService } from '../../services/taux-tva.service';\nimport { AuthService } from '../../services/auth.service';\nimport { ImageUrlService } from '../../services/image-url.service';\n\nimport {\n  Produit,\n  ProduitCreate,\n  ProduitUpdate,\n  CategorieDropdown,\n  SousCategorieDropdown,\n  MarqueDropdown,\n  FormeDropdown,\n  TauxTVADropdown\n} from '../../models';\n\n@Component({\n  selector: 'app-products',\n  standalone: true,\n  imports: [CommonModule, FormsModule],\n  templateUrl: './products.component.html',\n  styleUrls: ['./products.component.scss']\n})\nexport class ProductsComponent implements OnInit {\n  // Signaux pour la gestion d'état\n  products = signal<Produit[]>([]);\n  isLoading = signal(false);\n  error = signal<string | null>(null);\n  showForm = signal(false);\n  isEditMode = signal(false);\n  selectedProduct = signal<Produit | null>(null);\n  searchQuery = signal('');\n\n  // Dropdowns\n  categoriesDropdown = signal<CategorieDropdown[]>([]);\n  sousCategoriesDropdown = signal<SousCategorieDropdown[]>([]);\n  marquesDropdown = signal<MarqueDropdown[]>([]);\n  formesDropdown = signal<FormeDropdown[]>([]);\n  tauxTVADropdown = signal<TauxTVADropdown[]>([]);\n\n  // Gestion des images\n  selectedImageFiles: File[] = [];\n  imagePreviewUrls: string[] = [];\n\n  // Filtres\n  filters = {\n    stockFaible: false,\n    enPromotion: false,\n    misEnAvant: false\n  };\n\n  // Valeurs du formulaire pour ngModel\n  formData = {\n    referenceOriginal: '',\n    referenceFournisseur: '',\n    codeABarre: '',\n    nom: '',\n    description: '',\n    prixAchat: '',\n    prixVente: '',\n    stock: '',\n    pourcentageRemise: '0',\n    categorieId: '',\n    sousCategorieId: '',\n    marqueId: '',\n    formeId: '',\n    tauxTVAId: ''\n  };\n\n  // Calcul des prix\n  calculatedPrixTTC: number = 0;\n  selectedTauxTVA: number = 0;\n\n  // Pagination\n  currentPage = signal(1);\n  itemsPerPage = 12;\n\n  // Affichage des détails\n  showDetails = signal(false);\n  selectedProductDetails = signal<Produit | null>(null);\n  loadingDetails = signal(false);\n\n  constructor(\n    private produitService: ProduitService,\n    private categorieService: CategorieService,\n    private sousCategorieService: SousCategorieService,\n    private marqueService: MarqueService,\n    private formeService: FormeService,\n    private tauxTVAService: TauxTVAService,\n    private authService: AuthService,\n    public imageUrlService: ImageUrlService\n  ) {}\n\n  ngOnInit(): void {\n    console.log('🚀 Initialisation du composant ProductsComponent');\n    this.testBackendConnection();\n    this.loadData();\n    this.loadDropdowns();\n  }\n\n  testBackendConnection(): void {\n    console.log('🔗 Test de connexion au backend...');\n\n    // Test simple avec les catégories\n    this.categorieService.getDropdown().subscribe({\n      next: (response) => {\n        console.log('✅ Backend accessible - Catégories:', response);\n      },\n      error: (error) => {\n        console.error('❌ Backend inaccessible:', error);\n        console.error('❌ Status:', error.status);\n        console.error('❌ Message:', error.message);\n        console.error('❌ URL:', error.url);\n      }\n    });\n  }\n\n  // Computed properties\n  stats = computed(() => {\n    const products = this.products();\n    return {\n      total: products.length,\n      active: products.filter(p => p.stock > 0).length,\n      outOfStock: products.filter(p => p.stock === 0).length,\n      totalValue: products.reduce((sum, p) => {\n        // Utiliser le prix avec promotions si disponible, sinon le prix TTC normal\n        const prixFinal = p.prixApresRemises || p.prixVenteTTC;\n        return sum + (prixFinal * p.stock);\n      }, 0)\n    };\n  });\n\n  filteredProducts = computed(() => {\n    const products = this.products();\n    const query = this.searchQuery().toLowerCase();\n    \n    if (!query) return products;\n    \n    return products.filter(product =>\n      product.nom.toLowerCase().includes(query) ||\n      product.description?.toLowerCase().includes(query) ||\n      product.referenceOriginal.toLowerCase().includes(query)\n    );\n  });\n\n  currentPageProducts = computed(() => {\n    const filtered = this.filteredProducts();\n    const start = (this.currentPage() - 1) * this.itemsPerPage;\n    const end = start + this.itemsPerPage;\n    return filtered.slice(start, end);\n  });\n\n  // Méthodes de chargement des données\n  loadData(): void {\n    console.log('🔄 ProductsComponent: Début du chargement des données...');\n    this.isLoading.set(true);\n    this.error.set(null);\n\n    // Debug: Vérifier l'état d'authentification\n    const isAuth = this.authService.isAuthenticated();\n    const currentUser = this.authService.getCurrentUser();\n    const token = localStorage.getItem('auth_token') || localStorage.getItem('token');\n\n    console.log('🔍 ProductsComponent: État d\\'authentification:');\n    console.log('  - isAuthenticated:', isAuth);\n    console.log('  - currentUser:', currentUser);\n    console.log('  - token présent:', !!token);\n    console.log('  - localStorage keys:', Object.keys(localStorage));\n\n    if (!currentUser?.id) {\n      console.error('❌ ProductsComponent: Utilisateur non connecté ou ID manquant');\n      console.log('  - currentUser:', currentUser);\n      this.error.set('Utilisateur non connecté. Veuillez vous reconnecter.');\n      this.isLoading.set(false);\n      return;\n    }\n\n    console.log(`🔄 ProductsComponent: Chargement des produits pour le fournisseur ID: ${currentUser.id}`);\n\n    // Charger seulement les produits du fournisseur connecté\n    this.produitService.getByFournisseur(currentUser.id).subscribe({\n      next: (products) => {\n        console.log(`✅ ProductsComponent: ${products.length} produits du fournisseur chargés avec images:`, products);\n\n        // Vérifier si les produits ont des images\n        products.forEach(product => {\n          console.log(`🖼️ Produit ${product.nom}:`, {\n            id: product.id,\n            images: product.images,\n            imagePrincipaleUrl: product.imagePrincipaleUrl,\n            imagesCount: product.images?.length || 0\n          });\n        });\n\n        // Utiliser directement les produits de getByFournisseur qui incluent déjà les images\n        this.products.set(products);\n        this.isLoading.set(false);\n      },\n      error: (error) => {\n        console.error('❌ ProductsComponent: Erreur lors du chargement des produits:', error);\n        console.log('  - Status:', error.status);\n        console.log('  - Message:', error.message);\n        console.log('  - URL:', error.url);\n\n        // Message d'erreur plus détaillé\n        let errorMessage = 'Erreur lors du chargement des produits';\n        if (error.status === 401) {\n          errorMessage = 'Non autorisé. Veuillez vous reconnecter.';\n        } else if (error.status === 404) {\n          errorMessage = 'Endpoint non trouvé. Vérifiez le backend.';\n        } else if (error.status === 0) {\n          errorMessage = 'Impossible de contacter le serveur.';\n        }\n\n        this.error.set(errorMessage);\n        this.isLoading.set(false);\n      }\n    });\n  }\n\n  loadDropdowns(): void {\n    console.log('🔄 Début du chargement des dropdowns...');\n\n    // Charger les catégories\n    console.log('📋 Chargement des catégories...');\n    this.categorieService.getAll().subscribe({\n      next: (categories) => {\n        console.log('✅ Catégories reçues:', categories);\n        // Le backend retourne directement les objets avec les bonnes propriétés\n        const categoriesFormatted = categories.map((cat: any) => ({\n          id: cat.id,\n          nom: cat.nom,\n          sousCategoriesCount: cat.sousCategoriesCount || 0\n        }));\n        console.log('✅ Catégories formatées:', categoriesFormatted);\n        this.categoriesDropdown.set(categoriesFormatted);\n      },\n      error: (error) => {\n        console.error('❌ Erreur lors du chargement des catégories:', error);\n        this.categoriesDropdown.set([]);\n      }\n    });\n\n    // Charger les marques\n    console.log('📋 Chargement des marques...');\n    this.marqueService.getAll().subscribe({\n      next: (marques) => {\n        console.log('✅ Marques reçues:', marques);\n        // Le backend retourne des objets avec 'name' et 'logo'\n        const marquesFormatted = marques.map((marque: any) => ({\n          id: marque.id,\n          name: marque.name,\n          logo: marque.logo || ''\n        }));\n        console.log('✅ Marques formatées:', marquesFormatted);\n        this.marquesDropdown.set(marquesFormatted);\n      },\n      error: (error) => {\n        console.error('❌ Erreur lors du chargement des marques:', error);\n        this.marquesDropdown.set([]);\n      }\n    });\n\n    // Charger les formes\n    console.log('📋 Chargement des formes...');\n    this.formeService.getAll().subscribe({\n      next: (formes) => {\n        console.log('✅ Formes reçues:', formes);\n        // Le backend retourne des objets avec 'nom', 'categorieId' et 'imageUrl'\n        const formesFormatted = formes.map((forme: any) => ({\n          id: forme.id,\n          nom: forme.nom,\n          categorieId: forme.categorieId,\n          imageUrl: forme.imageUrl || ''\n        }));\n        console.log('✅ Formes formatées:', formesFormatted);\n        this.formesDropdown.set(formesFormatted);\n      },\n      error: (error) => {\n        console.error('❌ Erreur lors du chargement des formes:', error);\n        this.formesDropdown.set([]);\n      }\n    });\n\n    // Charger les taux TVA\n    console.log('📋 Chargement des taux TVA...');\n    this.tauxTVAService.getAll().subscribe({\n      next: (taux) => {\n        console.log('✅ Taux TVA reçus:', taux);\n        // Le backend retourne des objets avec 'libelle', 'taux' et 'estActif'\n        const tauxFormatted = taux.map((tva: any) => ({\n          id: tva.id,\n          libelle: tva.libelle,\n          taux: tva.taux,\n          estActif: tva.estActif\n        }));\n        console.log('✅ Taux TVA formatés:', tauxFormatted);\n        this.tauxTVADropdown.set(tauxFormatted);\n      },\n      error: (error) => {\n        console.error('❌ Erreur lors du chargement des taux TVA:', error);\n        this.tauxTVADropdown.set([]);\n      }\n    });\n\n    console.log('🔄 Fin de l\\'initialisation du chargement des dropdowns');\n  }\n\n  // Méthodes d'interface utilisateur\n  onSearch(event: any): void {\n    this.searchQuery.set(event.target.value);\n    this.currentPage.set(1);\n  }\n\n  openAddForm(): void {\n    this.isEditMode.set(false);\n    this.selectedProduct.set(null);\n    this.showForm.set(true);\n    this.clearImageSelection();\n    this.resetFormData();\n\n    // Forcer le rechargement des dropdowns si ils sont vides\n    if (this.categoriesDropdown().length === 0) {\n      console.log('⚠️ Dropdowns vides, rechargement forcé...');\n      this.loadDropdowns();\n    }\n\n    // Afficher l'état des dropdowns\n    console.log('📊 État des dropdowns à l\\'ouverture du formulaire:');\n    console.log('- Catégories:', this.categoriesDropdown());\n    console.log('- Marques:', this.marquesDropdown());\n    console.log('- Formes:', this.formesDropdown());\n    console.log('- Taux TVA:', this.tauxTVADropdown());\n  }\n\n  resetFormData(): void {\n    this.formData = {\n      referenceOriginal: '',\n      referenceFournisseur: this.generateUniqueReference(),\n      codeABarre: '',\n      nom: '',\n      description: '',\n      prixAchat: '',\n      prixVente: '',\n      stock: '',\n      pourcentageRemise: '30', // Valeur par défaut de 30% pour la remise outlet\n      categorieId: '',\n      sousCategorieId: '',\n      marqueId: '',\n      formeId: '',\n      tauxTVAId: ''\n    };\n    console.log('🔄 Formulaire réinitialisé avec remise par défaut de 30%');\n  }\n\n  generateUniqueReference(): string {\n    const timestamp = Date.now();\n    const random = Math.floor(Math.random() * 1000);\n    return `FOUR-${timestamp}-${random}`;\n  }\n\n  editProduct(product: Produit): void {\n    console.log('🔧 Début de l\\'édition du produit:', product);\n    console.log('🔧 ID du produit:', product.id);\n    console.log('🔧 Nom du produit:', product.nom);\n\n    // Définir le mode édition\n    this.isEditMode.set(true);\n    console.log('✅ Mode édition activé:', this.isEditMode());\n\n    // Afficher le formulaire\n    this.showForm.set(true);\n    console.log('✅ Formulaire affiché:', this.showForm());\n\n    // Forcer le rechargement des dropdowns si nécessaire\n    if (this.categoriesDropdown().length === 0) {\n      console.log('⚠️ Dropdowns vides, rechargement...');\n      this.loadDropdowns();\n    }\n\n    // Charger les détails complets du produit depuis l'API\n    console.log('🔄 Chargement des détails complets du produit depuis l\\'API...');\n    this.produitService.getById(product.id).subscribe({\n      next: (productDetails) => {\n        console.log('✅ Détails complets du produit reçus:', productDetails);\n\n        // Sélectionner le produit avec tous les détails\n        this.selectedProduct.set(productDetails);\n        console.log('✅ Produit complet sélectionné:', this.selectedProduct());\n\n        // Charger les données dans le formulaire\n        setTimeout(() => {\n          console.log('📝 Chargement des données complètes dans le formulaire...');\n          this.loadProductDataIntoForm(productDetails);\n        }, 200);\n      },\n      error: (error) => {\n        console.error('❌ Erreur lors du chargement des détails du produit:', error);\n        // En cas d'erreur, utiliser les données de base\n        this.selectedProduct.set(product);\n        setTimeout(() => {\n          this.loadProductDataIntoForm(product);\n        }, 200);\n      }\n    });\n  }\n\n  loadProductDataIntoForm(product: Produit): void {\n    console.log('📝 Début du chargement des données du produit dans le formulaire:', product);\n    console.log('📝 Données du produit à charger:');\n    console.log('- ID:', product.id);\n    console.log('- Nom:', product.nom);\n    console.log('- Prix achat HT:', product.prixAchatHT);\n    console.log('- Prix vente HT:', product.prixVenteHT);\n    console.log('- Stock:', product.stock);\n    console.log('- Marque ID:', product.marqueId);\n    console.log('- Forme ID:', product.formeId);\n    console.log('- Taux TVA ID:', product.tauxTVAId);\n    console.log('- Sous-catégorie ID:', product.sousCategorieId);\n\n    // Attendre que le formulaire soit rendu\n    setTimeout(() => {\n      console.log('📝 Remplissage des champs du formulaire avec formData...');\n\n      // Pré-remplir les champs de base avec formData\n      console.log('📝 Remplissage des champs de base...');\n      this.formData.referenceOriginal = product.referenceOriginal;\n      console.log('- Référence originale:', this.formData.referenceOriginal);\n\n      this.formData.referenceFournisseur = product.referenceFournisseur || '';\n      console.log('- Référence fournisseur:', this.formData.referenceFournisseur);\n\n      this.formData.codeABarre = product.codeABarre;\n      console.log('- Code à barres:', this.formData.codeABarre);\n\n      this.formData.nom = product.nom;\n      console.log('- Nom:', this.formData.nom);\n\n      this.formData.description = product.description || '';\n      console.log('- Description:', this.formData.description);\n\n      this.formData.prixAchat = product.prixAchatHT.toString();\n      console.log('- Prix achat:', this.formData.prixAchat);\n\n      this.formData.prixVente = product.prixVenteHT.toString();\n      console.log('- Prix vente:', this.formData.prixVente);\n\n      this.formData.stock = product.stock.toString();\n      console.log('- Stock:', this.formData.stock);\n\n      this.formData.pourcentageRemise = product.pourcentageRemiseTotale?.toString() || '0';\n      console.log('- Pourcentage remise:', this.formData.pourcentageRemise);\n\n      // Pré-sélectionner les dropdowns avec formData\n      console.log('📝 Remplissage des dropdowns...');\n      this.formData.marqueId = product.marqueId.toString();\n      console.log('- Marque ID:', this.formData.marqueId);\n\n      this.formData.formeId = product.formeId.toString();\n      console.log('- Forme ID:', this.formData.formeId);\n\n      this.formData.tauxTVAId = product.tauxTVAId.toString();\n      console.log('- Taux TVA ID:', this.formData.tauxTVAId);\n\n      // Charger la catégorie et sous-catégorie\n      console.log('📋 Chargement des catégories et sous-catégories...');\n      console.log('- Sous-catégorie du produit:', product.sousCategorie);\n      console.log('- ID sous-catégorie:', product.sousCategorieId);\n\n      if (product.sousCategorie?.categorieId || product.sousCategorieId) {\n        // Utiliser l'ID de catégorie de la sous-catégorie ou chercher dans les données\n        const categorieId = product.sousCategorie?.categorieId;\n        if (categorieId) {\n          this.formData.categorieId = categorieId.toString();\n          console.log('✅ Catégorie sélectionnée:', this.formData.categorieId);\n\n          // Charger les sous-catégories pour cette catégorie\n          this.loadSousCategories(categorieId);\n\n          // Attendre que les sous-catégories soient chargées\n          setTimeout(() => {\n            this.formData.sousCategorieId = product.sousCategorieId.toString();\n            console.log('✅ Sous-catégorie sélectionnée:', this.formData.sousCategorieId);\n          }, 300);\n        } else {\n          // Si pas de catégorie dans sousCategorie, essayer de la trouver\n          console.log('⚠️ Pas de categorieId dans sousCategorie, recherche...');\n          this.formData.sousCategorieId = product.sousCategorieId.toString();\n        }\n      } else {\n        console.log('⚠️ Aucune sous-catégorie trouvée pour ce produit');\n      }\n\n      // Mettre à jour le calcul du prix TTC\n      this.selectedTauxTVA = this.tauxTVADropdown().find(t => t.id === product.tauxTVAId)?.taux || 0;\n      this.calculatePrixTTC();\n\n      console.log('✅ Données du produit chargées dans le formulaire');\n      console.log('📊 État final de formData:', this.formData);\n    }, 200);\n  }\n\n\n\n  private loadSousCategories(categorieId: number): void {\n    this.sousCategorieService.getByCategorie(categorieId).subscribe({\n      next: (sousCategories) => {\n        console.log('✅ Sous-catégories chargées pour édition:', sousCategories);\n        const sousCategoriesArray = Array.isArray(sousCategories) ? sousCategories : [];\n        this.sousCategoriesDropdown.set(sousCategoriesArray);\n      },\n      error: (error) => {\n        console.error('❌ Erreur lors du chargement des sous-catégories pour édition:', error);\n        this.sousCategoriesDropdown.set([]);\n      }\n    });\n  }\n\n  closeForm(): void {\n    this.showForm.set(false);\n    this.isEditMode.set(false);\n    this.selectedProduct.set(null);\n    this.clearImageSelection();\n    this.resetFormData();\n  }\n\n  viewProductDetails(product: Produit): void {\n    console.log('👁️ Chargement des détails complets du produit:', product.id);\n\n    this.loadingDetails.set(true);\n\n    // Charger les détails complets du produit avec toutes les relations\n    this.produitService.getById(product.id).subscribe({\n      next: (productDetails) => {\n        console.log('✅ Détails complets du produit chargés:', productDetails);\n        console.log('📊 Relations chargées:');\n        console.log('- Marque:', productDetails.marque);\n        console.log('- Forme:', productDetails.forme);\n        console.log('- Sous-catégorie:', productDetails.sousCategorie);\n        console.log('- Taux TVA:', productDetails.tauxTVA);\n\n        this.selectedProductDetails.set(productDetails);\n        this.showDetails.set(true);\n        this.loadingDetails.set(false);\n      },\n      error: (error) => {\n        console.error('❌ Erreur lors du chargement des détails du produit:', error);\n        // En cas d'erreur, utiliser les données de base\n        this.selectedProductDetails.set(product);\n        this.showDetails.set(true);\n        this.loadingDetails.set(false);\n      }\n    });\n  }\n\n  closeDetails(): void {\n    this.showDetails.set(false);\n    this.selectedProductDetails.set(null);\n  }\n\n  deleteProduct(product: Produit): void {\n    if (confirm(`Êtes-vous sûr de vouloir supprimer \"${product.nom}\" ?`)) {\n      this.produitService.delete(product.id).subscribe({\n        next: () => {\n          this.loadData();\n          alert('Produit supprimé avec succès');\n        },\n        error: () => alert('Erreur lors de la suppression')\n      });\n    }\n  }\n\n  // Méthodes utilitaires\n  formatPrice(price: number): string {\n    return new Intl.NumberFormat('fr-TN', {\n      style: 'currency',\n      currency: 'TND',\n      minimumFractionDigits: 2,\n      maximumFractionDigits: 2\n    }).format(price).replace('TND', 'DT');\n  }\n\n  getFinalPrice(product: Produit): number {\n    // Priorité : prixApresRemises > prixApresAutresPromotions > prixApresRemisesOutlet > prixVenteTTC\n    let finalPrice = product.prixVenteTTC;\n\n    if (product.prixApresRemises && product.prixApresRemises > 0) {\n      finalPrice = product.prixApresRemises;\n    } else if (product.prixApresAutresPromotions && product.prixApresAutresPromotions > 0) {\n      finalPrice = product.prixApresAutresPromotions;\n    } else if (product.prixApresRemisesOutlet && product.prixApresRemisesOutlet > 0) {\n      finalPrice = product.prixApresRemisesOutlet;\n    }\n\n    return finalPrice;\n  }\n\n  hasPromotion(product: Produit): boolean {\n    // Il y a une promotion si un des prix avec remise est différent du prix TTC original\n    return !!(\n      (product.prixApresRemises && product.prixApresRemises > 0 && product.prixApresRemises < product.prixVenteTTC) ||\n      (product.prixApresAutresPromotions && product.prixApresAutresPromotions > 0 && product.prixApresAutresPromotions < product.prixVenteTTC) ||\n      (product.prixApresRemisesOutlet && product.prixApresRemisesOutlet > 0 && product.prixApresRemisesOutlet < product.prixVenteTTC)\n    );\n  }\n\n  getStatusClass(product: Produit): string {\n    if (product.stock === 0) return 'status-out-of-stock';\n    if (product.stock <= 5) return 'status-low-stock';\n    return 'status-in-stock';\n  }\n\n  getStatusText(product: Produit): string {\n    if (product.stock === 0) return 'Rupture';\n    if (product.stock <= 5) return 'Stock faible';\n    return 'En stock';\n  }\n\n  // Méthodes pour les images\n  clearImageSelection(): void {\n    this.selectedImageFiles = [];\n    this.imagePreviewUrls = [];\n  }\n\n  onImageFilesSelected(event: Event): void {\n    const input = event.target as HTMLInputElement;\n    if (input.files && input.files.length > 0) {\n      this.selectedImageFiles = Array.from(input.files);\n      this.generateImagePreviews();\n    }\n  }\n\n  onSingleImageSelected(event: Event): void {\n    const input = event.target as HTMLInputElement;\n    const file = input.files?.[0];\n\n    if (file) {\n      console.log('📷 Nouvelle image sélectionnée:', file.name);\n\n      // Vérifier la limite de 5 images\n      if (this.selectedImageFiles.length >= 5) {\n        alert('Vous ne pouvez pas ajouter plus de 5 images.');\n        return;\n      }\n\n      // Ajouter la nouvelle image à la liste\n      this.selectedImageFiles.push(file);\n\n      // Générer la prévisualisation pour cette nouvelle image\n      const reader = new FileReader();\n      reader.onload = (e) => {\n        if (e.target?.result) {\n          this.imagePreviewUrls.push(e.target.result as string);\n        }\n      };\n      reader.readAsDataURL(file);\n\n      console.log('✅ Image ajoutée. Total:', this.selectedImageFiles.length);\n\n      // Réinitialiser l'input file pour permettre de sélectionner la même image à nouveau\n      input.value = '';\n    }\n  }\n\n  generateImagePreviews(): void {\n    this.imagePreviewUrls = [];\n    this.selectedImageFiles.forEach(file => {\n      const reader = new FileReader();\n      reader.onload = (e) => {\n        if (e.target?.result) {\n          this.imagePreviewUrls.push(e.target.result as string);\n        }\n      };\n      reader.readAsDataURL(file);\n    });\n  }\n\n  getImagePreview(file: File): string {\n    const index = this.selectedImageFiles.indexOf(file);\n    return this.imagePreviewUrls[index] || '';\n  }\n\n  removeImageFile(index: number): void {\n    this.selectedImageFiles.splice(index, 1);\n    this.imagePreviewUrls.splice(index, 1);\n  }\n\n  onImageError(event: Event): void {\n    const img = event.target as HTMLImageElement;\n    if (img && !img.src.includes('data:image')) {\n      console.log('❌ Erreur de chargement d\\'image:', img.src);\n      img.src = this.imageUrlService.getPlaceholderUrl();\n    }\n  }\n\n  getMainProductImage(product: Produit): string {\n    console.log('🖼️ getMainProductImage appelé pour produit:', {\n      id: product.id,\n      nom: product.nom,\n      images: product.images,\n      imagePrincipaleUrl: product.imagePrincipaleUrl,\n      imagesLength: product.images?.length\n    });\n\n    // Essayer d'abord imagePrincipaleUrl\n    if (product.imagePrincipaleUrl) {\n      console.log('🖼️ Utilisation de imagePrincipaleUrl:', product.imagePrincipaleUrl);\n      const url = this.imageUrlService.getProduitImageUrl(product.imagePrincipaleUrl);\n      console.log('🖼️ URL finale construite:', url);\n      return url;\n    }\n\n    // Ensuite essayer le tableau images\n    if (product.images?.length > 0) {\n      console.log('🖼️ Utilisation du tableau images:', product.images);\n      // Chercher l'image principale\n      const mainImage = product.images.find(img => img.isMain);\n      const imageUrl = mainImage?.imageUrl || product.images[0].imageUrl;\n      console.log('🖼️ Image sélectionnée:', { mainImage, imageUrl });\n\n      if (imageUrl) {\n        const url = this.imageUrlService.getProduitImageUrl(imageUrl);\n        console.log('🖼️ URL finale construite:', url);\n        return url;\n      }\n    }\n\n    // Si aucune image n'est trouvée, utiliser le placeholder\n    console.log('🖼️ Aucune image trouvée, utilisation du placeholder');\n    return this.imageUrlService.getPlaceholderUrl();\n  }\n\n  // Méthodes pour le calcul des prix\n  onPrixChange(): void {\n    this.calculatePrixTTC();\n  }\n\n  onTauxTVAChange(event: any): void {\n    const tauxTVAId = +event.target.value;\n    const selectedTaux = this.tauxTVADropdown().find(t => t.id === tauxTVAId);\n    this.selectedTauxTVA = selectedTaux?.taux || 0;\n    this.calculatePrixTTC();\n  }\n\n  calculatePrixTTC(): void {\n    const prixVenteHTInput = document.querySelector('input[name=\"prixVente\"]') as HTMLInputElement;\n    const prixVenteHT = prixVenteHTInput ? +prixVenteHTInput.value : 0;\n\n    if (prixVenteHT > 0 && this.selectedTauxTVA > 0) {\n      this.calculatedPrixTTC = prixVenteHT * (1 + this.selectedTauxTVA / 100);\n    } else {\n      this.calculatedPrixTTC = 0;\n    }\n  }\n\n  // Méthodes pour les statistiques\n  getTotalStock(): number {\n    return this.products().reduce((total, product) => total + (product.stock || 0), 0);\n  }\n\n  getTotalValue(): number {\n    return this.products().reduce((total, product) => {\n      const stock = product.stock || 0;\n      const prix = product.prixVenteHT || 0;\n      return total + (stock * prix);\n    }, 0);\n  }\n\n  getLowStockCount(): number {\n    const seuilStock = 10; // Seuil de stock faible\n    return this.products().filter(product => (product.stock || 0) <= seuilStock).length;\n  }\n\n  getStockLevelClass(stock: number): string {\n    if (stock <= 0) return 'stock-empty';\n    if (stock <= 10) return 'stock-low';\n    if (stock <= 50) return 'stock-medium';\n    return 'stock-ok';\n  }\n\n  // TrackBy functions pour optimiser les performances\n  trackBySousCategorieId(index: number, item: any): number {\n    return item.id;\n  }\n\n  onCategorieChange(event: any): void {\n    const categorieId = +event.target.value;\n    console.log('🔄 Changement de catégorie:', categorieId);\n\n    if (categorieId) {\n      // Charger les sous-catégories\n      console.log('📂 Récupération des sous-catégories pour la catégorie:', categorieId);\n      this.sousCategorieService.getByCategorie(categorieId).subscribe({\n        next: (sousCategories) => {\n          console.log('✅ Sous-catégories reçues:', sousCategories);\n          const sousCategoriesArray = Array.isArray(sousCategories) ? sousCategories : [];\n          console.log('📋 Sous-catégories array:', sousCategoriesArray);\n          this.sousCategoriesDropdown.set(sousCategoriesArray);\n          console.log('🎯 Signal mis à jour, valeur actuelle:', this.sousCategoriesDropdown());\n        },\n        error: (error) => {\n          console.error('❌ Erreur sous-catégories:', error);\n          this.sousCategoriesDropdown.set([]);\n        }\n      });\n\n      // Pour l'instant, charger toutes les formes et taux TVA\n      // TODO: Implémenter getByCategorie dans les services si nécessaire\n      this.formeService.getDropdown().subscribe({\n        next: (formes) => this.formesDropdown.set(formes),\n        error: (error) => console.error('❌ Erreur formes:', error)\n      });\n\n      this.tauxTVAService.getDropdown().subscribe({\n        next: (taux) => this.tauxTVADropdown.set(taux),\n        error: (error) => console.error('❌ Erreur taux TVA:', error)\n      });\n    } else {\n      // Réinitialiser les dropdowns\n      this.sousCategoriesDropdown.set([]);\n      this.formesDropdown.set([]);\n      this.tauxTVADropdown.set([]);\n    }\n  }\n\n  refreshDropdowns(): void {\n    console.log('🔄 Actualisation manuelle des dropdowns...');\n\n    // Réinitialiser tous les dropdowns\n    this.categoriesDropdown.set([]);\n    this.sousCategoriesDropdown.set([]);\n    this.marquesDropdown.set([]);\n    this.formesDropdown.set([]);\n    this.tauxTVADropdown.set([]);\n\n    this.loadDropdowns();\n\n    setTimeout(() => {\n      console.log('📊 État des dropdowns après rechargement:');\n      console.log('- Catégories:', this.categoriesDropdown());\n      console.log('- Marques:', this.marquesDropdown());\n      console.log('- Formes:', this.formesDropdown());\n      console.log('- Taux TVA:', this.tauxTVADropdown());\n    }, 2000);\n  }\n\n  openQuickCreateModal(type: string): void {\n    console.log('➕ Ouverture du modal de création rapide pour:', type);\n    alert(`Fonctionnalité de création rapide pour ${type} à implémenter`);\n  }\n\n  onSubmit(form: any): void {\n    if (!form.valid) {\n      alert('Veuillez remplir tous les champs obligatoires');\n      return;\n    }\n\n    const formData = form.value;\n    const currentUser = this.authService.getCurrentUser();\n\n    if (!currentUser?.id) {\n      alert('Utilisateur non connecté');\n      return;\n    }\n\n    if (this.isEditMode() && this.selectedProduct()) {\n      // Mode édition\n      const update: ProduitUpdate = {\n        id: this.selectedProduct()!.id,\n        nom: formData.nom,\n        description: formData.description || '',\n        prixVenteHT: +formData.prixVente || undefined,\n        stock: +formData.stock || undefined,\n        pourcentageRemise: +formData.pourcentageRemise || undefined,\n        imageFiles: this.selectedImageFiles.length > 0 ? this.selectedImageFiles : undefined\n      };\n\n      this.produitService.update(this.selectedProduct()!.id, update).subscribe({\n        next: () => {\n          this.loadData();\n          this.closeForm();\n          alert('Produit mis à jour avec succès');\n        },\n        error: (error) => {\n          console.error('❌ Erreur lors de la mise à jour:', error);\n          alert('Erreur lors de la mise à jour du produit');\n        }\n      });\n    } else {\n      // Mode création\n      if (!formData.referenceOriginal || !formData.nom || !formData.description ||\n          !formData.prixAchat || !formData.prixVente || !formData.sousCategorieId ||\n          !formData.marqueId || !formData.formeId || !formData.tauxTVAId) {\n        alert('Veuillez remplir tous les champs obligatoires');\n        return;\n      }\n\n      const newProduct: ProduitCreate = {\n        nom: formData.nom,\n        description: formData.description || '',\n        referenceOriginal: formData.referenceOriginal,\n        referenceFournisseur: formData.referenceFournisseur || undefined,\n        codeABarre: formData.codeABarre || `${Date.now()}${Math.floor(Math.random() * 1000)}`,\n        prixAchatHT: +formData.prixAchat || 0,\n        prixVenteHT: +formData.prixVente || 0,\n        stock: +formData.stock || 0,\n        fournisseurId: currentUser.id,\n        sousCategorieId: +formData.sousCategorieId,\n        marqueId: +formData.marqueId,\n        formeId: +formData.formeId,\n        tauxTVAId: +formData.tauxTVAId,\n        pourcentageRemise: +formData.pourcentageRemise || undefined,\n        imageFiles: this.selectedImageFiles.length > 0 ? this.selectedImageFiles : undefined\n      };\n\n      this.produitService.create(newProduct).subscribe({\n        next: () => {\n          this.loadData();\n          this.closeForm();\n          alert('Produit créé avec succès');\n        },\n        error: (error) => {\n          console.error('❌ Erreur lors de la création:', error);\n          alert('Erreur lors de la création du produit');\n        }\n      });\n    }\n  }\n}\n", "<div class=\"products-container\">\n  <!-- Header -->\n  <div class=\"products-header\">\n    <h1>🛍️ Gestion des Produits</h1>\n    <div>\n      <button class=\"btn btn-secondary\" (click)=\"loadData()\" style=\"margin-right: 10px;\">\n        🔄 Recharger\n      </button>\n      <button class=\"btn btn-primary\" (click)=\"openAddForm()\">\n        ➕ Ajouter un produit\n      </button>\n    </div>\n  </div>\n\n  <!-- Statistiques -->\n  <div class=\"stats-grid\">\n    <div class=\"stat-card\">\n      <div class=\"stat-number\">{{ stats().total }}</div>\n      <div class=\"stat-label\">Total Produits</div>\n    </div>\n    <div class=\"stat-card\">\n      <div class=\"stat-number\">{{ stats().active }}</div>\n      <div class=\"stat-label\">En Stock</div>\n    </div>\n    <div class=\"stat-card\">\n      <div class=\"stat-number\">{{ stats().outOfStock }}</div>\n      <div class=\"stat-label\">Rupture</div>\n    </div>\n    <div class=\"stat-card\">\n      <div class=\"stat-number\">{{ formatPrice(stats().totalValue) }}</div>\n      <div class=\"stat-label\">Valeur Totale</div>\n    </div>\n  </div>\n\n  <!-- Recherche -->\n  <div class=\"search-box\">\n    <input \n      type=\"text\" \n      placeholder=\"Rechercher...\" \n      [value]=\"searchQuery()\"\n      (input)=\"onSearch($event)\"\n      class=\"search-input\"\n    />\n  </div>\n\n  <!-- Message d'erreur -->\n  <div *ngIf=\"error()\" class=\"error-message\" style=\"background: #fee; border: 1px solid #fcc; padding: 15px; margin: 20px 0; border-radius: 5px; color: #c00;\">\n    <h3>❌ Erreur</h3>\n    <p>{{ error() }}</p>\n    <button class=\"btn btn-primary\" (click)=\"loadData()\">🔄 Réessayer</button>\n  </div>\n\n  <!-- En-tête avec statistiques -->\n  <div class=\"page-header\" *ngIf=\"!isLoading() && !error()\">\n    <div class=\"header-content\">\n      <h1>📦 Gestion des Produits</h1>\n      <p>Gérez votre catalogue de produits et suivez vos stocks</p>\n    </div>\n\n    <div class=\"header-actions\">\n      <button class=\"btn btn-primary\" (click)=\"openAddForm()\">\n        ➕ Nouveau Produit\n      </button>\n    </div>\n  </div>\n\n  <!-- Statistiques -->\n  <div class=\"stats-section\" *ngIf=\"products().length > 0 && !isLoading() && !error()\">\n    <div class=\"stats-grid\">\n      <div class=\"stat-card\">\n        <div class=\"stat-icon\">📦</div>\n        <div class=\"stat-content\">\n          <div class=\"stat-value\">{{ products().length }}</div>\n          <div class=\"stat-label\">Produits</div>\n        </div>\n      </div>\n\n      <div class=\"stat-card\">\n        <div class=\"stat-icon\">📊</div>\n        <div class=\"stat-content\">\n          <div class=\"stat-value\">{{ getTotalStock() }}</div>\n          <div class=\"stat-label\">Unités en stock</div>\n        </div>\n      </div>\n\n      <div class=\"stat-card\">\n        <div class=\"stat-icon\">💰</div>\n        <div class=\"stat-content\">\n          <div class=\"stat-value\">{{ getTotalValue() | number:'1.0-0' }} DT</div>\n          <div class=\"stat-label\">Valeur du stock</div>\n        </div>\n      </div>\n\n      <div class=\"stat-card alert\" *ngIf=\"getLowStockCount() > 0\">\n        <div class=\"stat-icon\">⚠️</div>\n        <div class=\"stat-content\">\n          <div class=\"stat-value\">{{ getLowStockCount() }}</div>\n          <div class=\"stat-label\">Stock faible</div>\n        </div>\n      </div>\n    </div>\n  </div>\n\n  <!-- Barre d'outils -->\n  <div class=\"toolbar\" *ngIf=\"!isLoading() && !error()\">\n    <div class=\"search-section\">\n      <div class=\"search-box\">\n        <input\n          type=\"text\"\n          [(ngModel)]=\"searchQuery\"\n          placeholder=\"Rechercher un produit...\"\n          class=\"form-control\"\n        />\n        <span class=\"search-icon\">🔍</span>\n      </div>\n\n      <div class=\"filters\">\n        <label class=\"filter-checkbox\">\n          <input type=\"checkbox\" [(ngModel)]=\"filters.stockFaible\" />\n          <span>Stock faible</span>\n        </label>\n        <label class=\"filter-checkbox\">\n          <input type=\"checkbox\" [(ngModel)]=\"filters.enPromotion\" />\n          <span>En promotion</span>\n        </label>\n        <label class=\"filter-checkbox\">\n          <input type=\"checkbox\" [(ngModel)]=\"filters.misEnAvant\" />\n          <span>Mis en avant</span>\n        </label>\n      </div>\n    </div>\n  </div>\n\n  <!-- Liste des produits -->\n  <div class=\"products-section\" *ngIf=\"!isLoading() && !error(); else loadingTemplate\">\n    <div class=\"table-container\" *ngIf=\"currentPageProducts().length > 0\">\n      <table class=\"products-table\">\n        <thead>\n          <tr>\n            <th>Image</th>\n            <th>Produit</th>\n            <th>Référence</th>\n            <th>Prix</th>\n            <th>Stock</th>\n            <th>Catégorie</th>\n            <th>Statut</th>\n            <th>Actions</th>\n          </tr>\n        </thead>\n        <tbody>\n          <tr *ngFor=\"let product of currentPageProducts()\" class=\"product-row\">\n            <td class=\"image-cell\">\n              <img\n                [src]=\"getMainProductImage(product)\"\n                [alt]=\"product.nom\"\n                class=\"product-image\"\n                (error)=\"onImageError($event)\">\n            </td>\n\n            <td class=\"product-info\">\n              <div class=\"product-name\">{{ product.nom }}</div>\n              <div class=\"product-meta\">\n                <span class=\"date\" *ngIf=\"product.dateAjout\">Ajouté le {{ product.dateAjout | date:'dd/MM/yyyy' }}</span>\n                <span *ngIf=\"hasPromotion(product)\" class=\"featured-badge\">🏷️ PROMO</span>\n              </div>\n              <div class=\"product-description\" *ngIf=\"product.description\">{{ product.description }}</div>\n            </td>\n\n            <td class=\"reference-cell\">\n              <div class=\"ref-original\">{{ product.referenceOriginal }}</div>\n              <div *ngIf=\"product.referenceFournisseur\" class=\"ref-fournisseur\">\n                Fournisseur: {{ product.referenceFournisseur }}\n              </div>\n            </td>\n\n            <td class=\"price-cell\">\n              <div class=\"price-container\">\n                <!-- Prix initial (barré si promotion) -->\n                <div class=\"price-initial\" *ngIf=\"hasPromotion(product)\">\n                  <span class=\"price-label\">Prix initial:</span>\n                  <span class=\"price-value initial crossed\">{{ formatPrice(product.prixVenteTTC) }}</span>\n                </div>\n\n                <!-- Prix final -->\n                <div class=\"price-final\">\n                  <span class=\"price-label\" *ngIf=\"hasPromotion(product)\">Prix final:</span>\n                  <span class=\"price-value final\">{{ formatPrice(getFinalPrice(product)) }}</span>\n                </div>\n\n                <!-- Badge de remise -->\n                <div class=\"discount-badge\" *ngIf=\"hasPromotion(product) && product.pourcentageRemiseTotale\">\n                  -{{ product.pourcentageRemiseTotale }}%\n                </div>\n              </div>\n            </td>\n\n            <td class=\"stock-cell\">\n              <div class=\"stock-container\">\n                <span class=\"stock-value\" [ngClass]=\"{'stock-critique': product.stock <= 10}\">\n                  {{ product.stock }}\n                </span>\n                <div class=\"stock-alert\" *ngIf=\"product.stock <= 10\">\n                  ⚠️ Stock critique\n                </div>\n              </div>\n            </td>\n\n            <td class=\"category-cell\">\n              <div class=\"category\">{{ product.sousCategorie?.nom }}</div>\n              <div class=\"brand\" *ngIf=\"product.marque?.name\">{{ product.marque?.name }}</div>\n            </td>\n\n            <td class=\"status-cell\">\n              <span class=\"status-badge\" [class]=\"getStatusClass(product)\">\n                {{ getStatusText(product) }}\n              </span>\n            </td>\n\n            <td class=\"actions-cell\">\n              <div class=\"action-buttons\">\n                <button\n                  class=\"btn-action btn-view\"\n                  (click)=\"viewProductDetails(product)\"\n                  title=\"Voir les détails\">\n                  👁️\n                </button>\n\n                <button\n                  class=\"btn-action btn-edit\"\n                  (click)=\"editProduct(product)\"\n                  title=\"Modifier le produit\">\n                  ✏️\n                </button>\n\n                <button\n                  class=\"btn-action btn-delete\"\n                  (click)=\"deleteProduct(product)\"\n                  title=\"Supprimer le produit\">\n                  🗑️\n                </button>\n              </div>\n            </td>\n          </tr>\n        </tbody>\n      </table>\n    </div>\n  </div>\n\n  <!-- Template de chargement -->\n  <ng-template #loadingTemplate>\n    <div class=\"loading\">\n      <div class=\"spinner\"></div>\n      <p>Chargement des produits...</p>\n    </div>\n  </ng-template>\n\n  <!-- Message si aucun produit -->\n  <div *ngIf=\"currentPageProducts().length === 0 && !isLoading()\" class=\"no-products\">\n    <h3>Aucun produit trouvé</h3>\n    <p>Commencez par ajouter votre premier produit</p>\n    <button class=\"btn btn-primary\" (click)=\"openAddForm()\">\n      ➕ Ajouter votre premier produit\n    </button>\n  </div>\n\n  <!-- Modal des détails du produit -->\n  <div class=\"modal-overlay\" *ngIf=\"showDetails()\" (click)=\"closeDetails()\">\n    <div class=\"modal-content details-modal\" (click)=\"$event.stopPropagation()\">\n      <div class=\"modal-header\">\n        <h3>Détails du produit</h3>\n        <button type=\"button\" class=\"btn btn-sm btn-outline-secondary\" (click)=\"closeDetails()\">\n          ✕ Fermer\n        </button>\n      </div>\n\n      <div class=\"product-details-content\" *ngIf=\"selectedProductDetails()\">\n        <div class=\"details-grid\">\n          <!-- Informations de base -->\n          <div class=\"detail-section\">\n            <h4>Informations générales</h4>\n            <div class=\"detail-item\">\n              <label>Nom :</label>\n              <span>{{ selectedProductDetails()!.nom }}</span>\n            </div>\n            <div class=\"detail-item\">\n              <label>Description :</label>\n              <span>{{ selectedProductDetails()!.description }}</span>\n            </div>\n            <div class=\"detail-item\">\n              <label>Référence originale :</label>\n              <span>{{ selectedProductDetails()!.referenceOriginal }}</span>\n            </div>\n            <div class=\"detail-item\" *ngIf=\"selectedProductDetails()!.referenceFournisseur\">\n              <label>Référence fournisseur :</label>\n              <span>{{ selectedProductDetails()!.referenceFournisseur }}</span>\n            </div>\n            <div class=\"detail-item\">\n              <label>Code à barres :</label>\n              <span>{{ selectedProductDetails()!.codeABarre }}</span>\n            </div>\n          </div>\n\n          <!-- Prix et stock -->\n          <div class=\"detail-section\">\n            <h4>Prix et stock</h4>\n            <div class=\"detail-item\">\n              <label>Prix d'achat HT :</label>\n              <span>{{ formatPrice(selectedProductDetails()!.prixAchatHT) }}</span>\n            </div>\n            <div class=\"detail-item\">\n              <label>Prix de vente HT :</label>\n              <span>{{ formatPrice(selectedProductDetails()!.prixVenteHT) }}</span>\n            </div>\n            <div class=\"detail-item\">\n              <label>Prix de vente TTC :</label>\n              <span>{{ formatPrice(selectedProductDetails()!.prixVenteTTC) }}</span>\n            </div>\n            <div class=\"detail-item\" *ngIf=\"selectedProductDetails()!.prixApresRemisesOutlet && selectedProductDetails()!.prixApresRemisesOutlet !== selectedProductDetails()!.prixVenteTTC\">\n              <label>Prix après remise outlet :</label>\n              <span class=\"outlet-price\">{{ formatPrice(selectedProductDetails()!.prixApresRemisesOutlet!) }}</span>\n            </div>\n            <div class=\"detail-item\" *ngIf=\"hasPromotion(selectedProductDetails()!)\">\n              <label>Prix final après toutes promotions :</label>\n              <span class=\"promotion-price\">{{ formatPrice(selectedProductDetails()!.prixApresRemises!) }}</span>\n            </div>\n            <div class=\"detail-item\">\n              <label>Stock :</label>\n              <span [class]=\"getStatusClass(selectedProductDetails()!)\">{{ selectedProductDetails()!.stock }} unités</span>\n            </div>\n            <div class=\"detail-item\" *ngIf=\"selectedProductDetails()?.pourcentageRemiseTotale && selectedProductDetails()!.pourcentageRemiseTotale! > 0\">\n              <label>Économie totale :</label>\n              <span class=\"savings-badge\">-{{ selectedProductDetails()!.pourcentageRemiseTotale }}%</span>\n              <small class=\"text-muted\">\n                ({{ formatPrice((selectedProductDetails()!.prixVenteTTC || 0) - (selectedProductDetails()!.prixApresRemises || 0)) }} d'économie)\n              </small>\n            </div>\n          </div>\n\n          <!-- Catégories -->\n          <div class=\"detail-section\">\n            <h4>Classifications</h4>\n            <div class=\"detail-item\" *ngIf=\"selectedProductDetails()!.sousCategorie\">\n              <label>Sous-catégorie :</label>\n              <span>{{ selectedProductDetails()!.sousCategorie!.nom }}</span>\n            </div>\n            <div class=\"detail-item\" *ngIf=\"selectedProductDetails()!.marque\">\n              <label>Marque :</label>\n              <span>{{ selectedProductDetails()!.marque!.name }}</span>\n            </div>\n            <div class=\"detail-item\" *ngIf=\"selectedProductDetails()!.forme\">\n              <label>Forme :</label>\n              <span>{{ selectedProductDetails()!.forme!.nom }}</span>\n            </div>\n            <div class=\"detail-item\" *ngIf=\"selectedProductDetails()!.tauxTVA\">\n              <label>Taux TVA :</label>\n              <span>{{ selectedProductDetails()!.tauxTVA!.libelle }} ({{ selectedProductDetails()!.tauxTVA!.taux }}%)</span>\n            </div>\n          </div>\n\n          <!-- Images -->\n          <div class=\"detail-section\" *ngIf=\"selectedProductDetails()!.images && selectedProductDetails()!.images!.length > 0\">\n            <h4>Images</h4>\n            <div class=\"images-gallery\">\n              <div class=\"image-item\" *ngFor=\"let image of selectedProductDetails()!.images\">\n                <img\n                  [src]=\"imageUrlService.getProduitImageUrl(image.imageUrl)\"\n                  [alt]=\"image.altText || 'Image produit'\"\n                />\n                <span *ngIf=\"image.isMain\" class=\"main-badge\">Principale</span>\n              </div>\n            </div>\n          </div>\n        </div>\n\n        <div class=\"details-actions\">\n          <button class=\"btn btn-primary\" (click)=\"editProduct(selectedProductDetails()!); closeDetails()\">\n            ✏️ Modifier ce produit\n          </button>\n          <button class=\"btn btn-secondary\" (click)=\"closeDetails()\">\n            Fermer\n          </button>\n        </div>\n      </div>\n    </div>\n  </div>\n\n  <!-- Modal du formulaire -->\n  <div class=\"modal-overlay\" *ngIf=\"showForm()\" (click)=\"closeForm()\">\n    <div class=\"modal-content\" (click)=\"$event.stopPropagation()\">\n      <div class=\"modal-header\">\n        <h3>{{ isEditMode() ? 'Modifier' : 'Ajouter' }} un produit</h3>\n        <div class=\"modal-actions\">\n          <button type=\"button\" class=\"btn btn-sm btn-outline-secondary\" (click)=\"refreshDropdowns()\" title=\"Recharger les listes\">\n            🔄 Actualiser\n          </button>\n          <button type=\"button\" class=\"btn btn-sm btn-outline-secondary\" (click)=\"closeForm()\">\n            ✕ Fermer\n          </button>\n        </div>\n      </div>\n      \n      <form #productForm=\"ngForm\" name=\"productForm\" (ngSubmit)=\"onSubmit(productForm)\" class=\"product-form\">\n        <!-- Informations de base -->\n        <div class=\"form-section\">\n          <h4>Informations de base</h4>\n          \n          <div class=\"form-row\">\n            <div class=\"form-group\">\n              <label>Référence originale *</label>\n              <input\n                type=\"text\"\n                name=\"referenceOriginal\"\n                [(ngModel)]=\"formData.referenceOriginal\"\n                required\n                class=\"form-control\"\n                placeholder=\"REF-001\"\n              />\n            </div>\n            <div class=\"form-group\">\n              <label>Référence fournisseur</label>\n              <input\n                type=\"text\"\n                name=\"referenceFournisseur\"\n                [(ngModel)]=\"formData.referenceFournisseur\"\n                class=\"form-control\"\n                placeholder=\"FOUR-001\"\n              />\n            </div>\n          </div>\n\n          <div class=\"form-row\">\n            <div class=\"form-group\">\n              <label>Nom du produit *</label>\n              <input\n                type=\"text\"\n                name=\"nom\"\n                [(ngModel)]=\"formData.nom\"\n                required\n                class=\"form-control\"\n                placeholder=\"Nom du produit\"\n              />\n            </div>\n            <div class=\"form-group\">\n              <label>Code à barres</label>\n              <input\n                type=\"text\"\n                name=\"codeABarre\"\n                [(ngModel)]=\"formData.codeABarre\"\n                class=\"form-control\"\n                placeholder=\"Généré automatiquement\"\n              />\n            </div>\n          </div>\n\n          <div class=\"form-group\">\n            <label>Description *</label>\n            <textarea\n              name=\"description\"\n              [(ngModel)]=\"formData.description\"\n              required\n              class=\"form-control\"\n              rows=\"3\"\n              placeholder=\"Description du produit\"\n            ></textarea>\n          </div>\n        </div>\n\n        <!-- Prix et stock -->\n        <div class=\"form-section\">\n          <h4>Prix et stock</h4>\n          \n          <div class=\"form-row\">\n            <div class=\"form-group\">\n              <label>Prix d'achat HT (DT) *</label>\n              <input\n                type=\"number\"\n                name=\"prixAchat\"\n                [(ngModel)]=\"formData.prixAchat\"\n                required\n                min=\"0\"\n                step=\"0.01\"\n                class=\"form-control\"\n                (input)=\"onPrixChange()\"\n              />\n            </div>\n            <div class=\"form-group\">\n              <label>Prix de vente HT (DT) *</label>\n              <input\n                type=\"number\"\n                name=\"prixVente\"\n                [(ngModel)]=\"formData.prixVente\"\n                required\n                min=\"0\"\n                step=\"0.01\"\n                class=\"form-control\"\n                (input)=\"onPrixChange()\"\n              />\n            </div>\n          </div>\n\n          <!-- Affichage du prix TTC calculé -->\n          <div class=\"form-group\" *ngIf=\"calculatedPrixTTC > 0\">\n            <div class=\"price-info\">\n              <strong>Prix de vente TTC calculé : {{ formatPrice(calculatedPrixTTC) }}</strong>\n              <small class=\"text-muted\">(TVA {{ selectedTauxTVA }}% incluse)</small>\n            </div>\n          </div>\n\n          <div class=\"form-row\">\n            <div class=\"form-group\">\n              <label>Stock initial</label>\n              <input\n                type=\"number\"\n                name=\"stock\"\n                [(ngModel)]=\"formData.stock\"\n                min=\"0\"\n                class=\"form-control\"\n                placeholder=\"0\"\n              />\n            </div>\n            <div class=\"form-group\">\n              <label>Pourcentage de remise outlet (%)</label>\n              <input\n                type=\"number\"\n                name=\"pourcentageRemise\"\n                [(ngModel)]=\"formData.pourcentageRemise\"\n                min=\"0\"\n                max=\"100\"\n                step=\"1\"\n                class=\"form-control\"\n                placeholder=\"30\"\n                title=\"Remise outlet appliquée automatiquement (défaut: 30%)\"\n              />\n              <small class=\"form-text text-muted\">\n                💡 Remise outlet appliquée automatiquement sur ce produit (défaut: 30%)\n              </small>\n            </div>\n          </div>\n        </div>\n\n        <!-- Catégories et classifications -->\n        <div class=\"form-section\">\n          <h4>Catégories et classifications</h4>\n          \n          <div class=\"form-row\">\n            <div class=\"form-group\">\n              <label>Catégorie *</label>\n              <div class=\"input-with-button\">\n                <select name=\"categorieId\" [(ngModel)]=\"formData.categorieId\" required class=\"form-control\" (change)=\"onCategorieChange($event)\">\n                  <option value=\"\">Sélectionner</option>\n                  <option *ngFor=\"let cat of categoriesDropdown()\" [value]=\"cat.id\">\n                    {{ cat.nom }}\n                  </option>\n                </select>\n              </div>\n            </div>\n            <div class=\"form-group\">\n              <label>Sous-catégorie * ({{ sousCategoriesDropdown().length }} disponibles)</label>\n              <div class=\"input-with-button\">\n                <select name=\"sousCategorieId\" [(ngModel)]=\"formData.sousCategorieId\" required class=\"form-control\">\n                  <option value=\"\">Sélectionner une sous-catégorie</option>\n                  <option *ngFor=\"let subCat of sousCategoriesDropdown(); trackBy: trackBySousCategorieId\" [value]=\"subCat.id\">\n                    {{ subCat.nom }}\n                  </option>\n                </select>\n                <!-- Debug info -->\n                <small class=\"debug-info\" style=\"color: #666; font-size: 12px;\">\n                  Debug: {{ sousCategoriesDropdown().length }} sous-catégories chargées\n                </small>\n              </div>\n            </div>\n          </div>\n\n          <div class=\"form-row\">\n            <div class=\"form-group\">\n              <label>Marque *</label>\n              <div class=\"input-with-button\">\n                <select name=\"marqueId\" [(ngModel)]=\"formData.marqueId\" required class=\"form-control\">\n                  <option value=\"\">Sélectionner</option>\n                  <option *ngFor=\"let marque of marquesDropdown()\" [value]=\"marque.id\">\n                    {{ marque.name }}\n                  </option>\n                </select>\n              </div>\n            </div>\n            <div class=\"form-group\">\n              <label>Forme *</label>\n              <div class=\"input-with-button\">\n                <select name=\"formeId\" [(ngModel)]=\"formData.formeId\" required class=\"form-control\">\n                  <option value=\"\">Sélectionner</option>\n                  <option *ngFor=\"let forme of formesDropdown()\" [value]=\"forme.id\">\n                    {{ forme.nom }}\n                  </option>\n                </select>\n              </div>\n            </div>\n          </div>\n\n          <div class=\"form-row\">\n            <div class=\"form-group\">\n              <label>Taux TVA *</label>\n              <div class=\"input-with-button\">\n                <select name=\"tauxTVAId\" [(ngModel)]=\"formData.tauxTVAId\" required class=\"form-control\" (change)=\"onTauxTVAChange($event)\">\n                  <option value=\"\">Sélectionner</option>\n                  <option *ngFor=\"let taux of tauxTVADropdown()\" [value]=\"taux.id\">\n                    {{ taux.libelle }} ({{ taux.taux }}%)\n                  </option>\n                </select>\n              </div>\n            </div>\n          </div>\n        </div>\n\n        <!-- Images du produit -->\n        <div class=\"form-section\">\n          <h4>Images du produit</h4>\n\n          <!-- Images existantes (en mode édition) -->\n          <div *ngIf=\"isEditMode() && selectedProduct()?.images?.length\" class=\"existing-images\">\n            <h6>Images actuelles :</h6>\n            <div class=\"image-preview-container\">\n              <div class=\"image-preview existing\" *ngFor=\"let image of selectedProduct()!.images; let i = index\">\n                <img\n                  [src]=\"imageUrlService.getProduitImageUrl(image.imageUrl)\"\n                  [alt]=\"image.altText || 'Image produit'\"\n                />\n                <div class=\"image-info\">\n                  <span class=\"image-name\">Image {{ i + 1 }}</span>\n                  <span class=\"image-main\" *ngIf=\"image.isMain\">(Principale)</span>\n                </div>\n              </div>\n            </div>\n            <small class=\"form-text text-muted\">\n              Les nouvelles images sélectionnées ci-dessous remplaceront les images existantes.\n            </small>\n          </div>\n\n          <!-- Sélection d'images une par une -->\n          <div class=\"form-group\">\n            <label>{{ isEditMode() ? 'Nouvelles images' : 'Images du produit' }}</label>\n\n            <!-- Input file caché -->\n            <input\n              #fileInput\n              type=\"file\"\n              accept=\"image/*\"\n              style=\"display: none;\"\n              (change)=\"onSingleImageSelected($event)\"\n            />\n\n            <!-- Bouton pour ajouter la première image ou d'autres images -->\n            <div class=\"image-upload-actions\">\n              <button\n                type=\"button\"\n                class=\"btn btn-outline-primary\"\n                (click)=\"fileInput.click()\"\n                [disabled]=\"selectedImageFiles.length >= 5\"\n              >\n                <span *ngIf=\"selectedImageFiles.length === 0\">📷 Choisir une image</span>\n                <span *ngIf=\"selectedImageFiles.length > 0\">➕ Ajouter autre image</span>\n              </button>\n\n              <small class=\"form-text text-muted\" style=\"margin-left: 10px;\">\n                {{ selectedImageFiles.length }}/5 images sélectionnées\n                <span *ngIf=\"selectedImageFiles.length >= 5\"> (Maximum atteint)</span>\n              </small>\n            </div>\n\n            <small class=\"form-text text-muted\" style=\"margin-top: 5px;\">\n              {{ isEditMode() ? 'Ajoutez de nouvelles images pour remplacer les existantes.' : 'La première image sera l\\'image principale.' }}\n            </small>\n\n            <!-- Prévisualisation des images sélectionnées -->\n            <div class=\"image-preview-container\" *ngIf=\"selectedImageFiles.length > 0\" style=\"margin-top: 15px;\">\n              <div class=\"image-preview new\" *ngFor=\"let file of selectedImageFiles; let i = index\">\n                <img [src]=\"getImagePreview(file)\" [alt]=\"file.name\" />\n                <div class=\"image-info\">\n                  <span class=\"image-name\">{{ file.name }}</span>\n                  <span class=\"image-main\" *ngIf=\"i === 0\">({{ isEditMode() ? 'Nouvelle image principale' : 'Image principale' }})</span>\n                  <button type=\"button\" class=\"btn-remove\" (click)=\"removeImageFile(i)\" title=\"Supprimer cette image\">×</button>\n                </div>\n              </div>\n            </div>\n          </div>\n        </div>\n\n        <!-- Actions -->\n        <div class=\"form-actions\">\n          <button type=\"button\" class=\"btn btn-secondary\" (click)=\"closeForm()\">\n            Annuler\n          </button>\n          <button type=\"submit\" class=\"btn btn-primary\" [disabled]=\"!productForm.valid || isLoading()\">\n            {{ isEditMode() ? 'Modifier' : 'Ajouter' }}\n          </button>\n        </div>\n      </form>\n    </div>\n  </div>\n</div>\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AC8CE,IAAA,yBAAA,GAAA,OAAA,EAAA,EAA6J,GAAA,IAAA;AACvJ,IAAA,iBAAA,GAAA,eAAA;AAAQ,IAAA,uBAAA;AACZ,IAAA,yBAAA,GAAA,GAAA;AAAG,IAAA,iBAAA,CAAA;AAAa,IAAA,uBAAA;AAChB,IAAA,yBAAA,GAAA,UAAA,CAAA;AAAgC,IAAA,qBAAA,SAAA,SAAA,4DAAA;AAAA,MAAA,wBAAA,GAAA;AAAA,YAAA,SAAA,wBAAA;AAAA,aAAA,sBAAS,OAAA,SAAA,CAAU;IAAA,CAAA;AAAE,IAAA,iBAAA,GAAA,wBAAA;AAAY,IAAA,uBAAA,EAAS;;;;AADvE,IAAA,oBAAA,CAAA;AAAA,IAAA,4BAAA,OAAA,MAAA,CAAA;;;;;;AAKL,IAAA,yBAAA,GAAA,OAAA,EAAA,EAA0D,GAAA,OAAA,EAAA,EAC5B,GAAA,IAAA;AACtB,IAAA,iBAAA,GAAA,gCAAA;AAAuB,IAAA,uBAAA;AAC3B,IAAA,yBAAA,GAAA,GAAA;AAAG,IAAA,iBAAA,GAAA,2DAAA;AAAsD,IAAA,uBAAA,EAAI;AAG/D,IAAA,yBAAA,GAAA,OAAA,EAAA,EAA4B,GAAA,UAAA,CAAA;AACM,IAAA,qBAAA,SAAA,SAAA,4DAAA;AAAA,MAAA,wBAAA,GAAA;AAAA,YAAA,SAAA,wBAAA;AAAA,aAAA,sBAAS,OAAA,YAAA,CAAa;IAAA,CAAA;AACpD,IAAA,iBAAA,GAAA,0BAAA;AACF,IAAA,uBAAA,EAAS,EACL;;;;;AA8BJ,IAAA,yBAAA,GAAA,OAAA,EAAA,EAA4D,GAAA,OAAA,EAAA;AACnC,IAAA,iBAAA,GAAA,cAAA;AAAE,IAAA,uBAAA;AACzB,IAAA,yBAAA,GAAA,OAAA,EAAA,EAA0B,GAAA,OAAA,EAAA;AACA,IAAA,iBAAA,CAAA;AAAwB,IAAA,uBAAA;AAChD,IAAA,yBAAA,GAAA,OAAA,EAAA;AAAwB,IAAA,iBAAA,GAAA,cAAA;AAAY,IAAA,uBAAA,EAAM,EACtC;;;;AAFoB,IAAA,oBAAA,CAAA;AAAA,IAAA,4BAAA,OAAA,iBAAA,CAAA;;;;;AA7BhC,IAAA,yBAAA,GAAA,OAAA,EAAA,EAAqF,GAAA,OAAA,CAAA,EAC3D,GAAA,OAAA,CAAA,EACC,GAAA,OAAA,EAAA;AACE,IAAA,iBAAA,GAAA,WAAA;AAAE,IAAA,uBAAA;AACzB,IAAA,yBAAA,GAAA,OAAA,EAAA,EAA0B,GAAA,OAAA,EAAA;AACA,IAAA,iBAAA,CAAA;AAAuB,IAAA,uBAAA;AAC/C,IAAA,yBAAA,GAAA,OAAA,EAAA;AAAwB,IAAA,iBAAA,GAAA,UAAA;AAAQ,IAAA,uBAAA,EAAM,EAClC;AAGR,IAAA,yBAAA,IAAA,OAAA,CAAA,EAAuB,IAAA,OAAA,EAAA;AACE,IAAA,iBAAA,IAAA,WAAA;AAAE,IAAA,uBAAA;AACzB,IAAA,yBAAA,IAAA,OAAA,EAAA,EAA0B,IAAA,OAAA,EAAA;AACA,IAAA,iBAAA,EAAA;AAAqB,IAAA,uBAAA;AAC7C,IAAA,yBAAA,IAAA,OAAA,EAAA;AAAwB,IAAA,iBAAA,IAAA,oBAAA;AAAe,IAAA,uBAAA,EAAM,EACzC;AAGR,IAAA,yBAAA,IAAA,OAAA,CAAA,EAAuB,IAAA,OAAA,EAAA;AACE,IAAA,iBAAA,IAAA,WAAA;AAAE,IAAA,uBAAA;AACzB,IAAA,yBAAA,IAAA,OAAA,EAAA,EAA0B,IAAA,OAAA,EAAA;AACA,IAAA,iBAAA,EAAA;;AAAyC,IAAA,uBAAA;AACjE,IAAA,yBAAA,IAAA,OAAA,EAAA;AAAwB,IAAA,iBAAA,IAAA,iBAAA;AAAe,IAAA,uBAAA,EAAM,EACzC;AAGR,IAAA,qBAAA,IAAA,0CAAA,GAAA,GAAA,OAAA,EAAA;AAOF,IAAA,uBAAA,EAAM;;;;AA5BwB,IAAA,oBAAA,CAAA;AAAA,IAAA,4BAAA,OAAA,SAAA,EAAA,MAAA;AAQA,IAAA,oBAAA,CAAA;AAAA,IAAA,4BAAA,OAAA,cAAA,CAAA;AAQA,IAAA,oBAAA,CAAA;AAAA,IAAA,6BAAA,IAAA,sBAAA,IAAA,GAAA,OAAA,cAAA,GAAA,OAAA,GAAA,KAAA;AAKE,IAAA,oBAAA,CAAA;AAAA,IAAA,qBAAA,QAAA,OAAA,iBAAA,IAAA,CAAA;;;;;;AAWlC,IAAA,yBAAA,GAAA,OAAA,EAAA,EAAsD,GAAA,OAAA,EAAA,EACxB,GAAA,OAAA,EAAA,EACF,GAAA,SAAA,EAAA;AAGpB,IAAA,2BAAA,iBAAA,SAAA,iEAAA,QAAA;AAAA,MAAA,wBAAA,GAAA;AAAA,YAAA,SAAA,wBAAA;AAAA,MAAA,6BAAA,OAAA,aAAA,MAAA,MAAA,OAAA,cAAA;AAAA,aAAA,sBAAA,MAAA;IAAA,CAAA;AAFF,IAAA,uBAAA;AAMA,IAAA,yBAAA,GAAA,QAAA,EAAA;AAA0B,IAAA,iBAAA,GAAA,WAAA;AAAE,IAAA,uBAAA,EAAO;AAGrC,IAAA,yBAAA,GAAA,OAAA,EAAA,EAAqB,GAAA,SAAA,EAAA,EACY,GAAA,SAAA,EAAA;AACN,IAAA,2BAAA,iBAAA,SAAA,iEAAA,QAAA;AAAA,MAAA,wBAAA,GAAA;AAAA,YAAA,SAAA,wBAAA;AAAA,MAAA,6BAAA,OAAA,QAAA,aAAA,MAAA,MAAA,OAAA,QAAA,cAAA;AAAA,aAAA,sBAAA,MAAA;IAAA,CAAA;AAAvB,IAAA,uBAAA;AACA,IAAA,yBAAA,GAAA,MAAA;AAAM,IAAA,iBAAA,IAAA,cAAA;AAAY,IAAA,uBAAA,EAAO;AAE3B,IAAA,yBAAA,IAAA,SAAA,EAAA,EAA+B,IAAA,SAAA,EAAA;AACN,IAAA,2BAAA,iBAAA,SAAA,kEAAA,QAAA;AAAA,MAAA,wBAAA,GAAA;AAAA,YAAA,SAAA,wBAAA;AAAA,MAAA,6BAAA,OAAA,QAAA,aAAA,MAAA,MAAA,OAAA,QAAA,cAAA;AAAA,aAAA,sBAAA,MAAA;IAAA,CAAA;AAAvB,IAAA,uBAAA;AACA,IAAA,yBAAA,IAAA,MAAA;AAAM,IAAA,iBAAA,IAAA,cAAA;AAAY,IAAA,uBAAA,EAAO;AAE3B,IAAA,yBAAA,IAAA,SAAA,EAAA,EAA+B,IAAA,SAAA,EAAA;AACN,IAAA,2BAAA,iBAAA,SAAA,kEAAA,QAAA;AAAA,MAAA,wBAAA,GAAA;AAAA,YAAA,SAAA,wBAAA;AAAA,MAAA,6BAAA,OAAA,QAAA,YAAA,MAAA,MAAA,OAAA,QAAA,aAAA;AAAA,aAAA,sBAAA,MAAA;IAAA,CAAA;AAAvB,IAAA,uBAAA;AACA,IAAA,yBAAA,IAAA,MAAA;AAAM,IAAA,iBAAA,IAAA,cAAA;AAAY,IAAA,uBAAA,EAAO,EACnB,EACJ,EACF;;;;AArBA,IAAA,oBAAA,CAAA;AAAA,IAAA,2BAAA,WAAA,OAAA,WAAA;AASuB,IAAA,oBAAA,CAAA;AAAA,IAAA,2BAAA,WAAA,OAAA,QAAA,WAAA;AAIA,IAAA,oBAAA,CAAA;AAAA,IAAA,2BAAA,WAAA,OAAA,QAAA,WAAA;AAIA,IAAA,oBAAA,CAAA;AAAA,IAAA,2BAAA,WAAA,OAAA,QAAA,UAAA;;;;;AAoCjB,IAAA,yBAAA,GAAA,QAAA,EAAA;AAA6C,IAAA,iBAAA,CAAA;;AAAqD,IAAA,uBAAA;;;;AAArD,IAAA,oBAAA;AAAA,IAAA,6BAAA,iBAAA,sBAAA,GAAA,GAAA,WAAA,WAAA,YAAA,GAAA,EAAA;;;;;AAC7C,IAAA,yBAAA,GAAA,QAAA,EAAA;AAA2D,IAAA,iBAAA,GAAA,uBAAA;AAAS,IAAA,uBAAA;;;;;AAEtE,IAAA,yBAAA,GAAA,OAAA,EAAA;AAA6D,IAAA,iBAAA,CAAA;AAAyB,IAAA,uBAAA;;;;AAAzB,IAAA,oBAAA;AAAA,IAAA,4BAAA,WAAA,WAAA;;;;;AAK7D,IAAA,yBAAA,GAAA,OAAA,EAAA;AACE,IAAA,iBAAA,CAAA;AACF,IAAA,uBAAA;;;;AADE,IAAA,oBAAA;AAAA,IAAA,6BAAA,kBAAA,WAAA,sBAAA,GAAA;;;;;AAOA,IAAA,yBAAA,GAAA,OAAA,EAAA,EAAyD,GAAA,QAAA,EAAA;AAC7B,IAAA,iBAAA,GAAA,eAAA;AAAa,IAAA,uBAAA;AACvC,IAAA,yBAAA,GAAA,QAAA,EAAA;AAA0C,IAAA,iBAAA,CAAA;AAAuC,IAAA,uBAAA,EAAO;;;;;AAA9C,IAAA,oBAAA,CAAA;AAAA,IAAA,4BAAA,OAAA,YAAA,WAAA,YAAA,CAAA;;;;;AAK1C,IAAA,yBAAA,GAAA,QAAA,EAAA;AAAwD,IAAA,iBAAA,GAAA,aAAA;AAAW,IAAA,uBAAA;;;;;AAKrE,IAAA,yBAAA,GAAA,OAAA,EAAA;AACE,IAAA,iBAAA,CAAA;AACF,IAAA,uBAAA;;;;AADE,IAAA,oBAAA;AAAA,IAAA,6BAAA,MAAA,WAAA,yBAAA,IAAA;;;;;AAUF,IAAA,yBAAA,GAAA,OAAA,EAAA;AACE,IAAA,iBAAA,GAAA,+BAAA;AACF,IAAA,uBAAA;;;;;AAMF,IAAA,yBAAA,GAAA,OAAA,EAAA;AAAgD,IAAA,iBAAA,CAAA;AAA0B,IAAA,uBAAA;;;;AAA1B,IAAA,oBAAA;AAAA,IAAA,4BAAA,WAAA,UAAA,OAAA,OAAA,WAAA,OAAA,IAAA;;;;;;AA3DpD,IAAA,yBAAA,GAAA,MAAA,EAAA,EAAsE,GAAA,MAAA,EAAA,EAC7C,GAAA,OAAA,EAAA;AAKnB,IAAA,qBAAA,SAAA,SAAA,mEAAA,QAAA;AAAA,MAAA,wBAAA,GAAA;AAAA,YAAA,SAAA,wBAAA,CAAA;AAAA,aAAA,sBAAS,OAAA,aAAA,MAAA,CAAoB;IAAA,CAAA;AAJ/B,IAAA,uBAAA,EAIiC;AAGnC,IAAA,yBAAA,GAAA,MAAA,EAAA,EAAyB,GAAA,OAAA,EAAA;AACG,IAAA,iBAAA,CAAA;AAAiB,IAAA,uBAAA;AAC3C,IAAA,yBAAA,GAAA,OAAA,EAAA;AACE,IAAA,qBAAA,GAAA,sDAAA,GAAA,GAAA,QAAA,EAAA,EAA6C,GAAA,sDAAA,GAAA,GAAA,QAAA,EAAA;AAE/C,IAAA,uBAAA;AACA,IAAA,qBAAA,GAAA,qDAAA,GAAA,GAAA,OAAA,EAAA;AACF,IAAA,uBAAA;AAEA,IAAA,yBAAA,IAAA,MAAA,EAAA,EAA2B,IAAA,OAAA,EAAA;AACC,IAAA,iBAAA,EAAA;AAA+B,IAAA,uBAAA;AACzD,IAAA,qBAAA,IAAA,sDAAA,GAAA,GAAA,OAAA,EAAA;AAGF,IAAA,uBAAA;AAEA,IAAA,yBAAA,IAAA,MAAA,EAAA,EAAuB,IAAA,OAAA,EAAA;AAGnB,IAAA,qBAAA,IAAA,sDAAA,GAAA,GAAA,OAAA,EAAA;AAMA,IAAA,yBAAA,IAAA,OAAA,EAAA;AACE,IAAA,qBAAA,IAAA,uDAAA,GAAA,GAAA,QAAA,EAAA;AACA,IAAA,yBAAA,IAAA,QAAA,EAAA;AAAgC,IAAA,iBAAA,EAAA;AAAyC,IAAA,uBAAA,EAAO;AAIlF,IAAA,qBAAA,IAAA,sDAAA,GAAA,GAAA,OAAA,EAAA;AAGF,IAAA,uBAAA,EAAM;AAGR,IAAA,yBAAA,IAAA,MAAA,EAAA,EAAuB,IAAA,OAAA,EAAA,EACQ,IAAA,QAAA,EAAA;AAEzB,IAAA,iBAAA,EAAA;AACF,IAAA,uBAAA;AACA,IAAA,qBAAA,IAAA,sDAAA,GAAA,GAAA,OAAA,EAAA;AAGF,IAAA,uBAAA,EAAM;AAGR,IAAA,yBAAA,IAAA,MAAA,EAAA,EAA0B,IAAA,OAAA,EAAA;AACF,IAAA,iBAAA,EAAA;AAAgC,IAAA,uBAAA;AACtD,IAAA,qBAAA,IAAA,sDAAA,GAAA,GAAA,OAAA,EAAA;AACF,IAAA,uBAAA;AAEA,IAAA,yBAAA,IAAA,MAAA,EAAA,EAAwB,IAAA,QAAA,EAAA;AAEpB,IAAA,iBAAA,EAAA;AACF,IAAA,uBAAA,EAAO;AAGT,IAAA,yBAAA,IAAA,MAAA,EAAA,EAAyB,IAAA,OAAA,EAAA,EACK,IAAA,UAAA,EAAA;AAGxB,IAAA,qBAAA,SAAA,SAAA,yEAAA;AAAA,YAAA,aAAA,wBAAA,GAAA,EAAA;AAAA,YAAA,SAAA,wBAAA,CAAA;AAAA,aAAA,sBAAS,OAAA,mBAAA,UAAA,CAA2B;IAAA,CAAA;AAEpC,IAAA,iBAAA,IAAA,mBAAA;AACF,IAAA,uBAAA;AAEA,IAAA,yBAAA,IAAA,UAAA,EAAA;AAEE,IAAA,qBAAA,SAAA,SAAA,yEAAA;AAAA,YAAA,aAAA,wBAAA,GAAA,EAAA;AAAA,YAAA,SAAA,wBAAA,CAAA;AAAA,aAAA,sBAAS,OAAA,YAAA,UAAA,CAAoB;IAAA,CAAA;AAE7B,IAAA,iBAAA,IAAA,gBAAA;AACF,IAAA,uBAAA;AAEA,IAAA,yBAAA,IAAA,UAAA,EAAA;AAEE,IAAA,qBAAA,SAAA,SAAA,yEAAA;AAAA,YAAA,aAAA,wBAAA,GAAA,EAAA;AAAA,YAAA,SAAA,wBAAA,CAAA;AAAA,aAAA,sBAAS,OAAA,cAAA,UAAA,CAAsB;IAAA,CAAA;AAE/B,IAAA,iBAAA,IAAA,mBAAA;AACF,IAAA,uBAAA,EAAS,EACL,EACH;;;;;AAxFD,IAAA,oBAAA,CAAA;AAAA,IAAA,qBAAA,OAAA,OAAA,oBAAA,UAAA,GAAA,uBAAA,EAAoC,OAAA,WAAA,GAAA;AAOZ,IAAA,oBAAA,CAAA;AAAA,IAAA,4BAAA,WAAA,GAAA;AAEJ,IAAA,oBAAA,CAAA;AAAA,IAAA,qBAAA,QAAA,WAAA,SAAA;AACb,IAAA,oBAAA;AAAA,IAAA,qBAAA,QAAA,OAAA,aAAA,UAAA,CAAA;AAEyB,IAAA,oBAAA;AAAA,IAAA,qBAAA,QAAA,WAAA,WAAA;AAIR,IAAA,oBAAA,CAAA;AAAA,IAAA,4BAAA,WAAA,iBAAA;AACpB,IAAA,oBAAA;AAAA,IAAA,qBAAA,QAAA,WAAA,oBAAA;AAQwB,IAAA,oBAAA,CAAA;AAAA,IAAA,qBAAA,QAAA,OAAA,aAAA,UAAA,CAAA;AAOC,IAAA,oBAAA,CAAA;AAAA,IAAA,qBAAA,QAAA,OAAA,aAAA,UAAA,CAAA;AACK,IAAA,oBAAA,CAAA;AAAA,IAAA,4BAAA,OAAA,YAAA,OAAA,cAAA,UAAA,CAAA,CAAA;AAIL,IAAA,oBAAA;AAAA,IAAA,qBAAA,QAAA,OAAA,aAAA,UAAA,KAAA,WAAA,uBAAA;AAQH,IAAA,oBAAA,CAAA;AAAA,IAAA,qBAAA,WAAA,0BAAA,IAAA,KAAA,WAAA,SAAA,EAAA,CAAA;AACxB,IAAA,oBAAA;AAAA,IAAA,6BAAA,KAAA,WAAA,OAAA,GAAA;AAEwB,IAAA,oBAAA;AAAA,IAAA,qBAAA,QAAA,WAAA,SAAA,EAAA;AAON,IAAA,oBAAA,CAAA;AAAA,IAAA,4BAAA,WAAA,iBAAA,OAAA,OAAA,WAAA,cAAA,GAAA;AACF,IAAA,oBAAA;AAAA,IAAA,qBAAA,QAAA,WAAA,UAAA,OAAA,OAAA,WAAA,OAAA,IAAA;AAIO,IAAA,oBAAA,CAAA;AAAA,IAAA,qBAAA,OAAA,eAAA,UAAA,CAAA;AACzB,IAAA,oBAAA;AAAA,IAAA,6BAAA,KAAA,OAAA,cAAA,UAAA,GAAA,GAAA;;;;;AA/EZ,IAAA,yBAAA,GAAA,OAAA,EAAA,EAAsE,GAAA,SAAA,EAAA,EACtC,GAAA,OAAA,EACrB,GAAA,IAAA,EACD,GAAA,IAAA;AACE,IAAA,iBAAA,GAAA,OAAA;AAAK,IAAA,uBAAA;AACT,IAAA,yBAAA,GAAA,IAAA;AAAI,IAAA,iBAAA,GAAA,SAAA;AAAO,IAAA,uBAAA;AACX,IAAA,yBAAA,GAAA,IAAA;AAAI,IAAA,iBAAA,GAAA,iBAAA;AAAS,IAAA,uBAAA;AACb,IAAA,yBAAA,IAAA,IAAA;AAAI,IAAA,iBAAA,IAAA,MAAA;AAAI,IAAA,uBAAA;AACR,IAAA,yBAAA,IAAA,IAAA;AAAI,IAAA,iBAAA,IAAA,OAAA;AAAK,IAAA,uBAAA;AACT,IAAA,yBAAA,IAAA,IAAA;AAAI,IAAA,iBAAA,IAAA,cAAA;AAAS,IAAA,uBAAA;AACb,IAAA,yBAAA,IAAA,IAAA;AAAI,IAAA,iBAAA,IAAA,QAAA;AAAM,IAAA,uBAAA;AACV,IAAA,yBAAA,IAAA,IAAA;AAAI,IAAA,iBAAA,IAAA,SAAA;AAAO,IAAA,uBAAA,EAAK,EACb;AAEP,IAAA,yBAAA,IAAA,OAAA;AACE,IAAA,qBAAA,IAAA,+CAAA,IAAA,IAAA,MAAA,EAAA;AA6FF,IAAA,uBAAA,EAAQ,EACF;;;;AA9FoB,IAAA,oBAAA,EAAA;AAAA,IAAA,qBAAA,WAAA,OAAA,oBAAA,CAAA;;;;;AAhBhC,IAAA,yBAAA,GAAA,OAAA,EAAA;AACE,IAAA,qBAAA,GAAA,yCAAA,IAAA,GAAA,OAAA,EAAA;AA+GF,IAAA,uBAAA;;;;AA/GgC,IAAA,oBAAA;AAAA,IAAA,qBAAA,QAAA,OAAA,oBAAA,EAAA,SAAA,CAAA;;;;;AAmH9B,IAAA,yBAAA,GAAA,OAAA,EAAA;AACE,IAAA,oBAAA,GAAA,OAAA,EAAA;AACA,IAAA,yBAAA,GAAA,GAAA;AAAG,IAAA,iBAAA,GAAA,4BAAA;AAA0B,IAAA,uBAAA,EAAI;;;;;;AAKrC,IAAA,yBAAA,GAAA,OAAA,EAAA,EAAoF,GAAA,IAAA;AAC9E,IAAA,iBAAA,GAAA,yBAAA;AAAoB,IAAA,uBAAA;AACxB,IAAA,yBAAA,GAAA,GAAA;AAAG,IAAA,iBAAA,GAAA,6CAAA;AAA2C,IAAA,uBAAA;AAC9C,IAAA,yBAAA,GAAA,UAAA,CAAA;AAAgC,IAAA,qBAAA,SAAA,SAAA,4DAAA;AAAA,MAAA,wBAAA,GAAA;AAAA,YAAA,SAAA,wBAAA;AAAA,aAAA,sBAAS,OAAA,YAAA,CAAa;IAAA,CAAA;AACpD,IAAA,iBAAA,GAAA,wCAAA;AACF,IAAA,uBAAA,EAAS;;;;;AA8BD,IAAA,yBAAA,GAAA,OAAA,EAAA,EAAgF,GAAA,OAAA;AACvE,IAAA,iBAAA,GAAA,+BAAA;AAAuB,IAAA,uBAAA;AAC9B,IAAA,yBAAA,GAAA,MAAA;AAAM,IAAA,iBAAA,CAAA;AAAoD,IAAA,uBAAA,EAAO;;;;AAA3D,IAAA,oBAAA,CAAA;AAAA,IAAA,4BAAA,OAAA,uBAAA,EAAA,oBAAA;;;;;AAuBR,IAAA,yBAAA,GAAA,OAAA,EAAA,EAAiL,GAAA,OAAA;AACxK,IAAA,iBAAA,GAAA,+BAAA;AAA0B,IAAA,uBAAA;AACjC,IAAA,yBAAA,GAAA,QAAA,GAAA;AAA2B,IAAA,iBAAA,CAAA;AAAoE,IAAA,uBAAA,EAAO;;;;AAA3E,IAAA,oBAAA,CAAA;AAAA,IAAA,4BAAA,OAAA,YAAA,OAAA,uBAAA,EAAA,sBAAA,CAAA;;;;;AAE7B,IAAA,yBAAA,GAAA,OAAA,EAAA,EAAyE,GAAA,OAAA;AAChE,IAAA,iBAAA,GAAA,yCAAA;AAAoC,IAAA,uBAAA;AAC3C,IAAA,yBAAA,GAAA,QAAA,GAAA;AAA8B,IAAA,iBAAA,CAAA;AAA8D,IAAA,uBAAA,EAAO;;;;AAArE,IAAA,oBAAA,CAAA;AAAA,IAAA,4BAAA,OAAA,YAAA,OAAA,uBAAA,EAAA,gBAAA,CAAA;;;;;AAMhC,IAAA,yBAAA,GAAA,OAAA,EAAA,EAA6I,GAAA,OAAA;AACpI,IAAA,iBAAA,GAAA,sBAAA;AAAiB,IAAA,uBAAA;AACxB,IAAA,yBAAA,GAAA,QAAA,GAAA;AAA4B,IAAA,iBAAA,CAAA;AAAyD,IAAA,uBAAA;AACrF,IAAA,yBAAA,GAAA,SAAA,GAAA;AACE,IAAA,iBAAA,CAAA;AACF,IAAA,uBAAA,EAAQ;;;;AAHoB,IAAA,oBAAA,CAAA;AAAA,IAAA,6BAAA,KAAA,OAAA,uBAAA,EAAA,yBAAA,GAAA;AAE1B,IAAA,oBAAA,CAAA;AAAA,IAAA,6BAAA,MAAA,OAAA,aAAA,OAAA,uBAAA,EAAA,gBAAA,MAAA,OAAA,uBAAA,EAAA,oBAAA,EAAA,GAAA,kBAAA;;;;;AAQJ,IAAA,yBAAA,GAAA,OAAA,EAAA,EAAyE,GAAA,OAAA;AAChE,IAAA,iBAAA,GAAA,qBAAA;AAAgB,IAAA,uBAAA;AACvB,IAAA,yBAAA,GAAA,MAAA;AAAM,IAAA,iBAAA,CAAA;AAAkD,IAAA,uBAAA,EAAO;;;;AAAzD,IAAA,oBAAA,CAAA;AAAA,IAAA,4BAAA,OAAA,uBAAA,EAAA,cAAA,GAAA;;;;;AAER,IAAA,yBAAA,GAAA,OAAA,EAAA,EAAkE,GAAA,OAAA;AACzD,IAAA,iBAAA,GAAA,UAAA;AAAQ,IAAA,uBAAA;AACf,IAAA,yBAAA,GAAA,MAAA;AAAM,IAAA,iBAAA,CAAA;AAA4C,IAAA,uBAAA,EAAO;;;;AAAnD,IAAA,oBAAA,CAAA;AAAA,IAAA,4BAAA,OAAA,uBAAA,EAAA,OAAA,IAAA;;;;;AAER,IAAA,yBAAA,GAAA,OAAA,EAAA,EAAiE,GAAA,OAAA;AACxD,IAAA,iBAAA,GAAA,SAAA;AAAO,IAAA,uBAAA;AACd,IAAA,yBAAA,GAAA,MAAA;AAAM,IAAA,iBAAA,CAAA;AAA0C,IAAA,uBAAA,EAAO;;;;AAAjD,IAAA,oBAAA,CAAA;AAAA,IAAA,4BAAA,OAAA,uBAAA,EAAA,MAAA,GAAA;;;;;AAER,IAAA,yBAAA,GAAA,OAAA,EAAA,EAAmE,GAAA,OAAA;AAC1D,IAAA,iBAAA,GAAA,YAAA;AAAU,IAAA,uBAAA;AACjB,IAAA,yBAAA,GAAA,MAAA;AAAM,IAAA,iBAAA,CAAA;AAAiG,IAAA,uBAAA,EAAO;;;;AAAxG,IAAA,oBAAA,CAAA;AAAA,IAAA,6BAAA,IAAA,OAAA,uBAAA,EAAA,QAAA,SAAA,MAAA,OAAA,uBAAA,EAAA,QAAA,MAAA,IAAA;;;;;AAaJ,IAAA,yBAAA,GAAA,QAAA,GAAA;AAA8C,IAAA,iBAAA,GAAA,YAAA;AAAU,IAAA,uBAAA;;;;;AAL1D,IAAA,yBAAA,GAAA,OAAA,GAAA;AACE,IAAA,oBAAA,GAAA,OAAA,GAAA;AAIA,IAAA,qBAAA,GAAA,6DAAA,GAAA,GAAA,QAAA,GAAA;AACF,IAAA,uBAAA;;;;;AAJI,IAAA,oBAAA;AAAA,IAAA,qBAAA,OAAA,OAAA,gBAAA,mBAAA,UAAA,QAAA,GAAA,uBAAA,EAA0D,OAAA,UAAA,WAAA,eAAA;AAGrD,IAAA,oBAAA;AAAA,IAAA,qBAAA,QAAA,UAAA,MAAA;;;;;AARb,IAAA,yBAAA,GAAA,OAAA,EAAA,EAAqH,GAAA,IAAA;AAC/G,IAAA,iBAAA,GAAA,QAAA;AAAM,IAAA,uBAAA;AACV,IAAA,yBAAA,GAAA,OAAA,GAAA;AACE,IAAA,qBAAA,GAAA,sDAAA,GAAA,GAAA,OAAA,GAAA;AAOF,IAAA,uBAAA,EAAM;;;;AAPsC,IAAA,oBAAA,CAAA;AAAA,IAAA,qBAAA,WAAA,OAAA,uBAAA,EAAA,MAAA;;;;;;AAxFlD,IAAA,yBAAA,GAAA,OAAA,EAAA,EAAsE,GAAA,OAAA,EAAA,EAC1C,GAAA,OAAA,EAAA,EAEI,GAAA,IAAA;AACtB,IAAA,iBAAA,GAAA,8BAAA;AAAsB,IAAA,uBAAA;AAC1B,IAAA,yBAAA,GAAA,OAAA,EAAA,EAAyB,GAAA,OAAA;AAChB,IAAA,iBAAA,GAAA,OAAA;AAAK,IAAA,uBAAA;AACZ,IAAA,yBAAA,GAAA,MAAA;AAAM,IAAA,iBAAA,CAAA;AAAmC,IAAA,uBAAA,EAAO;AAElD,IAAA,yBAAA,IAAA,OAAA,EAAA,EAAyB,IAAA,OAAA;AAChB,IAAA,iBAAA,IAAA,eAAA;AAAa,IAAA,uBAAA;AACpB,IAAA,yBAAA,IAAA,MAAA;AAAM,IAAA,iBAAA,EAAA;AAA2C,IAAA,uBAAA,EAAO;AAE1D,IAAA,yBAAA,IAAA,OAAA,EAAA,EAAyB,IAAA,OAAA;AAChB,IAAA,iBAAA,IAAA,6BAAA;AAAqB,IAAA,uBAAA;AAC5B,IAAA,yBAAA,IAAA,MAAA;AAAM,IAAA,iBAAA,EAAA;AAAiD,IAAA,uBAAA,EAAO;AAEhE,IAAA,qBAAA,IAAA,gDAAA,GAAA,GAAA,OAAA,EAAA;AAIA,IAAA,yBAAA,IAAA,OAAA,EAAA,EAAyB,IAAA,OAAA;AAChB,IAAA,iBAAA,IAAA,oBAAA;AAAe,IAAA,uBAAA;AACtB,IAAA,yBAAA,IAAA,MAAA;AAAM,IAAA,iBAAA,EAAA;AAA0C,IAAA,uBAAA,EAAO,EACnD;AAIR,IAAA,yBAAA,IAAA,OAAA,EAAA,EAA4B,IAAA,IAAA;AACtB,IAAA,iBAAA,IAAA,eAAA;AAAa,IAAA,uBAAA;AACjB,IAAA,yBAAA,IAAA,OAAA,EAAA,EAAyB,IAAA,OAAA;AAChB,IAAA,iBAAA,IAAA,mBAAA;AAAiB,IAAA,uBAAA;AACxB,IAAA,yBAAA,IAAA,MAAA;AAAM,IAAA,iBAAA,EAAA;AAAwD,IAAA,uBAAA,EAAO;AAEvE,IAAA,yBAAA,IAAA,OAAA,EAAA,EAAyB,IAAA,OAAA;AAChB,IAAA,iBAAA,IAAA,oBAAA;AAAkB,IAAA,uBAAA;AACzB,IAAA,yBAAA,IAAA,MAAA;AAAM,IAAA,iBAAA,EAAA;AAAwD,IAAA,uBAAA,EAAO;AAEvE,IAAA,yBAAA,IAAA,OAAA,EAAA,EAAyB,IAAA,OAAA;AAChB,IAAA,iBAAA,IAAA,qBAAA;AAAmB,IAAA,uBAAA;AAC1B,IAAA,yBAAA,IAAA,MAAA;AAAM,IAAA,iBAAA,EAAA;AAAyD,IAAA,uBAAA,EAAO;AAExE,IAAA,qBAAA,IAAA,gDAAA,GAAA,GAAA,OAAA,EAAA,EAAiL,IAAA,gDAAA,GAAA,GAAA,OAAA,EAAA;AAQjL,IAAA,yBAAA,IAAA,OAAA,EAAA,EAAyB,IAAA,OAAA;AAChB,IAAA,iBAAA,IAAA,SAAA;AAAO,IAAA,uBAAA;AACd,IAAA,yBAAA,IAAA,MAAA;AAA0D,IAAA,iBAAA,EAAA;AAA4C,IAAA,uBAAA,EAAO;AAE/G,IAAA,qBAAA,IAAA,gDAAA,GAAA,GAAA,OAAA,EAAA;AAOF,IAAA,uBAAA;AAGA,IAAA,yBAAA,IAAA,OAAA,EAAA,EAA4B,IAAA,IAAA;AACtB,IAAA,iBAAA,IAAA,iBAAA;AAAe,IAAA,uBAAA;AACnB,IAAA,qBAAA,IAAA,gDAAA,GAAA,GAAA,OAAA,EAAA,EAAyE,IAAA,gDAAA,GAAA,GAAA,OAAA,EAAA,EAIP,IAAA,gDAAA,GAAA,GAAA,OAAA,EAAA,EAID,IAAA,gDAAA,GAAA,GAAA,OAAA,EAAA;AAQnE,IAAA,uBAAA;AAGA,IAAA,qBAAA,IAAA,gDAAA,GAAA,GAAA,OAAA,EAAA;AAYF,IAAA,uBAAA;AAEA,IAAA,yBAAA,IAAA,OAAA,EAAA,EAA6B,IAAA,UAAA,CAAA;AACK,IAAA,qBAAA,SAAA,SAAA,mEAAA;AAAA,MAAA,wBAAA,IAAA;AAAA,YAAA,SAAA,wBAAA,CAAA;AAAS,aAAA,YAAY,OAAA,uBAAA,CAAwB;AAAE,aAAA,sBAAE,OAAA,aAAA,CAAc;IAAA,CAAA;AAC7F,IAAA,iBAAA,IAAA,oCAAA;AACF,IAAA,uBAAA;AACA,IAAA,yBAAA,IAAA,UAAA,GAAA;AAAkC,IAAA,qBAAA,SAAA,SAAA,mEAAA;AAAA,MAAA,wBAAA,IAAA;AAAA,YAAA,SAAA,wBAAA,CAAA;AAAA,aAAA,sBAAS,OAAA,aAAA,CAAc;IAAA,CAAA;AACvD,IAAA,iBAAA,IAAA,UAAA;AACF,IAAA,uBAAA,EAAS,EACL;;;;;AAnGM,IAAA,oBAAA,CAAA;AAAA,IAAA,4BAAA,OAAA,uBAAA,EAAA,GAAA;AAIA,IAAA,oBAAA,CAAA;AAAA,IAAA,4BAAA,OAAA,uBAAA,EAAA,WAAA;AAIA,IAAA,oBAAA,CAAA;AAAA,IAAA,4BAAA,OAAA,uBAAA,EAAA,iBAAA;AAEkB,IAAA,oBAAA;AAAA,IAAA,qBAAA,QAAA,OAAA,uBAAA,EAAA,oBAAA;AAMlB,IAAA,oBAAA,CAAA;AAAA,IAAA,4BAAA,OAAA,uBAAA,EAAA,UAAA;AASA,IAAA,oBAAA,CAAA;AAAA,IAAA,4BAAA,OAAA,YAAA,OAAA,uBAAA,EAAA,WAAA,CAAA;AAIA,IAAA,oBAAA,CAAA;AAAA,IAAA,4BAAA,OAAA,YAAA,OAAA,uBAAA,EAAA,WAAA,CAAA;AAIA,IAAA,oBAAA,CAAA;AAAA,IAAA,4BAAA,OAAA,YAAA,OAAA,uBAAA,EAAA,YAAA,CAAA;AAEkB,IAAA,oBAAA;AAAA,IAAA,qBAAA,QAAA,OAAA,uBAAA,EAAA,0BAAA,OAAA,uBAAA,EAAA,2BAAA,OAAA,uBAAA,EAAA,YAAA;AAIA,IAAA,oBAAA;AAAA,IAAA,qBAAA,QAAA,OAAA,aAAA,OAAA,uBAAA,CAAA,CAAA;AAMlB,IAAA,oBAAA,CAAA;AAAA,IAAA,qBAAA,OAAA,eAAA,OAAA,uBAAA,CAAA,CAAA;AAAoD,IAAA,oBAAA;AAAA,IAAA,6BAAA,IAAA,OAAA,uBAAA,EAAA,OAAA,YAAA;AAElC,IAAA,oBAAA;AAAA,IAAA,qBAAA,UAAA,WAAA,OAAA,uBAAA,MAAA,OAAA,OAAA,SAAA,4BAAA,OAAA,uBAAA,EAAA,0BAAA,CAAA;AAYA,IAAA,oBAAA,CAAA;AAAA,IAAA,qBAAA,QAAA,OAAA,uBAAA,EAAA,aAAA;AAIA,IAAA,oBAAA;AAAA,IAAA,qBAAA,QAAA,OAAA,uBAAA,EAAA,MAAA;AAIA,IAAA,oBAAA;AAAA,IAAA,qBAAA,QAAA,OAAA,uBAAA,EAAA,KAAA;AAIA,IAAA,oBAAA;AAAA,IAAA,qBAAA,QAAA,OAAA,uBAAA,EAAA,OAAA;AAOC,IAAA,oBAAA;AAAA,IAAA,qBAAA,QAAA,OAAA,uBAAA,EAAA,UAAA,OAAA,uBAAA,EAAA,OAAA,SAAA,CAAA;;;;;;AA9FrC,IAAA,yBAAA,GAAA,OAAA,EAAA;AAAiD,IAAA,qBAAA,SAAA,SAAA,yDAAA;AAAA,MAAA,wBAAA,GAAA;AAAA,YAAA,SAAA,wBAAA;AAAA,aAAA,sBAAS,OAAA,aAAA,CAAc;IAAA,CAAA;AACtE,IAAA,yBAAA,GAAA,OAAA,EAAA;AAAyC,IAAA,qBAAA,SAAA,SAAA,uDAAA,QAAA;AAAA,MAAA,wBAAA,GAAA;AAAA,aAAA,sBAAS,OAAA,gBAAA,CAAwB;IAAA,CAAA;AACxE,IAAA,yBAAA,GAAA,OAAA,EAAA,EAA0B,GAAA,IAAA;AACpB,IAAA,iBAAA,GAAA,uBAAA;AAAkB,IAAA,uBAAA;AACtB,IAAA,yBAAA,GAAA,UAAA,EAAA;AAA+D,IAAA,qBAAA,SAAA,SAAA,4DAAA;AAAA,MAAA,wBAAA,GAAA;AAAA,YAAA,SAAA,wBAAA;AAAA,aAAA,sBAAS,OAAA,aAAA,CAAc;IAAA,CAAA;AACpF,IAAA,iBAAA,GAAA,iBAAA;AACF,IAAA,uBAAA,EAAS;AAGX,IAAA,qBAAA,GAAA,yCAAA,IAAA,IAAA,OAAA,EAAA;AA4GF,IAAA,uBAAA,EAAM;;;;AA5GkC,IAAA,oBAAA,CAAA;AAAA,IAAA,qBAAA,QAAA,OAAA,uBAAA,CAAA;;;;;AAkOlC,IAAA,yBAAA,GAAA,OAAA,GAAA,EAAsD,GAAA,OAAA,GAAA,EAC5B,GAAA,QAAA;AACd,IAAA,iBAAA,CAAA;AAAgE,IAAA,uBAAA;AACxE,IAAA,yBAAA,GAAA,SAAA,GAAA;AAA0B,IAAA,iBAAA,CAAA;AAAoC,IAAA,uBAAA,EAAQ,EAClE;;;;AAFI,IAAA,oBAAA,CAAA;AAAA,IAAA,6BAAA,mCAAA,OAAA,YAAA,OAAA,iBAAA,GAAA,EAAA;AACkB,IAAA,oBAAA,CAAA;AAAA,IAAA,6BAAA,SAAA,OAAA,iBAAA,YAAA;;;;;AA8CtB,IAAA,yBAAA,GAAA,UAAA,GAAA;AACE,IAAA,iBAAA,CAAA;AACF,IAAA,uBAAA;;;;AAFiD,IAAA,qBAAA,SAAA,QAAA,EAAA;AAC/C,IAAA,oBAAA;AAAA,IAAA,6BAAA,KAAA,QAAA,KAAA,GAAA;;;;;AAUF,IAAA,yBAAA,GAAA,UAAA,GAAA;AACE,IAAA,iBAAA,CAAA;AACF,IAAA,uBAAA;;;;AAFyF,IAAA,qBAAA,SAAA,WAAA,EAAA;AACvF,IAAA,oBAAA;AAAA,IAAA,6BAAA,KAAA,WAAA,KAAA,GAAA;;;;;AAiBF,IAAA,yBAAA,GAAA,UAAA,GAAA;AACE,IAAA,iBAAA,CAAA;AACF,IAAA,uBAAA;;;;AAFiD,IAAA,qBAAA,SAAA,WAAA,EAAA;AAC/C,IAAA,oBAAA;AAAA,IAAA,6BAAA,KAAA,WAAA,MAAA,GAAA;;;;;AAUF,IAAA,yBAAA,GAAA,UAAA,GAAA;AACE,IAAA,iBAAA,CAAA;AACF,IAAA,uBAAA;;;;AAF+C,IAAA,qBAAA,SAAA,UAAA,EAAA;AAC7C,IAAA,oBAAA;AAAA,IAAA,6BAAA,KAAA,UAAA,KAAA,GAAA;;;;;AAaF,IAAA,yBAAA,GAAA,UAAA,GAAA;AACE,IAAA,iBAAA,CAAA;AACF,IAAA,uBAAA;;;;AAF+C,IAAA,qBAAA,SAAA,SAAA,EAAA;AAC7C,IAAA,oBAAA;AAAA,IAAA,6BAAA,KAAA,SAAA,SAAA,MAAA,SAAA,MAAA,KAAA;;;;;AAuBF,IAAA,yBAAA,GAAA,QAAA,GAAA;AAA8C,IAAA,iBAAA,GAAA,cAAA;AAAY,IAAA,uBAAA;;;;;AAP9D,IAAA,yBAAA,GAAA,OAAA,GAAA;AACE,IAAA,oBAAA,GAAA,OAAA,GAAA;AAIA,IAAA,yBAAA,GAAA,OAAA,GAAA,EAAwB,GAAA,QAAA,GAAA;AACG,IAAA,iBAAA,CAAA;AAAiB,IAAA,uBAAA;AAC1C,IAAA,qBAAA,GAAA,wDAAA,GAAA,GAAA,QAAA,GAAA;AACF,IAAA,uBAAA,EAAM;;;;;;AANJ,IAAA,oBAAA;AAAA,IAAA,qBAAA,OAAA,OAAA,gBAAA,mBAAA,UAAA,QAAA,GAAA,uBAAA,EAA0D,OAAA,UAAA,WAAA,eAAA;AAIjC,IAAA,oBAAA,CAAA;AAAA,IAAA,6BAAA,UAAA,QAAA,GAAA,EAAA;AACC,IAAA,oBAAA;AAAA,IAAA,qBAAA,QAAA,UAAA,MAAA;;;;;AAVlC,IAAA,yBAAA,GAAA,OAAA,GAAA,EAAuF,GAAA,IAAA;AACjF,IAAA,iBAAA,GAAA,oBAAA;AAAkB,IAAA,uBAAA;AACtB,IAAA,yBAAA,GAAA,OAAA,GAAA;AACE,IAAA,qBAAA,GAAA,iDAAA,GAAA,GAAA,OAAA,GAAA;AAUF,IAAA,uBAAA;AACA,IAAA,yBAAA,GAAA,SAAA,GAAA;AACE,IAAA,iBAAA,GAAA,2FAAA;AACF,IAAA,uBAAA,EAAQ;;;;AAbgD,IAAA,oBAAA,CAAA;AAAA,IAAA,qBAAA,WAAA,OAAA,gBAAA,EAAA,MAAA;;;;;AAqCpD,IAAA,yBAAA,GAAA,MAAA;AAA8C,IAAA,iBAAA,GAAA,6BAAA;AAAoB,IAAA,uBAAA;;;;;AAClE,IAAA,yBAAA,GAAA,MAAA;AAA4C,IAAA,iBAAA,GAAA,4BAAA;AAAqB,IAAA,uBAAA;;;;;AAKjE,IAAA,yBAAA,GAAA,MAAA;AAA8C,IAAA,iBAAA,GAAA,oBAAA;AAAiB,IAAA,uBAAA;;;;;AAc7D,IAAA,yBAAA,GAAA,QAAA,GAAA;AAAyC,IAAA,iBAAA,CAAA;AAAuE,IAAA,uBAAA;;;;AAAvE,IAAA,oBAAA;AAAA,IAAA,6BAAA,KAAA,OAAA,WAAA,IAAA,8BAAA,oBAAA,GAAA;;;;;;AAJ7C,IAAA,yBAAA,GAAA,OAAA,GAAA;AACE,IAAA,oBAAA,GAAA,OAAA,GAAA;AACA,IAAA,yBAAA,GAAA,OAAA,GAAA,EAAwB,GAAA,QAAA,GAAA;AACG,IAAA,iBAAA,CAAA;AAAe,IAAA,uBAAA;AACxC,IAAA,qBAAA,GAAA,wDAAA,GAAA,GAAA,QAAA,GAAA;AACA,IAAA,yBAAA,GAAA,UAAA,GAAA;AAAyC,IAAA,qBAAA,SAAA,SAAA,0EAAA;AAAA,YAAA,QAAA,wBAAA,IAAA,EAAA;AAAA,YAAA,SAAA,wBAAA,CAAA;AAAA,aAAA,sBAAS,OAAA,gBAAA,KAAA,CAAkB;IAAA,CAAA;AAAgC,IAAA,iBAAA,GAAA,MAAA;AAAC,IAAA,uBAAA,EAAS,EAC1G;;;;;;AALD,IAAA,oBAAA;AAAA,IAAA,qBAAA,OAAA,OAAA,gBAAA,QAAA,GAAA,uBAAA,EAA6B,OAAA,SAAA,IAAA;AAEP,IAAA,oBAAA,CAAA;AAAA,IAAA,4BAAA,SAAA,IAAA;AACC,IAAA,oBAAA;AAAA,IAAA,qBAAA,QAAA,UAAA,CAAA;;;;;AALhC,IAAA,yBAAA,GAAA,OAAA,GAAA;AACE,IAAA,qBAAA,GAAA,iDAAA,GAAA,GAAA,OAAA,GAAA;AAQF,IAAA,uBAAA;;;;AARkD,IAAA,oBAAA;AAAA,IAAA,qBAAA,WAAA,OAAA,kBAAA;;;;;;AA/R5D,IAAA,yBAAA,GAAA,OAAA,EAAA;AAA8C,IAAA,qBAAA,SAAA,SAAA,yDAAA;AAAA,MAAA,wBAAA,IAAA;AAAA,YAAA,SAAA,wBAAA;AAAA,aAAA,sBAAS,OAAA,UAAA,CAAW;IAAA,CAAA;AAChE,IAAA,yBAAA,GAAA,OAAA,GAAA;AAA2B,IAAA,qBAAA,SAAA,SAAA,uDAAA,QAAA;AAAA,MAAA,wBAAA,IAAA;AAAA,aAAA,sBAAS,OAAA,gBAAA,CAAwB;IAAA,CAAA;AAC1D,IAAA,yBAAA,GAAA,OAAA,EAAA,EAA0B,GAAA,IAAA;AACpB,IAAA,iBAAA,CAAA;AAAsD,IAAA,uBAAA;AAC1D,IAAA,yBAAA,GAAA,OAAA,GAAA,EAA2B,GAAA,UAAA,GAAA;AACsC,IAAA,qBAAA,SAAA,SAAA,4DAAA;AAAA,MAAA,wBAAA,IAAA;AAAA,YAAA,SAAA,wBAAA;AAAA,aAAA,sBAAS,OAAA,iBAAA,CAAkB;IAAA,CAAA;AACxF,IAAA,iBAAA,GAAA,wBAAA;AACF,IAAA,uBAAA;AACA,IAAA,yBAAA,GAAA,UAAA,EAAA;AAA+D,IAAA,qBAAA,SAAA,SAAA,4DAAA;AAAA,MAAA,wBAAA,IAAA;AAAA,YAAA,SAAA,wBAAA;AAAA,aAAA,sBAAS,OAAA,UAAA,CAAW;IAAA,CAAA;AACjF,IAAA,iBAAA,GAAA,iBAAA;AACF,IAAA,uBAAA,EAAS,EACL;AAGR,IAAA,yBAAA,IAAA,QAAA,KAAA,CAAA;AAA+C,IAAA,qBAAA,YAAA,SAAA,8DAAA;AAAA,MAAA,wBAAA,IAAA;AAAA,YAAA,kBAAA,sBAAA,EAAA;AAAA,YAAA,SAAA,wBAAA;AAAA,aAAA,sBAAY,OAAA,SAAA,eAAA,CAAqB;IAAA,CAAA;AAE9E,IAAA,yBAAA,IAAA,OAAA,GAAA,EAA0B,IAAA,IAAA;AACpB,IAAA,iBAAA,IAAA,sBAAA;AAAoB,IAAA,uBAAA;AAExB,IAAA,yBAAA,IAAA,OAAA,GAAA,EAAsB,IAAA,OAAA,GAAA,EACI,IAAA,OAAA;AACf,IAAA,iBAAA,IAAA,6BAAA;AAAqB,IAAA,uBAAA;AAC5B,IAAA,yBAAA,IAAA,SAAA,GAAA;AAGE,IAAA,2BAAA,iBAAA,SAAA,kEAAA,QAAA;AAAA,MAAA,wBAAA,IAAA;AAAA,YAAA,SAAA,wBAAA;AAAA,MAAA,6BAAA,OAAA,SAAA,mBAAA,MAAA,MAAA,OAAA,SAAA,oBAAA;AAAA,aAAA,sBAAA,MAAA;IAAA,CAAA;AAHF,IAAA,uBAAA,EAOE;AAEJ,IAAA,yBAAA,IAAA,OAAA,GAAA,EAAwB,IAAA,OAAA;AACf,IAAA,iBAAA,IAAA,6BAAA;AAAqB,IAAA,uBAAA;AAC5B,IAAA,yBAAA,IAAA,SAAA,GAAA;AAGE,IAAA,2BAAA,iBAAA,SAAA,kEAAA,QAAA;AAAA,MAAA,wBAAA,IAAA;AAAA,YAAA,SAAA,wBAAA;AAAA,MAAA,6BAAA,OAAA,SAAA,sBAAA,MAAA,MAAA,OAAA,SAAA,uBAAA;AAAA,aAAA,sBAAA,MAAA;IAAA,CAAA;AAHF,IAAA,uBAAA,EAME,EACE;AAGR,IAAA,yBAAA,IAAA,OAAA,GAAA,EAAsB,IAAA,OAAA,GAAA,EACI,IAAA,OAAA;AACf,IAAA,iBAAA,IAAA,kBAAA;AAAgB,IAAA,uBAAA;AACvB,IAAA,yBAAA,IAAA,SAAA,GAAA;AAGE,IAAA,2BAAA,iBAAA,SAAA,kEAAA,QAAA;AAAA,MAAA,wBAAA,IAAA;AAAA,YAAA,SAAA,wBAAA;AAAA,MAAA,6BAAA,OAAA,SAAA,KAAA,MAAA,MAAA,OAAA,SAAA,MAAA;AAAA,aAAA,sBAAA,MAAA;IAAA,CAAA;AAHF,IAAA,uBAAA,EAOE;AAEJ,IAAA,yBAAA,IAAA,OAAA,GAAA,EAAwB,IAAA,OAAA;AACf,IAAA,iBAAA,IAAA,kBAAA;AAAa,IAAA,uBAAA;AACpB,IAAA,yBAAA,IAAA,SAAA,GAAA;AAGE,IAAA,2BAAA,iBAAA,SAAA,kEAAA,QAAA;AAAA,MAAA,wBAAA,IAAA;AAAA,YAAA,SAAA,wBAAA;AAAA,MAAA,6BAAA,OAAA,SAAA,YAAA,MAAA,MAAA,OAAA,SAAA,aAAA;AAAA,aAAA,sBAAA,MAAA;IAAA,CAAA;AAHF,IAAA,uBAAA,EAME,EACE;AAGR,IAAA,yBAAA,IAAA,OAAA,GAAA,EAAwB,IAAA,OAAA;AACf,IAAA,iBAAA,IAAA,eAAA;AAAa,IAAA,uBAAA;AACpB,IAAA,yBAAA,IAAA,YAAA,GAAA;AAEE,IAAA,2BAAA,iBAAA,SAAA,qEAAA,QAAA;AAAA,MAAA,wBAAA,IAAA;AAAA,YAAA,SAAA,wBAAA;AAAA,MAAA,6BAAA,OAAA,SAAA,aAAA,MAAA,MAAA,OAAA,SAAA,cAAA;AAAA,aAAA,sBAAA,MAAA;IAAA,CAAA;AAKD,IAAA,uBAAA,EAAW,EACR;AAIR,IAAA,yBAAA,IAAA,OAAA,GAAA,EAA0B,IAAA,IAAA;AACpB,IAAA,iBAAA,IAAA,eAAA;AAAa,IAAA,uBAAA;AAEjB,IAAA,yBAAA,IAAA,OAAA,GAAA,EAAsB,IAAA,OAAA,GAAA,EACI,IAAA,OAAA;AACf,IAAA,iBAAA,IAAA,wBAAA;AAAsB,IAAA,uBAAA;AAC7B,IAAA,yBAAA,IAAA,SAAA,GAAA;AAGE,IAAA,2BAAA,iBAAA,SAAA,kEAAA,QAAA;AAAA,MAAA,wBAAA,IAAA;AAAA,YAAA,SAAA,wBAAA;AAAA,MAAA,6BAAA,OAAA,SAAA,WAAA,MAAA,MAAA,OAAA,SAAA,YAAA;AAAA,aAAA,sBAAA,MAAA;IAAA,CAAA;AAKA,IAAA,qBAAA,SAAA,SAAA,4DAAA;AAAA,MAAA,wBAAA,IAAA;AAAA,YAAA,SAAA,wBAAA;AAAA,aAAA,sBAAS,OAAA,aAAA,CAAc;IAAA,CAAA;AARzB,IAAA,uBAAA,EASE;AAEJ,IAAA,yBAAA,IAAA,OAAA,GAAA,EAAwB,IAAA,OAAA;AACf,IAAA,iBAAA,IAAA,yBAAA;AAAuB,IAAA,uBAAA;AAC9B,IAAA,yBAAA,IAAA,SAAA,GAAA;AAGE,IAAA,2BAAA,iBAAA,SAAA,kEAAA,QAAA;AAAA,MAAA,wBAAA,IAAA;AAAA,YAAA,SAAA,wBAAA;AAAA,MAAA,6BAAA,OAAA,SAAA,WAAA,MAAA,MAAA,OAAA,SAAA,YAAA;AAAA,aAAA,sBAAA,MAAA;IAAA,CAAA;AAKA,IAAA,qBAAA,SAAA,SAAA,4DAAA;AAAA,MAAA,wBAAA,IAAA;AAAA,YAAA,SAAA,wBAAA;AAAA,aAAA,sBAAS,OAAA,aAAA,CAAc;IAAA,CAAA;AARzB,IAAA,uBAAA,EASE,EACE;AAIR,IAAA,qBAAA,IAAA,0CAAA,GAAA,GAAA,OAAA,GAAA;AAOA,IAAA,yBAAA,IAAA,OAAA,GAAA,EAAsB,IAAA,OAAA,GAAA,EACI,IAAA,OAAA;AACf,IAAA,iBAAA,IAAA,eAAA;AAAa,IAAA,uBAAA;AACpB,IAAA,yBAAA,IAAA,SAAA,GAAA;AAGE,IAAA,2BAAA,iBAAA,SAAA,kEAAA,QAAA;AAAA,MAAA,wBAAA,IAAA;AAAA,YAAA,SAAA,wBAAA;AAAA,MAAA,6BAAA,OAAA,SAAA,OAAA,MAAA,MAAA,OAAA,SAAA,QAAA;AAAA,aAAA,sBAAA,MAAA;IAAA,CAAA;AAHF,IAAA,uBAAA,EAOE;AAEJ,IAAA,yBAAA,IAAA,OAAA,GAAA,EAAwB,IAAA,OAAA;AACf,IAAA,iBAAA,IAAA,kCAAA;AAAgC,IAAA,uBAAA;AACvC,IAAA,yBAAA,IAAA,SAAA,GAAA;AAGE,IAAA,2BAAA,iBAAA,SAAA,kEAAA,QAAA;AAAA,MAAA,wBAAA,IAAA;AAAA,YAAA,SAAA,wBAAA;AAAA,MAAA,6BAAA,OAAA,SAAA,mBAAA,MAAA,MAAA,OAAA,SAAA,oBAAA;AAAA,aAAA,sBAAA,MAAA;IAAA,CAAA;AAHF,IAAA,uBAAA;AAWA,IAAA,yBAAA,IAAA,SAAA,GAAA;AACE,IAAA,iBAAA,IAAA,wFAAA;AACF,IAAA,uBAAA,EAAQ,EACJ,EACF;AAIR,IAAA,yBAAA,IAAA,OAAA,GAAA,EAA0B,IAAA,IAAA;AACpB,IAAA,iBAAA,IAAA,kCAAA;AAA6B,IAAA,uBAAA;AAEjC,IAAA,yBAAA,IAAA,OAAA,GAAA,EAAsB,IAAA,OAAA,GAAA,EACI,IAAA,OAAA;AACf,IAAA,iBAAA,IAAA,gBAAA;AAAW,IAAA,uBAAA;AAClB,IAAA,yBAAA,IAAA,OAAA,GAAA,EAA+B,IAAA,UAAA,GAAA;AACF,IAAA,2BAAA,iBAAA,SAAA,mEAAA,QAAA;AAAA,MAAA,wBAAA,IAAA;AAAA,YAAA,SAAA,wBAAA;AAAA,MAAA,6BAAA,OAAA,SAAA,aAAA,MAAA,MAAA,OAAA,SAAA,cAAA;AAAA,aAAA,sBAAA,MAAA;IAAA,CAAA;AAAiE,IAAA,qBAAA,UAAA,SAAA,4DAAA,QAAA;AAAA,MAAA,wBAAA,IAAA;AAAA,YAAA,SAAA,wBAAA;AAAA,aAAA,sBAAU,OAAA,kBAAA,MAAA,CAAyB;IAAA,CAAA;AAC7H,IAAA,yBAAA,IAAA,UAAA,GAAA;AAAiB,IAAA,iBAAA,IAAA,iBAAA;AAAY,IAAA,uBAAA;AAC7B,IAAA,qBAAA,IAAA,6CAAA,GAAA,GAAA,UAAA,GAAA;AAGF,IAAA,uBAAA,EAAS,EACL;AAER,IAAA,yBAAA,IAAA,OAAA,GAAA,EAAwB,IAAA,OAAA;AACf,IAAA,iBAAA,EAAA;AAAoE,IAAA,uBAAA;AAC3E,IAAA,yBAAA,IAAA,OAAA,GAAA,EAA+B,IAAA,UAAA,GAAA;AACE,IAAA,2BAAA,iBAAA,SAAA,mEAAA,QAAA;AAAA,MAAA,wBAAA,IAAA;AAAA,YAAA,SAAA,wBAAA;AAAA,MAAA,6BAAA,OAAA,SAAA,iBAAA,MAAA,MAAA,OAAA,SAAA,kBAAA;AAAA,aAAA,sBAAA,MAAA;IAAA,CAAA;AAC7B,IAAA,yBAAA,IAAA,UAAA,GAAA;AAAiB,IAAA,iBAAA,IAAA,uCAAA;AAA+B,IAAA,uBAAA;AAChD,IAAA,qBAAA,IAAA,6CAAA,GAAA,GAAA,UAAA,GAAA;AAGF,IAAA,uBAAA;AAEA,IAAA,yBAAA,IAAA,SAAA,GAAA;AACE,IAAA,iBAAA,EAAA;AACF,IAAA,uBAAA,EAAQ,EACJ,EACF;AAGR,IAAA,yBAAA,IAAA,OAAA,GAAA,EAAsB,IAAA,OAAA,GAAA,EACI,IAAA,OAAA;AACf,IAAA,iBAAA,IAAA,UAAA;AAAQ,IAAA,uBAAA;AACf,IAAA,yBAAA,IAAA,OAAA,GAAA,EAA+B,IAAA,UAAA,GAAA;AACL,IAAA,2BAAA,iBAAA,SAAA,mEAAA,QAAA;AAAA,MAAA,wBAAA,IAAA;AAAA,YAAA,SAAA,wBAAA;AAAA,MAAA,6BAAA,OAAA,SAAA,UAAA,MAAA,MAAA,OAAA,SAAA,WAAA;AAAA,aAAA,sBAAA,MAAA;IAAA,CAAA;AACtB,IAAA,yBAAA,IAAA,UAAA,GAAA;AAAiB,IAAA,iBAAA,IAAA,iBAAA;AAAY,IAAA,uBAAA;AAC7B,IAAA,qBAAA,IAAA,6CAAA,GAAA,GAAA,UAAA,GAAA;AAGF,IAAA,uBAAA,EAAS,EACL;AAER,IAAA,yBAAA,IAAA,OAAA,GAAA,EAAwB,IAAA,OAAA;AACf,IAAA,iBAAA,IAAA,SAAA;AAAO,IAAA,uBAAA;AACd,IAAA,yBAAA,IAAA,OAAA,GAAA,EAA+B,IAAA,UAAA,GAAA;AACN,IAAA,2BAAA,iBAAA,SAAA,mEAAA,QAAA;AAAA,MAAA,wBAAA,IAAA;AAAA,YAAA,SAAA,wBAAA;AAAA,MAAA,6BAAA,OAAA,SAAA,SAAA,MAAA,MAAA,OAAA,SAAA,UAAA;AAAA,aAAA,sBAAA,MAAA;IAAA,CAAA;AACrB,IAAA,yBAAA,IAAA,UAAA,GAAA;AAAiB,IAAA,iBAAA,IAAA,iBAAA;AAAY,IAAA,uBAAA;AAC7B,IAAA,qBAAA,IAAA,6CAAA,GAAA,GAAA,UAAA,GAAA;AAGF,IAAA,uBAAA,EAAS,EACL,EACF;AAGR,IAAA,yBAAA,KAAA,OAAA,GAAA,EAAsB,KAAA,OAAA,GAAA,EACI,KAAA,OAAA;AACf,IAAA,iBAAA,KAAA,YAAA;AAAU,IAAA,uBAAA;AACjB,IAAA,yBAAA,KAAA,OAAA,GAAA,EAA+B,KAAA,UAAA,GAAA;AACJ,IAAA,2BAAA,iBAAA,SAAA,oEAAA,QAAA;AAAA,MAAA,wBAAA,IAAA;AAAA,YAAA,SAAA,wBAAA;AAAA,MAAA,6BAAA,OAAA,SAAA,WAAA,MAAA,MAAA,OAAA,SAAA,YAAA;AAAA,aAAA,sBAAA,MAAA;IAAA,CAAA;AAA+D,IAAA,qBAAA,UAAA,SAAA,6DAAA,QAAA;AAAA,MAAA,wBAAA,IAAA;AAAA,YAAA,SAAA,wBAAA;AAAA,aAAA,sBAAU,OAAA,gBAAA,MAAA,CAAuB;IAAA,CAAA;AACvH,IAAA,yBAAA,KAAA,UAAA,GAAA;AAAiB,IAAA,iBAAA,KAAA,iBAAA;AAAY,IAAA,uBAAA;AAC7B,IAAA,qBAAA,KAAA,8CAAA,GAAA,GAAA,UAAA,GAAA;AAGF,IAAA,uBAAA,EAAS,EACL,EACF,EACF;AAIR,IAAA,yBAAA,KAAA,OAAA,GAAA,EAA0B,KAAA,IAAA;AACpB,IAAA,iBAAA,KAAA,mBAAA;AAAiB,IAAA,uBAAA;AAGrB,IAAA,qBAAA,KAAA,2CAAA,GAAA,GAAA,OAAA,GAAA;AAoBA,IAAA,yBAAA,KAAA,OAAA,GAAA,EAAwB,KAAA,OAAA;AACf,IAAA,iBAAA,GAAA;AAA6D,IAAA,uBAAA;AAGpE,IAAA,yBAAA,KAAA,SAAA,KAAA,CAAA;AAKE,IAAA,qBAAA,UAAA,SAAA,4DAAA,QAAA;AAAA,MAAA,wBAAA,IAAA;AAAA,YAAA,SAAA,wBAAA;AAAA,aAAA,sBAAU,OAAA,sBAAA,MAAA,CAA6B;IAAA,CAAA;AALzC,IAAA,uBAAA;AASA,IAAA,yBAAA,KAAA,OAAA,GAAA,EAAkC,KAAA,UAAA,GAAA;AAI9B,IAAA,qBAAA,SAAA,SAAA,8DAAA;AAAA,MAAA,wBAAA,IAAA;AAAA,YAAA,gBAAA,sBAAA,GAAA;AAAA,aAAA,sBAAS,cAAA,MAAA,CAAiB;IAAA,CAAA;AAG1B,IAAA,qBAAA,KAAA,4CAAA,GAAA,GAAA,QAAA,GAAA,EAA8C,KAAA,4CAAA,GAAA,GAAA,QAAA,GAAA;AAEhD,IAAA,uBAAA;AAEA,IAAA,yBAAA,KAAA,SAAA,GAAA;AACE,IAAA,iBAAA,GAAA;AACA,IAAA,qBAAA,KAAA,4CAAA,GAAA,GAAA,QAAA,GAAA;AACF,IAAA,uBAAA,EAAQ;AAGV,IAAA,yBAAA,KAAA,SAAA,GAAA;AACE,IAAA,iBAAA,GAAA;AACF,IAAA,uBAAA;AAGA,IAAA,qBAAA,KAAA,2CAAA,GAAA,GAAA,OAAA,GAAA;AAUF,IAAA,uBAAA,EAAM;AAIR,IAAA,yBAAA,KAAA,OAAA,GAAA,EAA0B,KAAA,UAAA,GAAA;AACwB,IAAA,qBAAA,SAAA,SAAA,8DAAA;AAAA,MAAA,wBAAA,IAAA;AAAA,YAAA,SAAA,wBAAA;AAAA,aAAA,sBAAS,OAAA,UAAA,CAAW;IAAA,CAAA;AAClE,IAAA,iBAAA,KAAA,WAAA;AACF,IAAA,uBAAA;AACA,IAAA,yBAAA,KAAA,UAAA,GAAA;AACE,IAAA,iBAAA,GAAA;AACF,IAAA,uBAAA,EAAS,EACL,EACD,EACH;;;;;;AAlTE,IAAA,oBAAA,CAAA;AAAA,IAAA,6BAAA,IAAA,OAAA,WAAA,IAAA,aAAA,WAAA,aAAA;AAsBI,IAAA,oBAAA,EAAA;AAAA,IAAA,2BAAA,WAAA,OAAA,SAAA,iBAAA;AAWA,IAAA,oBAAA,CAAA;AAAA,IAAA,2BAAA,WAAA,OAAA,SAAA,oBAAA;AAaA,IAAA,oBAAA,CAAA;AAAA,IAAA,2BAAA,WAAA,OAAA,SAAA,GAAA;AAWA,IAAA,oBAAA,CAAA;AAAA,IAAA,2BAAA,WAAA,OAAA,SAAA,UAAA;AAWF,IAAA,oBAAA,CAAA;AAAA,IAAA,2BAAA,WAAA,OAAA,SAAA,WAAA;AAmBE,IAAA,oBAAA,CAAA;AAAA,IAAA,2BAAA,WAAA,OAAA,SAAA,SAAA;AAaA,IAAA,oBAAA,CAAA;AAAA,IAAA,2BAAA,WAAA,OAAA,SAAA,SAAA;AAWmB,IAAA,oBAAA;AAAA,IAAA,qBAAA,QAAA,OAAA,oBAAA,CAAA;AAanB,IAAA,oBAAA,CAAA;AAAA,IAAA,2BAAA,WAAA,OAAA,SAAA,KAAA;AAWA,IAAA,oBAAA,CAAA;AAAA,IAAA,2BAAA,WAAA,OAAA,SAAA,iBAAA;AAuB2B,IAAA,oBAAA,EAAA;AAAA,IAAA,2BAAA,WAAA,OAAA,SAAA,WAAA;AAED,IAAA,oBAAA,CAAA;AAAA,IAAA,qBAAA,WAAA,OAAA,mBAAA,CAAA;AAOrB,IAAA,oBAAA,CAAA;AAAA,IAAA,6BAAA,yBAAA,OAAA,uBAAA,EAAA,QAAA,eAAA;AAE0B,IAAA,oBAAA,CAAA;AAAA,IAAA,2BAAA,WAAA,OAAA,SAAA,eAAA;AAEF,IAAA,oBAAA,CAAA;AAAA,IAAA,qBAAA,WAAA,OAAA,uBAAA,CAAA,EAA6B,gBAAA,OAAA,sBAAA;AAMxD,IAAA,oBAAA,CAAA;AAAA,IAAA,6BAAA,YAAA,OAAA,uBAAA,EAAA,QAAA,kCAAA;AAUsB,IAAA,oBAAA,CAAA;AAAA,IAAA,2BAAA,WAAA,OAAA,SAAA,QAAA;AAEK,IAAA,oBAAA,CAAA;AAAA,IAAA,qBAAA,WAAA,OAAA,gBAAA,CAAA;AASN,IAAA,oBAAA,CAAA;AAAA,IAAA,2BAAA,WAAA,OAAA,SAAA,OAAA;AAEK,IAAA,oBAAA,CAAA;AAAA,IAAA,qBAAA,WAAA,OAAA,eAAA,CAAA;AAYH,IAAA,oBAAA,CAAA;AAAA,IAAA,2BAAA,WAAA,OAAA,SAAA,SAAA;AAEE,IAAA,oBAAA,CAAA;AAAA,IAAA,qBAAA,WAAA,OAAA,gBAAA,CAAA;AAc3B,IAAA,oBAAA,CAAA;AAAA,IAAA,qBAAA,QAAA,OAAA,WAAA,OAAA,WAAA,OAAA,gBAAA,MAAA,OAAA,OAAA,SAAA,UAAA,OAAA,OAAA,SAAA,OAAA,OAAA;AAqBG,IAAA,oBAAA,CAAA;AAAA,IAAA,4BAAA,OAAA,WAAA,IAAA,qBAAA,mBAAA;AAiBH,IAAA,oBAAA,CAAA;AAAA,IAAA,qBAAA,YAAA,OAAA,mBAAA,UAAA,CAAA;AAEO,IAAA,oBAAA;AAAA,IAAA,qBAAA,QAAA,OAAA,mBAAA,WAAA,CAAA;AACA,IAAA,oBAAA;AAAA,IAAA,qBAAA,QAAA,OAAA,mBAAA,SAAA,CAAA;AAIP,IAAA,oBAAA,CAAA;AAAA,IAAA,6BAAA,KAAA,OAAA,mBAAA,QAAA,gCAAA;AACO,IAAA,oBAAA;AAAA,IAAA,qBAAA,QAAA,OAAA,mBAAA,UAAA,CAAA;AAKT,IAAA,oBAAA,CAAA;AAAA,IAAA,6BAAA,KAAA,OAAA,WAAA,IAAA,+DAAA,iDAAA,GAAA;AAIoC,IAAA,oBAAA;AAAA,IAAA,qBAAA,QAAA,OAAA,mBAAA,SAAA,CAAA;AAkBM,IAAA,oBAAA,CAAA;AAAA,IAAA,qBAAA,YAAA,CAAA,gBAAA,SAAA,OAAA,UAAA,CAAA;AAC5C,IAAA,oBAAA;AAAA,IAAA,6BAAA,KAAA,OAAA,WAAA,IAAA,aAAA,WAAA,GAAA;;;ADrpBN,IAAO,oBAAP,MAAO,mBAAiB;EA4DlB;EACA;EACA;EACA;EACA;EACA;EACA;EACD;;EAjET,WAAW,OAAkB,CAAA,CAAE;EAC/B,YAAY,OAAO,KAAK;EACxB,QAAQ,OAAsB,IAAI;EAClC,WAAW,OAAO,KAAK;EACvB,aAAa,OAAO,KAAK;EACzB,kBAAkB,OAAuB,IAAI;EAC7C,cAAc,OAAO,EAAE;;EAGvB,qBAAqB,OAA4B,CAAA,CAAE;EACnD,yBAAyB,OAAgC,CAAA,CAAE;EAC3D,kBAAkB,OAAyB,CAAA,CAAE;EAC7C,iBAAiB,OAAwB,CAAA,CAAE;EAC3C,kBAAkB,OAA0B,CAAA,CAAE;;EAG9C,qBAA6B,CAAA;EAC7B,mBAA6B,CAAA;;EAG7B,UAAU;IACR,aAAa;IACb,aAAa;IACb,YAAY;;;EAId,WAAW;IACT,mBAAmB;IACnB,sBAAsB;IACtB,YAAY;IACZ,KAAK;IACL,aAAa;IACb,WAAW;IACX,WAAW;IACX,OAAO;IACP,mBAAmB;IACnB,aAAa;IACb,iBAAiB;IACjB,UAAU;IACV,SAAS;IACT,WAAW;;;EAIb,oBAA4B;EAC5B,kBAA0B;;EAG1B,cAAc,OAAO,CAAC;EACtB,eAAe;;EAGf,cAAc,OAAO,KAAK;EAC1B,yBAAyB,OAAuB,IAAI;EACpD,iBAAiB,OAAO,KAAK;EAE7B,YACU,gBACA,kBACA,sBACA,eACA,cACA,gBACA,aACD,iBAAgC;AAP/B,SAAA,iBAAA;AACA,SAAA,mBAAA;AACA,SAAA,uBAAA;AACA,SAAA,gBAAA;AACA,SAAA,eAAA;AACA,SAAA,iBAAA;AACA,SAAA,cAAA;AACD,SAAA,kBAAA;EACN;EAEH,WAAQ;AACN,YAAQ,IAAI,yDAAkD;AAC9D,SAAK,sBAAqB;AAC1B,SAAK,SAAQ;AACb,SAAK,cAAa;EACpB;EAEA,wBAAqB;AACnB,YAAQ,IAAI,2CAAoC;AAGhD,SAAK,iBAAiB,YAAW,EAAG,UAAU;MAC5C,MAAM,CAAC,aAAY;AACjB,gBAAQ,IAAI,8CAAsC,QAAQ;MAC5D;MACA,OAAO,CAAC,UAAS;AACf,gBAAQ,MAAM,gCAA2B,KAAK;AAC9C,gBAAQ,MAAM,kBAAa,MAAM,MAAM;AACvC,gBAAQ,MAAM,mBAAc,MAAM,OAAO;AACzC,gBAAQ,MAAM,eAAU,MAAM,GAAG;MACnC;KACD;EACH;;EAGA,QAAQ,SAAS,MAAK;AACpB,UAAM,WAAW,KAAK,SAAQ;AAC9B,WAAO;MACL,OAAO,SAAS;MAChB,QAAQ,SAAS,OAAO,OAAK,EAAE,QAAQ,CAAC,EAAE;MAC1C,YAAY,SAAS,OAAO,OAAK,EAAE,UAAU,CAAC,EAAE;MAChD,YAAY,SAAS,OAAO,CAAC,KAAK,MAAK;AAErC,cAAM,YAAY,EAAE,oBAAoB,EAAE;AAC1C,eAAO,MAAO,YAAY,EAAE;MAC9B,GAAG,CAAC;;EAER,CAAC;EAED,mBAAmB,SAAS,MAAK;AAC/B,UAAM,WAAW,KAAK,SAAQ;AAC9B,UAAM,QAAQ,KAAK,YAAW,EAAG,YAAW;AAE5C,QAAI,CAAC;AAAO,aAAO;AAEnB,WAAO,SAAS,OAAO,aACrB,QAAQ,IAAI,YAAW,EAAG,SAAS,KAAK,KACxC,QAAQ,aAAa,YAAW,EAAG,SAAS,KAAK,KACjD,QAAQ,kBAAkB,YAAW,EAAG,SAAS,KAAK,CAAC;EAE3D,CAAC;EAED,sBAAsB,SAAS,MAAK;AAClC,UAAM,WAAW,KAAK,iBAAgB;AACtC,UAAM,SAAS,KAAK,YAAW,IAAK,KAAK,KAAK;AAC9C,UAAM,MAAM,QAAQ,KAAK;AACzB,WAAO,SAAS,MAAM,OAAO,GAAG;EAClC,CAAC;;EAGD,WAAQ;AACN,YAAQ,IAAI,uEAA0D;AACtE,SAAK,UAAU,IAAI,IAAI;AACvB,SAAK,MAAM,IAAI,IAAI;AAGnB,UAAM,SAAS,KAAK,YAAY,gBAAe;AAC/C,UAAM,cAAc,KAAK,YAAY,eAAc;AACnD,UAAM,QAAQ,aAAa,QAAQ,YAAY,KAAK,aAAa,QAAQ,OAAO;AAEhF,YAAQ,IAAI,0DAAiD;AAC7D,YAAQ,IAAI,wBAAwB,MAAM;AAC1C,YAAQ,IAAI,oBAAoB,WAAW;AAC3C,YAAQ,IAAI,yBAAsB,CAAC,CAAC,KAAK;AACzC,YAAQ,IAAI,0BAA0B,OAAO,KAAK,YAAY,CAAC;AAE/D,QAAI,CAAC,aAAa,IAAI;AACpB,cAAQ,MAAM,sEAA8D;AAC5E,cAAQ,IAAI,oBAAoB,WAAW;AAC3C,WAAK,MAAM,IAAI,yDAAsD;AACrE,WAAK,UAAU,IAAI,KAAK;AACxB;IACF;AAEA,YAAQ,IAAI,gFAAyE,YAAY,EAAE,EAAE;AAGrG,SAAK,eAAe,iBAAiB,YAAY,EAAE,EAAE,UAAU;MAC7D,MAAM,CAAC,aAAY;AACjB,gBAAQ,IAAI,6BAAwB,SAAS,MAAM,oDAAiD,QAAQ;AAG5G,iBAAS,QAAQ,aAAU;AACzB,kBAAQ,IAAI,2BAAe,QAAQ,GAAG,KAAK;YACzC,IAAI,QAAQ;YACZ,QAAQ,QAAQ;YAChB,oBAAoB,QAAQ;YAC5B,aAAa,QAAQ,QAAQ,UAAU;WACxC;QACH,CAAC;AAGD,aAAK,SAAS,IAAI,QAAQ;AAC1B,aAAK,UAAU,IAAI,KAAK;MAC1B;MACA,OAAO,CAAC,UAAS;AACf,gBAAQ,MAAM,qEAAgE,KAAK;AACnF,gBAAQ,IAAI,eAAe,MAAM,MAAM;AACvC,gBAAQ,IAAI,gBAAgB,MAAM,OAAO;AACzC,gBAAQ,IAAI,YAAY,MAAM,GAAG;AAGjC,YAAI,eAAe;AACnB,YAAI,MAAM,WAAW,KAAK;AACxB,yBAAe;QACjB,WAAW,MAAM,WAAW,KAAK;AAC/B,yBAAe;QACjB,WAAW,MAAM,WAAW,GAAG;AAC7B,yBAAe;QACjB;AAEA,aAAK,MAAM,IAAI,YAAY;AAC3B,aAAK,UAAU,IAAI,KAAK;MAC1B;KACD;EACH;EAEA,gBAAa;AACX,YAAQ,IAAI,mDAAyC;AAGrD,YAAQ,IAAI,2CAAiC;AAC7C,SAAK,iBAAiB,OAAM,EAAG,UAAU;MACvC,MAAM,CAAC,eAAc;AACnB,gBAAQ,IAAI,mCAAwB,UAAU;AAE9C,cAAM,sBAAsB,WAAW,IAAI,CAAC,SAAc;UACxD,IAAI,IAAI;UACR,KAAK,IAAI;UACT,qBAAqB,IAAI,uBAAuB;UAChD;AACF,gBAAQ,IAAI,sCAA2B,mBAAmB;AAC1D,aAAK,mBAAmB,IAAI,mBAAmB;MACjD;MACA,OAAO,CAAC,UAAS;AACf,gBAAQ,MAAM,uDAA+C,KAAK;AAClE,aAAK,mBAAmB,IAAI,CAAA,CAAE;MAChC;KACD;AAGD,YAAQ,IAAI,qCAA8B;AAC1C,SAAK,cAAc,OAAM,EAAG,UAAU;MACpC,MAAM,CAAC,YAAW;AAChB,gBAAQ,IAAI,6BAAqB,OAAO;AAExC,cAAM,mBAAmB,QAAQ,IAAI,CAAC,YAAiB;UACrD,IAAI,OAAO;UACX,MAAM,OAAO;UACb,MAAM,OAAO,QAAQ;UACrB;AACF,gBAAQ,IAAI,gCAAwB,gBAAgB;AACpD,aAAK,gBAAgB,IAAI,gBAAgB;MAC3C;MACA,OAAO,CAAC,UAAS;AACf,gBAAQ,MAAM,iDAA4C,KAAK;AAC/D,aAAK,gBAAgB,IAAI,CAAA,CAAE;MAC7B;KACD;AAGD,YAAQ,IAAI,oCAA6B;AACzC,SAAK,aAAa,OAAM,EAAG,UAAU;MACnC,MAAM,CAAC,WAAU;AACf,gBAAQ,IAAI,4BAAoB,MAAM;AAEtC,cAAM,kBAAkB,OAAO,IAAI,CAAC,WAAgB;UAClD,IAAI,MAAM;UACV,KAAK,MAAM;UACX,aAAa,MAAM;UACnB,UAAU,MAAM,YAAY;UAC5B;AACF,gBAAQ,IAAI,+BAAuB,eAAe;AAClD,aAAK,eAAe,IAAI,eAAe;MACzC;MACA,OAAO,CAAC,UAAS;AACf,gBAAQ,MAAM,gDAA2C,KAAK;AAC9D,aAAK,eAAe,IAAI,CAAA,CAAE;MAC5B;KACD;AAGD,YAAQ,IAAI,sCAA+B;AAC3C,SAAK,eAAe,OAAM,EAAG,UAAU;MACrC,MAAM,CAAC,SAAQ;AACb,gBAAQ,IAAI,6BAAqB,IAAI;AAErC,cAAM,gBAAgB,KAAK,IAAI,CAAC,SAAc;UAC5C,IAAI,IAAI;UACR,SAAS,IAAI;UACb,MAAM,IAAI;UACV,UAAU,IAAI;UACd;AACF,gBAAQ,IAAI,gCAAwB,aAAa;AACjD,aAAK,gBAAgB,IAAI,aAAa;MACxC;MACA,OAAO,CAAC,UAAS;AACf,gBAAQ,MAAM,kDAA6C,KAAK;AAChE,aAAK,gBAAgB,IAAI,CAAA,CAAE;MAC7B;KACD;AAED,YAAQ,IAAI,+DAAyD;EACvE;;EAGA,SAAS,OAAU;AACjB,SAAK,YAAY,IAAI,MAAM,OAAO,KAAK;AACvC,SAAK,YAAY,IAAI,CAAC;EACxB;EAEA,cAAW;AACT,SAAK,WAAW,IAAI,KAAK;AACzB,SAAK,gBAAgB,IAAI,IAAI;AAC7B,SAAK,SAAS,IAAI,IAAI;AACtB,SAAK,oBAAmB;AACxB,SAAK,cAAa;AAGlB,QAAI,KAAK,mBAAkB,EAAG,WAAW,GAAG;AAC1C,cAAQ,IAAI,wDAA2C;AACvD,WAAK,cAAa;IACpB;AAGA,YAAQ,IAAI,iEAAqD;AACjE,YAAQ,IAAI,oBAAiB,KAAK,mBAAkB,CAAE;AACtD,YAAQ,IAAI,cAAc,KAAK,gBAAe,CAAE;AAChD,YAAQ,IAAI,aAAa,KAAK,eAAc,CAAE;AAC9C,YAAQ,IAAI,eAAe,KAAK,gBAAe,CAAE;EACnD;EAEA,gBAAa;AACX,SAAK,WAAW;MACd,mBAAmB;MACnB,sBAAsB,KAAK,wBAAuB;MAClD,YAAY;MACZ,KAAK;MACL,aAAa;MACb,WAAW;MACX,WAAW;MACX,OAAO;MACP,mBAAmB;;MACnB,aAAa;MACb,iBAAiB;MACjB,UAAU;MACV,SAAS;MACT,WAAW;;AAEb,YAAQ,IAAI,0EAA0D;EACxE;EAEA,0BAAuB;AACrB,UAAM,YAAY,KAAK,IAAG;AAC1B,UAAM,SAAS,KAAK,MAAM,KAAK,OAAM,IAAK,GAAI;AAC9C,WAAO,QAAQ,SAAS,IAAI,MAAM;EACpC;EAEA,YAAY,SAAgB;AAC1B,YAAQ,IAAI,kDAAsC,OAAO;AACzD,YAAQ,IAAI,4BAAqB,QAAQ,EAAE;AAC3C,YAAQ,IAAI,6BAAsB,QAAQ,GAAG;AAG7C,SAAK,WAAW,IAAI,IAAI;AACxB,YAAQ,IAAI,qCAA0B,KAAK,WAAU,CAAE;AAGvD,SAAK,SAAS,IAAI,IAAI;AACtB,YAAQ,IAAI,iCAAyB,KAAK,SAAQ,CAAE;AAGpD,QAAI,KAAK,mBAAkB,EAAG,WAAW,GAAG;AAC1C,cAAQ,IAAI,+CAAqC;AACjD,WAAK,cAAa;IACpB;AAGA,YAAQ,IAAI,yEAAgE;AAC5E,SAAK,eAAe,QAAQ,QAAQ,EAAE,EAAE,UAAU;MAChD,MAAM,CAAC,mBAAkB;AACvB,gBAAQ,IAAI,mDAAwC,cAAc;AAGlE,aAAK,gBAAgB,IAAI,cAAc;AACvC,gBAAQ,IAAI,6CAAkC,KAAK,gBAAe,CAAE;AAGpE,mBAAW,MAAK;AACd,kBAAQ,IAAI,wEAA2D;AACvE,eAAK,wBAAwB,cAAc;QAC7C,GAAG,GAAG;MACR;MACA,OAAO,CAAC,UAAS;AACf,gBAAQ,MAAM,+DAAuD,KAAK;AAE1E,aAAK,gBAAgB,IAAI,OAAO;AAChC,mBAAW,MAAK;AACd,eAAK,wBAAwB,OAAO;QACtC,GAAG,GAAG;MACR;KACD;EACH;EAEA,wBAAwB,SAAgB;AACtC,YAAQ,IAAI,kFAAqE,OAAO;AACxF,YAAQ,IAAI,+CAAkC;AAC9C,YAAQ,IAAI,SAAS,QAAQ,EAAE;AAC/B,YAAQ,IAAI,UAAU,QAAQ,GAAG;AACjC,YAAQ,IAAI,oBAAoB,QAAQ,WAAW;AACnD,YAAQ,IAAI,oBAAoB,QAAQ,WAAW;AACnD,YAAQ,IAAI,YAAY,QAAQ,KAAK;AACrC,YAAQ,IAAI,gBAAgB,QAAQ,QAAQ;AAC5C,YAAQ,IAAI,eAAe,QAAQ,OAAO;AAC1C,YAAQ,IAAI,kBAAkB,QAAQ,SAAS;AAC/C,YAAQ,IAAI,2BAAwB,QAAQ,eAAe;AAG3D,eAAW,MAAK;AACd,cAAQ,IAAI,iEAA0D;AAGtE,cAAQ,IAAI,6CAAsC;AAClD,WAAK,SAAS,oBAAoB,QAAQ;AAC1C,cAAQ,IAAI,gCAA0B,KAAK,SAAS,iBAAiB;AAErE,WAAK,SAAS,uBAAuB,QAAQ,wBAAwB;AACrE,cAAQ,IAAI,kCAA4B,KAAK,SAAS,oBAAoB;AAE1E,WAAK,SAAS,aAAa,QAAQ;AACnC,cAAQ,IAAI,uBAAoB,KAAK,SAAS,UAAU;AAExD,WAAK,SAAS,MAAM,QAAQ;AAC5B,cAAQ,IAAI,UAAU,KAAK,SAAS,GAAG;AAEvC,WAAK,SAAS,cAAc,QAAQ,eAAe;AACnD,cAAQ,IAAI,kBAAkB,KAAK,SAAS,WAAW;AAEvD,WAAK,SAAS,YAAY,QAAQ,YAAY,SAAQ;AACtD,cAAQ,IAAI,iBAAiB,KAAK,SAAS,SAAS;AAEpD,WAAK,SAAS,YAAY,QAAQ,YAAY,SAAQ;AACtD,cAAQ,IAAI,iBAAiB,KAAK,SAAS,SAAS;AAEpD,WAAK,SAAS,QAAQ,QAAQ,MAAM,SAAQ;AAC5C,cAAQ,IAAI,YAAY,KAAK,SAAS,KAAK;AAE3C,WAAK,SAAS,oBAAoB,QAAQ,yBAAyB,SAAQ,KAAM;AACjF,cAAQ,IAAI,yBAAyB,KAAK,SAAS,iBAAiB;AAGpE,cAAQ,IAAI,wCAAiC;AAC7C,WAAK,SAAS,WAAW,QAAQ,SAAS,SAAQ;AAClD,cAAQ,IAAI,gBAAgB,KAAK,SAAS,QAAQ;AAElD,WAAK,SAAS,UAAU,QAAQ,QAAQ,SAAQ;AAChD,cAAQ,IAAI,eAAe,KAAK,SAAS,OAAO;AAEhD,WAAK,SAAS,YAAY,QAAQ,UAAU,SAAQ;AACpD,cAAQ,IAAI,kBAAkB,KAAK,SAAS,SAAS;AAGrD,cAAQ,IAAI,iEAAoD;AAChE,cAAQ,IAAI,mCAAgC,QAAQ,aAAa;AACjE,cAAQ,IAAI,2BAAwB,QAAQ,eAAe;AAE3D,UAAI,QAAQ,eAAe,eAAe,QAAQ,iBAAiB;AAEjE,cAAM,cAAc,QAAQ,eAAe;AAC3C,YAAI,aAAa;AACf,eAAK,SAAS,cAAc,YAAY,SAAQ;AAChD,kBAAQ,IAAI,2CAA6B,KAAK,SAAS,WAAW;AAGlE,eAAK,mBAAmB,WAAW;AAGnC,qBAAW,MAAK;AACd,iBAAK,SAAS,kBAAkB,QAAQ,gBAAgB,SAAQ;AAChE,oBAAQ,IAAI,gDAAkC,KAAK,SAAS,eAAe;UAC7E,GAAG,GAAG;QACR,OAAO;AAEL,kBAAQ,IAAI,kEAAwD;AACpE,eAAK,SAAS,kBAAkB,QAAQ,gBAAgB,SAAQ;QAClE;MACF,OAAO;AACL,gBAAQ,IAAI,kEAAkD;MAChE;AAGA,WAAK,kBAAkB,KAAK,gBAAe,EAAG,KAAK,OAAK,EAAE,OAAO,QAAQ,SAAS,GAAG,QAAQ;AAC7F,WAAK,iBAAgB;AAErB,cAAQ,IAAI,6DAAkD;AAC9D,cAAQ,IAAI,wCAA8B,KAAK,QAAQ;IACzD,GAAG,GAAG;EACR;EAIQ,mBAAmB,aAAmB;AAC5C,SAAK,qBAAqB,eAAe,WAAW,EAAE,UAAU;MAC9D,MAAM,CAAC,mBAAkB;AACvB,gBAAQ,IAAI,0DAA4C,cAAc;AACtE,cAAM,sBAAsB,MAAM,QAAQ,cAAc,IAAI,iBAAiB,CAAA;AAC7E,aAAK,uBAAuB,IAAI,mBAAmB;MACrD;MACA,OAAO,CAAC,UAAS;AACf,gBAAQ,MAAM,4EAAiE,KAAK;AACpF,aAAK,uBAAuB,IAAI,CAAA,CAAE;MACpC;KACD;EACH;EAEA,YAAS;AACP,SAAK,SAAS,IAAI,KAAK;AACvB,SAAK,WAAW,IAAI,KAAK;AACzB,SAAK,gBAAgB,IAAI,IAAI;AAC7B,SAAK,oBAAmB;AACxB,SAAK,cAAa;EACpB;EAEA,mBAAmB,SAAgB;AACjC,YAAQ,IAAI,kEAAmD,QAAQ,EAAE;AAEzE,SAAK,eAAe,IAAI,IAAI;AAG5B,SAAK,eAAe,QAAQ,QAAQ,EAAE,EAAE,UAAU;MAChD,MAAM,CAAC,mBAAkB;AACvB,gBAAQ,IAAI,qDAA0C,cAAc;AACpE,gBAAQ,IAAI,kCAAwB;AACpC,gBAAQ,IAAI,aAAa,eAAe,MAAM;AAC9C,gBAAQ,IAAI,YAAY,eAAe,KAAK;AAC5C,gBAAQ,IAAI,wBAAqB,eAAe,aAAa;AAC7D,gBAAQ,IAAI,eAAe,eAAe,OAAO;AAEjD,aAAK,uBAAuB,IAAI,cAAc;AAC9C,aAAK,YAAY,IAAI,IAAI;AACzB,aAAK,eAAe,IAAI,KAAK;MAC/B;MACA,OAAO,CAAC,UAAS;AACf,gBAAQ,MAAM,+DAAuD,KAAK;AAE1E,aAAK,uBAAuB,IAAI,OAAO;AACvC,aAAK,YAAY,IAAI,IAAI;AACzB,aAAK,eAAe,IAAI,KAAK;MAC/B;KACD;EACH;EAEA,eAAY;AACV,SAAK,YAAY,IAAI,KAAK;AAC1B,SAAK,uBAAuB,IAAI,IAAI;EACtC;EAEA,cAAc,SAAgB;AAC5B,QAAI,QAAQ,6CAAuC,QAAQ,GAAG,KAAK,GAAG;AACpE,WAAK,eAAe,OAAO,QAAQ,EAAE,EAAE,UAAU;QAC/C,MAAM,MAAK;AACT,eAAK,SAAQ;AACb,gBAAM,oCAA8B;QACtC;QACA,OAAO,MAAM,MAAM,+BAA+B;OACnD;IACH;EACF;;EAGA,YAAY,OAAa;AACvB,WAAO,IAAI,KAAK,aAAa,SAAS;MACpC,OAAO;MACP,UAAU;MACV,uBAAuB;MACvB,uBAAuB;KACxB,EAAE,OAAO,KAAK,EAAE,QAAQ,OAAO,IAAI;EACtC;EAEA,cAAc,SAAgB;AAE5B,QAAI,aAAa,QAAQ;AAEzB,QAAI,QAAQ,oBAAoB,QAAQ,mBAAmB,GAAG;AAC5D,mBAAa,QAAQ;IACvB,WAAW,QAAQ,6BAA6B,QAAQ,4BAA4B,GAAG;AACrF,mBAAa,QAAQ;IACvB,WAAW,QAAQ,0BAA0B,QAAQ,yBAAyB,GAAG;AAC/E,mBAAa,QAAQ;IACvB;AAEA,WAAO;EACT;EAEA,aAAa,SAAgB;AAE3B,WAAO,CAAC,EACL,QAAQ,oBAAoB,QAAQ,mBAAmB,KAAK,QAAQ,mBAAmB,QAAQ,gBAC/F,QAAQ,6BAA6B,QAAQ,4BAA4B,KAAK,QAAQ,4BAA4B,QAAQ,gBAC1H,QAAQ,0BAA0B,QAAQ,yBAAyB,KAAK,QAAQ,yBAAyB,QAAQ;EAEtH;EAEA,eAAe,SAAgB;AAC7B,QAAI,QAAQ,UAAU;AAAG,aAAO;AAChC,QAAI,QAAQ,SAAS;AAAG,aAAO;AAC/B,WAAO;EACT;EAEA,cAAc,SAAgB;AAC5B,QAAI,QAAQ,UAAU;AAAG,aAAO;AAChC,QAAI,QAAQ,SAAS;AAAG,aAAO;AAC/B,WAAO;EACT;;EAGA,sBAAmB;AACjB,SAAK,qBAAqB,CAAA;AAC1B,SAAK,mBAAmB,CAAA;EAC1B;EAEA,qBAAqB,OAAY;AAC/B,UAAM,QAAQ,MAAM;AACpB,QAAI,MAAM,SAAS,MAAM,MAAM,SAAS,GAAG;AACzC,WAAK,qBAAqB,MAAM,KAAK,MAAM,KAAK;AAChD,WAAK,sBAAqB;IAC5B;EACF;EAEA,sBAAsB,OAAY;AAChC,UAAM,QAAQ,MAAM;AACpB,UAAM,OAAO,MAAM,QAAQ,CAAC;AAE5B,QAAI,MAAM;AACR,cAAQ,IAAI,gDAAmC,KAAK,IAAI;AAGxD,UAAI,KAAK,mBAAmB,UAAU,GAAG;AACvC,cAAM,8CAA8C;AACpD;MACF;AAGA,WAAK,mBAAmB,KAAK,IAAI;AAGjC,YAAM,SAAS,IAAI,WAAU;AAC7B,aAAO,SAAS,CAAC,MAAK;AACpB,YAAI,EAAE,QAAQ,QAAQ;AACpB,eAAK,iBAAiB,KAAK,EAAE,OAAO,MAAgB;QACtD;MACF;AACA,aAAO,cAAc,IAAI;AAEzB,cAAQ,IAAI,mCAA2B,KAAK,mBAAmB,MAAM;AAGrE,YAAM,QAAQ;IAChB;EACF;EAEA,wBAAqB;AACnB,SAAK,mBAAmB,CAAA;AACxB,SAAK,mBAAmB,QAAQ,UAAO;AACrC,YAAM,SAAS,IAAI,WAAU;AAC7B,aAAO,SAAS,CAAC,MAAK;AACpB,YAAI,EAAE,QAAQ,QAAQ;AACpB,eAAK,iBAAiB,KAAK,EAAE,OAAO,MAAgB;QACtD;MACF;AACA,aAAO,cAAc,IAAI;IAC3B,CAAC;EACH;EAEA,gBAAgB,MAAU;AACxB,UAAM,QAAQ,KAAK,mBAAmB,QAAQ,IAAI;AAClD,WAAO,KAAK,iBAAiB,KAAK,KAAK;EACzC;EAEA,gBAAgB,OAAa;AAC3B,SAAK,mBAAmB,OAAO,OAAO,CAAC;AACvC,SAAK,iBAAiB,OAAO,OAAO,CAAC;EACvC;EAEA,aAAa,OAAY;AACvB,UAAM,MAAM,MAAM;AAClB,QAAI,OAAO,CAAC,IAAI,IAAI,SAAS,YAAY,GAAG;AAC1C,cAAQ,IAAI,wCAAoC,IAAI,GAAG;AACvD,UAAI,MAAM,KAAK,gBAAgB,kBAAiB;IAClD;EACF;EAEA,oBAAoB,SAAgB;AAClC,YAAQ,IAAI,+DAAgD;MAC1D,IAAI,QAAQ;MACZ,KAAK,QAAQ;MACb,QAAQ,QAAQ;MAChB,oBAAoB,QAAQ;MAC5B,cAAc,QAAQ,QAAQ;KAC/B;AAGD,QAAI,QAAQ,oBAAoB;AAC9B,cAAQ,IAAI,sDAA0C,QAAQ,kBAAkB;AAChF,YAAM,MAAM,KAAK,gBAAgB,mBAAmB,QAAQ,kBAAkB;AAC9E,cAAQ,IAAI,0CAA8B,GAAG;AAC7C,aAAO;IACT;AAGA,QAAI,QAAQ,QAAQ,SAAS,GAAG;AAC9B,cAAQ,IAAI,kDAAsC,QAAQ,MAAM;AAEhE,YAAM,YAAY,QAAQ,OAAO,KAAK,SAAO,IAAI,MAAM;AACvD,YAAM,WAAW,WAAW,YAAY,QAAQ,OAAO,CAAC,EAAE;AAC1D,cAAQ,IAAI,6CAA2B,EAAE,WAAW,SAAQ,CAAE;AAE9D,UAAI,UAAU;AACZ,cAAM,MAAM,KAAK,gBAAgB,mBAAmB,QAAQ;AAC5D,gBAAQ,IAAI,0CAA8B,GAAG;AAC7C,eAAO;MACT;IACF;AAGA,YAAQ,IAAI,qEAAsD;AAClE,WAAO,KAAK,gBAAgB,kBAAiB;EAC/C;;EAGA,eAAY;AACV,SAAK,iBAAgB;EACvB;EAEA,gBAAgB,OAAU;AACxB,UAAM,YAAY,CAAC,MAAM,OAAO;AAChC,UAAM,eAAe,KAAK,gBAAe,EAAG,KAAK,OAAK,EAAE,OAAO,SAAS;AACxE,SAAK,kBAAkB,cAAc,QAAQ;AAC7C,SAAK,iBAAgB;EACvB;EAEA,mBAAgB;AACd,UAAM,mBAAmB,SAAS,cAAc,yBAAyB;AACzE,UAAM,cAAc,mBAAmB,CAAC,iBAAiB,QAAQ;AAEjE,QAAI,cAAc,KAAK,KAAK,kBAAkB,GAAG;AAC/C,WAAK,oBAAoB,eAAe,IAAI,KAAK,kBAAkB;IACrE,OAAO;AACL,WAAK,oBAAoB;IAC3B;EACF;;EAGA,gBAAa;AACX,WAAO,KAAK,SAAQ,EAAG,OAAO,CAAC,OAAO,YAAY,SAAS,QAAQ,SAAS,IAAI,CAAC;EACnF;EAEA,gBAAa;AACX,WAAO,KAAK,SAAQ,EAAG,OAAO,CAAC,OAAO,YAAW;AAC/C,YAAM,QAAQ,QAAQ,SAAS;AAC/B,YAAM,OAAO,QAAQ,eAAe;AACpC,aAAO,QAAS,QAAQ;IAC1B,GAAG,CAAC;EACN;EAEA,mBAAgB;AACd,UAAM,aAAa;AACnB,WAAO,KAAK,SAAQ,EAAG,OAAO,cAAY,QAAQ,SAAS,MAAM,UAAU,EAAE;EAC/E;EAEA,mBAAmB,OAAa;AAC9B,QAAI,SAAS;AAAG,aAAO;AACvB,QAAI,SAAS;AAAI,aAAO;AACxB,QAAI,SAAS;AAAI,aAAO;AACxB,WAAO;EACT;;EAGA,uBAAuB,OAAe,MAAS;AAC7C,WAAO,KAAK;EACd;EAEA,kBAAkB,OAAU;AAC1B,UAAM,cAAc,CAAC,MAAM,OAAO;AAClC,YAAQ,IAAI,yCAA+B,WAAW;AAEtD,QAAI,aAAa;AAEf,cAAQ,IAAI,6EAA0D,WAAW;AACjF,WAAK,qBAAqB,eAAe,WAAW,EAAE,UAAU;QAC9D,MAAM,CAAC,mBAAkB;AACvB,kBAAQ,IAAI,wCAA6B,cAAc;AACvD,gBAAM,sBAAsB,MAAM,QAAQ,cAAc,IAAI,iBAAiB,CAAA;AAC7E,kBAAQ,IAAI,uCAA6B,mBAAmB;AAC5D,eAAK,uBAAuB,IAAI,mBAAmB;AACnD,kBAAQ,IAAI,oDAA0C,KAAK,uBAAsB,CAAE;QACrF;QACA,OAAO,CAAC,UAAS;AACf,kBAAQ,MAAM,qCAA6B,KAAK;AAChD,eAAK,uBAAuB,IAAI,CAAA,CAAE;QACpC;OACD;AAID,WAAK,aAAa,YAAW,EAAG,UAAU;QACxC,MAAM,CAAC,WAAW,KAAK,eAAe,IAAI,MAAM;QAChD,OAAO,CAAC,UAAU,QAAQ,MAAM,yBAAoB,KAAK;OAC1D;AAED,WAAK,eAAe,YAAW,EAAG,UAAU;QAC1C,MAAM,CAAC,SAAS,KAAK,gBAAgB,IAAI,IAAI;QAC7C,OAAO,CAAC,UAAU,QAAQ,MAAM,2BAAsB,KAAK;OAC5D;IACH,OAAO;AAEL,WAAK,uBAAuB,IAAI,CAAA,CAAE;AAClC,WAAK,eAAe,IAAI,CAAA,CAAE;AAC1B,WAAK,gBAAgB,IAAI,CAAA,CAAE;IAC7B;EACF;EAEA,mBAAgB;AACd,YAAQ,IAAI,mDAA4C;AAGxD,SAAK,mBAAmB,IAAI,CAAA,CAAE;AAC9B,SAAK,uBAAuB,IAAI,CAAA,CAAE;AAClC,SAAK,gBAAgB,IAAI,CAAA,CAAE;AAC3B,SAAK,eAAe,IAAI,CAAA,CAAE;AAC1B,SAAK,gBAAgB,IAAI,CAAA,CAAE;AAE3B,SAAK,cAAa;AAElB,eAAW,MAAK;AACd,cAAQ,IAAI,wDAA2C;AACvD,cAAQ,IAAI,oBAAiB,KAAK,mBAAkB,CAAE;AACtD,cAAQ,IAAI,cAAc,KAAK,gBAAe,CAAE;AAChD,cAAQ,IAAI,aAAa,KAAK,eAAc,CAAE;AAC9C,cAAQ,IAAI,eAAe,KAAK,gBAAe,CAAE;IACnD,GAAG,GAAI;EACT;EAEA,qBAAqB,MAAY;AAC/B,YAAQ,IAAI,yDAAiD,IAAI;AACjE,UAAM,gDAA0C,IAAI,sBAAgB;EACtE;EAEA,SAAS,MAAS;AAChB,QAAI,CAAC,KAAK,OAAO;AACf,YAAM,+CAA+C;AACrD;IACF;AAEA,UAAM,WAAW,KAAK;AACtB,UAAM,cAAc,KAAK,YAAY,eAAc;AAEnD,QAAI,CAAC,aAAa,IAAI;AACpB,YAAM,6BAA0B;AAChC;IACF;AAEA,QAAI,KAAK,WAAU,KAAM,KAAK,gBAAe,GAAI;AAE/C,YAAM,SAAwB;QAC5B,IAAI,KAAK,gBAAe,EAAI;QAC5B,KAAK,SAAS;QACd,aAAa,SAAS,eAAe;QACrC,aAAa,CAAC,SAAS,aAAa;QACpC,OAAO,CAAC,SAAS,SAAS;QAC1B,mBAAmB,CAAC,SAAS,qBAAqB;QAClD,YAAY,KAAK,mBAAmB,SAAS,IAAI,KAAK,qBAAqB;;AAG7E,WAAK,eAAe,OAAO,KAAK,gBAAe,EAAI,IAAI,MAAM,EAAE,UAAU;QACvE,MAAM,MAAK;AACT,eAAK,SAAQ;AACb,eAAK,UAAS;AACd,gBAAM,sCAAgC;QACxC;QACA,OAAO,CAAC,UAAS;AACf,kBAAQ,MAAM,4CAAoC,KAAK;AACvD,gBAAM,6CAA0C;QAClD;OACD;IACH,OAAO;AAEL,UAAI,CAAC,SAAS,qBAAqB,CAAC,SAAS,OAAO,CAAC,SAAS,eAC1D,CAAC,SAAS,aAAa,CAAC,SAAS,aAAa,CAAC,SAAS,mBACxD,CAAC,SAAS,YAAY,CAAC,SAAS,WAAW,CAAC,SAAS,WAAW;AAClE,cAAM,+CAA+C;AACrD;MACF;AAEA,YAAM,aAA4B;QAChC,KAAK,SAAS;QACd,aAAa,SAAS,eAAe;QACrC,mBAAmB,SAAS;QAC5B,sBAAsB,SAAS,wBAAwB;QACvD,YAAY,SAAS,cAAc,GAAG,KAAK,IAAG,CAAE,GAAG,KAAK,MAAM,KAAK,OAAM,IAAK,GAAI,CAAC;QACnF,aAAa,CAAC,SAAS,aAAa;QACpC,aAAa,CAAC,SAAS,aAAa;QACpC,OAAO,CAAC,SAAS,SAAS;QAC1B,eAAe,YAAY;QAC3B,iBAAiB,CAAC,SAAS;QAC3B,UAAU,CAAC,SAAS;QACpB,SAAS,CAAC,SAAS;QACnB,WAAW,CAAC,SAAS;QACrB,mBAAmB,CAAC,SAAS,qBAAqB;QAClD,YAAY,KAAK,mBAAmB,SAAS,IAAI,KAAK,qBAAqB;;AAG7E,WAAK,eAAe,OAAO,UAAU,EAAE,UAAU;QAC/C,MAAM,MAAK;AACT,eAAK,SAAQ;AACb,eAAK,UAAS;AACd,gBAAM,mCAA0B;QAClC;QACA,OAAO,CAAC,UAAS;AACf,kBAAQ,MAAM,yCAAiC,KAAK;AACpD,gBAAM,0CAAuC;QAC/C;OACD;IACH;EACF;;qCAx4BW,oBAAiB,4BAAA,cAAA,GAAA,4BAAA,gBAAA,GAAA,4BAAA,oBAAA,GAAA,4BAAA,aAAA,GAAA,4BAAA,YAAA,GAAA,4BAAA,cAAA,GAAA,4BAAA,WAAA,GAAA,4BAAA,eAAA,CAAA;EAAA;yEAAjB,oBAAiB,WAAA,CAAA,CAAA,cAAA,CAAA,GAAA,OAAA,IAAA,MAAA,IAAA,QAAA,CAAA,CAAA,mBAAA,EAAA,GAAA,CAAA,eAAA,QAAA,GAAA,CAAA,aAAA,EAAA,GAAA,CAAA,GAAA,oBAAA,GAAA,CAAA,GAAA,iBAAA,GAAA,CAAA,GAAA,OAAA,iBAAA,GAAA,gBAAA,QAAA,GAAA,OAAA,GAAA,CAAA,GAAA,OAAA,eAAA,GAAA,OAAA,GAAA,CAAA,GAAA,YAAA,GAAA,CAAA,GAAA,WAAA,GAAA,CAAA,GAAA,aAAA,GAAA,CAAA,GAAA,YAAA,GAAA,CAAA,GAAA,YAAA,GAAA,CAAA,QAAA,QAAA,eAAA,iBAAA,GAAA,gBAAA,GAAA,SAAA,OAAA,GAAA,CAAA,SAAA,iBAAA,SAAA,6GAAA,GAAA,MAAA,GAAA,CAAA,SAAA,eAAA,GAAA,MAAA,GAAA,CAAA,SAAA,iBAAA,GAAA,MAAA,GAAA,CAAA,SAAA,WAAA,GAAA,MAAA,GAAA,CAAA,SAAA,oBAAA,GAAA,QAAA,UAAA,GAAA,CAAA,SAAA,eAAA,GAAA,MAAA,GAAA,CAAA,SAAA,iBAAA,GAAA,SAAA,GAAA,MAAA,GAAA,CAAA,GAAA,iBAAA,GAAA,cAAA,QAAA,UAAA,kBAAA,WAAA,QAAA,UAAA,UAAA,iBAAA,OAAA,SAAA,MAAA,GAAA,CAAA,GAAA,aAAA,GAAA,CAAA,GAAA,gBAAA,GAAA,CAAA,GAAA,gBAAA,GAAA,CAAA,GAAA,eAAA,GAAA,CAAA,GAAA,WAAA,GAAA,CAAA,GAAA,cAAA,GAAA,CAAA,GAAA,YAAA,GAAA,CAAA,SAAA,mBAAA,GAAA,MAAA,GAAA,CAAA,GAAA,aAAA,OAAA,GAAA,CAAA,GAAA,SAAA,GAAA,CAAA,GAAA,gBAAA,GAAA,CAAA,QAAA,QAAA,eAAA,4BAAA,GAAA,gBAAA,GAAA,iBAAA,SAAA,GAAA,CAAA,GAAA,aAAA,GAAA,CAAA,GAAA,SAAA,GAAA,CAAA,GAAA,iBAAA,GAAA,CAAA,QAAA,YAAA,GAAA,iBAAA,SAAA,GAAA,CAAA,GAAA,kBAAA,GAAA,CAAA,SAAA,mBAAA,GAAA,MAAA,GAAA,CAAA,GAAA,iBAAA,GAAA,CAAA,GAAA,gBAAA,GAAA,CAAA,SAAA,eAAA,GAAA,SAAA,SAAA,GAAA,CAAA,GAAA,aAAA,GAAA,CAAA,GAAA,YAAA,GAAA,CAAA,GAAA,iBAAA,GAAA,SAAA,OAAA,KAAA,GAAA,CAAA,GAAA,cAAA,GAAA,CAAA,GAAA,cAAA,GAAA,CAAA,GAAA,cAAA,GAAA,CAAA,SAAA,QAAA,GAAA,MAAA,GAAA,CAAA,SAAA,kBAAA,GAAA,MAAA,GAAA,CAAA,SAAA,uBAAA,GAAA,MAAA,GAAA,CAAA,GAAA,gBAAA,GAAA,CAAA,GAAA,cAAA,GAAA,CAAA,SAAA,mBAAA,GAAA,MAAA,GAAA,CAAA,GAAA,YAAA,GAAA,CAAA,GAAA,iBAAA,GAAA,CAAA,SAAA,iBAAA,GAAA,MAAA,GAAA,CAAA,GAAA,aAAA,GAAA,CAAA,SAAA,eAAA,GAAA,MAAA,GAAA,CAAA,GAAA,eAAA,OAAA,GAAA,CAAA,SAAA,kBAAA,GAAA,MAAA,GAAA,CAAA,GAAA,YAAA,GAAA,CAAA,GAAA,iBAAA,GAAA,CAAA,GAAA,eAAA,GAAA,SAAA,GAAA,CAAA,SAAA,eAAA,GAAA,MAAA,GAAA,CAAA,GAAA,eAAA,GAAA,CAAA,GAAA,UAAA,GAAA,CAAA,SAAA,SAAA,GAAA,MAAA,GAAA,CAAA,GAAA,aAAA,GAAA,CAAA,GAAA,cAAA,GAAA,CAAA,GAAA,cAAA,GAAA,CAAA,GAAA,gBAAA,GAAA,CAAA,SAAA,uBAAA,GAAA,cAAA,YAAA,GAAA,OAAA,GAAA,CAAA,SAAA,uBAAA,GAAA,cAAA,YAAA,GAAA,OAAA,GAAA,CAAA,SAAA,wBAAA,GAAA,cAAA,cAAA,GAAA,OAAA,GAAA,CAAA,GAAA,MAAA,GAAA,CAAA,GAAA,gBAAA,GAAA,CAAA,GAAA,qBAAA,GAAA,CAAA,GAAA,iBAAA,GAAA,CAAA,GAAA,eAAA,GAAA,CAAA,GAAA,aAAA,GAAA,CAAA,GAAA,eAAA,WAAA,SAAA,GAAA,CAAA,GAAA,gBAAA,GAAA,CAAA,GAAA,aAAA,GAAA,CAAA,GAAA,OAAA,GAAA,CAAA,GAAA,SAAA,GAAA,CAAA,GAAA,SAAA,GAAA,CAAA,GAAA,aAAA,GAAA,CAAA,GAAA,iBAAA,GAAA,OAAA,GAAA,CAAA,GAAA,iBAAA,iBAAA,GAAA,OAAA,GAAA,CAAA,GAAA,cAAA,GAAA,CAAA,QAAA,UAAA,GAAA,OAAA,UAAA,yBAAA,GAAA,OAAA,GAAA,CAAA,SAAA,2BAAA,GAAA,MAAA,GAAA,CAAA,GAAA,yBAAA,GAAA,CAAA,GAAA,cAAA,GAAA,CAAA,GAAA,gBAAA,GAAA,CAAA,GAAA,aAAA,GAAA,CAAA,SAAA,eAAA,GAAA,MAAA,GAAA,CAAA,SAAA,kBAAA,GAAA,MAAA,GAAA,CAAA,GAAA,iBAAA,GAAA,CAAA,GAAA,OAAA,iBAAA,GAAA,OAAA,GAAA,CAAA,GAAA,cAAA,GAAA,CAAA,GAAA,iBAAA,GAAA,CAAA,GAAA,eAAA,GAAA,CAAA,GAAA,YAAA,GAAA,CAAA,GAAA,gBAAA,GAAA,CAAA,SAAA,cAAA,GAAA,SAAA,SAAA,GAAA,CAAA,GAAA,YAAA,GAAA,CAAA,GAAA,OAAA,KAAA,GAAA,CAAA,SAAA,cAAA,GAAA,MAAA,GAAA,CAAA,GAAA,YAAA,GAAA,CAAA,GAAA,iBAAA,GAAA,OAAA,GAAA,CAAA,GAAA,eAAA,GAAA,CAAA,QAAA,UAAA,SAAA,wBAAA,GAAA,OAAA,UAAA,yBAAA,GAAA,OAAA,GAAA,CAAA,QAAA,eAAA,GAAA,gBAAA,GAAA,UAAA,GAAA,CAAA,GAAA,cAAA,GAAA,CAAA,GAAA,UAAA,GAAA,CAAA,GAAA,YAAA,GAAA,CAAA,QAAA,QAAA,QAAA,qBAAA,YAAA,IAAA,eAAA,WAAA,GAAA,gBAAA,GAAA,iBAAA,SAAA,GAAA,CAAA,QAAA,QAAA,QAAA,wBAAA,eAAA,YAAA,GAAA,gBAAA,GAAA,iBAAA,SAAA,GAAA,CAAA,QAAA,QAAA,QAAA,OAAA,YAAA,IAAA,eAAA,kBAAA,GAAA,gBAAA,GAAA,iBAAA,SAAA,GAAA,CAAA,QAAA,QAAA,QAAA,cAAA,eAAA,mCAAA,GAAA,gBAAA,GAAA,iBAAA,SAAA,GAAA,CAAA,QAAA,eAAA,YAAA,IAAA,QAAA,KAAA,eAAA,0BAAA,GAAA,gBAAA,GAAA,iBAAA,SAAA,GAAA,CAAA,QAAA,UAAA,QAAA,aAAA,YAAA,IAAA,OAAA,KAAA,QAAA,QAAA,GAAA,gBAAA,GAAA,iBAAA,SAAA,SAAA,GAAA,CAAA,QAAA,UAAA,QAAA,aAAA,YAAA,IAAA,OAAA,KAAA,QAAA,QAAA,GAAA,gBAAA,GAAA,iBAAA,SAAA,SAAA,GAAA,CAAA,SAAA,cAAA,GAAA,MAAA,GAAA,CAAA,QAAA,UAAA,QAAA,SAAA,OAAA,KAAA,eAAA,KAAA,GAAA,gBAAA,GAAA,iBAAA,SAAA,GAAA,CAAA,QAAA,UAAA,QAAA,qBAAA,OAAA,KAAA,OAAA,OAAA,QAAA,KAAA,eAAA,MAAA,SAAA,+DAAA,GAAA,gBAAA,GAAA,iBAAA,SAAA,GAAA,CAAA,GAAA,aAAA,YAAA,GAAA,CAAA,GAAA,mBAAA,GAAA,CAAA,QAAA,eAAA,YAAA,IAAA,GAAA,gBAAA,GAAA,iBAAA,UAAA,SAAA,GAAA,CAAA,SAAA,EAAA,GAAA,CAAA,GAAA,SAAA,GAAA,SAAA,SAAA,GAAA,CAAA,QAAA,mBAAA,YAAA,IAAA,GAAA,gBAAA,GAAA,iBAAA,SAAA,GAAA,CAAA,GAAA,SAAA,GAAA,SAAA,WAAA,cAAA,GAAA,CAAA,GAAA,cAAA,GAAA,SAAA,QAAA,aAAA,MAAA,GAAA,CAAA,QAAA,YAAA,YAAA,IAAA,GAAA,gBAAA,GAAA,iBAAA,SAAA,GAAA,CAAA,QAAA,WAAA,YAAA,IAAA,GAAA,gBAAA,GAAA,iBAAA,SAAA,GAAA,CAAA,QAAA,aAAA,YAAA,IAAA,GAAA,gBAAA,GAAA,iBAAA,UAAA,SAAA,GAAA,CAAA,SAAA,mBAAA,GAAA,MAAA,GAAA,CAAA,QAAA,QAAA,UAAA,WAAA,GAAA,WAAA,QAAA,GAAA,QAAA,GAAA,CAAA,GAAA,sBAAA,GAAA,CAAA,QAAA,UAAA,GAAA,OAAA,uBAAA,GAAA,SAAA,UAAA,GAAA,CAAA,GAAA,MAAA,GAAA,CAAA,GAAA,aAAA,cAAA,GAAA,eAAA,MAAA,GAAA,CAAA,GAAA,aAAA,cAAA,GAAA,cAAA,KAAA,GAAA,CAAA,SAAA,2BAAA,SAAA,qBAAA,GAAA,MAAA,GAAA,CAAA,GAAA,cAAA,GAAA,CAAA,QAAA,UAAA,GAAA,OAAA,iBAAA,GAAA,OAAA,GAAA,CAAA,QAAA,UAAA,GAAA,OAAA,eAAA,GAAA,UAAA,GAAA,CAAA,GAAA,YAAA,GAAA,CAAA,GAAA,OAAA,GAAA,CAAA,GAAA,iBAAA,GAAA,CAAA,GAAA,yBAAA,GAAA,CAAA,SAAA,0BAAA,GAAA,SAAA,SAAA,GAAA,CAAA,GAAA,iBAAA,UAAA,GAAA,CAAA,GAAA,YAAA,GAAA,CAAA,GAAA,YAAA,GAAA,CAAA,SAAA,cAAA,GAAA,MAAA,GAAA,CAAA,GAAA,YAAA,GAAA,CAAA,GAAA,2BAAA,GAAA,cAAA,MAAA,GAAA,CAAA,SAAA,qBAAA,GAAA,SAAA,SAAA,GAAA,CAAA,GAAA,iBAAA,KAAA,GAAA,CAAA,QAAA,UAAA,SAAA,yBAAA,GAAA,cAAA,GAAA,OAAA,CAAA,GAAA,UAAA,SAAA,2BAAA,IAAA,KAAA;AAAA,QAAA,KAAA,GAAA;;AC/B9B,MAAA,yBAAA,GAAA,OAAA,CAAA,EAAgC,GAAA,OAAA,CAAA,EAED,GAAA,IAAA;AACvB,MAAA,iBAAA,GAAA,sCAAA;AAAwB,MAAA,uBAAA;AAC5B,MAAA,yBAAA,GAAA,KAAA,EAAK,GAAA,UAAA,CAAA;AAC+B,MAAA,qBAAA,SAAA,SAAA,qDAAA;AAAA,QAAA,wBAAA,GAAA;AAAA,eAAA,sBAAS,IAAA,SAAA,CAAU;MAAA,CAAA;AACnD,MAAA,iBAAA,GAAA,uBAAA;AACF,MAAA,uBAAA;AACA,MAAA,yBAAA,GAAA,UAAA,CAAA;AAAgC,MAAA,qBAAA,SAAA,SAAA,qDAAA;AAAA,QAAA,wBAAA,GAAA;AAAA,eAAA,sBAAS,IAAA,YAAA,CAAa;MAAA,CAAA;AACpD,MAAA,iBAAA,GAAA,6BAAA;AACF,MAAA,uBAAA,EAAS,EACL;AAIR,MAAA,yBAAA,GAAA,OAAA,CAAA,EAAwB,IAAA,OAAA,CAAA,EACC,IAAA,OAAA,CAAA;AACI,MAAA,iBAAA,EAAA;AAAmB,MAAA,uBAAA;AAC5C,MAAA,yBAAA,IAAA,OAAA,EAAA;AAAwB,MAAA,iBAAA,IAAA,gBAAA;AAAc,MAAA,uBAAA,EAAM;AAE9C,MAAA,yBAAA,IAAA,OAAA,CAAA,EAAuB,IAAA,OAAA,CAAA;AACI,MAAA,iBAAA,EAAA;AAAoB,MAAA,uBAAA;AAC7C,MAAA,yBAAA,IAAA,OAAA,EAAA;AAAwB,MAAA,iBAAA,IAAA,UAAA;AAAQ,MAAA,uBAAA,EAAM;AAExC,MAAA,yBAAA,IAAA,OAAA,CAAA,EAAuB,IAAA,OAAA,CAAA;AACI,MAAA,iBAAA,EAAA;AAAwB,MAAA,uBAAA;AACjD,MAAA,yBAAA,IAAA,OAAA,EAAA;AAAwB,MAAA,iBAAA,IAAA,SAAA;AAAO,MAAA,uBAAA,EAAM;AAEvC,MAAA,yBAAA,IAAA,OAAA,CAAA,EAAuB,IAAA,OAAA,CAAA;AACI,MAAA,iBAAA,EAAA;AAAqC,MAAA,uBAAA;AAC9D,MAAA,yBAAA,IAAA,OAAA,EAAA;AAAwB,MAAA,iBAAA,IAAA,eAAA;AAAa,MAAA,uBAAA,EAAM,EACvC;AAIR,MAAA,yBAAA,IAAA,OAAA,EAAA,EAAwB,IAAA,SAAA,EAAA;AAKpB,MAAA,qBAAA,SAAA,SAAA,mDAAA,QAAA;AAAA,QAAA,wBAAA,GAAA;AAAA,eAAA,sBAAS,IAAA,SAAA,MAAA,CAAgB;MAAA,CAAA;AAJ3B,MAAA,uBAAA,EAME;AAIJ,MAAA,qBAAA,IAAA,mCAAA,GAAA,GAAA,OAAA,EAAA,EAA6J,IAAA,mCAAA,GAAA,GAAA,OAAA,EAAA,EAOnG,IAAA,mCAAA,IAAA,GAAA,OAAA,EAAA,EAc2B,IAAA,mCAAA,IAAA,GAAA,OAAA,EAAA,EAqC/B,IAAA,mCAAA,GAAA,GAAA,OAAA,EAAA,EA8B+B,IAAA,2CAAA,GAAA,GAAA,eAAA,MAAA,GAAA,gCAAA,EAmHvD,IAAA,mCAAA,GAAA,GAAA,OAAA,EAAA,EAQsD,IAAA,mCAAA,GAAA,GAAA,OAAA,EAAA,EASV,IAAA,mCAAA,KAAA,IAAA,OAAA,EAAA;AAgb5E,MAAA,uBAAA;;;;AAzqB+B,MAAA,oBAAA,EAAA;AAAA,MAAA,4BAAA,IAAA,MAAA,EAAA,KAAA;AAIA,MAAA,oBAAA,CAAA;AAAA,MAAA,4BAAA,IAAA,MAAA,EAAA,MAAA;AAIA,MAAA,oBAAA,CAAA;AAAA,MAAA,4BAAA,IAAA,MAAA,EAAA,UAAA;AAIA,MAAA,oBAAA,CAAA;AAAA,MAAA,4BAAA,IAAA,YAAA,IAAA,MAAA,EAAA,UAAA,CAAA;AAUzB,MAAA,oBAAA,CAAA;AAAA,MAAA,qBAAA,SAAA,IAAA,YAAA,CAAA;AAOE,MAAA,oBAAA;AAAA,MAAA,qBAAA,QAAA,IAAA,MAAA,CAAA;AAOoB,MAAA,oBAAA;AAAA,MAAA,qBAAA,QAAA,CAAA,IAAA,UAAA,KAAA,CAAA,IAAA,MAAA,CAAA;AAcE,MAAA,oBAAA;AAAA,MAAA,qBAAA,QAAA,IAAA,SAAA,EAAA,SAAA,KAAA,CAAA,IAAA,UAAA,KAAA,CAAA,IAAA,MAAA,CAAA;AAqCN,MAAA,oBAAA;AAAA,MAAA,qBAAA,QAAA,CAAA,IAAA,UAAA,KAAA,CAAA,IAAA,MAAA,CAAA;AA8BS,MAAA,oBAAA;AAAA,MAAA,qBAAA,QAAA,CAAA,IAAA,UAAA,KAAA,CAAA,IAAA,MAAA,CAAA,EAAgC,YAAA,mBAAA;AA2HzD,MAAA,oBAAA,CAAA;AAAA,MAAA,qBAAA,QAAA,IAAA,oBAAA,EAAA,WAAA,KAAA,CAAA,IAAA,UAAA,CAAA;AASsB,MAAA,oBAAA;AAAA,MAAA,qBAAA,QAAA,IAAA,YAAA,CAAA;AAyHA,MAAA,oBAAA;AAAA,MAAA,qBAAA,QAAA,IAAA,SAAA,CAAA;;oBDxWlB,cAAY,SAAA,SAAA,MAAA,aAAA,UAAE,aAAW,oBAAA,gBAAA,8BAAA,sBAAA,qBAAA,8BAAA,4BAAA,iBAAA,sBAAA,mBAAA,cAAA,cAAA,SAAA,MAAA,GAAA,QAAA,CAAA,q8tBAAA,EAAA,CAAA;;;sEAIxB,mBAAiB,CAAA;UAP7B;uBACW,gBAAc,YACZ,MAAI,SACP,CAAC,cAAc,WAAW,GAAC,UAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;GAAA,QAAA,CAAA,8rkBAAA,EAAA,CAAA;;;;6EAIzB,mBAAiB,EAAA,WAAA,qBAAA,UAAA,qDAAA,YAAA,GAAA,CAAA;AAAA,GAAA;", "names": []}
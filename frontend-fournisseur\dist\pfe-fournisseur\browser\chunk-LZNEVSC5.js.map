{"version": 3, "sources": ["src/app/components/layout/header/header.component.ts", "src/app/components/layout/header/header.component.html", "src/app/components/layout/sidebar/sidebar.component.ts", "src/app/components/layout/sidebar/sidebar.component.html", "src/app/components/layout/dashboard-layout/dashboard-layout.component.ts", "src/app/components/layout/dashboard-layout/dashboard-layout.component.html"], "sourcesContent": ["import { Component, OnInit, Output, EventEmitter } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { Router } from '@angular/router';\nimport { AuthService } from '../../../services/auth.service';\nimport { AdminAuthService } from '../../../services/admin-auth.service';\nimport { User } from '../../../models/user.model';\nimport { NotificationIconComponent } from '../../notification-icon/notification-icon.component';\n\n@Component({\n  selector: 'app-header',\n  standalone: true,\n  imports: [CommonModule, NotificationIconComponent],\n  templateUrl: './header.component.html',\n  styleUrls: ['./header.component.css']\n})\nexport class HeaderComponent implements OnInit {\n  @Output() toggleSidebar = new EventEmitter<void>();\n\n  currentUser: User | null = null;\n  showUserMenu = false;\n\n  constructor(\n    private authService: AuthService,\n    private adminAuthService: AdminAuthService,\n    private router: Router\n  ) {}\n\n  ngOnInit(): void {\n    this.authService.currentUser$.subscribe(user => {\n      this.currentUser = user;\n    });\n  }\n\n  onToggleSidebar(): void {\n    this.toggleSidebar.emit();\n  }\n\n  toggleUserMenu(): void {\n    this.showUserMenu = !this.showUserMenu;\n  }\n\n  closeUserMenu(): void {\n    this.showUserMenu = false;\n  }\n\n  navigateToProfile(): void {\n    this.closeUserMenu();\n    this.router.navigate(['/dashboard/profil']);\n  }\n\n  logout(): void {\n    this.closeUserMenu();\n    this.authService.logout();\n    this.router.navigate(['/login']);\n  }\n\n  getUserInitials(): string {\n    if (!this.currentUser) return '';\n    \n    const firstInitial = this.currentUser.prenom?.charAt(0).toUpperCase() || '';\n    const lastInitial = this.currentUser.nom?.charAt(0).toUpperCase() || '';\n    \n    return firstInitial + lastInitial;\n  }\n\n  getUserFullName(): string {\n    if (!this.currentUser) return '';\n\n    return `${this.currentUser.prenom} ${this.currentUser.nom}`;\n  }\n\n  get isUsingMockServices(): boolean {\n    return false; // Temporairement désactivé\n  }\n\n  isAdmin(): boolean {\n    return this.adminAuthService.getCurrentUser() !== null;\n  }\n}\n", "<header class=\"header\">\n  <div class=\"header-left\">\n    <button class=\"sidebar-toggle\" (click)=\"onToggleSidebar()\" type=\"button\">\n      <span class=\"hamburger-line\"></span>\n      <span class=\"hamburger-line\"></span>\n      <span class=\"hamburger-line\"></span>\n    </button>\n    \n    <div class=\"header-title\">\n      <h1>Espace Fournisseur</h1>\n    </div>\n  </div>\n\n  <div class=\"header-right\">\n    <!-- Notifications (seulement pour les fournisseurs, pas les admins) -->\n    <div class=\"header-notifications\" *ngIf=\"!isAdmin()\">\n      <app-notification-icon></app-notification-icon>\n    </div>\n\n    <!-- User Menu -->\n    <div class=\"user-menu\" (clickOutside)=\"closeUserMenu()\">\n      <button class=\"user-menu-trigger\" (click)=\"toggleUserMenu()\" type=\"button\">\n        <div class=\"user-avatar\">\n          {{ getUserInitials() }}\n        </div>\n        <div class=\"user-info\">\n          <span class=\"user-name\">{{ getUserFullName() }}</span>\n          <span class=\"user-role\">Fournisseur</span>\n        </div>\n        <svg class=\"dropdown-arrow\" [class.rotated]=\"showUserMenu\" width=\"16\" height=\"16\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" stroke-width=\"2\">\n          <polyline points=\"6,9 12,15 18,9\"></polyline>\n        </svg>\n      </button>\n\n      <div class=\"user-dropdown\" [class.show]=\"showUserMenu\">\n        <div class=\"dropdown-header\">\n          <div class=\"user-avatar-large\">\n            {{ getUserInitials() }}\n          </div>\n          <div class=\"user-details\">\n            <div class=\"user-name\">{{ getUserFullName() }}</div>\n            <div class=\"user-email\">{{ currentUser?.email }}</div>\n          </div>\n        </div>\n        \n        <div class=\"dropdown-divider\"></div>\n        \n        <div class=\"dropdown-menu\">\n          <button class=\"dropdown-item\" (click)=\"navigateToProfile()\" type=\"button\">\n            <svg width=\"16\" height=\"16\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" stroke-width=\"2\">\n              <path d=\"M20 21v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2\"></path>\n              <circle cx=\"12\" cy=\"7\" r=\"4\"></circle>\n            </svg>\n            Mon Profil\n          </button>\n          \n          <button class=\"dropdown-item\" type=\"button\">\n            <svg width=\"16\" height=\"16\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" stroke-width=\"2\">\n              <circle cx=\"12\" cy=\"12\" r=\"3\"></circle>\n              <path d=\"M19.4 15a1.65 1.65 0 0 0 .33 1.82l.06.06a2 2 0 0 1 0 2.83 2 2 0 0 1-2.83 0l-.06-.06a1.65 1.65 0 0 0-1.82-.33 1.65 1.65 0 0 0-1 1.51V21a2 2 0 0 1-2 2 2 2 0 0 1-2-2v-.09A1.65 1.65 0 0 0 9 19.4a1.65 1.65 0 0 0-1.82.33l-.06.06a2 2 0 0 1-2.83 0 2 2 0 0 1 0-2.83l.06-.06a1.65 1.65 0 0 0 .33-1.82 1.65 1.65 0 0 0-1.51-1H3a2 2 0 0 1-2-2 2 2 0 0 1 2-2h.09A1.65 1.65 0 0 0 4.6 9a1.65 1.65 0 0 0-.33-1.82l-.06-.06a2 2 0 0 1 0-2.83 2 2 0 0 1 2.83 0l.06.06a1.65 1.65 0 0 0 1.82.33H9a1.65 1.65 0 0 0 1 1.51V3a2 2 0 0 1 2-2 2 2 0 0 1 2 2v.09a1.65 1.65 0 0 0 1 1.51 1.65 1.65 0 0 0 1.82-.33l.06-.06a2 2 0 0 1 2.83 0 2 2 0 0 1 0 2.83l-.06.06a1.65 1.65 0 0 0-.33 1.82V9a1.65 1.65 0 0 0 1.51 1H21a2 2 0 0 1 2 2 2 2 0 0 1-2 2h-.09a1.65 1.65 0 0 0-1.51 1z\"></path>\n            </svg>\n            Paramètres\n          </button>\n        </div>\n        \n        <div class=\"dropdown-divider\"></div>\n        \n        <button class=\"dropdown-item logout-item\" (click)=\"logout()\" type=\"button\">\n          <svg width=\"16\" height=\"16\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" stroke-width=\"2\">\n            <path d=\"M9 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h4\"></path>\n            <polyline points=\"16,17 21,12 16,7\"></polyline>\n            <line x1=\"21\" y1=\"12\" x2=\"9\" y2=\"12\"></line>\n          </svg>\n          Déconnexion\n        </button>\n      </div>\n    </div>\n  </div>\n</header>\n", "import { Component, Input, Output, EventEmitter, OnInit, OnD<PERSON>roy, HostListener } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { Router, RouterModule } from '@angular/router';\nimport { DomSanitizer, SafeHtml } from '@angular/platform-browser';\n\ninterface MenuItem {\n  label: string;\n  icon: string;\n  route?: string;\n  children?: MenuItem[];\n  expanded?: boolean;\n}\n\n@Component({\n  selector: 'app-sidebar',\n  standalone: true,\n  imports: [CommonModule, RouterModule],\n  templateUrl: './sidebar.component.html',\n  styleUrls: ['./sidebar.component.css']\n})\nexport class SidebarComponent implements OnInit, OnDestroy {\n  @Input() isCollapsed = false;\n  @Output() toggleSidebar = new EventEmitter<void>();\n  @Output() closeSidebar = new EventEmitter<void>();\n\n  isMobile = false;\n  isTablet = false;\n\n  menuItems: MenuItem[] = [\n    {\n      label: 'Dashboard',\n      icon: 'dashboard',\n      route: '/dashboard/overview'\n    },\n    {\n      label: 'Mon Profil',\n      icon: 'user',\n      children: [\n        {\n          label: 'Informations',\n          icon: 'info',\n          route: '/dashboard/profil'\n        }\n      ]\n    },\n    {\n      label: 'Produits',\n      icon: 'package',\n      children: [\n        {\n          label: 'Liste des produits',\n          icon: 'list',\n          route: '/dashboard/products'\n        },\n        {\n          label: 'Gestion du stock',\n          icon: 'inventory',\n          route: '/dashboard/stock'\n        }\n      ]\n    },\n    {\n      label: 'Référentiels',\n      icon: 'settings',\n      children: [\n        {\n          label: 'Gestion complète',\n          icon: 'database',\n          route: '/dashboard/referentiels'\n        },\n        {\n          label: 'Demandes Catégories',\n          icon: 'request',\n          route: '/dashboard/demandes-categories'\n        }\n      ]\n    },\n    {\n      label: 'Commandes',\n      icon: 'orders',\n      children: [\n\n        {\n          label: 'Historique',\n          icon: 'history',\n          route: '/dashboard/orders'\n        }\n      ]\n    },\n    {\n      label: 'Livraisons',\n      icon: 'truck',\n      route: '/dashboard/deliveries'\n    },\n    {\n      label: 'Avis Clients',\n      icon: 'star',\n      route: '/dashboard/avis'\n    }\n  ];\n\n  constructor(\n    private router: Router,\n    private sanitizer: DomSanitizer\n  ) {}\n\n  ngOnInit(): void {\n    this.checkScreenSize();\n  }\n\n  ngOnDestroy(): void {\n    // Cleanup if needed\n  }\n\n  @HostListener('window:resize', ['$event'])\n  onResize(): void {\n    this.checkScreenSize();\n  }\n\n  /**\n   * Vérifier la taille de l'écran et ajuster la responsivité\n   */\n  private checkScreenSize(): void {\n    const width = window.innerWidth;\n    this.isMobile = width < 768;\n    this.isTablet = width >= 768 && width < 1024;\n\n    // Auto-collapse sur mobile\n    if (this.isMobile && !this.isCollapsed) {\n      this.isCollapsed = true;\n    }\n  }\n\n  toggleSubmenu(item: MenuItem): void {\n    if (item.children) {\n      item.expanded = !item.expanded;\n    }\n  }\n\n  navigateTo(route: string): void {\n    this.router.navigate([route]);\n\n    // Fermer la sidebar sur mobile après navigation\n    if (this.isMobile) {\n      this.closeSidebar.emit();\n    }\n  }\n\n  /**\n   * Toggle sidebar (pour le bouton hamburger)\n   */\n  onToggleSidebar(): void {\n    this.toggleSidebar.emit();\n  }\n\n  /**\n   * Fermer la sidebar (pour l'overlay mobile)\n   */\n  onCloseSidebar(): void {\n    this.closeSidebar.emit();\n  }\n\n  isActiveRoute(route: string): boolean {\n    return this.router.url === route;\n  }\n\n  isParentActive(item: MenuItem): boolean {\n    if (!item.children) return false;\n    \n    return item.children.some(child => \n      child.route && this.isActiveRoute(child.route)\n    );\n  }\n\n  getIconSvg(iconName: string): SafeHtml {\n    const icons: { [key: string]: string } = {\n      dashboard: `<svg width=\"20\" height=\"20\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" stroke-width=\"2\">\n        <rect x=\"3\" y=\"3\" width=\"7\" height=\"7\"></rect>\n        <rect x=\"14\" y=\"3\" width=\"7\" height=\"7\"></rect>\n        <rect x=\"14\" y=\"14\" width=\"7\" height=\"7\"></rect>\n        <rect x=\"3\" y=\"14\" width=\"7\" height=\"7\"></rect>\n      </svg>`,\n      user: `<svg width=\"20\" height=\"20\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" stroke-width=\"2\">\n        <path d=\"M20 21v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2\"></path>\n        <circle cx=\"12\" cy=\"7\" r=\"4\"></circle>\n      </svg>`,\n      package: `<svg width=\"20\" height=\"20\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" stroke-width=\"2\">\n        <line x1=\"16.5\" y1=\"9.4\" x2=\"7.5\" y2=\"4.21\"></line>\n        <path d=\"M21 16V8a2 2 0 0 0-1-1.73l-7-4a2 2 0 0 0-2 0l-7 4A2 2 0 0 0 3 8v8a2 2 0 0 0 1 1.73l7 4a2 2 0 0 0 2 0l7-4A2 2 0 0 0 21 16z\"></path>\n        <polyline points=\"3.27,6.96 12,12.01 20.73,6.96\"></polyline>\n        <line x1=\"12\" y1=\"22.08\" x2=\"12\" y2=\"12\"></line>\n      </svg>`,\n      orders: `<svg width=\"20\" height=\"20\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" stroke-width=\"2\">\n        <path d=\"M9 11H5a2 2 0 0 0-2 2v7a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7a2 2 0 0 0-2-2h-4\"></path>\n        <rect x=\"9\" y=\"7\" width=\"6\" height=\"6\"></rect>\n      </svg>`,\n      truck: `<svg width=\"20\" height=\"20\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" stroke-width=\"2\">\n        <rect x=\"1\" y=\"3\" width=\"15\" height=\"13\"></rect>\n        <polygon points=\"16,8 20,8 23,11 23,16 16,16 16,8\"></polygon>\n        <circle cx=\"5.5\" cy=\"18.5\" r=\"2.5\"></circle>\n        <circle cx=\"18.5\" cy=\"18.5\" r=\"2.5\"></circle>\n      </svg>`,\n      info: `<svg width=\"16\" height=\"16\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" stroke-width=\"2\">\n        <circle cx=\"12\" cy=\"12\" r=\"10\"></circle>\n        <line x1=\"12\" y1=\"16\" x2=\"12\" y2=\"12\"></line>\n        <line x1=\"12\" y1=\"8\" x2=\"12.01\" y2=\"8\"></line>\n      </svg>`,\n      bank: `<svg width=\"16\" height=\"16\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" stroke-width=\"2\">\n        <line x1=\"12\" y1=\"1\" x2=\"12\" y2=\"23\"></line>\n        <path d=\"M17 5H9.5a3.5 3.5 0 0 0 0 7h5a3.5 3.5 0 0 1 0 7H6\"></path>\n      </svg>`,\n      list: `<svg width=\"16\" height=\"16\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" stroke-width=\"2\">\n        <line x1=\"8\" y1=\"6\" x2=\"21\" y2=\"6\"></line>\n        <line x1=\"8\" y1=\"12\" x2=\"21\" y2=\"12\"></line>\n        <line x1=\"8\" y1=\"18\" x2=\"21\" y2=\"18\"></line>\n        <line x1=\"3\" y1=\"6\" x2=\"3.01\" y2=\"6\"></line>\n        <line x1=\"3\" y1=\"12\" x2=\"3.01\" y2=\"12\"></line>\n        <line x1=\"3\" y1=\"18\" x2=\"3.01\" y2=\"18\"></line>\n      </svg>`,\n      plus: `<svg width=\"16\" height=\"16\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" stroke-width=\"2\">\n        <line x1=\"12\" y1=\"5\" x2=\"12\" y2=\"19\"></line>\n        <line x1=\"5\" y1=\"12\" x2=\"19\" y2=\"12\"></line>\n      </svg>`,\n      inventory: `<svg width=\"16\" height=\"16\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" stroke-width=\"2\">\n        <path d=\"M21 8a2 2 0 0 0-1-1.73l-7-4a2 2 0 0 0-2 0l-7 4A2 2 0 0 0 3 8v8a2 2 0 0 0 1 1.73l7 4a2 2 0 0 0 2 0l7-4A2 2 0 0 0 21 16V8z\"></path>\n        <path d=\"M3.27 6.96L12 12.01l8.73-5.05\"></path>\n        <line x1=\"12\" y1=\"22.08\" x2=\"12\" y2=\"12\"></line>\n      </svg>`,\n      inbox: `<svg width=\"16\" height=\"16\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" stroke-width=\"2\">\n        <polyline points=\"22,12 18,12 15,21 9,21 6,12 2,12\"></polyline>\n        <path d=\"M5.45 5.11L2 12v6a2 2 0 0 0 2 2h16a2 2 0 0 0 2-2v-6l-3.45-6.89A2 2 0 0 0 16.76 4H7.24a2 2 0 0 0-1.79 1.11z\"></path>\n      </svg>`,\n      history: `<svg width=\"16\" height=\"16\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" stroke-width=\"2\">\n        <circle cx=\"12\" cy=\"12\" r=\"10\"></circle>\n        <polyline points=\"12,6 12,12 16,14\"></polyline>\n      </svg>`,\n      settings: `<svg width=\"20\" height=\"20\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" stroke-width=\"2\">\n        <circle cx=\"12\" cy=\"12\" r=\"3\"></circle>\n        <path d=\"M19.4 15a1.65 1.65 0 0 0 .33 1.82l.06.06a2 2 0 0 1 0 2.83 2 2 0 0 1-2.83 0l-.06-.06a1.65 1.65 0 0 0-1.82-.33 1.65 1.65 0 0 0-1 1.51V21a2 2 0 0 1-2 2 2 2 0 0 1-2-2v-.09A1.65 1.65 0 0 0 9 19.4a1.65 1.65 0 0 0-1.82.33l-.06.06a2 2 0 0 1-2.83 0 2 2 0 0 1 0-2.83l.06-.06a1.65 1.65 0 0 0 .33-1.82 1.65 1.65 0 0 0-1.51-1H3a2 2 0 0 1-2-2 2 2 0 0 1 2-2h.09A1.65 1.65 0 0 0 4.6 9a1.65 1.65 0 0 0-.33-1.82l-.06-.06a2 2 0 0 1 0-2.83 2 2 0 0 1 2.83 0l.06.06a1.65 1.65 0 0 0 1.82.33H9a1.65 1.65 0 0 0 1 1.51V3a2 2 0 0 1 2-2 2 2 0 0 1 2 2v.09a1.65 1.65 0 0 0 1 1.51 1.65 1.65 0 0 0 1.82-.33l.06-.06a2 2 0 0 1 2.83 0 2 2 0 0 1 0 2.83l-.06.06a1.65 1.65 0 0 0-.33 1.82V9a1.65 1.65 0 0 0 1.51 1H21a2 2 0 0 1 2 2 2 2 0 0 1-2 2h-.09a1.65 1.65 0 0 0-1.51 1z\"></path>\n      </svg>`,\n      database: `<svg width=\"16\" height=\"16\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" stroke-width=\"2\">\n        <ellipse cx=\"12\" cy=\"5\" rx=\"9\" ry=\"3\"></ellipse>\n        <path d=\"M21 12c0 1.66-4 3-9 3s-9-1.34-9-3\"></path>\n        <path d=\"M3 5v14c0 1.66 4 3 9 3s9-1.34 9-3V5\"></path>\n      </svg>`,\n      star: `<svg width=\"16\" height=\"16\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" stroke-width=\"2\">\n        <polygon points=\"12,2 15.09,8.26 22,9.27 17,14.14 18.18,21.02 12,17.77 5.82,21.02 7,14.14 2,9.27 8.91,8.26\"></polygon>\n      </svg>`,\n      request: `<svg width=\"16\" height=\"16\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" stroke-width=\"2\">\n        <path d=\"M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z\"></path>\n        <polyline points=\"14,2 14,8 20,8\"></polyline>\n        <line x1=\"16\" y1=\"13\" x2=\"8\" y2=\"13\"></line>\n        <line x1=\"16\" y1=\"17\" x2=\"8\" y2=\"17\"></line>\n        <polyline points=\"10,9 9,9 8,9\"></polyline>\n      </svg>`\n    };\n\n    const iconSvg = icons[iconName] || '';\n    return this.sanitizer.bypassSecurityTrustHtml(iconSvg);\n  }\n}\n", "<aside class=\"sidebar\"\n       [class.collapsed]=\"isCollapsed\"\n       [class.mobile]=\"isMobile\"\n       [class.tablet]=\"isTablet\">\n\n  <!-- Overlay pour mobile -->\n  <div class=\"sidebar-overlay\"\n       *ngIf=\"isMobile && !isCollapsed\"\n       (click)=\"onCloseSidebar()\"></div>\n\n  <div class=\"sidebar-header\">\n    <div class=\"logo\">\n      <div class=\"logo-icon\">\n        <svg width=\"32\" height=\"32\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" stroke-width=\"2\">\n          <path d=\"M21 16V8a2 2 0 0 0-1-1.73l-7-4a2 2 0 0 0-2 0l-7 4A2 2 0 0 0 3 8v8a2 2 0 0 0 1 1.73l7 4a2 2 0 0 0 2 0l7-4A2 2 0 0 0 21 16z\"></path>\n          <polyline points=\"3.27,6.96 12,12.01 20.73,6.96\"></polyline>\n          <line x1=\"12\" y1=\"22.08\" x2=\"12\" y2=\"12\"></line>\n        </svg>\n      </div>\n      <div class=\"logo-text\" *ngIf=\"!isCollapsed\">\n        <span class=\"logo-title\">FournisseurApp</span>\n        <span class=\"logo-subtitle\">Gestion</span>\n      </div>\n    </div>\n\n    <!-- Bouton de fermeture pour mobile -->\n    <button class=\"close-btn\"\n            *ngIf=\"isMobile && !isCollapsed\"\n            (click)=\"onCloseSidebar()\"\n            type=\"button\"\n            aria-label=\"Fermer le menu\">\n      <svg width=\"24\" height=\"24\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" stroke-width=\"2\">\n        <line x1=\"18\" y1=\"6\" x2=\"6\" y2=\"18\"></line>\n        <line x1=\"6\" y1=\"6\" x2=\"18\" y2=\"18\"></line>\n      </svg>\n    </button>\n  </div>\n\n  <nav class=\"sidebar-nav\">\n    <ul class=\"nav-list\">\n      <li class=\"nav-item\" *ngFor=\"let item of menuItems\">\n        <!-- Menu item with children -->\n        <div *ngIf=\"item.children; else singleItem\" class=\"nav-item-wrapper\">\n          <button \n            class=\"nav-link parent-link\"\n            [class.active]=\"isParentActive(item)\"\n            [class.expanded]=\"item.expanded\"\n            (click)=\"toggleSubmenu(item)\"\n            type=\"button\"\n          >\n            <span class=\"nav-icon\" [innerHTML]=\"getIconSvg(item.icon)\"></span>\n            <span class=\"nav-text\" *ngIf=\"!isCollapsed\">{{ item.label }}</span>\n            <span class=\"nav-arrow\" *ngIf=\"!isCollapsed\">\n              <svg width=\"16\" height=\"16\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" stroke-width=\"2\">\n                <polyline points=\"6,9 12,15 18,9\"></polyline>\n              </svg>\n            </span>\n          </button>\n          \n          <ul class=\"submenu\" [class.expanded]=\"item.expanded && !isCollapsed\">\n            <li class=\"submenu-item\" *ngFor=\"let child of item.children\">\n              <button\n                class=\"submenu-link\"\n                [class.active]=\"isActiveRoute(child.route!)\"\n                (click)=\"navigateTo(child.route!)\"\n                type=\"button\"\n              >\n                <span class=\"submenu-icon\" [innerHTML]=\"getIconSvg(child.icon)\"></span>\n                <span class=\"submenu-text\">{{ child.label }}</span>\n              </button>\n            </li>\n          </ul>\n        </div>\n\n        <!-- Single menu item -->\n        <ng-template #singleItem>\n          <button\n            class=\"nav-link\"\n            [class.active]=\"isActiveRoute(item.route!)\"\n            (click)=\"navigateTo(item.route!)\"\n            type=\"button\"\n          >\n            <span class=\"nav-icon\" [innerHTML]=\"getIconSvg(item.icon)\"></span>\n            <span class=\"nav-text\" *ngIf=\"!isCollapsed\">{{ item.label }}</span>\n          </button>\n        </ng-template>\n      </li>\n    </ul>\n  </nav>\n\n  <div class=\"sidebar-footer\" *ngIf=\"!isCollapsed\">\n    <div class=\"footer-content\">\n      <p class=\"footer-text\">Version 1.0.0</p>\n      <p class=\"footer-copyright\">© 2024 FournisseurApp</p>\n    </div>\n  </div>\n</aside>\n", "import { Component, OnInit, HostListener } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { RouterOutlet } from '@angular/router';\nimport { HeaderComponent } from '../header/header.component';\nimport { SidebarComponent } from '../sidebar/sidebar.component';\n\n@Component({\n  selector: 'app-dashboard-layout',\n  standalone: true,\n  imports: [CommonModule, RouterOutlet, HeaderComponent, SidebarComponent],\n  templateUrl: './dashboard-layout.component.html',\n  styleUrls: ['./dashboard-layout.component.css']\n})\nexport class DashboardLayoutComponent implements OnInit {\n  isSidebarCollapsed = false;\n  isMobile = false;\n  showMobileSidebar = false;\n\n  ngOnInit(): void {\n    this.checkScreenSize();\n  }\n\n  @HostListener('window:resize', ['$event'])\n  onResize(event: any): void {\n    this.checkScreenSize();\n  }\n\n  private checkScreenSize(): void {\n    this.isMobile = window.innerWidth < 768;\n    \n    if (this.isMobile) {\n      this.isSidebarCollapsed = false;\n      this.showMobileSidebar = false;\n    }\n  }\n\n  onToggleSidebar(): void {\n    if (this.isMobile) {\n      this.showMobileSidebar = !this.showMobileSidebar;\n    } else {\n      this.isSidebarCollapsed = !this.isSidebarCollapsed;\n    }\n  }\n\n  onCloseMobileSidebar(): void {\n    if (this.isMobile) {\n      this.showMobileSidebar = false;\n    }\n  }\n}\n", "<div class=\"dashboard-layout\">\n  <!-- Mobile Overlay -->\n  <div \n    class=\"mobile-overlay\" \n    [class.show]=\"showMobileSidebar && isMobile\"\n    (click)=\"onCloseMobileSidebar()\"\n  ></div>\n\n  <!-- Sidebar -->\n  <div class=\"sidebar-container\" [class.mobile-show]=\"showMobileSidebar && isMobile\">\n    <app-sidebar\n      [isCollapsed]=\"isMobile ? !showMobileSidebar : isSidebarCollapsed\"\n      (toggleSidebar)=\"onToggleSidebar()\"\n      (closeSidebar)=\"onCloseMobileSidebar()\"\n    ></app-sidebar>\n  </div>\n\n  <!-- Main Content Area -->\n  <div class=\"main-container\" [class.sidebar-collapsed]=\"isSidebarCollapsed && !isMobile\">\n    <!-- Header -->\n    <app-header (toggleSidebar)=\"onToggleSidebar()\"></app-header>\n\n    <!-- Page Content -->\n    <main class=\"main-content\">\n      <div class=\"content-wrapper\">\n        <router-outlet></router-outlet>\n      </div>\n    </main>\n  </div>\n</div>\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACeI,IAAA,yBAAA,GAAA,OAAA,EAAA;AACE,IAAA,oBAAA,GAAA,uBAAA;AACF,IAAA,uBAAA;;;ADFE,IAAO,kBAAP,MAAO,iBAAe;EAOhB;EACA;EACA;EARA,gBAAgB,IAAI,aAAY;EAE1C,cAA2B;EAC3B,eAAe;EAEf,YACU,aACA,kBACA,QAAc;AAFd,SAAA,cAAA;AACA,SAAA,mBAAA;AACA,SAAA,SAAA;EACP;EAEH,WAAQ;AACN,SAAK,YAAY,aAAa,UAAU,UAAO;AAC7C,WAAK,cAAc;IACrB,CAAC;EACH;EAEA,kBAAe;AACb,SAAK,cAAc,KAAI;EACzB;EAEA,iBAAc;AACZ,SAAK,eAAe,CAAC,KAAK;EAC5B;EAEA,gBAAa;AACX,SAAK,eAAe;EACtB;EAEA,oBAAiB;AACf,SAAK,cAAa;AAClB,SAAK,OAAO,SAAS,CAAC,mBAAmB,CAAC;EAC5C;EAEA,SAAM;AACJ,SAAK,cAAa;AAClB,SAAK,YAAY,OAAM;AACvB,SAAK,OAAO,SAAS,CAAC,QAAQ,CAAC;EACjC;EAEA,kBAAe;AACb,QAAI,CAAC,KAAK;AAAa,aAAO;AAE9B,UAAM,eAAe,KAAK,YAAY,QAAQ,OAAO,CAAC,EAAE,YAAW,KAAM;AACzE,UAAM,cAAc,KAAK,YAAY,KAAK,OAAO,CAAC,EAAE,YAAW,KAAM;AAErE,WAAO,eAAe;EACxB;EAEA,kBAAe;AACb,QAAI,CAAC,KAAK;AAAa,aAAO;AAE9B,WAAO,GAAG,KAAK,YAAY,MAAM,IAAI,KAAK,YAAY,GAAG;EAC3D;EAEA,IAAI,sBAAmB;AACrB,WAAO;EACT;EAEA,UAAO;AACL,WAAO,KAAK,iBAAiB,eAAc,MAAO;EACpD;;qCA9DW,kBAAe,4BAAA,WAAA,GAAA,4BAAA,gBAAA,GAAA,4BAAA,MAAA,CAAA;EAAA;yEAAf,kBAAe,WAAA,CAAA,CAAA,YAAA,CAAA,GAAA,SAAA,EAAA,eAAA,gBAAA,GAAA,OAAA,IAAA,MAAA,IAAA,QAAA,CAAA,CAAA,GAAA,QAAA,GAAA,CAAA,GAAA,aAAA,GAAA,CAAA,QAAA,UAAA,GAAA,kBAAA,GAAA,OAAA,GAAA,CAAA,GAAA,gBAAA,GAAA,CAAA,GAAA,cAAA,GAAA,CAAA,GAAA,cAAA,GAAA,CAAA,SAAA,wBAAA,GAAA,MAAA,GAAA,CAAA,GAAA,aAAA,GAAA,cAAA,GAAA,CAAA,QAAA,UAAA,GAAA,qBAAA,GAAA,OAAA,GAAA,CAAA,GAAA,aAAA,GAAA,CAAA,GAAA,WAAA,GAAA,CAAA,GAAA,WAAA,GAAA,CAAA,GAAA,WAAA,GAAA,CAAA,SAAA,MAAA,UAAA,MAAA,WAAA,aAAA,QAAA,QAAA,UAAA,gBAAA,gBAAA,KAAA,GAAA,gBAAA,GAAA,CAAA,UAAA,gBAAA,GAAA,CAAA,GAAA,eAAA,GAAA,CAAA,GAAA,iBAAA,GAAA,CAAA,GAAA,mBAAA,GAAA,CAAA,GAAA,cAAA,GAAA,CAAA,GAAA,YAAA,GAAA,CAAA,GAAA,kBAAA,GAAA,CAAA,GAAA,eAAA,GAAA,CAAA,QAAA,UAAA,GAAA,iBAAA,GAAA,OAAA,GAAA,CAAA,SAAA,MAAA,UAAA,MAAA,WAAA,aAAA,QAAA,QAAA,UAAA,gBAAA,gBAAA,GAAA,GAAA,CAAA,KAAA,2CAAA,GAAA,CAAA,MAAA,MAAA,MAAA,KAAA,KAAA,GAAA,GAAA,CAAA,QAAA,UAAA,GAAA,eAAA,GAAA,CAAA,MAAA,MAAA,MAAA,MAAA,KAAA,GAAA,GAAA,CAAA,KAAA,guBAAA,GAAA,CAAA,QAAA,UAAA,GAAA,iBAAA,eAAA,GAAA,OAAA,GAAA,CAAA,KAAA,yCAAA,GAAA,CAAA,UAAA,kBAAA,GAAA,CAAA,MAAA,MAAA,MAAA,MAAA,MAAA,KAAA,MAAA,IAAA,GAAA,CAAA,GAAA,sBAAA,CAAA,GAAA,UAAA,SAAA,yBAAA,IAAA,KAAA;AAAA,QAAA,KAAA,GAAA;ACf5B,MAAA,yBAAA,GAAA,UAAA,CAAA,EAAuB,GAAA,OAAA,CAAA,EACI,GAAA,UAAA,CAAA;AACQ,MAAA,qBAAA,SAAA,SAAA,mDAAA;AAAA,eAAS,IAAA,gBAAA;MAAiB,CAAA;AACvD,MAAA,oBAAA,GAAA,QAAA,CAAA,EAAoC,GAAA,QAAA,CAAA,EACA,GAAA,QAAA,CAAA;AAEtC,MAAA,uBAAA;AAEA,MAAA,yBAAA,GAAA,OAAA,CAAA,EAA0B,GAAA,IAAA;AACpB,MAAA,iBAAA,GAAA,oBAAA;AAAkB,MAAA,uBAAA,EAAK,EACvB;AAGR,MAAA,yBAAA,GAAA,OAAA,CAAA;AAEE,MAAA,qBAAA,IAAA,iCAAA,GAAA,GAAA,OAAA,CAAA;AAKA,MAAA,yBAAA,IAAA,OAAA,CAAA;AAAuB,MAAA,qBAAA,gBAAA,SAAA,wDAAA;AAAA,eAAgB,IAAA,cAAA;MAAe,CAAA;AACpD,MAAA,yBAAA,IAAA,UAAA,CAAA;AAAkC,MAAA,qBAAA,SAAA,SAAA,oDAAA;AAAA,eAAS,IAAA,eAAA;MAAgB,CAAA;AACzD,MAAA,yBAAA,IAAA,OAAA,CAAA;AACE,MAAA,iBAAA,EAAA;AACF,MAAA,uBAAA;AACA,MAAA,yBAAA,IAAA,OAAA,EAAA,EAAuB,IAAA,QAAA,EAAA;AACG,MAAA,iBAAA,EAAA;AAAuB,MAAA,uBAAA;AAC/C,MAAA,yBAAA,IAAA,QAAA,EAAA;AAAwB,MAAA,iBAAA,IAAA,aAAA;AAAW,MAAA,uBAAA,EAAO;;AAE5C,MAAA,yBAAA,IAAA,OAAA,EAAA;AACE,MAAA,oBAAA,IAAA,YAAA,EAAA;AACF,MAAA,uBAAA,EAAM;;AAGR,MAAA,yBAAA,IAAA,OAAA,EAAA,EAAuD,IAAA,OAAA,EAAA,EACxB,IAAA,OAAA,EAAA;AAEzB,MAAA,iBAAA,EAAA;AACF,MAAA,uBAAA;AACA,MAAA,yBAAA,IAAA,OAAA,EAAA,EAA0B,IAAA,OAAA,EAAA;AACD,MAAA,iBAAA,EAAA;AAAuB,MAAA,uBAAA;AAC9C,MAAA,yBAAA,IAAA,OAAA,EAAA;AAAwB,MAAA,iBAAA,EAAA;AAAwB,MAAA,uBAAA,EAAM,EAClD;AAGR,MAAA,oBAAA,IAAA,OAAA,EAAA;AAEA,MAAA,yBAAA,IAAA,OAAA,EAAA,EAA2B,IAAA,UAAA,EAAA;AACK,MAAA,qBAAA,SAAA,SAAA,oDAAA;AAAA,eAAS,IAAA,kBAAA;MAAmB,CAAA;;AACxD,MAAA,yBAAA,IAAA,OAAA,EAAA;AACE,MAAA,oBAAA,IAAA,QAAA,EAAA,EAA2D,IAAA,UAAA,EAAA;AAE7D,MAAA,uBAAA;AACA,MAAA,iBAAA,IAAA,cAAA;AACF,MAAA,uBAAA;;AAEA,MAAA,yBAAA,IAAA,UAAA,EAAA;;AACE,MAAA,yBAAA,IAAA,OAAA,EAAA;AACE,MAAA,oBAAA,IAAA,UAAA,EAAA,EAAuC,IAAA,QAAA,EAAA;AAEzC,MAAA,uBAAA;AACA,MAAA,iBAAA,IAAA,iBAAA;AACF,MAAA,uBAAA,EAAS;;AAGX,MAAA,oBAAA,IAAA,OAAA,EAAA;AAEA,MAAA,yBAAA,IAAA,UAAA,EAAA;AAA0C,MAAA,qBAAA,SAAA,SAAA,oDAAA;AAAA,eAAS,IAAA,OAAA;MAAQ,CAAA;;AACzD,MAAA,yBAAA,IAAA,OAAA,EAAA;AACE,MAAA,oBAAA,IAAA,QAAA,EAAA,EAAyD,IAAA,YAAA,EAAA,EACV,IAAA,QAAA,EAAA;AAEjD,MAAA,uBAAA;AACA,MAAA,iBAAA,IAAA,kBAAA;AACF,MAAA,uBAAA,EAAS,EACL,EACF,EACF;;;AA9D+B,MAAA,oBAAA,EAAA;AAAA,MAAA,qBAAA,QAAA,CAAA,IAAA,QAAA,CAAA;AAQ7B,MAAA,oBAAA,CAAA;AAAA,MAAA,6BAAA,KAAA,IAAA,gBAAA,GAAA,GAAA;AAGwB,MAAA,oBAAA,CAAA;AAAA,MAAA,4BAAA,IAAA,gBAAA,CAAA;AAGE,MAAA,oBAAA,CAAA;AAAA,MAAA,sBAAA,WAAA,IAAA,YAAA;AAKH,MAAA,oBAAA,CAAA;AAAA,MAAA,sBAAA,QAAA,IAAA,YAAA;AAGrB,MAAA,oBAAA,CAAA;AAAA,MAAA,6BAAA,KAAA,IAAA,gBAAA,GAAA,GAAA;AAGuB,MAAA,oBAAA,CAAA;AAAA,MAAA,4BAAA,IAAA,gBAAA,CAAA;AACC,MAAA,oBAAA,CAAA;AAAA,MAAA,4BAAA,IAAA,eAAA,OAAA,OAAA,IAAA,YAAA,KAAA;;oBD9BxB,cAAY,MAAE,yBAAyB,GAAA,QAAA,CAAA,26SAAA,EAAA,CAAA;;;sEAItC,iBAAe,CAAA;UAP3B;uBACW,cAAY,YACV,MAAI,SACP,CAAC,cAAc,yBAAyB,GAAC,UAAA,wwHAAA,QAAA,CAAA,olQAAA,EAAA,CAAA;qFAKxC,eAAa,CAAA;UAAtB;;;;6EADU,iBAAe,EAAA,WAAA,mBAAA,UAAA,wDAAA,YAAA,GAAA,CAAA;AAAA,GAAA;;;;;;AGT1B,IAAA,yBAAA,GAAA,OAAA,EAAA;AAEK,IAAA,qBAAA,SAAA,SAAA,uDAAA;AAAA,MAAA,wBAAA,GAAA;AAAA,YAAA,SAAA,wBAAA;AAAA,aAAA,sBAAS,OAAA,eAAA,CAAgB;IAAA,CAAA;AAAE,IAAA,uBAAA;;;;;AAW5B,IAAA,yBAAA,GAAA,OAAA,EAAA,EAA4C,GAAA,QAAA,EAAA;AACjB,IAAA,iBAAA,GAAA,gBAAA;AAAc,IAAA,uBAAA;AACvC,IAAA,yBAAA,GAAA,QAAA,EAAA;AAA4B,IAAA,iBAAA,GAAA,SAAA;AAAO,IAAA,uBAAA,EAAO;;;;;;AAK9C,IAAA,yBAAA,GAAA,UAAA,EAAA;AAEQ,IAAA,qBAAA,SAAA,SAAA,8DAAA;AAAA,MAAA,wBAAA,GAAA;AAAA,YAAA,SAAA,wBAAA;AAAA,aAAA,sBAAS,OAAA,eAAA,CAAgB;IAAA,CAAA;;AAG/B,IAAA,yBAAA,GAAA,OAAA,EAAA;AACE,IAAA,oBAAA,GAAA,QAAA,EAAA,EAA2C,GAAA,QAAA,EAAA;AAE7C,IAAA,uBAAA,EAAM;;;;;AAiBA,IAAA,yBAAA,GAAA,QAAA,EAAA;AAA4C,IAAA,iBAAA,CAAA;AAAgB,IAAA,uBAAA;;;;AAAhB,IAAA,oBAAA;AAAA,IAAA,4BAAA,QAAA,KAAA;;;;;AAC5C,IAAA,yBAAA,GAAA,QAAA,EAAA;;AACE,IAAA,yBAAA,GAAA,OAAA,EAAA;AACE,IAAA,oBAAA,GAAA,YAAA,EAAA;AACF,IAAA,uBAAA,EAAM;;;;;;AAKR,IAAA,yBAAA,GAAA,MAAA,EAAA,EAA6D,GAAA,UAAA,EAAA;AAIzD,IAAA,qBAAA,SAAA,SAAA,qEAAA;AAAA,YAAA,WAAA,wBAAA,GAAA,EAAA;AAAA,YAAA,SAAA,wBAAA,CAAA;AAAA,aAAA,sBAAS,OAAA,WAAA,SAAA,KAAA,CAAwB;IAAA,CAAA;AAGjC,IAAA,oBAAA,GAAA,QAAA,EAAA;AACA,IAAA,yBAAA,GAAA,QAAA,EAAA;AAA2B,IAAA,iBAAA,CAAA;AAAiB,IAAA,uBAAA,EAAO,EAC5C;;;;;AANP,IAAA,oBAAA;AAAA,IAAA,sBAAA,UAAA,OAAA,cAAA,SAAA,KAAA,CAAA;AAI2B,IAAA,oBAAA;AAAA,IAAA,qBAAA,aAAA,OAAA,WAAA,SAAA,IAAA,GAAA,wBAAA;AACA,IAAA,oBAAA,CAAA;AAAA,IAAA,4BAAA,SAAA,KAAA;;;;;;AA1BnC,IAAA,yBAAA,GAAA,OAAA,EAAA,EAAqE,GAAA,UAAA,EAAA;AAKjE,IAAA,qBAAA,SAAA,SAAA,gEAAA;AAAA,MAAA,wBAAA,GAAA;AAAA,YAAA,UAAA,wBAAA,EAAA;AAAA,YAAA,SAAA,wBAAA;AAAA,aAAA,sBAAS,OAAA,cAAA,OAAA,CAAmB;IAAA,CAAA;AAG5B,IAAA,oBAAA,GAAA,QAAA,EAAA;AACA,IAAA,qBAAA,GAAA,8CAAA,GAAA,GAAA,QAAA,EAAA,EAA4C,GAAA,8CAAA,GAAA,GAAA,QAAA,EAAA;AAM9C,IAAA,uBAAA;AAEA,IAAA,yBAAA,GAAA,MAAA,EAAA;AACE,IAAA,qBAAA,GAAA,4CAAA,GAAA,GAAA,MAAA,EAAA;AAWF,IAAA,uBAAA,EAAK;;;;;AA1BH,IAAA,oBAAA;AAAA,IAAA,sBAAA,UAAA,OAAA,eAAA,OAAA,CAAA,EAAqC,YAAA,QAAA,QAAA;AAKd,IAAA,oBAAA;AAAA,IAAA,qBAAA,aAAA,OAAA,WAAA,QAAA,IAAA,GAAA,wBAAA;AACC,IAAA,oBAAA;AAAA,IAAA,qBAAA,QAAA,CAAA,OAAA,WAAA;AACC,IAAA,oBAAA;AAAA,IAAA,qBAAA,QAAA,CAAA,OAAA,WAAA;AAOP,IAAA,oBAAA;AAAA,IAAA,sBAAA,YAAA,QAAA,YAAA,CAAA,OAAA,WAAA;AACyB,IAAA,oBAAA;AAAA,IAAA,qBAAA,WAAA,QAAA,QAAA;;;;;AAuB3C,IAAA,yBAAA,GAAA,QAAA,EAAA;AAA4C,IAAA,iBAAA,CAAA;AAAgB,IAAA,uBAAA;;;;AAAhB,IAAA,oBAAA;AAAA,IAAA,4BAAA,QAAA,KAAA;;;;;;AAP9C,IAAA,yBAAA,GAAA,UAAA,EAAA;AAGE,IAAA,qBAAA,SAAA,SAAA,wEAAA;AAAA,MAAA,wBAAA,GAAA;AAAA,YAAA,UAAA,wBAAA,EAAA;AAAA,YAAA,SAAA,wBAAA;AAAA,aAAA,sBAAS,OAAA,WAAA,QAAA,KAAA,CAAuB;IAAA,CAAA;AAGhC,IAAA,oBAAA,GAAA,QAAA,EAAA;AACA,IAAA,qBAAA,GAAA,sDAAA,GAAA,GAAA,QAAA,EAAA;AACF,IAAA,uBAAA;;;;;AANE,IAAA,sBAAA,UAAA,OAAA,cAAA,QAAA,KAAA,CAAA;AAIuB,IAAA,oBAAA;AAAA,IAAA,qBAAA,aAAA,OAAA,WAAA,QAAA,IAAA,GAAA,wBAAA;AACC,IAAA,oBAAA;AAAA,IAAA,qBAAA,QAAA,CAAA,OAAA,WAAA;;;;;AA3C9B,IAAA,yBAAA,GAAA,MAAA,EAAA;AAEE,IAAA,qBAAA,GAAA,uCAAA,GAAA,IAAA,OAAA,EAAA,EAAqE,GAAA,+CAAA,GAAA,GAAA,eAAA,MAAA,GAAA,gCAAA;AA4CvE,IAAA,uBAAA;;;;;AA5CQ,IAAA,oBAAA;AAAA,IAAA,qBAAA,QAAA,QAAA,QAAA,EAAqB,YAAA,aAAA;;;;;AAgDjC,IAAA,yBAAA,GAAA,OAAA,EAAA,EAAiD,GAAA,OAAA,EAAA,EACnB,GAAA,KAAA,EAAA;AACH,IAAA,iBAAA,GAAA,eAAA;AAAa,IAAA,uBAAA;AACpC,IAAA,yBAAA,GAAA,KAAA,EAAA;AAA4B,IAAA,iBAAA,GAAA,0BAAA;AAAqB,IAAA,uBAAA,EAAI,EACjD;;;AD1EJ,IAAO,mBAAP,MAAO,kBAAgB;EAkFjB;EACA;EAlFD,cAAc;EACb,gBAAgB,IAAI,aAAY;EAChC,eAAe,IAAI,aAAY;EAEzC,WAAW;EACX,WAAW;EAEX,YAAwB;IACtB;MACE,OAAO;MACP,MAAM;MACN,OAAO;;IAET;MACE,OAAO;MACP,MAAM;MACN,UAAU;QACR;UACE,OAAO;UACP,MAAM;UACN,OAAO;;;;IAIb;MACE,OAAO;MACP,MAAM;MACN,UAAU;QACR;UACE,OAAO;UACP,MAAM;UACN,OAAO;;QAET;UACE,OAAO;UACP,MAAM;UACN,OAAO;;;;IAIb;MACE,OAAO;MACP,MAAM;MACN,UAAU;QACR;UACE,OAAO;UACP,MAAM;UACN,OAAO;;QAET;UACE,OAAO;UACP,MAAM;UACN,OAAO;;;;IAIb;MACE,OAAO;MACP,MAAM;MACN,UAAU;QAER;UACE,OAAO;UACP,MAAM;UACN,OAAO;;;;IAIb;MACE,OAAO;MACP,MAAM;MACN,OAAO;;IAET;MACE,OAAO;MACP,MAAM;MACN,OAAO;;;EAIX,YACU,QACA,WAAuB;AADvB,SAAA,SAAA;AACA,SAAA,YAAA;EACP;EAEH,WAAQ;AACN,SAAK,gBAAe;EACtB;EAEA,cAAW;EAEX;EAGA,WAAQ;AACN,SAAK,gBAAe;EACtB;;;;EAKQ,kBAAe;AACrB,UAAM,QAAQ,OAAO;AACrB,SAAK,WAAW,QAAQ;AACxB,SAAK,WAAW,SAAS,OAAO,QAAQ;AAGxC,QAAI,KAAK,YAAY,CAAC,KAAK,aAAa;AACtC,WAAK,cAAc;IACrB;EACF;EAEA,cAAc,MAAc;AAC1B,QAAI,KAAK,UAAU;AACjB,WAAK,WAAW,CAAC,KAAK;IACxB;EACF;EAEA,WAAW,OAAa;AACtB,SAAK,OAAO,SAAS,CAAC,KAAK,CAAC;AAG5B,QAAI,KAAK,UAAU;AACjB,WAAK,aAAa,KAAI;IACxB;EACF;;;;EAKA,kBAAe;AACb,SAAK,cAAc,KAAI;EACzB;;;;EAKA,iBAAc;AACZ,SAAK,aAAa,KAAI;EACxB;EAEA,cAAc,OAAa;AACzB,WAAO,KAAK,OAAO,QAAQ;EAC7B;EAEA,eAAe,MAAc;AAC3B,QAAI,CAAC,KAAK;AAAU,aAAO;AAE3B,WAAO,KAAK,SAAS,KAAK,WACxB,MAAM,SAAS,KAAK,cAAc,MAAM,KAAK,CAAC;EAElD;EAEA,WAAW,UAAgB;AACzB,UAAM,QAAmC;MACvC,WAAW;;;;;;MAMX,MAAM;;;;MAIN,SAAS;;;;;;MAMT,QAAQ;;;;MAIR,OAAO;;;;;;MAMP,MAAM;;;;;MAKN,MAAM;;;;MAIN,MAAM;;;;;;;;MAQN,MAAM;;;;MAIN,WAAW;;;;;MAKX,OAAO;;;;MAIP,SAAS;;;;MAIT,UAAU;;;;MAIV,UAAU;;;;;MAKV,MAAM;;;MAGN,SAAS;;;;;;;;AASX,UAAM,UAAU,MAAM,QAAQ,KAAK;AACnC,WAAO,KAAK,UAAU,wBAAwB,OAAO;EACvD;;qCA/OW,mBAAgB,4BAAA,MAAA,GAAA,4BAAA,YAAA,CAAA;EAAA;yEAAhB,mBAAgB,WAAA,CAAA,CAAA,aAAA,CAAA,GAAA,cAAA,SAAA,8BAAA,IAAA,KAAA;AAAA,QAAA,KAAA,GAAA;AAAhB,MAAA,qBAAA,UAAA,SAAA,2CAAA,QAAA;AAAA,eAAA,IAAA,SAAA,MAAA;MAAgB,GAAA,OAAA,yBAAA;;;;ACpB7B,MAAA,yBAAA,GAAA,SAAA,CAAA;AAME,MAAA,qBAAA,GAAA,iCAAA,GAAA,GAAA,OAAA,CAAA;AAIA,MAAA,yBAAA,GAAA,OAAA,CAAA,EAA4B,GAAA,OAAA,CAAA,EACR,GAAA,OAAA,CAAA;;AAEd,MAAA,yBAAA,GAAA,OAAA,CAAA;AACE,MAAA,oBAAA,GAAA,QAAA,CAAA,EAA2I,GAAA,YAAA,CAAA,EAC/E,GAAA,QAAA,CAAA;AAE9D,MAAA,uBAAA,EAAM;AAER,MAAA,qBAAA,GAAA,iCAAA,GAAA,GAAA,OAAA,EAAA;AAIF,MAAA,uBAAA;AAGA,MAAA,qBAAA,IAAA,qCAAA,GAAA,GAAA,UAAA,EAAA;AAUF,MAAA,uBAAA;;AAEA,MAAA,yBAAA,IAAA,OAAA,EAAA,EAAyB,IAAA,MAAA,EAAA;AAErB,MAAA,qBAAA,IAAA,iCAAA,GAAA,GAAA,MAAA,EAAA;AA+CF,MAAA,uBAAA,EAAK;AAGP,MAAA,qBAAA,IAAA,kCAAA,GAAA,GAAA,OAAA,EAAA;AAMF,MAAA,uBAAA;;;AA/FO,MAAA,sBAAA,aAAA,IAAA,WAAA,EAA+B,UAAA,IAAA,QAAA,EACN,UAAA,IAAA,QAAA;AAKxB,MAAA,oBAAA;AAAA,MAAA,qBAAA,QAAA,IAAA,YAAA,CAAA,IAAA,WAAA;AAYsB,MAAA,oBAAA,CAAA;AAAA,MAAA,qBAAA,QAAA,CAAA,IAAA,WAAA;AAQjB,MAAA,oBAAA;AAAA,MAAA,qBAAA,QAAA,IAAA,YAAA,CAAA,IAAA,WAAA;AAa+B,MAAA,oBAAA,CAAA;AAAA,MAAA,qBAAA,WAAA,IAAA,SAAA;AAkDb,MAAA,oBAAA;AAAA,MAAA,qBAAA,QAAA,CAAA,IAAA,WAAA;;oBD1EnB,cAAY,SAAA,MAAE,YAAY,GAAA,QAAA,CAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kDAAA,EAAA,CAAA;;;sEAIzB,kBAAgB,CAAA;UAP5B;uBACW,eAAa,YACX,MAAI,SACP,CAAC,cAAc,YAAY,GAAC,UAAA,gsHAAA,QAAA,CAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;CAAA,EAAA,CAAA;0DAK5B,aAAW,CAAA;UAAnB;MACS,eAAa,CAAA;UAAtB;MACS,cAAY,CAAA;UAArB;MA4FD,UAAQ,CAAA;UADP;WAAa,iBAAiB,CAAC,QAAQ,CAAC;;;;6EA9F9B,kBAAgB,EAAA,WAAA,oBAAA,UAAA,0DAAA,YAAA,GAAA,CAAA;AAAA,GAAA;;;AEPvB,IAAO,2BAAP,MAAO,0BAAwB;EACnC,qBAAqB;EACrB,WAAW;EACX,oBAAoB;EAEpB,WAAQ;AACN,SAAK,gBAAe;EACtB;EAGA,SAAS,OAAU;AACjB,SAAK,gBAAe;EACtB;EAEQ,kBAAe;AACrB,SAAK,WAAW,OAAO,aAAa;AAEpC,QAAI,KAAK,UAAU;AACjB,WAAK,qBAAqB;AAC1B,WAAK,oBAAoB;IAC3B;EACF;EAEA,kBAAe;AACb,QAAI,KAAK,UAAU;AACjB,WAAK,oBAAoB,CAAC,KAAK;IACjC,OAAO;AACL,WAAK,qBAAqB,CAAC,KAAK;IAClC;EACF;EAEA,uBAAoB;AAClB,QAAI,KAAK,UAAU;AACjB,WAAK,oBAAoB;IAC3B;EACF;;qCAnCW,2BAAwB;EAAA;yEAAxB,2BAAwB,WAAA,CAAA,CAAA,sBAAA,CAAA,GAAA,cAAA,SAAA,sCAAA,IAAA,KAAA;AAAA,QAAA,KAAA,GAAA;AAAxB,MAAA,qBAAA,UAAA,SAAA,mDAAA,QAAA;AAAA,eAAA,IAAA,SAAA,MAAA;MAAgB,GAAA,OAAA,yBAAA;;;;ACb7B,MAAA,yBAAA,GAAA,OAAA,CAAA,EAA8B,GAAA,OAAA,CAAA;AAK1B,MAAA,qBAAA,SAAA,SAAA,yDAAA;AAAA,eAAS,IAAA,qBAAA;MAAsB,CAAA;AAChC,MAAA,uBAAA;AAGD,MAAA,yBAAA,GAAA,OAAA,CAAA,EAAmF,GAAA,eAAA,CAAA;AAG/E,MAAA,qBAAA,iBAAA,SAAA,yEAAA;AAAA,eAAiB,IAAA,gBAAA;MAAiB,CAAA,EAAC,gBAAA,SAAA,wEAAA;AAAA,eACnB,IAAA,qBAAA;MAAsB,CAAA;AACvC,MAAA,uBAAA,EAAc;AAIjB,MAAA,yBAAA,GAAA,OAAA,CAAA,EAAwF,GAAA,cAAA,CAAA;AAE1E,MAAA,qBAAA,iBAAA,SAAA,wEAAA;AAAA,eAAiB,IAAA,gBAAA;MAAiB,CAAA;AAAE,MAAA,uBAAA;AAGhD,MAAA,yBAAA,GAAA,QAAA,CAAA,EAA2B,GAAA,OAAA,CAAA;AAEvB,MAAA,oBAAA,GAAA,eAAA;AACF,MAAA,uBAAA,EAAM,EACD,EACH;;;AAxBJ,MAAA,oBAAA;AAAA,MAAA,sBAAA,QAAA,IAAA,qBAAA,IAAA,QAAA;AAK6B,MAAA,oBAAA;AAAA,MAAA,sBAAA,eAAA,IAAA,qBAAA,IAAA,QAAA;AAE3B,MAAA,oBAAA;AAAA,MAAA,qBAAA,eAAA,IAAA,WAAA,CAAA,IAAA,oBAAA,IAAA,kBAAA;AAOwB,MAAA,oBAAA;AAAA,MAAA,sBAAA,qBAAA,IAAA,sBAAA,CAAA,IAAA,QAAA;;oBDTlB,cAAc,cAAc,iBAAiB,gBAAgB,GAAA,QAAA,CAAA,goFAAA,EAAA,CAAA;;;sEAI5D,0BAAwB,CAAA;UAPpC;uBACW,wBAAsB,YACpB,MAAI,SACP,CAAC,cAAc,cAAc,iBAAiB,gBAAgB,GAAC,UAAA,u5BAAA,QAAA,CAAA,uwEAAA,EAAA,CAAA;cAcxE,UAAQ,CAAA;UADP;WAAa,iBAAiB,CAAC,QAAQ,CAAC;;;;6EAT9B,0BAAwB,EAAA,WAAA,4BAAA,UAAA,4EAAA,YAAA,GAAA,CAAA;AAAA,GAAA;", "names": []}
// Modèle Produit selon l'API backend réelle (ProduitDto)
export interface Produit {
  id: number;
  referenceOriginal: string;
  referenceFournisseur?: string;
  codeABarre: string;
  nom: string;
  description?: string;
  prixAchatHT: number;
  prixVenteHT: number;
  prixVenteTTC: number;
  prixApresRemises: number;
  prixApresRemisesOutlet: number;
  prixApresAutresPromotions: number;
  pourcentageRemiseTotale?: number;
  dateAjout: Date;
  estNouveau: boolean;
  tauxTVAId: number;
  stock: number;
  sousCategorieId: number;
  marqueId: number;
  formeId: number;
  fournisseurId: number;
  noteMoyenne: number;
  nombreAvis: number;

  // Navigation properties (optionnelles)
  avis?: AvisDto[];
  marque?: MarqueDto;
  forme?: FormeDto;
  fournisseur?: FournisseurDto;
  sousCategorie?: SousCategorieDto;
  tauxTVA?: TauxTVADto;  // Ajout de la propriété tauxTVA manquante
  images: ImageProduitDto[];
  imagePrincipaleUrl?: string;
}

// DTOs pour les relations
export interface AvisDto {
  id: number;
  note: number;
  commentaire?: string;
  dateCreation: Date;
  clientId: number;
  produitId: number;
}

export interface MarqueDto {
  id: number;
  name: string;  // Le backend utilise 'name' pas 'nom'
  logo: string;
}

export interface FormeDto {
  id: number;
  nom: string;
  categorieId: number;
  imageUrl: string;
}

export interface FournisseurDto {
  id: number;
  nom: string;
  prenom: string;
  email: string;
  raisonSociale: string;
}

export interface SousCategorieDto {
  id: number;
  nom: string;
  categorieId: number;
}

export interface TauxTVADto {
  id: number;
  libelle: string;  // Le backend utilise 'libelle' pas 'nom'
  taux: number;
  estActif: boolean;
}

export interface ImageProduitDto {
  id: number;
  produitId: number;
  imageUrl: string;
  altText?: string;
  isMain: boolean;
  ordre: number;
}

// Alias pour compatibilité
export interface ImageProduit extends ImageProduitDto {}

// Interface pour créer un produit selon ProduitCreateDto backend
export interface ProduitCreate {
  referenceOriginal: string;
  referenceFournisseur?: string;
  codeABarre: string;
  nom: string;
  description: string;
  prixAchatHT: number;
  prixVenteHT: number;
  tauxTVAId: number;
  stock: number;
  sousCategorieId: number;
  marqueId: number;
  formeId: number;
  fournisseurId: number;
  imageFiles?: File[];
  pourcentageRemise?: number;
}

// Interface pour mettre à jour un produit selon ProduitUpdateDto backend
export interface ProduitUpdate {
  id: number;
  nom: string;
  description: string;
  prixVenteHT?: number;
  stock?: number;
  pourcentageRemise?: number;
  imageFiles?: File[];
}

// Interfaces pour les mises à jour spécifiques
export interface StockUpdate {
  id: number;
  stock: number;
}

export interface PrixUpdate {
  nouveauPrix: number;
  motif?: string;
}



# Script pour creer un compte fournisseur de test
$apiUrl = "http://localhost:5014/api"

# Donnees du fournisseur de test
$testData = @{
    email = "<EMAIL>"
    password = "TestSupplier123!"
    nom = "TestSupplier"
    prenom = "Test"
    nomEntreprise = "Test Optics"
    telephone = "0123456789"
    adresse = "123 Test Street"
    ville = "Test City"
    codePostal = "12345"
    pays = "France"
    siret = "12345678901234"
    description = "Entreprise de test pour les avis"
}

try {
    Write-Host "Creation du compte fournisseur de test..." -ForegroundColor Yellow

    # Convertir en JSON
    $jsonData = $testData | ConvertTo-Json
    Write-Host "Donnees a envoyer:" -ForegroundColor Cyan
    Write-Host $jsonData

    # Envoyer la requete
    $response = Invoke-WebRequest -Uri "$apiUrl/Auth/register/fournisseur" -Method POST -ContentType "application/json" -Body $jsonData

    Write-Host "Compte cree avec succes!" -ForegroundColor Green
    Write-Host "Status: $($response.StatusCode)" -ForegroundColor Green
    Write-Host "Response: $($response.Content)" -ForegroundColor Green

    # Maintenant essayer de se connecter
    Write-Host "Test de connexion..." -ForegroundColor Yellow

    $loginData = @{
        email = $testData.email
        password = $testData.password
    } | ConvertTo-Json

    $loginResponse = Invoke-WebRequest -Uri "$apiUrl/Auth/login" -Method POST -ContentType "application/json" -Body $loginData
    $loginResult = $loginResponse.Content | ConvertFrom-Json

    Write-Host "Connexion reussie!" -ForegroundColor Green
    Write-Host "Token: $($loginResult.token.Substring(0, 20))..." -ForegroundColor Green

    # Tester l'endpoint des avis
    Write-Host "Test de l'endpoint des avis..." -ForegroundColor Yellow

    $headers = @{
        "Authorization" = "Bearer $($loginResult.token)"
        "Content-Type" = "application/json"
    }

    $avisResponse = Invoke-WebRequest -Uri "$apiUrl/AvisModeration/fournisseur" -Method GET -Headers $headers

    Write-Host "Endpoint des avis fonctionne!" -ForegroundColor Green
    Write-Host "Status: $($avisResponse.StatusCode)" -ForegroundColor Green
    Write-Host "Nombre d'avis: $(($avisResponse.Content | ConvertFrom-Json).Length)" -ForegroundColor Green

    # Tester l'endpoint des statistiques
    Write-Host "Test de l'endpoint des statistiques..." -ForegroundColor Yellow

    $statsResponse = Invoke-WebRequest -Uri "$apiUrl/AvisModeration/statistiques/fournisseur" -Method GET -Headers $headers

    Write-Host "Endpoint des statistiques fonctionne!" -ForegroundColor Green
    Write-Host "Status: $($statsResponse.StatusCode)" -ForegroundColor Green
    Write-Host "Statistiques: $($statsResponse.Content)" -ForegroundColor Green

    Write-Host "Tous les tests sont passes avec succes!" -ForegroundColor Green
    Write-Host "Email: $($testData.email)" -ForegroundColor Cyan
    Write-Host "Password: $($testData.password)" -ForegroundColor Cyan

} catch {
    Write-Host "Erreur: $($_.Exception.Message)" -ForegroundColor Red
    if ($_.Exception.Response) {
        $errorResponse = $_.Exception.Response.GetResponseStream()
        $reader = New-Object System.IO.StreamReader($errorResponse)
        $errorContent = $reader.ReadToEnd()
        Write-Host "Details de l'erreur: $errorContent" -ForegroundColor Red
    }
}

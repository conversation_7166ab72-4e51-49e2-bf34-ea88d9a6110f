{"version": 3, "sources": ["src/app/components/admin/category-management/category-management.component.ts", "src/app/components/admin/category-management/category-management.component.html"], "sourcesContent": ["import { Component, OnInit, signal, computed } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { FormsModule, ReactiveFormsModule, FormBuilder, FormGroup, Validators } from '@angular/forms';\nimport { AdminService } from '../../../services/admin.service';\nimport { CategorieService } from '../../../services/categorie.service';\nimport { SousCategorieService } from '../../../services/sous-categorie.service';\n\ninterface CategorieAdmin {\n  id: number;\n  nom: string;\n  description?: string;\n  estValide: boolean;\n  dateCreation: string;\n  nombreProduits: number;\n  sousCategories?: SousCategorieAdmin[];\n}\n\ninterface SousCategorieAdmin {\n  id: number;\n  nom: string;\n  description?: string;\n  categorieId: number;\n  categorieNom: string;\n  estValide: boolean;\n  dateCreation: string;\n  nombreProduits: number;\n}\n\n@Component({\n  selector: 'app-category-management',\n  standalone: true,\n  imports: [CommonModule, FormsModule, ReactiveFormsModule],\n  templateUrl: './category-management.component.html',\n  styleUrls: ['./category-management.component.css']\n})\nexport class CategoryManagementComponent implements OnInit {\n  // Angular 19: Signals\n  categories = signal<CategorieAdmin[]>([]);\n  sousCategories = signal<SousCategorieAdmin[]>([]);\n  isLoading = signal(false);\n  error = signal('');\n  activeTab = signal<'categories' | 'souscategories'>('categories');\n  \n  // Modals\n  showCategoryModal = signal(false);\n  showSubCategoryModal = signal(false);\n  editingCategory = signal<CategorieAdmin | null>(null);\n  editingSousCategory = signal<SousCategorieAdmin | null>(null);\n\n  // Forms\n  categoryForm: FormGroup;\n  subCategoryForm: FormGroup;\n\n  constructor(\n    private adminService: AdminService,\n    private categorieService: CategorieService,\n    private sousCategorieService: SousCategorieService,\n    private fb: FormBuilder\n  ) {\n    this.categoryForm = this.fb.group({\n      nom: ['', [Validators.required, Validators.minLength(2)]],\n      description: ['']\n    });\n\n    this.subCategoryForm = this.fb.group({\n      nom: ['', [Validators.required, Validators.minLength(2)]],\n      description: [''],\n      categorieId: ['', [Validators.required]]\n    });\n  }\n\n  ngOnInit(): void {\n    this.loadCategories();\n    this.loadSousCategories();\n  }\n\n  loadCategories(): void {\n    this.isLoading.set(true);\n    this.error.set('');\n\n    this.categorieService.getAll().subscribe({\n      next: (categories) => {\n        console.log('✅ Catégories reçues:', categories);\n\n        // Convertir les catégories en CategorieAdmin avec comptage des produits\n        const categoriesAdmin: CategorieAdmin[] = [];\n        let processedCount = 0;\n\n        if (categories.length === 0) {\n          this.categories.set([]);\n          this.isLoading.set(false);\n          return;\n        }\n\n        categories.forEach(cat => {\n          // Récupérer le nombre de produits pour chaque catégorie\n          this.categorieService.getProduitsCount(cat.id).subscribe({\n            next: (nombreProduits) => {\n              categoriesAdmin.push({\n                id: cat.id,\n                nom: cat.nom,\n                description: cat.description,\n                estValide: cat.estValidee,\n                dateCreation: new Date().toISOString(),\n                nombreProduits: nombreProduits,\n                sousCategories: []\n              });\n\n              processedCount++;\n              if (processedCount === categories.length) {\n                // Trier par nom\n                categoriesAdmin.sort((a, b) => a.nom.localeCompare(b.nom));\n                this.categories.set(categoriesAdmin);\n                this.isLoading.set(false);\n              }\n            },\n            error: (error) => {\n              console.warn(`Erreur comptage produits pour catégorie ${cat.id}:`, error);\n              categoriesAdmin.push({\n                id: cat.id,\n                nom: cat.nom,\n                description: cat.description,\n                estValide: cat.estValidee,\n                dateCreation: new Date().toISOString(),\n                nombreProduits: 0,\n                sousCategories: []\n              });\n\n              processedCount++;\n              if (processedCount === categories.length) {\n                categoriesAdmin.sort((a, b) => a.nom.localeCompare(b.nom));\n                this.categories.set(categoriesAdmin);\n                this.isLoading.set(false);\n              }\n            }\n          });\n        });\n      },\n      error: (error) => {\n        console.error('❌ Erreur lors du chargement des catégories:', error);\n        this.error.set('Erreur lors du chargement des catégories');\n        this.isLoading.set(false);\n      }\n    });\n  }\n\n  loadSousCategories(): void {\n    this.sousCategorieService.getAll().subscribe({\n      next: (sousCategories) => {\n        console.log('✅ Sous-catégories reçues:', sousCategories);\n\n        // Convertir les sous-catégories en SousCategorieAdmin avec comptage des produits\n        const sousCategoriesAdmin: SousCategorieAdmin[] = [];\n        let processedCount = 0;\n\n        if (sousCategories.length === 0) {\n          this.sousCategories.set([]);\n          return;\n        }\n\n        sousCategories.forEach(sousCat => {\n          // Récupérer le nombre de produits pour chaque sous-catégorie\n          this.sousCategorieService.getProduitsCount(sousCat.id).subscribe({\n            next: (nombreProduits) => {\n              sousCategoriesAdmin.push({\n                id: sousCat.id,\n                nom: sousCat.nom,\n                description: sousCat.description,\n                categorieId: sousCat.categorieId,\n                categorieNom: this.getCategoryName(sousCat.categorieId),\n                estValide: sousCat.estValidee,\n                dateCreation: new Date().toISOString(),\n                nombreProduits: nombreProduits\n              });\n\n              processedCount++;\n              if (processedCount === sousCategories.length) {\n                // Trier par nom\n                sousCategoriesAdmin.sort((a, b) => a.nom.localeCompare(b.nom));\n                this.sousCategories.set(sousCategoriesAdmin);\n              }\n            },\n            error: (error) => {\n              console.warn(`Erreur comptage produits pour sous-catégorie ${sousCat.id}:`, error);\n              sousCategoriesAdmin.push({\n                id: sousCat.id,\n                nom: sousCat.nom,\n                description: sousCat.description,\n                categorieId: sousCat.categorieId,\n                categorieNom: this.getCategoryName(sousCat.categorieId),\n                estValide: sousCat.estValidee,\n                dateCreation: new Date().toISOString(),\n                nombreProduits: 0\n              });\n\n              processedCount++;\n              if (processedCount === sousCategories.length) {\n                sousCategoriesAdmin.sort((a, b) => a.nom.localeCompare(b.nom));\n                this.sousCategories.set(sousCategoriesAdmin);\n              }\n            }\n          });\n        });\n      },\n      error: (error) => {\n        console.error('❌ Erreur lors du chargement des sous-catégories:', error);\n        this.error.set('Erreur lors du chargement des sous-catégories');\n      }\n    });\n  }\n\n  // ==================== CATÉGORIES ====================\n  \n  openCategoryModal(category?: CategorieAdmin): void {\n    this.editingCategory.set(category || null);\n    \n    if (category) {\n      this.categoryForm.patchValue({\n        nom: category.nom,\n        description: category.description || ''\n      });\n    } else {\n      this.categoryForm.reset();\n    }\n    \n    this.showCategoryModal.set(true);\n  }\n\n  closeCategoryModal(): void {\n    this.showCategoryModal.set(false);\n    this.editingCategory.set(null);\n    this.categoryForm.reset();\n  }\n\n  saveCategoryForm(): void {\n    if (this.categoryForm.valid) {\n      const formData = this.categoryForm.value;\n      const editingCat = this.editingCategory();\n\n      if (editingCat) {\n        // Modification\n        this.adminService.updateCategorie(editingCat.id, formData).subscribe({\n          next: () => {\n            this.loadCategories();\n            this.closeCategoryModal();\n            alert('Catégorie modifiée avec succès');\n          },\n          error: (error) => {\n            console.error('Erreur lors de la modification:', error);\n            alert('Erreur lors de la modification de la catégorie');\n          }\n        });\n      } else {\n        // Création\n        this.adminService.createCategorie(formData).subscribe({\n          next: () => {\n            this.loadCategories();\n            this.closeCategoryModal();\n            alert('Catégorie créée avec succès');\n          },\n          error: (error) => {\n            console.error('Erreur lors de la création:', error);\n            alert('Erreur lors de la création de la catégorie');\n          }\n        });\n      }\n    }\n  }\n\n  deleteCategory(category: CategorieAdmin): void {\n    if (confirm(`Êtes-vous sûr de vouloir supprimer la catégorie \"${category.nom}\" ?\\n\\nCette action supprimera aussi toutes les sous-catégories associées.`)) {\n      this.adminService.deleteCategorie(category.id).subscribe({\n        next: () => {\n          this.loadCategories();\n          this.loadSousCategories();\n          alert('Catégorie supprimée avec succès');\n        },\n        error: (error) => {\n          console.error('Erreur lors de la suppression:', error);\n          alert('Erreur lors de la suppression de la catégorie');\n        }\n      });\n    }\n  }\n\n  validateCategory(category: CategorieAdmin): void {\n    this.adminService.validerCategorie(category.id).subscribe({\n      next: () => {\n        // Mettre à jour localement\n        const cats = this.categories();\n        const index = cats.findIndex(c => c.id === category.id);\n        if (index !== -1) {\n          cats[index] = { ...cats[index], estValide: true };\n          this.categories.set([...cats]);\n        }\n        alert('Catégorie validée avec succès');\n      },\n      error: (error) => {\n        console.error('Erreur lors de la validation:', error);\n        let errorMessage = 'Erreur lors de la validation de la catégorie';\n\n        if (error.status === 404) {\n          errorMessage = 'Endpoint de validation non trouvé. Vérifiez que le backend est démarré.';\n        } else if (error.status === 0) {\n          errorMessage = 'Impossible de contacter le serveur. Vérifiez que le backend est démarré sur https://localhost:7264';\n        } else if (error.error?.message) {\n          errorMessage = `Erreur: ${error.error.message}`;\n        }\n\n        alert(errorMessage);\n      }\n    });\n  }\n\n  rejectCategory(category: CategorieAdmin): void {\n    const motif = prompt('Motif du refus:');\n    if (motif) {\n      this.adminService.refuserCategorie(category.id, motif).subscribe({\n        next: () => {\n          // Mettre à jour localement\n          const cats = this.categories();\n          const index = cats.findIndex(c => c.id === category.id);\n          if (index !== -1) {\n            cats[index] = { ...cats[index], estValide: false };\n            this.categories.set([...cats]);\n          }\n          alert('Catégorie refusée avec succès');\n        },\n        error: (error) => {\n          console.error('Erreur lors du refus:', error);\n          alert('Erreur lors du refus de la catégorie');\n        }\n      });\n    }\n  }\n\n  // ==================== SOUS-CATÉGORIES ====================\n  \n  openSubCategoryModal(sousCategory?: SousCategorieAdmin): void {\n    this.editingSousCategory.set(sousCategory || null);\n    \n    if (sousCategory) {\n      this.subCategoryForm.patchValue({\n        nom: sousCategory.nom,\n        description: sousCategory.description || '',\n        categorieId: sousCategory.categorieId\n      });\n    } else {\n      this.subCategoryForm.reset();\n    }\n    \n    this.showSubCategoryModal.set(true);\n  }\n\n  closeSubCategoryModal(): void {\n    this.showSubCategoryModal.set(false);\n    this.editingSousCategory.set(null);\n    this.subCategoryForm.reset();\n  }\n\n  saveSubCategoryForm(): void {\n    if (this.subCategoryForm.valid) {\n      const formData = this.subCategoryForm.value;\n      const editingSousCat = this.editingSousCategory();\n\n      if (editingSousCat) {\n        // Modification\n        this.adminService.updateSousCategorie(editingSousCat.id, formData).subscribe({\n          next: () => {\n            this.loadSousCategories();\n            this.closeSubCategoryModal();\n            alert('Sous-catégorie modifiée avec succès');\n          },\n          error: (error) => {\n            console.error('Erreur lors de la modification:', error);\n            alert('Erreur lors de la modification de la sous-catégorie');\n          }\n        });\n      } else {\n        // Création\n        this.adminService.createSousCategorie(formData).subscribe({\n          next: () => {\n            this.loadSousCategories();\n            this.closeSubCategoryModal();\n            alert('Sous-catégorie créée avec succès');\n          },\n          error: (error) => {\n            console.error('Erreur lors de la création:', error);\n            alert('Erreur lors de la création de la sous-catégorie');\n          }\n        });\n      }\n    }\n  }\n\n  deleteSubCategory(sousCategory: SousCategorieAdmin): void {\n    if (confirm(`Êtes-vous sûr de vouloir supprimer la sous-catégorie \"${sousCategory.nom}\" ?`)) {\n      this.adminService.deleteSousCategorie(sousCategory.id).subscribe({\n        next: () => {\n          this.loadSousCategories();\n          alert('Sous-catégorie supprimée avec succès');\n        },\n        error: (error) => {\n          console.error('Erreur lors de la suppression:', error);\n          alert('Erreur lors de la suppression de la sous-catégorie');\n        }\n      });\n    }\n  }\n\n  validateSubCategory(sousCategory: SousCategorieAdmin): void {\n    this.adminService.validerSousCategorie(sousCategory.id).subscribe({\n      next: () => {\n        // Mettre à jour localement\n        const sousCats = this.sousCategories();\n        const index = sousCats.findIndex(sc => sc.id === sousCategory.id);\n        if (index !== -1) {\n          sousCats[index] = { ...sousCats[index], estValide: true };\n          this.sousCategories.set([...sousCats]);\n        }\n        alert('Sous-catégorie validée avec succès');\n      },\n      error: (error) => {\n        console.error('Erreur lors de la validation:', error);\n        alert('Erreur lors de la validation de la sous-catégorie');\n      }\n    });\n  }\n\n  rejectSubCategory(sousCategory: SousCategorieAdmin): void {\n    const motif = prompt('Motif du refus:');\n    if (motif) {\n      this.adminService.refuserSousCategorie(sousCategory.id, motif).subscribe({\n        next: () => {\n          // Mettre à jour localement\n          const sousCats = this.sousCategories();\n          const index = sousCats.findIndex(sc => sc.id === sousCategory.id);\n          if (index !== -1) {\n            sousCats[index] = { ...sousCats[index], estValide: false };\n            this.sousCategories.set([...sousCats]);\n          }\n          alert('Sous-catégorie refusée avec succès');\n        },\n        error: (error) => {\n          console.error('Erreur lors du refus:', error);\n          alert('Erreur lors du refus de la sous-catégorie');\n        }\n      });\n    }\n  }\n\n  // ==================== UTILITAIRES ====================\n  \n  setActiveTab(tab: 'categories' | 'souscategories'): void {\n    this.activeTab.set(tab);\n  }\n\n  getStatusClass(estValide: boolean): string {\n    return estValide ? 'status-validated' : 'status-pending';\n  }\n\n  getStatusText(estValide: boolean): string {\n    return estValide ? 'Validée' : 'En attente';\n  }\n\n  formatDate(date: string): string {\n    return new Date(date).toLocaleDateString('fr-FR', {\n      year: 'numeric',\n      month: 'short',\n      day: 'numeric'\n    });\n  }\n\n  getCategoryName(categorieId: number): string {\n    const category = this.categories().find(c => c.id === categorieId);\n    return category ? category.nom : 'Catégorie inconnue';\n  }\n\n  refresh(): void {\n    this.loadCategories();\n    this.loadSousCategories();\n  }\n}\n", "<div class=\"category-management-container\">\n  <!-- Header -->\n  <div class=\"page-header\">\n    <div class=\"header-content\">\n      <h1 class=\"page-title\">\n        <i class=\"icon-folder\"></i>\n        Gestion des Catégories\n      </h1>\n      <div class=\"header-actions\">\n        <button class=\"btn btn-secondary\" (click)=\"refresh()\" [disabled]=\"isLoading()\">\n          <i class=\"icon-refresh\"></i>\n          Actualiser\n        </button>\n      </div>\n    </div>\n  </div>\n\n  <!-- Tabs -->\n  <div class=\"tabs-container\">\n    <div class=\"tabs\">\n      <button \n        class=\"tab-button\"\n        [class.active]=\"activeTab() === 'categories'\"\n        (click)=\"setActiveTab('categories')\">\n        <i class=\"icon-folder\"></i>\n        Catégories ({{ categories().length }})\n      </button>\n      <button \n        class=\"tab-button\"\n        [class.active]=\"activeTab() === 'souscategories'\"\n        (click)=\"setActiveTab('souscategories')\">\n        <i class=\"icon-folder-open\"></i>\n        Sous-catégories ({{ sousCategories().length }})\n      </button>\n    </div>\n  </div>\n\n  <!-- Message d'erreur -->\n  <div *ngIf=\"error()\" class=\"error-message\">\n    <i class=\"icon-alert\"></i>\n    {{ error() }}\n  </div>\n\n  <!-- Loading -->\n  <div *ngIf=\"isLoading()\" class=\"loading-container\">\n    <div class=\"loading-spinner\"></div>\n    <p>Chargement...</p>\n  </div>\n\n  <!-- Onglet Catégories -->\n  <div *ngIf=\"activeTab() === 'categories' && !isLoading()\" class=\"tab-content\">\n    <div class=\"section-header\">\n      <h2>Catégories</h2>\n      <button class=\"btn btn-primary\" (click)=\"openCategoryModal()\">\n        <i class=\"icon-plus\"></i>\n        Nouvelle catégorie\n      </button>\n    </div>\n\n    <div class=\"table-container\">\n      <table class=\"data-table\">\n        <thead>\n          <tr>\n            <th>Nom</th>\n            <th>Description</th>\n            <th>Statut</th>\n            <th>Produits</th>\n            <th>Date création</th>\n            <th>Actions</th>\n          </tr>\n        </thead>\n        <tbody>\n          <tr *ngFor=\"let category of categories()\" class=\"data-row\">\n            <td class=\"name-cell\">\n              <div class=\"item-name\">{{ category.nom }}</div>\n              <div class=\"item-id\">ID: {{ category.id }}</div>\n            </td>\n            \n            <td class=\"description-cell\">\n              <span *ngIf=\"category.description; else noDescription\">\n                {{ category.description }}\n              </span>\n              <ng-template #noDescription>\n                <span class=\"no-description\">Aucune description</span>\n              </ng-template>\n            </td>\n            \n            <td>\n              <span class=\"status-badge\" [ngClass]=\"getStatusClass(category.estValide)\">\n                {{ getStatusText(category.estValide) }}\n              </span>\n            </td>\n            \n            <td class=\"count-cell\">{{ category.nombreProduits }}</td>\n            \n            <td class=\"date-cell\">{{ formatDate(category.dateCreation) }}</td>\n            \n            <td class=\"actions-cell\">\n              <div class=\"action-buttons\">\n                <button \n                  class=\"btn-action btn-edit\"\n                  (click)=\"openCategoryModal(category)\"\n                  title=\"Modifier\">\n                  <i class=\"icon-edit\"></i>\n                </button>\n                \n                <button \n                  *ngIf=\"!category.estValide\"\n                  class=\"btn-action btn-validate\"\n                  (click)=\"validateCategory(category)\"\n                  title=\"Valider\">\n                  <i class=\"icon-check\"></i>\n                </button>\n                \n                <button \n                  *ngIf=\"category.estValide\"\n                  class=\"btn-action btn-reject\"\n                  (click)=\"rejectCategory(category)\"\n                  title=\"Refuser\">\n                  <i class=\"icon-x\"></i>\n                </button>\n                \n                <button \n                  class=\"btn-action btn-delete\"\n                  (click)=\"deleteCategory(category)\"\n                  title=\"Supprimer\">\n                  <i class=\"icon-trash\"></i>\n                </button>\n              </div>\n            </td>\n          </tr>\n        </tbody>\n      </table>\n\n      <div *ngIf=\"categories().length === 0\" class=\"no-data\">\n        <i class=\"icon-folder\"></i>\n        <h3>Aucune catégorie</h3>\n        <p>Aucune catégorie n'a été créée pour le moment.</p>\n      </div>\n    </div>\n  </div>\n\n  <!-- Onglet Sous-catégories -->\n  <div *ngIf=\"activeTab() === 'souscategories' && !isLoading()\" class=\"tab-content\">\n    <div class=\"section-header\">\n      <h2>Sous-catégories</h2>\n      <button class=\"btn btn-primary\" (click)=\"openSubCategoryModal()\">\n        <i class=\"icon-plus\"></i>\n        Nouvelle sous-catégorie\n      </button>\n    </div>\n\n    <div class=\"table-container\">\n      <table class=\"data-table\">\n        <thead>\n          <tr>\n            <th>Nom</th>\n            <th>Catégorie parent</th>\n            <th>Description</th>\n            <th>Statut</th>\n            <th>Produits</th>\n            <th>Date création</th>\n            <th>Actions</th>\n          </tr>\n        </thead>\n        <tbody>\n          <tr *ngFor=\"let sousCategory of sousCategories()\" class=\"data-row\">\n            <td class=\"name-cell\">\n              <div class=\"item-name\">{{ sousCategory.nom }}</div>\n              <div class=\"item-id\">ID: {{ sousCategory.id }}</div>\n            </td>\n            \n            <td class=\"parent-cell\">{{ getCategoryName(sousCategory.categorieId) }}</td>\n            \n            <td class=\"description-cell\">\n              <span *ngIf=\"sousCategory.description; else noDescription\">\n                {{ sousCategory.description }}\n              </span>\n              <ng-template #noDescription>\n                <span class=\"no-description\">Aucune description</span>\n              </ng-template>\n            </td>\n            \n            <td>\n              <span class=\"status-badge\" [ngClass]=\"getStatusClass(sousCategory.estValide)\">\n                {{ getStatusText(sousCategory.estValide) }}\n              </span>\n            </td>\n            \n            <td class=\"count-cell\">{{ sousCategory.nombreProduits }}</td>\n            \n            <td class=\"date-cell\">{{ formatDate(sousCategory.dateCreation) }}</td>\n            \n            <td class=\"actions-cell\">\n              <div class=\"action-buttons\">\n                <button \n                  class=\"btn-action btn-edit\"\n                  (click)=\"openSubCategoryModal(sousCategory)\"\n                  title=\"Modifier\">\n                  <i class=\"icon-edit\"></i>\n                </button>\n                \n                <button \n                  *ngIf=\"!sousCategory.estValide\"\n                  class=\"btn-action btn-validate\"\n                  (click)=\"validateSubCategory(sousCategory)\"\n                  title=\"Valider\">\n                  <i class=\"icon-check\"></i>\n                </button>\n                \n                <button \n                  *ngIf=\"sousCategory.estValide\"\n                  class=\"btn-action btn-reject\"\n                  (click)=\"rejectSubCategory(sousCategory)\"\n                  title=\"Refuser\">\n                  <i class=\"icon-x\"></i>\n                </button>\n                \n                <button \n                  class=\"btn-action btn-delete\"\n                  (click)=\"deleteSubCategory(sousCategory)\"\n                  title=\"Supprimer\">\n                  <i class=\"icon-trash\"></i>\n                </button>\n              </div>\n            </td>\n          </tr>\n        </tbody>\n      </table>\n\n      <div *ngIf=\"sousCategories().length === 0\" class=\"no-data\">\n        <i class=\"icon-folder-open\"></i>\n        <h3>Aucune sous-catégorie</h3>\n        <p>Aucune sous-catégorie n'a été créée pour le moment.</p>\n      </div>\n    </div>\n  </div>\n</div>\n\n<!-- Modal Catégorie -->\n<div *ngIf=\"showCategoryModal()\" class=\"modal-overlay\" (click)=\"closeCategoryModal()\">\n  <div class=\"modal-content\" (click)=\"$event.stopPropagation()\">\n    <div class=\"modal-header\">\n      <h3>{{ editingCategory() ? 'Modifier la catégorie' : 'Nouvelle catégorie' }}</h3>\n      <button class=\"btn-close\" (click)=\"closeCategoryModal()\">\n        <i class=\"icon-x\"></i>\n      </button>\n    </div>\n    \n    <form [formGroup]=\"categoryForm\" (ngSubmit)=\"saveCategoryForm()\" class=\"modal-form\">\n      <div class=\"form-group\">\n        <label for=\"categoryName\">Nom *</label>\n        <input \n          id=\"categoryName\"\n          type=\"text\" \n          formControlName=\"nom\"\n          class=\"form-input\"\n          placeholder=\"Nom de la catégorie\">\n        <div *ngIf=\"categoryForm.get('nom')?.invalid && categoryForm.get('nom')?.touched\" class=\"error-text\">\n          Le nom est requis (minimum 2 caractères)\n        </div>\n      </div>\n      \n      <div class=\"form-group\">\n        <label for=\"categoryDescription\">Description</label>\n        <textarea \n          id=\"categoryDescription\"\n          formControlName=\"description\"\n          class=\"form-textarea\"\n          placeholder=\"Description de la catégorie\"\n          rows=\"3\"></textarea>\n      </div>\n      \n      <div class=\"modal-actions\">\n        <button type=\"button\" class=\"btn btn-secondary\" (click)=\"closeCategoryModal()\">\n          Annuler\n        </button>\n        <button type=\"submit\" class=\"btn btn-primary\" [disabled]=\"categoryForm.invalid\">\n          {{ editingCategory() ? 'Modifier' : 'Créer' }}\n        </button>\n      </div>\n    </form>\n  </div>\n</div>\n\n<!-- Modal Sous-catégorie -->\n<div *ngIf=\"showSubCategoryModal()\" class=\"modal-overlay\" (click)=\"closeSubCategoryModal()\">\n  <div class=\"modal-content\" (click)=\"$event.stopPropagation()\">\n    <div class=\"modal-header\">\n      <h3>{{ editingSousCategory() ? 'Modifier la sous-catégorie' : 'Nouvelle sous-catégorie' }}</h3>\n      <button class=\"btn-close\" (click)=\"closeSubCategoryModal()\">\n        <i class=\"icon-x\"></i>\n      </button>\n    </div>\n    \n    <form [formGroup]=\"subCategoryForm\" (ngSubmit)=\"saveSubCategoryForm()\" class=\"modal-form\">\n      <div class=\"form-group\">\n        <label for=\"subCategoryName\">Nom *</label>\n        <input \n          id=\"subCategoryName\"\n          type=\"text\" \n          formControlName=\"nom\"\n          class=\"form-input\"\n          placeholder=\"Nom de la sous-catégorie\">\n        <div *ngIf=\"subCategoryForm.get('nom')?.invalid && subCategoryForm.get('nom')?.touched\" class=\"error-text\">\n          Le nom est requis (minimum 2 caractères)\n        </div>\n      </div>\n      \n      <div class=\"form-group\">\n        <label for=\"parentCategory\">Catégorie parent *</label>\n        <select \n          id=\"parentCategory\"\n          formControlName=\"categorieId\"\n          class=\"form-select\">\n          <option value=\"\">Sélectionner une catégorie</option>\n          <option *ngFor=\"let category of categories()\" [value]=\"category.id\">\n            {{ category.nom }}\n          </option>\n        </select>\n        <div *ngIf=\"subCategoryForm.get('categorieId')?.invalid && subCategoryForm.get('categorieId')?.touched\" class=\"error-text\">\n          La catégorie parent est requise\n        </div>\n      </div>\n      \n      <div class=\"form-group\">\n        <label for=\"subCategoryDescription\">Description</label>\n        <textarea \n          id=\"subCategoryDescription\"\n          formControlName=\"description\"\n          class=\"form-textarea\"\n          placeholder=\"Description de la sous-catégorie\"\n          rows=\"3\"></textarea>\n      </div>\n      \n      <div class=\"modal-actions\">\n        <button type=\"button\" class=\"btn btn-secondary\" (click)=\"closeSubCategoryModal()\">\n          Annuler\n        </button>\n        <button type=\"submit\" class=\"btn btn-primary\" [disabled]=\"subCategoryForm.invalid\">\n          {{ editingSousCategory() ? 'Modifier' : 'Créer' }}\n        </button>\n      </div>\n    </form>\n  </div>\n</div>\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACsCE,IAAA,yBAAA,GAAA,OAAA,EAAA;AACE,IAAA,oBAAA,GAAA,KAAA,EAAA;AACA,IAAA,iBAAA,CAAA;AACF,IAAA,uBAAA;;;;AADE,IAAA,oBAAA,CAAA;AAAA,IAAA,6BAAA,KAAA,OAAA,MAAA,GAAA,GAAA;;;;;AAIF,IAAA,yBAAA,GAAA,OAAA,EAAA;AACE,IAAA,oBAAA,GAAA,OAAA,EAAA;AACA,IAAA,yBAAA,GAAA,GAAA;AAAG,IAAA,iBAAA,GAAA,eAAA;AAAa,IAAA,uBAAA,EAAI;;;;;AAiCV,IAAA,yBAAA,GAAA,MAAA;AACE,IAAA,iBAAA,CAAA;AACF,IAAA,uBAAA;;;;AADE,IAAA,oBAAA;AAAA,IAAA,6BAAA,KAAA,YAAA,aAAA,GAAA;;;;;AAGA,IAAA,yBAAA,GAAA,QAAA,EAAA;AAA6B,IAAA,iBAAA,GAAA,oBAAA;AAAkB,IAAA,uBAAA;;;;;;AAuB/C,IAAA,yBAAA,GAAA,UAAA,EAAA;AAGE,IAAA,qBAAA,SAAA,SAAA,sFAAA;AAAA,MAAA,wBAAA,GAAA;AAAA,YAAA,cAAA,wBAAA,EAAA;AAAA,YAAA,SAAA,wBAAA,CAAA;AAAA,aAAA,sBAAS,OAAA,iBAAA,WAAA,CAA0B;IAAA,CAAA;AAEnC,IAAA,oBAAA,GAAA,KAAA,EAAA;AACF,IAAA,uBAAA;;;;;;AAEA,IAAA,yBAAA,GAAA,UAAA,EAAA;AAGE,IAAA,qBAAA,SAAA,SAAA,sFAAA;AAAA,MAAA,wBAAA,GAAA;AAAA,YAAA,cAAA,wBAAA,EAAA;AAAA,YAAA,SAAA,wBAAA,CAAA;AAAA,aAAA,sBAAS,OAAA,eAAA,WAAA,CAAwB;IAAA,CAAA;AAEjC,IAAA,oBAAA,GAAA,KAAA,EAAA;AACF,IAAA,uBAAA;;;;;;AAhDN,IAAA,yBAAA,GAAA,MAAA,EAAA,EAA2D,GAAA,MAAA,EAAA,EACnC,GAAA,OAAA,EAAA;AACG,IAAA,iBAAA,CAAA;AAAkB,IAAA,uBAAA;AACzC,IAAA,yBAAA,GAAA,OAAA,EAAA;AAAqB,IAAA,iBAAA,CAAA;AAAqB,IAAA,uBAAA,EAAM;AAGlD,IAAA,yBAAA,GAAA,MAAA,EAAA;AACE,IAAA,qBAAA,GAAA,0DAAA,GAAA,GAAA,QAAA,EAAA,EAAuD,GAAA,iEAAA,GAAA,GAAA,eAAA,MAAA,GAAA,gCAAA;AAMzD,IAAA,uBAAA;AAEA,IAAA,yBAAA,IAAA,IAAA,EAAI,IAAA,QAAA,EAAA;AAEA,IAAA,iBAAA,EAAA;AACF,IAAA,uBAAA,EAAO;AAGT,IAAA,yBAAA,IAAA,MAAA,EAAA;AAAuB,IAAA,iBAAA,EAAA;AAA6B,IAAA,uBAAA;AAEpD,IAAA,yBAAA,IAAA,MAAA,EAAA;AAAsB,IAAA,iBAAA,EAAA;AAAuC,IAAA,uBAAA;AAE7D,IAAA,yBAAA,IAAA,MAAA,EAAA,EAAyB,IAAA,OAAA,EAAA,EACK,IAAA,UAAA,EAAA;AAGxB,IAAA,qBAAA,SAAA,SAAA,6EAAA;AAAA,YAAA,cAAA,wBAAA,GAAA,EAAA;AAAA,YAAA,SAAA,wBAAA,CAAA;AAAA,aAAA,sBAAS,OAAA,kBAAA,WAAA,CAA2B;IAAA,CAAA;AAEpC,IAAA,oBAAA,IAAA,KAAA,EAAA;AACF,IAAA,uBAAA;AAEA,IAAA,qBAAA,IAAA,6DAAA,GAAA,GAAA,UAAA,EAAA,EAIkB,IAAA,6DAAA,GAAA,GAAA,UAAA,EAAA;AAYlB,IAAA,yBAAA,IAAA,UAAA,EAAA;AAEE,IAAA,qBAAA,SAAA,SAAA,6EAAA;AAAA,YAAA,cAAA,wBAAA,GAAA,EAAA;AAAA,YAAA,SAAA,wBAAA,CAAA;AAAA,aAAA,sBAAS,OAAA,eAAA,WAAA,CAAwB;IAAA,CAAA;AAEjC,IAAA,oBAAA,IAAA,KAAA,EAAA;AACF,IAAA,uBAAA,EAAS,EACL,EACH;;;;;;AAvDoB,IAAA,oBAAA,CAAA;AAAA,IAAA,4BAAA,YAAA,GAAA;AACF,IAAA,oBAAA,CAAA;AAAA,IAAA,6BAAA,QAAA,YAAA,IAAA,EAAA;AAId,IAAA,oBAAA,CAAA;AAAA,IAAA,qBAAA,QAAA,YAAA,WAAA,EAA4B,YAAA,gBAAA;AASR,IAAA,oBAAA,CAAA;AAAA,IAAA,qBAAA,WAAA,OAAA,eAAA,YAAA,SAAA,CAAA;AACzB,IAAA,oBAAA;AAAA,IAAA,6BAAA,KAAA,OAAA,cAAA,YAAA,SAAA,GAAA,GAAA;AAImB,IAAA,oBAAA,CAAA;AAAA,IAAA,4BAAA,YAAA,cAAA;AAED,IAAA,oBAAA,CAAA;AAAA,IAAA,4BAAA,OAAA,WAAA,YAAA,YAAA,CAAA;AAYf,IAAA,oBAAA,CAAA;AAAA,IAAA,qBAAA,QAAA,CAAA,YAAA,SAAA;AAQA,IAAA,oBAAA;AAAA,IAAA,qBAAA,QAAA,YAAA,SAAA;;;;;AAmBb,IAAA,yBAAA,GAAA,OAAA,EAAA;AACE,IAAA,oBAAA,GAAA,KAAA,CAAA;AACA,IAAA,yBAAA,GAAA,IAAA;AAAI,IAAA,iBAAA,GAAA,qBAAA;AAAgB,IAAA,uBAAA;AACpB,IAAA,yBAAA,GAAA,GAAA;AAAG,IAAA,iBAAA,GAAA,+DAAA;AAA8C,IAAA,uBAAA,EAAI;;;;;;AAvF3D,IAAA,yBAAA,GAAA,OAAA,EAAA,EAA8E,GAAA,OAAA,EAAA,EAChD,GAAA,IAAA;AACtB,IAAA,iBAAA,GAAA,eAAA;AAAU,IAAA,uBAAA;AACd,IAAA,yBAAA,GAAA,UAAA,EAAA;AAAgC,IAAA,qBAAA,SAAA,SAAA,sEAAA;AAAA,MAAA,wBAAA,GAAA;AAAA,YAAA,SAAA,wBAAA;AAAA,aAAA,sBAAS,OAAA,kBAAA,CAAmB;IAAA,CAAA;AAC1D,IAAA,oBAAA,GAAA,KAAA,EAAA;AACA,IAAA,iBAAA,GAAA,yBAAA;AACF,IAAA,uBAAA,EAAS;AAGX,IAAA,yBAAA,GAAA,OAAA,EAAA,EAA6B,GAAA,SAAA,EAAA,EACD,GAAA,OAAA,EACjB,IAAA,IAAA,EACD,IAAA,IAAA;AACE,IAAA,iBAAA,IAAA,KAAA;AAAG,IAAA,uBAAA;AACP,IAAA,yBAAA,IAAA,IAAA;AAAI,IAAA,iBAAA,IAAA,aAAA;AAAW,IAAA,uBAAA;AACf,IAAA,yBAAA,IAAA,IAAA;AAAI,IAAA,iBAAA,IAAA,QAAA;AAAM,IAAA,uBAAA;AACV,IAAA,yBAAA,IAAA,IAAA;AAAI,IAAA,iBAAA,IAAA,UAAA;AAAQ,IAAA,uBAAA;AACZ,IAAA,yBAAA,IAAA,IAAA;AAAI,IAAA,iBAAA,IAAA,kBAAA;AAAa,IAAA,uBAAA;AACjB,IAAA,yBAAA,IAAA,IAAA;AAAI,IAAA,iBAAA,IAAA,SAAA;AAAO,IAAA,uBAAA,EAAK,EACb;AAEP,IAAA,yBAAA,IAAA,OAAA;AACE,IAAA,qBAAA,IAAA,mDAAA,IAAA,IAAA,MAAA,EAAA;AA2DF,IAAA,uBAAA,EAAQ;AAGV,IAAA,qBAAA,IAAA,oDAAA,GAAA,GAAA,OAAA,EAAA;AAKF,IAAA,uBAAA,EAAM;;;;AAnEyB,IAAA,oBAAA,EAAA;AAAA,IAAA,qBAAA,WAAA,OAAA,WAAA,CAAA;AA8DvB,IAAA,oBAAA;AAAA,IAAA,qBAAA,QAAA,OAAA,WAAA,EAAA,WAAA,CAAA;;;;;AAyCE,IAAA,yBAAA,GAAA,MAAA;AACE,IAAA,iBAAA,CAAA;AACF,IAAA,uBAAA;;;;AADE,IAAA,oBAAA;AAAA,IAAA,6BAAA,KAAA,iBAAA,aAAA,GAAA;;;;;AAGA,IAAA,yBAAA,GAAA,QAAA,EAAA;AAA6B,IAAA,iBAAA,GAAA,oBAAA;AAAkB,IAAA,uBAAA;;;;;;AAuB/C,IAAA,yBAAA,GAAA,UAAA,EAAA;AAGE,IAAA,qBAAA,SAAA,SAAA,sFAAA;AAAA,MAAA,wBAAA,IAAA;AAAA,YAAA,mBAAA,wBAAA,EAAA;AAAA,YAAA,SAAA,wBAAA,CAAA;AAAA,aAAA,sBAAS,OAAA,oBAAA,gBAAA,CAAiC;IAAA,CAAA;AAE1C,IAAA,oBAAA,GAAA,KAAA,EAAA;AACF,IAAA,uBAAA;;;;;;AAEA,IAAA,yBAAA,GAAA,UAAA,EAAA;AAGE,IAAA,qBAAA,SAAA,SAAA,sFAAA;AAAA,MAAA,wBAAA,IAAA;AAAA,YAAA,mBAAA,wBAAA,EAAA;AAAA,YAAA,SAAA,wBAAA,CAAA;AAAA,aAAA,sBAAS,OAAA,kBAAA,gBAAA,CAA+B;IAAA,CAAA;AAExC,IAAA,oBAAA,GAAA,KAAA,EAAA;AACF,IAAA,uBAAA;;;;;;AAlDN,IAAA,yBAAA,GAAA,MAAA,EAAA,EAAmE,GAAA,MAAA,EAAA,EAC3C,GAAA,OAAA,EAAA;AACG,IAAA,iBAAA,CAAA;AAAsB,IAAA,uBAAA;AAC7C,IAAA,yBAAA,GAAA,OAAA,EAAA;AAAqB,IAAA,iBAAA,CAAA;AAAyB,IAAA,uBAAA,EAAM;AAGtD,IAAA,yBAAA,GAAA,MAAA,EAAA;AAAwB,IAAA,iBAAA,CAAA;AAA+C,IAAA,uBAAA;AAEvE,IAAA,yBAAA,GAAA,MAAA,EAAA;AACE,IAAA,qBAAA,GAAA,0DAAA,GAAA,GAAA,QAAA,EAAA,EAA2D,IAAA,kEAAA,GAAA,GAAA,eAAA,MAAA,GAAA,gCAAA;AAM7D,IAAA,uBAAA;AAEA,IAAA,yBAAA,IAAA,IAAA,EAAI,IAAA,QAAA,EAAA;AAEA,IAAA,iBAAA,EAAA;AACF,IAAA,uBAAA,EAAO;AAGT,IAAA,yBAAA,IAAA,MAAA,EAAA;AAAuB,IAAA,iBAAA,EAAA;AAAiC,IAAA,uBAAA;AAExD,IAAA,yBAAA,IAAA,MAAA,EAAA;AAAsB,IAAA,iBAAA,EAAA;AAA2C,IAAA,uBAAA;AAEjE,IAAA,yBAAA,IAAA,MAAA,EAAA,EAAyB,IAAA,OAAA,EAAA,EACK,IAAA,UAAA,EAAA;AAGxB,IAAA,qBAAA,SAAA,SAAA,6EAAA;AAAA,YAAA,mBAAA,wBAAA,GAAA,EAAA;AAAA,YAAA,SAAA,wBAAA,CAAA;AAAA,aAAA,sBAAS,OAAA,qBAAA,gBAAA,CAAkC;IAAA,CAAA;AAE3C,IAAA,oBAAA,IAAA,KAAA,EAAA;AACF,IAAA,uBAAA;AAEA,IAAA,qBAAA,IAAA,6DAAA,GAAA,GAAA,UAAA,EAAA,EAIkB,IAAA,6DAAA,GAAA,GAAA,UAAA,EAAA;AAYlB,IAAA,yBAAA,IAAA,UAAA,EAAA;AAEE,IAAA,qBAAA,SAAA,SAAA,6EAAA;AAAA,YAAA,mBAAA,wBAAA,GAAA,EAAA;AAAA,YAAA,SAAA,wBAAA,CAAA;AAAA,aAAA,sBAAS,OAAA,kBAAA,gBAAA,CAA+B;IAAA,CAAA;AAExC,IAAA,oBAAA,IAAA,KAAA,EAAA;AACF,IAAA,uBAAA,EAAS,EACL,EACH;;;;;;AAzDoB,IAAA,oBAAA,CAAA;AAAA,IAAA,4BAAA,iBAAA,GAAA;AACF,IAAA,oBAAA,CAAA;AAAA,IAAA,6BAAA,QAAA,iBAAA,IAAA,EAAA;AAGC,IAAA,oBAAA,CAAA;AAAA,IAAA,4BAAA,OAAA,gBAAA,iBAAA,WAAA,CAAA;AAGf,IAAA,oBAAA,CAAA;AAAA,IAAA,qBAAA,QAAA,iBAAA,WAAA,EAAgC,YAAA,iBAAA;AASZ,IAAA,oBAAA,CAAA;AAAA,IAAA,qBAAA,WAAA,OAAA,eAAA,iBAAA,SAAA,CAAA;AACzB,IAAA,oBAAA;AAAA,IAAA,6BAAA,KAAA,OAAA,cAAA,iBAAA,SAAA,GAAA,GAAA;AAImB,IAAA,oBAAA,CAAA;AAAA,IAAA,4BAAA,iBAAA,cAAA;AAED,IAAA,oBAAA,CAAA;AAAA,IAAA,4BAAA,OAAA,WAAA,iBAAA,YAAA,CAAA;AAYf,IAAA,oBAAA,CAAA;AAAA,IAAA,qBAAA,QAAA,CAAA,iBAAA,SAAA;AAQA,IAAA,oBAAA;AAAA,IAAA,qBAAA,QAAA,iBAAA,SAAA;;;;;AAmBb,IAAA,yBAAA,GAAA,OAAA,EAAA;AACE,IAAA,oBAAA,GAAA,KAAA,EAAA;AACA,IAAA,yBAAA,GAAA,IAAA;AAAI,IAAA,iBAAA,GAAA,0BAAA;AAAqB,IAAA,uBAAA;AACzB,IAAA,yBAAA,GAAA,GAAA;AAAG,IAAA,iBAAA,GAAA,oEAAA;AAAmD,IAAA,uBAAA,EAAI;;;;;;AA1FhE,IAAA,yBAAA,GAAA,OAAA,EAAA,EAAkF,GAAA,OAAA,EAAA,EACpD,GAAA,IAAA;AACtB,IAAA,iBAAA,GAAA,oBAAA;AAAe,IAAA,uBAAA;AACnB,IAAA,yBAAA,GAAA,UAAA,EAAA;AAAgC,IAAA,qBAAA,SAAA,SAAA,sEAAA;AAAA,MAAA,wBAAA,GAAA;AAAA,YAAA,SAAA,wBAAA;AAAA,aAAA,sBAAS,OAAA,qBAAA,CAAsB;IAAA,CAAA;AAC7D,IAAA,oBAAA,GAAA,KAAA,EAAA;AACA,IAAA,iBAAA,GAAA,8BAAA;AACF,IAAA,uBAAA,EAAS;AAGX,IAAA,yBAAA,GAAA,OAAA,EAAA,EAA6B,GAAA,SAAA,EAAA,EACD,GAAA,OAAA,EACjB,IAAA,IAAA,EACD,IAAA,IAAA;AACE,IAAA,iBAAA,IAAA,KAAA;AAAG,IAAA,uBAAA;AACP,IAAA,yBAAA,IAAA,IAAA;AAAI,IAAA,iBAAA,IAAA,qBAAA;AAAgB,IAAA,uBAAA;AACpB,IAAA,yBAAA,IAAA,IAAA;AAAI,IAAA,iBAAA,IAAA,aAAA;AAAW,IAAA,uBAAA;AACf,IAAA,yBAAA,IAAA,IAAA;AAAI,IAAA,iBAAA,IAAA,QAAA;AAAM,IAAA,uBAAA;AACV,IAAA,yBAAA,IAAA,IAAA;AAAI,IAAA,iBAAA,IAAA,UAAA;AAAQ,IAAA,uBAAA;AACZ,IAAA,yBAAA,IAAA,IAAA;AAAI,IAAA,iBAAA,IAAA,kBAAA;AAAa,IAAA,uBAAA;AACjB,IAAA,yBAAA,IAAA,IAAA;AAAI,IAAA,iBAAA,IAAA,SAAA;AAAO,IAAA,uBAAA,EAAK,EACb;AAEP,IAAA,yBAAA,IAAA,OAAA;AACE,IAAA,qBAAA,IAAA,mDAAA,IAAA,IAAA,MAAA,EAAA;AA6DF,IAAA,uBAAA,EAAQ;AAGV,IAAA,qBAAA,IAAA,oDAAA,GAAA,GAAA,OAAA,EAAA;AAKF,IAAA,uBAAA,EAAM;;;;AArE6B,IAAA,oBAAA,EAAA;AAAA,IAAA,qBAAA,WAAA,OAAA,eAAA,CAAA;AAgE3B,IAAA,oBAAA;AAAA,IAAA,qBAAA,QAAA,OAAA,eAAA,EAAA,WAAA,CAAA;;;;;AA4BJ,IAAA,yBAAA,GAAA,OAAA,EAAA;AACE,IAAA,iBAAA,GAAA,+CAAA;AACF,IAAA,uBAAA;;;;;;AApBR,IAAA,yBAAA,GAAA,OAAA,EAAA;AAAuD,IAAA,qBAAA,SAAA,SAAA,mEAAA;AAAA,MAAA,wBAAA,IAAA;AAAA,YAAA,SAAA,wBAAA;AAAA,aAAA,sBAAS,OAAA,mBAAA,CAAoB;IAAA,CAAA;AAClF,IAAA,yBAAA,GAAA,OAAA,EAAA;AAA2B,IAAA,qBAAA,SAAA,SAAA,iEAAA,QAAA;AAAA,MAAA,wBAAA,IAAA;AAAA,aAAA,sBAAS,OAAA,gBAAA,CAAwB;IAAA,CAAA;AAC1D,IAAA,yBAAA,GAAA,OAAA,EAAA,EAA0B,GAAA,IAAA;AACpB,IAAA,iBAAA,CAAA;AAAwE,IAAA,uBAAA;AAC5E,IAAA,yBAAA,GAAA,UAAA,EAAA;AAA0B,IAAA,qBAAA,SAAA,SAAA,sEAAA;AAAA,MAAA,wBAAA,IAAA;AAAA,YAAA,SAAA,wBAAA;AAAA,aAAA,sBAAS,OAAA,mBAAA,CAAoB;IAAA,CAAA;AACrD,IAAA,oBAAA,GAAA,KAAA,EAAA;AACF,IAAA,uBAAA,EAAS;AAGX,IAAA,yBAAA,GAAA,QAAA,EAAA;AAAiC,IAAA,qBAAA,YAAA,SAAA,uEAAA;AAAA,MAAA,wBAAA,IAAA;AAAA,YAAA,SAAA,wBAAA;AAAA,aAAA,sBAAY,OAAA,iBAAA,CAAkB;IAAA,CAAA;AAC7D,IAAA,yBAAA,GAAA,OAAA,EAAA,EAAwB,GAAA,SAAA,EAAA;AACI,IAAA,iBAAA,IAAA,OAAA;AAAK,IAAA,uBAAA;AAC/B,IAAA,oBAAA,IAAA,SAAA,EAAA;AAMA,IAAA,qBAAA,IAAA,oDAAA,GAAA,GAAA,OAAA,EAAA;AAGF,IAAA,uBAAA;AAEA,IAAA,yBAAA,IAAA,OAAA,EAAA,EAAwB,IAAA,SAAA,EAAA;AACW,IAAA,iBAAA,IAAA,aAAA;AAAW,IAAA,uBAAA;AAC5C,IAAA,oBAAA,IAAA,YAAA,EAAA;AAMF,IAAA,uBAAA;AAEA,IAAA,yBAAA,IAAA,OAAA,EAAA,EAA2B,IAAA,UAAA,EAAA;AACuB,IAAA,qBAAA,SAAA,SAAA,uEAAA;AAAA,MAAA,wBAAA,IAAA;AAAA,YAAA,SAAA,wBAAA;AAAA,aAAA,sBAAS,OAAA,mBAAA,CAAoB;IAAA,CAAA;AAC3E,IAAA,iBAAA,IAAA,WAAA;AACF,IAAA,uBAAA;AACA,IAAA,yBAAA,IAAA,UAAA,EAAA;AACE,IAAA,iBAAA,EAAA;AACF,IAAA,uBAAA,EAAS,EACL,EACD,EACH;;;;;AAvCE,IAAA,oBAAA,CAAA;AAAA,IAAA,4BAAA,OAAA,gBAAA,IAAA,6BAAA,uBAAA;AAMA,IAAA,oBAAA,CAAA;AAAA,IAAA,qBAAA,aAAA,OAAA,YAAA;AASI,IAAA,oBAAA,CAAA;AAAA,IAAA,qBAAA,UAAA,UAAA,OAAA,aAAA,IAAA,KAAA,MAAA,OAAA,OAAA,QAAA,cAAA,UAAA,OAAA,aAAA,IAAA,KAAA,MAAA,OAAA,OAAA,QAAA,QAAA;AAmBwC,IAAA,oBAAA,CAAA;AAAA,IAAA,qBAAA,YAAA,OAAA,aAAA,OAAA;AAC5C,IAAA,oBAAA;AAAA,IAAA,6BAAA,KAAA,OAAA,gBAAA,IAAA,aAAA,YAAA,GAAA;;;;;AA0BF,IAAA,yBAAA,GAAA,OAAA,EAAA;AACE,IAAA,iBAAA,GAAA,+CAAA;AACF,IAAA,uBAAA;;;;;AAUE,IAAA,yBAAA,GAAA,UAAA,EAAA;AACE,IAAA,iBAAA,CAAA;AACF,IAAA,uBAAA;;;;AAF8C,IAAA,qBAAA,SAAA,aAAA,EAAA;AAC5C,IAAA,oBAAA;AAAA,IAAA,6BAAA,KAAA,aAAA,KAAA,GAAA;;;;;AAGJ,IAAA,yBAAA,GAAA,OAAA,EAAA;AACE,IAAA,iBAAA,GAAA,sCAAA;AACF,IAAA,uBAAA;;;;;;AApCR,IAAA,yBAAA,GAAA,OAAA,EAAA;AAA0D,IAAA,qBAAA,SAAA,SAAA,mEAAA;AAAA,MAAA,wBAAA,IAAA;AAAA,YAAA,SAAA,wBAAA;AAAA,aAAA,sBAAS,OAAA,sBAAA,CAAuB;IAAA,CAAA;AACxF,IAAA,yBAAA,GAAA,OAAA,EAAA;AAA2B,IAAA,qBAAA,SAAA,SAAA,iEAAA,QAAA;AAAA,MAAA,wBAAA,IAAA;AAAA,aAAA,sBAAS,OAAA,gBAAA,CAAwB;IAAA,CAAA;AAC1D,IAAA,yBAAA,GAAA,OAAA,EAAA,EAA0B,GAAA,IAAA;AACpB,IAAA,iBAAA,CAAA;AAAsF,IAAA,uBAAA;AAC1F,IAAA,yBAAA,GAAA,UAAA,EAAA;AAA0B,IAAA,qBAAA,SAAA,SAAA,sEAAA;AAAA,MAAA,wBAAA,IAAA;AAAA,YAAA,SAAA,wBAAA;AAAA,aAAA,sBAAS,OAAA,sBAAA,CAAuB;IAAA,CAAA;AACxD,IAAA,oBAAA,GAAA,KAAA,EAAA;AACF,IAAA,uBAAA,EAAS;AAGX,IAAA,yBAAA,GAAA,QAAA,EAAA;AAAoC,IAAA,qBAAA,YAAA,SAAA,uEAAA;AAAA,MAAA,wBAAA,IAAA;AAAA,YAAA,SAAA,wBAAA;AAAA,aAAA,sBAAY,OAAA,oBAAA,CAAqB;IAAA,CAAA;AACnE,IAAA,yBAAA,GAAA,OAAA,EAAA,EAAwB,GAAA,SAAA,EAAA;AACO,IAAA,iBAAA,IAAA,OAAA;AAAK,IAAA,uBAAA;AAClC,IAAA,oBAAA,IAAA,SAAA,EAAA;AAMA,IAAA,qBAAA,IAAA,oDAAA,GAAA,GAAA,OAAA,EAAA;AAGF,IAAA,uBAAA;AAEA,IAAA,yBAAA,IAAA,OAAA,EAAA,EAAwB,IAAA,SAAA,EAAA;AACM,IAAA,iBAAA,IAAA,uBAAA;AAAkB,IAAA,uBAAA;AAC9C,IAAA,yBAAA,IAAA,UAAA,EAAA,EAGsB,IAAA,UAAA,EAAA;AACH,IAAA,iBAAA,IAAA,kCAAA;AAA0B,IAAA,uBAAA;AAC3C,IAAA,qBAAA,IAAA,uDAAA,GAAA,GAAA,UAAA,EAAA;AAGF,IAAA,uBAAA;AACA,IAAA,qBAAA,IAAA,oDAAA,GAAA,GAAA,OAAA,EAAA;AAGF,IAAA,uBAAA;AAEA,IAAA,yBAAA,IAAA,OAAA,EAAA,EAAwB,IAAA,SAAA,EAAA;AACc,IAAA,iBAAA,IAAA,aAAA;AAAW,IAAA,uBAAA;AAC/C,IAAA,oBAAA,IAAA,YAAA,EAAA;AAMF,IAAA,uBAAA;AAEA,IAAA,yBAAA,IAAA,OAAA,EAAA,EAA2B,IAAA,UAAA,EAAA;AACuB,IAAA,qBAAA,SAAA,SAAA,uEAAA;AAAA,MAAA,wBAAA,IAAA;AAAA,YAAA,SAAA,wBAAA;AAAA,aAAA,sBAAS,OAAA,sBAAA,CAAuB;IAAA,CAAA;AAC9E,IAAA,iBAAA,IAAA,WAAA;AACF,IAAA,uBAAA;AACA,IAAA,yBAAA,IAAA,UAAA,EAAA;AACE,IAAA,iBAAA,EAAA;AACF,IAAA,uBAAA,EAAS,EACL,EACD,EACH;;;;;;AAvDE,IAAA,oBAAA,CAAA;AAAA,IAAA,4BAAA,OAAA,oBAAA,IAAA,kCAAA,4BAAA;AAMA,IAAA,oBAAA,CAAA;AAAA,IAAA,qBAAA,aAAA,OAAA,eAAA;AASI,IAAA,oBAAA,CAAA;AAAA,IAAA,qBAAA,UAAA,UAAA,OAAA,gBAAA,IAAA,KAAA,MAAA,OAAA,OAAA,QAAA,cAAA,UAAA,OAAA,gBAAA,IAAA,KAAA,MAAA,OAAA,OAAA,QAAA,QAAA;AAYyB,IAAA,oBAAA,CAAA;AAAA,IAAA,qBAAA,WAAA,OAAA,WAAA,CAAA;AAIzB,IAAA,oBAAA;AAAA,IAAA,qBAAA,UAAA,UAAA,OAAA,gBAAA,IAAA,aAAA,MAAA,OAAA,OAAA,QAAA,cAAA,UAAA,OAAA,gBAAA,IAAA,aAAA,MAAA,OAAA,OAAA,QAAA,QAAA;AAmBwC,IAAA,oBAAA,CAAA;AAAA,IAAA,qBAAA,YAAA,OAAA,gBAAA,OAAA;AAC5C,IAAA,oBAAA;AAAA,IAAA,6BAAA,KAAA,OAAA,oBAAA,IAAA,aAAA,YAAA,GAAA;;;ADjTJ,IAAO,8BAAP,MAAO,6BAA2B;EAmB5B;EACA;EACA;EACA;;EApBV,aAAa,OAAyB,CAAA,CAAE;EACxC,iBAAiB,OAA6B,CAAA,CAAE;EAChD,YAAY,OAAO,KAAK;EACxB,QAAQ,OAAO,EAAE;EACjB,YAAY,OAAwC,YAAY;;EAGhE,oBAAoB,OAAO,KAAK;EAChC,uBAAuB,OAAO,KAAK;EACnC,kBAAkB,OAA8B,IAAI;EACpD,sBAAsB,OAAkC,IAAI;;EAG5D;EACA;EAEA,YACU,cACA,kBACA,sBACA,IAAe;AAHf,SAAA,eAAA;AACA,SAAA,mBAAA;AACA,SAAA,uBAAA;AACA,SAAA,KAAA;AAER,SAAK,eAAe,KAAK,GAAG,MAAM;MAChC,KAAK,CAAC,IAAI,CAAC,WAAW,UAAU,WAAW,UAAU,CAAC,CAAC,CAAC;MACxD,aAAa,CAAC,EAAE;KACjB;AAED,SAAK,kBAAkB,KAAK,GAAG,MAAM;MACnC,KAAK,CAAC,IAAI,CAAC,WAAW,UAAU,WAAW,UAAU,CAAC,CAAC,CAAC;MACxD,aAAa,CAAC,EAAE;MAChB,aAAa,CAAC,IAAI,CAAC,WAAW,QAAQ,CAAC;KACxC;EACH;EAEA,WAAQ;AACN,SAAK,eAAc;AACnB,SAAK,mBAAkB;EACzB;EAEA,iBAAc;AACZ,SAAK,UAAU,IAAI,IAAI;AACvB,SAAK,MAAM,IAAI,EAAE;AAEjB,SAAK,iBAAiB,OAAM,EAAG,UAAU;MACvC,MAAM,CAAC,eAAc;AACnB,gBAAQ,IAAI,mCAAwB,UAAU;AAG9C,cAAM,kBAAoC,CAAA;AAC1C,YAAI,iBAAiB;AAErB,YAAI,WAAW,WAAW,GAAG;AAC3B,eAAK,WAAW,IAAI,CAAA,CAAE;AACtB,eAAK,UAAU,IAAI,KAAK;AACxB;QACF;AAEA,mBAAW,QAAQ,SAAM;AAEvB,eAAK,iBAAiB,iBAAiB,IAAI,EAAE,EAAE,UAAU;YACvD,MAAM,CAAC,mBAAkB;AACvB,8BAAgB,KAAK;gBACnB,IAAI,IAAI;gBACR,KAAK,IAAI;gBACT,aAAa,IAAI;gBACjB,WAAW,IAAI;gBACf,eAAc,oBAAI,KAAI,GAAG,YAAW;gBACpC;gBACA,gBAAgB,CAAA;eACjB;AAED;AACA,kBAAI,mBAAmB,WAAW,QAAQ;AAExC,gCAAgB,KAAK,CAAC,GAAG,MAAM,EAAE,IAAI,cAAc,EAAE,GAAG,CAAC;AACzD,qBAAK,WAAW,IAAI,eAAe;AACnC,qBAAK,UAAU,IAAI,KAAK;cAC1B;YACF;YACA,OAAO,CAAC,UAAS;AACf,sBAAQ,KAAK,8CAA2C,IAAI,EAAE,KAAK,KAAK;AACxE,8BAAgB,KAAK;gBACnB,IAAI,IAAI;gBACR,KAAK,IAAI;gBACT,aAAa,IAAI;gBACjB,WAAW,IAAI;gBACf,eAAc,oBAAI,KAAI,GAAG,YAAW;gBACpC,gBAAgB;gBAChB,gBAAgB,CAAA;eACjB;AAED;AACA,kBAAI,mBAAmB,WAAW,QAAQ;AACxC,gCAAgB,KAAK,CAAC,GAAG,MAAM,EAAE,IAAI,cAAc,EAAE,GAAG,CAAC;AACzD,qBAAK,WAAW,IAAI,eAAe;AACnC,qBAAK,UAAU,IAAI,KAAK;cAC1B;YACF;WACD;QACH,CAAC;MACH;MACA,OAAO,CAAC,UAAS;AACf,gBAAQ,MAAM,uDAA+C,KAAK;AAClE,aAAK,MAAM,IAAI,6CAA0C;AACzD,aAAK,UAAU,IAAI,KAAK;MAC1B;KACD;EACH;EAEA,qBAAkB;AAChB,SAAK,qBAAqB,OAAM,EAAG,UAAU;MAC3C,MAAM,CAAC,mBAAkB;AACvB,gBAAQ,IAAI,wCAA6B,cAAc;AAGvD,cAAM,sBAA4C,CAAA;AAClD,YAAI,iBAAiB;AAErB,YAAI,eAAe,WAAW,GAAG;AAC/B,eAAK,eAAe,IAAI,CAAA,CAAE;AAC1B;QACF;AAEA,uBAAe,QAAQ,aAAU;AAE/B,eAAK,qBAAqB,iBAAiB,QAAQ,EAAE,EAAE,UAAU;YAC/D,MAAM,CAAC,mBAAkB;AACvB,kCAAoB,KAAK;gBACvB,IAAI,QAAQ;gBACZ,KAAK,QAAQ;gBACb,aAAa,QAAQ;gBACrB,aAAa,QAAQ;gBACrB,cAAc,KAAK,gBAAgB,QAAQ,WAAW;gBACtD,WAAW,QAAQ;gBACnB,eAAc,oBAAI,KAAI,GAAG,YAAW;gBACpC;eACD;AAED;AACA,kBAAI,mBAAmB,eAAe,QAAQ;AAE5C,oCAAoB,KAAK,CAAC,GAAG,MAAM,EAAE,IAAI,cAAc,EAAE,GAAG,CAAC;AAC7D,qBAAK,eAAe,IAAI,mBAAmB;cAC7C;YACF;YACA,OAAO,CAAC,UAAS;AACf,sBAAQ,KAAK,mDAAgD,QAAQ,EAAE,KAAK,KAAK;AACjF,kCAAoB,KAAK;gBACvB,IAAI,QAAQ;gBACZ,KAAK,QAAQ;gBACb,aAAa,QAAQ;gBACrB,aAAa,QAAQ;gBACrB,cAAc,KAAK,gBAAgB,QAAQ,WAAW;gBACtD,WAAW,QAAQ;gBACnB,eAAc,oBAAI,KAAI,GAAG,YAAW;gBACpC,gBAAgB;eACjB;AAED;AACA,kBAAI,mBAAmB,eAAe,QAAQ;AAC5C,oCAAoB,KAAK,CAAC,GAAG,MAAM,EAAE,IAAI,cAAc,EAAE,GAAG,CAAC;AAC7D,qBAAK,eAAe,IAAI,mBAAmB;cAC7C;YACF;WACD;QACH,CAAC;MACH;MACA,OAAO,CAAC,UAAS;AACf,gBAAQ,MAAM,4DAAoD,KAAK;AACvE,aAAK,MAAM,IAAI,kDAA+C;MAChE;KACD;EACH;;EAIA,kBAAkB,UAAyB;AACzC,SAAK,gBAAgB,IAAI,YAAY,IAAI;AAEzC,QAAI,UAAU;AACZ,WAAK,aAAa,WAAW;QAC3B,KAAK,SAAS;QACd,aAAa,SAAS,eAAe;OACtC;IACH,OAAO;AACL,WAAK,aAAa,MAAK;IACzB;AAEA,SAAK,kBAAkB,IAAI,IAAI;EACjC;EAEA,qBAAkB;AAChB,SAAK,kBAAkB,IAAI,KAAK;AAChC,SAAK,gBAAgB,IAAI,IAAI;AAC7B,SAAK,aAAa,MAAK;EACzB;EAEA,mBAAgB;AACd,QAAI,KAAK,aAAa,OAAO;AAC3B,YAAM,WAAW,KAAK,aAAa;AACnC,YAAM,aAAa,KAAK,gBAAe;AAEvC,UAAI,YAAY;AAEd,aAAK,aAAa,gBAAgB,WAAW,IAAI,QAAQ,EAAE,UAAU;UACnE,MAAM,MAAK;AACT,iBAAK,eAAc;AACnB,iBAAK,mBAAkB;AACvB,kBAAM,yCAAgC;UACxC;UACA,OAAO,CAAC,UAAS;AACf,oBAAQ,MAAM,mCAAmC,KAAK;AACtD,kBAAM,mDAAgD;UACxD;SACD;MACH,OAAO;AAEL,aAAK,aAAa,gBAAgB,QAAQ,EAAE,UAAU;UACpD,MAAM,MAAK;AACT,iBAAK,eAAc;AACnB,iBAAK,mBAAkB;AACvB,kBAAM,yCAA6B;UACrC;UACA,OAAO,CAAC,UAAS;AACf,oBAAQ,MAAM,kCAA+B,KAAK;AAClD,kBAAM,kDAA4C;UACpD;SACD;MACH;IACF;EACF;EAEA,eAAe,UAAwB;AACrC,QAAI,QAAQ,6DAAoD,SAAS,GAAG;;0EAA4E,GAAG;AACzJ,WAAK,aAAa,gBAAgB,SAAS,EAAE,EAAE,UAAU;QACvD,MAAM,MAAK;AACT,eAAK,eAAc;AACnB,eAAK,mBAAkB;AACvB,gBAAM,0CAAiC;QACzC;QACA,OAAO,CAAC,UAAS;AACf,kBAAQ,MAAM,kCAAkC,KAAK;AACrD,gBAAM,kDAA+C;QACvD;OACD;IACH;EACF;EAEA,iBAAiB,UAAwB;AACvC,SAAK,aAAa,iBAAiB,SAAS,EAAE,EAAE,UAAU;MACxD,MAAM,MAAK;AAET,cAAM,OAAO,KAAK,WAAU;AAC5B,cAAM,QAAQ,KAAK,UAAU,OAAK,EAAE,OAAO,SAAS,EAAE;AACtD,YAAI,UAAU,IAAI;AAChB,eAAK,KAAK,IAAI,iCAAK,KAAK,KAAK,IAAf,EAAkB,WAAW,KAAI;AAC/C,eAAK,WAAW,IAAI,CAAC,GAAG,IAAI,CAAC;QAC/B;AACA,cAAM,wCAA+B;MACvC;MACA,OAAO,CAAC,UAAS;AACf,gBAAQ,MAAM,iCAAiC,KAAK;AACpD,YAAI,eAAe;AAEnB,YAAI,MAAM,WAAW,KAAK;AACxB,yBAAe;QACjB,WAAW,MAAM,WAAW,GAAG;AAC7B,yBAAe;QACjB,WAAW,MAAM,OAAO,SAAS;AAC/B,yBAAe,WAAW,MAAM,MAAM,OAAO;QAC/C;AAEA,cAAM,YAAY;MACpB;KACD;EACH;EAEA,eAAe,UAAwB;AACrC,UAAM,QAAQ,OAAO,iBAAiB;AACtC,QAAI,OAAO;AACT,WAAK,aAAa,iBAAiB,SAAS,IAAI,KAAK,EAAE,UAAU;QAC/D,MAAM,MAAK;AAET,gBAAM,OAAO,KAAK,WAAU;AAC5B,gBAAM,QAAQ,KAAK,UAAU,OAAK,EAAE,OAAO,SAAS,EAAE;AACtD,cAAI,UAAU,IAAI;AAChB,iBAAK,KAAK,IAAI,iCAAK,KAAK,KAAK,IAAf,EAAkB,WAAW,MAAK;AAChD,iBAAK,WAAW,IAAI,CAAC,GAAG,IAAI,CAAC;UAC/B;AACA,gBAAM,wCAA+B;QACvC;QACA,OAAO,CAAC,UAAS;AACf,kBAAQ,MAAM,yBAAyB,KAAK;AAC5C,gBAAM,yCAAsC;QAC9C;OACD;IACH;EACF;;EAIA,qBAAqB,cAAiC;AACpD,SAAK,oBAAoB,IAAI,gBAAgB,IAAI;AAEjD,QAAI,cAAc;AAChB,WAAK,gBAAgB,WAAW;QAC9B,KAAK,aAAa;QAClB,aAAa,aAAa,eAAe;QACzC,aAAa,aAAa;OAC3B;IACH,OAAO;AACL,WAAK,gBAAgB,MAAK;IAC5B;AAEA,SAAK,qBAAqB,IAAI,IAAI;EACpC;EAEA,wBAAqB;AACnB,SAAK,qBAAqB,IAAI,KAAK;AACnC,SAAK,oBAAoB,IAAI,IAAI;AACjC,SAAK,gBAAgB,MAAK;EAC5B;EAEA,sBAAmB;AACjB,QAAI,KAAK,gBAAgB,OAAO;AAC9B,YAAM,WAAW,KAAK,gBAAgB;AACtC,YAAM,iBAAiB,KAAK,oBAAmB;AAE/C,UAAI,gBAAgB;AAElB,aAAK,aAAa,oBAAoB,eAAe,IAAI,QAAQ,EAAE,UAAU;UAC3E,MAAM,MAAK;AACT,iBAAK,mBAAkB;AACvB,iBAAK,sBAAqB;AAC1B,kBAAM,8CAAqC;UAC7C;UACA,OAAO,CAAC,UAAS;AACf,oBAAQ,MAAM,mCAAmC,KAAK;AACtD,kBAAM,wDAAqD;UAC7D;SACD;MACH,OAAO;AAEL,aAAK,aAAa,oBAAoB,QAAQ,EAAE,UAAU;UACxD,MAAM,MAAK;AACT,iBAAK,mBAAkB;AACvB,iBAAK,sBAAqB;AAC1B,kBAAM,8CAAkC;UAC1C;UACA,OAAO,CAAC,UAAS;AACf,oBAAQ,MAAM,kCAA+B,KAAK;AAClD,kBAAM,uDAAiD;UACzD;SACD;MACH;IACF;EACF;EAEA,kBAAkB,cAAgC;AAChD,QAAI,QAAQ,kEAAyD,aAAa,GAAG,KAAK,GAAG;AAC3F,WAAK,aAAa,oBAAoB,aAAa,EAAE,EAAE,UAAU;QAC/D,MAAM,MAAK;AACT,eAAK,mBAAkB;AACvB,gBAAM,+CAAsC;QAC9C;QACA,OAAO,CAAC,UAAS;AACf,kBAAQ,MAAM,kCAAkC,KAAK;AACrD,gBAAM,uDAAoD;QAC5D;OACD;IACH;EACF;EAEA,oBAAoB,cAAgC;AAClD,SAAK,aAAa,qBAAqB,aAAa,EAAE,EAAE,UAAU;MAChE,MAAM,MAAK;AAET,cAAM,WAAW,KAAK,eAAc;AACpC,cAAM,QAAQ,SAAS,UAAU,QAAM,GAAG,OAAO,aAAa,EAAE;AAChE,YAAI,UAAU,IAAI;AAChB,mBAAS,KAAK,IAAI,iCAAK,SAAS,KAAK,IAAnB,EAAsB,WAAW,KAAI;AACvD,eAAK,eAAe,IAAI,CAAC,GAAG,QAAQ,CAAC;QACvC;AACA,cAAM,6CAAoC;MAC5C;MACA,OAAO,CAAC,UAAS;AACf,gBAAQ,MAAM,iCAAiC,KAAK;AACpD,cAAM,sDAAmD;MAC3D;KACD;EACH;EAEA,kBAAkB,cAAgC;AAChD,UAAM,QAAQ,OAAO,iBAAiB;AACtC,QAAI,OAAO;AACT,WAAK,aAAa,qBAAqB,aAAa,IAAI,KAAK,EAAE,UAAU;QACvE,MAAM,MAAK;AAET,gBAAM,WAAW,KAAK,eAAc;AACpC,gBAAM,QAAQ,SAAS,UAAU,QAAM,GAAG,OAAO,aAAa,EAAE;AAChE,cAAI,UAAU,IAAI;AAChB,qBAAS,KAAK,IAAI,iCAAK,SAAS,KAAK,IAAnB,EAAsB,WAAW,MAAK;AACxD,iBAAK,eAAe,IAAI,CAAC,GAAG,QAAQ,CAAC;UACvC;AACA,gBAAM,6CAAoC;QAC5C;QACA,OAAO,CAAC,UAAS;AACf,kBAAQ,MAAM,yBAAyB,KAAK;AAC5C,gBAAM,8CAA2C;QACnD;OACD;IACH;EACF;;EAIA,aAAa,KAAoC;AAC/C,SAAK,UAAU,IAAI,GAAG;EACxB;EAEA,eAAe,WAAkB;AAC/B,WAAO,YAAY,qBAAqB;EAC1C;EAEA,cAAc,WAAkB;AAC9B,WAAO,YAAY,eAAY;EACjC;EAEA,WAAW,MAAY;AACrB,WAAO,IAAI,KAAK,IAAI,EAAE,mBAAmB,SAAS;MAChD,MAAM;MACN,OAAO;MACP,KAAK;KACN;EACH;EAEA,gBAAgB,aAAmB;AACjC,UAAM,WAAW,KAAK,WAAU,EAAG,KAAK,OAAK,EAAE,OAAO,WAAW;AACjE,WAAO,WAAW,SAAS,MAAM;EACnC;EAEA,UAAO;AACL,SAAK,eAAc;AACnB,SAAK,mBAAkB;EACzB;;qCA9bW,8BAA2B,4BAAA,YAAA,GAAA,4BAAA,gBAAA,GAAA,4BAAA,oBAAA,GAAA,4BAAA,WAAA,CAAA;EAAA;yEAA3B,8BAA2B,WAAA,CAAA,CAAA,yBAAA,CAAA,GAAA,OAAA,IAAA,MAAA,IAAA,QAAA,CAAA,CAAA,iBAAA,EAAA,GAAA,CAAA,GAAA,+BAAA,GAAA,CAAA,GAAA,aAAA,GAAA,CAAA,GAAA,gBAAA,GAAA,CAAA,GAAA,YAAA,GAAA,CAAA,GAAA,aAAA,GAAA,CAAA,GAAA,gBAAA,GAAA,CAAA,GAAA,OAAA,iBAAA,GAAA,SAAA,UAAA,GAAA,CAAA,GAAA,cAAA,GAAA,CAAA,GAAA,gBAAA,GAAA,CAAA,GAAA,MAAA,GAAA,CAAA,GAAA,cAAA,GAAA,OAAA,GAAA,CAAA,GAAA,kBAAA,GAAA,CAAA,SAAA,iBAAA,GAAA,MAAA,GAAA,CAAA,SAAA,qBAAA,GAAA,MAAA,GAAA,CAAA,SAAA,eAAA,GAAA,MAAA,GAAA,CAAA,SAAA,iBAAA,GAAA,SAAA,GAAA,MAAA,GAAA,CAAA,GAAA,eAAA,GAAA,CAAA,GAAA,YAAA,GAAA,CAAA,GAAA,mBAAA,GAAA,CAAA,GAAA,iBAAA,GAAA,CAAA,GAAA,aAAA,GAAA,CAAA,GAAA,gBAAA,GAAA,CAAA,GAAA,OAAA,eAAA,GAAA,OAAA,GAAA,CAAA,GAAA,WAAA,GAAA,CAAA,GAAA,iBAAA,GAAA,CAAA,GAAA,YAAA,GAAA,CAAA,SAAA,YAAA,GAAA,SAAA,SAAA,GAAA,CAAA,SAAA,WAAA,GAAA,MAAA,GAAA,CAAA,GAAA,UAAA,GAAA,CAAA,GAAA,WAAA,GAAA,CAAA,GAAA,WAAA,GAAA,CAAA,GAAA,SAAA,GAAA,CAAA,GAAA,kBAAA,GAAA,CAAA,GAAA,QAAA,UAAA,GAAA,CAAA,GAAA,gBAAA,GAAA,SAAA,GAAA,CAAA,GAAA,YAAA,GAAA,CAAA,GAAA,WAAA,GAAA,CAAA,GAAA,cAAA,GAAA,CAAA,GAAA,gBAAA,GAAA,CAAA,SAAA,YAAA,GAAA,cAAA,YAAA,GAAA,OAAA,GAAA,CAAA,GAAA,WAAA,GAAA,CAAA,SAAA,2BAAA,SAAA,WAAA,GAAA,SAAA,GAAA,MAAA,GAAA,CAAA,SAAA,yBAAA,SAAA,WAAA,GAAA,SAAA,GAAA,MAAA,GAAA,CAAA,SAAA,aAAA,GAAA,cAAA,cAAA,GAAA,OAAA,GAAA,CAAA,GAAA,YAAA,GAAA,CAAA,GAAA,gBAAA,GAAA,CAAA,SAAA,WAAA,GAAA,cAAA,gBAAA,GAAA,OAAA,GAAA,CAAA,GAAA,YAAA,GAAA,CAAA,SAAA,WAAA,GAAA,cAAA,cAAA,GAAA,OAAA,GAAA,CAAA,GAAA,QAAA,GAAA,CAAA,GAAA,SAAA,GAAA,CAAA,GAAA,aAAA,GAAA,CAAA,GAAA,iBAAA,GAAA,OAAA,GAAA,CAAA,GAAA,iBAAA,GAAA,OAAA,GAAA,CAAA,GAAA,cAAA,GAAA,CAAA,GAAA,aAAA,GAAA,OAAA,GAAA,CAAA,GAAA,cAAA,GAAA,YAAA,WAAA,GAAA,CAAA,GAAA,YAAA,GAAA,CAAA,OAAA,cAAA,GAAA,CAAA,MAAA,gBAAA,QAAA,QAAA,mBAAA,OAAA,eAAA,0BAAA,GAAA,YAAA,GAAA,CAAA,SAAA,cAAA,GAAA,MAAA,GAAA,CAAA,OAAA,qBAAA,GAAA,CAAA,MAAA,uBAAA,mBAAA,eAAA,eAAA,kCAAA,QAAA,KAAA,GAAA,eAAA,GAAA,CAAA,GAAA,eAAA,GAAA,CAAA,QAAA,UAAA,GAAA,OAAA,iBAAA,GAAA,OAAA,GAAA,CAAA,QAAA,UAAA,GAAA,OAAA,eAAA,GAAA,UAAA,GAAA,CAAA,GAAA,YAAA,GAAA,CAAA,OAAA,iBAAA,GAAA,CAAA,MAAA,mBAAA,QAAA,QAAA,mBAAA,OAAA,eAAA,+BAAA,GAAA,YAAA,GAAA,CAAA,OAAA,gBAAA,GAAA,CAAA,MAAA,kBAAA,mBAAA,eAAA,GAAA,aAAA,GAAA,CAAA,SAAA,EAAA,GAAA,CAAA,GAAA,SAAA,GAAA,SAAA,SAAA,GAAA,CAAA,OAAA,wBAAA,GAAA,CAAA,MAAA,0BAAA,mBAAA,eAAA,eAAA,uCAAA,QAAA,KAAA,GAAA,eAAA,GAAA,CAAA,GAAA,OAAA,CAAA,GAAA,UAAA,SAAA,qCAAA,IAAA,KAAA;AAAA,QAAA,KAAA,GAAA;ACnCxC,MAAA,yBAAA,GAAA,OAAA,CAAA,EAA2C,GAAA,OAAA,CAAA,EAEhB,GAAA,OAAA,CAAA,EACK,GAAA,MAAA,CAAA;AAExB,MAAA,oBAAA,GAAA,KAAA,CAAA;AACA,MAAA,iBAAA,GAAA,6BAAA;AACF,MAAA,uBAAA;AACA,MAAA,yBAAA,GAAA,OAAA,CAAA,EAA4B,GAAA,UAAA,CAAA;AACQ,MAAA,qBAAA,SAAA,SAAA,+DAAA;AAAA,eAAS,IAAA,QAAA;MAAS,CAAA;AAClD,MAAA,oBAAA,GAAA,KAAA,CAAA;AACA,MAAA,iBAAA,GAAA,cAAA;AACF,MAAA,uBAAA,EAAS,EACL,EACF;AAIR,MAAA,yBAAA,IAAA,OAAA,CAAA,EAA4B,IAAA,OAAA,EAAA,EACR,IAAA,UAAA,EAAA;AAId,MAAA,qBAAA,SAAA,SAAA,gEAAA;AAAA,eAAS,IAAA,aAAa,YAAY;MAAC,CAAA;AACnC,MAAA,oBAAA,IAAA,KAAA,CAAA;AACA,MAAA,iBAAA,EAAA;AACF,MAAA,uBAAA;AACA,MAAA,yBAAA,IAAA,UAAA,EAAA;AAGE,MAAA,qBAAA,SAAA,SAAA,gEAAA;AAAA,eAAS,IAAA,aAAa,gBAAgB;MAAC,CAAA;AACvC,MAAA,oBAAA,IAAA,KAAA,EAAA;AACA,MAAA,iBAAA,EAAA;AACF,MAAA,uBAAA,EAAS,EACL;AAIR,MAAA,qBAAA,IAAA,6CAAA,GAAA,GAAA,OAAA,EAAA,EAA2C,IAAA,6CAAA,GAAA,GAAA,OAAA,EAAA,EAMQ,IAAA,6CAAA,IAAA,GAAA,OAAA,EAAA,EAM2B,IAAA,6CAAA,IAAA,GAAA,OAAA,EAAA;AA2LhF,MAAA,uBAAA;AAGA,MAAA,qBAAA,IAAA,6CAAA,IAAA,GAAA,OAAA,EAAA,EAAsF,IAAA,6CAAA,IAAA,GAAA,OAAA,EAAA;;;AAvOxB,MAAA,oBAAA,CAAA;AAAA,MAAA,qBAAA,YAAA,IAAA,UAAA,CAAA;AAatD,MAAA,oBAAA,CAAA;AAAA,MAAA,sBAAA,UAAA,IAAA,UAAA,MAAA,YAAA;AAGA,MAAA,oBAAA,CAAA;AAAA,MAAA,6BAAA,oBAAA,IAAA,WAAA,EAAA,QAAA,IAAA;AAIA,MAAA,oBAAA;AAAA,MAAA,sBAAA,UAAA,IAAA,UAAA,MAAA,gBAAA;AAGA,MAAA,oBAAA,CAAA;AAAA,MAAA,6BAAA,yBAAA,IAAA,eAAA,EAAA,QAAA,IAAA;AAMA,MAAA,oBAAA;AAAA,MAAA,qBAAA,QAAA,IAAA,MAAA,CAAA;AAMA,MAAA,oBAAA;AAAA,MAAA,qBAAA,QAAA,IAAA,UAAA,CAAA;AAMA,MAAA,oBAAA;AAAA,MAAA,qBAAA,QAAA,IAAA,UAAA,MAAA,gBAAA,CAAA,IAAA,UAAA,CAAA;AA6FA,MAAA,oBAAA;AAAA,MAAA,qBAAA,QAAA,IAAA,UAAA,MAAA,oBAAA,CAAA,IAAA,UAAA,CAAA;AAiGF,MAAA,oBAAA;AAAA,MAAA,qBAAA,QAAA,IAAA,kBAAA,CAAA;AA8CA,MAAA,oBAAA;AAAA,MAAA,qBAAA,QAAA,IAAA,qBAAA,CAAA;;oBD/PM,cAAY,SAAA,SAAA,MAAE,aAAW,oBAAA,gBAAA,8BAAA,sBAAA,4BAAA,iBAAA,sBAAE,qBAAmB,oBAAA,eAAA,GAAA,QAAA,CAAA,s3RAAA,EAAA,CAAA;;;sEAI7C,6BAA2B,CAAA;UAPvC;uBACW,2BAAyB,YACvB,MAAI,SACP,CAAC,cAAc,aAAa,mBAAmB,GAAC,UAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;GAAA,QAAA,CAAA,2lOAAA,EAAA,CAAA;;;;6EAI9C,6BAA2B,EAAA,WAAA,+BAAA,UAAA,iFAAA,YAAA,GAAA,CAAA;AAAA,GAAA;", "names": []}
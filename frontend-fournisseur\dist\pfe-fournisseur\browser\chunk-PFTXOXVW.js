import {
  NotificationService
} from "./chunk-2WHFEWR5.js";
import {
  AuthService
} from "./chunk-ZSXXNL7U.js";
import "./chunk-GFHHPDQ6.js";
import "./chunk-7JDDWGD3.js";
import {
  CommonModule,
  Component,
  NgForOf,
  NgIf,
  Subscription,
  setClassMetadata,
  ɵsetClassDebugInfo,
  ɵɵadvance,
  ɵɵclassProp,
  ɵɵdefineComponent,
  ɵɵdirectiveInject,
  ɵɵelement,
  ɵɵelementEnd,
  ɵɵelementStart,
  ɵɵgetCurrentView,
  ɵɵlistener,
  ɵɵnamespaceHTML,
  ɵɵnamespaceSVG,
  ɵɵnextContext,
  ɵɵproperty,
  ɵɵresetView,
  ɵɵrestoreView,
  ɵɵtemplate,
  ɵɵtext,
  ɵɵtextInterpolate1
} from "./chunk-UBZQS7JS.js";

// src/app/notifications/notifications.component.ts
function NotificationsComponent_button_8_Template(rf, ctx) {
  if (rf & 1) {
    const _r1 = \u0275\u0275getCurrentView();
    \u0275\u0275elementStart(0, "button", 12);
    \u0275\u0275listener("click", function NotificationsComponent_button_8_Template_button_click_0_listener() {
      \u0275\u0275restoreView(_r1);
      const ctx_r1 = \u0275\u0275nextContext();
      return \u0275\u0275resetView(ctx_r1.markAllAsRead());
    });
    \u0275\u0275namespaceSVG();
    \u0275\u0275elementStart(1, "svg", 13);
    \u0275\u0275element(2, "polyline", 14)(3, "path", 15);
    \u0275\u0275elementEnd();
    \u0275\u0275text(4);
    \u0275\u0275elementEnd();
  }
  if (rf & 2) {
    const ctx_r1 = \u0275\u0275nextContext();
    \u0275\u0275advance(4);
    \u0275\u0275textInterpolate1(" Tout marquer comme lu (", ctx_r1.unreadCount, ") ");
  }
}
function NotificationsComponent_div_9_div_7_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "div", 20);
    \u0275\u0275namespaceSVG();
    \u0275\u0275elementStart(1, "svg", 13);
    \u0275\u0275element(2, "circle", 21);
    \u0275\u0275elementEnd();
    \u0275\u0275text(3);
    \u0275\u0275elementEnd();
  }
  if (rf & 2) {
    const ctx_r1 = \u0275\u0275nextContext(2);
    \u0275\u0275advance(3);
    \u0275\u0275textInterpolate1(" ", ctx_r1.unreadCount, " non lue(s) ");
  }
}
function NotificationsComponent_div_9_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "div", 16)(1, "div", 17)(2, "div", 18);
    \u0275\u0275namespaceSVG();
    \u0275\u0275elementStart(3, "svg", 13);
    \u0275\u0275element(4, "path", 3)(5, "path", 4);
    \u0275\u0275elementEnd();
    \u0275\u0275text(6);
    \u0275\u0275elementEnd();
    \u0275\u0275template(7, NotificationsComponent_div_9_div_7_Template, 4, 1, "div", 19);
    \u0275\u0275elementEnd()();
  }
  if (rf & 2) {
    const ctx_r1 = \u0275\u0275nextContext();
    \u0275\u0275advance(6);
    \u0275\u0275textInterpolate1(" ", ctx_r1.notifications.length, " notification(s) ");
    \u0275\u0275advance();
    \u0275\u0275property("ngIf", ctx_r1.unreadCount > 0);
  }
}
function NotificationsComponent_div_11_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "div", 22);
    \u0275\u0275element(1, "div", 23);
    \u0275\u0275elementStart(2, "p");
    \u0275\u0275text(3, "Chargement des notifications...");
    \u0275\u0275elementEnd()();
  }
}
function NotificationsComponent_div_12_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "div", 24);
    \u0275\u0275namespaceSVG();
    \u0275\u0275elementStart(1, "svg", 25);
    \u0275\u0275element(2, "path", 26)(3, "circle", 27);
    \u0275\u0275elementEnd();
    \u0275\u0275namespaceHTML();
    \u0275\u0275elementStart(4, "h3");
    \u0275\u0275text(5, "Aucune notification");
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(6, "p");
    \u0275\u0275text(7, "Vous n'avez aucune notification pour le moment.");
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(8, "p", 28);
    \u0275\u0275text(9, "Les notifications appara\xEEtront ici lorsque :");
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(10, "ul", 29)(11, "li");
    \u0275\u0275text(12, "\u2022 Un client laisse un avis sur vos produits");
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(13, "li");
    \u0275\u0275text(14, "\u2022 Une nouvelle commande est pass\xE9e");
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(15, "li");
    \u0275\u0275text(16, "\u2022 Un paiement est confirm\xE9");
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(17, "li");
    \u0275\u0275text(18, "\u2022 Des mises \xE0 jour importantes sont disponibles");
    \u0275\u0275elementEnd()()();
  }
}
function NotificationsComponent_div_13_div_1__svg_svg_3_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275namespaceSVG();
    \u0275\u0275elementStart(0, "svg", 47);
    \u0275\u0275element(1, "circle", 21);
    \u0275\u0275elementEnd();
  }
}
function NotificationsComponent_div_13_div_1__svg_svg_4_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275namespaceSVG();
    \u0275\u0275elementStart(0, "svg", 48);
    \u0275\u0275element(1, "path", 49)(2, "polyline", 50);
    \u0275\u0275elementEnd();
  }
}
function NotificationsComponent_div_13_div_1_button_8_Template(rf, ctx) {
  if (rf & 1) {
    const _r4 = \u0275\u0275getCurrentView();
    \u0275\u0275elementStart(0, "button", 51);
    \u0275\u0275listener("click", function NotificationsComponent_div_13_div_1_button_8_Template_button_click_0_listener() {
      \u0275\u0275restoreView(_r4);
      const notification_r5 = \u0275\u0275nextContext().$implicit;
      const ctx_r1 = \u0275\u0275nextContext(2);
      return \u0275\u0275resetView(ctx_r1.markAsRead(notification_r5));
    });
    \u0275\u0275namespaceSVG();
    \u0275\u0275elementStart(1, "svg", 13);
    \u0275\u0275element(2, "polyline", 52);
    \u0275\u0275elementEnd()();
  }
}
function NotificationsComponent_div_13_div_1_Template(rf, ctx) {
  if (rf & 1) {
    const _r3 = \u0275\u0275getCurrentView();
    \u0275\u0275elementStart(0, "div", 32)(1, "div", 33)(2, "div", 34);
    \u0275\u0275template(3, NotificationsComponent_div_13_div_1__svg_svg_3_Template, 2, 0, "svg", 35)(4, NotificationsComponent_div_13_div_1__svg_svg_4_Template, 3, 0, "svg", 36);
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(5, "div", 37);
    \u0275\u0275text(6);
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(7, "div", 38);
    \u0275\u0275template(8, NotificationsComponent_div_13_div_1_button_8_Template, 3, 0, "button", 39);
    \u0275\u0275elementStart(9, "button", 40);
    \u0275\u0275listener("click", function NotificationsComponent_div_13_div_1_Template_button_click_9_listener() {
      const notification_r5 = \u0275\u0275restoreView(_r3).$implicit;
      const ctx_r1 = \u0275\u0275nextContext(2);
      return \u0275\u0275resetView(ctx_r1.deleteNotification(notification_r5.id));
    });
    \u0275\u0275namespaceSVG();
    \u0275\u0275elementStart(10, "svg", 13);
    \u0275\u0275element(11, "polyline", 41)(12, "path", 42);
    \u0275\u0275elementEnd()()()();
    \u0275\u0275namespaceHTML();
    \u0275\u0275elementStart(13, "div", 43)(14, "span", 44);
    \u0275\u0275namespaceSVG();
    \u0275\u0275elementStart(15, "svg", 45);
    \u0275\u0275element(16, "circle", 21)(17, "polyline", 46);
    \u0275\u0275elementEnd();
    \u0275\u0275text(18);
    \u0275\u0275elementEnd()()();
  }
  if (rf & 2) {
    const notification_r5 = ctx.$implicit;
    const ctx_r1 = \u0275\u0275nextContext(2);
    \u0275\u0275classProp("unread", !notification_r5.estLue);
    \u0275\u0275advance(3);
    \u0275\u0275property("ngIf", !notification_r5.estLue);
    \u0275\u0275advance();
    \u0275\u0275property("ngIf", notification_r5.estLue);
    \u0275\u0275advance(2);
    \u0275\u0275textInterpolate1(" ", notification_r5.contenu, " ");
    \u0275\u0275advance(2);
    \u0275\u0275property("ngIf", !notification_r5.estLue);
    \u0275\u0275advance(10);
    \u0275\u0275textInterpolate1(" ", ctx_r1.formatDate(notification_r5.dateEnvoi), " ");
  }
}
function NotificationsComponent_div_13_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "div", 30);
    \u0275\u0275template(1, NotificationsComponent_div_13_div_1_Template, 19, 7, "div", 31);
    \u0275\u0275elementEnd();
  }
  if (rf & 2) {
    const ctx_r1 = \u0275\u0275nextContext();
    \u0275\u0275advance();
    \u0275\u0275property("ngForOf", ctx_r1.notifications)("ngForTrackBy", ctx_r1.trackByNotificationId);
  }
}
var NotificationsComponent = class _NotificationsComponent {
  notificationService;
  authService;
  notifications = [];
  unreadCount = 0;
  loading = true;
  subscriptions = new Subscription();
  constructor(notificationService, authService) {
    this.notificationService = notificationService;
    this.authService = authService;
  }
  ngOnInit() {
    this.subscriptions.add(this.notificationService.notifications$.subscribe((notifications) => {
      this.notifications = notifications;
      this.loading = false;
    }));
    this.subscriptions.add(this.notificationService.unreadCount$.subscribe((count) => {
      this.unreadCount = count;
    }));
    this.loadNotifications();
  }
  ngOnDestroy() {
    this.subscriptions.unsubscribe();
  }
  loadNotifications() {
    const currentUser = this.authService.getCurrentUser();
    if (currentUser?.id) {
      this.notificationService.getUserNotifications(currentUser.id).subscribe({
        error: () => this.loading = false
      });
    } else {
      this.loading = false;
    }
  }
  markAsRead(notification) {
    this.notificationService.markAsRead(notification.id).subscribe();
  }
  markAllAsRead() {
    const unreadNotifications = this.notifications.filter((n) => !n.estLue);
    unreadNotifications.forEach((notification) => {
      this.notificationService.markAsRead(notification.id).subscribe();
    });
  }
  deleteNotification(notificationId) {
    this.notificationService.deleteNotification(notificationId).subscribe();
  }
  trackByNotificationId(index, notification) {
    return notification.id;
  }
  formatDate(date) {
    const now = /* @__PURE__ */ new Date();
    const notifDate = new Date(date);
    const diffInMinutes = Math.floor((now.getTime() - notifDate.getTime()) / (1e3 * 60));
    if (diffInMinutes < 1)
      return "\xC0 l'instant";
    if (diffInMinutes < 60)
      return `Il y a ${diffInMinutes} minute${diffInMinutes > 1 ? "s" : ""}`;
    const diffInHours = Math.floor(diffInMinutes / 60);
    if (diffInHours < 24)
      return `Il y a ${diffInHours} heure${diffInHours > 1 ? "s" : ""}`;
    const diffInDays = Math.floor(diffInHours / 24);
    if (diffInDays < 7)
      return `Il y a ${diffInDays} jour${diffInDays > 1 ? "s" : ""}`;
    if (diffInDays < 30) {
      const diffInWeeks = Math.floor(diffInDays / 7);
      return `Il y a ${diffInWeeks} semaine${diffInWeeks > 1 ? "s" : ""}`;
    }
    return notifDate.toLocaleDateString("fr-FR", {
      day: "numeric",
      month: "long",
      year: "numeric"
    });
  }
  static \u0275fac = function NotificationsComponent_Factory(__ngFactoryType__) {
    return new (__ngFactoryType__ || _NotificationsComponent)(\u0275\u0275directiveInject(NotificationService), \u0275\u0275directiveInject(AuthService));
  };
  static \u0275cmp = /* @__PURE__ */ \u0275\u0275defineComponent({ type: _NotificationsComponent, selectors: [["app-notifications"]], decls: 14, vars: 5, consts: [[1, "notifications-container"], [1, "notifications-header"], ["width", "24", "height", "24", "viewBox", "0 0 24 24", "fill", "none", "stroke", "currentColor", "stroke-width", "2"], ["d", "M18 8A6 6 0 0 0 6 8c0 7-3 9-3 9h18s-3-2-3-9"], ["d", "M13.73 21a2 2 0 0 1-3.46 0"], [1, "header-actions"], ["class", "primary-btn", 3, "click", 4, "ngIf"], ["class", "notifications-stats", 4, "ngIf"], [1, "notifications-content"], ["class", "loading-container", 4, "ngIf"], ["class", "empty-state", 4, "ngIf"], ["class", "notifications-list", 4, "ngIf"], [1, "primary-btn", 3, "click"], ["width", "16", "height", "16", "viewBox", "0 0 24 24", "fill", "none", "stroke", "currentColor", "stroke-width", "2"], ["points", "9,11 12,14 22,4"], ["d", "M21,12v7a2,2 0 0,1 -2,2H5a2,2 0 0,1 -2,-2V5a2,2 0 0,1 2,-2h11"], [1, "notifications-stats"], [1, "stats-chips"], [1, "stat-chip"], ["class", "stat-chip warn", 4, "ngIf"], [1, "stat-chip", "warn"], ["cx", "12", "cy", "12", "r", "10"], [1, "loading-container"], [1, "spinner"], [1, "empty-state"], ["width", "64", "height", "64", "viewBox", "0 0 24 24", "fill", "none", "stroke", "currentColor", "stroke-width", "2", 1, "empty-icon"], ["d", "M20 21v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2"], ["cx", "12", "cy", "7", "r", "4"], [1, "empty-subtitle"], [1, "empty-list"], [1, "notifications-list"], ["class", "notification-card", 3, "unread", 4, "ngFor", "ngForOf", "ngForTrackBy"], [1, "notification-card"], [1, "notification-header"], [1, "notification-status"], ["class", "unread-indicator", "width", "12", "height", "12", "viewBox", "0 0 24 24", "fill", "currentColor", 4, "ngIf"], ["class", "read-indicator", "width", "16", "height", "16", "viewBox", "0 0 24 24", "fill", "none", "stroke", "currentColor", "stroke-width", "2", 4, "ngIf"], [1, "notification-content"], [1, "notification-actions"], ["class", "action-btn primary", "title", "Marquer comme lu", 3, "click", 4, "ngIf"], ["title", "Supprimer", 1, "action-btn", "delete", 3, "click"], ["points", "3,6 5,6 21,6"], ["d", "M19,6v14a2,2 0 0,1 -2,2H7a2,2 0 0,1 -2,-2V6m3,0V4a2,2 0 0,1 2,-2h4a2,2 0 0,1 2,2v2"], [1, "notification-meta"], [1, "notification-date"], ["width", "14", "height", "14", "viewBox", "0 0 24 24", "fill", "none", "stroke", "currentColor", "stroke-width", "2"], ["points", "12,6 12,12 16,14"], ["width", "12", "height", "12", "viewBox", "0 0 24 24", "fill", "currentColor", 1, "unread-indicator"], ["width", "16", "height", "16", "viewBox", "0 0 24 24", "fill", "none", "stroke", "currentColor", "stroke-width", "2", 1, "read-indicator"], ["d", "M22 11.08V12a10 10 0 1 1-5.93-9.14"], ["points", "22,4 12,14.01 9,11.01"], ["title", "Marquer comme lu", 1, "action-btn", "primary", 3, "click"], ["points", "20,6 9,17 4,12"]], template: function NotificationsComponent_Template(rf, ctx) {
    if (rf & 1) {
      \u0275\u0275elementStart(0, "div", 0)(1, "div", 1)(2, "h2");
      \u0275\u0275namespaceSVG();
      \u0275\u0275elementStart(3, "svg", 2);
      \u0275\u0275element(4, "path", 3)(5, "path", 4);
      \u0275\u0275elementEnd();
      \u0275\u0275text(6, " Mes Notifications ");
      \u0275\u0275elementEnd();
      \u0275\u0275namespaceHTML();
      \u0275\u0275elementStart(7, "div", 5);
      \u0275\u0275template(8, NotificationsComponent_button_8_Template, 5, 1, "button", 6);
      \u0275\u0275elementEnd()();
      \u0275\u0275template(9, NotificationsComponent_div_9_Template, 8, 2, "div", 7);
      \u0275\u0275elementStart(10, "div", 8);
      \u0275\u0275template(11, NotificationsComponent_div_11_Template, 4, 0, "div", 9)(12, NotificationsComponent_div_12_Template, 19, 0, "div", 10)(13, NotificationsComponent_div_13_Template, 2, 2, "div", 11);
      \u0275\u0275elementEnd()();
    }
    if (rf & 2) {
      \u0275\u0275advance(8);
      \u0275\u0275property("ngIf", ctx.unreadCount > 0);
      \u0275\u0275advance();
      \u0275\u0275property("ngIf", ctx.notifications.length > 0);
      \u0275\u0275advance(2);
      \u0275\u0275property("ngIf", ctx.loading);
      \u0275\u0275advance();
      \u0275\u0275property("ngIf", !ctx.loading && ctx.notifications.length === 0);
      \u0275\u0275advance();
      \u0275\u0275property("ngIf", !ctx.loading && ctx.notifications.length > 0);
    }
  }, dependencies: [CommonModule, NgForOf, NgIf], styles: ["\n\n.notifications-container[_ngcontent-%COMP%] {\n  max-width: 900px;\n  margin: 0 auto;\n  padding: 24px;\n}\n.notifications-header[_ngcontent-%COMP%] {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 24px;\n  padding: 20px;\n  background:\n    linear-gradient(\n      135deg,\n      #667eea 0%,\n      #764ba2 100%);\n  border-radius: 12px;\n  color: white;\n}\n.notifications-header[_ngcontent-%COMP%]   h2[_ngcontent-%COMP%] {\n  margin: 0;\n  display: flex;\n  align-items: center;\n  gap: 12px;\n  font-size: 24px;\n  font-weight: 500;\n}\n.primary-btn[_ngcontent-%COMP%] {\n  background: rgba(255, 255, 255, 0.2);\n  border: 1px solid rgba(255, 255, 255, 0.3);\n  color: white;\n  padding: 8px 16px;\n  border-radius: 6px;\n  cursor: pointer;\n  display: flex;\n  align-items: center;\n  gap: 8px;\n  font-size: 14px;\n  transition: all 0.2s;\n}\n.primary-btn[_ngcontent-%COMP%]:hover {\n  background: rgba(255, 255, 255, 0.3);\n}\n.notifications-stats[_ngcontent-%COMP%] {\n  margin-bottom: 24px;\n}\n.stats-chips[_ngcontent-%COMP%] {\n  display: flex;\n  gap: 12px;\n  flex-wrap: wrap;\n}\n.stat-chip[_ngcontent-%COMP%] {\n  display: flex;\n  align-items: center;\n  gap: 8px;\n  padding: 8px 16px;\n  background: #f5f5f5;\n  border-radius: 20px;\n  font-size: 14px;\n  color: #333;\n}\n.stat-chip.warn[_ngcontent-%COMP%] {\n  background: #ffebee;\n  color: #c62828;\n}\n.loading-container[_ngcontent-%COMP%] {\n  text-align: center;\n  padding: 48px;\n}\n.spinner[_ngcontent-%COMP%] {\n  width: 40px;\n  height: 40px;\n  border: 4px solid #f3f3f3;\n  border-top: 4px solid #667eea;\n  border-radius: 50%;\n  animation: _ngcontent-%COMP%_spin 1s linear infinite;\n  margin: 0 auto 16px;\n}\n@keyframes _ngcontent-%COMP%_spin {\n  0% {\n    transform: rotate(0deg);\n  }\n  100% {\n    transform: rotate(360deg);\n  }\n}\n.empty-state[_ngcontent-%COMP%] {\n  text-align: center;\n  padding: 48px;\n  color: #666;\n}\n.empty-icon[_ngcontent-%COMP%] {\n  margin-bottom: 16px;\n  color: #ccc;\n}\n.empty-subtitle[_ngcontent-%COMP%] {\n  margin-top: 24px;\n  font-weight: 500;\n  color: #333;\n}\n.empty-list[_ngcontent-%COMP%] {\n  text-align: left;\n  display: inline-block;\n  margin-top: 16px;\n  list-style: none;\n  padding: 0;\n}\n.empty-list[_ngcontent-%COMP%]   li[_ngcontent-%COMP%] {\n  margin-bottom: 8px;\n  color: #666;\n}\n.notifications-list[_ngcontent-%COMP%] {\n  display: flex;\n  flex-direction: column;\n  gap: 16px;\n}\n.notification-card[_ngcontent-%COMP%] {\n  background: white;\n  border-radius: 12px;\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\n  transition: all 0.3s ease;\n  border-left: 4px solid transparent;\n  overflow: hidden;\n}\n.notification-card.unread[_ngcontent-%COMP%] {\n  border-left-color: #2196f3;\n  background-color: #f8fbff;\n  box-shadow: 0 2px 8px rgba(33, 150, 243, 0.1);\n}\n.notification-card[_ngcontent-%COMP%]:hover {\n  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.12);\n  transform: translateY(-2px);\n}\n.notification-header[_ngcontent-%COMP%] {\n  display: flex;\n  align-items: flex-start;\n  gap: 12px;\n  padding: 16px;\n}\n.notification-status[_ngcontent-%COMP%] {\n  flex-shrink: 0;\n  padding-top: 4px;\n}\n.unread-indicator[_ngcontent-%COMP%] {\n  color: #2196f3;\n}\n.read-indicator[_ngcontent-%COMP%] {\n  color: #4caf50;\n}\n.notification-content[_ngcontent-%COMP%] {\n  flex: 1;\n  font-size: 16px;\n  line-height: 1.5;\n  margin: 0;\n  color: #333;\n}\n.notification-actions[_ngcontent-%COMP%] {\n  display: flex;\n  gap: 4px;\n  flex-shrink: 0;\n}\n.action-btn[_ngcontent-%COMP%] {\n  background: none;\n  border: none;\n  cursor: pointer;\n  padding: 8px;\n  border-radius: 4px;\n  transition: all 0.2s;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n}\n.action-btn.primary[_ngcontent-%COMP%] {\n  color: #2196f3;\n}\n.action-btn.primary[_ngcontent-%COMP%]:hover {\n  background: rgba(33, 150, 243, 0.1);\n}\n.action-btn.delete[_ngcontent-%COMP%] {\n  color: #f44336;\n}\n.action-btn.delete[_ngcontent-%COMP%]:hover {\n  background: rgba(244, 67, 54, 0.1);\n}\n.notification-meta[_ngcontent-%COMP%] {\n  display: flex;\n  align-items: center;\n  gap: 16px;\n  margin-top: 8px;\n  padding: 0 16px 16px;\n}\n.notification-date[_ngcontent-%COMP%] {\n  display: flex;\n  align-items: center;\n  gap: 4px;\n  font-size: 14px;\n  color: #666;\n}\n@media (max-width: 768px) {\n  .notifications-container[_ngcontent-%COMP%] {\n    padding: 16px;\n  }\n  .notifications-header[_ngcontent-%COMP%] {\n    flex-direction: column;\n    align-items: flex-start;\n    gap: 16px;\n    text-align: center;\n  }\n  .notifications-header[_ngcontent-%COMP%]   h2[_ngcontent-%COMP%] {\n    font-size: 20px;\n  }\n  .notification-header[_ngcontent-%COMP%] {\n    flex-direction: column;\n    align-items: flex-start;\n    gap: 8px;\n  }\n  .notification-actions[_ngcontent-%COMP%] {\n    align-self: flex-end;\n  }\n  .stats-chips[_ngcontent-%COMP%] {\n    justify-content: center;\n  }\n}\n/*# sourceMappingURL=notifications.component.css.map */"] });
};
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && setClassMetadata(NotificationsComponent, [{
    type: Component,
    args: [{ selector: "app-notifications", standalone: true, imports: [CommonModule], template: `
    <div class="notifications-container">
      <div class="notifications-header">
        <h2>
          <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
            <path d="M18 8A6 6 0 0 0 6 8c0 7-3 9-3 9h18s-3-2-3-9"></path>
            <path d="M13.73 21a2 2 0 0 1-3.46 0"></path>
          </svg>
          Mes Notifications
        </h2>
        <div class="header-actions">
          <button
            class="primary-btn"
            *ngIf="unreadCount > 0"
            (click)="markAllAsRead()">
            <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
              <polyline points="9,11 12,14 22,4"></polyline>
              <path d="M21,12v7a2,2 0 0,1 -2,2H5a2,2 0 0,1 -2,-2V5a2,2 0 0,1 2,-2h11"></path>
            </svg>
            Tout marquer comme lu ({{ unreadCount }})
          </button>
        </div>
      </div>

      <div class="notifications-stats" *ngIf="notifications.length > 0">
        <div class="stats-chips">
          <div class="stat-chip">
            <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
              <path d="M18 8A6 6 0 0 0 6 8c0 7-3 9-3 9h18s-3-2-3-9"></path>
              <path d="M13.73 21a2 2 0 0 1-3.46 0"></path>
            </svg>
            {{ notifications.length }} notification(s)
          </div>
          <div *ngIf="unreadCount > 0" class="stat-chip warn">
            <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
              <circle cx="12" cy="12" r="10"></circle>
            </svg>
            {{ unreadCount }} non lue(s)
          </div>
        </div>
      </div>

      <div class="notifications-content">
        <div *ngIf="loading" class="loading-container">
          <div class="spinner"></div>
          <p>Chargement des notifications...</p>
        </div>

        <div *ngIf="!loading && notifications.length === 0" class="empty-state">
          <svg class="empty-icon" width="64" height="64" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
            <path d="M20 21v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2"></path>
            <circle cx="12" cy="7" r="4"></circle>
          </svg>
          <h3>Aucune notification</h3>
          <p>Vous n'avez aucune notification pour le moment.</p>
          <p class="empty-subtitle">Les notifications appara\xEEtront ici lorsque :</p>
          <ul class="empty-list">
            <li>\u2022 Un client laisse un avis sur vos produits</li>
            <li>\u2022 Une nouvelle commande est pass\xE9e</li>
            <li>\u2022 Un paiement est confirm\xE9</li>
            <li>\u2022 Des mises \xE0 jour importantes sont disponibles</li>
          </ul>
        </div>

        <div *ngIf="!loading && notifications.length > 0" class="notifications-list">
          <div
            *ngFor="let notification of notifications; trackBy: trackByNotificationId"
            class="notification-card"
            [class.unread]="!notification.estLue">

            <div class="notification-header">
              <div class="notification-status">
                <svg
                  *ngIf="!notification.estLue"
                  class="unread-indicator"
                  width="12" height="12" viewBox="0 0 24 24" fill="currentColor">
                  <circle cx="12" cy="12" r="10"></circle>
                </svg>
                <svg
                  *ngIf="notification.estLue"
                  class="read-indicator"
                  width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                  <path d="M22 11.08V12a10 10 0 1 1-5.93-9.14"></path>
                  <polyline points="22,4 12,14.01 9,11.01"></polyline>
                </svg>
              </div>

              <div class="notification-content">
                {{ notification.contenu }}
              </div>

              <div class="notification-actions">
                <button
                  *ngIf="!notification.estLue"
                  (click)="markAsRead(notification)"
                  class="action-btn primary"
                  title="Marquer comme lu">
                  <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                    <polyline points="20,6 9,17 4,12"></polyline>
                  </svg>
                </button>

                <button
                  (click)="deleteNotification(notification.id)"
                  class="action-btn delete"
                  title="Supprimer">
                  <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                    <polyline points="3,6 5,6 21,6"></polyline>
                    <path d="M19,6v14a2,2 0 0,1 -2,2H7a2,2 0 0,1 -2,-2V6m3,0V4a2,2 0 0,1 2,-2h4a2,2 0 0,1 2,2v2"></path>
                  </svg>
                </button>
              </div>
            </div>

            <div class="notification-meta">
              <span class="notification-date">
                <svg width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                  <circle cx="12" cy="12" r="10"></circle>
                  <polyline points="12,6 12,12 16,14"></polyline>
                </svg>
                {{ formatDate(notification.dateEnvoi) }}
              </span>
            </div>
          </div>
        </div>
      </div>
    </div>
  `, styles: ["/* angular:styles/component:css;d4877b2aa7799027be6eb862e21536d67532f77c47a5d94bea4ca1fd812fec43;C:/Users/<USER>/Desktop/STE/PFE/PFE/frontend-fournisseur/src/app/notifications/notifications.component.ts */\n.notifications-container {\n  max-width: 900px;\n  margin: 0 auto;\n  padding: 24px;\n}\n.notifications-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 24px;\n  padding: 20px;\n  background:\n    linear-gradient(\n      135deg,\n      #667eea 0%,\n      #764ba2 100%);\n  border-radius: 12px;\n  color: white;\n}\n.notifications-header h2 {\n  margin: 0;\n  display: flex;\n  align-items: center;\n  gap: 12px;\n  font-size: 24px;\n  font-weight: 500;\n}\n.primary-btn {\n  background: rgba(255, 255, 255, 0.2);\n  border: 1px solid rgba(255, 255, 255, 0.3);\n  color: white;\n  padding: 8px 16px;\n  border-radius: 6px;\n  cursor: pointer;\n  display: flex;\n  align-items: center;\n  gap: 8px;\n  font-size: 14px;\n  transition: all 0.2s;\n}\n.primary-btn:hover {\n  background: rgba(255, 255, 255, 0.3);\n}\n.notifications-stats {\n  margin-bottom: 24px;\n}\n.stats-chips {\n  display: flex;\n  gap: 12px;\n  flex-wrap: wrap;\n}\n.stat-chip {\n  display: flex;\n  align-items: center;\n  gap: 8px;\n  padding: 8px 16px;\n  background: #f5f5f5;\n  border-radius: 20px;\n  font-size: 14px;\n  color: #333;\n}\n.stat-chip.warn {\n  background: #ffebee;\n  color: #c62828;\n}\n.loading-container {\n  text-align: center;\n  padding: 48px;\n}\n.spinner {\n  width: 40px;\n  height: 40px;\n  border: 4px solid #f3f3f3;\n  border-top: 4px solid #667eea;\n  border-radius: 50%;\n  animation: spin 1s linear infinite;\n  margin: 0 auto 16px;\n}\n@keyframes spin {\n  0% {\n    transform: rotate(0deg);\n  }\n  100% {\n    transform: rotate(360deg);\n  }\n}\n.empty-state {\n  text-align: center;\n  padding: 48px;\n  color: #666;\n}\n.empty-icon {\n  margin-bottom: 16px;\n  color: #ccc;\n}\n.empty-subtitle {\n  margin-top: 24px;\n  font-weight: 500;\n  color: #333;\n}\n.empty-list {\n  text-align: left;\n  display: inline-block;\n  margin-top: 16px;\n  list-style: none;\n  padding: 0;\n}\n.empty-list li {\n  margin-bottom: 8px;\n  color: #666;\n}\n.notifications-list {\n  display: flex;\n  flex-direction: column;\n  gap: 16px;\n}\n.notification-card {\n  background: white;\n  border-radius: 12px;\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\n  transition: all 0.3s ease;\n  border-left: 4px solid transparent;\n  overflow: hidden;\n}\n.notification-card.unread {\n  border-left-color: #2196f3;\n  background-color: #f8fbff;\n  box-shadow: 0 2px 8px rgba(33, 150, 243, 0.1);\n}\n.notification-card:hover {\n  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.12);\n  transform: translateY(-2px);\n}\n.notification-header {\n  display: flex;\n  align-items: flex-start;\n  gap: 12px;\n  padding: 16px;\n}\n.notification-status {\n  flex-shrink: 0;\n  padding-top: 4px;\n}\n.unread-indicator {\n  color: #2196f3;\n}\n.read-indicator {\n  color: #4caf50;\n}\n.notification-content {\n  flex: 1;\n  font-size: 16px;\n  line-height: 1.5;\n  margin: 0;\n  color: #333;\n}\n.notification-actions {\n  display: flex;\n  gap: 4px;\n  flex-shrink: 0;\n}\n.action-btn {\n  background: none;\n  border: none;\n  cursor: pointer;\n  padding: 8px;\n  border-radius: 4px;\n  transition: all 0.2s;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n}\n.action-btn.primary {\n  color: #2196f3;\n}\n.action-btn.primary:hover {\n  background: rgba(33, 150, 243, 0.1);\n}\n.action-btn.delete {\n  color: #f44336;\n}\n.action-btn.delete:hover {\n  background: rgba(244, 67, 54, 0.1);\n}\n.notification-meta {\n  display: flex;\n  align-items: center;\n  gap: 16px;\n  margin-top: 8px;\n  padding: 0 16px 16px;\n}\n.notification-date {\n  display: flex;\n  align-items: center;\n  gap: 4px;\n  font-size: 14px;\n  color: #666;\n}\n@media (max-width: 768px) {\n  .notifications-container {\n    padding: 16px;\n  }\n  .notifications-header {\n    flex-direction: column;\n    align-items: flex-start;\n    gap: 16px;\n    text-align: center;\n  }\n  .notifications-header h2 {\n    font-size: 20px;\n  }\n  .notification-header {\n    flex-direction: column;\n    align-items: flex-start;\n    gap: 8px;\n  }\n  .notification-actions {\n    align-self: flex-end;\n  }\n  .stats-chips {\n    justify-content: center;\n  }\n}\n/*# sourceMappingURL=notifications.component.css.map */\n"] }]
  }], () => [{ type: NotificationService }, { type: AuthService }], null);
})();
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && \u0275setClassDebugInfo(NotificationsComponent, { className: "NotificationsComponent", filePath: "src/app/notifications/notifications.component.ts", lineNumber: 396 });
})();
export {
  NotificationsComponent
};
//# sourceMappingURL=chunk-PFTXOXVW.js.map

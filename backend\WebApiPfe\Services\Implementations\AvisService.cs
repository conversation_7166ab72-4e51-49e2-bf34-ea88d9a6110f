using Microsoft.EntityFrameworkCore;
using WebApiPfe.Models.Entity;
using WebApiPfe.Models.Enum;
using WebApiPfe.Services.Interfaces;
using WebApiPfe.DTOs.ReadDTOs;
using WebApiPfe.DTOs.CreateDTOs;
using WebApiPfe.DTOs.Admin;
using System.ComponentModel.DataAnnotations;

namespace WebApiPfe.Services.Implementations
{
    public class AvisService : IAvisService
    {
        private readonly AppDbContext _context;
        private readonly ILogger<AvisService> _logger;
        private readonly IProduitService _produitService;
        private readonly IClientService _clientService;
        private readonly IServiceProvider _serviceProvider;

        public AvisService(
            AppDbContext context,
            ILogger<AvisService> logger,
            IProduitService produitService,
            IClientService clientService,
            IServiceProvider serviceProvider)
        {
            _context = context;
            _logger = logger;
            _produitService = produitService;
            _clientService = clientService;
            _serviceProvider = serviceProvider;
        }

        public async Task<AvisDto> AjouterAvisAsync(AvisCreateDto dto, int clientId)
        {
            if (dto == null)
                throw new ArgumentNullException(nameof(dto));

            if (!await _produitService.ExistsAsync(dto.ProduitId))
                throw new KeyNotFoundException("Produit introuvable");

            if (!await _clientService.ExistsAsync(clientId))
                throw new KeyNotFoundException("Client introuvable");

            // Vérifier si le client a déjà un avis pour ce produit
            var avisExistant = await _context.Avis
                .FirstOrDefaultAsync(a => a.ClientId == clientId && a.ProduitId == dto.ProduitId);

            if (avisExistant != null)
            {
                // Mettre à jour l'avis existant
                avisExistant.Note = dto.Note;
                avisExistant.Commentaire = dto.Commentaire ?? string.Empty;
                avisExistant.DatePublication = DateTime.UtcNow;

                _context.Avis.Update(avisExistant);
                await _context.SaveChangesAsync();

                // Lancer la mise à jour de la note en arrière-plan avec un nouveau scope
                _ = Task.Run(async () =>
                {
                    using var scope = _serviceProvider.CreateScope();
                    var scopedContext = scope.ServiceProvider.GetRequiredService<AppDbContext>();
                    await UpdateProduitNoteAsync(dto.ProduitId, scopedContext);
                });

                // Récupérer l'avis mis à jour avec ses relations
                var avisMisAJour = await _context.Avis
                    .Include(a => a.Produit)
                    .Include(a => a.Client)
                    .FirstOrDefaultAsync(a => a.Id == avisExistant.Id);

                return avisMisAJour != null ? MapToDto(avisMisAJour) : null;
            }

            // Créer un nouvel avis si aucun n'existe
            var avis = new Avis
            {
                ProduitId = dto.ProduitId,
                ClientId = clientId,
                Note = dto.Note,
                Commentaire = dto.Commentaire,
                DatePublication = DateTime.UtcNow
            };

            try
            {
                await _context.Avis.AddAsync(avis);
                await _context.SaveChangesAsync();

                // Lancer la mise à jour de la note en arrière-plan avec un nouveau scope
                _ = Task.Run(async () =>
                {
                    using var scope = _serviceProvider.CreateScope();
                    var scopedContext = scope.ServiceProvider.GetRequiredService<AppDbContext>();
                    await UpdateProduitNoteAsync(avis.ProduitId, scopedContext);
                });

                // Récupérer l'avis créé avec ses relations
                var avisCreated = await _context.Avis
                    .Include(a => a.Produit)
                    .Include(a => a.Client)
                    .FirstOrDefaultAsync(a => a.Id == avis.Id && a.ProduitId == avis.ProduitId);

                return avisCreated != null ? MapToDto(avisCreated) : null;
            }
            catch (DbUpdateException ex)
            {
                _logger.LogError(ex, "Erreur lors de l'ajout d'un avis");
                throw new Exception("Erreur lors de la création de l'avis", ex);
            }
        }

        public async Task<AvisDto?> GetByIdAsync(int produitId, int id)
        {
            var avis = await _context.Avis
                .Include(a => a.Produit)
                .Include(a => a.Client)
                .FirstOrDefaultAsync(a => a.Id == id && a.ProduitId == produitId);

            if (avis == null)
                return null;

            return MapToDto(avis);
        }



        public async Task<int> GetNombreAvisAsync(int produitId)
        {
            return await _context.Avis
                .CountAsync(a => a.ProduitId == produitId);
        }

        private async Task<int> GetNombreAvisAsync(int produitId, AppDbContext context)
        {
            return await context.Avis
                .CountAsync(a => a.ProduitId == produitId);
        }

        public async Task<double> GetNoteMoyenneAsync(int produitId)
        {
            var moyenne = await _context.Avis
                .Where(a => a.ProduitId == produitId)
                .AverageAsync(a => (double?)a.Note) ?? 0;

            return Math.Round(moyenne, 1);
        }

        private async Task<double> GetNoteMoyenneAsync(int produitId, AppDbContext context)
        {
            var moyenne = await context.Avis
                .Where(a => a.ProduitId == produitId)
                .AverageAsync(a => (double?)a.Note) ?? 0;

            return Math.Round(moyenne, 2);
        }

        private async Task UpdateProduitNoteAsync(int produitId, AppDbContext context)
        {
            try
            {
                var produit = await context.Produits.FindAsync(produitId);
                if (produit != null)
                {
                    produit.NoteMoyenne = await GetNoteMoyenneAsync(produitId, context);
                    produit.NombreAvis = await GetNombreAvisAsync(produitId, context);
                    await context.SaveChangesAsync();
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Erreur mise à jour note produit ID {produitId}");
            }
        }
        public async Task<List<AvisDto>> GetAvisByProduitAsync(int produitId)
        {
            var avisList = await _context.Avis
                .Where(a => a.ProduitId == produitId)
                .Include(a => a.Client)
                .ToListAsync();

            return avisList.Select(MapToDto).ToList();
        }

        public async Task<AvisDto?> GetAvisClientProduitAsync(int clientId, int produitId)
        {
            var avis = await _context.Avis
                .Where(a => a.ClientId == clientId && a.ProduitId == produitId)
                .Include(a => a.Client)
                .FirstOrDefaultAsync();

            return avis != null ? MapToDto(avis) : null;
        }

        private AvisDto MapToDto(Avis avis)
        {
            return new AvisDto
            {
                Id = avis.Id,
                Note = avis.Note,
                Commentaire = avis.Commentaire,
                DatePublication = avis.DatePublication,
                ClientNom = $"{avis.Client?.Nom} {avis.Client?.Prenom}",
                ProduitId = avis.ProduitId,
                ProduitNom = avis.Produit?.Nom
            };
        }

        // Nouvelles méthodes pour la modération

        public async Task<List<AvisModerationDto>> GetAvisForModerationAsync(AvisFilterDto filter)
        {
            var query = _context.Avis
                .Include(a => a.Client)
                .Include(a => a.Produit)
                    .ThenInclude(p => p.Fournisseur)
                .Include(a => a.Moderateur)
                .AsQueryable();

            // Appliquer les filtres
            if (filter.Statut.HasValue)
                query = query.Where(a => a.Statut == filter.Statut.Value);

            if (!string.IsNullOrEmpty(filter.FournisseurId))
            {
                if (int.TryParse(filter.FournisseurId, out int fournisseurIdInt))
                {
                    query = query.Where(a => a.Produit.FournisseurId == fournisseurIdInt);
                }
            }

            if (filter.ProduitId.HasValue)
                query = query.Where(a => a.ProduitId == filter.ProduitId.Value);

            if (filter.DateDebut.HasValue)
                query = query.Where(a => a.DatePublication >= filter.DateDebut.Value);

            if (filter.DateFin.HasValue)
                query = query.Where(a => a.DatePublication <= filter.DateFin.Value);

            if (!string.IsNullOrEmpty(filter.Recherche))
            {
                query = query.Where(a => a.Commentaire.Contains(filter.Recherche) ||
                                        a.Produit.Nom.Contains(filter.Recherche) ||
                                        a.Client.Nom.Contains(filter.Recherche) ||
                                        a.Client.Prenom.Contains(filter.Recherche));
            }

            // Tri
            query = filter.SortBy.ToLower() switch
            {
                "note" => filter.SortDesc ? query.OrderByDescending(a => a.Note) : query.OrderBy(a => a.Note),
                "statut" => filter.SortDesc ? query.OrderByDescending(a => a.Statut) : query.OrderBy(a => a.Statut),
                "client" => filter.SortDesc ? query.OrderByDescending(a => a.Client.Nom) : query.OrderBy(a => a.Client.Nom),
                "produit" => filter.SortDesc ? query.OrderByDescending(a => a.Produit.Nom) : query.OrderBy(a => a.Produit.Nom),
                _ => filter.SortDesc ? query.OrderByDescending(a => a.DatePublication) : query.OrderBy(a => a.DatePublication)
            };

            // Pagination
            var avis = await query
                .Skip((filter.Page - 1) * filter.PageSize)
                .Take(filter.PageSize)
                .ToListAsync();

            return avis.Select(MapToModerationDto).ToList();
        }

        public async Task<AvisModerationDto?> GetAvisModerationAsync(int id)
        {
            var avis = await _context.Avis
                .Include(a => a.Client)
                .Include(a => a.Produit)
                    .ThenInclude(p => p.Fournisseur)
                .Include(a => a.Moderateur)
                .FirstOrDefaultAsync(a => a.Id == id);

            return avis != null ? MapToModerationDto(avis) : null;
        }

        public async Task<AvisModerationDto> ModererAvisAsync(int id, ModererAvisDto dto, int moderateurId)
        {
            var avis = await _context.Avis
                .Include(a => a.Client)
                .Include(a => a.Produit)
                    .ThenInclude(p => p.Fournisseur)
                .FirstOrDefaultAsync(a => a.Id == id);

            if (avis == null)
                throw new KeyNotFoundException("Avis non trouvé");

            avis.Statut = dto.Statut;
            avis.CommentaireModeration = dto.CommentaireModeration;
            avis.DateModeration = DateTime.UtcNow;
            avis.ModerePar = moderateurId;

            await _context.SaveChangesAsync();

            return MapToModerationDto(avis);
        }

        public async Task<AvisModerationDto> SupprimerCommentaireAsync(int id, int moderateurId, string? raisonSuppression = null)
        {
            var avis = await _context.Avis
                .Include(a => a.Client)
                .Include(a => a.Produit)
                    .ThenInclude(p => p.Fournisseur)
                .FirstOrDefaultAsync(a => a.Id == id);

            if (avis == null)
                throw new KeyNotFoundException("Avis non trouvé");

            // Sauvegarder le commentaire original dans CommentaireModeration pour pouvoir le restaurer
            var commentaireOriginal = avis.Commentaire;

            // Remplacer le commentaire par un message de suppression
            avis.Commentaire = "[Commentaire supprimé par l'administrateur]";
            avis.CommentaireSupprime = true;
            avis.Statut = StatutAvis.CommentaireSupprime;
            avis.DateModeration = DateTime.UtcNow;
            avis.ModerePar = moderateurId;

            // Stocker le commentaire original et la raison dans CommentaireModeration
            // Format: "ORIGINAL:{commentaire_original}|RAISON:{raison}"
            avis.CommentaireModeration = $"ORIGINAL:{commentaireOriginal}|RAISON:{raisonSuppression ?? "Commentaire supprimé par l'administrateur"}";

            // Créer une notification pour le client
            if (avis.Client != null)
            {
                var notification = new Notification
                {
                    UtilisateurId = avis.Client.Id,
                    Titre = "Commentaire d'avis supprimé",
                    Message = $"Votre avis était supprimé à cause de '{raisonSuppression ?? "contenu inapproprié"}' mais ne t'inquiète pas, votre note est mise en considération.",
                    DateEnvoi = DateTime.UtcNow,
                    EstLue = false,
                    Type = TypeNotification.General
                };

                _context.Notifications.Add(notification);
            }

            await _context.SaveChangesAsync();

            return MapToModerationDto(avis);
        }

        public async Task<AvisModerationDto> RestaurerCommentaireAsync(int id, int moderateurId)
        {
            var avis = await _context.Avis
                .Include(a => a.Client)
                .Include(a => a.Produit)
                    .ThenInclude(p => p.Fournisseur)
                .FirstOrDefaultAsync(a => a.Id == id);

            if (avis == null)
                throw new KeyNotFoundException("Avis non trouvé");

            if (avis.Statut != StatutAvis.CommentaireSupprime)
                throw new InvalidOperationException("Cet avis n'a pas de commentaire supprimé à restaurer");

            if (string.IsNullOrEmpty(avis.CommentaireModeration))
                throw new InvalidOperationException("Aucun commentaire original trouvé pour la restauration");

            // Extraire le commentaire original du format "ORIGINAL:{commentaire}|RAISON:{raison}"
            var commentaireOriginal = "";
            if (avis.CommentaireModeration.StartsWith("ORIGINAL:"))
            {
                var parts = avis.CommentaireModeration.Split("|RAISON:");
                if (parts.Length > 0)
                {
                    commentaireOriginal = parts[0].Substring("ORIGINAL:".Length);
                }
            }

            if (string.IsNullOrEmpty(commentaireOriginal))
                throw new InvalidOperationException("Impossible d'extraire le commentaire original");

            // Restaurer le commentaire original
            avis.Commentaire = commentaireOriginal;
            avis.Statut = StatutAvis.Publie;
            avis.DateModeration = DateTime.UtcNow;
            avis.ModerePar = moderateurId;
            avis.CommentaireSupprime = false;
            avis.CommentaireModeration = null; // Effacer la sauvegarde

            await _context.SaveChangesAsync();

            return MapToModerationDto(avis);
        }

        public async Task<bool> SupprimerAvisAsync(int id)
        {
            var avis = await _context.Avis.FindAsync(id);
            if (avis == null)
            {
                return false;
            }

            _context.Avis.Remove(avis);
            await _context.SaveChangesAsync();

            return true;
        }

        public async Task<List<AvisModerationDto>> GetAvisFournisseurAsync(int fournisseurId, AvisFilterDto filter)
        {
            filter.FournisseurId = fournisseurId.ToString();
            return await GetAvisForModerationAsync(filter);
        }

        public async Task<AvisStatsDto> GetAvisStatsAsync()
        {
            var stats = new AvisStatsDto();

            var avis = await _context.Avis.ToListAsync();

            stats.TotalAvis = avis.Count;
            stats.AvisPublies = avis.Count(a => a.Statut == StatutAvis.Publie);
            stats.AvisCommentaireSupprime = avis.Count(a => a.Statut == StatutAvis.CommentaireSupprime);
            stats.AvisSignales = avis.Count(a => a.Statut == StatutAvis.Signale);

            // Calcul de la moyenne sur tous les avis (la note persiste toujours)
            if (avis.Any())
            {
                stats.NoteMoyenneGlobale = avis.Average(a => a.Note);
            }
            else
            {
                stats.NoteMoyenneGlobale = 0;
            }

            // Statistiques par note (toutes les notes comptent)
            for (int i = 1; i <= 5; i++)
            {
                stats.AvisParNote[i] = avis.Count(a => a.Note == i);
            }

            // Avis récents
            var recentFilter = new AvisFilterDto { Page = 1, PageSize = 5 };
            stats.AvisRecents = await GetAvisForModerationAsync(recentFilter);

            return stats;
        }

        public async Task<AvisStatsDto> GetAvisStatsFournisseurAsync(int fournisseurId)
        {
            var stats = new AvisStatsDto();

            var avis = await _context.Avis
                .Where(a => a.Produit.FournisseurId == fournisseurId)
                .ToListAsync();

            stats.TotalAvis = avis.Count;
            // Ces propriétés n'existent plus dans le nouveau système
            // stats.AvisEnAttente = avis.Count(a => a.Statut == StatutAvis.EnAttente);
            // stats.AvisApprouves = avis.Count(a => a.Statut == StatutAvis.Approuve);
            // stats.AvisRejetes = avis.Count(a => a.Statut == StatutAvis.Rejete);
            stats.AvisSignales = avis.Count(a => a.Statut == StatutAvis.Signale);

            if (stats.TotalAvis > 0)
            {
                // Toutes les notes comptent maintenant
                stats.NoteMoyenneGlobale = avis.Average(a => a.Note);
            }

            // Statistiques par note
            for (int i = 1; i <= 5; i++)
            {
                stats.AvisParNote[i] = avis.Count(a => a.Note == i);
            }

            // Avis récents du fournisseur
            var recentFilter = new AvisFilterDto { FournisseurId = fournisseurId.ToString(), Page = 1, PageSize = 5 };
            stats.AvisRecents = await GetAvisForModerationAsync(recentFilter);

            return stats;
        }

        public async Task<AvisModerationDto> RepondreAvisAsync(int id, string reponse, int fournisseurId)
        {
            var avis = await _context.Avis
                .Include(a => a.Client)
                .Include(a => a.Produit)
                    .ThenInclude(p => p.Fournisseur)
                .FirstOrDefaultAsync(a => a.Id == id && a.Produit.FournisseurId == fournisseurId);

            if (avis == null)
                throw new KeyNotFoundException("Avis non trouvé ou vous n'avez pas l'autorisation");

            avis.CommentaireModeration = reponse;
            avis.DateModeration = DateTime.UtcNow;
            avis.ModerePar = fournisseurId;

            await _context.SaveChangesAsync();

            return MapToModerationDto(avis);
        }

        public async Task<AvisStatsDto> GetAvisStatsFournisseurAsync(string fournisseurId)
        {
            var stats = new AvisStatsDto();

            // Convertir l'ID string en int pour la requête
            if (!int.TryParse(fournisseurId, out int fournisseurIdInt))
            {
                throw new ArgumentException("ID fournisseur invalide", nameof(fournisseurId));
            }

            var avis = await _context.Avis
                .Where(a => a.Produit.FournisseurId == fournisseurIdInt)
                .ToListAsync();

            stats.TotalAvis = avis.Count;
            stats.AvisPublies = avis.Count(a => a.Statut == StatutAvis.Publie);
            stats.AvisCommentaireSupprime = avis.Count(a => a.Statut == StatutAvis.CommentaireSupprime);
            stats.AvisSignales = avis.Count(a => a.Statut == StatutAvis.Signale);
            stats.NoteMoyenneGlobale = avis.Any() ? avis.Average(a => a.Note) : 0;

            // Répartition par note
            for (int i = 1; i <= 5; i++)
            {
                stats.AvisParNote[i] = avis.Count(a => a.Note == i);
            }

            // Avis récents (5 derniers)
            var recentFilter = new AvisFilterDto
            {
                FournisseurId = fournisseurId,
                Page = 1,
                PageSize = 5,
                SortBy = "DatePublication",
                SortDesc = true
            };
            stats.AvisRecents = await GetAvisForModerationAsync(recentFilter);

            return stats;
        }

        public async Task<AvisModerationDto> SignalerAvisAsync(int id, string fournisseurId, string raisonSignalement)
        {
            // Convertir l'ID string en int pour la requête
            if (!int.TryParse(fournisseurId, out int fournisseurIdInt))
            {
                throw new ArgumentException("ID fournisseur invalide", nameof(fournisseurId));
            }

            var avis = await _context.Avis
                .Include(a => a.Client)
                .Include(a => a.Produit)
                    .ThenInclude(p => p.Fournisseur)
                .FirstOrDefaultAsync(a => a.Id == id && a.Produit.FournisseurId == fournisseurIdInt);

            if (avis == null)
                throw new KeyNotFoundException("Avis non trouvé ou vous n'avez pas l'autorisation de le signaler");

            if (avis.Statut == StatutAvis.Signale)
                throw new InvalidOperationException("Cet avis a déjà été signalé");

            // Marquer l'avis comme signalé
            avis.Statut = StatutAvis.Signale;
            avis.DateModeration = DateTime.UtcNow;
            avis.ModerePar = fournisseurIdInt;
            avis.CommentaireModeration = $"Signalé par le fournisseur: {raisonSignalement}";

            await _context.SaveChangesAsync();

            _logger.LogInformation("Avis {AvisId} signalé par le fournisseur {FournisseurId} avec la raison: {Raison}",
                id, fournisseurId, raisonSignalement);

            return MapToModerationDto(avis);
        }

        public async Task<AvisModerationDto> SignalerAvisAsync(int id, int fournisseurId, string raisonSignalement, string? detailsSignalement = null)
        {
            var avis = await _context.Avis
                .Include(a => a.Client)
                .Include(a => a.Produit)
                    .ThenInclude(p => p.Fournisseur)
                .FirstOrDefaultAsync(a => a.Id == id && a.Produit.FournisseurId == fournisseurId);

            if (avis == null)
                throw new KeyNotFoundException("Avis non trouvé ou vous n'avez pas l'autorisation de le signaler");

            if (avis.Statut == StatutAvis.Signale)
                throw new InvalidOperationException("Cet avis a déjà été signalé");

            // Marquer l'avis comme signalé
            avis.Statut = StatutAvis.Signale;
            avis.DateModeration = DateTime.UtcNow;
            avis.ModerePar = fournisseurId;

            // Construire le commentaire de modération avec les détails
            var commentaireModeration = $"Signalé par le fournisseur: {raisonSignalement}";
            if (!string.IsNullOrWhiteSpace(detailsSignalement))
            {
                commentaireModeration += $"\nDétails: {detailsSignalement}";
            }
            avis.CommentaireModeration = commentaireModeration;

            await _context.SaveChangesAsync();

            // Envoyer une notification à l'admin
            await EnvoyerNotificationSignalementAsync(avis, fournisseurId, raisonSignalement, detailsSignalement);

            _logger.LogInformation("🚨 Avis {AvisId} signalé par le fournisseur {FournisseurId} avec la raison: {Raison}",
                id, fournisseurId, raisonSignalement);

            return MapToModerationDto(avis);
        }

        private async Task EnvoyerNotificationSignalementAsync(Avis avis, int fournisseurId, string raisonSignalement, string? detailsSignalement)
        {
            try
            {
                // Récupérer les informations du fournisseur
                var fournisseur = await _context.Fournisseurs
                    .FirstOrDefaultAsync(f => f.Id == fournisseurId);

                if (fournisseur == null) return;

                // Récupérer tous les admins
                var admins = await _context.Users
                    .Where(u => u.Role == "Admin")
                    .ToListAsync();

                foreach (var admin in admins)
                {
                    // Créer une notification pour chaque admin
                    var notification = new Notification
                    {
                        UserId = admin.Id,
                        Type = "SignalementAvis",
                        Titre = "🚨 Nouvel avis signalé par un fournisseur",
                        Message = $"Le fournisseur {fournisseur.RaisonSociale} a signalé un avis.\n" +
                                 $"Produit: {avis.Produit.Nom}\n" +
                                 $"Client: {avis.Client.Prenom} {avis.Client.Nom}\n" +
                                 $"Raison: {raisonSignalement}" +
                                 (!string.IsNullOrWhiteSpace(detailsSignalement) ? $"\nDétails: {detailsSignalement}" : ""),
                        DateCreation = DateTime.UtcNow,
                        EstLue = false,
                        Url = $"/admin/avis-moderation?avisId={avis.Id}"
                    };

                    _context.Notifications.Add(notification);
                }

                await _context.SaveChangesAsync();

                _logger.LogInformation("✅ Notifications de signalement envoyées à {Count} administrateurs", admins.Count);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "❌ Erreur lors de l'envoi des notifications de signalement");
                // Ne pas faire échouer le signalement si l'envoi de notification échoue
            }
        }

        private AvisModerationDto MapToModerationDto(Avis avis)
        {
            return new AvisModerationDto
            {
                Id = avis.Id,
                Note = avis.Note,
                // Transmettre le commentaire original, la logique d'affichage sera gérée côté frontend
                Commentaire = avis.Commentaire,
                DatePublication = avis.DatePublication,
                Statut = avis.Statut,
                StatutLibelle = GetStatutLibelle(avis.Statut),
                CommentaireSupprime = avis.CommentaireSupprime,
                DateModeration = avis.DateModeration,
                CommentaireModeration = avis.CommentaireModeration,
                NomModerateur = avis.Moderateur != null ? $"{avis.Moderateur.Prenom} {avis.Moderateur.Nom}" : null,

                ClientId = avis.ClientId,
                ClientNom = avis.Client?.Nom ?? "",
                ClientPrenom = avis.Client?.Prenom ?? "",
                ClientEmail = avis.Client?.Email ?? "",

                ProduitId = avis.ProduitId,
                ProduitNom = avis.Produit?.Nom ?? "",
                ProduitReference = avis.Produit?.ReferenceFournisseur ?? "",

                FournisseurId = avis.Produit?.FournisseurId ?? 0,
                FournisseurNom = avis.Produit?.Fournisseur != null ? $"{avis.Produit.Fournisseur.Prenom} {avis.Produit.Fournisseur.Nom}" : "",
                FournisseurRaisonSociale = avis.Produit?.Fournisseur?.RaisonSociale ?? ""
            };
        }

        private static string GetStatutLibelle(StatutAvis statut)
        {
            return statut switch
            {
                StatutAvis.Publie => "Publié",
                StatutAvis.CommentaireSupprime => "Commentaire supprimé",
                StatutAvis.Signale => "Signalé",
                _ => "Non défini"
            };
        }
    }
}
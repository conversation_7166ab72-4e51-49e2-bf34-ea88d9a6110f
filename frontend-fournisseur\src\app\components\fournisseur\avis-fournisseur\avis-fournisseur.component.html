<div class="container-fluid">
  <!-- En-tête moderne -->
  <div class="page-header mb-4">
    <div class="header-content">
      <div class="header-title">
        <div class="title-icon">
          <i class="bi bi-star-fill"></i>
        </div>
        <div class="title-text">
          <h1 class="page-title">Mes Avis Clients</h1>
          <p class="page-subtitle">Consultez et répondez aux avis de vos clients</p>
        </div>
      </div>
      <div class="header-actions">
        <button class="btn btn-outline-primary" (click)="loadAvis()" title="Actualiser">
          <i class="bi bi-arrow-clockwise me-2"></i>
          Actualiser
        </button>
      </div>
    </div>
  </div>

  <!-- Statistiques en ligne -->
  <div class="stats-container mb-4" *ngIf="stats">
    <div class="stats-card stats-primary">
      <div class="stats-icon">
        <i class="bi bi-chat-square-text"></i>
      </div>
      <div class="stats-content">
        <div class="stats-number">{{ stats.totalAvis }}</div>
        <div class="stats-label">Total Avis</div>
      </div>
    </div>
    <div class="stats-card stats-warning">
      <div class="stats-icon">
        <i class="bi bi-exclamation-triangle"></i>
      </div>
      <div class="stats-content">
        <div class="stats-number">{{ stats.avisSignales || 0 }}</div>
        <div class="stats-label">Signalés</div>
      </div>
    </div>

    <div class="stats-card stats-info">
      <div class="stats-icon">
        <i class="bi bi-star-fill"></i>
      </div>
      <div class="stats-content">
        <div class="stats-number">{{ stats.noteMoyenneGlobale | number:'1.1-1' }}</div>
        <div class="stats-label">Note Moyenne</div>
      </div>
    </div>
  </div>

  <!-- Filtres et recherche -->
  <div class="filters-section mb-4">
    <div class="filters-card">
      <div class="filters-header">
        <h5><i class="bi bi-funnel me-2"></i>Filtres</h5>
      </div>
      <div class="filters-content">
        <div class="row g-3">
          <div class="col-md-3">
            <label class="form-label">Statut</label>
            <select class="form-select" [(ngModel)]="filter.statut" (change)="onFilterChange()">
              <option [value]="undefined">Tous les statuts</option>
              <option [value]="StatutAvis.Signale">Signalés</option>
              <option [value]="StatutAvis.CommentaireSupprime">Commentaire supprimé</option>
            </select>
          </div>
          
          <div class="col-md-3">
            <label class="form-label">Recherche</label>
            <div class="input-group">
              <span class="input-group-text"><i class="bi bi-search"></i></span>
              <input type="text" class="form-control" placeholder="Rechercher..." 
                     [(ngModel)]="filter.recherche" (keyup.enter)="onFilterChange()">
            </div>
          </div>
          
          <div class="col-md-2">
            <label class="form-label">Trier par</label>
            <select class="form-select" [(ngModel)]="filter.sortBy" (change)="onFilterChange()">
              <option value="datePublication">Date</option>
              <option value="note">Note</option>
              <option value="produitNom">Produit</option>
            </select>
          </div>
          
          <div class="col-md-2">
            <label class="form-label">Ordre</label>
            <select class="form-select" [(ngModel)]="filter.sortDesc" (change)="onFilterChange()">
              <option [value]="true">Décroissant</option>
              <option [value]="false">Croissant</option>
            </select>
          </div>
          
          <div class="col-md-2 d-flex align-items-end">
            <button class="btn btn-outline-secondary w-100" (click)="resetFilters()">
              <i class="bi bi-x-circle me-2"></i>Reset
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Message d'erreur -->
  <div *ngIf="error" class="alert alert-danger alert-dismissible fade show">
    <i class="bi bi-exclamation-triangle me-2"></i>
    {{ error }}
    <button type="button" class="btn-close" (click)="error = null"></button>
  </div>

  <!-- Loading -->
  <div *ngIf="loading" class="loading-container">
    <div class="loading-spinner">
      <div class="spinner-border text-primary" role="status">
        <span class="visually-hidden">Chargement...</span>
      </div>
      <p class="loading-text">Chargement des avis...</p>
    </div>
  </div>

  <!-- Liste des avis -->
  <div *ngIf="!loading" class="avis-container">
    <div *ngIf="avis.length === 0" class="empty-state">
      <div class="empty-icon">
        <i class="bi bi-chat-square-text"></i>
      </div>
      <h3>Aucun avis trouvé</h3>
      <p>Il n'y a pas encore d'avis correspondant à vos critères.</p>
    </div>

    <div *ngIf="avis.length > 0" class="avis-list">
      <div *ngFor="let avisItem of avis" class="avis-card">
        <div class="avis-header">
          <div class="avis-product">
            <h6 class="product-name">{{ avisItem.produitNom }}</h6>
            <small class="product-ref">Réf: {{ avisItem.produitReference }}</small>
          </div>
          <div class="avis-date">
            <small class="text-muted">{{ avisItem.datePublication | date:'dd/MM/yyyy HH:mm' }}</small>
          </div>
        </div>

        <div class="avis-content">
          <div class="avis-client">
            <div class="client-info">
              <i class="bi bi-person-circle me-2"></i>
              <span>{{ avisItem.clientPrenom }} {{ avisItem.clientNom }}</span>
            </div>
            <div class="avis-rating">
              <div class="stars">
                <span *ngFor="let star of getStars(avisItem.note)" 
                      class="bi" [class]="'bi-' + star"></span>
              </div>
              <span class="rating-value">{{ avisItem.note }}/5</span>
            </div>
          </div>

          <!-- Commentaire -->
          <div class="avis-comment">
            <!-- Commentaire présent et non supprimé -->
            <div *ngIf="avisItem.commentaire && avisItem.commentaire.trim() && !avisItem.commentaireSupprime"
                 class="comment-content">
              <i class="bi bi-chat-quote me-2"></i>
              <span>{{ avisItem.commentaire }}</span>
            </div>
            <!-- Commentaire supprimé par l'admin -->
            <div *ngIf="avisItem.commentaireSupprime"
                 class="comment-deleted">
              <i class="bi bi-chat-square-x me-2"></i>
              <span>Commentaire supprimé</span>
            </div>
            <!-- Avis sans commentaire (note seule) -->
            <div *ngIf="(!avisItem.commentaire || !avisItem.commentaire.trim()) && !avisItem.commentaireSupprime"
                 class="comment-none">
              <i class="bi bi-star me-2"></i>
              <span>Note sans commentaire</span>
            </div>
          </div>

          <!-- Statut et actions -->
          <div class="avis-footer">
            <div class="avis-status">
              <span class="badge" [class]="'badge-' + getStatutClass(avisItem.statut)">
                <i class="bi" [class]="getStatutIcon(avisItem.statut)"></i>
                {{ avisItem.statutLibelle }}
              </span>
            </div>
            <div class="avis-actions">
              <button class="btn btn-sm btn-outline-warning" (click)="signalerAvis(avisItem)" title="Signaler">
                <i class="bi bi-flag me-1"></i>
                <span>Signaler</span>
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

<!-- Modale de signalement -->
<div class="modal fade" id="signalementModal" tabindex="-1" aria-labelledby="signalementModalLabel" aria-hidden="true"
     [class.show]="showSignalementModal" [style.display]="showSignalementModal ? 'block' : 'none'">
  <div class="modal-dialog modal-lg">
    <div class="modal-content">
      <div class="modal-header bg-warning text-white">
        <h5 class="modal-title" id="signalementModalLabel">
          <i class="bi bi-flag-fill me-2"></i>
          Signaler un problème avec cet avis
        </h5>
        <button type="button" class="btn-close btn-close-white" (click)="closeSignalementModal()" aria-label="Close"></button>
      </div>

      <div class="modal-body" *ngIf="selectedAvisForSignalement">
        <!-- Informations sur l'avis à signaler -->
        <div class="alert alert-info">
          <h6><i class="bi bi-info-circle me-2"></i>Avis à signaler :</h6>
          <p><strong>Produit :</strong> {{ selectedAvisForSignalement.produitNom }}</p>
          <p><strong>Client :</strong> {{ selectedAvisForSignalement.clientNom }}</p>
          <p><strong>Note :</strong>
            <span class="ms-2">
              <i class="bi bi-star-fill text-warning" *ngFor="let star of [].constructor(selectedAvisForSignalement.note)"></i>
              <i class="bi bi-star text-muted" *ngFor="let star of [].constructor(5 - selectedAvisForSignalement.note)"></i>
            </span>
          </p>
          <p><strong>Commentaire :</strong></p>
          <div class="bg-light p-3 rounded">
            {{ selectedAvisForSignalement.commentaire || 'Aucun commentaire' }}
          </div>
        </div>

        <!-- Formulaire de signalement -->
        <form (ngSubmit)="signalerAvis()" #signalementForm="ngForm">
          <div class="mb-3">
            <label for="raisonSignalement" class="form-label">
              <i class="bi bi-exclamation-triangle me-2"></i>
              <strong>Raison du signalement *</strong>
            </label>
            <select class="form-select" id="raisonSignalement"
                    [(ngModel)]="signalementData.raisonSignalement"
                    name="raisonSignalement" required>
              <option value="">-- Sélectionnez une raison --</option>
              <option value="contenu_inapproprie">Contenu inapproprié ou offensant</option>
              <option value="faux_avis">Faux avis ou spam</option>
              <option value="information_incorrecte">Informations incorrectes sur le produit</option>
              <option value="violation_regles">Violation des règles de la plateforme</option>
              <option value="diffamation">Diffamation ou attaque personnelle</option>
              <option value="autre">Autre raison</option>
            </select>
          </div>

          <div class="mb-3">
            <label for="detailsSignalement" class="form-label">
              <i class="bi bi-chat-text me-2"></i>
              <strong>Détails supplémentaires</strong>
            </label>
            <textarea class="form-control" id="detailsSignalement" rows="4"
                      [(ngModel)]="signalementData.detailsSignalement"
                      name="detailsSignalement"
                      placeholder="Expliquez en détail pourquoi vous signalez cet avis..."></textarea>
            <div class="form-text">
              Fournissez des détails spécifiques pour aider l'équipe de modération à traiter votre signalement.
            </div>
          </div>

          <div class="alert alert-warning">
            <i class="bi bi-info-circle me-2"></i>
            <strong>Important :</strong> Votre signalement sera envoyé à l'équipe de modération qui l'examinera dans les plus brefs délais.
            Les signalements abusifs peuvent entraîner des sanctions.
          </div>
        </form>
      </div>

      <div class="modal-footer">
        <button type="button" class="btn btn-secondary" (click)="closeSignalementModal()">
          <i class="bi bi-x-circle me-2"></i>Annuler
        </button>
        <button type="button" class="btn btn-warning"
                (click)="signalerAvis()"
                [disabled]="!signalementData.raisonSignalement || loading">
          <i class="bi bi-flag-fill me-2"></i>
          <span *ngIf="!loading">Signaler cet avis</span>
          <span *ngIf="loading">
            <span class="spinner-border spinner-border-sm me-2" role="status"></span>
            Signalement en cours...
          </span>
        </button>
      </div>
    </div>
  </div>
</div>

<!-- Backdrop pour la modale -->
<div class="modal-backdrop fade" [class.show]="showSignalementModal" *ngIf="showSignalementModal"
     (click)="closeSignalementModal()"></div>

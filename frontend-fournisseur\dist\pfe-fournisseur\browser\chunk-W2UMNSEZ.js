import {
  NotificationIconComponent
} from "./chunk-NEMJGOZR.js";
import "./chunk-2WHFEWR5.js";
import "./chunk-WQ24PYVH.js";
import "./chunk-ZSXXNL7U.js";
import {
  AdminAuthService
} from "./chunk-2RV3R4JN.js";
import {
  Router,
  RouterLink,
  RouterLinkActive,
  RouterOutlet
} from "./chunk-6BVUYNW4.js";
import "./chunk-GFHHPDQ6.js";
import "./chunk-7JDDWGD3.js";
import {
  CommonModule,
  Component,
  setClassMetadata,
  ɵsetClassDebugInfo,
  ɵɵadvance,
  ɵɵdefineComponent,
  ɵɵdirectiveInject,
  ɵɵelement,
  ɵɵelementEnd,
  ɵɵelementStart,
  ɵɵtext,
  ɵɵtextInterpolate
} from "./chunk-UBZQS7JS.js";

// src/app/components/admin/layout/admin-layout/admin-layout.component.ts
var AdminLayoutComponent = class _AdminLayoutComponent {
  adminAuthService;
  router;
  currentUser = null;
  constructor(adminAuthService, router) {
    this.adminAuthService = adminAuthService;
    this.router = router;
  }
  ngOnInit() {
    this.adminAuthService.currentUser$.subscribe((user) => {
      this.currentUser = user;
    });
  }
  logout() {
    this.adminAuthService.logout().subscribe(() => {
      this.router.navigate(["/admin/login"]);
    });
  }
  static \u0275fac = function AdminLayoutComponent_Factory(__ngFactoryType__) {
    return new (__ngFactoryType__ || _AdminLayoutComponent)(\u0275\u0275directiveInject(AdminAuthService), \u0275\u0275directiveInject(Router));
  };
  static \u0275cmp = /* @__PURE__ */ \u0275\u0275defineComponent({ type: _AdminLayoutComponent, selectors: [["app-admin-layout"]], decls: 29, vars: 1, consts: [[1, "admin-layout"], [1, "admin-header"], [1, "header-actions"], [1, "user-role"], [1, "admin-body"], [1, "admin-sidebar"], [1, "sidebar-nav"], ["routerLink", "/admin/dashboard", "routerLinkActive", "active", 1, "nav-item"], ["routerLink", "/admin/dashboard/users", "routerLinkActive", "active", 1, "nav-item"], ["routerLink", "/admin/dashboard/products", "routerLinkActive", "active", 1, "nav-item"], ["routerLink", "/admin/dashboard/demandes", "routerLinkActive", "active", 1, "nav-item"], ["routerLink", "/admin/dashboard/categories", "routerLinkActive", "active", 1, "nav-item"], ["routerLink", "/admin/dashboard/orders", "routerLinkActive", "active", 1, "nav-item"], ["routerLink", "/admin/dashboard/avis-moderation", "routerLinkActive", "active", 1, "nav-item"], ["routerLink", "/admin/dashboard/reports", "routerLinkActive", "active", 1, "nav-item"], [1, "admin-content"]], template: function AdminLayoutComponent_Template(rf, ctx) {
    if (rf & 1) {
      \u0275\u0275elementStart(0, "div", 0)(1, "header", 1)(2, "h1");
      \u0275\u0275text(3, "Administration - OptiLet");
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(4, "div", 2);
      \u0275\u0275element(5, "app-notification-icon");
      \u0275\u0275elementStart(6, "span", 3);
      \u0275\u0275text(7);
      \u0275\u0275elementEnd()()();
      \u0275\u0275elementStart(8, "div", 4)(9, "div", 5)(10, "nav", 6)(11, "a", 7);
      \u0275\u0275text(12, "\u{1F3E0} Dashboard");
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(13, "a", 8);
      \u0275\u0275text(14, "\u{1F465} Utilisateurs");
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(15, "a", 9);
      \u0275\u0275text(16, "\u{1F4E6} Produits");
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(17, "a", 10);
      \u0275\u0275text(18, "\u{1F4CB} Demandes");
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(19, "a", 11);
      \u0275\u0275text(20, "\u{1F4C1} Cat\xE9gories");
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(21, "a", 12);
      \u0275\u0275text(22, "\u{1F6D2} Commandes");
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(23, "a", 13);
      \u0275\u0275text(24, "\u2B50 Mod\xE9ration Avis");
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(25, "a", 14);
      \u0275\u0275text(26, "\u{1F4C8} Rapports");
      \u0275\u0275elementEnd()()();
      \u0275\u0275elementStart(27, "main", 15);
      \u0275\u0275element(28, "router-outlet");
      \u0275\u0275elementEnd()()();
    }
    if (rf & 2) {
      \u0275\u0275advance(7);
      \u0275\u0275textInterpolate(ctx.currentUser == null ? null : ctx.currentUser.role);
    }
  }, dependencies: [CommonModule, RouterOutlet, RouterLink, RouterLinkActive, NotificationIconComponent], styles: ["\n\n.admin-layout[_ngcontent-%COMP%] {\n  display: flex;\n  flex-direction: column;\n  min-height: 100vh;\n  background: #f8fafc;\n}\n.admin-body[_ngcontent-%COMP%] {\n  display: flex;\n  flex: 1;\n}\n.admin-sidebar[_ngcontent-%COMP%] {\n  width: 280px;\n  background:\n    linear-gradient(\n      135deg,\n      #667eea 0%,\n      #764ba2 100%);\n  color: white;\n  display: flex;\n  flex-direction: column;\n  box-shadow: 4px 0 20px rgba(102, 126, 234, 0.2);\n}\n.sidebar-nav[_ngcontent-%COMP%] {\n  flex: 1;\n  padding: 1rem 0;\n  display: flex;\n  flex-direction: column;\n}\n.nav-item[_ngcontent-%COMP%] {\n  display: flex;\n  align-items: center;\n  padding: 1rem 2rem;\n  color: rgba(255, 255, 255, 0.9);\n  text-decoration: none;\n  transition: all 0.3s ease;\n  border-left: 3px solid transparent;\n  font-weight: 500;\n  font-size: 0.95rem;\n  margin: 0;\n  border-bottom: 1px solid rgba(255, 255, 255, 0.1);\n}\n.nav-item[_ngcontent-%COMP%]:hover {\n  background: rgba(255, 255, 255, 0.15);\n  color: white;\n  border-left-color: #667eea;\n  transform: translateX(4px);\n  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);\n}\n.nav-item.active[_ngcontent-%COMP%] {\n  background: rgba(102, 126, 234, 0.2);\n  color: white;\n  border-left-color: #667eea;\n  font-weight: 600;\n}\n.admin-main[_ngcontent-%COMP%] {\n  flex: 1;\n  display: flex;\n  flex-direction: column;\n}\n.admin-header[_ngcontent-%COMP%] {\n  background:\n    linear-gradient(\n      135deg,\n      #667eea 0%,\n      #764ba2 100%);\n  padding: 1.5rem 2rem;\n  border-bottom: none;\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);\n  width: 100%;\n  position: sticky;\n  top: 0;\n  z-index: 1000;\n}\n.header-actions[_ngcontent-%COMP%] {\n  display: flex;\n  align-items: center;\n  gap: 1.5rem;\n}\n.admin-header[_ngcontent-%COMP%]   h1[_ngcontent-%COMP%] {\n  margin: 0;\n  font-size: 1.75rem;\n  color: white;\n  font-weight: 600;\n  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);\n}\n.user-role[_ngcontent-%COMP%] {\n  padding: 0.75rem 1.25rem;\n  background: rgba(255, 255, 255, 0.2);\n  color: white;\n  border-radius: 25px;\n  font-size: 0.875rem;\n  font-weight: 500;\n  border: 1px solid rgba(255, 255, 255, 0.3);\n  -webkit-backdrop-filter: blur(10px);\n  backdrop-filter: blur(10px);\n  transition: all 0.3s ease;\n}\n.user-role[_ngcontent-%COMP%]:hover {\n  background: rgba(255, 255, 255, 0.3);\n  transform: translateY(-1px);\n}\n.admin-content[_ngcontent-%COMP%] {\n  flex: 1;\n  padding: 0;\n  overflow-y: auto;\n  background: #f8fafc;\n}\n@media (max-width: 768px) {\n  .admin-header[_ngcontent-%COMP%] {\n    padding: 1rem;\n  }\n  .admin-header[_ngcontent-%COMP%]   h1[_ngcontent-%COMP%] {\n    font-size: 1.25rem;\n  }\n  .admin-body[_ngcontent-%COMP%] {\n    flex-direction: column;\n  }\n  .admin-sidebar[_ngcontent-%COMP%] {\n    width: 100%;\n    height: auto;\n    order: 2;\n  }\n  .admin-content[_ngcontent-%COMP%] {\n    order: 1;\n  }\n  .sidebar-nav[_ngcontent-%COMP%] {\n    display: flex;\n    overflow-x: auto;\n    padding: 1rem;\n    gap: 0.5rem;\n  }\n  .nav-item[_ngcontent-%COMP%] {\n    white-space: nowrap;\n    min-width: 120px;\n    text-align: center;\n    padding: 0.75rem 1rem;\n    border-radius: 12px;\n    border-left: none;\n    border-bottom: 3px solid transparent;\n  }\n  .nav-item[_ngcontent-%COMP%]:hover {\n    border-left: none;\n    border-bottom-color: #667eea;\n    transform: translateX(0);\n    transform: translateY(-2px);\n  }\n}\n/*# sourceMappingURL=admin-layout.component.css.map */"] });
};
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && setClassMetadata(AdminLayoutComponent, [{
    type: Component,
    args: [{ selector: "app-admin-layout", standalone: true, imports: [CommonModule, RouterOutlet, RouterLink, RouterLinkActive, NotificationIconComponent], template: `
    <div class="admin-layout">
      <!-- Header sur toute la largeur -->
      <header class="admin-header">
        <h1>Administration - OptiLet</h1>
        <div class="header-actions">
          <app-notification-icon></app-notification-icon>
          <span class="user-role">{{ currentUser?.role }}</span>
        </div>
      </header>

      <!-- Container pour sidebar et contenu -->
      <div class="admin-body">
        <div class="admin-sidebar">
          <nav class="sidebar-nav">
            <a routerLink="/admin/dashboard" routerLinkActive="active" class="nav-item">\u{1F3E0} Dashboard</a>
            <a routerLink="/admin/dashboard/users" routerLinkActive="active" class="nav-item">\u{1F465} Utilisateurs</a>
            <a routerLink="/admin/dashboard/products" routerLinkActive="active" class="nav-item">\u{1F4E6} Produits</a>
            <a routerLink="/admin/dashboard/demandes" routerLinkActive="active" class="nav-item">\u{1F4CB} Demandes</a>
            <a routerLink="/admin/dashboard/categories" routerLinkActive="active" class="nav-item">\u{1F4C1} Cat\xE9gories</a>
            <a routerLink="/admin/dashboard/orders" routerLinkActive="active" class="nav-item">\u{1F6D2} Commandes</a>
            <a routerLink="/admin/dashboard/avis-moderation" routerLinkActive="active" class="nav-item">\u2B50 Mod\xE9ration Avis</a>
            <a routerLink="/admin/dashboard/reports" routerLinkActive="active" class="nav-item">\u{1F4C8} Rapports</a>
          </nav>
        </div>

        <main class="admin-content">
          <router-outlet></router-outlet>
        </main>
      </div>
    </div>
  `, styles: ["/* angular:styles/component:css;0978fe960c77ec66545c59bdedbcdfc2284822a9f60a3ee37c77f4f054cd9a5e;C:/Users/<USER>/Desktop/STE/PFE/PFE/frontend-fournisseur/src/app/components/admin/layout/admin-layout/admin-layout.component.ts */\n.admin-layout {\n  display: flex;\n  flex-direction: column;\n  min-height: 100vh;\n  background: #f8fafc;\n}\n.admin-body {\n  display: flex;\n  flex: 1;\n}\n.admin-sidebar {\n  width: 280px;\n  background:\n    linear-gradient(\n      135deg,\n      #667eea 0%,\n      #764ba2 100%);\n  color: white;\n  display: flex;\n  flex-direction: column;\n  box-shadow: 4px 0 20px rgba(102, 126, 234, 0.2);\n}\n.sidebar-nav {\n  flex: 1;\n  padding: 1rem 0;\n  display: flex;\n  flex-direction: column;\n}\n.nav-item {\n  display: flex;\n  align-items: center;\n  padding: 1rem 2rem;\n  color: rgba(255, 255, 255, 0.9);\n  text-decoration: none;\n  transition: all 0.3s ease;\n  border-left: 3px solid transparent;\n  font-weight: 500;\n  font-size: 0.95rem;\n  margin: 0;\n  border-bottom: 1px solid rgba(255, 255, 255, 0.1);\n}\n.nav-item:hover {\n  background: rgba(255, 255, 255, 0.15);\n  color: white;\n  border-left-color: #667eea;\n  transform: translateX(4px);\n  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);\n}\n.nav-item.active {\n  background: rgba(102, 126, 234, 0.2);\n  color: white;\n  border-left-color: #667eea;\n  font-weight: 600;\n}\n.admin-main {\n  flex: 1;\n  display: flex;\n  flex-direction: column;\n}\n.admin-header {\n  background:\n    linear-gradient(\n      135deg,\n      #667eea 0%,\n      #764ba2 100%);\n  padding: 1.5rem 2rem;\n  border-bottom: none;\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);\n  width: 100%;\n  position: sticky;\n  top: 0;\n  z-index: 1000;\n}\n.header-actions {\n  display: flex;\n  align-items: center;\n  gap: 1.5rem;\n}\n.admin-header h1 {\n  margin: 0;\n  font-size: 1.75rem;\n  color: white;\n  font-weight: 600;\n  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);\n}\n.user-role {\n  padding: 0.75rem 1.25rem;\n  background: rgba(255, 255, 255, 0.2);\n  color: white;\n  border-radius: 25px;\n  font-size: 0.875rem;\n  font-weight: 500;\n  border: 1px solid rgba(255, 255, 255, 0.3);\n  -webkit-backdrop-filter: blur(10px);\n  backdrop-filter: blur(10px);\n  transition: all 0.3s ease;\n}\n.user-role:hover {\n  background: rgba(255, 255, 255, 0.3);\n  transform: translateY(-1px);\n}\n.admin-content {\n  flex: 1;\n  padding: 0;\n  overflow-y: auto;\n  background: #f8fafc;\n}\n@media (max-width: 768px) {\n  .admin-header {\n    padding: 1rem;\n  }\n  .admin-header h1 {\n    font-size: 1.25rem;\n  }\n  .admin-body {\n    flex-direction: column;\n  }\n  .admin-sidebar {\n    width: 100%;\n    height: auto;\n    order: 2;\n  }\n  .admin-content {\n    order: 1;\n  }\n  .sidebar-nav {\n    display: flex;\n    overflow-x: auto;\n    padding: 1rem;\n    gap: 0.5rem;\n  }\n  .nav-item {\n    white-space: nowrap;\n    min-width: 120px;\n    text-align: center;\n    padding: 0.75rem 1rem;\n    border-radius: 12px;\n    border-left: none;\n    border-bottom: 3px solid transparent;\n  }\n  .nav-item:hover {\n    border-left: none;\n    border-bottom-color: #667eea;\n    transform: translateX(0);\n    transform: translateY(-2px);\n  }\n}\n/*# sourceMappingURL=admin-layout.component.css.map */\n"] }]
  }], () => [{ type: AdminAuthService }, { type: Router }], null);
})();
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && \u0275setClassDebugInfo(AdminLayoutComponent, { className: "AdminLayoutComponent", filePath: "src/app/components/admin/layout/admin-layout/admin-layout.component.ts", lineNumber: 213 });
})();
export {
  AdminLayoutComponent
};
//# sourceMappingURL=chunk-W2UMNSEZ.js.map

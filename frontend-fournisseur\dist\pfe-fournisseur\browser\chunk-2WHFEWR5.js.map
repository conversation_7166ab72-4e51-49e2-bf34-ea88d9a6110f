{"version": 3, "sources": ["src/app/services/notification.service.ts"], "sourcesContent": ["import { Injectable } from '@angular/core';\nimport { HttpClient, HttpParams } from '@angular/common/http';\nimport { Observable, BehaviorSubject } from 'rxjs';\nimport { map, tap } from 'rxjs/operators';\nimport { environment } from '../../environments/environment';\n\nexport interface NotificationDto {\n  id: number;\n  contenu: string;\n  dateEnvoi: Date;\n  estLue: boolean;\n  utilisateurId: number;\n}\n\nexport interface CreateNotificationDto {\n  contenu: string;\n  utilisateurId: number;\n}\n\n@Injectable({\n  providedIn: 'root'\n})\nexport class NotificationService {\n  private apiUrl = `${environment.apiUrl}/notification`;\n  private notificationsSubject = new BehaviorSubject<NotificationDto[]>([]);\n  private unreadCountSubject = new BehaviorSubject<number>(0);\n\n  public notifications$ = this.notificationsSubject.asObservable();\n  public unreadCount$ = this.unreadCountSubject.asObservable();\n\n  constructor(private http: HttpClient) {}\n\n  public getUserNotifications(userId: number): Observable<NotificationDto[]> {\n    return this.http.get<NotificationDto[]>(`${this.apiUrl}/user/${userId}`).pipe(\n      map(notifs => this.sortNotifications(notifs)),\n      tap(notifs => {\n        this.notificationsSubject.next(notifs);\n        this.updateUnreadCount(notifs);\n      })\n    );\n  }\n\n  public getUnreadNotifications(userId: number): Observable<NotificationDto[]> {\n    return this.http.get<NotificationDto[]>(`${this.apiUrl}/user/${userId}/unread`).pipe(\n      tap(notifs => this.updateUnreadCount(notifs))\n    );\n  }\n\n  public markAsRead(notificationId: number): Observable<void> {\n    return this.http.patch<void>(`${this.apiUrl}/${notificationId}/read`, {}).pipe(\n      tap(() => {\n        const updated = this.notificationsSubject.value.map(n =>\n          n.id === notificationId ? { ...n, estLue: true } : n\n        );\n        this.notificationsSubject.next(updated);\n        this.updateUnreadCount(updated);\n      })\n    );\n  }\n\n  public createNotification(notification: CreateNotificationDto): Observable<NotificationDto> {\n    return this.http.post<NotificationDto>(this.apiUrl, notification);\n  }\n\n  public deleteNotification(notificationId: number): Observable<void> {\n    return this.http.delete<void>(`${this.apiUrl}/${notificationId}`).pipe(\n      tap(() => {\n        const updated = this.notificationsSubject.value.filter(n => n.id !== notificationId);\n        this.notificationsSubject.next(updated);\n        this.updateUnreadCount(updated);\n      })\n    );\n  }\n\n  private sortNotifications(notifs: NotificationDto[]): NotificationDto[] {\n    return [...notifs].sort((a, b) =>\n      new Date(b.dateEnvoi).getTime() - new Date(a.dateEnvoi).getTime()\n    );\n  }\n\n  private updateUnreadCount(notifs: NotificationDto[]): void {\n    const count = notifs.filter(n => !n.estLue).length;\n    this.unreadCountSubject.next(count);\n  }\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;;AAsBM,IAAO,sBAAP,MAAO,qBAAmB;EAQV;EAPZ,SAAS,GAAG,YAAY,MAAM;EAC9B,uBAAuB,IAAI,gBAAmC,CAAA,CAAE;EAChE,qBAAqB,IAAI,gBAAwB,CAAC;EAEnD,iBAAiB,KAAK,qBAAqB,aAAY;EACvD,eAAe,KAAK,mBAAmB,aAAY;EAE1D,YAAoB,MAAgB;AAAhB,SAAA,OAAA;EAAmB;EAEhC,qBAAqB,QAAc;AACxC,WAAO,KAAK,KAAK,IAAuB,GAAG,KAAK,MAAM,SAAS,MAAM,EAAE,EAAE,KACvE,IAAI,YAAU,KAAK,kBAAkB,MAAM,CAAC,GAC5C,IAAI,YAAS;AACX,WAAK,qBAAqB,KAAK,MAAM;AACrC,WAAK,kBAAkB,MAAM;IAC/B,CAAC,CAAC;EAEN;EAEO,uBAAuB,QAAc;AAC1C,WAAO,KAAK,KAAK,IAAuB,GAAG,KAAK,MAAM,SAAS,MAAM,SAAS,EAAE,KAC9E,IAAI,YAAU,KAAK,kBAAkB,MAAM,CAAC,CAAC;EAEjD;EAEO,WAAW,gBAAsB;AACtC,WAAO,KAAK,KAAK,MAAY,GAAG,KAAK,MAAM,IAAI,cAAc,SAAS,CAAA,CAAE,EAAE,KACxE,IAAI,MAAK;AACP,YAAM,UAAU,KAAK,qBAAqB,MAAM,IAAI,OAClD,EAAE,OAAO,iBAAiB,iCAAK,IAAL,EAAQ,QAAQ,KAAI,KAAK,CAAC;AAEtD,WAAK,qBAAqB,KAAK,OAAO;AACtC,WAAK,kBAAkB,OAAO;IAChC,CAAC,CAAC;EAEN;EAEO,mBAAmB,cAAmC;AAC3D,WAAO,KAAK,KAAK,KAAsB,KAAK,QAAQ,YAAY;EAClE;EAEO,mBAAmB,gBAAsB;AAC9C,WAAO,KAAK,KAAK,OAAa,GAAG,KAAK,MAAM,IAAI,cAAc,EAAE,EAAE,KAChE,IAAI,MAAK;AACP,YAAM,UAAU,KAAK,qBAAqB,MAAM,OAAO,OAAK,EAAE,OAAO,cAAc;AACnF,WAAK,qBAAqB,KAAK,OAAO;AACtC,WAAK,kBAAkB,OAAO;IAChC,CAAC,CAAC;EAEN;EAEQ,kBAAkB,QAAyB;AACjD,WAAO,CAAC,GAAG,MAAM,EAAE,KAAK,CAAC,GAAG,MAC1B,IAAI,KAAK,EAAE,SAAS,EAAE,QAAO,IAAK,IAAI,KAAK,EAAE,SAAS,EAAE,QAAO,CAAE;EAErE;EAEQ,kBAAkB,QAAyB;AACjD,UAAM,QAAQ,OAAO,OAAO,OAAK,CAAC,EAAE,MAAM,EAAE;AAC5C,SAAK,mBAAmB,KAAK,KAAK;EACpC;;qCA7DW,sBAAmB,mBAAA,UAAA,CAAA;EAAA;4EAAnB,sBAAmB,SAAnB,qBAAmB,WAAA,YAFlB,OAAM,CAAA;;;sEAEP,qBAAmB,CAAA;UAH/B;WAAW;MACV,YAAY;KACb;;;", "names": []}
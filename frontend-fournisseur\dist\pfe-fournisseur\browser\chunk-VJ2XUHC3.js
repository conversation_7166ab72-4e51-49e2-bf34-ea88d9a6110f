import {
  AvisModerationService,
  StatutAvis
} from "./chunk-6YR6544A.js";
import {
  Default<PERSON><PERSON>ueAccessor,
  FormsModule,
  NgControlStatus,
  NgControlStatusGroup,
  NgForm,
  NgModel,
  NgSelectOption,
  RequiredValidator,
  SelectControlValueAccessor,
  ɵNgNoValidate,
  ɵNgSelectMultipleOption
} from "./chunk-HQBVYEOO.js";
import "./chunk-GFHHPDQ6.js";
import "./chunk-7JDDWGD3.js";
import {
  CommonModule,
  Component,
  NgForOf,
  NgIf,
  setClassMetadata,
  ɵsetClassDebugInfo,
  ɵɵadvance,
  ɵɵclassMap,
  ɵɵclassProp,
  ɵɵdefineComponent,
  ɵɵdirectiveInject,
  ɵɵelement,
  ɵɵelementEnd,
  ɵɵelementStart,
  ɵɵgetCurrentView,
  ɵɵlistener,
  ɵɵnextContext,
  ɵɵproperty,
  ɵɵpureFunction0,
  ɵɵresetView,
  ɵɵrestoreView,
  ɵɵtemplate,
  ɵɵtext,
  ɵɵtextInterpolate,
  ɵɵtextInterpolate1,
  ɵɵtextInterpolate2,
  ɵɵtwoWayBindingSet,
  ɵɵtwoWayListener,
  ɵɵtwoWayProperty
} from "./chunk-UBZQS7JS.js";

// src/app/components/admin/avis-moderation/avis-moderation.component.ts
var _c0 = () => [1, 2, 3, 4, 5];
function AvisModerationComponent_div_15_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "div", 34)(1, "div", 35)(2, "div", 36);
    \u0275\u0275element(3, "i", 37);
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(4, "div", 38)(5, "div", 39);
    \u0275\u0275text(6);
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(7, "div", 40);
    \u0275\u0275text(8, "Total Avis");
    \u0275\u0275elementEnd()()();
    \u0275\u0275elementStart(9, "div", 41)(10, "div", 36);
    \u0275\u0275element(11, "i", 42);
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(12, "div", 38)(13, "div", 39);
    \u0275\u0275text(14);
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(15, "div", 40);
    \u0275\u0275text(16, "Publi\xE9s");
    \u0275\u0275elementEnd()()();
    \u0275\u0275elementStart(17, "div", 43)(18, "div", 36);
    \u0275\u0275element(19, "i", 44);
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(20, "div", 38)(21, "div", 39);
    \u0275\u0275text(22);
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(23, "div", 40);
    \u0275\u0275text(24, "Commentaires supprim\xE9s");
    \u0275\u0275elementEnd()()()();
  }
  if (rf & 2) {
    const ctx_r0 = \u0275\u0275nextContext();
    \u0275\u0275advance(6);
    \u0275\u0275textInterpolate(ctx_r0.stats.totalAvis);
    \u0275\u0275advance(8);
    \u0275\u0275textInterpolate(ctx_r0.stats.avisPublies);
    \u0275\u0275advance(8);
    \u0275\u0275textInterpolate(ctx_r0.stats.avisCommentaireSupprime || 0);
  }
}
function AvisModerationComponent_div_47_Template(rf, ctx) {
  if (rf & 1) {
    const _r2 = \u0275\u0275getCurrentView();
    \u0275\u0275elementStart(0, "div", 45);
    \u0275\u0275element(1, "i", 46);
    \u0275\u0275text(2);
    \u0275\u0275elementStart(3, "button", 47);
    \u0275\u0275listener("click", function AvisModerationComponent_div_47_Template_button_click_3_listener() {
      \u0275\u0275restoreView(_r2);
      const ctx_r0 = \u0275\u0275nextContext();
      return \u0275\u0275resetView(ctx_r0.clearError());
    });
    \u0275\u0275elementEnd()();
  }
  if (rf & 2) {
    const ctx_r0 = \u0275\u0275nextContext();
    \u0275\u0275advance(2);
    \u0275\u0275textInterpolate1(" ", ctx_r0.error, " ");
  }
}
function AvisModerationComponent_div_48_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "div", 48)(1, "div", 49)(2, "span", 50);
    \u0275\u0275text(3, "Chargement...");
    \u0275\u0275elementEnd()()();
  }
}
function AvisModerationComponent_div_49_div_9_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "div", 57);
    \u0275\u0275element(1, "i", 58);
    \u0275\u0275elementStart(2, "h4", 59);
    \u0275\u0275text(3, "Aucun avis trouv\xE9");
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(4, "p", 60);
    \u0275\u0275text(5, "Aucun avis ne correspond aux crit\xE8res de recherche.");
    \u0275\u0275elementEnd()();
  }
}
function AvisModerationComponent_div_49_div_10_span_17_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "span", 87);
    \u0275\u0275element(1, "i", 88);
    \u0275\u0275elementEnd();
  }
  if (rf & 2) {
    const i_r4 = ctx.$implicit;
    const avisItem_r5 = \u0275\u0275nextContext().$implicit;
    \u0275\u0275classProp("filled", i_r4 <= avisItem_r5.note);
  }
}
function AvisModerationComponent_div_49_div_10_div_24_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "div", 89);
    \u0275\u0275element(1, "i", 90);
    \u0275\u0275elementStart(2, "span");
    \u0275\u0275text(3);
    \u0275\u0275elementEnd()();
  }
  if (rf & 2) {
    const avisItem_r5 = \u0275\u0275nextContext().$implicit;
    \u0275\u0275advance(3);
    \u0275\u0275textInterpolate(avisItem_r5.commentaire);
  }
}
function AvisModerationComponent_div_49_div_10_div_25_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "div", 91);
    \u0275\u0275element(1, "i", 92);
    \u0275\u0275elementStart(2, "span");
    \u0275\u0275text(3, "Commentaire supprim\xE9");
    \u0275\u0275elementEnd()();
  }
}
function AvisModerationComponent_div_49_div_10_div_26_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "div", 93);
    \u0275\u0275element(1, "i", 94);
    \u0275\u0275elementStart(2, "span");
    \u0275\u0275text(3, "Note sans commentaire");
    \u0275\u0275elementEnd()();
  }
}
function AvisModerationComponent_div_49_div_10_button_28_Template(rf, ctx) {
  if (rf & 1) {
    const _r6 = \u0275\u0275getCurrentView();
    \u0275\u0275elementStart(0, "button", 95);
    \u0275\u0275listener("click", function AvisModerationComponent_div_49_div_10_button_28_Template_button_click_0_listener() {
      \u0275\u0275restoreView(_r6);
      const avisItem_r5 = \u0275\u0275nextContext().$implicit;
      const ctx_r0 = \u0275\u0275nextContext(2);
      return \u0275\u0275resetView(ctx_r0.openSuppressionModal(avisItem_r5));
    });
    \u0275\u0275element(1, "i", 44);
    \u0275\u0275elementStart(2, "span");
    \u0275\u0275text(3, "Supprimer");
    \u0275\u0275elementEnd()();
  }
}
function AvisModerationComponent_div_49_div_10_div_33_div_1_Template(rf, ctx) {
  if (rf & 1) {
    const _r7 = \u0275\u0275getCurrentView();
    \u0275\u0275elementStart(0, "div", 99)(1, "div", 100)(2, "h5", 101);
    \u0275\u0275element(3, "i", 46);
    \u0275\u0275text(4, " Supprimer le commentaire ");
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(5, "button", 47);
    \u0275\u0275listener("click", function AvisModerationComponent_div_49_div_10_div_33_div_1_Template_button_click_5_listener() {
      \u0275\u0275restoreView(_r7);
      const ctx_r0 = \u0275\u0275nextContext(4);
      return \u0275\u0275resetView(ctx_r0.closeSuppressionModal());
    });
    \u0275\u0275elementEnd()();
    \u0275\u0275elementStart(6, "div", 102)(7, "div", 103);
    \u0275\u0275element(8, "i", 104);
    \u0275\u0275elementStart(9, "strong");
    \u0275\u0275text(10, "Cette action va :");
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(11, "ul", 105)(12, "li");
    \u0275\u0275text(13, "Supprimer uniquement le commentaire (la note reste visible)");
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(14, "li");
    \u0275\u0275text(15, "Envoyer une notification au client : ");
    \u0275\u0275elementStart(16, "em");
    \u0275\u0275text(17, `"Votre avis \xE9tait supprim\xE9 \xE0 cause de [raison] mais ne t'inqui\xE8te pas, votre note est mise en consid\xE9ration"`);
    \u0275\u0275elementEnd()()()();
    \u0275\u0275elementStart(18, "form", 106);
    \u0275\u0275listener("ngSubmit", function AvisModerationComponent_div_49_div_10_div_33_div_1_Template_form_ngSubmit_18_listener() {
      \u0275\u0275restoreView(_r7);
      const ctx_r0 = \u0275\u0275nextContext(4);
      return \u0275\u0275resetView(ctx_r0.supprimerCommentaire());
    });
    \u0275\u0275elementStart(19, "div", 107)(20, "label", 20);
    \u0275\u0275element(21, "i", 108);
    \u0275\u0275text(22, " Raison de la suppression ");
    \u0275\u0275elementStart(23, "span", 109);
    \u0275\u0275text(24, "*");
    \u0275\u0275elementEnd()();
    \u0275\u0275elementStart(25, "textarea", 110);
    \u0275\u0275twoWayListener("ngModelChange", function AvisModerationComponent_div_49_div_10_div_33_div_1_Template_textarea_ngModelChange_25_listener($event) {
      \u0275\u0275restoreView(_r7);
      const ctx_r0 = \u0275\u0275nextContext(4);
      \u0275\u0275twoWayBindingSet(ctx_r0.suppressionForm.raison, $event) || (ctx_r0.suppressionForm.raison = $event);
      return \u0275\u0275resetView($event);
    });
    \u0275\u0275elementEnd()();
    \u0275\u0275elementStart(26, "div", 111)(27, "button", 112);
    \u0275\u0275listener("click", function AvisModerationComponent_div_49_div_10_div_33_div_1_Template_button_click_27_listener() {
      \u0275\u0275restoreView(_r7);
      const ctx_r0 = \u0275\u0275nextContext(4);
      return \u0275\u0275resetView(ctx_r0.closeSuppressionModal());
    });
    \u0275\u0275element(28, "i", 113);
    \u0275\u0275text(29, " Annuler ");
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(30, "button", 114);
    \u0275\u0275element(31, "i", 115);
    \u0275\u0275text(32, " Supprimer et notifier ");
    \u0275\u0275elementEnd()()()()();
  }
  if (rf & 2) {
    const ctx_r0 = \u0275\u0275nextContext(4);
    \u0275\u0275advance(25);
    \u0275\u0275twoWayProperty("ngModel", ctx_r0.suppressionForm.raison);
    \u0275\u0275advance(5);
    \u0275\u0275property("disabled", !ctx_r0.suppressionForm.raison || !ctx_r0.suppressionForm.raison.trim());
  }
}
function AvisModerationComponent_div_49_div_10_div_33_div_2_div_18_div_10_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "div", 129)(1, "small", 60);
    \u0275\u0275text(2);
    \u0275\u0275elementEnd()();
  }
  if (rf & 2) {
    const ctx_r0 = \u0275\u0275nextContext(6);
    \u0275\u0275advance(2);
    \u0275\u0275textInterpolate(ctx_r0.selectedAvis == null ? null : ctx_r0.selectedAvis.commentaireModeration);
  }
}
function AvisModerationComponent_div_49_div_10_div_33_div_2_div_18_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "div", 119)(1, "div", 126);
    \u0275\u0275element(2, "i", 127);
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(3, "div", 122)(4, "h6");
    \u0275\u0275text(5, "Mod\xE9ration");
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(6, "small", 60);
    \u0275\u0275text(7);
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(8, "p", 15);
    \u0275\u0275text(9);
    \u0275\u0275elementEnd();
    \u0275\u0275template(10, AvisModerationComponent_div_49_div_10_div_33_div_2_div_18_div_10_Template, 3, 1, "div", 128);
    \u0275\u0275elementEnd()();
  }
  if (rf & 2) {
    const ctx_r0 = \u0275\u0275nextContext(5);
    \u0275\u0275advance(7);
    \u0275\u0275textInterpolate1(" ", ctx_r0.formatDate((ctx_r0.selectedAvis == null ? null : ctx_r0.selectedAvis.dateModeration) || ""), " ");
    \u0275\u0275advance(2);
    \u0275\u0275textInterpolate1(" Par ", (ctx_r0.selectedAvis == null ? null : ctx_r0.selectedAvis.nomModerateur) || "Administrateur", " ");
    \u0275\u0275advance();
    \u0275\u0275property("ngIf", ctx_r0.selectedAvis == null ? null : ctx_r0.selectedAvis.commentaireModeration);
  }
}
function AvisModerationComponent_div_49_div_10_div_33_div_2_Template(rf, ctx) {
  if (rf & 1) {
    const _r8 = \u0275\u0275getCurrentView();
    \u0275\u0275elementStart(0, "div", 116)(1, "div", 100)(2, "h5", 101);
    \u0275\u0275element(3, "i", 117);
    \u0275\u0275text(4);
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(5, "button", 47);
    \u0275\u0275listener("click", function AvisModerationComponent_div_49_div_10_div_33_div_2_Template_button_click_5_listener() {
      \u0275\u0275restoreView(_r8);
      const ctx_r0 = \u0275\u0275nextContext(4);
      return \u0275\u0275resetView(ctx_r0.closeHistoryModal());
    });
    \u0275\u0275elementEnd()();
    \u0275\u0275elementStart(6, "div", 102)(7, "div", 118)(8, "div", 119)(9, "div", 120);
    \u0275\u0275element(10, "i", 121);
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(11, "div", 122)(12, "h6");
    \u0275\u0275text(13, "Avis publi\xE9");
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(14, "small", 60);
    \u0275\u0275text(15);
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(16, "p", 15);
    \u0275\u0275text(17);
    \u0275\u0275elementEnd()()();
    \u0275\u0275template(18, AvisModerationComponent_div_49_div_10_div_33_div_2_div_18_Template, 11, 3, "div", 123);
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(19, "div", 124)(20, "button", 112);
    \u0275\u0275listener("click", function AvisModerationComponent_div_49_div_10_div_33_div_2_Template_button_click_20_listener() {
      \u0275\u0275restoreView(_r8);
      const ctx_r0 = \u0275\u0275nextContext(4);
      return \u0275\u0275resetView(ctx_r0.closeHistoryModal());
    });
    \u0275\u0275element(21, "i", 125);
    \u0275\u0275text(22, " Moins de d\xE9tails ");
    \u0275\u0275elementEnd()()()();
  }
  if (rf & 2) {
    const ctx_r0 = \u0275\u0275nextContext(4);
    \u0275\u0275advance(4);
    \u0275\u0275textInterpolate1(" Historique de l'avis #", ctx_r0.selectedAvis == null ? null : ctx_r0.selectedAvis.id, " ");
    \u0275\u0275advance(11);
    \u0275\u0275textInterpolate1(" ", ctx_r0.formatDate((ctx_r0.selectedAvis == null ? null : ctx_r0.selectedAvis.datePublication) || ""), " ");
    \u0275\u0275advance(2);
    \u0275\u0275textInterpolate2(" Par ", ctx_r0.selectedAvis == null ? null : ctx_r0.selectedAvis.clientNom, " ", ctx_r0.selectedAvis == null ? null : ctx_r0.selectedAvis.clientPrenom, " ");
    \u0275\u0275advance();
    \u0275\u0275property("ngIf", ctx_r0.selectedAvis == null ? null : ctx_r0.selectedAvis.dateModeration);
  }
}
function AvisModerationComponent_div_49_div_10_div_33_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "div", 96);
    \u0275\u0275template(1, AvisModerationComponent_div_49_div_10_div_33_div_1_Template, 33, 2, "div", 97)(2, AvisModerationComponent_div_49_div_10_div_33_div_2_Template, 23, 5, "div", 98);
    \u0275\u0275elementEnd();
  }
  if (rf & 2) {
    const ctx_r0 = \u0275\u0275nextContext(3);
    \u0275\u0275advance();
    \u0275\u0275property("ngIf", ctx_r0.showSuppressionModal);
    \u0275\u0275advance();
    \u0275\u0275property("ngIf", ctx_r0.showHistoryModal);
  }
}
function AvisModerationComponent_div_49_div_10_Template(rf, ctx) {
  if (rf & 1) {
    const _r3 = \u0275\u0275getCurrentView();
    \u0275\u0275elementStart(0, "div", 61)(1, "div", 62)(2, "div", 63)(3, "div", 64)(4, "div", 65)(5, "h6", 66);
    \u0275\u0275text(6);
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(7, "div", 67);
    \u0275\u0275element(8, "i", 68);
    \u0275\u0275text(9);
    \u0275\u0275elementStart(10, "span", 69);
    \u0275\u0275text(11, "\u2022");
    \u0275\u0275elementEnd();
    \u0275\u0275element(12, "i", 70);
    \u0275\u0275text(13);
    \u0275\u0275elementEnd()();
    \u0275\u0275elementStart(14, "div", 71)(15, "div", 72)(16, "div", 73);
    \u0275\u0275template(17, AvisModerationComponent_div_49_div_10_span_17_Template, 2, 2, "span", 74);
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(18, "span", 75);
    \u0275\u0275text(19);
    \u0275\u0275elementEnd()();
    \u0275\u0275elementStart(20, "span", 76);
    \u0275\u0275element(21, "i", 77);
    \u0275\u0275text(22);
    \u0275\u0275elementEnd()()();
    \u0275\u0275elementStart(23, "div", 78);
    \u0275\u0275template(24, AvisModerationComponent_div_49_div_10_div_24_Template, 4, 1, "div", 79)(25, AvisModerationComponent_div_49_div_10_div_25_Template, 4, 0, "div", 80)(26, AvisModerationComponent_div_49_div_10_div_26_Template, 4, 0, "div", 81);
    \u0275\u0275elementEnd()();
    \u0275\u0275elementStart(27, "div", 82);
    \u0275\u0275template(28, AvisModerationComponent_div_49_div_10_button_28_Template, 4, 0, "button", 83);
    \u0275\u0275elementStart(29, "button", 84);
    \u0275\u0275listener("click", function AvisModerationComponent_div_49_div_10_Template_button_click_29_listener() {
      const avisItem_r5 = \u0275\u0275restoreView(_r3).$implicit;
      const ctx_r0 = \u0275\u0275nextContext(2);
      return \u0275\u0275resetView(ctx_r0.openHistoryModal(avisItem_r5));
    });
    \u0275\u0275element(30, "i", 85);
    \u0275\u0275elementStart(31, "span");
    \u0275\u0275text(32, "Historique");
    \u0275\u0275elementEnd()()()();
    \u0275\u0275template(33, AvisModerationComponent_div_49_div_10_div_33_Template, 3, 2, "div", 86);
    \u0275\u0275elementEnd();
  }
  if (rf & 2) {
    const avisItem_r5 = ctx.$implicit;
    const ctx_r0 = \u0275\u0275nextContext(2);
    \u0275\u0275advance(6);
    \u0275\u0275textInterpolate(avisItem_r5.produitNom);
    \u0275\u0275advance(3);
    \u0275\u0275textInterpolate2(" ", avisItem_r5.clientNom, " ", avisItem_r5.clientPrenom, " ");
    \u0275\u0275advance(4);
    \u0275\u0275textInterpolate1(" ", ctx_r0.formatDate(avisItem_r5.datePublication), " ");
    \u0275\u0275advance(4);
    \u0275\u0275property("ngForOf", \u0275\u0275pureFunction0(14, _c0));
    \u0275\u0275advance(2);
    \u0275\u0275textInterpolate1("", avisItem_r5.note, "/5");
    \u0275\u0275advance();
    \u0275\u0275classMap(ctx_r0.getStatutClass(avisItem_r5.statut));
    \u0275\u0275advance(2);
    \u0275\u0275textInterpolate1(" ", ctx_r0.getStatutText(avisItem_r5.statut), " ");
    \u0275\u0275advance(2);
    \u0275\u0275property("ngIf", avisItem_r5.commentaire && avisItem_r5.commentaire.trim() && !avisItem_r5.commentaireSupprime);
    \u0275\u0275advance();
    \u0275\u0275property("ngIf", avisItem_r5.commentaireSupprime);
    \u0275\u0275advance();
    \u0275\u0275property("ngIf", (!avisItem_r5.commentaire || !avisItem_r5.commentaire.trim()) && !avisItem_r5.commentaireSupprime);
    \u0275\u0275advance(2);
    \u0275\u0275property("ngIf", ctx_r0.peutSupprimerCommentaire(avisItem_r5));
    \u0275\u0275advance(5);
    \u0275\u0275property("ngIf", (ctx_r0.selectedAvis == null ? null : ctx_r0.selectedAvis.id) === avisItem_r5.id);
  }
}
function AvisModerationComponent_div_49_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "div", 51)(1, "div", 14)(2, "div", 52)(3, "h5", 15);
    \u0275\u0275element(4, "i", 37);
    \u0275\u0275text(5, " Avis \xE0 mod\xE9rer ");
    \u0275\u0275elementStart(6, "span", 53);
    \u0275\u0275text(7);
    \u0275\u0275elementEnd()()()();
    \u0275\u0275elementStart(8, "div", 54);
    \u0275\u0275template(9, AvisModerationComponent_div_49_div_9_Template, 6, 0, "div", 55)(10, AvisModerationComponent_div_49_div_10_Template, 34, 15, "div", 56);
    \u0275\u0275elementEnd()();
  }
  if (rf & 2) {
    const ctx_r0 = \u0275\u0275nextContext();
    \u0275\u0275advance(7);
    \u0275\u0275textInterpolate(ctx_r0.avis.length);
    \u0275\u0275advance(2);
    \u0275\u0275property("ngIf", ctx_r0.avis.length === 0);
    \u0275\u0275advance();
    \u0275\u0275property("ngForOf", ctx_r0.avis)("ngForTrackBy", ctx_r0.trackByAvisId);
  }
}
var AvisModerationComponent = class _AvisModerationComponent {
  avisModerationService;
  avis = [];
  loading = false;
  error = null;
  stats = null;
  // Filtres
  filter = {
    page: 1,
    pageSize: 10,
    sortBy: "datePublication",
    sortDesc: true
  };
  // Énumérations pour le template
  StatutAvis = StatutAvis;
  // Modales inline
  selectedAvis = null;
  showSuppressionModal = false;
  showHistoryModal = false;
  activeAvisId = null;
  // Formulaire de suppression
  suppressionForm = {
    raison: ""
  };
  constructor(avisModerationService) {
    this.avisModerationService = avisModerationService;
  }
  ngOnInit() {
    this.loadAvis();
    this.loadStats();
  }
  loadAvis() {
    this.loading = true;
    this.error = null;
    this.avisModerationService.getAvisForModeration(this.filter).subscribe({
      next: (response) => {
        this.avis = response || [];
        this.loading = false;
      },
      error: (error) => {
        console.error("Erreur lors du chargement des avis:", error);
        this.error = "Erreur lors du chargement des avis";
        this.loading = false;
      }
    });
  }
  loadStats() {
    this.avisModerationService.getAvisStats().subscribe({
      next: (stats) => {
        this.stats = stats;
      },
      error: (error) => {
        console.error("Erreur lors du chargement des statistiques:", error);
      }
    });
  }
  // === SUPPRESSION DE COMMENTAIRE ===
  openSuppressionModal(avis) {
    this.selectedAvis = avis;
    this.activeAvisId = avis.id;
    this.showSuppressionModal = true;
    this.suppressionForm.raison = "";
  }
  closeSuppressionModal() {
    this.showSuppressionModal = false;
    this.selectedAvis = null;
    this.activeAvisId = null;
    this.suppressionForm.raison = "";
  }
  supprimerCommentaire() {
    if (!this.selectedAvis || !this.suppressionForm.raison.trim()) {
      return;
    }
    this.loading = true;
    this.avisModerationService.supprimerCommentaire(this.selectedAvis.id, this.suppressionForm.raison).subscribe({
      next: () => {
        alert(`Commentaire supprim\xE9 avec succ\xE8s. Une notification a \xE9t\xE9 envoy\xE9e au client : "Votre avis \xE9tait supprim\xE9 \xE0 cause de '${this.suppressionForm.raison}' mais ne t'inqui\xE8te pas, votre note est mise en consid\xE9ration"`);
        this.loadAvis();
        this.loadStats();
        this.closeSuppressionModal();
        this.loading = false;
      },
      error: (error) => {
        console.error("Erreur lors de la suppression:", error);
        alert("Erreur lors de la suppression du commentaire");
        this.loading = false;
      }
    });
  }
  // === HISTORIQUE ===
  openHistoryModal(avis) {
    this.selectedAvis = avis;
    this.activeAvisId = avis.id;
    this.showHistoryModal = true;
  }
  closeHistoryModal() {
    this.showHistoryModal = false;
    this.selectedAvis = null;
    this.activeAvisId = null;
  }
  // === UTILITAIRES ===
  peutSupprimerCommentaire(avis) {
    return !!(avis.commentaire && avis.commentaire.trim().length > 0 && !avis.commentaireSupprime);
  }
  peutRestaurerCommentaire(avis) {
    return avis.commentaireSupprime;
  }
  getStatutClass(statut) {
    switch (statut) {
      case StatutAvis.Publie:
        return "badge-success";
      case StatutAvis.CommentaireSupprime:
        return "badge-danger";
      case StatutAvis.Signale:
        return "badge-warning";
      default:
        return "badge-secondary";
    }
  }
  getStatutText(statut) {
    switch (statut) {
      case StatutAvis.Publie:
        return "Publi\xE9";
      case StatutAvis.CommentaireSupprime:
        return "Commentaire supprim\xE9";
      case StatutAvis.Signale:
        return "Signal\xE9";
      default:
        return "Inconnu";
    }
  }
  formatDate(date) {
    if (!date)
      return "";
    const dateObj = typeof date === "string" ? new Date(date) : date;
    return dateObj.toLocaleDateString("fr-FR", {
      day: "2-digit",
      month: "2-digit",
      year: "numeric",
      hour: "2-digit",
      minute: "2-digit"
    });
  }
  // === FILTRES ET PAGINATION ===
  onFilterChange() {
    this.filter.page = 1;
    this.loadAvis();
  }
  onPageChange(page) {
    this.filter.page = page;
    this.loadAvis();
  }
  resetFilters() {
    this.filter = {
      page: 1,
      pageSize: 10,
      sortBy: "datePublication",
      sortDesc: true
    };
    this.loadAvis();
  }
  exportAvis() {
    alert("Fonctionnalit\xE9 d'export \xE0 impl\xE9menter");
  }
  restaurerCommentaire(_avis) {
    alert("Fonctionnalit\xE9 de restauration \xE0 impl\xE9menter");
  }
  // === GESTION DES ERREURS ===
  clearError() {
    this.error = null;
  }
  // === TRACKING ===
  trackByAvisId(_index, avis) {
    return avis.id;
  }
  static \u0275fac = function AvisModerationComponent_Factory(__ngFactoryType__) {
    return new (__ngFactoryType__ || _AvisModerationComponent)(\u0275\u0275directiveInject(AvisModerationService));
  };
  static \u0275cmp = /* @__PURE__ */ \u0275\u0275defineComponent({ type: _AvisModerationComponent, selectors: [["app-avis-moderation"]], decls: 50, vars: 9, consts: [[1, "container-fluid"], [1, "page-header", "mb-4"], [1, "header-content"], [1, "header-title"], [1, "title-icon"], [1, "bi", "bi-shield-check"], [1, "title-text"], [1, "page-title"], [1, "page-subtitle"], [1, "header-actions"], ["title", "Actualiser", 1, "btn", "btn-outline-primary", 3, "click"], [1, "bi", "bi-arrow-clockwise", "me-2"], ["class", "stats-container mb-4", 4, "ngIf"], [1, "card", "mb-4"], [1, "card-header"], [1, "mb-0"], [1, "bi", "bi-funnel", "me-2"], [1, "card-body"], [1, "row", "g-3"], [1, "col-lg-3", "col-md-6"], [1, "form-label"], [1, "bi", "bi-bookmark-check", "me-2"], [1, "form-select", 3, "ngModelChange", "change", "ngModel"], [3, "value"], [1, "bi", "bi-search", "me-2"], ["type", "text", "placeholder", "Produit, client, commentaire...", 1, "form-control", 3, "ngModelChange", "input", "ngModel"], [1, "col-lg-2", "col-md-6"], [1, "d-flex", "gap-2"], ["title", "R\xE9initialiser les filtres", 1, "btn", "btn-outline-secondary", 3, "click"], [1, "bi", "bi-arrow-clockwise", "me-1"], [1, "d-none", "d-lg-inline"], ["class", "alert alert-danger alert-dismissible", 4, "ngIf"], ["class", "text-center py-4", 4, "ngIf"], ["class", "card", 4, "ngIf"], [1, "stats-container", "mb-4"], [1, "stats-card", "stats-primary"], [1, "stats-icon"], [1, "bi", "bi-chat-square-text"], [1, "stats-content"], [1, "stats-number"], [1, "stats-label"], [1, "stats-card", "stats-success"], [1, "bi", "bi-check-circle"], [1, "stats-card", "stats-danger"], [1, "bi", "bi-trash"], [1, "alert", "alert-danger", "alert-dismissible"], [1, "bi", "bi-exclamation-triangle", "me-2"], ["type", "button", 1, "btn-close", 3, "click"], [1, "text-center", "py-4"], ["role", "status", 1, "spinner-border", "text-primary"], [1, "visually-hidden"], [1, "card"], [1, "d-flex", "justify-content-between", "align-items-center"], [1, "badge", "bg-primary", "ms-2"], [1, "card-body", "p-0"], ["class", "text-center py-5", 4, "ngIf"], ["class", "avis-card", 4, "ngFor", "ngForOf", "ngForTrackBy"], [1, "text-center", "py-5"], [1, "bi", "bi-inbox", "display-1", "text-muted"], [1, "text-muted", "mt-3"], [1, "text-muted"], [1, "avis-card"], [1, "avis-card-content"], [1, "avis-main-content"], [1, "avis-header"], [1, "avis-info"], [1, "product-name"], [1, "client-info"], [1, "bi", "bi-person-circle", "me-1"], [1, "date-separator"], [1, "bi", "bi-calendar3", "me-1"], [1, "avis-rating-status"], [1, "rating-display"], [1, "stars"], ["class", "star", 3, "filled", 4, "ngFor", "ngForOf"], [1, "rating-number"], [1, "status-badge"], [1, "bi", "bi-circle-fill", "me-1"], [1, "avis-comment"], ["class", "comment-content", 4, "ngIf"], ["class", "comment-deleted", 4, "ngIf"], ["class", "comment-none", 4, "ngIf"], [1, "avis-actions"], ["class", "action-btn danger-btn", "title", "Supprimer le commentaire", 3, "click", 4, "ngIf"], ["title", "Voir l'historique", 1, "action-btn", "info-btn", 3, "click"], [1, "bi", "bi-clock-history"], ["class", "inline-modals", 4, "ngIf"], [1, "star"], [1, "bi", "bi-star-fill"], [1, "comment-content"], [1, "bi", "bi-chat-quote", "me-2"], [1, "comment-deleted"], [1, "bi", "bi-chat-square-x", "me-2"], [1, "comment-none"], [1, "bi", "bi-star", "me-2"], ["title", "Supprimer le commentaire", 1, "action-btn", "danger-btn", 3, "click"], [1, "inline-modals"], ["class", "inline-modal suppression-modal", 4, "ngIf"], ["class", "inline-modal history-modal", 4, "ngIf"], [1, "inline-modal", "suppression-modal"], [1, "modal-header"], [1, "modal-title"], [1, "modal-body"], [1, "alert", "alert-info"], [1, "bi", "bi-info-circle", "me-2"], [1, "mb-0", "mt-2"], [3, "ngSubmit"], [1, "mb-3"], [1, "bi", "bi-chat-text", "me-1"], [1, "text-danger"], ["name", "raison", "rows", "3", "required", "", "placeholder", "Expliquez pourquoi ce commentaire est supprim\xE9 (ex: contenu inappropri\xE9, spam, etc.)...", 1, "form-control", 3, "ngModelChange", "ngModel"], [1, "d-flex", "justify-content-end", "gap-2"], ["type", "button", 1, "btn", "btn-secondary", 3, "click"], [1, "bi", "bi-x-circle", "me-1"], ["type", "submit", 1, "btn", "btn-danger", 3, "disabled"], [1, "bi", "bi-trash", "me-1"], [1, "inline-modal", "history-modal"], [1, "bi", "bi-clock-history", "me-2"], [1, "timeline"], [1, "timeline-item"], [1, "timeline-marker", "bg-primary"], [1, "bi", "bi-plus-circle", "text-white"], [1, "timeline-content"], ["class", "timeline-item", 4, "ngIf"], [1, "d-flex", "justify-content-center", "mt-4"], [1, "bi", "bi-eye-slash", "me-2"], [1, "timeline-marker", "bg-warning"], [1, "bi", "bi-pencil-square", "text-white"], ["class", "mt-2", 4, "ngIf"], [1, "mt-2"]], template: function AvisModerationComponent_Template(rf, ctx) {
    if (rf & 1) {
      \u0275\u0275elementStart(0, "div", 0)(1, "div", 1)(2, "div", 2)(3, "div", 3)(4, "div", 4);
      \u0275\u0275element(5, "i", 5);
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(6, "div", 6)(7, "h1", 7);
      \u0275\u0275text(8, "Mod\xE9ration des Avis");
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(9, "p", 8);
      \u0275\u0275text(10, "G\xE9rez les avis clients et leurs commentaires");
      \u0275\u0275elementEnd()()();
      \u0275\u0275elementStart(11, "div", 9)(12, "button", 10);
      \u0275\u0275listener("click", function AvisModerationComponent_Template_button_click_12_listener() {
        return ctx.resetFilters();
      });
      \u0275\u0275element(13, "i", 11);
      \u0275\u0275text(14, " Actualiser ");
      \u0275\u0275elementEnd()()()();
      \u0275\u0275template(15, AvisModerationComponent_div_15_Template, 25, 3, "div", 12);
      \u0275\u0275elementStart(16, "div", 13)(17, "div", 14)(18, "h5", 15);
      \u0275\u0275element(19, "i", 16);
      \u0275\u0275text(20, " Filtres ");
      \u0275\u0275elementEnd()();
      \u0275\u0275elementStart(21, "div", 17)(22, "div", 18)(23, "div", 19)(24, "label", 20);
      \u0275\u0275element(25, "i", 21);
      \u0275\u0275text(26, " Statut ");
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(27, "select", 22);
      \u0275\u0275twoWayListener("ngModelChange", function AvisModerationComponent_Template_select_ngModelChange_27_listener($event) {
        \u0275\u0275twoWayBindingSet(ctx.filter.statut, $event) || (ctx.filter.statut = $event);
        return $event;
      });
      \u0275\u0275listener("change", function AvisModerationComponent_Template_select_change_27_listener() {
        return ctx.onFilterChange();
      });
      \u0275\u0275elementStart(28, "option", 23);
      \u0275\u0275text(29, "Tous les statuts");
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(30, "option", 23);
      \u0275\u0275text(31, "\u2705 Publi\xE9");
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(32, "option", 23);
      \u0275\u0275text(33, "\u26A0\uFE0F Signal\xE9");
      \u0275\u0275elementEnd()()();
      \u0275\u0275elementStart(34, "div", 19)(35, "label", 20);
      \u0275\u0275element(36, "i", 24);
      \u0275\u0275text(37, " Recherche ");
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(38, "input", 25);
      \u0275\u0275twoWayListener("ngModelChange", function AvisModerationComponent_Template_input_ngModelChange_38_listener($event) {
        \u0275\u0275twoWayBindingSet(ctx.filter.recherche, $event) || (ctx.filter.recherche = $event);
        return $event;
      });
      \u0275\u0275listener("input", function AvisModerationComponent_Template_input_input_38_listener() {
        return ctx.onFilterChange();
      });
      \u0275\u0275elementEnd()();
      \u0275\u0275elementStart(39, "div", 26)(40, "label", 20);
      \u0275\u0275text(41, "\xA0");
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(42, "div", 27)(43, "button", 28);
      \u0275\u0275listener("click", function AvisModerationComponent_Template_button_click_43_listener() {
        return ctx.resetFilters();
      });
      \u0275\u0275element(44, "i", 29);
      \u0275\u0275elementStart(45, "span", 30);
      \u0275\u0275text(46, "R\xE9initialiser");
      \u0275\u0275elementEnd()()()()()()();
      \u0275\u0275template(47, AvisModerationComponent_div_47_Template, 4, 1, "div", 31)(48, AvisModerationComponent_div_48_Template, 4, 0, "div", 32)(49, AvisModerationComponent_div_49_Template, 11, 4, "div", 33);
      \u0275\u0275elementEnd();
    }
    if (rf & 2) {
      \u0275\u0275advance(15);
      \u0275\u0275property("ngIf", ctx.stats);
      \u0275\u0275advance(12);
      \u0275\u0275twoWayProperty("ngModel", ctx.filter.statut);
      \u0275\u0275advance();
      \u0275\u0275property("value", void 0);
      \u0275\u0275advance(2);
      \u0275\u0275property("value", ctx.StatutAvis.Publie);
      \u0275\u0275advance(2);
      \u0275\u0275property("value", ctx.StatutAvis.Signale);
      \u0275\u0275advance(6);
      \u0275\u0275twoWayProperty("ngModel", ctx.filter.recherche);
      \u0275\u0275advance(9);
      \u0275\u0275property("ngIf", ctx.error);
      \u0275\u0275advance();
      \u0275\u0275property("ngIf", ctx.loading);
      \u0275\u0275advance();
      \u0275\u0275property("ngIf", !ctx.loading);
    }
  }, dependencies: [CommonModule, NgForOf, NgIf, FormsModule, \u0275NgNoValidate, NgSelectOption, \u0275NgSelectMultipleOption, DefaultValueAccessor, SelectControlValueAccessor, NgControlStatus, NgControlStatusGroup, RequiredValidator, NgModel, NgForm], styles: ['\n\n[_ngcontent-%COMP%]:root {\n  --primary-color: #007bff;\n  --secondary-color: #6c757d;\n  --success-color: #28a745;\n  --error-color: #dc3545;\n  --warning-color: #ffc107;\n  --info-color: #17a2b8;\n  --accent-color: #28a745;\n  --text-color: #333333;\n  --background-color: #ffffff;\n  --border-color: #e0e0e0;\n  --gradient-primary:\n    linear-gradient(\n      135deg,\n      #007bff,\n      rgb(51, 149.4, 255));\n  --primary-color-hover: rgb(0, 98.4, 204);\n  --accent-color-hover: rgb(30.1449275362, 125.8550724638, 52);\n}\n.btn-primary[_ngcontent-%COMP%] {\n  background: var(--gradient-primary);\n  border: none;\n  transition: all 0.3s ease;\n}\n.btn-primary[_ngcontent-%COMP%]:hover {\n  background: var(--primary-color-hover);\n  transform: translateY(-1px);\n}\n.btn-success[_ngcontent-%COMP%] {\n  background-color: var(--success-color);\n  border-color: var(--success-color);\n}\n.btn-success[_ngcontent-%COMP%]:hover {\n  background-color: var(--accent-color-hover);\n  border-color: var(--accent-color-hover);\n}\n.alert-success[_ngcontent-%COMP%] {\n  background-color: rgba(40, 167, 69, 0.1);\n  border-color: var(--success-color);\n  color: var(--success-color);\n}\n.alert-danger[_ngcontent-%COMP%] {\n  background-color: rgba(220, 53, 69, 0.1);\n  border-color: var(--error-color);\n  color: var(--error-color);\n}\n.alert-warning[_ngcontent-%COMP%] {\n  background-color: rgba(255, 193, 7, 0.1);\n  border-color: var(--warning-color);\n  color: #856404;\n}\n.alert-info[_ngcontent-%COMP%] {\n  background-color: rgba(23, 162, 184, 0.1);\n  border-color: var(--info-color);\n  color: var(--info-color);\n}\n.form-control[_ngcontent-%COMP%] {\n  background-color: var(--card-background-color);\n  border-color: var(--border-color);\n  color: var(--text-color);\n  transition: all 0.3s ease;\n}\n.form-control[_ngcontent-%COMP%]:focus {\n  background-color: var(--card-background-color);\n  border-color: var(--primary-color);\n  color: var(--text-color);\n  box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);\n}\n.table[_ngcontent-%COMP%] {\n  background-color: var(--card-background-color);\n  color: var(--text-color);\n}\n.table[_ngcontent-%COMP%]   th[_ngcontent-%COMP%] {\n  background-color: var(--primary-color);\n  color: white;\n  border: none;\n}\n.table[_ngcontent-%COMP%]   td[_ngcontent-%COMP%] {\n  border-color: var(--border-color);\n}\n.table-striped[_ngcontent-%COMP%]   tbody[_ngcontent-%COMP%]   tr[_ngcontent-%COMP%]:nth-of-type(odd) {\n  background-color: rgba(0, 0, 0, 0.05);\n}\n[data-theme=dark][_ngcontent-%COMP%]   .table-striped[_ngcontent-%COMP%]   tbody[_ngcontent-%COMP%]   tr[_ngcontent-%COMP%]:nth-of-type(odd) {\n  background-color: rgba(255, 255, 255, 0.05);\n}\n.navbar[_ngcontent-%COMP%] {\n  background-color: var(--card-background-color) !important;\n  border-bottom: 1px solid var(--border-color);\n  box-shadow: var(--card-shadow);\n}\n.navbar-brand[_ngcontent-%COMP%], \n.navbar-nav[_ngcontent-%COMP%]   .nav-link[_ngcontent-%COMP%] {\n  color: var(--text-color) !important;\n  transition: color 0.3s ease;\n}\n.navbar-nav[_ngcontent-%COMP%]   .nav-link[_ngcontent-%COMP%]:hover {\n  color: var(--primary-color) !important;\n}\n.sidebar[_ngcontent-%COMP%] {\n  background-color: var(--sidebar-bg);\n  color: var(--sidebar-text);\n  transition: all 0.3s ease;\n}\n.sidebar[_ngcontent-%COMP%]   .nav-link[_ngcontent-%COMP%] {\n  color: var(--sidebar-text);\n  transition: all 0.3s ease;\n  border-radius: 0.375rem;\n  margin: 0.25rem 0;\n}\n.sidebar[_ngcontent-%COMP%]   .nav-link[_ngcontent-%COMP%]:hover {\n  background-color: var(--sidebar-hover);\n  color: var(--sidebar-text);\n}\n.sidebar[_ngcontent-%COMP%]   .nav-link.active[_ngcontent-%COMP%] {\n  background-color: var(--primary-color);\n  color: white;\n}\n.badge[_ngcontent-%COMP%] {\n  font-size: 0.75em;\n  padding: 0.375rem 0.75rem;\n}\n.badge-success[_ngcontent-%COMP%] {\n  background-color: var(--success-color);\n}\n.badge-danger[_ngcontent-%COMP%] {\n  background-color: var(--error-color);\n}\n.badge-warning[_ngcontent-%COMP%] {\n  background-color: var(--warning-color);\n  color: #212529;\n}\n.badge-info[_ngcontent-%COMP%] {\n  background-color: var(--info-color);\n}\n@keyframes _ngcontent-%COMP%_fadeIn {\n  from {\n    opacity: 0;\n    transform: translateY(20px);\n  }\n  to {\n    opacity: 1;\n    transform: translateY(0);\n  }\n}\n@keyframes _ngcontent-%COMP%_slideIn {\n  from {\n    transform: translateX(-100%);\n  }\n  to {\n    transform: translateX(0);\n  }\n}\n.fade-in[_ngcontent-%COMP%] {\n  animation: _ngcontent-%COMP%_fadeIn 0.5s ease-out;\n}\n.slide-in[_ngcontent-%COMP%] {\n  animation: _ngcontent-%COMP%_slideIn 0.3s ease-out;\n}\n@media (max-width: 768px) {\n  .card[_ngcontent-%COMP%] {\n    margin-bottom: 1rem;\n  }\n  .table-responsive[_ngcontent-%COMP%] {\n    font-size: 0.875rem;\n  }\n}\n.modal-content[_ngcontent-%COMP%] {\n  background-color: var(--card-background-color);\n  border: 1px solid var(--border-color);\n}\n.modal-header[_ngcontent-%COMP%] {\n  border-bottom: 1px solid var(--border-color);\n}\n.modal-footer[_ngcontent-%COMP%] {\n  border-top: 1px solid var(--border-color);\n}\n.dropdown-menu[_ngcontent-%COMP%] {\n  background-color: var(--card-background-color);\n  border: 1px solid var(--border-color);\n  box-shadow: var(--card-shadow);\n}\n.dropdown-item[_ngcontent-%COMP%] {\n  color: var(--text-color);\n  transition: all 0.3s ease;\n}\n.dropdown-item[_ngcontent-%COMP%]:hover {\n  background-color: var(--card-background-color-hover);\n  color: var(--text-color-hover);\n}\n.tooltip[_ngcontent-%COMP%]   .tooltip-inner[_ngcontent-%COMP%] {\n  background-color: var(--sidebar-bg);\n  color: var(--sidebar-text);\n}\n.progress[_ngcontent-%COMP%] {\n  background-color: var(--border-color);\n}\n.progress-bar[_ngcontent-%COMP%] {\n  background-color: var(--primary-color);\n}\n.container-fluid[_ngcontent-%COMP%] {\n  background:\n    linear-gradient(\n      135deg,\n      #f5f7fa 0%,\n      #c3cfe2 100%);\n  min-height: 100vh;\n  padding: 2rem;\n  position: relative;\n}\n.container-fluid[_ngcontent-%COMP%]::before {\n  content: "";\n  position: absolute;\n  top: 0;\n  left: 0;\n  right: 0;\n  height: 300px;\n  background:\n    linear-gradient(\n      135deg,\n      rgba(102, 126, 234, 0.1) 0%,\n      rgba(118, 75, 162, 0.1) 100%);\n  z-index: 0;\n}\n.container-fluid[_ngcontent-%COMP%]    > *[_ngcontent-%COMP%] {\n  position: relative;\n  z-index: 1;\n}\n@media (max-width: 768px) {\n  .container-fluid[_ngcontent-%COMP%] {\n    padding: 1rem;\n  }\n}\n.page-header[_ngcontent-%COMP%] {\n  background:\n    linear-gradient(\n      135deg,\n      #667eea 0%,\n      #764ba2 100%);\n  border-radius: 24px;\n  padding: 3rem 2.5rem;\n  margin-bottom: 3rem;\n  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.1);\n  color: white;\n  position: relative;\n  overflow: hidden;\n}\n.page-header[_ngcontent-%COMP%]::before {\n  content: "";\n  position: absolute;\n  top: -50%;\n  right: -20%;\n  width: 200px;\n  height: 200px;\n  background:\n    radial-gradient(\n      circle,\n      rgba(255, 255, 255, 0.1) 0%,\n      transparent 70%);\n  border-radius: 50%;\n}\n.page-header[_ngcontent-%COMP%]::after {\n  content: "";\n  position: absolute;\n  bottom: -30%;\n  left: -10%;\n  width: 150px;\n  height: 150px;\n  background:\n    radial-gradient(\n      circle,\n      rgba(255, 255, 255, 0.08) 0%,\n      transparent 70%);\n  border-radius: 50%;\n}\n.page-header[_ngcontent-%COMP%]   .header-content[_ngcontent-%COMP%] {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  position: relative;\n  z-index: 2;\n}\n@media (max-width: 768px) {\n  .page-header[_ngcontent-%COMP%]   .header-content[_ngcontent-%COMP%] {\n    flex-direction: column;\n    gap: 2rem;\n    text-align: center;\n  }\n}\n.page-header[_ngcontent-%COMP%]   .header-title[_ngcontent-%COMP%] {\n  display: flex;\n  align-items: center;\n  gap: 1.5rem;\n}\n.page-header[_ngcontent-%COMP%]   .header-title[_ngcontent-%COMP%]   .title-icon[_ngcontent-%COMP%] {\n  background: rgba(255, 255, 255, 0.2);\n  -webkit-backdrop-filter: blur(10px);\n  backdrop-filter: blur(10px);\n  border-radius: 50%;\n  width: 80px;\n  height: 80px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  font-size: 2rem;\n  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);\n  border: 1px solid rgba(255, 255, 255, 0.1);\n}\n.page-header[_ngcontent-%COMP%]   .header-title[_ngcontent-%COMP%]   .title-text[_ngcontent-%COMP%]   .page-title[_ngcontent-%COMP%] {\n  font-size: 3rem;\n  font-weight: 800;\n  margin: 0;\n  text-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);\n  background:\n    linear-gradient(\n      45deg,\n      #fff,\n      rgba(255, 255, 255, 0.8));\n  -webkit-background-clip: text;\n  -webkit-text-fill-color: transparent;\n  background-clip: text;\n}\n@media (max-width: 768px) {\n  .page-header[_ngcontent-%COMP%]   .header-title[_ngcontent-%COMP%]   .title-text[_ngcontent-%COMP%]   .page-title[_ngcontent-%COMP%] {\n    font-size: 2.2rem;\n  }\n}\n.page-header[_ngcontent-%COMP%]   .header-title[_ngcontent-%COMP%]   .title-text[_ngcontent-%COMP%]   .page-subtitle[_ngcontent-%COMP%] {\n  font-size: 1.2rem;\n  opacity: 0.9;\n  margin: 0.5rem 0 0 0;\n  font-weight: 300;\n  letter-spacing: 0.5px;\n}\n.page-header[_ngcontent-%COMP%]   .header-actions[_ngcontent-%COMP%]   .btn[_ngcontent-%COMP%] {\n  background: rgba(255, 255, 255, 0.15);\n  -webkit-backdrop-filter: blur(10px);\n  backdrop-filter: blur(10px);\n  border: 2px solid rgba(255, 255, 255, 0.2);\n  color: white;\n  border-radius: 12px;\n  padding: 1rem 2rem;\n  font-weight: 600;\n  font-size: 1rem;\n  transition: all 0.4s cubic-bezier(0.25, 0.8, 0.25, 1);\n  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);\n}\n.page-header[_ngcontent-%COMP%]   .header-actions[_ngcontent-%COMP%]   .btn[_ngcontent-%COMP%]:hover {\n  background: rgba(255, 255, 255, 0.25);\n  border-color: rgba(255, 255, 255, 0.4);\n  transform: translateY(-3px);\n  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.15);\n}\n.page-header[_ngcontent-%COMP%]   .header-actions[_ngcontent-%COMP%]   .btn[_ngcontent-%COMP%]:active {\n  transform: translateY(-1px);\n}\n.stats-container[_ngcontent-%COMP%] {\n  display: flex;\n  gap: 1.5rem;\n  margin-bottom: 3rem;\n  overflow-x: auto;\n  padding-bottom: 0.5rem;\n}\n@media (max-width: 768px) {\n  .stats-container[_ngcontent-%COMP%] {\n    flex-direction: column;\n    gap: 1rem;\n  }\n}\n.stats-container[_ngcontent-%COMP%]::-webkit-scrollbar {\n  height: 4px;\n}\n.stats-container[_ngcontent-%COMP%]::-webkit-scrollbar-track {\n  background: rgba(255, 255, 255, 0.1);\n  border-radius: 2px;\n}\n.stats-container[_ngcontent-%COMP%]::-webkit-scrollbar-thumb {\n  background: rgba(102, 126, 234, 0.3);\n  border-radius: 2px;\n}\n.stats-container[_ngcontent-%COMP%]::-webkit-scrollbar-thumb:hover {\n  background: rgba(102, 126, 234, 0.5);\n}\n.stats-card[_ngcontent-%COMP%] {\n  background: rgba(255, 255, 255, 0.25);\n  -webkit-backdrop-filter: blur(20px);\n  backdrop-filter: blur(20px);\n  border: 1px solid rgba(255, 255, 255, 0.18);\n  border-radius: 16px;\n  padding: 2rem 1.5rem;\n  box-shadow: 0 10px 40px rgba(0, 0, 0, 0.1);\n  transition: all 0.4s cubic-bezier(0.25, 0.8, 0.25, 1);\n  flex: 1;\n  min-width: 220px;\n  display: flex;\n  align-items: center;\n  gap: 1.5rem;\n  position: relative;\n  overflow: hidden;\n}\n.stats-card[_ngcontent-%COMP%]::before {\n  content: "";\n  position: absolute;\n  top: 0;\n  left: 0;\n  right: 0;\n  height: 4px;\n  background:\n    linear-gradient(\n      90deg,\n      transparent,\n      currentColor,\n      transparent);\n  opacity: 0.6;\n}\n.stats-card[_ngcontent-%COMP%]:hover {\n  transform: translateY(-8px) scale(1.02);\n  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.1);\n  background: rgba(255, 255, 255, 0.35);\n  border-color: rgba(255, 255, 255, 0.3);\n}\n.stats-card[_ngcontent-%COMP%]   .stats-icon[_ngcontent-%COMP%] {\n  width: 60px;\n  height: 60px;\n  border-radius: 50%;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  font-size: 1.8rem;\n  color: white;\n  position: relative;\n}\n.stats-card[_ngcontent-%COMP%]   .stats-icon[_ngcontent-%COMP%]::after {\n  content: "";\n  position: absolute;\n  inset: -2px;\n  border-radius: 50%;\n  padding: 2px;\n  background:\n    linear-gradient(\n      45deg,\n      currentColor,\n      transparent,\n      currentColor);\n  mask: linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0);\n  mask-composite: exclude;\n  opacity: 0.3;\n}\n.stats-card[_ngcontent-%COMP%]   .stats-content[_ngcontent-%COMP%] {\n  flex: 1;\n}\n.stats-card[_ngcontent-%COMP%]   .stats-content[_ngcontent-%COMP%]   .stats-number[_ngcontent-%COMP%] {\n  font-size: 2.5rem;\n  font-weight: 800;\n  line-height: 1;\n  margin-bottom: 0.5rem;\n}\n.stats-card[_ngcontent-%COMP%]   .stats-content[_ngcontent-%COMP%]   .stats-label[_ngcontent-%COMP%] {\n  font-size: 1rem;\n  color: rgba(0, 0, 0, 0.7);\n  font-weight: 600;\n  text-transform: uppercase;\n  letter-spacing: 1px;\n}\n.stats-card.stats-primary[_ngcontent-%COMP%] {\n  color: #667eea;\n}\n.stats-card.stats-primary[_ngcontent-%COMP%]   .stats-icon[_ngcontent-%COMP%] {\n  background:\n    linear-gradient(\n      135deg,\n      #007bff 0%,\n      rgb(76.5, 162.6, 255) 50%,\n      rgb(0, 110.7, 229.5) 100%);\n}\n.stats-card.stats-success[_ngcontent-%COMP%] {\n  color: #11998e;\n}\n.stats-card.stats-success[_ngcontent-%COMP%]   .stats-icon[_ngcontent-%COMP%] {\n  background:\n    linear-gradient(\n      135deg,\n      #11998e 0%,\n      #38ef7d 100%);\n}\n.stats-card.stats-warning[_ngcontent-%COMP%] {\n  color: #f093fb;\n}\n.stats-card.stats-warning[_ngcontent-%COMP%]   .stats-icon[_ngcontent-%COMP%] {\n  background:\n    linear-gradient(\n      135deg,\n      #f093fb 0%,\n      #f5576c 100%);\n}\n.stats-card.stats-danger[_ngcontent-%COMP%] {\n  color: #fc466b;\n}\n.stats-card.stats-danger[_ngcontent-%COMP%]   .stats-icon[_ngcontent-%COMP%] {\n  background:\n    linear-gradient(\n      135deg,\n      #fc466b 0%,\n      #3f5efb 100%);\n}\n.card[_ngcontent-%COMP%] {\n  border: none;\n  border-radius: 16px;\n  box-shadow: 0 10px 40px rgba(0, 0, 0, 0.1);\n  transition: all 0.4s cubic-bezier(0.25, 0.8, 0.25, 1);\n  background: rgba(255, 255, 255, 0.9);\n  -webkit-backdrop-filter: blur(20px);\n  backdrop-filter: blur(20px);\n}\n.card[_ngcontent-%COMP%]:hover {\n  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.1);\n  transform: translateY(-2px);\n}\n.card[_ngcontent-%COMP%]   .card-header[_ngcontent-%COMP%] {\n  background:\n    linear-gradient(\n      135deg,\n      rgba(102, 126, 234, 0.1),\n      rgba(118, 75, 162, 0.1));\n  border-bottom: 1px solid rgba(102, 126, 234, 0.2);\n  border-radius: 16px 16px 0 0;\n  padding: 1.5rem;\n}\n.card[_ngcontent-%COMP%]   .card-header[_ngcontent-%COMP%]   h5[_ngcontent-%COMP%] {\n  color: #667eea;\n  font-weight: 700;\n  margin: 0;\n  font-size: 1.3rem;\n}\n.card[_ngcontent-%COMP%]   .card-header[_ngcontent-%COMP%]   h5[_ngcontent-%COMP%]   .bi[_ngcontent-%COMP%] {\n  margin-right: 0.75rem;\n  background: rgba(102, 126, 234, 0.2);\n  padding: 0.5rem;\n  border-radius: 50%;\n  font-size: 1rem;\n}\n.card[_ngcontent-%COMP%]   .card-body[_ngcontent-%COMP%] {\n  padding: 2rem;\n}\n.card[_ngcontent-%COMP%]   .card-body[_ngcontent-%COMP%]   .form-label[_ngcontent-%COMP%] {\n  font-weight: 600;\n  color: #333;\n  margin-bottom: 0.75rem;\n  font-size: 1rem;\n}\n.card[_ngcontent-%COMP%]   .card-body[_ngcontent-%COMP%]   .form-label[_ngcontent-%COMP%]   .bi[_ngcontent-%COMP%] {\n  color: #667eea;\n  margin-right: 0.5rem;\n}\n.card[_ngcontent-%COMP%]   .card-body[_ngcontent-%COMP%]   .form-control[_ngcontent-%COMP%], \n.card[_ngcontent-%COMP%]   .card-body[_ngcontent-%COMP%]   .form-select[_ngcontent-%COMP%] {\n  border: 2px solid rgba(102, 126, 234, 0.2);\n  border-radius: 12px;\n  padding: 0.875rem 1.25rem;\n  transition: all 0.4s cubic-bezier(0.25, 0.8, 0.25, 1);\n  font-size: 1rem;\n  background: rgba(255, 255, 255, 0.8);\n}\n.card[_ngcontent-%COMP%]   .card-body[_ngcontent-%COMP%]   .form-control[_ngcontent-%COMP%]:focus, \n.card[_ngcontent-%COMP%]   .card-body[_ngcontent-%COMP%]   .form-select[_ngcontent-%COMP%]:focus {\n  border-color: #667eea;\n  box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);\n  background: white;\n}\n.card[_ngcontent-%COMP%]   .card-body[_ngcontent-%COMP%]   .form-control[_ngcontent-%COMP%]::placeholder, \n.card[_ngcontent-%COMP%]   .card-body[_ngcontent-%COMP%]   .form-select[_ngcontent-%COMP%]::placeholder {\n  color: rgba(0, 0, 0, 0.5);\n}\n.card[_ngcontent-%COMP%]   .card-body[_ngcontent-%COMP%]   .btn[_ngcontent-%COMP%] {\n  border-radius: 12px;\n  padding: 0.875rem 1.5rem;\n  font-weight: 600;\n  transition: all 0.4s cubic-bezier(0.25, 0.8, 0.25, 1);\n  text-transform: uppercase;\n  letter-spacing: 0.5px;\n}\n.card[_ngcontent-%COMP%]   .card-body[_ngcontent-%COMP%]   .btn[_ngcontent-%COMP%]:hover {\n  transform: translateY(-2px);\n  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);\n}\n.card[_ngcontent-%COMP%]   .card-body[_ngcontent-%COMP%]   .btn.btn-outline-secondary[_ngcontent-%COMP%] {\n  border-color: #667eea;\n  color: #667eea;\n}\n.card[_ngcontent-%COMP%]   .card-body[_ngcontent-%COMP%]   .btn.btn-outline-secondary[_ngcontent-%COMP%]:hover {\n  background:\n    linear-gradient(\n      135deg,\n      #667eea 0%,\n      #764ba2 100%);\n  border-color: transparent;\n  color: white;\n}\n.avis-card[_ngcontent-%COMP%] {\n  background: rgba(255, 255, 255, 0.9);\n  -webkit-backdrop-filter: blur(20px);\n  backdrop-filter: blur(20px);\n  border: 1px solid rgba(255, 255, 255, 0.2);\n  border-radius: 16px;\n  box-shadow: 0 10px 40px rgba(0, 0, 0, 0.1);\n  margin-bottom: 2rem;\n  overflow: hidden;\n  transition: all 0.4s cubic-bezier(0.25, 0.8, 0.25, 1);\n  position: relative;\n}\n.avis-card[_ngcontent-%COMP%]::before {\n  content: "";\n  position: absolute;\n  top: 0;\n  left: 0;\n  right: 0;\n  height: 2px;\n  background:\n    linear-gradient(\n      135deg,\n      #667eea 0%,\n      #764ba2 100%);\n  opacity: 0.8;\n}\n.avis-card[_ngcontent-%COMP%]:hover {\n  transform: translateY(-10px);\n  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.1);\n  background: rgba(255, 255, 255, 0.95);\n  border-color: rgba(255, 255, 255, 0.3);\n}\n.avis-card[_ngcontent-%COMP%]   .avis-card-content[_ngcontent-%COMP%] {\n  display: flex;\n  min-height: 220px;\n}\n@media (max-width: 768px) {\n  .avis-card[_ngcontent-%COMP%]   .avis-card-content[_ngcontent-%COMP%] {\n    flex-direction: column;\n  }\n}\n.avis-card[_ngcontent-%COMP%]   .avis-main-content[_ngcontent-%COMP%] {\n  flex: 1;\n  padding: 2rem;\n}\n.avis-card[_ngcontent-%COMP%]   .avis-header[_ngcontent-%COMP%] {\n  display: flex;\n  justify-content: space-between;\n  align-items: flex-start;\n  margin-bottom: 1.5rem;\n}\n@media (max-width: 768px) {\n  .avis-card[_ngcontent-%COMP%]   .avis-header[_ngcontent-%COMP%] {\n    flex-direction: column;\n    gap: 1rem;\n  }\n}\n.avis-card[_ngcontent-%COMP%]   .avis-header[_ngcontent-%COMP%]   .avis-info[_ngcontent-%COMP%] {\n  flex: 1;\n}\n.avis-card[_ngcontent-%COMP%]   .avis-header[_ngcontent-%COMP%]   .avis-info[_ngcontent-%COMP%]   .product-name[_ngcontent-%COMP%] {\n  font-size: 1.4rem;\n  font-weight: 700;\n  color: #667eea;\n  margin-bottom: 0.75rem;\n  background:\n    linear-gradient(\n      45deg,\n      #667eea,\n      #764ba2);\n  -webkit-background-clip: text;\n  -webkit-text-fill-color: transparent;\n  background-clip: text;\n}\n.avis-card[_ngcontent-%COMP%]   .avis-header[_ngcontent-%COMP%]   .avis-info[_ngcontent-%COMP%]   .client-info[_ngcontent-%COMP%] {\n  color: rgba(0, 0, 0, 0.6);\n  font-size: 1rem;\n  display: flex;\n  align-items: center;\n  gap: 0.75rem;\n  font-weight: 500;\n}\n.avis-card[_ngcontent-%COMP%]   .avis-header[_ngcontent-%COMP%]   .avis-info[_ngcontent-%COMP%]   .client-info[_ngcontent-%COMP%]   .date-separator[_ngcontent-%COMP%] {\n  margin: 0 0.25rem;\n  color: #667eea;\n}\n.avis-card[_ngcontent-%COMP%]   .avis-header[_ngcontent-%COMP%]   .avis-info[_ngcontent-%COMP%]   .client-info[_ngcontent-%COMP%]   .bi[_ngcontent-%COMP%] {\n  color: #667eea;\n  font-size: 1.1rem;\n}\n.avis-card[_ngcontent-%COMP%]   .avis-header[_ngcontent-%COMP%]   .avis-rating-status[_ngcontent-%COMP%] {\n  text-align: right;\n}\n@media (max-width: 768px) {\n  .avis-card[_ngcontent-%COMP%]   .avis-header[_ngcontent-%COMP%]   .avis-rating-status[_ngcontent-%COMP%] {\n    text-align: left;\n  }\n}\n.avis-card[_ngcontent-%COMP%]   .avis-header[_ngcontent-%COMP%]   .avis-rating-status[_ngcontent-%COMP%]   .rating-display[_ngcontent-%COMP%] {\n  margin-bottom: 1rem;\n}\n.avis-card[_ngcontent-%COMP%]   .avis-header[_ngcontent-%COMP%]   .avis-rating-status[_ngcontent-%COMP%]   .rating-display[_ngcontent-%COMP%]   .stars[_ngcontent-%COMP%] {\n  display: flex;\n  gap: 0.25rem;\n  margin-bottom: 0.75rem;\n  justify-content: flex-end;\n}\n@media (max-width: 768px) {\n  .avis-card[_ngcontent-%COMP%]   .avis-header[_ngcontent-%COMP%]   .avis-rating-status[_ngcontent-%COMP%]   .rating-display[_ngcontent-%COMP%]   .stars[_ngcontent-%COMP%] {\n    justify-content: flex-start;\n  }\n}\n.avis-card[_ngcontent-%COMP%]   .avis-header[_ngcontent-%COMP%]   .avis-rating-status[_ngcontent-%COMP%]   .rating-display[_ngcontent-%COMP%]   .stars[_ngcontent-%COMP%]   .star[_ngcontent-%COMP%] {\n  font-size: 1.3rem;\n  color: #e0e0e0;\n  transition: all 0.2s ease-out;\n  filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.1));\n}\n.avis-card[_ngcontent-%COMP%]   .avis-header[_ngcontent-%COMP%]   .avis-rating-status[_ngcontent-%COMP%]   .rating-display[_ngcontent-%COMP%]   .stars[_ngcontent-%COMP%]   .star.filled[_ngcontent-%COMP%] {\n  color: #ffd700;\n  text-shadow: 0 0 10px rgba(255, 215, 0, 0.5);\n}\n.avis-card[_ngcontent-%COMP%]   .avis-header[_ngcontent-%COMP%]   .avis-rating-status[_ngcontent-%COMP%]   .rating-display[_ngcontent-%COMP%]   .rating-number[_ngcontent-%COMP%] {\n  font-weight: 700;\n  color: #333;\n  font-size: 1.1rem;\n  background:\n    linear-gradient(\n      45deg,\n      #ffd700,\n      #ffed4e);\n  -webkit-background-clip: text;\n  -webkit-text-fill-color: transparent;\n  background-clip: text;\n}\n.avis-card[_ngcontent-%COMP%]   .avis-header[_ngcontent-%COMP%]   .avis-rating-status[_ngcontent-%COMP%]   .status-badge[_ngcontent-%COMP%] {\n  display: inline-flex;\n  align-items: center;\n  padding: 0.5rem 1rem;\n  border-radius: 25px;\n  font-size: 0.85rem;\n  font-weight: 700;\n  text-transform: uppercase;\n  letter-spacing: 0.5px;\n  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);\n  border: 2px solid transparent;\n}\n.avis-card[_ngcontent-%COMP%]   .avis-header[_ngcontent-%COMP%]   .avis-rating-status[_ngcontent-%COMP%]   .status-badge[_ngcontent-%COMP%]   .bi[_ngcontent-%COMP%] {\n  font-size: 0.7rem;\n  margin-right: 0.5rem;\n}\n.avis-card[_ngcontent-%COMP%]   .avis-header[_ngcontent-%COMP%]   .avis-rating-status[_ngcontent-%COMP%]   .status-badge.badge-success[_ngcontent-%COMP%] {\n  background:\n    linear-gradient(\n      135deg,\n      #11998e 0%,\n      #38ef7d 100%);\n  color: white;\n  border-color: rgba(17, 153, 142, 0.3);\n}\n.avis-card[_ngcontent-%COMP%]   .avis-header[_ngcontent-%COMP%]   .avis-rating-status[_ngcontent-%COMP%]   .status-badge.badge-warning[_ngcontent-%COMP%] {\n  background:\n    linear-gradient(\n      135deg,\n      #f093fb 0%,\n      #f5576c 100%);\n  color: white;\n  border-color: rgba(240, 147, 251, 0.3);\n}\n.avis-card[_ngcontent-%COMP%]   .avis-header[_ngcontent-%COMP%]   .avis-rating-status[_ngcontent-%COMP%]   .status-badge.badge-danger[_ngcontent-%COMP%] {\n  background:\n    linear-gradient(\n      135deg,\n      #fc466b 0%,\n      #3f5efb 100%);\n  color: white;\n  border-color: rgba(252, 70, 107, 0.3);\n}\n.avis-card[_ngcontent-%COMP%]   .avis-header[_ngcontent-%COMP%]   .avis-rating-status[_ngcontent-%COMP%]   .status-badge.badge-secondary[_ngcontent-%COMP%] {\n  background:\n    linear-gradient(\n      135deg,\n      #6c757d,\n      #495057);\n  color: white;\n  border-color: rgba(108, 117, 125, 0.3);\n}\n.avis-card[_ngcontent-%COMP%]   .avis-comment[_ngcontent-%COMP%] {\n  background:\n    linear-gradient(\n      135deg,\n      rgba(102, 126, 234, 0.05),\n      rgba(118, 75, 162, 0.05));\n  border-radius: 12px;\n  padding: 1.5rem;\n  margin-bottom: 1.5rem;\n  border-left: 4px solid #667eea;\n  position: relative;\n}\n.avis-card[_ngcontent-%COMP%]   .avis-comment[_ngcontent-%COMP%]   .comment-content[_ngcontent-%COMP%] {\n  display: flex;\n  align-items: flex-start;\n  gap: 1rem;\n  font-style: italic;\n  line-height: 1.7;\n  font-size: 1.05rem;\n  color: rgba(0, 0, 0, 0.8);\n}\n.avis-card[_ngcontent-%COMP%]   .avis-comment[_ngcontent-%COMP%]   .comment-content[_ngcontent-%COMP%]   .bi[_ngcontent-%COMP%] {\n  color: #667eea;\n  margin-top: 0.25rem;\n  flex-shrink: 0;\n  font-size: 1.2rem;\n}\n.avis-card[_ngcontent-%COMP%]   .avis-comment[_ngcontent-%COMP%]   .comment-deleted[_ngcontent-%COMP%] {\n  display: flex;\n  align-items: center;\n  gap: 1rem;\n  color: rgba(252, 70, 107, 0.8);\n  font-style: italic;\n}\n.avis-card[_ngcontent-%COMP%]   .avis-comment[_ngcontent-%COMP%]   .comment-none[_ngcontent-%COMP%] {\n  display: flex;\n  align-items: center;\n  gap: 1rem;\n  color: rgba(108, 117, 125, 0.8);\n  font-style: italic;\n  font-weight: 600;\n}\n.avis-card[_ngcontent-%COMP%]   .avis-comment[_ngcontent-%COMP%]   .comment-none[_ngcontent-%COMP%]   .bi[_ngcontent-%COMP%] {\n  color: #fc466b;\n  font-size: 1.2rem;\n}\n.avis-card[_ngcontent-%COMP%]   .avis-actions[_ngcontent-%COMP%] {\n  background:\n    linear-gradient(\n      135deg,\n      rgba(102, 126, 234, 0.05),\n      rgba(118, 75, 162, 0.05));\n  -webkit-backdrop-filter: blur(10px);\n  backdrop-filter: blur(10px);\n  padding: 2rem;\n  border-left: 1px solid rgba(102, 126, 234, 0.2);\n  display: flex;\n  flex-direction: column;\n  gap: 1rem;\n  justify-content: center;\n  min-width: 220px;\n}\n@media (max-width: 768px) {\n  .avis-card[_ngcontent-%COMP%]   .avis-actions[_ngcontent-%COMP%] {\n    border-left: none;\n    border-top: 1px solid rgba(102, 126, 234, 0.2);\n    flex-direction: row;\n    min-width: auto;\n  }\n}\n.avis-card[_ngcontent-%COMP%]   .avis-actions[_ngcontent-%COMP%]   .action-btn[_ngcontent-%COMP%] {\n  display: flex;\n  align-items: center;\n  gap: 0.75rem;\n  padding: 1rem 1.5rem;\n  border-radius: 12px;\n  border: 2px solid;\n  background: transparent;\n  font-weight: 700;\n  font-size: 0.95rem;\n  transition: all 0.4s cubic-bezier(0.25, 0.8, 0.25, 1);\n  cursor: pointer;\n  text-decoration: none;\n  justify-content: center;\n  text-transform: uppercase;\n  letter-spacing: 0.5px;\n  position: relative;\n  overflow: hidden;\n}\n.avis-card[_ngcontent-%COMP%]   .avis-actions[_ngcontent-%COMP%]   .action-btn[_ngcontent-%COMP%]::before {\n  content: "";\n  position: absolute;\n  top: 0;\n  left: -100%;\n  width: 100%;\n  height: 100%;\n  background:\n    linear-gradient(\n      90deg,\n      transparent,\n      rgba(255, 255, 255, 0.2),\n      transparent);\n  transition: left 0.5s;\n}\n.avis-card[_ngcontent-%COMP%]   .avis-actions[_ngcontent-%COMP%]   .action-btn[_ngcontent-%COMP%]:hover::before {\n  left: 100%;\n}\n.avis-card[_ngcontent-%COMP%]   .avis-actions[_ngcontent-%COMP%]   .action-btn[_ngcontent-%COMP%]:hover {\n  transform: translateY(-3px);\n  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);\n}\n.avis-card[_ngcontent-%COMP%]   .avis-actions[_ngcontent-%COMP%]   .action-btn.danger-btn[_ngcontent-%COMP%] {\n  border-color: #fc466b;\n  color: #fc466b;\n}\n.avis-card[_ngcontent-%COMP%]   .avis-actions[_ngcontent-%COMP%]   .action-btn.danger-btn[_ngcontent-%COMP%]:hover {\n  background:\n    linear-gradient(\n      135deg,\n      #fc466b 0%,\n      #3f5efb 100%);\n  color: white;\n  border-color: transparent;\n}\n.avis-card[_ngcontent-%COMP%]   .avis-actions[_ngcontent-%COMP%]   .action-btn.info-btn[_ngcontent-%COMP%] {\n  border-color: #667eea;\n  color: #667eea;\n}\n.avis-card[_ngcontent-%COMP%]   .avis-actions[_ngcontent-%COMP%]   .action-btn.info-btn[_ngcontent-%COMP%]:hover {\n  background:\n    linear-gradient(\n      135deg,\n      #667eea 0%,\n      #764ba2 100%);\n  color: white;\n  border-color: transparent;\n}\n.avis-card[_ngcontent-%COMP%]   .avis-actions[_ngcontent-%COMP%]   .action-btn[_ngcontent-%COMP%]   .bi[_ngcontent-%COMP%] {\n  font-size: 1.1rem;\n}\n.inline-modal[_ngcontent-%COMP%] {\n  background: rgba(255, 255, 255, 0.95);\n  -webkit-backdrop-filter: blur(30px);\n  backdrop-filter: blur(30px);\n  border: 1px solid rgba(255, 255, 255, 0.2);\n  border-radius: 16px;\n  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.1);\n  margin-top: 1.5rem;\n  overflow: hidden;\n  animation: _ngcontent-%COMP%_slideDownElegant 0.5s cubic-bezier(0.25, 0.8, 0.25, 1);\n}\n.inline-modal[_ngcontent-%COMP%]   .modal-header[_ngcontent-%COMP%] {\n  background:\n    linear-gradient(\n      135deg,\n      rgba(102, 126, 234, 0.1),\n      rgba(118, 75, 162, 0.1));\n  padding: 2rem;\n  border-bottom: 1px solid rgba(102, 126, 234, 0.2);\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n}\n.inline-modal[_ngcontent-%COMP%]   .modal-header[_ngcontent-%COMP%]   .modal-title[_ngcontent-%COMP%] {\n  font-weight: 700;\n  color: #667eea;\n  margin: 0;\n  display: flex;\n  align-items: center;\n  font-size: 1.3rem;\n}\n.inline-modal[_ngcontent-%COMP%]   .modal-header[_ngcontent-%COMP%]   .modal-title[_ngcontent-%COMP%]   .bi[_ngcontent-%COMP%] {\n  margin-right: 0.75rem;\n  background: rgba(102, 126, 234, 0.2);\n  padding: 0.5rem;\n  border-radius: 50%;\n  font-size: 1rem;\n}\n.inline-modal[_ngcontent-%COMP%]   .modal-header[_ngcontent-%COMP%]   .btn-close[_ngcontent-%COMP%] {\n  background: none;\n  border: none;\n  font-size: 1.5rem;\n  cursor: pointer;\n  color: #667eea;\n  transition: all 0.4s cubic-bezier(0.25, 0.8, 0.25, 1);\n  width: 40px;\n  height: 40px;\n  border-radius: 50%;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n}\n.inline-modal[_ngcontent-%COMP%]   .modal-header[_ngcontent-%COMP%]   .btn-close[_ngcontent-%COMP%]:hover {\n  background: rgba(102, 126, 234, 0.1);\n  transform: scale(1.1);\n}\n.inline-modal[_ngcontent-%COMP%]   .modal-body[_ngcontent-%COMP%] {\n  padding: 2rem;\n}\n.inline-modal[_ngcontent-%COMP%]   .modal-body[_ngcontent-%COMP%]   .alert[_ngcontent-%COMP%] {\n  border-radius: 12px;\n  border: none;\n  padding: 1.5rem;\n  margin-bottom: 1.5rem;\n}\n.inline-modal[_ngcontent-%COMP%]   .modal-body[_ngcontent-%COMP%]   .alert.alert-info[_ngcontent-%COMP%] {\n  background:\n    linear-gradient(\n      135deg,\n      rgba(102, 126, 234, 0.1),\n      rgba(118, 75, 162, 0.1));\n  color: #667eea;\n  border-left: 4px solid #667eea;\n}\n.inline-modal[_ngcontent-%COMP%]   .modal-body[_ngcontent-%COMP%]   .alert.alert-info[_ngcontent-%COMP%]   .bi[_ngcontent-%COMP%] {\n  color: #667eea;\n  margin-right: 0.75rem;\n}\n.inline-modal[_ngcontent-%COMP%]   .modal-body[_ngcontent-%COMP%]   .alert[_ngcontent-%COMP%]   ul[_ngcontent-%COMP%] {\n  margin-bottom: 0;\n  padding-left: 1.5rem;\n}\n.inline-modal[_ngcontent-%COMP%]   .modal-body[_ngcontent-%COMP%]   .form-label[_ngcontent-%COMP%] {\n  font-weight: 700;\n  color: #333;\n  margin-bottom: 0.75rem;\n  display: flex;\n  align-items: center;\n  font-size: 1.1rem;\n}\n.inline-modal[_ngcontent-%COMP%]   .modal-body[_ngcontent-%COMP%]   .form-label[_ngcontent-%COMP%]   .bi[_ngcontent-%COMP%] {\n  margin-right: 0.75rem;\n  color: #667eea;\n  background: rgba(102, 126, 234, 0.1);\n  padding: 0.5rem;\n  border-radius: 50%;\n  font-size: 1rem;\n}\n.inline-modal[_ngcontent-%COMP%]   .modal-body[_ngcontent-%COMP%]   .form-label[_ngcontent-%COMP%]   .text-danger[_ngcontent-%COMP%] {\n  margin-left: 0.5rem;\n  color: #fc466b;\n}\n.inline-modal[_ngcontent-%COMP%]   .modal-body[_ngcontent-%COMP%]   .form-control[_ngcontent-%COMP%] {\n  border: 2px solid rgba(102, 126, 234, 0.2);\n  border-radius: 12px;\n  padding: 1rem 1.25rem;\n  transition: all 0.4s cubic-bezier(0.25, 0.8, 0.25, 1);\n  font-size: 1rem;\n  background: rgba(255, 255, 255, 0.8);\n  min-height: 120px;\n}\n.inline-modal[_ngcontent-%COMP%]   .modal-body[_ngcontent-%COMP%]   .form-control[_ngcontent-%COMP%]:focus {\n  border-color: #667eea;\n  box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);\n  background: white;\n}\n.inline-modal[_ngcontent-%COMP%]   .modal-body[_ngcontent-%COMP%]   .form-control[_ngcontent-%COMP%]::placeholder {\n  color: rgba(0, 0, 0, 0.5);\n  font-style: italic;\n}\n.inline-modal[_ngcontent-%COMP%]   .modal-body[_ngcontent-%COMP%]   .btn[_ngcontent-%COMP%] {\n  border-radius: 12px;\n  padding: 1rem 2rem;\n  font-weight: 700;\n  font-size: 1rem;\n  transition: all 0.4s cubic-bezier(0.25, 0.8, 0.25, 1);\n  display: flex;\n  align-items: center;\n  gap: 0.75rem;\n  text-transform: uppercase;\n  letter-spacing: 0.5px;\n}\n.inline-modal[_ngcontent-%COMP%]   .modal-body[_ngcontent-%COMP%]   .btn[_ngcontent-%COMP%]:hover {\n  transform: translateY(-3px);\n  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);\n}\n.inline-modal[_ngcontent-%COMP%]   .modal-body[_ngcontent-%COMP%]   .btn.btn-secondary[_ngcontent-%COMP%] {\n  background:\n    linear-gradient(\n      135deg,\n      #6c757d,\n      #495057);\n  border: none;\n  color: white;\n}\n.inline-modal[_ngcontent-%COMP%]   .modal-body[_ngcontent-%COMP%]   .btn.btn-secondary[_ngcontent-%COMP%]:hover {\n  background:\n    linear-gradient(\n      135deg,\n      #495057,\n      #343a40);\n}\n.inline-modal[_ngcontent-%COMP%]   .modal-body[_ngcontent-%COMP%]   .btn.btn-danger[_ngcontent-%COMP%] {\n  background:\n    linear-gradient(\n      135deg,\n      #fc466b 0%,\n      #3f5efb 100%);\n  border: none;\n  color: white;\n}\n.inline-modal[_ngcontent-%COMP%]   .modal-body[_ngcontent-%COMP%]   .btn.btn-danger[_ngcontent-%COMP%]:hover {\n  background:\n    linear-gradient(\n      135deg,\n      #3f5efb,\n      #fc466b);\n}\n.inline-modal[_ngcontent-%COMP%]   .modal-body[_ngcontent-%COMP%]   .btn[_ngcontent-%COMP%]   .bi[_ngcontent-%COMP%] {\n  font-size: 1.1rem;\n}\n.inline-modal[_ngcontent-%COMP%]   .modal-body[_ngcontent-%COMP%]   .d-flex.gap-2[_ngcontent-%COMP%] {\n  gap: 1rem !important;\n  justify-content: flex-end;\n  margin-top: 2rem;\n}\n.inline-modal.suppression-modal[_ngcontent-%COMP%] {\n  border-left: 4px solid #fc466b;\n}\n.inline-modal.history-modal[_ngcontent-%COMP%] {\n  border-left: 4px solid #667eea;\n}\n.timeline[_ngcontent-%COMP%] {\n  position: relative;\n  padding-left: 2.5rem;\n}\n.timeline[_ngcontent-%COMP%]::before {\n  content: "";\n  position: absolute;\n  left: 15px;\n  top: 0;\n  bottom: 0;\n  width: 3px;\n  background:\n    linear-gradient(\n      135deg,\n      #667eea 0%,\n      #764ba2 100%);\n  border-radius: 2px;\n}\n.timeline[_ngcontent-%COMP%]   .timeline-item[_ngcontent-%COMP%] {\n  position: relative;\n  margin-bottom: 2rem;\n}\n.timeline[_ngcontent-%COMP%]   .timeline-item[_ngcontent-%COMP%]   .timeline-marker[_ngcontent-%COMP%] {\n  position: absolute;\n  left: -25px;\n  top: 8px;\n  width: 35px;\n  height: 35px;\n  border-radius: 50%;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  color: white;\n  font-size: 0.9rem;\n  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);\n  border: 3px solid white;\n}\n.timeline[_ngcontent-%COMP%]   .timeline-item[_ngcontent-%COMP%]   .timeline-marker.bg-primary[_ngcontent-%COMP%] {\n  background:\n    linear-gradient(\n      135deg,\n      #667eea 0%,\n      #764ba2 100%);\n}\n.timeline[_ngcontent-%COMP%]   .timeline-item[_ngcontent-%COMP%]   .timeline-marker.bg-warning[_ngcontent-%COMP%] {\n  background:\n    linear-gradient(\n      135deg,\n      #f093fb 0%,\n      #f5576c 100%);\n}\n.timeline[_ngcontent-%COMP%]   .timeline-item[_ngcontent-%COMP%]   .timeline-content[_ngcontent-%COMP%] {\n  background: rgba(255, 255, 255, 0.8);\n  -webkit-backdrop-filter: blur(10px);\n  backdrop-filter: blur(10px);\n  padding: 1.5rem;\n  border-radius: 12px;\n  border-left: 4px solid #667eea;\n  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);\n}\n.timeline[_ngcontent-%COMP%]   .timeline-item[_ngcontent-%COMP%]   .timeline-content[_ngcontent-%COMP%]   h6[_ngcontent-%COMP%] {\n  margin-bottom: 0.5rem;\n  font-weight: 700;\n  color: #667eea;\n  font-size: 1.1rem;\n}\n.timeline[_ngcontent-%COMP%]   .timeline-item[_ngcontent-%COMP%]   .timeline-content[_ngcontent-%COMP%]   small[_ngcontent-%COMP%] {\n  color: rgba(0, 0, 0, 0.6);\n  font-size: 0.9rem;\n  font-weight: 500;\n}\n.timeline[_ngcontent-%COMP%]   .timeline-item[_ngcontent-%COMP%]   .timeline-content[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\n  margin: 0.75rem 0 0 0;\n  line-height: 1.6;\n  color: rgba(0, 0, 0, 0.8);\n}\n@keyframes _ngcontent-%COMP%_slideDownElegant {\n  from {\n    opacity: 0;\n    transform: translateY(-30px) scale(0.95);\n  }\n  to {\n    opacity: 1;\n    transform: translateY(0) scale(1);\n  }\n}\n.loading-spinner[_ngcontent-%COMP%] {\n  display: flex;\n  justify-content: center;\n  align-items: center;\n  padding: 4rem;\n}\n.loading-spinner[_ngcontent-%COMP%]   .spinner-border[_ngcontent-%COMP%] {\n  width: 4rem;\n  height: 4rem;\n  border-width: 4px;\n  color: #667eea;\n}\n.empty-state[_ngcontent-%COMP%] {\n  text-align: center;\n  padding: 4rem 2rem;\n  color: rgba(0, 0, 0, 0.6);\n}\n.empty-state[_ngcontent-%COMP%]   .bi[_ngcontent-%COMP%] {\n  font-size: 5rem;\n  margin-bottom: 1.5rem;\n  opacity: 0.3;\n  color: #667eea;\n}\n.empty-state[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%] {\n  margin-bottom: 0.75rem;\n  font-weight: 700;\n  color: #333;\n  font-size: 1.5rem;\n}\n.empty-state[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\n  margin: 0;\n  font-size: 1.1rem;\n  font-weight: 500;\n}\n.alert[_ngcontent-%COMP%] {\n  border-radius: 16px;\n  border: none;\n  padding: 1.5rem;\n}\n.alert.alert-danger[_ngcontent-%COMP%] {\n  background:\n    linear-gradient(\n      135deg,\n      rgba(252, 70, 107, 0.1),\n      rgba(63, 94, 251, 0.1));\n  color: #fc466b;\n  border-left: 4px solid #fc466b;\n}\n.alert.alert-danger[_ngcontent-%COMP%]   .bi[_ngcontent-%COMP%] {\n  color: #fc466b;\n  margin-right: 0.75rem;\n}\n.alert[_ngcontent-%COMP%]   .btn-close[_ngcontent-%COMP%] {\n  background: none;\n  border: none;\n  opacity: 0.7;\n  transition: all 0.4s cubic-bezier(0.25, 0.8, 0.25, 1);\n  width: 30px;\n  height: 30px;\n  border-radius: 50%;\n}\n.alert[_ngcontent-%COMP%]   .btn-close[_ngcontent-%COMP%]:hover {\n  opacity: 1;\n  background: rgba(0, 0, 0, 0.05);\n}\n@media (max-width: 576px) {\n  .container-fluid[_ngcontent-%COMP%] {\n    padding: 1rem 0.75rem;\n  }\n  .page-header[_ngcontent-%COMP%] {\n    padding: 2rem 1.5rem;\n    margin-bottom: 2rem;\n  }\n  .page-header[_ngcontent-%COMP%]   .title-text[_ngcontent-%COMP%]   .page-title[_ngcontent-%COMP%] {\n    font-size: 2rem;\n  }\n  .page-header[_ngcontent-%COMP%]   .title-icon[_ngcontent-%COMP%] {\n    width: 60px;\n    height: 60px;\n    font-size: 1.5rem;\n  }\n  .stats-container[_ngcontent-%COMP%] {\n    margin-bottom: 2rem;\n  }\n  .stats-card[_ngcontent-%COMP%] {\n    padding: 1.5rem 1rem;\n    min-width: auto;\n  }\n  .stats-card[_ngcontent-%COMP%]   .stats-icon[_ngcontent-%COMP%] {\n    width: 50px;\n    height: 50px;\n    font-size: 1.5rem;\n  }\n  .stats-card[_ngcontent-%COMP%]   .stats-content[_ngcontent-%COMP%]   .stats-number[_ngcontent-%COMP%] {\n    font-size: 2rem;\n  }\n  .avis-card[_ngcontent-%COMP%] {\n    margin-bottom: 1.5rem;\n  }\n  .avis-card[_ngcontent-%COMP%]   .avis-main-content[_ngcontent-%COMP%], \n   .avis-card[_ngcontent-%COMP%]   .avis-actions[_ngcontent-%COMP%] {\n    padding: 1.5rem;\n  }\n  .avis-card[_ngcontent-%COMP%]   .avis-actions[_ngcontent-%COMP%] {\n    gap: 0.75rem;\n  }\n  .avis-card[_ngcontent-%COMP%]   .avis-actions[_ngcontent-%COMP%]   .action-btn[_ngcontent-%COMP%] {\n    padding: 0.75rem 1rem;\n    font-size: 0.9rem;\n  }\n  .inline-modal[_ngcontent-%COMP%]   .modal-header[_ngcontent-%COMP%], \n   .inline-modal[_ngcontent-%COMP%]   .modal-body[_ngcontent-%COMP%] {\n    padding: 1.5rem;\n  }\n}\n/*# sourceMappingURL=avis-moderation.component.css.map */'] });
};
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && setClassMetadata(AvisModerationComponent, [{
    type: Component,
    args: [{ selector: "app-avis-moderation", standalone: true, imports: [CommonModule, FormsModule], template: `<div class="container-fluid">
  <!-- En-t\xEAte moderne -->
  <div class="page-header mb-4">
    <div class="header-content">
      <div class="header-title">
        <div class="title-icon">
          <i class="bi bi-shield-check"></i>
        </div>
        <div class="title-text">
          <h1 class="page-title">Mod\xE9ration des Avis</h1>
          <p class="page-subtitle">G\xE9rez les avis clients et leurs commentaires</p>
        </div>
      </div>
      <div class="header-actions">
        <button class="btn btn-outline-primary" (click)="resetFilters()" title="Actualiser">
          <i class="bi bi-arrow-clockwise me-2"></i>
          Actualiser
        </button>
      </div>
    </div>
  </div>

  <!-- Statistiques en ligne -->
  <div class="stats-container mb-4" *ngIf="stats">
    <div class="stats-card stats-primary">
      <div class="stats-icon">
        <i class="bi bi-chat-square-text"></i>
      </div>
      <div class="stats-content">
        <div class="stats-number">{{ stats.totalAvis }}</div>
        <div class="stats-label">Total Avis</div>
      </div>
    </div>

    <div class="stats-card stats-success">
      <div class="stats-icon">
        <i class="bi bi-check-circle"></i>
      </div>
      <div class="stats-content">
        <div class="stats-number">{{ stats.avisPublies }}</div>
        <div class="stats-label">Publi\xE9s</div>
      </div>
    </div>

    <div class="stats-card stats-danger">
      <div class="stats-icon">
        <i class="bi bi-trash"></i>
      </div>
      <div class="stats-content">
        <div class="stats-number">{{ stats.avisCommentaireSupprime || 0 }}</div>
        <div class="stats-label">Commentaires supprim\xE9s</div>
      </div>
    </div>
  </div>

  <!-- Filtres -->
  <div class="card mb-4">
    <div class="card-header">
      <h5 class="mb-0">
        <i class="bi bi-funnel me-2"></i>
        Filtres
      </h5>
    </div>
    <div class="card-body">
      <div class="row g-3">
        <div class="col-lg-3 col-md-6">
          <label class="form-label">
            <i class="bi bi-bookmark-check me-2"></i>
            Statut
          </label>
          <select class="form-select" [(ngModel)]="filter.statut" (change)="onFilterChange()">
            <option [value]="undefined">Tous les statuts</option>
            <option [value]="StatutAvis.Publie">\u2705 Publi\xE9</option>
            <option [value]="StatutAvis.Signale">\u26A0\uFE0F Signal\xE9</option>
          </select>
        </div>
        <div class="col-lg-3 col-md-6">
          <label class="form-label">
            <i class="bi bi-search me-2"></i>
            Recherche
          </label>
          <input type="text" class="form-control" placeholder="Produit, client, commentaire..."
                 [(ngModel)]="filter.recherche" (input)="onFilterChange()">
        </div>
        <div class="col-lg-2 col-md-6">
          <label class="form-label">&nbsp;</label>
          <div class="d-flex gap-2">
            <button class="btn btn-outline-secondary" (click)="resetFilters()" title="R\xE9initialiser les filtres">
              <i class="bi bi-arrow-clockwise me-1"></i>
              <span class="d-none d-lg-inline">R\xE9initialiser</span>
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Message d'erreur -->
  <div *ngIf="error" class="alert alert-danger alert-dismissible">
    <i class="bi bi-exclamation-triangle me-2"></i>
    {{ error }}
    <button type="button" class="btn-close" (click)="clearError()"></button>
  </div>

  <!-- Loading -->
  <div *ngIf="loading" class="text-center py-4">
    <div class="spinner-border text-primary" role="status">
      <span class="visually-hidden">Chargement...</span>
    </div>
  </div>

  <!-- Liste des avis -->
  <div class="card" *ngIf="!loading">
    <div class="card-header">
      <div class="d-flex justify-content-between align-items-center">
        <h5 class="mb-0">
          <i class="bi bi-chat-square-text"></i>
          Avis \xE0 mod\xE9rer
          <span class="badge bg-primary ms-2">{{ avis.length }}</span>
        </h5>
      </div>
    </div>
    <div class="card-body p-0">
      <div *ngIf="avis.length === 0" class="text-center py-5">
        <i class="bi bi-inbox display-1 text-muted"></i>
        <h4 class="text-muted mt-3">Aucun avis trouv\xE9</h4>
        <p class="text-muted">Aucun avis ne correspond aux crit\xE8res de recherche.</p>
      </div>

      <div *ngFor="let avisItem of avis; trackBy: trackByAvisId" class="avis-card">
        <div class="avis-card-content">
          <div class="avis-main-content">
            <!-- En-t\xEAte de l'avis -->
            <div class="avis-header">
              <div class="avis-info">
                <h6 class="product-name">{{ avisItem.produitNom }}</h6>
                <div class="client-info">
                  <i class="bi bi-person-circle me-1"></i>
                  {{ avisItem.clientNom }} {{ avisItem.clientPrenom }}
                  <span class="date-separator">\u2022</span>
                  <i class="bi bi-calendar3 me-1"></i>
                  {{ formatDate(avisItem.datePublication) }}
                </div>
              </div>
              <div class="avis-rating-status">
                <!-- Note avec \xE9toiles -->
                <div class="rating-display">
                  <div class="stars">
                    <span *ngFor="let i of [1,2,3,4,5]"
                          class="star"
                          [class.filled]="i <= avisItem.note">
                      <i class="bi bi-star-fill"></i>
                    </span>
                  </div>
                  <span class="rating-number">{{ avisItem.note }}/5</span>
                </div>
                <!-- Statut -->
                <span class="status-badge" [class]="getStatutClass(avisItem.statut)">
                  <i class="bi bi-circle-fill me-1"></i>
                  {{ getStatutText(avisItem.statut) }}
                </span>
              </div>
            </div>

            <!-- Commentaire -->
            <div class="avis-comment">
              <!-- Commentaire pr\xE9sent et non supprim\xE9 -->
              <div *ngIf="avisItem.commentaire && avisItem.commentaire.trim() && !avisItem.commentaireSupprime"
                   class="comment-content">
                <i class="bi bi-chat-quote me-2"></i>
                <span>{{ avisItem.commentaire }}</span>
              </div>
              <!-- Commentaire supprim\xE9 par l'admin -->
              <div *ngIf="avisItem.commentaireSupprime"
                   class="comment-deleted">
                <i class="bi bi-chat-square-x me-2"></i>
                <span>Commentaire supprim\xE9</span>
              </div>
              <!-- Avis sans commentaire (note seule) -->
              <div *ngIf="(!avisItem.commentaire || !avisItem.commentaire.trim()) && !avisItem.commentaireSupprime"
                   class="comment-none">
                <i class="bi bi-star me-2"></i>
                <span>Note sans commentaire</span>
              </div>
            </div>
          </div>

          <!-- Actions -->
          <div class="avis-actions">
            <button class="action-btn danger-btn"
                    *ngIf="peutSupprimerCommentaire(avisItem)"
                    (click)="openSuppressionModal(avisItem)"
                    title="Supprimer le commentaire">
              <i class="bi bi-trash"></i>
              <span>Supprimer</span>
            </button>

            <button class="action-btn info-btn"
                    (click)="openHistoryModal(avisItem)"
                    title="Voir l'historique">
              <i class="bi bi-clock-history"></i>
              <span>Historique</span>
            </button>
          </div>
        </div>

        <!-- Modales inline pour cet avis -->
        <div class="inline-modals" *ngIf="selectedAvis?.id === avisItem.id">

          <!-- Modal de suppression de commentaire inline -->
          <div *ngIf="showSuppressionModal" class="inline-modal suppression-modal">
            <div class="modal-header">
              <h5 class="modal-title">
                <i class="bi bi-exclamation-triangle me-2"></i>
                Supprimer le commentaire
              </h5>
              <button type="button" class="btn-close" (click)="closeSuppressionModal()"></button>
            </div>
            <div class="modal-body">
              <div class="alert alert-info">
                <i class="bi bi-info-circle me-2"></i>
                <strong>Cette action va :</strong>
                <ul class="mb-0 mt-2">
                  <li>Supprimer uniquement le commentaire (la note reste visible)</li>
                  <li>Envoyer une notification au client : <em>"Votre avis \xE9tait supprim\xE9 \xE0 cause de [raison] mais ne t'inqui\xE8te pas, votre note est mise en consid\xE9ration"</em></li>
                </ul>
              </div>
              <form (ngSubmit)="supprimerCommentaire()">
                <div class="mb-3">
                  <label class="form-label">
                    <i class="bi bi-chat-text me-1"></i>
                    Raison de la suppression <span class="text-danger">*</span>
                  </label>
                  <textarea class="form-control" [(ngModel)]="suppressionForm.raison"
                            name="raison" rows="3" required
                            placeholder="Expliquez pourquoi ce commentaire est supprim\xE9 (ex: contenu inappropri\xE9, spam, etc.)..."></textarea>
                </div>
                <div class="d-flex justify-content-end gap-2">
                  <button type="button" class="btn btn-secondary" (click)="closeSuppressionModal()">
                    <i class="bi bi-x-circle me-1"></i>
                    Annuler
                  </button>
                  <button type="submit" class="btn btn-danger" [disabled]="!suppressionForm.raison || !suppressionForm.raison.trim()">
                    <i class="bi bi-trash me-1"></i>
                    Supprimer et notifier
                  </button>
                </div>
              </form>
            </div>
          </div>

          <!-- Modal d'historique inline -->
          <div *ngIf="showHistoryModal" class="inline-modal history-modal">
            <div class="modal-header">
              <h5 class="modal-title">
                <i class="bi bi-clock-history me-2"></i>
                Historique de l'avis #{{ selectedAvis?.id }}
              </h5>
              <button type="button" class="btn-close" (click)="closeHistoryModal()"></button>
            </div>
            <div class="modal-body">
              <div class="timeline">
                <div class="timeline-item">
                  <div class="timeline-marker bg-primary">
                    <i class="bi bi-plus-circle text-white"></i>
                  </div>
                  <div class="timeline-content">
                    <h6>Avis publi\xE9</h6>
                    <small class="text-muted">
                      {{ formatDate(selectedAvis?.datePublication || '') }}
                    </small>
                    <p class="mb-0">
                      Par {{ selectedAvis?.clientNom }} {{ selectedAvis?.clientPrenom }}
                    </p>
                  </div>
                </div>
                
                <div *ngIf="selectedAvis?.dateModeration" class="timeline-item">
                  <div class="timeline-marker bg-warning">
                    <i class="bi bi-pencil-square text-white"></i>
                  </div>
                  <div class="timeline-content">
                    <h6>Mod\xE9ration</h6>
                    <small class="text-muted">
                      {{ formatDate(selectedAvis?.dateModeration || '') }}
                    </small>
                    <p class="mb-0">
                      Par {{ selectedAvis?.nomModerateur || 'Administrateur' }}
                    </p>
                    <div *ngIf="selectedAvis?.commentaireModeration" class="mt-2">
                      <small class="text-muted">{{ selectedAvis?.commentaireModeration }}</small>
                    </div>
                  </div>
                </div>
              </div>

              <!-- Bouton pour fermer l'historique -->
              <div class="d-flex justify-content-center mt-4">
                <button type="button"
                        class="btn btn-secondary"
                        (click)="closeHistoryModal()">
                  <i class="bi bi-eye-slash me-2"></i>
                  Moins de d\xE9tails
                </button>
              </div>
            </div>
          </div>

        </div>
      </div>
    </div>
  </div>
</div>
`, styles: ['/* src/app/components/admin/avis-moderation/avis-moderation.component.scss */\n:root {\n  --primary-color: #007bff;\n  --secondary-color: #6c757d;\n  --success-color: #28a745;\n  --error-color: #dc3545;\n  --warning-color: #ffc107;\n  --info-color: #17a2b8;\n  --accent-color: #28a745;\n  --text-color: #333333;\n  --background-color: #ffffff;\n  --border-color: #e0e0e0;\n  --gradient-primary:\n    linear-gradient(\n      135deg,\n      #007bff,\n      rgb(51, 149.4, 255));\n  --primary-color-hover: rgb(0, 98.4, 204);\n  --accent-color-hover: rgb(30.1449275362, 125.8550724638, 52);\n}\n.btn-primary {\n  background: var(--gradient-primary);\n  border: none;\n  transition: all 0.3s ease;\n}\n.btn-primary:hover {\n  background: var(--primary-color-hover);\n  transform: translateY(-1px);\n}\n.btn-success {\n  background-color: var(--success-color);\n  border-color: var(--success-color);\n}\n.btn-success:hover {\n  background-color: var(--accent-color-hover);\n  border-color: var(--accent-color-hover);\n}\n.alert-success {\n  background-color: rgba(40, 167, 69, 0.1);\n  border-color: var(--success-color);\n  color: var(--success-color);\n}\n.alert-danger {\n  background-color: rgba(220, 53, 69, 0.1);\n  border-color: var(--error-color);\n  color: var(--error-color);\n}\n.alert-warning {\n  background-color: rgba(255, 193, 7, 0.1);\n  border-color: var(--warning-color);\n  color: #856404;\n}\n.alert-info {\n  background-color: rgba(23, 162, 184, 0.1);\n  border-color: var(--info-color);\n  color: var(--info-color);\n}\n.form-control {\n  background-color: var(--card-background-color);\n  border-color: var(--border-color);\n  color: var(--text-color);\n  transition: all 0.3s ease;\n}\n.form-control:focus {\n  background-color: var(--card-background-color);\n  border-color: var(--primary-color);\n  color: var(--text-color);\n  box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);\n}\n.table {\n  background-color: var(--card-background-color);\n  color: var(--text-color);\n}\n.table th {\n  background-color: var(--primary-color);\n  color: white;\n  border: none;\n}\n.table td {\n  border-color: var(--border-color);\n}\n.table-striped tbody tr:nth-of-type(odd) {\n  background-color: rgba(0, 0, 0, 0.05);\n}\n[data-theme=dark] .table-striped tbody tr:nth-of-type(odd) {\n  background-color: rgba(255, 255, 255, 0.05);\n}\n.navbar {\n  background-color: var(--card-background-color) !important;\n  border-bottom: 1px solid var(--border-color);\n  box-shadow: var(--card-shadow);\n}\n.navbar-brand,\n.navbar-nav .nav-link {\n  color: var(--text-color) !important;\n  transition: color 0.3s ease;\n}\n.navbar-nav .nav-link:hover {\n  color: var(--primary-color) !important;\n}\n.sidebar {\n  background-color: var(--sidebar-bg);\n  color: var(--sidebar-text);\n  transition: all 0.3s ease;\n}\n.sidebar .nav-link {\n  color: var(--sidebar-text);\n  transition: all 0.3s ease;\n  border-radius: 0.375rem;\n  margin: 0.25rem 0;\n}\n.sidebar .nav-link:hover {\n  background-color: var(--sidebar-hover);\n  color: var(--sidebar-text);\n}\n.sidebar .nav-link.active {\n  background-color: var(--primary-color);\n  color: white;\n}\n.badge {\n  font-size: 0.75em;\n  padding: 0.375rem 0.75rem;\n}\n.badge-success {\n  background-color: var(--success-color);\n}\n.badge-danger {\n  background-color: var(--error-color);\n}\n.badge-warning {\n  background-color: var(--warning-color);\n  color: #212529;\n}\n.badge-info {\n  background-color: var(--info-color);\n}\n@keyframes fadeIn {\n  from {\n    opacity: 0;\n    transform: translateY(20px);\n  }\n  to {\n    opacity: 1;\n    transform: translateY(0);\n  }\n}\n@keyframes slideIn {\n  from {\n    transform: translateX(-100%);\n  }\n  to {\n    transform: translateX(0);\n  }\n}\n.fade-in {\n  animation: fadeIn 0.5s ease-out;\n}\n.slide-in {\n  animation: slideIn 0.3s ease-out;\n}\n@media (max-width: 768px) {\n  .card {\n    margin-bottom: 1rem;\n  }\n  .table-responsive {\n    font-size: 0.875rem;\n  }\n}\n.modal-content {\n  background-color: var(--card-background-color);\n  border: 1px solid var(--border-color);\n}\n.modal-header {\n  border-bottom: 1px solid var(--border-color);\n}\n.modal-footer {\n  border-top: 1px solid var(--border-color);\n}\n.dropdown-menu {\n  background-color: var(--card-background-color);\n  border: 1px solid var(--border-color);\n  box-shadow: var(--card-shadow);\n}\n.dropdown-item {\n  color: var(--text-color);\n  transition: all 0.3s ease;\n}\n.dropdown-item:hover {\n  background-color: var(--card-background-color-hover);\n  color: var(--text-color-hover);\n}\n.tooltip .tooltip-inner {\n  background-color: var(--sidebar-bg);\n  color: var(--sidebar-text);\n}\n.progress {\n  background-color: var(--border-color);\n}\n.progress-bar {\n  background-color: var(--primary-color);\n}\n.container-fluid {\n  background:\n    linear-gradient(\n      135deg,\n      #f5f7fa 0%,\n      #c3cfe2 100%);\n  min-height: 100vh;\n  padding: 2rem;\n  position: relative;\n}\n.container-fluid::before {\n  content: "";\n  position: absolute;\n  top: 0;\n  left: 0;\n  right: 0;\n  height: 300px;\n  background:\n    linear-gradient(\n      135deg,\n      rgba(102, 126, 234, 0.1) 0%,\n      rgba(118, 75, 162, 0.1) 100%);\n  z-index: 0;\n}\n.container-fluid > * {\n  position: relative;\n  z-index: 1;\n}\n@media (max-width: 768px) {\n  .container-fluid {\n    padding: 1rem;\n  }\n}\n.page-header {\n  background:\n    linear-gradient(\n      135deg,\n      #667eea 0%,\n      #764ba2 100%);\n  border-radius: 24px;\n  padding: 3rem 2.5rem;\n  margin-bottom: 3rem;\n  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.1);\n  color: white;\n  position: relative;\n  overflow: hidden;\n}\n.page-header::before {\n  content: "";\n  position: absolute;\n  top: -50%;\n  right: -20%;\n  width: 200px;\n  height: 200px;\n  background:\n    radial-gradient(\n      circle,\n      rgba(255, 255, 255, 0.1) 0%,\n      transparent 70%);\n  border-radius: 50%;\n}\n.page-header::after {\n  content: "";\n  position: absolute;\n  bottom: -30%;\n  left: -10%;\n  width: 150px;\n  height: 150px;\n  background:\n    radial-gradient(\n      circle,\n      rgba(255, 255, 255, 0.08) 0%,\n      transparent 70%);\n  border-radius: 50%;\n}\n.page-header .header-content {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  position: relative;\n  z-index: 2;\n}\n@media (max-width: 768px) {\n  .page-header .header-content {\n    flex-direction: column;\n    gap: 2rem;\n    text-align: center;\n  }\n}\n.page-header .header-title {\n  display: flex;\n  align-items: center;\n  gap: 1.5rem;\n}\n.page-header .header-title .title-icon {\n  background: rgba(255, 255, 255, 0.2);\n  -webkit-backdrop-filter: blur(10px);\n  backdrop-filter: blur(10px);\n  border-radius: 50%;\n  width: 80px;\n  height: 80px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  font-size: 2rem;\n  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);\n  border: 1px solid rgba(255, 255, 255, 0.1);\n}\n.page-header .header-title .title-text .page-title {\n  font-size: 3rem;\n  font-weight: 800;\n  margin: 0;\n  text-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);\n  background:\n    linear-gradient(\n      45deg,\n      #fff,\n      rgba(255, 255, 255, 0.8));\n  -webkit-background-clip: text;\n  -webkit-text-fill-color: transparent;\n  background-clip: text;\n}\n@media (max-width: 768px) {\n  .page-header .header-title .title-text .page-title {\n    font-size: 2.2rem;\n  }\n}\n.page-header .header-title .title-text .page-subtitle {\n  font-size: 1.2rem;\n  opacity: 0.9;\n  margin: 0.5rem 0 0 0;\n  font-weight: 300;\n  letter-spacing: 0.5px;\n}\n.page-header .header-actions .btn {\n  background: rgba(255, 255, 255, 0.15);\n  -webkit-backdrop-filter: blur(10px);\n  backdrop-filter: blur(10px);\n  border: 2px solid rgba(255, 255, 255, 0.2);\n  color: white;\n  border-radius: 12px;\n  padding: 1rem 2rem;\n  font-weight: 600;\n  font-size: 1rem;\n  transition: all 0.4s cubic-bezier(0.25, 0.8, 0.25, 1);\n  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);\n}\n.page-header .header-actions .btn:hover {\n  background: rgba(255, 255, 255, 0.25);\n  border-color: rgba(255, 255, 255, 0.4);\n  transform: translateY(-3px);\n  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.15);\n}\n.page-header .header-actions .btn:active {\n  transform: translateY(-1px);\n}\n.stats-container {\n  display: flex;\n  gap: 1.5rem;\n  margin-bottom: 3rem;\n  overflow-x: auto;\n  padding-bottom: 0.5rem;\n}\n@media (max-width: 768px) {\n  .stats-container {\n    flex-direction: column;\n    gap: 1rem;\n  }\n}\n.stats-container::-webkit-scrollbar {\n  height: 4px;\n}\n.stats-container::-webkit-scrollbar-track {\n  background: rgba(255, 255, 255, 0.1);\n  border-radius: 2px;\n}\n.stats-container::-webkit-scrollbar-thumb {\n  background: rgba(102, 126, 234, 0.3);\n  border-radius: 2px;\n}\n.stats-container::-webkit-scrollbar-thumb:hover {\n  background: rgba(102, 126, 234, 0.5);\n}\n.stats-card {\n  background: rgba(255, 255, 255, 0.25);\n  -webkit-backdrop-filter: blur(20px);\n  backdrop-filter: blur(20px);\n  border: 1px solid rgba(255, 255, 255, 0.18);\n  border-radius: 16px;\n  padding: 2rem 1.5rem;\n  box-shadow: 0 10px 40px rgba(0, 0, 0, 0.1);\n  transition: all 0.4s cubic-bezier(0.25, 0.8, 0.25, 1);\n  flex: 1;\n  min-width: 220px;\n  display: flex;\n  align-items: center;\n  gap: 1.5rem;\n  position: relative;\n  overflow: hidden;\n}\n.stats-card::before {\n  content: "";\n  position: absolute;\n  top: 0;\n  left: 0;\n  right: 0;\n  height: 4px;\n  background:\n    linear-gradient(\n      90deg,\n      transparent,\n      currentColor,\n      transparent);\n  opacity: 0.6;\n}\n.stats-card:hover {\n  transform: translateY(-8px) scale(1.02);\n  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.1);\n  background: rgba(255, 255, 255, 0.35);\n  border-color: rgba(255, 255, 255, 0.3);\n}\n.stats-card .stats-icon {\n  width: 60px;\n  height: 60px;\n  border-radius: 50%;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  font-size: 1.8rem;\n  color: white;\n  position: relative;\n}\n.stats-card .stats-icon::after {\n  content: "";\n  position: absolute;\n  inset: -2px;\n  border-radius: 50%;\n  padding: 2px;\n  background:\n    linear-gradient(\n      45deg,\n      currentColor,\n      transparent,\n      currentColor);\n  mask: linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0);\n  mask-composite: exclude;\n  opacity: 0.3;\n}\n.stats-card .stats-content {\n  flex: 1;\n}\n.stats-card .stats-content .stats-number {\n  font-size: 2.5rem;\n  font-weight: 800;\n  line-height: 1;\n  margin-bottom: 0.5rem;\n}\n.stats-card .stats-content .stats-label {\n  font-size: 1rem;\n  color: rgba(0, 0, 0, 0.7);\n  font-weight: 600;\n  text-transform: uppercase;\n  letter-spacing: 1px;\n}\n.stats-card.stats-primary {\n  color: #667eea;\n}\n.stats-card.stats-primary .stats-icon {\n  background:\n    linear-gradient(\n      135deg,\n      #007bff 0%,\n      rgb(76.5, 162.6, 255) 50%,\n      rgb(0, 110.7, 229.5) 100%);\n}\n.stats-card.stats-success {\n  color: #11998e;\n}\n.stats-card.stats-success .stats-icon {\n  background:\n    linear-gradient(\n      135deg,\n      #11998e 0%,\n      #38ef7d 100%);\n}\n.stats-card.stats-warning {\n  color: #f093fb;\n}\n.stats-card.stats-warning .stats-icon {\n  background:\n    linear-gradient(\n      135deg,\n      #f093fb 0%,\n      #f5576c 100%);\n}\n.stats-card.stats-danger {\n  color: #fc466b;\n}\n.stats-card.stats-danger .stats-icon {\n  background:\n    linear-gradient(\n      135deg,\n      #fc466b 0%,\n      #3f5efb 100%);\n}\n.card {\n  border: none;\n  border-radius: 16px;\n  box-shadow: 0 10px 40px rgba(0, 0, 0, 0.1);\n  transition: all 0.4s cubic-bezier(0.25, 0.8, 0.25, 1);\n  background: rgba(255, 255, 255, 0.9);\n  -webkit-backdrop-filter: blur(20px);\n  backdrop-filter: blur(20px);\n}\n.card:hover {\n  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.1);\n  transform: translateY(-2px);\n}\n.card .card-header {\n  background:\n    linear-gradient(\n      135deg,\n      rgba(102, 126, 234, 0.1),\n      rgba(118, 75, 162, 0.1));\n  border-bottom: 1px solid rgba(102, 126, 234, 0.2);\n  border-radius: 16px 16px 0 0;\n  padding: 1.5rem;\n}\n.card .card-header h5 {\n  color: #667eea;\n  font-weight: 700;\n  margin: 0;\n  font-size: 1.3rem;\n}\n.card .card-header h5 .bi {\n  margin-right: 0.75rem;\n  background: rgba(102, 126, 234, 0.2);\n  padding: 0.5rem;\n  border-radius: 50%;\n  font-size: 1rem;\n}\n.card .card-body {\n  padding: 2rem;\n}\n.card .card-body .form-label {\n  font-weight: 600;\n  color: #333;\n  margin-bottom: 0.75rem;\n  font-size: 1rem;\n}\n.card .card-body .form-label .bi {\n  color: #667eea;\n  margin-right: 0.5rem;\n}\n.card .card-body .form-control,\n.card .card-body .form-select {\n  border: 2px solid rgba(102, 126, 234, 0.2);\n  border-radius: 12px;\n  padding: 0.875rem 1.25rem;\n  transition: all 0.4s cubic-bezier(0.25, 0.8, 0.25, 1);\n  font-size: 1rem;\n  background: rgba(255, 255, 255, 0.8);\n}\n.card .card-body .form-control:focus,\n.card .card-body .form-select:focus {\n  border-color: #667eea;\n  box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);\n  background: white;\n}\n.card .card-body .form-control::placeholder,\n.card .card-body .form-select::placeholder {\n  color: rgba(0, 0, 0, 0.5);\n}\n.card .card-body .btn {\n  border-radius: 12px;\n  padding: 0.875rem 1.5rem;\n  font-weight: 600;\n  transition: all 0.4s cubic-bezier(0.25, 0.8, 0.25, 1);\n  text-transform: uppercase;\n  letter-spacing: 0.5px;\n}\n.card .card-body .btn:hover {\n  transform: translateY(-2px);\n  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);\n}\n.card .card-body .btn.btn-outline-secondary {\n  border-color: #667eea;\n  color: #667eea;\n}\n.card .card-body .btn.btn-outline-secondary:hover {\n  background:\n    linear-gradient(\n      135deg,\n      #667eea 0%,\n      #764ba2 100%);\n  border-color: transparent;\n  color: white;\n}\n.avis-card {\n  background: rgba(255, 255, 255, 0.9);\n  -webkit-backdrop-filter: blur(20px);\n  backdrop-filter: blur(20px);\n  border: 1px solid rgba(255, 255, 255, 0.2);\n  border-radius: 16px;\n  box-shadow: 0 10px 40px rgba(0, 0, 0, 0.1);\n  margin-bottom: 2rem;\n  overflow: hidden;\n  transition: all 0.4s cubic-bezier(0.25, 0.8, 0.25, 1);\n  position: relative;\n}\n.avis-card::before {\n  content: "";\n  position: absolute;\n  top: 0;\n  left: 0;\n  right: 0;\n  height: 2px;\n  background:\n    linear-gradient(\n      135deg,\n      #667eea 0%,\n      #764ba2 100%);\n  opacity: 0.8;\n}\n.avis-card:hover {\n  transform: translateY(-10px);\n  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.1);\n  background: rgba(255, 255, 255, 0.95);\n  border-color: rgba(255, 255, 255, 0.3);\n}\n.avis-card .avis-card-content {\n  display: flex;\n  min-height: 220px;\n}\n@media (max-width: 768px) {\n  .avis-card .avis-card-content {\n    flex-direction: column;\n  }\n}\n.avis-card .avis-main-content {\n  flex: 1;\n  padding: 2rem;\n}\n.avis-card .avis-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: flex-start;\n  margin-bottom: 1.5rem;\n}\n@media (max-width: 768px) {\n  .avis-card .avis-header {\n    flex-direction: column;\n    gap: 1rem;\n  }\n}\n.avis-card .avis-header .avis-info {\n  flex: 1;\n}\n.avis-card .avis-header .avis-info .product-name {\n  font-size: 1.4rem;\n  font-weight: 700;\n  color: #667eea;\n  margin-bottom: 0.75rem;\n  background:\n    linear-gradient(\n      45deg,\n      #667eea,\n      #764ba2);\n  -webkit-background-clip: text;\n  -webkit-text-fill-color: transparent;\n  background-clip: text;\n}\n.avis-card .avis-header .avis-info .client-info {\n  color: rgba(0, 0, 0, 0.6);\n  font-size: 1rem;\n  display: flex;\n  align-items: center;\n  gap: 0.75rem;\n  font-weight: 500;\n}\n.avis-card .avis-header .avis-info .client-info .date-separator {\n  margin: 0 0.25rem;\n  color: #667eea;\n}\n.avis-card .avis-header .avis-info .client-info .bi {\n  color: #667eea;\n  font-size: 1.1rem;\n}\n.avis-card .avis-header .avis-rating-status {\n  text-align: right;\n}\n@media (max-width: 768px) {\n  .avis-card .avis-header .avis-rating-status {\n    text-align: left;\n  }\n}\n.avis-card .avis-header .avis-rating-status .rating-display {\n  margin-bottom: 1rem;\n}\n.avis-card .avis-header .avis-rating-status .rating-display .stars {\n  display: flex;\n  gap: 0.25rem;\n  margin-bottom: 0.75rem;\n  justify-content: flex-end;\n}\n@media (max-width: 768px) {\n  .avis-card .avis-header .avis-rating-status .rating-display .stars {\n    justify-content: flex-start;\n  }\n}\n.avis-card .avis-header .avis-rating-status .rating-display .stars .star {\n  font-size: 1.3rem;\n  color: #e0e0e0;\n  transition: all 0.2s ease-out;\n  filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.1));\n}\n.avis-card .avis-header .avis-rating-status .rating-display .stars .star.filled {\n  color: #ffd700;\n  text-shadow: 0 0 10px rgba(255, 215, 0, 0.5);\n}\n.avis-card .avis-header .avis-rating-status .rating-display .rating-number {\n  font-weight: 700;\n  color: #333;\n  font-size: 1.1rem;\n  background:\n    linear-gradient(\n      45deg,\n      #ffd700,\n      #ffed4e);\n  -webkit-background-clip: text;\n  -webkit-text-fill-color: transparent;\n  background-clip: text;\n}\n.avis-card .avis-header .avis-rating-status .status-badge {\n  display: inline-flex;\n  align-items: center;\n  padding: 0.5rem 1rem;\n  border-radius: 25px;\n  font-size: 0.85rem;\n  font-weight: 700;\n  text-transform: uppercase;\n  letter-spacing: 0.5px;\n  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);\n  border: 2px solid transparent;\n}\n.avis-card .avis-header .avis-rating-status .status-badge .bi {\n  font-size: 0.7rem;\n  margin-right: 0.5rem;\n}\n.avis-card .avis-header .avis-rating-status .status-badge.badge-success {\n  background:\n    linear-gradient(\n      135deg,\n      #11998e 0%,\n      #38ef7d 100%);\n  color: white;\n  border-color: rgba(17, 153, 142, 0.3);\n}\n.avis-card .avis-header .avis-rating-status .status-badge.badge-warning {\n  background:\n    linear-gradient(\n      135deg,\n      #f093fb 0%,\n      #f5576c 100%);\n  color: white;\n  border-color: rgba(240, 147, 251, 0.3);\n}\n.avis-card .avis-header .avis-rating-status .status-badge.badge-danger {\n  background:\n    linear-gradient(\n      135deg,\n      #fc466b 0%,\n      #3f5efb 100%);\n  color: white;\n  border-color: rgba(252, 70, 107, 0.3);\n}\n.avis-card .avis-header .avis-rating-status .status-badge.badge-secondary {\n  background:\n    linear-gradient(\n      135deg,\n      #6c757d,\n      #495057);\n  color: white;\n  border-color: rgba(108, 117, 125, 0.3);\n}\n.avis-card .avis-comment {\n  background:\n    linear-gradient(\n      135deg,\n      rgba(102, 126, 234, 0.05),\n      rgba(118, 75, 162, 0.05));\n  border-radius: 12px;\n  padding: 1.5rem;\n  margin-bottom: 1.5rem;\n  border-left: 4px solid #667eea;\n  position: relative;\n}\n.avis-card .avis-comment .comment-content {\n  display: flex;\n  align-items: flex-start;\n  gap: 1rem;\n  font-style: italic;\n  line-height: 1.7;\n  font-size: 1.05rem;\n  color: rgba(0, 0, 0, 0.8);\n}\n.avis-card .avis-comment .comment-content .bi {\n  color: #667eea;\n  margin-top: 0.25rem;\n  flex-shrink: 0;\n  font-size: 1.2rem;\n}\n.avis-card .avis-comment .comment-deleted {\n  display: flex;\n  align-items: center;\n  gap: 1rem;\n  color: rgba(252, 70, 107, 0.8);\n  font-style: italic;\n}\n.avis-card .avis-comment .comment-none {\n  display: flex;\n  align-items: center;\n  gap: 1rem;\n  color: rgba(108, 117, 125, 0.8);\n  font-style: italic;\n  font-weight: 600;\n}\n.avis-card .avis-comment .comment-none .bi {\n  color: #fc466b;\n  font-size: 1.2rem;\n}\n.avis-card .avis-actions {\n  background:\n    linear-gradient(\n      135deg,\n      rgba(102, 126, 234, 0.05),\n      rgba(118, 75, 162, 0.05));\n  -webkit-backdrop-filter: blur(10px);\n  backdrop-filter: blur(10px);\n  padding: 2rem;\n  border-left: 1px solid rgba(102, 126, 234, 0.2);\n  display: flex;\n  flex-direction: column;\n  gap: 1rem;\n  justify-content: center;\n  min-width: 220px;\n}\n@media (max-width: 768px) {\n  .avis-card .avis-actions {\n    border-left: none;\n    border-top: 1px solid rgba(102, 126, 234, 0.2);\n    flex-direction: row;\n    min-width: auto;\n  }\n}\n.avis-card .avis-actions .action-btn {\n  display: flex;\n  align-items: center;\n  gap: 0.75rem;\n  padding: 1rem 1.5rem;\n  border-radius: 12px;\n  border: 2px solid;\n  background: transparent;\n  font-weight: 700;\n  font-size: 0.95rem;\n  transition: all 0.4s cubic-bezier(0.25, 0.8, 0.25, 1);\n  cursor: pointer;\n  text-decoration: none;\n  justify-content: center;\n  text-transform: uppercase;\n  letter-spacing: 0.5px;\n  position: relative;\n  overflow: hidden;\n}\n.avis-card .avis-actions .action-btn::before {\n  content: "";\n  position: absolute;\n  top: 0;\n  left: -100%;\n  width: 100%;\n  height: 100%;\n  background:\n    linear-gradient(\n      90deg,\n      transparent,\n      rgba(255, 255, 255, 0.2),\n      transparent);\n  transition: left 0.5s;\n}\n.avis-card .avis-actions .action-btn:hover::before {\n  left: 100%;\n}\n.avis-card .avis-actions .action-btn:hover {\n  transform: translateY(-3px);\n  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);\n}\n.avis-card .avis-actions .action-btn.danger-btn {\n  border-color: #fc466b;\n  color: #fc466b;\n}\n.avis-card .avis-actions .action-btn.danger-btn:hover {\n  background:\n    linear-gradient(\n      135deg,\n      #fc466b 0%,\n      #3f5efb 100%);\n  color: white;\n  border-color: transparent;\n}\n.avis-card .avis-actions .action-btn.info-btn {\n  border-color: #667eea;\n  color: #667eea;\n}\n.avis-card .avis-actions .action-btn.info-btn:hover {\n  background:\n    linear-gradient(\n      135deg,\n      #667eea 0%,\n      #764ba2 100%);\n  color: white;\n  border-color: transparent;\n}\n.avis-card .avis-actions .action-btn .bi {\n  font-size: 1.1rem;\n}\n.inline-modal {\n  background: rgba(255, 255, 255, 0.95);\n  -webkit-backdrop-filter: blur(30px);\n  backdrop-filter: blur(30px);\n  border: 1px solid rgba(255, 255, 255, 0.2);\n  border-radius: 16px;\n  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.1);\n  margin-top: 1.5rem;\n  overflow: hidden;\n  animation: slideDownElegant 0.5s cubic-bezier(0.25, 0.8, 0.25, 1);\n}\n.inline-modal .modal-header {\n  background:\n    linear-gradient(\n      135deg,\n      rgba(102, 126, 234, 0.1),\n      rgba(118, 75, 162, 0.1));\n  padding: 2rem;\n  border-bottom: 1px solid rgba(102, 126, 234, 0.2);\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n}\n.inline-modal .modal-header .modal-title {\n  font-weight: 700;\n  color: #667eea;\n  margin: 0;\n  display: flex;\n  align-items: center;\n  font-size: 1.3rem;\n}\n.inline-modal .modal-header .modal-title .bi {\n  margin-right: 0.75rem;\n  background: rgba(102, 126, 234, 0.2);\n  padding: 0.5rem;\n  border-radius: 50%;\n  font-size: 1rem;\n}\n.inline-modal .modal-header .btn-close {\n  background: none;\n  border: none;\n  font-size: 1.5rem;\n  cursor: pointer;\n  color: #667eea;\n  transition: all 0.4s cubic-bezier(0.25, 0.8, 0.25, 1);\n  width: 40px;\n  height: 40px;\n  border-radius: 50%;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n}\n.inline-modal .modal-header .btn-close:hover {\n  background: rgba(102, 126, 234, 0.1);\n  transform: scale(1.1);\n}\n.inline-modal .modal-body {\n  padding: 2rem;\n}\n.inline-modal .modal-body .alert {\n  border-radius: 12px;\n  border: none;\n  padding: 1.5rem;\n  margin-bottom: 1.5rem;\n}\n.inline-modal .modal-body .alert.alert-info {\n  background:\n    linear-gradient(\n      135deg,\n      rgba(102, 126, 234, 0.1),\n      rgba(118, 75, 162, 0.1));\n  color: #667eea;\n  border-left: 4px solid #667eea;\n}\n.inline-modal .modal-body .alert.alert-info .bi {\n  color: #667eea;\n  margin-right: 0.75rem;\n}\n.inline-modal .modal-body .alert ul {\n  margin-bottom: 0;\n  padding-left: 1.5rem;\n}\n.inline-modal .modal-body .form-label {\n  font-weight: 700;\n  color: #333;\n  margin-bottom: 0.75rem;\n  display: flex;\n  align-items: center;\n  font-size: 1.1rem;\n}\n.inline-modal .modal-body .form-label .bi {\n  margin-right: 0.75rem;\n  color: #667eea;\n  background: rgba(102, 126, 234, 0.1);\n  padding: 0.5rem;\n  border-radius: 50%;\n  font-size: 1rem;\n}\n.inline-modal .modal-body .form-label .text-danger {\n  margin-left: 0.5rem;\n  color: #fc466b;\n}\n.inline-modal .modal-body .form-control {\n  border: 2px solid rgba(102, 126, 234, 0.2);\n  border-radius: 12px;\n  padding: 1rem 1.25rem;\n  transition: all 0.4s cubic-bezier(0.25, 0.8, 0.25, 1);\n  font-size: 1rem;\n  background: rgba(255, 255, 255, 0.8);\n  min-height: 120px;\n}\n.inline-modal .modal-body .form-control:focus {\n  border-color: #667eea;\n  box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);\n  background: white;\n}\n.inline-modal .modal-body .form-control::placeholder {\n  color: rgba(0, 0, 0, 0.5);\n  font-style: italic;\n}\n.inline-modal .modal-body .btn {\n  border-radius: 12px;\n  padding: 1rem 2rem;\n  font-weight: 700;\n  font-size: 1rem;\n  transition: all 0.4s cubic-bezier(0.25, 0.8, 0.25, 1);\n  display: flex;\n  align-items: center;\n  gap: 0.75rem;\n  text-transform: uppercase;\n  letter-spacing: 0.5px;\n}\n.inline-modal .modal-body .btn:hover {\n  transform: translateY(-3px);\n  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);\n}\n.inline-modal .modal-body .btn.btn-secondary {\n  background:\n    linear-gradient(\n      135deg,\n      #6c757d,\n      #495057);\n  border: none;\n  color: white;\n}\n.inline-modal .modal-body .btn.btn-secondary:hover {\n  background:\n    linear-gradient(\n      135deg,\n      #495057,\n      #343a40);\n}\n.inline-modal .modal-body .btn.btn-danger {\n  background:\n    linear-gradient(\n      135deg,\n      #fc466b 0%,\n      #3f5efb 100%);\n  border: none;\n  color: white;\n}\n.inline-modal .modal-body .btn.btn-danger:hover {\n  background:\n    linear-gradient(\n      135deg,\n      #3f5efb,\n      #fc466b);\n}\n.inline-modal .modal-body .btn .bi {\n  font-size: 1.1rem;\n}\n.inline-modal .modal-body .d-flex.gap-2 {\n  gap: 1rem !important;\n  justify-content: flex-end;\n  margin-top: 2rem;\n}\n.inline-modal.suppression-modal {\n  border-left: 4px solid #fc466b;\n}\n.inline-modal.history-modal {\n  border-left: 4px solid #667eea;\n}\n.timeline {\n  position: relative;\n  padding-left: 2.5rem;\n}\n.timeline::before {\n  content: "";\n  position: absolute;\n  left: 15px;\n  top: 0;\n  bottom: 0;\n  width: 3px;\n  background:\n    linear-gradient(\n      135deg,\n      #667eea 0%,\n      #764ba2 100%);\n  border-radius: 2px;\n}\n.timeline .timeline-item {\n  position: relative;\n  margin-bottom: 2rem;\n}\n.timeline .timeline-item .timeline-marker {\n  position: absolute;\n  left: -25px;\n  top: 8px;\n  width: 35px;\n  height: 35px;\n  border-radius: 50%;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  color: white;\n  font-size: 0.9rem;\n  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);\n  border: 3px solid white;\n}\n.timeline .timeline-item .timeline-marker.bg-primary {\n  background:\n    linear-gradient(\n      135deg,\n      #667eea 0%,\n      #764ba2 100%);\n}\n.timeline .timeline-item .timeline-marker.bg-warning {\n  background:\n    linear-gradient(\n      135deg,\n      #f093fb 0%,\n      #f5576c 100%);\n}\n.timeline .timeline-item .timeline-content {\n  background: rgba(255, 255, 255, 0.8);\n  -webkit-backdrop-filter: blur(10px);\n  backdrop-filter: blur(10px);\n  padding: 1.5rem;\n  border-radius: 12px;\n  border-left: 4px solid #667eea;\n  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);\n}\n.timeline .timeline-item .timeline-content h6 {\n  margin-bottom: 0.5rem;\n  font-weight: 700;\n  color: #667eea;\n  font-size: 1.1rem;\n}\n.timeline .timeline-item .timeline-content small {\n  color: rgba(0, 0, 0, 0.6);\n  font-size: 0.9rem;\n  font-weight: 500;\n}\n.timeline .timeline-item .timeline-content p {\n  margin: 0.75rem 0 0 0;\n  line-height: 1.6;\n  color: rgba(0, 0, 0, 0.8);\n}\n@keyframes slideDownElegant {\n  from {\n    opacity: 0;\n    transform: translateY(-30px) scale(0.95);\n  }\n  to {\n    opacity: 1;\n    transform: translateY(0) scale(1);\n  }\n}\n.loading-spinner {\n  display: flex;\n  justify-content: center;\n  align-items: center;\n  padding: 4rem;\n}\n.loading-spinner .spinner-border {\n  width: 4rem;\n  height: 4rem;\n  border-width: 4px;\n  color: #667eea;\n}\n.empty-state {\n  text-align: center;\n  padding: 4rem 2rem;\n  color: rgba(0, 0, 0, 0.6);\n}\n.empty-state .bi {\n  font-size: 5rem;\n  margin-bottom: 1.5rem;\n  opacity: 0.3;\n  color: #667eea;\n}\n.empty-state h4 {\n  margin-bottom: 0.75rem;\n  font-weight: 700;\n  color: #333;\n  font-size: 1.5rem;\n}\n.empty-state p {\n  margin: 0;\n  font-size: 1.1rem;\n  font-weight: 500;\n}\n.alert {\n  border-radius: 16px;\n  border: none;\n  padding: 1.5rem;\n}\n.alert.alert-danger {\n  background:\n    linear-gradient(\n      135deg,\n      rgba(252, 70, 107, 0.1),\n      rgba(63, 94, 251, 0.1));\n  color: #fc466b;\n  border-left: 4px solid #fc466b;\n}\n.alert.alert-danger .bi {\n  color: #fc466b;\n  margin-right: 0.75rem;\n}\n.alert .btn-close {\n  background: none;\n  border: none;\n  opacity: 0.7;\n  transition: all 0.4s cubic-bezier(0.25, 0.8, 0.25, 1);\n  width: 30px;\n  height: 30px;\n  border-radius: 50%;\n}\n.alert .btn-close:hover {\n  opacity: 1;\n  background: rgba(0, 0, 0, 0.05);\n}\n@media (max-width: 576px) {\n  .container-fluid {\n    padding: 1rem 0.75rem;\n  }\n  .page-header {\n    padding: 2rem 1.5rem;\n    margin-bottom: 2rem;\n  }\n  .page-header .title-text .page-title {\n    font-size: 2rem;\n  }\n  .page-header .title-icon {\n    width: 60px;\n    height: 60px;\n    font-size: 1.5rem;\n  }\n  .stats-container {\n    margin-bottom: 2rem;\n  }\n  .stats-card {\n    padding: 1.5rem 1rem;\n    min-width: auto;\n  }\n  .stats-card .stats-icon {\n    width: 50px;\n    height: 50px;\n    font-size: 1.5rem;\n  }\n  .stats-card .stats-content .stats-number {\n    font-size: 2rem;\n  }\n  .avis-card {\n    margin-bottom: 1.5rem;\n  }\n  .avis-card .avis-main-content,\n  .avis-card .avis-actions {\n    padding: 1.5rem;\n  }\n  .avis-card .avis-actions {\n    gap: 0.75rem;\n  }\n  .avis-card .avis-actions .action-btn {\n    padding: 0.75rem 1rem;\n    font-size: 0.9rem;\n  }\n  .inline-modal .modal-header,\n  .inline-modal .modal-body {\n    padding: 1.5rem;\n  }\n}\n/*# sourceMappingURL=avis-moderation.component.css.map */\n'] }]
  }], () => [{ type: AvisModerationService }], null);
})();
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && \u0275setClassDebugInfo(AvisModerationComponent, { className: "AvisModerationComponent", filePath: "src/app/components/admin/avis-moderation/avis-moderation.component.ts", lineNumber: 13 });
})();
export {
  AvisModerationComponent
};
//# sourceMappingURL=chunk-VJ2XUHC3.js.map

using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using System.Security.Claims;
using WebApiPfe.DTOs.Admin;
using WebApiPfe.Models.Enum;
using WebApiPfe.Services.Interfaces;

namespace WebApiPfe.Controllers
{
    [ApiController]
    [Route("api/[controller]")]
    // [Authorize] // Temporairement désactivé pour test
    public class DemandesCategoriesController : ControllerBase
    {
        private readonly IDemandeCategorieService _demandeCategorieService;

        public DemandesCategoriesController(IDemandeCategorieService demandeCategorieService)
        {
            _demandeCategorieService = demandeCategorieService;
            Console.WriteLine("🏗️ DemandesCategoriesController créé");
        }



        /// <summary>
        /// Obtenir toutes les demandes de catégories (Admin seulement)
        /// </summary>
        [HttpGet]
        [AllowAnonymous] // Temporaire pour résoudre le problème d'authentification admin
        public async Task<ActionResult<IEnumerable<DemandeCategorieDto>>> GetAllDemandes()
        {
            try
            {
                var demandes = await _demandeCategorieService.GetAllDemandesAsync();
                return Ok(demandes);
            }
            catch (Exception ex)
            {
                return StatusCode(500, new { message = "Erreur lors de la récupération des demandes", error = ex.Message });
            }
        }

        /// <summary>
        /// Obtenir les demandes de catégories par statut (Admin seulement)
        /// </summary>
        [HttpGet("test")]
        [AllowAnonymous]
        public async Task<ActionResult<string>> TestEndpoint()
        {
            return Ok("Test endpoint fonctionne !");
        }

        [HttpGet("statut/{statut}")]
        [AllowAnonymous] // Complètement ouvert pour debug
        public async Task<ActionResult<IEnumerable<DemandeCategorieDto>>> GetDemandesByStatut(StatutDemande statut)
        {
            try
            {
                var demandes = await _demandeCategorieService.GetDemandesByStatutAsync(statut);
                return Ok(demandes);
            }
            catch (Exception ex)
            {
                return StatusCode(500, new { message = "Erreur lors de la récupération des demandes", error = ex.Message });
            }
        }

        /// <summary>
        /// Obtenir les demandes de catégories du fournisseur connecté
        /// </summary>
        [HttpGet("mes-demandes")]
        [AllowAnonymous] // Temporaire pour test
        public async Task<ActionResult<IEnumerable<DemandeCategorieDto>>> GetMesDemandes()
        {
            try
            {
                Console.WriteLine("🔍 GET /api/DemandesCategories/mes-demandes - Début de la méthode");

                // Vérifier quels fournisseurs existent
                var fournisseurs = await _demandeCategorieService.GetAllFournisseursAsync();
                Console.WriteLine($"📋 Fournisseurs disponibles: {string.Join(", ", fournisseurs.Select(f => $"ID:{f.Id} - {f.RaisonSociale}"))}");

                var userIdClaim = User.FindFirst(ClaimTypes.NameIdentifier)?.Value;
                int fournisseurId = fournisseurs.FirstOrDefault()?.Id ?? 1; // Utiliser le premier fournisseur par défaut

                if (!string.IsNullOrEmpty(userIdClaim) && int.TryParse(userIdClaim, out int authenticatedFournisseurId))
                {
                    fournisseurId = authenticatedFournisseurId;
                }

                Console.WriteLine($"🔍 Récupération des demandes pour FournisseurId: {fournisseurId}");
                var demandes = await _demandeCategorieService.GetDemandesByFournisseurAsync(fournisseurId);
                Console.WriteLine($"📊 Nombre de demandes trouvées: {demandes.Count()}");

                return Ok(demandes);
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ Erreur dans GetMesDemandes: {ex.Message}");
                return StatusCode(500, new { message = "Erreur lors de la récupération des demandes", error = ex.Message });
            }
        }

        /// <summary>
        /// Obtenir une demande de catégorie par ID
        /// </summary>
        [HttpGet("{id}")]
        public async Task<ActionResult<DemandeCategorieDto>> GetDemandeById(int id)
        {
            try
            {
                var demande = await _demandeCategorieService.GetDemandeByIdAsync(id);
                if (demande == null)
                {
                    return NotFound(new { message = "Demande non trouvée" });
                }

                // Vérifier les autorisations
                var userRole = User.FindFirst(ClaimTypes.Role)?.Value;
                var userIdClaim = User.FindFirst(ClaimTypes.NameIdentifier)?.Value;
                
                if (userRole == "Fournisseur" && int.TryParse(userIdClaim, out int fournisseurId))
                {
                    if (demande.FournisseurId != fournisseurId)
                    {
                        return Forbid();
                    }
                }
                else if (userRole != "Admin")
                {
                    return Forbid();
                }

                return Ok(demande);
            }
            catch (Exception ex)
            {
                return StatusCode(500, new { message = "Erreur lors de la récupération de la demande", error = ex.Message });
            }
        }

        /// <summary>
        /// Créer une nouvelle demande de catégorie (Fournisseur seulement)
        /// </summary>
        [HttpPost]
        [AllowAnonymous] // Temporaire pour test
        public async Task<ActionResult<DemandeCategorieDto>> CreateDemande([FromBody] CreateDemandeCategorieDto createDto)
        {
            Console.WriteLine("🔍 POST /api/DemandesCategories - Début de la méthode");
            Console.WriteLine($"📝 DTO reçu: {createDto?.Nom} - {createDto?.Description}");

            try
            {
                if (!ModelState.IsValid)
                {
                    Console.WriteLine("❌ ModelState invalide:");
                    foreach (var error in ModelState)
                    {
                        Console.WriteLine($"   - {error.Key}: {string.Join(", ", error.Value.Errors.Select(e => e.ErrorMessage))}");
                    }
                    return BadRequest(ModelState);
                }

                // Vérifier quels fournisseurs existent
                var fournisseurs = await _demandeCategorieService.GetAllFournisseursAsync();
                Console.WriteLine($"📋 Fournisseurs disponibles: {string.Join(", ", fournisseurs.Select(f => $"ID:{f.Id} - {f.RaisonSociale}"))}");

                // Utiliser l'ID du fournisseur connecté ou le premier fournisseur disponible
                int fournisseurId = fournisseurs.FirstOrDefault()?.Id ?? 1;

                var userIdClaim = User.FindFirst(ClaimTypes.NameIdentifier)?.Value;
                if (!string.IsNullOrEmpty(userIdClaim) && int.TryParse(userIdClaim, out int authenticatedFournisseurId))
                {
                    fournisseurId = authenticatedFournisseurId;
                }

                Console.WriteLine($"🔍 Création demande catégorie - FournisseurId: {fournisseurId}");
                Console.WriteLine($"📝 Demande: {createDto.Nom} - {createDto.Description}");

                var demande = await _demandeCategorieService.CreateDemandeAsync(createDto, fournisseurId);
                Console.WriteLine("✅ Demande créée avec succès, notifications envoyées aux admins");

                return CreatedAtAction(nameof(GetDemandeById), new { id = demande.Id }, demande);
            }
            catch (Exception ex)
            {
                return StatusCode(500, new { message = "Erreur lors de la création de la demande", error = ex.Message });
            }
        }

        /// <summary>
        /// Traiter une demande de catégorie (Admin seulement)
        /// </summary>
        [HttpPut("{id}/traiter")]
        [AllowAnonymous] // Temporaire pour résoudre le problème d'authentification admin
        public async Task<ActionResult<DemandeCategorieDto>> TraiterDemande(int id, [FromBody] TraiterDemandeCategorieDto traiterDto)
        {
            try
            {
                if (!ModelState.IsValid)
                {
                    return BadRequest(ModelState);
                }

                // Temporaire : utiliser un adminId par défaut en attendant la résolution de l'authentification
                var userIdClaim = User.FindFirst(ClaimTypes.NameIdentifier)?.Value;
                int adminId = 2063; // Admin existant : "Système Administrateur"
                if (!string.IsNullOrEmpty(userIdClaim) && int.TryParse(userIdClaim, out int parsedAdminId))
                {
                    adminId = parsedAdminId;
                }

                var demande = await _demandeCategorieService.TraiterDemandeAsync(id, traiterDto, adminId);
                return Ok(demande);
            }
            catch (ArgumentException ex)
            {
                return NotFound(new { message = ex.Message });
            }
            catch (InvalidOperationException ex)
            {
                return BadRequest(new { message = ex.Message });
            }
            catch (Exception ex)
            {
                return StatusCode(500, new { message = "Erreur lors du traitement de la demande", error = ex.Message });
            }
        }

        /// <summary>
        /// Supprimer une demande de catégorie
        /// </summary>
        [HttpDelete("{id}")]
        public async Task<ActionResult> DeleteDemande(int id)
        {
            try
            {
                var userRole = User.FindFirst(ClaimTypes.Role)?.Value;
                var userIdClaim = User.FindFirst(ClaimTypes.NameIdentifier)?.Value;

                // Vérifier les autorisations
                if (userRole == "Fournisseur" && int.TryParse(userIdClaim, out int fournisseurId))
                {
                    var demande = await _demandeCategorieService.GetDemandeByIdAsync(id);
                    if (demande == null)
                    {
                        return NotFound(new { message = "Demande non trouvée" });
                    }
                    if (demande.FournisseurId != fournisseurId)
                    {
                        return Forbid();
                    }
                    if (demande.Statut != StatutDemande.EnAttente)
                    {
                        return BadRequest(new { message = "Impossible de supprimer une demande déjà traitée" });
                    }
                }
                else if (userRole != "Admin")
                {
                    return Forbid();
                }

                var success = await _demandeCategorieService.DeleteDemandeAsync(id);
                if (!success)
                {
                    return NotFound(new { message = "Demande non trouvée" });
                }

                return NoContent();
            }
            catch (Exception ex)
            {
                return StatusCode(500, new { message = "Erreur lors de la suppression de la demande", error = ex.Message });
            }
        }
    }
}

import {
  ProduitService
} from "./chunk-YIUF6N3Y.js";
import {
  AuthService
} from "./chunk-ZSXXNL7U.js";
import {
  environment
} from "./chunk-GFHHPDQ6.js";
import {
  HttpClient,
  HttpHeaders
} from "./chunk-7JDDWGD3.js";
import {
  Injectable,
  catchError,
  forkJoin,
  map,
  of,
  setClassMetadata,
  ɵɵdefineInjectable,
  ɵɵinject
} from "./chunk-UBZQS7JS.js";

// src/app/services/dashboard.service.ts
var DashboardService = class _DashboardService {
  http;
  authService;
  produitService;
  apiUrl = environment.apiUrl;
  constructor(http, authService, produitService) {
    this.http = http;
    this.authService = authService;
    this.produitService = produitService;
  }
  /**
   * Récupérer l'ID du fournisseur connecté (utilise la même logique que la page produits)
   */
  getFournisseurId() {
    const currentUser = this.authService.getCurrentUser();
    return currentUser?.id || null;
  }
  /**
   * Headers avec token d'authentification
   */
  getAuthHeaders() {
    const token = this.authService.getToken();
    return new HttpHeaders({
      "Authorization": `Bearer ${token}`,
      "Content-Type": "application/json"
    });
  }
  /**
   * Récupérer les statistiques admin (globales)
   */
  getAdminStats() {
    return this.http.get(`${this.apiUrl}/api/Admin/statistiques`, {
      headers: this.getAuthHeaders()
    });
  }
  /**
   * Récupérer les informations du fournisseur connecté (priorité localStorage)
   */
  getFournisseurInfo() {
    console.log("\u{1F504} Dashboard: R\xE9cup\xE9ration des infos fournisseur...");
    const storedUser = localStorage.getItem("user");
    const currentUser = this.authService.getCurrentUser();
    if (storedUser) {
      try {
        const userData = JSON.parse(storedUser);
        console.log("\u{1F4E6} Dashboard: Donn\xE9es depuis localStorage:", userData);
        const fournisseur = {
          id: userData.id || currentUser?.id || 0,
          email: userData.email || currentUser?.email || "",
          nom: userData.nom || currentUser?.nom || "",
          prenom: userData.prenom || currentUser?.prenom || "",
          phoneNumber: userData.phoneNumber || userData.telephone || "",
          role: userData.role || "Fournisseur",
          dateNaissance: userData.dateNaissance || "",
          dateInscription: userData.dateInscription || (/* @__PURE__ */ new Date()).toISOString(),
          derniereConnexion: userData.derniereConnexion || null,
          estActif: userData.estActif !== void 0 ? userData.estActif : true,
          matriculeFiscale: userData.matriculeFiscale || "",
          raisonSociale: userData.raisonSociale || "",
          description: userData.description || "",
          ribMasque: userData.ribMasque || userData.rib || "",
          codeBanque: userData.codeBanque || "",
          commission: userData.commission || 0,
          delaiPreparationJours: userData.delaiPreparationJours || 3,
          fraisLivraisonBase: userData.fraisLivraisonBase || 5,
          logoFile: userData.logoFile || "",
          adresses: userData.adresses || []
        };
        console.log("\u2705 Dashboard: Fournisseur cr\xE9\xE9 depuis localStorage:", fournisseur);
        return of(fournisseur);
      } catch (error) {
        console.warn("\u26A0\uFE0F Dashboard: Erreur parsing localStorage:", error);
      }
    }
    if (currentUser) {
      const fournisseur = {
        id: currentUser.id,
        email: currentUser.email,
        nom: currentUser.nom,
        prenom: currentUser.prenom,
        phoneNumber: currentUser.phoneNumber || currentUser.telephone || "",
        role: currentUser.role || "Fournisseur",
        dateNaissance: "",
        dateInscription: (/* @__PURE__ */ new Date()).toISOString(),
        derniereConnexion: null,
        estActif: true,
        matriculeFiscale: "",
        raisonSociale: "",
        description: "",
        ribMasque: "",
        codeBanque: "",
        commission: 0,
        delaiPreparationJours: 3,
        fraisLivraisonBase: 5,
        logoFile: "",
        adresses: []
      };
      console.log("\u2705 Dashboard: Fournisseur cr\xE9\xE9 depuis currentUser:", fournisseur);
      return of(fournisseur);
    }
    console.warn("\u26A0\uFE0F Dashboard: Aucune donn\xE9e utilisateur disponible");
    return of(null);
  }
  /**
   * Récupérer les produits du fournisseur (utilise le même service que la page produits)
   */
  getFournisseurProducts() {
    const currentUser = this.authService.getCurrentUser();
    if (!currentUser?.id) {
      console.warn("Aucun utilisateur connect\xE9 trouv\xE9");
      return of([]);
    }
    console.log(`\u{1F504} Dashboard: R\xE9cup\xE9ration des produits pour le fournisseur ${currentUser.id}`);
    return this.produitService.getByFournisseur(currentUser.id).pipe(map((products) => {
      console.log(`\u2705 Dashboard: ${products.length} produits r\xE9cup\xE9r\xE9s:`, products);
      return products || [];
    }), catchError((error) => {
      console.error("\u274C Dashboard: Erreur lors de la r\xE9cup\xE9ration des produits:", error);
      return of([]);
    }));
  }
  /**
   * Récupérer les commandes fournisseur
   */
  getFournisseurOrders() {
    const fournisseurId = this.getFournisseurId();
    console.log("R\xE9cup\xE9ration des commandes...");
    return this.http.get(`${this.apiUrl}/api/CommandeFournisseur`, {
      headers: this.getAuthHeaders()
    }).pipe(map((orders) => {
      console.log(`${orders?.length || 0} commandes r\xE9cup\xE9r\xE9es:`, orders);
      if (fournisseurId && orders) {
        return orders.filter((order) => order.fournisseurId === fournisseurId);
      }
      return orders || [];
    }), catchError((error) => {
      console.error("Erreur lors de la r\xE9cup\xE9ration des commandes:", error);
      console.log("Aucune commande disponible - retour d'un tableau vide");
      return of([]);
    }));
  }
  /**
   * Calculer les statistiques du dashboard à partir des données réelles
   */
  getDashboardStats() {
    const fournisseurId = this.getFournisseurId();
    if (!fournisseurId) {
      return of({
        totalProducts: 0,
        activeOrders: 0,
        pendingDeliveries: 0,
        monthlyRevenue: 0
      });
    }
    return forkJoin({
      products: this.getFournisseurProducts(),
      orders: this.getFournisseurOrders()
    }).pipe(map(({ products, orders }) => {
      const totalProducts = products.length;
      const activeOrders = orders.filter((order) => order.statut !== "Livr\xE9" && order.statut !== "Annul\xE9").length;
      const currentMonth = (/* @__PURE__ */ new Date()).getMonth();
      const monthlyRevenue = orders.filter((order) => {
        const orderDate = new Date(order.dateCommande);
        return orderDate.getMonth() === currentMonth;
      }).reduce((total, order) => total + order.montantTotal, 0);
      const pendingDeliveries = orders.filter((order) => order.statut === "En pr\xE9paration" || order.statut === "Exp\xE9di\xE9").length;
      return {
        totalProducts,
        activeOrders,
        pendingDeliveries,
        monthlyRevenue
      };
    }), catchError((error) => {
      console.error("Erreur lors du calcul des statistiques:", error);
      return of({
        totalProducts: 0,
        activeOrders: 0,
        pendingDeliveries: 0,
        monthlyRevenue: 0
      });
    }));
  }
  /**
   * Récupérer les commandes récentes
   */
  getRecentOrders() {
    return this.getFournisseurOrders().pipe(map((orders) => {
      return orders.sort((a, b) => new Date(b.dateCommande).getTime() - new Date(a.dateCommande).getTime()).slice(0, 5).map((order) => ({
        id: order.id,
        reference: order.reference,
        client: order.nomFournisseur,
        // Nom du client (à adapter selon votre modèle)
        date: new Date(order.dateCommande),
        amount: order.montantTotal,
        status: order.statut
      }));
    }));
  }
  /**
   * Récupérer l'activité récente
   */
  getRecentActivity() {
    return forkJoin({
      products: this.getFournisseurProducts(),
      orders: this.getFournisseurOrders()
    }).pipe(map(({ products, orders }) => {
      const activities = [];
      const recentProducts = products.sort((a, b) => new Date(b.dateAjout).getTime() - new Date(a.dateAjout).getTime()).slice(0, 2);
      recentProducts.forEach((product) => {
        activities.push({
          icon: "\u{1F4E6}",
          title: `Produit "${product.nom}" ajout\xE9`,
          time: this.getRelativeTime(new Date(product.dateAjout))
        });
      });
      const recentOrders = orders.sort((a, b) => new Date(b.dateCommande).getTime() - new Date(a.dateCommande).getTime()).slice(0, 2);
      recentOrders.forEach((order) => {
        activities.push({
          icon: "\u{1F4CB}",
          title: `Commande ${order.reference} re\xE7ue`,
          time: this.getRelativeTime(new Date(order.dateCommande))
        });
      });
      return activities.slice(0, 3);
    }));
  }
  /**
   * Calculer le temps relatif (il y a X heures/jours)
   */
  getRelativeTime(date) {
    const now = /* @__PURE__ */ new Date();
    const diffMs = now.getTime() - date.getTime();
    const diffHours = Math.floor(diffMs / (1e3 * 60 * 60));
    const diffDays = Math.floor(diffHours / 24);
    if (diffDays > 0) {
      return diffDays === 1 ? "Hier" : `Il y a ${diffDays} jours`;
    } else if (diffHours > 0) {
      return `Il y a ${diffHours} heure${diffHours > 1 ? "s" : ""}`;
    } else {
      return "\xC0 l'instant";
    }
  }
  /**
   * Formater le prix en dinars tunisiens
   */
  formatCurrency(amount) {
    return new Intl.NumberFormat("fr-TN", {
      style: "currency",
      currency: "TND"
    }).format(amount);
  }
  static \u0275fac = function DashboardService_Factory(__ngFactoryType__) {
    return new (__ngFactoryType__ || _DashboardService)(\u0275\u0275inject(HttpClient), \u0275\u0275inject(AuthService), \u0275\u0275inject(ProduitService));
  };
  static \u0275prov = /* @__PURE__ */ \u0275\u0275defineInjectable({ token: _DashboardService, factory: _DashboardService.\u0275fac, providedIn: "root" });
};
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && setClassMetadata(DashboardService, [{
    type: Injectable,
    args: [{
      providedIn: "root"
    }]
  }], () => [{ type: HttpClient }, { type: AuthService }, { type: ProduitService }], null);
})();

export {
  DashboardService
};
//# sourceMappingURL=chunk-Y3FP3KQV.js.map

{"version": 3, "sources": ["src/app/components/admin/reports/reports.component.ts"], "sourcesContent": ["import { Component, OnInit } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { FormsModule } from '@angular/forms';\n\n@Component({\n  selector: 'app-admin-reports',\n  standalone: true,\n  imports: [CommonModule, FormsModule],\n  template: `\n    <div class=\"reports-page\">\n      <div class=\"page-header\">\n        <h1>📈 Rapports et Analyses</h1>\n        <p>Générer et consulter les rapports de performance</p>\n      </div>\n\n      <div class=\"reports-grid\">\n        <!-- Filtres -->\n        <div class=\"filters-section\">\n          <h2>🔍 Filtres</h2>\n          <div class=\"filter-group\">\n            <label>Période</label>\n            <select [(ngModel)]=\"selectedPeriod\" class=\"form-select\">\n              <option value=\"today\">Aujourd'hui</option>\n              <option value=\"week\">Cette semaine</option>\n              <option value=\"month\">Ce mois</option>\n              <option value=\"quarter\">Ce trimestre</option>\n              <option value=\"year\">Cette année</option>\n              <option value=\"custom\">Personnalisé</option>\n            </select>\n          </div>\n          \n          <div class=\"filter-group\" *ngIf=\"selectedPeriod === 'custom'\">\n            <label>Date de début</label>\n            <input type=\"date\" [(ngModel)]=\"startDate\" class=\"form-input\">\n          </div>\n          \n          <div class=\"filter-group\" *ngIf=\"selectedPeriod === 'custom'\">\n            <label>Date de fin</label>\n            <input type=\"date\" [(ngModel)]=\"endDate\" class=\"form-input\">\n          </div>\n\n          <button class=\"btn-generate\" (click)=\"generateReports()\">\n            📊 Générer les rapports\n          </button>\n        </div>\n\n        <!-- Types de rapports -->\n        <div class=\"report-types\">\n          <h2>📋 Types de rapports</h2>\n          <div class=\"report-card\" *ngFor=\"let report of reportTypes\" (click)=\"selectReport(report)\">\n            <div class=\"report-icon\">{{ report.icon }}</div>\n            <div class=\"report-info\">\n              <h3>{{ report.name }}</h3>\n              <p>{{ report.description }}</p>\n            </div>\n            <div class=\"report-action\">\n              <button class=\"btn-download\">⬇️</button>\n            </div>\n          </div>\n        </div>\n\n        <!-- Graphiques -->\n        <div class=\"charts-section\">\n          <h2>📊 Visualisations</h2>\n          \n          <!-- Graphique des ventes -->\n          <div class=\"chart-container\">\n            <h3>Évolution des ventes</h3>\n            <div class=\"chart-placeholder\">\n              <div class=\"chart-bar\" *ngFor=\"let data of salesData\" [style.height.%]=\"data.percentage\">\n                <span class=\"chart-value\">{{ data.value }}</span>\n              </div>\n            </div>\n          </div>\n\n          <!-- Graphique des utilisateurs -->\n          <div class=\"chart-container\">\n            <h3>Nouveaux utilisateurs</h3>\n            <div class=\"chart-placeholder\">\n              <div class=\"chart-line\">\n                <div class=\"line-point\" *ngFor=\"let point of userGrowth\" \n                     [style.left.%]=\"point.x\" \n                     [style.bottom.%]=\"point.y\">\n                </div>\n              </div>\n            </div>\n          </div>\n        </div>\n\n        <!-- Métriques clés -->\n        <div class=\"metrics-section\">\n          <h2>🎯 Métriques clés</h2>\n          <div class=\"metrics-grid\">\n            <div class=\"metric-card\" *ngFor=\"let metric of keyMetrics\">\n              <div class=\"metric-icon\">{{ metric.icon }}</div>\n              <div class=\"metric-content\">\n                <div class=\"metric-value\">{{ metric.value }}</div>\n                <div class=\"metric-label\">{{ metric.label }}</div>\n                <div class=\"metric-change\" [class]=\"metric.changeType\">\n                  {{ metric.change }}\n                </div>\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n  `,\n  styles: [`\n    .reports-page {\n      padding: 2rem;\n      background: #f8fafc;\n      min-height: 100vh;\n    }\n\n    .page-header {\n      margin-bottom: 2rem;\n    }\n\n    .page-header h1 {\n      font-size: 2rem;\n      font-weight: 700;\n      color: #1e293b;\n      margin: 0 0 0.5rem 0;\n    }\n\n    .page-header p {\n      color: #64748b;\n      margin: 0;\n    }\n\n    .reports-grid {\n      display: grid;\n      grid-template-columns: 300px 1fr;\n      gap: 2rem;\n    }\n\n    .filters-section, .report-types, .charts-section, .metrics-section {\n      background: white;\n      padding: 2rem;\n      border-radius: 16px;\n      box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);\n      margin-bottom: 2rem;\n    }\n\n    .filters-section h2, .report-types h2, .charts-section h2, .metrics-section h2 {\n      font-size: 1.25rem;\n      font-weight: 600;\n      color: #1e293b;\n      margin: 0 0 1.5rem 0;\n    }\n\n    .filter-group {\n      margin-bottom: 1rem;\n    }\n\n    .filter-group label {\n      display: block;\n      font-weight: 500;\n      color: #374151;\n      margin-bottom: 0.5rem;\n    }\n\n    .form-select, .form-input {\n      width: 100%;\n      padding: 0.75rem;\n      border: 1px solid #d1d5db;\n      border-radius: 8px;\n      font-size: 0.875rem;\n    }\n\n    .btn-generate {\n      width: 100%;\n      padding: 1rem;\n      background: linear-gradient(135deg, #667eea, #764ba2);\n      color: white;\n      border: none;\n      border-radius: 12px;\n      font-weight: 600;\n      cursor: pointer;\n      margin-top: 1rem;\n    }\n\n    .report-card {\n      display: flex;\n      align-items: center;\n      padding: 1rem;\n      border: 1px solid #e5e7eb;\n      border-radius: 12px;\n      margin-bottom: 1rem;\n      cursor: pointer;\n      transition: all 0.3s ease;\n    }\n\n    .report-card:hover {\n      border-color: #667eea;\n      transform: translateY(-2px);\n      box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);\n    }\n\n    .report-icon {\n      font-size: 2rem;\n      margin-right: 1rem;\n    }\n\n    .report-info {\n      flex: 1;\n    }\n\n    .report-info h3 {\n      font-size: 1rem;\n      font-weight: 600;\n      color: #1e293b;\n      margin: 0 0 0.25rem 0;\n    }\n\n    .report-info p {\n      color: #64748b;\n      font-size: 0.875rem;\n      margin: 0;\n    }\n\n    .btn-download {\n      background: #f1f5f9;\n      border: 1px solid #e2e8f0;\n      border-radius: 8px;\n      padding: 0.5rem;\n      cursor: pointer;\n    }\n\n    .chart-container {\n      margin-bottom: 2rem;\n    }\n\n    .chart-container h3 {\n      font-size: 1.125rem;\n      font-weight: 600;\n      color: #1e293b;\n      margin-bottom: 1rem;\n    }\n\n    .chart-placeholder {\n      height: 200px;\n      background: #f8fafc;\n      border-radius: 8px;\n      display: flex;\n      align-items: end;\n      justify-content: space-around;\n      padding: 1rem;\n      position: relative;\n    }\n\n    .chart-bar {\n      width: 40px;\n      background: linear-gradient(135deg, #667eea, #764ba2);\n      border-radius: 4px 4px 0 0;\n      position: relative;\n      min-height: 20px;\n    }\n\n    .chart-value {\n      position: absolute;\n      top: -25px;\n      left: 50%;\n      transform: translateX(-50%);\n      font-size: 0.75rem;\n      font-weight: 600;\n      color: #374151;\n    }\n\n    .chart-line {\n      width: 100%;\n      height: 100%;\n      position: relative;\n    }\n\n    .line-point {\n      position: absolute;\n      width: 8px;\n      height: 8px;\n      background: #667eea;\n      border-radius: 50%;\n      transform: translate(-50%, 50%);\n    }\n\n    .metrics-grid {\n      display: grid;\n      grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));\n      gap: 1rem;\n    }\n\n    .metric-card {\n      display: flex;\n      align-items: center;\n      padding: 1.5rem;\n      background: #f8fafc;\n      border-radius: 12px;\n      gap: 1rem;\n    }\n\n    .metric-icon {\n      font-size: 2rem;\n    }\n\n    .metric-value {\n      font-size: 1.5rem;\n      font-weight: 700;\n      color: #1e293b;\n    }\n\n    .metric-label {\n      color: #64748b;\n      font-size: 0.875rem;\n    }\n\n    .metric-change {\n      font-size: 0.75rem;\n      font-weight: 600;\n      padding: 0.25rem 0.5rem;\n      border-radius: 12px;\n    }\n\n    .metric-change.positive {\n      background: #dcfce7;\n      color: #166534;\n    }\n\n    .metric-change.negative {\n      background: #fecaca;\n      color: #991b1b;\n    }\n\n    @media (max-width: 768px) {\n      .reports-page {\n        padding: 1rem;\n      }\n      \n      .reports-grid {\n        grid-template-columns: 1fr;\n      }\n      \n      .metrics-grid {\n        grid-template-columns: 1fr;\n      }\n    }\n  `]\n})\nexport class ReportsComponent implements OnInit {\n  selectedPeriod = 'month';\n  startDate = '';\n  endDate = '';\n\n  reportTypes = [\n    {\n      id: 'sales',\n      name: 'Rapport des ventes',\n      description: 'Analyse détaillée des ventes par période',\n      icon: '💰'\n    },\n    {\n      id: 'users',\n      name: 'Rapport utilisateurs',\n      description: 'Statistiques d\\'inscription et d\\'activité',\n      icon: '👥'\n    },\n    {\n      id: 'products',\n      name: 'Rapport produits',\n      description: 'Performance des produits et stock',\n      icon: '📦'\n    },\n    {\n      id: 'orders',\n      name: 'Rapport commandes',\n      description: 'Analyse des commandes et livraisons',\n      icon: '🛒'\n    }\n  ];\n\n  salesData = [\n    { value: 1200, percentage: 60 },\n    { value: 1800, percentage: 90 },\n    { value: 1500, percentage: 75 },\n    { value: 2000, percentage: 100 },\n    { value: 1700, percentage: 85 },\n    { value: 2200, percentage: 110 },\n    { value: 1900, percentage: 95 }\n  ];\n\n  userGrowth = [\n    { x: 10, y: 20 },\n    { x: 25, y: 35 },\n    { x: 40, y: 45 },\n    { x: 55, y: 60 },\n    { x: 70, y: 55 },\n    { x: 85, y: 75 }\n  ];\n\n  keyMetrics = [\n    {\n      icon: '💰',\n      value: '€45,230',\n      label: 'Revenus totaux',\n      change: '+12.5%',\n      changeType: 'positive'\n    },\n    {\n      icon: '🛒',\n      value: '1,234',\n      label: 'Commandes',\n      change: '+8.2%',\n      changeType: 'positive'\n    },\n    {\n      icon: '👥',\n      value: '856',\n      label: 'Nouveaux clients',\n      change: '-2.1%',\n      changeType: 'negative'\n    },\n    {\n      icon: '📦',\n      value: '2,456',\n      label: 'Produits vendus',\n      change: '+15.3%',\n      changeType: 'positive'\n    }\n  ];\n\n  ngOnInit(): void {\n    this.loadReportsData();\n  }\n\n  loadReportsData(): void {\n    // Charger les données des rapports\n    console.log('Chargement des données de rapports...');\n  }\n\n  generateReports(): void {\n    console.log('Génération des rapports pour la période:', this.selectedPeriod);\n    // Générer les rapports selon les filtres\n  }\n\n  selectReport(report: any): void {\n    console.log('Rapport sélectionné:', report);\n    // Afficher le rapport détaillé\n  }\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA+BU,IAAA,yBAAA,GAAA,OAAA,CAAA,EAA8D,GAAA,OAAA;AACrD,IAAA,iBAAA,GAAA,kBAAA;AAAa,IAAA,uBAAA;AACpB,IAAA,yBAAA,GAAA,SAAA,EAAA;AAAmB,IAAA,2BAAA,iBAAA,SAAA,gEAAA,QAAA;AAAA,MAAA,wBAAA,GAAA;AAAA,YAAA,SAAA,wBAAA;AAAA,MAAA,6BAAA,OAAA,WAAA,MAAA,MAAA,OAAA,YAAA;AAAA,aAAA,sBAAA,MAAA;IAAA,CAAA;AAAnB,IAAA,uBAAA,EAA8D;;;;AAA3C,IAAA,oBAAA,CAAA;AAAA,IAAA,2BAAA,WAAA,OAAA,SAAA;;;;;;AAGrB,IAAA,yBAAA,GAAA,OAAA,CAAA,EAA8D,GAAA,OAAA;AACrD,IAAA,iBAAA,GAAA,aAAA;AAAW,IAAA,uBAAA;AAClB,IAAA,yBAAA,GAAA,SAAA,EAAA;AAAmB,IAAA,2BAAA,iBAAA,SAAA,gEAAA,QAAA;AAAA,MAAA,wBAAA,GAAA;AAAA,YAAA,SAAA,wBAAA;AAAA,MAAA,6BAAA,OAAA,SAAA,MAAA,MAAA,OAAA,UAAA;AAAA,aAAA,sBAAA,MAAA;IAAA,CAAA;AAAnB,IAAA,uBAAA,EAA4D;;;;AAAzC,IAAA,oBAAA,CAAA;AAAA,IAAA,2BAAA,WAAA,OAAA,OAAA;;;;;;AAWrB,IAAA,yBAAA,GAAA,OAAA,EAAA;AAA4D,IAAA,qBAAA,SAAA,SAAA,wDAAA;AAAA,YAAA,YAAA,wBAAA,GAAA,EAAA;AAAA,YAAA,SAAA,wBAAA;AAAA,aAAA,sBAAS,OAAA,aAAA,SAAA,CAAoB;IAAA,CAAA;AACvF,IAAA,yBAAA,GAAA,OAAA,EAAA;AAAyB,IAAA,iBAAA,CAAA;AAAiB,IAAA,uBAAA;AAC1C,IAAA,yBAAA,GAAA,OAAA,EAAA,EAAyB,GAAA,IAAA;AACnB,IAAA,iBAAA,CAAA;AAAiB,IAAA,uBAAA;AACrB,IAAA,yBAAA,GAAA,GAAA;AAAG,IAAA,iBAAA,CAAA;AAAwB,IAAA,uBAAA,EAAI;AAEjC,IAAA,yBAAA,GAAA,OAAA,EAAA,EAA2B,GAAA,UAAA,EAAA;AACI,IAAA,iBAAA,IAAA,cAAA;AAAE,IAAA,uBAAA,EAAS,EACpC;;;;AAPmB,IAAA,oBAAA,CAAA;AAAA,IAAA,4BAAA,UAAA,IAAA;AAEnB,IAAA,oBAAA,CAAA;AAAA,IAAA,4BAAA,UAAA,IAAA;AACD,IAAA,oBAAA,CAAA;AAAA,IAAA,4BAAA,UAAA,WAAA;;;;;AAgBH,IAAA,yBAAA,GAAA,OAAA,EAAA,EAAyF,GAAA,QAAA,EAAA;AAC7D,IAAA,iBAAA,CAAA;AAAgB,IAAA,uBAAA,EAAO;;;;AADG,IAAA,sBAAA,UAAA,QAAA,YAAA,GAAA;AAC1B,IAAA,oBAAA,CAAA;AAAA,IAAA,4BAAA,QAAA,KAAA;;;;;AAU1B,IAAA,oBAAA,GAAA,OAAA,EAAA;;;;AACK,IAAA,sBAAA,QAAA,SAAA,GAAA,GAAA,EAAwB,UAAA,SAAA,GAAA,GAAA;;;;;AAYjC,IAAA,yBAAA,GAAA,OAAA,EAAA,EAA2D,GAAA,OAAA,EAAA;AAChC,IAAA,iBAAA,CAAA;AAAiB,IAAA,uBAAA;AAC1C,IAAA,yBAAA,GAAA,OAAA,EAAA,EAA4B,GAAA,OAAA,EAAA;AACA,IAAA,iBAAA,CAAA;AAAkB,IAAA,uBAAA;AAC5C,IAAA,yBAAA,GAAA,OAAA,EAAA;AAA0B,IAAA,iBAAA,CAAA;AAAkB,IAAA,uBAAA;AAC5C,IAAA,yBAAA,GAAA,OAAA,EAAA;AACE,IAAA,iBAAA,CAAA;AACF,IAAA,uBAAA,EAAM,EACF;;;;AAPmB,IAAA,oBAAA,CAAA;AAAA,IAAA,4BAAA,UAAA,IAAA;AAEG,IAAA,oBAAA,CAAA;AAAA,IAAA,4BAAA,UAAA,KAAA;AACA,IAAA,oBAAA,CAAA;AAAA,IAAA,4BAAA,UAAA,KAAA;AACC,IAAA,oBAAA;AAAA,IAAA,qBAAA,UAAA,UAAA;AACzB,IAAA,oBAAA;AAAA,IAAA,6BAAA,KAAA,UAAA,QAAA,GAAA;;;AAwPZ,IAAO,mBAAP,MAAO,kBAAgB;EAC3B,iBAAiB;EACjB,YAAY;EACZ,UAAU;EAEV,cAAc;IACZ;MACE,IAAI;MACJ,MAAM;MACN,aAAa;MACb,MAAM;;IAER;MACE,IAAI;MACJ,MAAM;MACN,aAAa;MACb,MAAM;;IAER;MACE,IAAI;MACJ,MAAM;MACN,aAAa;MACb,MAAM;;IAER;MACE,IAAI;MACJ,MAAM;MACN,aAAa;MACb,MAAM;;;EAIV,YAAY;IACV,EAAE,OAAO,MAAM,YAAY,GAAE;IAC7B,EAAE,OAAO,MAAM,YAAY,GAAE;IAC7B,EAAE,OAAO,MAAM,YAAY,GAAE;IAC7B,EAAE,OAAO,KAAM,YAAY,IAAG;IAC9B,EAAE,OAAO,MAAM,YAAY,GAAE;IAC7B,EAAE,OAAO,MAAM,YAAY,IAAG;IAC9B,EAAE,OAAO,MAAM,YAAY,GAAE;;EAG/B,aAAa;IACX,EAAE,GAAG,IAAI,GAAG,GAAE;IACd,EAAE,GAAG,IAAI,GAAG,GAAE;IACd,EAAE,GAAG,IAAI,GAAG,GAAE;IACd,EAAE,GAAG,IAAI,GAAG,GAAE;IACd,EAAE,GAAG,IAAI,GAAG,GAAE;IACd,EAAE,GAAG,IAAI,GAAG,GAAE;;EAGhB,aAAa;IACX;MACE,MAAM;MACN,OAAO;MACP,OAAO;MACP,QAAQ;MACR,YAAY;;IAEd;MACE,MAAM;MACN,OAAO;MACP,OAAO;MACP,QAAQ;MACR,YAAY;;IAEd;MACE,MAAM;MACN,OAAO;MACP,OAAO;MACP,QAAQ;MACR,YAAY;;IAEd;MACE,MAAM;MACN,OAAO;MACP,OAAO;MACP,QAAQ;MACR,YAAY;;;EAIhB,WAAQ;AACN,SAAK,gBAAe;EACtB;EAEA,kBAAe;AAEb,YAAQ,IAAI,0CAAuC;EACrD;EAEA,kBAAe;AACb,YAAQ,IAAI,qDAA4C,KAAK,cAAc;EAE7E;EAEA,aAAa,QAAW;AACtB,YAAQ,IAAI,8BAAwB,MAAM;EAE5C;;qCAnGW,mBAAgB;EAAA;yEAAhB,mBAAgB,WAAA,CAAA,CAAA,mBAAA,CAAA,GAAA,OAAA,IAAA,MAAA,GAAA,QAAA,CAAA,CAAA,GAAA,cAAA,GAAA,CAAA,GAAA,aAAA,GAAA,CAAA,GAAA,cAAA,GAAA,CAAA,GAAA,iBAAA,GAAA,CAAA,GAAA,cAAA,GAAA,CAAA,GAAA,eAAA,GAAA,iBAAA,SAAA,GAAA,CAAA,SAAA,OAAA,GAAA,CAAA,SAAA,MAAA,GAAA,CAAA,SAAA,OAAA,GAAA,CAAA,SAAA,SAAA,GAAA,CAAA,SAAA,MAAA,GAAA,CAAA,SAAA,QAAA,GAAA,CAAA,SAAA,gBAAA,GAAA,MAAA,GAAA,CAAA,GAAA,gBAAA,GAAA,OAAA,GAAA,CAAA,GAAA,cAAA,GAAA,CAAA,SAAA,eAAA,GAAA,SAAA,GAAA,SAAA,SAAA,GAAA,CAAA,GAAA,gBAAA,GAAA,CAAA,GAAA,iBAAA,GAAA,CAAA,GAAA,mBAAA,GAAA,CAAA,SAAA,aAAA,GAAA,UAAA,GAAA,SAAA,SAAA,GAAA,CAAA,GAAA,YAAA,GAAA,CAAA,SAAA,cAAA,GAAA,QAAA,UAAA,GAAA,SAAA,SAAA,GAAA,CAAA,GAAA,iBAAA,GAAA,CAAA,GAAA,cAAA,GAAA,CAAA,SAAA,eAAA,GAAA,SAAA,SAAA,GAAA,CAAA,QAAA,QAAA,GAAA,cAAA,GAAA,iBAAA,SAAA,GAAA,CAAA,GAAA,eAAA,GAAA,OAAA,GAAA,CAAA,GAAA,aAAA,GAAA,CAAA,GAAA,aAAA,GAAA,CAAA,GAAA,eAAA,GAAA,CAAA,GAAA,cAAA,GAAA,CAAA,GAAA,WAAA,GAAA,CAAA,GAAA,aAAA,GAAA,CAAA,GAAA,YAAA,GAAA,CAAA,GAAA,aAAA,GAAA,CAAA,GAAA,aAAA,GAAA,CAAA,GAAA,gBAAA,GAAA,CAAA,GAAA,cAAA,GAAA,CAAA,GAAA,cAAA,GAAA,CAAA,GAAA,eAAA,CAAA,GAAA,UAAA,SAAA,0BAAA,IAAA,KAAA;AAAA,QAAA,KAAA,GAAA;AAlVzB,MAAA,yBAAA,GAAA,OAAA,CAAA,EAA0B,GAAA,OAAA,CAAA,EACC,GAAA,IAAA;AACnB,MAAA,iBAAA,GAAA,gCAAA;AAAuB,MAAA,uBAAA;AAC3B,MAAA,yBAAA,GAAA,GAAA;AAAG,MAAA,iBAAA,GAAA,wDAAA;AAAgD,MAAA,uBAAA,EAAI;AAGzD,MAAA,yBAAA,GAAA,OAAA,CAAA,EAA0B,GAAA,OAAA,CAAA,EAEK,GAAA,IAAA;AACvB,MAAA,iBAAA,GAAA,mBAAA;AAAU,MAAA,uBAAA;AACd,MAAA,yBAAA,IAAA,OAAA,CAAA,EAA0B,IAAA,OAAA;AACjB,MAAA,iBAAA,IAAA,YAAA;AAAO,MAAA,uBAAA;AACd,MAAA,yBAAA,IAAA,UAAA,CAAA;AAAQ,MAAA,2BAAA,iBAAA,SAAA,2DAAA,QAAA;AAAA,QAAA,6BAAA,IAAA,gBAAA,MAAA,MAAA,IAAA,iBAAA;AAAA,eAAA;MAAA,CAAA;AACN,MAAA,yBAAA,IAAA,UAAA,CAAA;AAAsB,MAAA,iBAAA,IAAA,aAAA;AAAW,MAAA,uBAAA;AACjC,MAAA,yBAAA,IAAA,UAAA,CAAA;AAAqB,MAAA,iBAAA,IAAA,eAAA;AAAa,MAAA,uBAAA;AAClC,MAAA,yBAAA,IAAA,UAAA,CAAA;AAAsB,MAAA,iBAAA,IAAA,SAAA;AAAO,MAAA,uBAAA;AAC7B,MAAA,yBAAA,IAAA,UAAA,CAAA;AAAwB,MAAA,iBAAA,IAAA,cAAA;AAAY,MAAA,uBAAA;AACpC,MAAA,yBAAA,IAAA,UAAA,EAAA;AAAqB,MAAA,iBAAA,IAAA,gBAAA;AAAW,MAAA,uBAAA;AAChC,MAAA,yBAAA,IAAA,UAAA,EAAA;AAAuB,MAAA,iBAAA,IAAA,iBAAA;AAAY,MAAA,uBAAA,EAAS,EACrC;AAGX,MAAA,qBAAA,IAAA,kCAAA,GAAA,GAAA,OAAA,EAAA,EAA8D,IAAA,kCAAA,GAAA,GAAA,OAAA,EAAA;AAU9D,MAAA,yBAAA,IAAA,UAAA,EAAA;AAA6B,MAAA,qBAAA,SAAA,SAAA,qDAAA;AAAA,eAAS,IAAA,gBAAA;MAAiB,CAAA;AACrD,MAAA,iBAAA,IAAA,wCAAA;AACF,MAAA,uBAAA,EAAS;AAIX,MAAA,yBAAA,IAAA,OAAA,EAAA,EAA0B,IAAA,IAAA;AACpB,MAAA,iBAAA,IAAA,6BAAA;AAAoB,MAAA,uBAAA;AACxB,MAAA,qBAAA,IAAA,kCAAA,IAAA,GAAA,OAAA,EAAA;AAUF,MAAA,uBAAA;AAGA,MAAA,yBAAA,IAAA,OAAA,EAAA,EAA4B,IAAA,IAAA;AACtB,MAAA,iBAAA,IAAA,0BAAA;AAAiB,MAAA,uBAAA;AAGrB,MAAA,yBAAA,IAAA,OAAA,EAAA,EAA6B,IAAA,IAAA;AACvB,MAAA,iBAAA,IAAA,yBAAA;AAAoB,MAAA,uBAAA;AACxB,MAAA,yBAAA,IAAA,OAAA,EAAA;AACE,MAAA,qBAAA,IAAA,kCAAA,GAAA,GAAA,OAAA,EAAA;AAGF,MAAA,uBAAA,EAAM;AAIR,MAAA,yBAAA,IAAA,OAAA,EAAA,EAA6B,IAAA,IAAA;AACvB,MAAA,iBAAA,IAAA,uBAAA;AAAqB,MAAA,uBAAA;AACzB,MAAA,yBAAA,IAAA,OAAA,EAAA,EAA+B,IAAA,OAAA,EAAA;AAE3B,MAAA,qBAAA,IAAA,kCAAA,GAAA,GAAA,OAAA,EAAA;AAIF,MAAA,uBAAA,EAAM,EACF,EACF;AAIR,MAAA,yBAAA,IAAA,OAAA,EAAA,EAA6B,IAAA,IAAA;AACvB,MAAA,iBAAA,IAAA,gCAAA;AAAiB,MAAA,uBAAA;AACrB,MAAA,yBAAA,IAAA,OAAA,EAAA;AACE,MAAA,qBAAA,IAAA,kCAAA,IAAA,GAAA,OAAA,EAAA;AAUF,MAAA,uBAAA,EAAM,EACF,EACF;;;AApFQ,MAAA,oBAAA,EAAA;AAAA,MAAA,2BAAA,WAAA,IAAA,cAAA;AAUiB,MAAA,oBAAA,EAAA;AAAA,MAAA,qBAAA,QAAA,IAAA,mBAAA,QAAA;AAKA,MAAA,oBAAA;AAAA,MAAA,qBAAA,QAAA,IAAA,mBAAA,QAAA;AAaiB,MAAA,oBAAA,CAAA;AAAA,MAAA,qBAAA,WAAA,IAAA,WAAA;AAoBA,MAAA,oBAAA,CAAA;AAAA,MAAA,qBAAA,WAAA,IAAA,SAAA;AAWI,MAAA,oBAAA,CAAA;AAAA,MAAA,qBAAA,WAAA,IAAA,UAAA;AAaF,MAAA,oBAAA,CAAA;AAAA,MAAA,qBAAA,WAAA,IAAA,UAAA;;oBAtF5C,cAAY,SAAA,MAAE,aAAW,gBAAA,8BAAA,sBAAA,4BAAA,iBAAA,OAAA,GAAA,QAAA,CAAA,s5JAAA,EAAA,CAAA;;;sEAoVxB,kBAAgB,CAAA;UAvV5B;uBACW,qBAAmB,YACjB,MAAI,SACP,CAAC,cAAc,WAAW,GAAC,UAC1B;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;KAmGT,QAAA,CAAA,4lIAAA,EAAA,CAAA;;;;6EAgPU,kBAAgB,EAAA,WAAA,oBAAA,UAAA,yDAAA,YAAA,IAAA,CAAA;AAAA,GAAA;", "names": []}
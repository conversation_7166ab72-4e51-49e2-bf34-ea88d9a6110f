using System.ComponentModel.DataAnnotations;
using WebApiPfe.Models.Entity;

namespace WebApiPfe.DTOs.Admin
{
    public class AvisModerationDto
    {
        public int Id { get; set; }
        
        [Range(1, 5)]
        public int Note { get; set; }
        
        public string? Commentaire { get; set; }
        
        public DateTime DatePublication { get; set; }
        
        public StatutAvis Statut { get; set; }

        public string StatutLibelle { get; set; } = string.Empty;

        // Indique si le commentaire a été supprimé par l'admin
        public bool CommentaireSupprime { get; set; } = false;

        public DateTime? DateModeration { get; set; }

        public string? CommentaireModeration { get; set; }
        
        public string? NomModerateur { get; set; }
        
        // Informations client
        public int ClientId { get; set; }
        public string ClientNom { get; set; } = string.Empty;
        public string ClientPrenom { get; set; } = string.Empty;
        public string ClientEmail { get; set; } = string.Empty;
        
        // Informations produit
        public int ProduitId { get; set; }
        public string ProduitNom { get; set; } = string.Empty;
        public string ProduitReference { get; set; } = string.Empty;
        
        // Informations fournisseur
        public int FournisseurId { get; set; }
        public string FournisseurNom { get; set; } = string.Empty;
        public string FournisseurRaisonSociale { get; set; } = string.Empty;
    }

    public class ModererAvisDto
    {
        [Required]
        public StatutAvis Statut { get; set; }
        
        [StringLength(500, ErrorMessage = "Le commentaire de modération ne peut pas dépasser 500 caractères")]
        public string? CommentaireModeration { get; set; }
    }

    public class AvisFilterDto
    {
        public StatutAvis? Statut { get; set; }
        public string? FournisseurId { get; set; }
        public int? ProduitId { get; set; }
        public DateTime? DateDebut { get; set; }
        public DateTime? DateFin { get; set; }
        public string? Recherche { get; set; }
        public int Page { get; set; } = 1;
        public int PageSize { get; set; } = 10;
        public string SortBy { get; set; } = "DatePublication";
        public bool SortDesc { get; set; } = true;
    }

    public class SupprimerCommentaireDto
    {
        public string? RaisonSuppression { get; set; }
    }

    public class SignalerAvisDto
    {
        [Required(ErrorMessage = "La raison du signalement est obligatoire")]
        [StringLength(500, ErrorMessage = "La raison du signalement ne peut pas dépasser 500 caractères")]
        public string RaisonSignalement { get; set; } = string.Empty;
    }

    public class AvisStatsDto
    {
        public int TotalAvis { get; set; }
        public int AvisPublies { get; set; }
        public int AvisCommentaireSupprime { get; set; }
        public int AvisSignales { get; set; }
        public double NoteMoyenneGlobale { get; set; }
        public Dictionary<int, int> AvisParNote { get; set; } = new Dictionary<int, int>();
        public List<AvisModerationDto> AvisRecents { get; set; } = new List<AvisModerationDto>();
    }
}

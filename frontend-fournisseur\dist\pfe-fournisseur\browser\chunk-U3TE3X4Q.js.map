{"version": 3, "sources": ["src/app/components/admin/tools/batch-operations/batch-operations.component.ts"], "sourcesContent": ["import { Component } from '@angular/core';\nimport { CommonModule } from '@angular/common';\n\n@Component({\n  selector: 'app-batch-operations',\n  standalone: true,\n  imports: [CommonModule],\n  template: `<div><h1>⚙️ Opérations par lots</h1><p>En développement...</p></div>`\n})\nexport class BatchOperationsComponent {}\n"], "mappings": ";;;;;;;;;;;;AASM,IAAO,2BAAP,MAAO,0BAAwB;;qCAAxB,2BAAwB;EAAA;yEAAxB,2BAAwB,WAAA,CAAA,CAAA,sBAAA,CAAA,GAAA,OAAA,GAAA,MAAA,GAAA,UAAA,SAAA,kCAAA,IAAA,KAAA;AAAA,QAAA,KAAA,GAAA;AAFxB,MAAA,yBAAA,GAAA,KAAA,EAAK,GAAA,IAAA;AAAI,MAAA,iBAAA,GAAA,qCAAA;AAAsB,MAAA,uBAAA;AAAK,MAAA,yBAAA,GAAA,GAAA;AAAG,MAAA,iBAAA,GAAA,wBAAA;AAAmB,MAAA,uBAAA,EAAI;;oBAD/D,YAAY,GAAA,eAAA,EAAA,CAAA;;;sEAGX,0BAAwB,CAAA;UANpC;WAAU;MACT,UAAU;MACV,YAAY;MACZ,SAAS,CAAC,YAAY;MACtB,UAAU;KACX;;;;6EACY,0BAAwB,EAAA,WAAA,4BAAA,UAAA,iFAAA,YAAA,GAAA,CAAA;AAAA,GAAA;", "names": []}
{"version": 3, "sources": ["angular:styles/component:css;d4877b2aa7799027be6eb862e21536d67532f77c47a5d94bea4ca1fd812fec43;C:/Users/<USER>/Desktop/STE/PFE/PFE/frontend-fournisseur/src/app/notifications/notifications.component.ts"], "sourcesContent": ["\n    .notifications-container {\n      max-width: 900px;\n      margin: 0 auto;\n      padding: 24px;\n    }\n\n    .notifications-header {\n      display: flex;\n      justify-content: space-between;\n      align-items: center;\n      margin-bottom: 24px;\n      padding: 20px;\n      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n      border-radius: 12px;\n      color: white;\n    }\n\n    .notifications-header h2 {\n      margin: 0;\n      display: flex;\n      align-items: center;\n      gap: 12px;\n      font-size: 24px;\n      font-weight: 500;\n    }\n\n    .primary-btn {\n      background: rgba(255, 255, 255, 0.2);\n      border: 1px solid rgba(255, 255, 255, 0.3);\n      color: white;\n      padding: 8px 16px;\n      border-radius: 6px;\n      cursor: pointer;\n      display: flex;\n      align-items: center;\n      gap: 8px;\n      font-size: 14px;\n      transition: all 0.2s;\n    }\n\n    .primary-btn:hover {\n      background: rgba(255, 255, 255, 0.3);\n    }\n\n    .notifications-stats {\n      margin-bottom: 24px;\n    }\n\n    .stats-chips {\n      display: flex;\n      gap: 12px;\n      flex-wrap: wrap;\n    }\n\n    .stat-chip {\n      display: flex;\n      align-items: center;\n      gap: 8px;\n      padding: 8px 16px;\n      background: #f5f5f5;\n      border-radius: 20px;\n      font-size: 14px;\n      color: #333;\n    }\n\n    .stat-chip.warn {\n      background: #ffebee;\n      color: #c62828;\n    }\n\n    .loading-container {\n      text-align: center;\n      padding: 48px;\n    }\n\n    .spinner {\n      width: 40px;\n      height: 40px;\n      border: 4px solid #f3f3f3;\n      border-top: 4px solid #667eea;\n      border-radius: 50%;\n      animation: spin 1s linear infinite;\n      margin: 0 auto 16px;\n    }\n\n    @keyframes spin {\n      0% { transform: rotate(0deg); }\n      100% { transform: rotate(360deg); }\n    }\n\n    .empty-state {\n      text-align: center;\n      padding: 48px;\n      color: #666;\n    }\n\n    .empty-icon {\n      margin-bottom: 16px;\n      color: #ccc;\n    }\n\n    .empty-subtitle {\n      margin-top: 24px;\n      font-weight: 500;\n      color: #333;\n    }\n\n    .empty-list {\n      text-align: left;\n      display: inline-block;\n      margin-top: 16px;\n      list-style: none;\n      padding: 0;\n    }\n\n    .empty-list li {\n      margin-bottom: 8px;\n      color: #666;\n    }\n\n    .notifications-list {\n      display: flex;\n      flex-direction: column;\n      gap: 16px;\n    }\n\n    .notification-card {\n      background: white;\n      border-radius: 12px;\n      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\n      transition: all 0.3s ease;\n      border-left: 4px solid transparent;\n      overflow: hidden;\n    }\n\n    .notification-card.unread {\n      border-left-color: #2196f3;\n      background-color: #f8fbff;\n      box-shadow: 0 2px 8px rgba(33, 150, 243, 0.1);\n    }\n\n    .notification-card:hover {\n      box-shadow: 0 4px 16px rgba(0,0,0,0.12);\n      transform: translateY(-2px);\n    }\n\n    .notification-header {\n      display: flex;\n      align-items: flex-start;\n      gap: 12px;\n      padding: 16px;\n    }\n\n    .notification-status {\n      flex-shrink: 0;\n      padding-top: 4px;\n    }\n\n    .unread-indicator {\n      color: #2196f3;\n    }\n\n    .read-indicator {\n      color: #4caf50;\n    }\n\n    .notification-content {\n      flex: 1;\n      font-size: 16px;\n      line-height: 1.5;\n      margin: 0;\n      color: #333;\n    }\n\n    .notification-actions {\n      display: flex;\n      gap: 4px;\n      flex-shrink: 0;\n    }\n\n    .action-btn {\n      background: none;\n      border: none;\n      cursor: pointer;\n      padding: 8px;\n      border-radius: 4px;\n      transition: all 0.2s;\n      display: flex;\n      align-items: center;\n      justify-content: center;\n    }\n\n    .action-btn.primary {\n      color: #2196f3;\n    }\n\n    .action-btn.primary:hover {\n      background: rgba(33, 150, 243, 0.1);\n    }\n\n    .action-btn.delete {\n      color: #f44336;\n    }\n\n    .action-btn.delete:hover {\n      background: rgba(244, 67, 54, 0.1);\n    }\n\n    .notification-meta {\n      display: flex;\n      align-items: center;\n      gap: 16px;\n      margin-top: 8px;\n      padding: 0 16px 16px;\n    }\n\n    .notification-date {\n      display: flex;\n      align-items: center;\n      gap: 4px;\n      font-size: 14px;\n      color: #666;\n    }\n\n    @media (max-width: 768px) {\n      .notifications-container {\n        padding: 16px;\n      }\n\n      .notifications-header {\n        flex-direction: column;\n        align-items: flex-start;\n        gap: 16px;\n        text-align: center;\n      }\n\n      .notifications-header h2 {\n        font-size: 20px;\n      }\n\n      .notification-header {\n        flex-direction: column;\n        align-items: flex-start;\n        gap: 8px;\n      }\n\n      .notification-actions {\n        align-self: flex-end;\n      }\n\n      .stats-chips {\n        justify-content: center;\n      }\n    }\n  "], "mappings": ";AACI,CAAC;AACC,aAAW;AACX,UAAQ,EAAE;AACV,WAAS;AACX;AAEA,CAAC;AACC,WAAS;AACT,mBAAiB;AACjB,eAAa;AACb,iBAAe;AACf,WAAS;AACT;AAAA,IAAY;AAAA,MAAgB,MAAhB;AAAA,MAAwB,QAAQ,EAAhC;AAAA,MAAoC,QAAQ;AACxD,iBAAe;AACf,SAAO;AACT;AAEA,CAXC,qBAWqB;AACpB,UAAQ;AACR,WAAS;AACT,eAAa;AACb,OAAK;AACL,aAAW;AACX,eAAa;AACf;AAEA,CAAC;AACC,cAAY,KAAK,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE;AAChC,UAAQ,IAAI,MAAM,KAAK,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE;AACtC,SAAO;AACP,WAAS,IAAI;AACb,iBAAe;AACf,UAAQ;AACR,WAAS;AACT,eAAa;AACb,OAAK;AACL,aAAW;AACX,cAAY,IAAI;AAClB;AAEA,CAdC,WAcW;AACV,cAAY,KAAK,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE;AAClC;AAEA,CAAC;AACC,iBAAe;AACjB;AAEA,CAAC;AACC,WAAS;AACT,OAAK;AACL,aAAW;AACb;AAEA,CAAC;AACC,WAAS;AACT,eAAa;AACb,OAAK;AACL,WAAS,IAAI;AACb,cAAY;AACZ,iBAAe;AACf,aAAW;AACX,SAAO;AACT;AAEA,CAXC,SAWS,CAAC;AACT,cAAY;AACZ,SAAO;AACT;AAEA,CAAC;AACC,cAAY;AACZ,WAAS;AACX;AAEA,CAAC;AACC,SAAO;AACP,UAAQ;AACR,UAAQ,IAAI,MAAM;AAClB,cAAY,IAAI,MAAM;AACtB,iBAAe;AACf,aAAW,KAAK,GAAG,OAAO;AAC1B,UAAQ,EAAE,KAAK;AACjB;AAEA,WAJa;AAKX;AAAK,eAAW,OAAO;AAAO;AAC9B;AAAO,eAAW,OAAO;AAAS;AACpC;AAEA,CAAC;AACC,cAAY;AACZ,WAAS;AACT,SAAO;AACT;AAEA,CAAC;AACC,iBAAe;AACf,SAAO;AACT;AAEA,CAAC;AACC,cAAY;AACZ,eAAa;AACb,SAAO;AACT;AAEA,CAAC;AACC,cAAY;AACZ,WAAS;AACT,cAAY;AACZ,cAAY;AACZ,WAAS;AACX;AAEA,CARC,WAQW;AACV,iBAAe;AACf,SAAO;AACT;AAEA,CAAC;AACC,WAAS;AACT,kBAAgB;AAChB,OAAK;AACP;AAEA,CAAC;AACC,cAAY;AACZ,iBAAe;AACf,cAAY,EAAE,IAAI,IAAI,KAAK,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE;AACpC,cAAY,IAAI,KAAK;AACrB,eAAa,IAAI,MAAM;AACvB,YAAU;AACZ;AAEA,CATC,iBASiB,CAAC;AACjB,qBAAmB;AACnB,oBAAkB;AAClB,cAAY,EAAE,IAAI,IAAI,KAAK,EAAE,EAAE,GAAG,EAAE,GAAG,EAAE;AAC3C;AAEA,CAfC,iBAeiB;AAChB,cAAY,EAAE,IAAI,KAAK,KAAK,CAAC,EAAC,CAAC,EAAC,CAAC,EAAC;AAClC,aAAW,WAAW;AACxB;AAEA,CAAC;AACC,WAAS;AACT,eAAa;AACb,OAAK;AACL,WAAS;AACX;AAEA,CAAC;AACC,eAAa;AACb,eAAa;AACf;AAEA,CAAC;AACC,SAAO;AACT;AAEA,CAAC;AACC,SAAO;AACT;AAEA,CAAC;AACC,QAAM;AACN,aAAW;AACX,eAAa;AACb,UAAQ;AACR,SAAO;AACT;AAEA,CAAC;AACC,WAAS;AACT,OAAK;AACL,eAAa;AACf;AAEA,CAAC;AACC,cAAY;AACZ,UAAQ;AACR,UAAQ;AACR,WAAS;AACT,iBAAe;AACf,cAAY,IAAI;AAChB,WAAS;AACT,eAAa;AACb,mBAAiB;AACnB;AAEA,CAZC,UAYU,CAAC;AACV,SAAO;AACT;AAEA,CAhBC,UAgBU,CAJC,OAIO;AACjB,cAAY,KAAK,EAAE,EAAE,GAAG,EAAE,GAAG,EAAE;AACjC;AAEA,CApBC,UAoBU,CAAC;AACV,SAAO;AACT;AAEA,CAxBC,UAwBU,CAJC,MAIM;AAChB,cAAY,KAAK,GAAG,EAAE,EAAE,EAAE,EAAE,EAAE;AAChC;AAEA,CAAC;AACC,WAAS;AACT,eAAa;AACb,OAAK;AACL,cAAY;AACZ,WAAS,EAAE,KAAK;AAClB;AAEA,CAAC;AACC,WAAS;AACT,eAAa;AACb,OAAK;AACL,aAAW;AACX,SAAO;AACT;AAEA,OAAO,CAAC,SAAS,EAAE;AACjB,GAjOD;AAkOG,aAAS;AACX;AAEA,GA/ND;AAgOG,oBAAgB;AAChB,iBAAa;AACb,SAAK;AACL,gBAAY;AACd;AAEA,GAtOD,qBAsOuB;AACpB,eAAW;AACb;AAEA,GA9FD;AA+FG,oBAAgB;AAChB,iBAAa;AACb,SAAK;AACP;AAEA,GAxED;AAyEG,gBAAY;AACd;AAEA,GA1MD;AA2MG,qBAAiB;AACnB;AACF;", "names": []}
using Microsoft.EntityFrameworkCore;
using WebApiPfe.DTOs.Admin;
using WebApiPfe.Models.Entity;
using WebApiPfe.Models.Enum;
using WebApiPfe.Services.Interfaces;

namespace WebApiPfe.Services.Implementations
{
    public class PromotionGestionService : IPromotionGestionService
    {
        private readonly AppDbContext _context;
        private readonly ILogger<PromotionGestionService> _logger;

        public PromotionGestionService(AppDbContext context, ILogger<PromotionGestionService> logger)
        {
            _context = context;
            _logger = logger;
        }

        public async Task<List<PromotionGestionDto>> GetPromotionsAsync(PromotionFilterDto filter)
        {
            var query = _context.PromotionGestions
                .Include(p => p.Fournisseur)
                .Include(p => p.Produit)
                .AsQueryable();

            // Appliquer les filtres
            if (filter.Type.HasValue)
                query = query.Where(p => p.Type == filter.Type.Value);

            if (filter.EstActive.HasValue)
                query = query.Where(p => p.EstActive == filter.EstActive.Value);

            if (filter.FournisseurId.HasValue)
                query = query.Where(p => p.FournisseurId == filter.FournisseurId.Value);

            if (filter.DateDebut.HasValue)
                query = query.Where(p => p.DateDebut >= filter.DateDebut.Value);

            if (filter.DateFin.HasValue)
                query = query.Where(p => p.DateFin <= filter.DateFin.Value);

            if (!string.IsNullOrEmpty(filter.Recherche))
            {
                query = query.Where(p => p.Code.Contains(filter.Recherche) ||
                                        p.Nom.Contains(filter.Recherche) ||
                                        (p.Description != null && p.Description.Contains(filter.Recherche)));
            }

            // Tri
            query = filter.SortBy.ToLower() switch
            {
                "code" => filter.SortDesc ? query.OrderByDescending(p => p.Code) : query.OrderBy(p => p.Code),
                "nom" => filter.SortDesc ? query.OrderByDescending(p => p.Nom) : query.OrderBy(p => p.Nom),
                "type" => filter.SortDesc ? query.OrderByDescending(p => p.Type) : query.OrderBy(p => p.Type),
                "valeur" => filter.SortDesc ? query.OrderByDescending(p => p.Valeur) : query.OrderBy(p => p.Valeur),
                "datedebut" => filter.SortDesc ? query.OrderByDescending(p => p.DateDebut) : query.OrderBy(p => p.DateDebut),
                "datefin" => filter.SortDesc ? query.OrderByDescending(p => p.DateFin) : query.OrderBy(p => p.DateFin),
                _ => filter.SortDesc ? query.OrderByDescending(p => p.DateCreation) : query.OrderBy(p => p.DateCreation)
            };

            // Pagination
            var promotions = await query
                .Skip((filter.Page - 1) * filter.PageSize)
                .Take(filter.PageSize)
                .ToListAsync();

            return promotions.Select(MapToDto).ToList();
        }

        public async Task<PromotionGestionDto?> GetPromotionAsync(int id)
        {
            var promotion = await _context.PromotionGestions
                .Include(p => p.Fournisseur)
                .Include(p => p.Produit)
                .FirstOrDefaultAsync(p => p.Id == id);

            return promotion != null ? MapToDto(promotion) : null;
        }

        public async Task<PromotionGestionDto> CreatePromotionAsync(PromotionCreateDto dto, int? fournisseurId = null)
        {
            // Vérifier l'unicité du code
            if (await CodeExistsAsync(dto.Code))
                throw new InvalidOperationException("Ce code de promotion existe déjà");

            var promotion = new PromotionGestion
            {
                Code = dto.Code,
                Nom = dto.Nom,
                Description = dto.Description,
                Type = dto.Type,
                Valeur = dto.Valeur,
                DateDebut = dto.DateDebut,
                DateFin = dto.DateFin,
                UtilisationsMax = dto.UtilisationsMax,
                MontantMinimum = dto.MontantMinimum,
                ProduitId = dto.ProduitId,
                FournisseurId = fournisseurId ?? dto.FournisseurId,
                EstActive = true,
                DateCreation = DateTime.UtcNow
            };

            _context.PromotionGestions.Add(promotion);
            await _context.SaveChangesAsync();

            return await GetPromotionAsync(promotion.Id) ?? throw new InvalidOperationException("Erreur lors de la création");
        }

        public async Task<PromotionGestionDto> UpdatePromotionAsync(int id, PromotionUpdateDto dto)
        {
            var promotion = await _context.PromotionGestions.FindAsync(id);
            if (promotion == null)
                throw new KeyNotFoundException("Promotion non trouvée");

            promotion.Nom = dto.Nom;
            promotion.Description = dto.Description;
            promotion.Valeur = dto.Valeur;
            promotion.DateDebut = dto.DateDebut;
            promotion.DateFin = dto.DateFin;
            promotion.EstActive = dto.EstActive;
            promotion.UtilisationsMax = dto.UtilisationsMax;
            promotion.MontantMinimum = dto.MontantMinimum;

            await _context.SaveChangesAsync();

            return await GetPromotionAsync(id) ?? throw new InvalidOperationException("Erreur lors de la mise à jour");
        }

        public async Task DeletePromotionAsync(int id)
        {
            var promotion = await _context.PromotionGestions.FindAsync(id);
            if (promotion == null)
                throw new KeyNotFoundException("Promotion non trouvée");

            _context.PromotionGestions.Remove(promotion);
            await _context.SaveChangesAsync();
        }

        public async Task<PromotionGestionDto> TogglePromotionAsync(int id)
        {
            var promotion = await _context.PromotionGestions.FindAsync(id);
            if (promotion == null)
                throw new KeyNotFoundException("Promotion non trouvée");

            promotion.EstActive = !promotion.EstActive;
            await _context.SaveChangesAsync();

            return await GetPromotionAsync(id) ?? throw new InvalidOperationException("Erreur lors de la mise à jour");
        }

        public async Task<PromotionGestionDto> ActivatePromotionAsync(int id)
        {
            var promotion = await _context.PromotionGestions.FindAsync(id);
            if (promotion == null)
                throw new KeyNotFoundException("Promotion non trouvée");

            promotion.EstActive = true;
            await _context.SaveChangesAsync();

            return await GetPromotionAsync(id) ?? throw new InvalidOperationException("Erreur lors de l'activation");
        }

        public async Task<PromotionGestionDto> DeactivatePromotionAsync(int id)
        {
            var promotion = await _context.PromotionGestions.FindAsync(id);
            if (promotion == null)
                throw new KeyNotFoundException("Promotion non trouvée");

            promotion.EstActive = false;
            await _context.SaveChangesAsync();

            return await GetPromotionAsync(id) ?? throw new InvalidOperationException("Erreur lors de la désactivation");
        }

        public async Task<PromotionValidationResultDto> ValidatePromotionAsync(ValidatePromotionDto dto)
        {
            var promotion = await _context.PromotionGestions
                .Include(p => p.Produit)
                .FirstOrDefaultAsync(p => p.Code == dto.Code && p.EstActive);

            if (promotion == null)
            {
                return new PromotionValidationResultDto
                {
                    EstValide = false,
                    MessageErreur = "Code de promotion invalide ou inactif"
                };
            }

            // Vérifier les dates
            var now = DateTime.UtcNow;
            if (now < promotion.DateDebut || now > promotion.DateFin)
            {
                return new PromotionValidationResultDto
                {
                    EstValide = false,
                    MessageErreur = "Cette promotion n'est pas valide à cette période"
                };
            }

            // Vérifier le montant minimum
            if (promotion.MontantMinimum.HasValue && dto.MontantCommande < promotion.MontantMinimum.Value)
            {
                return new PromotionValidationResultDto
                {
                    EstValide = false,
                    MessageErreur = $"Montant minimum requis: {promotion.MontantMinimum:C}"
                };
            }

            // Vérifier les utilisations
            if (promotion.UtilisationsMax.HasValue && promotion.UtilisationsActuelles >= promotion.UtilisationsMax.Value)
            {
                return new PromotionValidationResultDto
                {
                    EstValide = false,
                    MessageErreur = "Cette promotion a atteint sa limite d'utilisation"
                };
            }

            // Vérifier le produit spécifique
            if (promotion.ProduitId.HasValue && dto.ProduitId != promotion.ProduitId.Value)
            {
                return new PromotionValidationResultDto
                {
                    EstValide = false,
                    MessageErreur = "Cette promotion ne s'applique pas à ce produit"
                };
            }

            // Calculer la réduction
            var montantReduction = await CalculateDiscountAsync(promotion.Id, dto.MontantCommande);

            return new PromotionValidationResultDto
            {
                EstValide = true,
                MontantReduction = montantReduction,
                Promotion = MapToDto(promotion)
            };
        }

        public async Task<List<PromotionGestionDto>> GetApplicablePromotionsAsync(int? produitId = null, decimal? montantCommande = null)
        {
            var query = _context.PromotionGestions
                .Include(p => p.Fournisseur)
                .Include(p => p.Produit)
                .Where(p => p.EstActive && p.DateDebut <= DateTime.UtcNow && p.DateFin >= DateTime.UtcNow);

            if (produitId.HasValue)
                query = query.Where(p => p.ProduitId == null || p.ProduitId == produitId.Value);

            if (montantCommande.HasValue)
                query = query.Where(p => p.MontantMinimum == null || p.MontantMinimum <= montantCommande.Value);

            var promotions = await query.ToListAsync();
            return promotions.Select(MapToDto).ToList();
        }

        public async Task<PromotionStatsDto> GetPromotionStatsAsync()
        {
            var promotions = await _context.PromotionGestions.ToListAsync();
            var now = DateTime.UtcNow;

            var stats = new PromotionStatsDto
            {
                TotalPromotions = promotions.Count,
                PromotionsActives = promotions.Count(p => p.EstActive && p.DateDebut <= now && p.DateFin >= now),
                PromotionsExpirees = promotions.Count(p => p.DateFin < now),
                PromotionsEnAttente = promotions.Count(p => p.DateDebut > now)
            };

            // Calculer les statistiques d'utilisation
            var utilisations = await _context.PromotionUtiliseesGestion.ToListAsync();
            stats.TotalUtilisations = utilisations.Count;
            stats.MontantTotalEconomise = utilisations.Sum(u => u.MontantReduction);

            if (stats.TotalPromotions > 0)
            {
                stats.TauxUtilisationMoyen = (decimal)stats.TotalUtilisations / stats.TotalPromotions;
            }

            // Promotions par type
            foreach (TypePromotionGestion type in Enum.GetValues<TypePromotionGestion>())
            {
                stats.PromotionsParType[type] = promotions.Count(p => p.Type == type);
            }

            // Promotions populaires et récentes
            var recentFilter = new PromotionFilterDto { Page = 1, PageSize = 5 };
            stats.PromotionsRecentes = await GetPromotionsAsync(recentFilter);

            return stats;
        }

        public async Task<PromotionStatsDto> GetPromotionStatsFournisseurAsync(int fournisseurId)
        {
            var filter = new PromotionFilterDto { FournisseurId = fournisseurId };
            var promotions = await GetPromotionsAsync(filter);

            var stats = new PromotionStatsDto();
            // Implémenter les statistiques spécifiques au fournisseur
            // ... (logique similaire mais filtrée par fournisseur)

            return stats;
        }

        public async Task<List<PromotionGestionDto>> GetPromotionsFournisseurAsync(int fournisseurId, PromotionFilterDto filter)
        {
            filter.FournisseurId = fournisseurId;
            return await GetPromotionsAsync(filter);
        }

        public async Task<List<PromotionGestionDto>> GetPromotionsExpirantsAsync(int jours = 7)
        {
            var dateLimit = DateTime.UtcNow.AddDays(jours);
            var promotions = await _context.PromotionGestions
                .Include(p => p.Fournisseur)
                .Include(p => p.Produit)
                .Where(p => p.EstActive && p.DateFin <= dateLimit && p.DateFin >= DateTime.UtcNow)
                .ToListAsync();

            return promotions.Select(MapToDto).ToList();
        }

        public async Task<List<PromotionGestionDto>> GetPromotionsPopulairesAsync(int limit = 10)
        {
            var promotions = await _context.PromotionGestions
                .Include(p => p.Fournisseur)
                .Include(p => p.Produit)
                .Where(p => p.EstActive)
                .OrderByDescending(p => p.UtilisationsActuelles)
                .Take(limit)
                .ToListAsync();

            return promotions.Select(MapToDto).ToList();
        }

        public async Task<bool> CodeExistsAsync(string code, int? excludeId = null)
        {
            var query = _context.PromotionGestions.Where(p => p.Code == code);
            if (excludeId.HasValue)
                query = query.Where(p => p.Id != excludeId.Value);

            return await query.AnyAsync();
        }

        public async Task<decimal> CalculateDiscountAsync(int promotionId, decimal montantOriginal)
        {
            var promotion = await _context.PromotionGestions.FindAsync(promotionId);
            if (promotion == null) return 0;

            return promotion.Type switch
            {
                TypePromotionGestion.Pourcentage => montantOriginal * (promotion.Valeur / 100),
                TypePromotionGestion.MontantFixe => Math.Min(promotion.Valeur, montantOriginal),
                _ => 0
            };
        }

        public async Task IncrementUtilisationAsync(int promotionId)
        {
            var promotion = await _context.PromotionGestions.FindAsync(promotionId);
            if (promotion != null)
            {
                promotion.UtilisationsActuelles++;
                await _context.SaveChangesAsync();
            }
        }

        public async Task<int> CleanupExpiredPromotionsAsync()
        {
            var expiredPromotions = await _context.PromotionGestions
                .Where(p => p.DateFin < DateTime.UtcNow)
                .ToListAsync();

            foreach (var promotion in expiredPromotions)
            {
                promotion.EstActive = false;
            }

            await _context.SaveChangesAsync();
            return expiredPromotions.Count;
        }

        public async Task<List<PromotionGestionDto>> GetPromotionsToExpireAsync(int jours = 3)
        {
            return await GetPromotionsExpirantsAsync(jours);
        }

        private PromotionGestionDto MapToDto(PromotionGestion promotion)
        {
            return new PromotionGestionDto
            {
                Id = promotion.Id,
                Code = promotion.Code,
                Nom = promotion.Nom,
                Description = promotion.Description,
                Type = promotion.Type,
                Valeur = promotion.Valeur,
                DateDebut = promotion.DateDebut,
                DateFin = promotion.DateFin,
                EstActive = promotion.EstActive,
                UtilisationsMax = promotion.UtilisationsMax,
                UtilisationsActuelles = promotion.UtilisationsActuelles,
                MontantMinimum = promotion.MontantMinimum,
                DateCreation = promotion.DateCreation,
                FournisseurId = promotion.FournisseurId,
                FournisseurNom = promotion.Fournisseur != null ? $"{promotion.Fournisseur.Prenom} {promotion.Fournisseur.Nom}" : null,
                FournisseurRaisonSociale = promotion.Fournisseur?.RaisonSociale,
                ProduitId = promotion.ProduitId,
                ProduitNom = promotion.Produit?.Nom
            };
        }
    }
}

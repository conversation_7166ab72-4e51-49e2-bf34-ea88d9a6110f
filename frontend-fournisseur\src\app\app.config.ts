import { ApplicationConfig, provideZoneChangeDetection, inject } from '@angular/core';
import { provideRouter, withEnabledBlockingInitialNavigation, withInMemoryScrolling, withRouterConfig } from '@angular/router';
import { provideHttpClient, withInterceptors, withF<PERSON>ch, HTTP_INTERCEPTORS, HttpInterceptorFn } from '@angular/common/http';
import { provideAnimationsAsync } from '@angular/platform-browser/animations/async';

import { routes } from './app.routes';
import { HttpInterceptorService } from './services';
import { HttpErrorInterceptor } from './interceptors/http-error.interceptor';
import { CorsInterceptor } from './interceptors/cors.interceptor';
import { AuthService } from './services/auth.service';
import { AdminAuthService } from './services/admin-auth.service';

// Intercepteur fonctionnel pour l'authentification (Angular 19)
const authInterceptor: HttpInterceptorFn = (req, next) => {
  const authService = inject(AuthService);
  const adminAuthService = inject(AdminAuthService);

  // Vérifier d'abord si c'est une requête admin
  const isAdminRequest = req.url.includes('/admin/') || req.url.includes('/Admin/') || req.url.includes('/DemandesCategories');

  let token: string | null = null;

  if (isAdminRequest) {
    // Pour les requêtes admin, utiliser le token admin
    token = localStorage.getItem('admin_access_token');
    console.log('🔍 Requête Admin détectée:', {
      url: req.url,
      hasAdminToken: !!token,
      tokenPreview: token ? token.substring(0, 20) + '...' : 'Aucun'
    });
  } else {
    // Pour les autres requêtes, utiliser le token normal
    token = authService.getToken();
    console.log('🔍 Requête normale:', {
      url: req.url,
      hasToken: !!token,
      tokenPreview: token ? token.substring(0, 20) + '...' : 'Aucun'
    });
  }

  if (token) {
    const authReq = req.clone({
      headers: req.headers.set('Authorization', `Bearer ${token}`)
    });
    console.log('✅ Token ajouté à la requête');
    return next(authReq);
  }

  console.warn('⚠️ Aucun token disponible pour:', req.url);
  return next(req);
};

export const appConfig: ApplicationConfig = {
  providers: [
    // Angular 19: Nouvelle détection de changement optimisée
    provideZoneChangeDetection({
      eventCoalescing: true,
      runCoalescing: true
    }),

    // Angular 19: Router amélioré avec nouvelles options
    provideRouter(
      routes,
      withEnabledBlockingInitialNavigation(),
      withInMemoryScrolling({
        scrollPositionRestoration: 'top',
        anchorScrolling: 'enabled'
      }),
      withRouterConfig({
        onSameUrlNavigation: 'reload'
      })
    ),

    // Angular 19: HTTP Client avec fetch API et intercepteur fonctionnel
    provideHttpClient(
      withFetch(), // Nouvelle API fetch native
      withInterceptors([authInterceptor]) // Intercepteur fonctionnel pour l'authentification
    ),

    // Angular 19: Animations asynchrones pour de meilleures performances
    provideAnimationsAsync(),

    // Les intercepteurs legacy sont remplacés par l'intercepteur fonctionnel authInterceptor
  ]
};

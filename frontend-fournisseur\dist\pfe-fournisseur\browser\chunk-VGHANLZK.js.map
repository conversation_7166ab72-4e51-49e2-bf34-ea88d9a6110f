{"version": 3, "sources": ["src/app/services/sous-categorie.service.ts"], "sourcesContent": ["import { Injectable } from '@angular/core';\nimport { HttpClient } from '@angular/common/http';\nimport { Observable } from 'rxjs';\nimport { tap } from 'rxjs/operators';\nimport {\n  SousCategorie,\n  SousCategorieCreate,\n  SousCategorieUpdate,\n  SousCategorieDropdown\n} from '../models';\nimport { environment } from '../../environments/environment';\n\n@Injectable({\n  providedIn: 'root'\n})\nexport class SousCategorieService {\n  private readonly API_URL = `${environment.apiUrl || 'https://localhost:7264/api'}/SousCategories`;\n\n  constructor(private http: HttpClient) {}\n\n  /**\n   * GET /api/SousCategories - Obtenir toutes les sous-catégories\n   */\n  getAll(fournisseurId?: number): Observable<SousCategorie[]> {\n    console.log('📦 Récupération des sous-catégories');\n    console.log('🔍 FournisseurId reçu:', fournisseurId);\n    let url = `${this.API_URL}/enriched`;\n    if (fournisseurId) {\n      url += `?fournisseurId=${fournisseurId}`;\n    }\n    console.log('🌐 URL finale:', url);\n    return this.http.get<SousCategorie[]>(url)\n      .pipe(\n        tap(response => console.log('✅ Sous-catégories récupérées:', response))\n      );\n  }\n\n  /**\n   * GET /api/SousCategories/{id} - Obtenir une sous-catégorie par ID\n   */\n  getById(id: number): Observable<SousCategorie> {\n    console.log('🔍 Récupération de la sous-catégorie ID:', id);\n    return this.http.get<SousCategorie>(`${this.API_URL}/${id}`)\n      .pipe(\n        tap(response => console.log('✅ Sous-catégorie récupérée:', response))\n      );\n  }\n\n  /**\n   * POST /api/SousCategories - Créer une nouvelle sous-catégorie\n   */\n  create(sousCategorie: SousCategorieCreate): Observable<SousCategorie> {\n    console.log('➕ Création d\\'une nouvelle sous-catégorie:', sousCategorie);\n    return this.http.post<SousCategorie>(this.API_URL, sousCategorie)\n      .pipe(\n        tap(response => console.log('✅ Sous-catégorie créée:', response))\n      );\n  }\n\n  /**\n   * PUT /api/SousCategories/{id} - Mettre à jour une sous-catégorie\n   */\n  update(id: number, sousCategorie: SousCategorieUpdate): Observable<SousCategorie> {\n    console.log('✏️ Mise à jour de la sous-catégorie ID:', id, sousCategorie);\n    return this.http.put<SousCategorie>(`${this.API_URL}/${id}`, sousCategorie)\n      .pipe(\n        tap(response => console.log('✅ Sous-catégorie mise à jour:', response))\n      );\n  }\n\n  /**\n   * DELETE /api/SousCategories/{id} - Supprimer une sous-catégorie\n   */\n  delete(id: number): Observable<void> {\n    console.log('🗑️ Suppression de la sous-catégorie ID:', id);\n    return this.http.delete<void>(`${this.API_URL}/${id}`)\n      .pipe(\n        tap(() => console.log('✅ Sous-catégorie supprimée:', id))\n      );\n  }\n\n  /**\n   * GET /api/SousCategories/by-categorie/{categorieId} - Obtenir les sous-catégories d'une catégorie\n   */\n  getByCategorie(categorieId: number): Observable<SousCategorie[]> {\n    console.log('📂 Récupération des sous-catégories pour la catégorie:', categorieId);\n    return this.http.get<SousCategorie[]>(`${this.API_URL}/by-categorie/${categorieId}`)\n      .pipe(\n        tap(response => console.log('✅ Sous-catégories par catégorie récupérées:', response))\n      );\n  }\n\n  /**\n   * GET /api/SousCategories/{id}/produits-count - Obtenir le nombre de produits d'une sous-catégorie\n   */\n  getProduitsCount(id: number): Observable<number> {\n    console.log('🔢 Récupération du nombre de produits pour la sous-catégorie:', id);\n    return this.http.get<number>(`${this.API_URL}/${id}/produits-count`)\n      .pipe(\n        tap(response => console.log('✅ Nombre de produits récupéré:', response))\n      );\n  }\n\n  /**\n   * GET /api/SousCategories/dropdown/{categorieId} - Obtenir les sous-catégories pour dropdown\n   */\n  getDropdown(categorieId: number): Observable<SousCategorieDropdown[]> {\n    console.log('📋 Récupération des sous-catégories pour dropdown, catégorie:', categorieId);\n    return this.http.get<SousCategorieDropdown[]>(`${this.API_URL}/dropdown/${categorieId}`)\n      .pipe(\n        tap(response => console.log('✅ Dropdown sous-catégories récupéré:', response))\n      );\n  }\n}\n"], "mappings": ";;;;;;;;;;;;;;;AAeM,IAAO,uBAAP,MAAO,sBAAoB;EAGX;EAFH,UAAU,GAAG,YAAY,UAAU,4BAA4B;EAEhF,YAAoB,MAAgB;AAAhB,SAAA,OAAA;EAAmB;;;;EAKvC,OAAO,eAAsB;AAC3B,YAAQ,IAAI,qDAAqC;AACjD,YAAQ,IAAI,oCAA0B,aAAa;AACnD,QAAI,MAAM,GAAG,KAAK,OAAO;AACzB,QAAI,eAAe;AACjB,aAAO,kBAAkB,aAAa;IACxC;AACA,YAAQ,IAAI,yBAAkB,GAAG;AACjC,WAAO,KAAK,KAAK,IAAqB,GAAG,EACtC,KACC,IAAI,cAAY,QAAQ,IAAI,kDAAiC,QAAQ,CAAC,CAAC;EAE7E;;;;EAKA,QAAQ,IAAU;AAChB,YAAQ,IAAI,4DAA4C,EAAE;AAC1D,WAAO,KAAK,KAAK,IAAmB,GAAG,KAAK,OAAO,IAAI,EAAE,EAAE,EACxD,KACC,IAAI,cAAY,QAAQ,IAAI,gDAA+B,QAAQ,CAAC,CAAC;EAE3E;;;;EAKA,OAAO,eAAkC;AACvC,YAAQ,IAAI,wDAA8C,aAAa;AACvE,WAAO,KAAK,KAAK,KAAoB,KAAK,SAAS,aAAa,EAC7D,KACC,IAAI,cAAY,QAAQ,IAAI,yCAA2B,QAAQ,CAAC,CAAC;EAEvE;;;;EAKA,OAAO,IAAY,eAAkC;AACnD,YAAQ,IAAI,2DAA2C,IAAI,aAAa;AACxE,WAAO,KAAK,KAAK,IAAmB,GAAG,KAAK,OAAO,IAAI,EAAE,IAAI,aAAa,EACvE,KACC,IAAI,cAAY,QAAQ,IAAI,4CAAiC,QAAQ,CAAC,CAAC;EAE7E;;;;EAKA,OAAO,IAAU;AACf,YAAQ,IAAI,2DAA4C,EAAE;AAC1D,WAAO,KAAK,KAAK,OAAa,GAAG,KAAK,OAAO,IAAI,EAAE,EAAE,EAClD,KACC,IAAI,MAAM,QAAQ,IAAI,0CAA+B,EAAE,CAAC,CAAC;EAE/D;;;;EAKA,eAAe,aAAmB;AAChC,YAAQ,IAAI,6EAA0D,WAAW;AACjF,WAAO,KAAK,KAAK,IAAqB,GAAG,KAAK,OAAO,iBAAiB,WAAW,EAAE,EAChF,KACC,IAAI,cAAY,QAAQ,IAAI,mEAA+C,QAAQ,CAAC,CAAC;EAE3F;;;;EAKA,iBAAiB,IAAU;AACzB,YAAQ,IAAI,iFAAiE,EAAE;AAC/E,WAAO,KAAK,KAAK,IAAY,GAAG,KAAK,OAAO,IAAI,EAAE,iBAAiB,EAChE,KACC,IAAI,cAAY,QAAQ,IAAI,gDAAkC,QAAQ,CAAC,CAAC;EAE9E;;;;EAKA,YAAY,aAAmB;AAC7B,YAAQ,IAAI,oFAAiE,WAAW;AACxF,WAAO,KAAK,KAAK,IAA6B,GAAG,KAAK,OAAO,aAAa,WAAW,EAAE,EACpF,KACC,IAAI,cAAY,QAAQ,IAAI,yDAAwC,QAAQ,CAAC,CAAC;EAEpF;;qCAjGW,uBAAoB,mBAAA,UAAA,CAAA;EAAA;4EAApB,uBAAoB,SAApB,sBAAoB,WAAA,YAFnB,OAAM,CAAA;;;sEAEP,sBAAoB,CAAA;UAHhC;WAAW;MACV,YAAY;KACb;;;", "names": []}
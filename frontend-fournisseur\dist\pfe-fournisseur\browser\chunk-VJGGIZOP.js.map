{"version": 3, "sources": ["src/app/components/admin/user-management/user-management.component.ts", "src/app/components/admin/user-management/user-management.component.html"], "sourcesContent": ["import { Component, OnInit, signal, computed } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { FormsModule } from '@angular/forms';\nimport { AdminService, UtilisateurAdmin, AdminListResponse } from '../../../services/admin.service';\n\n@Component({\n  selector: 'app-user-management',\n  standalone: true,\n  imports: [CommonModule, FormsModule],\n  templateUrl: './user-management.component.html',\n  styleUrls: ['./user-management.component.css']\n})\nexport class UserManagementComponent implements OnInit {\n  // Angular 19: Signals\n  utilisateurs = signal<UtilisateurAdmin[]>([]);\n  isLoading = signal(false);\n  error = signal('');\n  searchTerm = signal('');\n  selectedRole = signal('');\n  selectedStatus = signal('');\n  currentPage = signal(1);\n  pageSize = signal(10);\n  totalItems = signal(0);\n\n  // Computed signals - Plus besoin de filtrage côté client car fait côté serveur\n\n  totalPages = computed(() => Math.ceil(this.totalItems() / this.pageSize()));\n\n  roles = ['Admin', 'Fournisseur', 'Client'];\n\n  // Expose Math to template\n  Math = Math;\n\n  constructor(private adminService: AdminService) {}\n\n  ngOnInit(): void {\n    // Charger les utilisateurs depuis le backend uniquement\n    this.loadUtilisateurs();\n  }\n\n\n\n  loadUtilisateurs(): void {\n    this.isLoading.set(true);\n    this.error.set('');\n\n    const params = {\n      page: this.currentPage(),\n      pageSize: this.pageSize(),\n      search: this.searchTerm() || undefined,\n      role: this.selectedRole() || undefined,\n      estActif: this.selectedStatus() === 'actif' ? true :\n                this.selectedStatus() === 'inactif' ? false : undefined\n    };\n\n    this.adminService.getUtilisateurs(params).subscribe({\n      next: (response) => {\n        console.log('✅ Réponse paginée reçue:', response);\n\n        this.utilisateurs.set(response.utilisateurs || []);\n        this.totalItems.set(response.totalCount || 0);\n        this.isLoading.set(false);\n      },\n      error: (error) => {\n        console.error('❌ Erreur lors du chargement des utilisateurs:', error);\n        this.error.set(`Erreur lors du chargement des utilisateurs: ${error.status || 'Erreur réseau'}`);\n        this.isLoading.set(false);\n      }\n    });\n  }\n\n  toggleUtilisateur(user: UtilisateurAdmin): void {\n    const action = user.estActif ? 'désactiver' : 'activer';\n    \n    if (confirm(`Êtes-vous sûr de vouloir ${action} l'utilisateur ${user.nomComplet} ?`)) {\n      this.adminService.toggleUtilisateur(user.id).subscribe({\n        next: () => {\n          // Mettre à jour localement\n          const users = this.utilisateurs();\n          const index = users.findIndex(u => u.id === user.id);\n          if (index !== -1) {\n            users[index] = { ...users[index], estActif: !users[index].estActif };\n            this.utilisateurs.set([...users]);\n          }\n          alert(`Utilisateur ${action} avec succès`);\n        },\n        error: (error) => {\n          console.error('Erreur lors du toggle:', error);\n          alert(`Erreur lors de l'${action}ation de l'utilisateur`);\n        }\n      });\n    }\n  }\n\n  deleteUtilisateur(user: UtilisateurAdmin): void {\n    if (confirm(`Êtes-vous sûr de vouloir supprimer définitivement l'utilisateur ${user.nomComplet} ?\\n\\nCette action est irréversible.`)) {\n      this.adminService.deleteUtilisateur(user.id).subscribe({\n        next: () => {\n          // Supprimer localement\n          const users = this.utilisateurs().filter(u => u.id !== user.id);\n          this.utilisateurs.set(users);\n          this.totalItems.set(this.totalItems() - 1);\n          alert('Utilisateur supprimé avec succès');\n        },\n        error: (error) => {\n          console.error('Erreur lors de la suppression:', error);\n          alert('Erreur lors de la suppression de l\\'utilisateur');\n        }\n      });\n    }\n  }\n\n  onSearch(): void {\n    this.currentPage.set(1);\n    this.loadUtilisateurs();\n  }\n\n  onFilterChange(): void {\n    this.currentPage.set(1);\n    this.loadUtilisateurs();\n  }\n\n  onPageChange(page: number): void {\n    this.currentPage.set(page);\n    this.loadUtilisateurs();\n  }\n\n  getStatusClass(user: UtilisateurAdmin): string {\n    return user.estActif ? 'status-active' : 'status-inactive';\n  }\n\n  getStatusText(user: UtilisateurAdmin): string {\n    return user.estActif ? 'Actif' : 'Inactif';\n  }\n\n  getRoleClass(role: string): string {\n    switch (role) {\n      case 'Admin': return 'role-admin';\n      case 'Fournisseur': return 'role-fournisseur';\n      case 'Client': return 'role-client';\n      default: return 'role-default';\n    }\n  }\n\n  getInitials(nomComplet: string): string {\n    if (!nomComplet) return '??';\n\n    const parts = nomComplet.trim().split(' ');\n    if (parts.length >= 2) {\n      return (parts[0].charAt(0) + parts[parts.length - 1].charAt(0)).toUpperCase();\n    }\n    return nomComplet.charAt(0).toUpperCase() + '?';\n  }\n\n  formatDate(date: string | Date): string {\n    if (!date) return 'Non défini';\n\n    const dateObj = typeof date === 'string' ? new Date(date) : date;\n\n    if (isNaN(dateObj.getTime())) {\n      return 'Date invalide';\n    }\n\n    return dateObj.toLocaleDateString('fr-FR', {\n      year: 'numeric',\n      month: 'short',\n      day: 'numeric',\n      hour: '2-digit',\n      minute: '2-digit'\n    });\n  }\n\n  refresh(): void {\n    this.loadUtilisateurs();\n  }\n\n  exportUsers(): void {\n    // TODO: Implémenter l'export des utilisateurs\n    alert('Fonctionnalité d\\'export en cours de développement');\n  }\n}\n", "<div class=\"user-management-container\">\n  <!-- Header -->\n  <div class=\"page-header\">\n    <div class=\"header-content\">\n      <h1 class=\"page-title\">\n        <i class=\"icon-users\"></i>\n        Gestion des Utilisateurs\n      </h1>\n      <div class=\"header-actions\">\n        <button class=\"btn btn-secondary\" (click)=\"refresh()\" [disabled]=\"isLoading()\">\n          <i class=\"icon-refresh\"></i>\n          Actualiser\n        </button>\n        <button class=\"btn btn-primary\" (click)=\"exportUsers()\">\n          <i class=\"icon-download\"></i>\n          Exporter\n        </button>\n\n      </div>\n    </div>\n  </div>\n\n  <!-- Filtres et Recherche -->\n  <div class=\"filters-section\">\n    <div class=\"search-box\">\n      <i class=\"icon-search\"></i>\n      <input \n        type=\"text\" \n        placeholder=\"Rechercher par nom, prénom ou email...\"\n        [(ngModel)]=\"searchTerm\"\n        (input)=\"onSearch()\"\n        class=\"search-input\">\n    </div>\n\n    <div class=\"filters\">\n      <select [(ngModel)]=\"selectedRole\" (change)=\"onFilterChange()\" class=\"filter-select\">\n        <option value=\"\">Tous les rôles</option>\n        <option *ngFor=\"let role of roles\" [value]=\"role\">{{ role }}</option>\n      </select>\n\n      <select [(ngModel)]=\"selectedStatus\" (change)=\"onFilterChange()\" class=\"filter-select\">\n        <option value=\"\">Tous les statuts</option>\n        <option value=\"actif\">Actifs</option>\n        <option value=\"inactif\">Inactifs</option>\n      </select>\n    </div>\n  </div>\n\n  <!-- Message d'erreur -->\n  <div *ngIf=\"error()\" class=\"error-message\">\n    <i class=\"icon-alert\"></i>\n    {{ error() }}\n  </div>\n\n  <!-- Loading -->\n   \n  <div *ngIf=\"isLoading()\" class=\"loading-container\">\n    <div class=\"loading-spinner\"></div>\n    <p>Chargement des utilisateurs...</p>\n  </div>\n\n  <!-- Debug Info -->\n  <div *ngIf=\"!isLoading() && !error()\" class=\"debug-info\" style=\"background: #f0f9ff; padding: 1rem; margin: 1rem 0; border-radius: 8px; border: 1px solid #0ea5e9;\">\n    <h4 style=\"margin: 0 0 0.5rem 0; color: #0369a1;\">🔍Info General</h4>\n    <p><strong>Nombre d'utilisateurs:</strong> {{ utilisateurs().length }}</p>\n    <p><strong>Total items:</strong> {{ totalItems() }}</p>\n    <p><strong>Utilisateurs affichés:</strong> {{ utilisateurs().length }}</p>\n\n  </div>\n\n  <!-- Tableau des utilisateurs -->\n  <div *ngIf=\"!isLoading() && !error()\" class=\"table-container\">\n    <table class=\"users-table\">\n      <thead>\n        <tr>\n          <th>Utilisateur</th>\n          <th>Email</th>\n          <th>Rôle</th>\n          <th>Statut</th>\n          <th>Date création</th>\n          <th>Dernière connexion</th>\n          <th>Actions</th>\n        </tr>\n      </thead>\n      <tbody>\n        <tr *ngFor=\"let user of utilisateurs()\" class=\"user-row\">\n          <td class=\"user-info\">\n            <div class=\"user-avatar\">\n              {{ getInitials(user.nomComplet) }}\n            </div>\n            <div class=\"user-details\">\n              <div class=\"user-name\">{{ user.nomComplet }}</div>\n              <div class=\"user-id\">ID: {{ user.id }}</div>\n            </div>\n          </td>\n          \n          <td class=\"user-email\">{{ user.email }}</td>\n          \n          <td>\n            <span class=\"role-badge\" [ngClass]=\"getRoleClass(user.role)\">\n              {{ user.role }}\n            </span>\n          </td>\n          \n          <td>\n            <span class=\"status-badge\" [ngClass]=\"getStatusClass(user)\">\n              {{ getStatusText(user) }}\n            </span>\n          </td>\n          \n          <td class=\"date-cell\">{{ formatDate(user.dateInscription) }}</td>\n          \n          <td class=\"date-cell\">\n            <span *ngIf=\"user.derniereConnexion; else noConnection\">\n              {{ formatDate(user.derniereConnexion) }}\n            </span>\n            <ng-template #noConnection>\n              <span class=\"no-connection\">Jamais connecté</span>\n            </ng-template>\n          </td>\n          \n          <td class=\"actions-cell\">\n            <div class=\"action-buttons\">\n              <button \n                class=\"btn-action btn-toggle\"\n                [class.btn-activate]=\"!user.estActif\"\n                [class.btn-deactivate]=\"user.estActif\"\n                (click)=\"toggleUtilisateur(user)\"\n                [title]=\"user.estActif ? 'Désactiver' : 'Activer'\">\n                <i [class]=\"user.estActif ? 'icon-pause' : 'icon-play'\"></i>\n              </button>\n              \n              <button \n                class=\"btn-action btn-delete\"\n                (click)=\"deleteUtilisateur(user)\"\n                title=\"Supprimer\">\n                <i class=\"icon-trash\"></i>\n              </button>\n            </div>\n          </td>\n        </tr>\n      </tbody>\n    </table>\n\n    <!-- Message si aucun utilisateur -->\n    <div *ngIf=\"utilisateurs().length === 0\" class=\"no-data\">\n      <i class=\"icon-users\"></i>\n      <h3>Aucun utilisateur trouvé</h3>\n      <p>Aucun utilisateur ne correspond aux critères de recherche.</p>\n    </div>\n  </div>\n\n  <!-- Pagination -->\n  <div *ngIf=\"totalPages() > 1\" class=\"pagination-container\">\n    <div class=\"pagination-info\">\n      Affichage de {{ (currentPage() - 1) * pageSize() + 1 }} à \n      {{ Math.min(currentPage() * pageSize(), totalItems()) }} sur {{ totalItems() }} utilisateurs\n    </div>\n    \n    <div class=\"pagination\">\n      <button \n        class=\"btn-page\"\n        [disabled]=\"currentPage() === 1\"\n        (click)=\"onPageChange(currentPage() - 1)\">\n        <i class=\"icon-chevron-left\"></i>\n      </button>\n      \n      <span class=\"page-info\">\n        Page {{ currentPage() }} sur {{ totalPages() }}\n      </span>\n      \n      <button \n        class=\"btn-page\"\n        [disabled]=\"currentPage() === totalPages()\"\n        (click)=\"onPageChange(currentPage() + 1)\">\n        <i class=\"icon-chevron-right\"></i>\n      </button>\n    </div>\n  </div>\n</div>\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACqCQ,IAAA,yBAAA,GAAA,UAAA,EAAA;AAAkD,IAAA,iBAAA,CAAA;AAAU,IAAA,uBAAA;;;;AAAzB,IAAA,qBAAA,SAAA,OAAA;AAAe,IAAA,oBAAA;AAAA,IAAA,4BAAA,OAAA;;;;;AAYxD,IAAA,yBAAA,GAAA,OAAA,EAAA;AACE,IAAA,oBAAA,GAAA,KAAA,EAAA;AACA,IAAA,iBAAA,CAAA;AACF,IAAA,uBAAA;;;;AADE,IAAA,oBAAA,CAAA;AAAA,IAAA,6BAAA,KAAA,OAAA,MAAA,GAAA,GAAA;;;;;AAKF,IAAA,yBAAA,GAAA,OAAA,EAAA;AACE,IAAA,oBAAA,GAAA,OAAA,EAAA;AACA,IAAA,yBAAA,GAAA,GAAA;AAAG,IAAA,iBAAA,GAAA,gCAAA;AAA8B,IAAA,uBAAA,EAAI;;;;;AAIvC,IAAA,yBAAA,GAAA,OAAA,EAAA,EAAoK,GAAA,MAAA,EAAA;AAChH,IAAA,iBAAA,GAAA,uBAAA;AAAc,IAAA,uBAAA;AAChE,IAAA,yBAAA,GAAA,GAAA,EAAG,GAAA,QAAA;AAAQ,IAAA,iBAAA,GAAA,wBAAA;AAAsB,IAAA,uBAAA;AAAU,IAAA,iBAAA,CAAA;AAA2B,IAAA,uBAAA;AACtE,IAAA,yBAAA,GAAA,GAAA,EAAG,GAAA,QAAA;AAAQ,IAAA,iBAAA,GAAA,cAAA;AAAY,IAAA,uBAAA;AAAU,IAAA,iBAAA,EAAA;AAAkB,IAAA,uBAAA;AACnD,IAAA,yBAAA,IAAA,GAAA,EAAG,IAAA,QAAA;AAAQ,IAAA,iBAAA,IAAA,2BAAA;AAAsB,IAAA,uBAAA;AAAU,IAAA,iBAAA,EAAA;AAA2B,IAAA,uBAAA,EAAI;;;;AAF/B,IAAA,oBAAA,CAAA;AAAA,IAAA,6BAAA,KAAA,OAAA,aAAA,EAAA,QAAA,EAAA;AACV,IAAA,oBAAA,CAAA;AAAA,IAAA,6BAAA,KAAA,OAAA,WAAA,GAAA,EAAA;AACU,IAAA,oBAAA,CAAA;AAAA,IAAA,6BAAA,KAAA,OAAA,aAAA,EAAA,QAAA,EAAA;;;;;AA+CnC,IAAA,yBAAA,GAAA,MAAA;AACE,IAAA,iBAAA,CAAA;AACF,IAAA,uBAAA;;;;;AADE,IAAA,oBAAA;AAAA,IAAA,6BAAA,KAAA,OAAA,WAAA,QAAA,iBAAA,GAAA,GAAA;;;;;AAGA,IAAA,yBAAA,GAAA,QAAA,EAAA;AAA4B,IAAA,iBAAA,GAAA,oBAAA;AAAe,IAAA,uBAAA;;;;;;AAhCjD,IAAA,yBAAA,GAAA,MAAA,EAAA,EAAyD,GAAA,MAAA,EAAA,EACjC,GAAA,OAAA,EAAA;AAElB,IAAA,iBAAA,CAAA;AACF,IAAA,uBAAA;AACA,IAAA,yBAAA,GAAA,OAAA,EAAA,EAA0B,GAAA,OAAA,EAAA;AACD,IAAA,iBAAA,CAAA;AAAqB,IAAA,uBAAA;AAC5C,IAAA,yBAAA,GAAA,OAAA,EAAA;AAAqB,IAAA,iBAAA,CAAA;AAAiB,IAAA,uBAAA,EAAM,EACxC;AAGR,IAAA,yBAAA,GAAA,MAAA,EAAA;AAAuB,IAAA,iBAAA,EAAA;AAAgB,IAAA,uBAAA;AAEvC,IAAA,yBAAA,IAAA,IAAA,EAAI,IAAA,QAAA,EAAA;AAEA,IAAA,iBAAA,EAAA;AACF,IAAA,uBAAA,EAAO;AAGT,IAAA,yBAAA,IAAA,IAAA,EAAI,IAAA,QAAA,EAAA;AAEA,IAAA,iBAAA,EAAA;AACF,IAAA,uBAAA,EAAO;AAGT,IAAA,yBAAA,IAAA,MAAA,EAAA;AAAsB,IAAA,iBAAA,EAAA;AAAsC,IAAA,uBAAA;AAE5D,IAAA,yBAAA,IAAA,MAAA,EAAA;AACE,IAAA,qBAAA,IAAA,uDAAA,GAAA,GAAA,QAAA,EAAA,EAAwD,IAAA,8DAAA,GAAA,GAAA,eAAA,MAAA,GAAA,gCAAA;AAM1D,IAAA,uBAAA;AAEA,IAAA,yBAAA,IAAA,MAAA,EAAA,EAAyB,IAAA,OAAA,EAAA,EACK,IAAA,UAAA,EAAA;AAKxB,IAAA,qBAAA,SAAA,SAAA,yEAAA;AAAA,YAAA,UAAA,wBAAA,GAAA,EAAA;AAAA,YAAA,SAAA,wBAAA,CAAA;AAAA,aAAA,sBAAS,OAAA,kBAAA,OAAA,CAAuB;IAAA,CAAA;AAEhC,IAAA,oBAAA,IAAA,GAAA;AACF,IAAA,uBAAA;AAEA,IAAA,yBAAA,IAAA,UAAA,EAAA;AAEE,IAAA,qBAAA,SAAA,SAAA,yEAAA;AAAA,YAAA,UAAA,wBAAA,GAAA,EAAA;AAAA,YAAA,SAAA,wBAAA,CAAA;AAAA,aAAA,sBAAS,OAAA,kBAAA,OAAA,CAAuB;IAAA,CAAA;AAEhC,IAAA,oBAAA,IAAA,KAAA,EAAA;AACF,IAAA,uBAAA,EAAS,EACL,EACH;;;;;;AAnDD,IAAA,oBAAA,CAAA;AAAA,IAAA,6BAAA,KAAA,OAAA,YAAA,QAAA,UAAA,GAAA,GAAA;AAGuB,IAAA,oBAAA,CAAA;AAAA,IAAA,4BAAA,QAAA,UAAA;AACF,IAAA,oBAAA,CAAA;AAAA,IAAA,6BAAA,QAAA,QAAA,IAAA,EAAA;AAIF,IAAA,oBAAA,CAAA;AAAA,IAAA,4BAAA,QAAA,KAAA;AAGI,IAAA,oBAAA,CAAA;AAAA,IAAA,qBAAA,WAAA,OAAA,aAAA,QAAA,IAAA,CAAA;AACvB,IAAA,oBAAA;AAAA,IAAA,6BAAA,KAAA,QAAA,MAAA,GAAA;AAKyB,IAAA,oBAAA,CAAA;AAAA,IAAA,qBAAA,WAAA,OAAA,eAAA,OAAA,CAAA;AACzB,IAAA,oBAAA;AAAA,IAAA,6BAAA,KAAA,OAAA,cAAA,OAAA,GAAA,GAAA;AAIkB,IAAA,oBAAA,CAAA;AAAA,IAAA,4BAAA,OAAA,WAAA,QAAA,eAAA,CAAA;AAGb,IAAA,oBAAA,CAAA;AAAA,IAAA,qBAAA,QAAA,QAAA,iBAAA,EAA8B,YAAA,eAAA;AAYjC,IAAA,oBAAA,CAAA;AAAA,IAAA,sBAAA,gBAAA,CAAA,QAAA,QAAA,EAAqC,kBAAA,QAAA,QAAA;AAGrC,IAAA,qBAAA,SAAA,QAAA,WAAA,kBAAA,SAAA;AACG,IAAA,oBAAA;AAAA,IAAA,qBAAA,QAAA,WAAA,eAAA,WAAA;;;;;AAgBf,IAAA,yBAAA,GAAA,OAAA,EAAA;AACE,IAAA,oBAAA,GAAA,KAAA,CAAA;AACA,IAAA,yBAAA,GAAA,IAAA;AAAI,IAAA,iBAAA,GAAA,6BAAA;AAAwB,IAAA,uBAAA;AAC5B,IAAA,yBAAA,GAAA,GAAA;AAAG,IAAA,iBAAA,GAAA,+DAAA;AAA0D,IAAA,uBAAA,EAAI;;;;;AA7ErE,IAAA,yBAAA,GAAA,OAAA,EAAA,EAA8D,GAAA,SAAA,EAAA,EACjC,GAAA,OAAA,EAClB,GAAA,IAAA,EACD,GAAA,IAAA;AACE,IAAA,iBAAA,GAAA,aAAA;AAAW,IAAA,uBAAA;AACf,IAAA,yBAAA,GAAA,IAAA;AAAI,IAAA,iBAAA,GAAA,OAAA;AAAK,IAAA,uBAAA;AACT,IAAA,yBAAA,GAAA,IAAA;AAAI,IAAA,iBAAA,GAAA,SAAA;AAAI,IAAA,uBAAA;AACR,IAAA,yBAAA,IAAA,IAAA;AAAI,IAAA,iBAAA,IAAA,QAAA;AAAM,IAAA,uBAAA;AACV,IAAA,yBAAA,IAAA,IAAA;AAAI,IAAA,iBAAA,IAAA,kBAAA;AAAa,IAAA,uBAAA;AACjB,IAAA,yBAAA,IAAA,IAAA;AAAI,IAAA,iBAAA,IAAA,uBAAA;AAAkB,IAAA,uBAAA;AACtB,IAAA,yBAAA,IAAA,IAAA;AAAI,IAAA,iBAAA,IAAA,SAAA;AAAO,IAAA,uBAAA,EAAK,EACb;AAEP,IAAA,yBAAA,IAAA,OAAA;AACE,IAAA,qBAAA,IAAA,+CAAA,IAAA,IAAA,MAAA,EAAA;AAwDF,IAAA,uBAAA,EAAQ;AAIV,IAAA,qBAAA,IAAA,gDAAA,GAAA,GAAA,OAAA,EAAA;AAKF,IAAA,uBAAA;;;;AAjE2B,IAAA,oBAAA,EAAA;AAAA,IAAA,qBAAA,WAAA,OAAA,aAAA,CAAA;AA4DnB,IAAA,oBAAA;AAAA,IAAA,qBAAA,QAAA,OAAA,aAAA,EAAA,WAAA,CAAA;;;;;;AAQR,IAAA,yBAAA,GAAA,OAAA,EAAA,EAA2D,GAAA,OAAA,EAAA;AAEvD,IAAA,iBAAA,CAAA;AAEF,IAAA,uBAAA;AAEA,IAAA,yBAAA,GAAA,OAAA,EAAA,EAAwB,GAAA,UAAA,EAAA;AAIpB,IAAA,qBAAA,SAAA,SAAA,kEAAA;AAAA,MAAA,wBAAA,GAAA;AAAA,YAAA,SAAA,wBAAA;AAAA,aAAA,sBAAS,OAAA,aAAa,OAAA,YAAA,IAAgB,CAAC,CAAC;IAAA,CAAA;AACxC,IAAA,oBAAA,GAAA,KAAA,EAAA;AACF,IAAA,uBAAA;AAEA,IAAA,yBAAA,GAAA,QAAA,EAAA;AACE,IAAA,iBAAA,CAAA;AACF,IAAA,uBAAA;AAEA,IAAA,yBAAA,GAAA,UAAA,EAAA;AAGE,IAAA,qBAAA,SAAA,SAAA,kEAAA;AAAA,MAAA,wBAAA,GAAA;AAAA,YAAA,SAAA,wBAAA;AAAA,aAAA,sBAAS,OAAA,aAAa,OAAA,YAAA,IAAgB,CAAC,CAAC;IAAA,CAAA;AACxC,IAAA,oBAAA,GAAA,KAAA,EAAA;AACF,IAAA,uBAAA,EAAS,EACL;;;;AAtBJ,IAAA,oBAAA,CAAA;AAAA,IAAA,6BAAA,mBAAA,OAAA,YAAA,IAAA,KAAA,OAAA,SAAA,IAAA,GAAA,UAAA,OAAA,KAAA,IAAA,OAAA,YAAA,IAAA,OAAA,SAAA,GAAA,OAAA,WAAA,CAAA,GAAA,SAAA,OAAA,WAAA,GAAA,gBAAA;AAOE,IAAA,oBAAA,CAAA;AAAA,IAAA,qBAAA,YAAA,OAAA,YAAA,MAAA,CAAA;AAMA,IAAA,oBAAA,CAAA;AAAA,IAAA,6BAAA,UAAA,OAAA,YAAA,GAAA,SAAA,OAAA,WAAA,GAAA,GAAA;AAKA,IAAA,oBAAA;AAAA,IAAA,qBAAA,YAAA,OAAA,YAAA,MAAA,OAAA,WAAA,CAAA;;;ADjKF,IAAO,0BAAP,MAAO,yBAAuB;EAqBd;;EAnBpB,eAAe,OAA2B,CAAA,CAAE;EAC5C,YAAY,OAAO,KAAK;EACxB,QAAQ,OAAO,EAAE;EACjB,aAAa,OAAO,EAAE;EACtB,eAAe,OAAO,EAAE;EACxB,iBAAiB,OAAO,EAAE;EAC1B,cAAc,OAAO,CAAC;EACtB,WAAW,OAAO,EAAE;EACpB,aAAa,OAAO,CAAC;;EAIrB,aAAa,SAAS,MAAM,KAAK,KAAK,KAAK,WAAU,IAAK,KAAK,SAAQ,CAAE,CAAC;EAE1E,QAAQ,CAAC,SAAS,eAAe,QAAQ;;EAGzC,OAAO;EAEP,YAAoB,cAA0B;AAA1B,SAAA,eAAA;EAA6B;EAEjD,WAAQ;AAEN,SAAK,iBAAgB;EACvB;EAIA,mBAAgB;AACd,SAAK,UAAU,IAAI,IAAI;AACvB,SAAK,MAAM,IAAI,EAAE;AAEjB,UAAM,SAAS;MACb,MAAM,KAAK,YAAW;MACtB,UAAU,KAAK,SAAQ;MACvB,QAAQ,KAAK,WAAU,KAAM;MAC7B,MAAM,KAAK,aAAY,KAAM;MAC7B,UAAU,KAAK,eAAc,MAAO,UAAU,OACpC,KAAK,eAAc,MAAO,YAAY,QAAQ;;AAG1D,SAAK,aAAa,gBAAgB,MAAM,EAAE,UAAU;MAClD,MAAM,CAAC,aAAY;AACjB,gBAAQ,IAAI,0CAA4B,QAAQ;AAEhD,aAAK,aAAa,IAAI,SAAS,gBAAgB,CAAA,CAAE;AACjD,aAAK,WAAW,IAAI,SAAS,cAAc,CAAC;AAC5C,aAAK,UAAU,IAAI,KAAK;MAC1B;MACA,OAAO,CAAC,UAAS;AACf,gBAAQ,MAAM,sDAAiD,KAAK;AACpE,aAAK,MAAM,IAAI,+CAA+C,MAAM,UAAU,kBAAe,EAAE;AAC/F,aAAK,UAAU,IAAI,KAAK;MAC1B;KACD;EACH;EAEA,kBAAkB,MAAsB;AACtC,UAAM,SAAS,KAAK,WAAW,kBAAe;AAE9C,QAAI,QAAQ,kCAA4B,MAAM,kBAAkB,KAAK,UAAU,IAAI,GAAG;AACpF,WAAK,aAAa,kBAAkB,KAAK,EAAE,EAAE,UAAU;QACrD,MAAM,MAAK;AAET,gBAAM,QAAQ,KAAK,aAAY;AAC/B,gBAAM,QAAQ,MAAM,UAAU,OAAK,EAAE,OAAO,KAAK,EAAE;AACnD,cAAI,UAAU,IAAI;AAChB,kBAAM,KAAK,IAAI,iCAAK,MAAM,KAAK,IAAhB,EAAmB,UAAU,CAAC,MAAM,KAAK,EAAE,SAAQ;AAClE,iBAAK,aAAa,IAAI,CAAC,GAAG,KAAK,CAAC;UAClC;AACA,gBAAM,eAAe,MAAM,iBAAc;QAC3C;QACA,OAAO,CAAC,UAAS;AACf,kBAAQ,MAAM,0BAA0B,KAAK;AAC7C,gBAAM,oBAAoB,MAAM,wBAAwB;QAC1D;OACD;IACH;EACF;EAEA,kBAAkB,MAAsB;AACtC,QAAI,QAAQ,4EAAmE,KAAK,UAAU;;kCAAsC,GAAG;AACrI,WAAK,aAAa,kBAAkB,KAAK,EAAE,EAAE,UAAU;QACrD,MAAM,MAAK;AAET,gBAAM,QAAQ,KAAK,aAAY,EAAG,OAAO,OAAK,EAAE,OAAO,KAAK,EAAE;AAC9D,eAAK,aAAa,IAAI,KAAK;AAC3B,eAAK,WAAW,IAAI,KAAK,WAAU,IAAK,CAAC;AACzC,gBAAM,wCAAkC;QAC1C;QACA,OAAO,CAAC,UAAS;AACf,kBAAQ,MAAM,kCAAkC,KAAK;AACrD,gBAAM,gDAAiD;QACzD;OACD;IACH;EACF;EAEA,WAAQ;AACN,SAAK,YAAY,IAAI,CAAC;AACtB,SAAK,iBAAgB;EACvB;EAEA,iBAAc;AACZ,SAAK,YAAY,IAAI,CAAC;AACtB,SAAK,iBAAgB;EACvB;EAEA,aAAa,MAAY;AACvB,SAAK,YAAY,IAAI,IAAI;AACzB,SAAK,iBAAgB;EACvB;EAEA,eAAe,MAAsB;AACnC,WAAO,KAAK,WAAW,kBAAkB;EAC3C;EAEA,cAAc,MAAsB;AAClC,WAAO,KAAK,WAAW,UAAU;EACnC;EAEA,aAAa,MAAY;AACvB,YAAQ,MAAM;MACZ,KAAK;AAAS,eAAO;MACrB,KAAK;AAAe,eAAO;MAC3B,KAAK;AAAU,eAAO;MACtB;AAAS,eAAO;IAClB;EACF;EAEA,YAAY,YAAkB;AAC5B,QAAI,CAAC;AAAY,aAAO;AAExB,UAAM,QAAQ,WAAW,KAAI,EAAG,MAAM,GAAG;AACzC,QAAI,MAAM,UAAU,GAAG;AACrB,cAAQ,MAAM,CAAC,EAAE,OAAO,CAAC,IAAI,MAAM,MAAM,SAAS,CAAC,EAAE,OAAO,CAAC,GAAG,YAAW;IAC7E;AACA,WAAO,WAAW,OAAO,CAAC,EAAE,YAAW,IAAK;EAC9C;EAEA,WAAW,MAAmB;AAC5B,QAAI,CAAC;AAAM,aAAO;AAElB,UAAM,UAAU,OAAO,SAAS,WAAW,IAAI,KAAK,IAAI,IAAI;AAE5D,QAAI,MAAM,QAAQ,QAAO,CAAE,GAAG;AAC5B,aAAO;IACT;AAEA,WAAO,QAAQ,mBAAmB,SAAS;MACzC,MAAM;MACN,OAAO;MACP,KAAK;MACL,MAAM;MACN,QAAQ;KACT;EACH;EAEA,UAAO;AACL,SAAK,iBAAgB;EACvB;EAEA,cAAW;AAET,UAAM,yDAAoD;EAC5D;;qCAvKW,0BAAuB,4BAAA,YAAA,CAAA;EAAA;yEAAvB,0BAAuB,WAAA,CAAA,CAAA,qBAAA,CAAA,GAAA,OAAA,IAAA,MAAA,IAAA,QAAA,CAAA,CAAA,gBAAA,EAAA,GAAA,CAAA,GAAA,2BAAA,GAAA,CAAA,GAAA,aAAA,GAAA,CAAA,GAAA,gBAAA,GAAA,CAAA,GAAA,YAAA,GAAA,CAAA,GAAA,YAAA,GAAA,CAAA,GAAA,gBAAA,GAAA,CAAA,GAAA,OAAA,iBAAA,GAAA,SAAA,UAAA,GAAA,CAAA,GAAA,cAAA,GAAA,CAAA,GAAA,OAAA,eAAA,GAAA,OAAA,GAAA,CAAA,GAAA,eAAA,GAAA,CAAA,GAAA,iBAAA,GAAA,CAAA,GAAA,YAAA,GAAA,CAAA,GAAA,aAAA,GAAA,CAAA,QAAA,QAAA,eAAA,6CAAA,GAAA,gBAAA,GAAA,iBAAA,SAAA,SAAA,GAAA,CAAA,GAAA,SAAA,GAAA,CAAA,GAAA,iBAAA,GAAA,iBAAA,UAAA,SAAA,GAAA,CAAA,SAAA,EAAA,GAAA,CAAA,GAAA,SAAA,GAAA,SAAA,SAAA,GAAA,CAAA,SAAA,OAAA,GAAA,CAAA,SAAA,SAAA,GAAA,CAAA,SAAA,iBAAA,GAAA,MAAA,GAAA,CAAA,SAAA,qBAAA,GAAA,MAAA,GAAA,CAAA,SAAA,cAAA,SAAA,sGAAA,GAAA,MAAA,GAAA,CAAA,SAAA,mBAAA,GAAA,MAAA,GAAA,CAAA,SAAA,wBAAA,GAAA,MAAA,GAAA,CAAA,GAAA,OAAA,GAAA,CAAA,GAAA,eAAA,GAAA,CAAA,GAAA,YAAA,GAAA,CAAA,GAAA,mBAAA,GAAA,CAAA,GAAA,iBAAA,GAAA,CAAA,GAAA,cAAA,GAAA,cAAA,WAAA,WAAA,QAAA,UAAA,UAAA,iBAAA,OAAA,UAAA,mBAAA,GAAA,CAAA,GAAA,UAAA,gBAAA,SAAA,SAAA,GAAA,CAAA,GAAA,iBAAA,GAAA,CAAA,GAAA,aAAA,GAAA,CAAA,SAAA,YAAA,GAAA,SAAA,SAAA,GAAA,CAAA,SAAA,WAAA,GAAA,MAAA,GAAA,CAAA,GAAA,UAAA,GAAA,CAAA,GAAA,WAAA,GAAA,CAAA,GAAA,aAAA,GAAA,CAAA,GAAA,cAAA,GAAA,CAAA,GAAA,WAAA,GAAA,CAAA,GAAA,SAAA,GAAA,CAAA,GAAA,YAAA,GAAA,CAAA,GAAA,cAAA,GAAA,SAAA,GAAA,CAAA,GAAA,gBAAA,GAAA,SAAA,GAAA,CAAA,GAAA,WAAA,GAAA,CAAA,GAAA,QAAA,UAAA,GAAA,CAAA,GAAA,cAAA,GAAA,CAAA,GAAA,gBAAA,GAAA,CAAA,GAAA,cAAA,cAAA,GAAA,SAAA,OAAA,GAAA,CAAA,SAAA,aAAA,GAAA,cAAA,cAAA,GAAA,OAAA,GAAA,CAAA,GAAA,YAAA,GAAA,CAAA,GAAA,eAAA,GAAA,CAAA,GAAA,SAAA,GAAA,CAAA,GAAA,sBAAA,GAAA,CAAA,GAAA,iBAAA,GAAA,CAAA,GAAA,YAAA,GAAA,CAAA,GAAA,YAAA,GAAA,SAAA,UAAA,GAAA,CAAA,GAAA,mBAAA,GAAA,CAAA,GAAA,WAAA,GAAA,CAAA,GAAA,oBAAA,CAAA,GAAA,UAAA,SAAA,iCAAA,IAAA,KAAA;AAAA,QAAA,KAAA,GAAA;ACZpC,MAAA,yBAAA,GAAA,OAAA,CAAA,EAAuC,GAAA,OAAA,CAAA,EAEZ,GAAA,OAAA,CAAA,EACK,GAAA,MAAA,CAAA;AAExB,MAAA,oBAAA,GAAA,KAAA,CAAA;AACA,MAAA,iBAAA,GAAA,4BAAA;AACF,MAAA,uBAAA;AACA,MAAA,yBAAA,GAAA,OAAA,CAAA,EAA4B,GAAA,UAAA,CAAA;AACQ,MAAA,qBAAA,SAAA,SAAA,2DAAA;AAAA,eAAS,IAAA,QAAA;MAAS,CAAA;AAClD,MAAA,oBAAA,GAAA,KAAA,CAAA;AACA,MAAA,iBAAA,GAAA,cAAA;AACF,MAAA,uBAAA;AACA,MAAA,yBAAA,IAAA,UAAA,CAAA;AAAgC,MAAA,qBAAA,SAAA,SAAA,4DAAA;AAAA,eAAS,IAAA,YAAA;MAAa,CAAA;AACpD,MAAA,oBAAA,IAAA,KAAA,EAAA;AACA,MAAA,iBAAA,IAAA,YAAA;AACF,MAAA,uBAAA,EAAS,EAEL,EACF;AAIR,MAAA,yBAAA,IAAA,OAAA,EAAA,EAA6B,IAAA,OAAA,EAAA;AAEzB,MAAA,oBAAA,IAAA,KAAA,EAAA;AACA,MAAA,yBAAA,IAAA,SAAA,EAAA;AAGE,MAAA,2BAAA,iBAAA,SAAA,iEAAA,QAAA;AAAA,QAAA,6BAAA,IAAA,YAAA,MAAA,MAAA,IAAA,aAAA;AAAA,eAAA;MAAA,CAAA;AACA,MAAA,qBAAA,SAAA,SAAA,2DAAA;AAAA,eAAS,IAAA,SAAA;MAAU,CAAA;AAJrB,MAAA,uBAAA,EAKuB;AAGzB,MAAA,yBAAA,IAAA,OAAA,EAAA,EAAqB,IAAA,UAAA,EAAA;AACX,MAAA,2BAAA,iBAAA,SAAA,kEAAA,QAAA;AAAA,QAAA,6BAAA,IAAA,cAAA,MAAA,MAAA,IAAA,eAAA;AAAA,eAAA;MAAA,CAAA;AAA2B,MAAA,qBAAA,UAAA,SAAA,6DAAA;AAAA,eAAU,IAAA,eAAA;MAAgB,CAAA;AAC3D,MAAA,yBAAA,IAAA,UAAA,EAAA;AAAiB,MAAA,iBAAA,IAAA,mBAAA;AAAc,MAAA,uBAAA;AAC/B,MAAA,qBAAA,IAAA,4CAAA,GAAA,GAAA,UAAA,EAAA;AACF,MAAA,uBAAA;AAEA,MAAA,yBAAA,IAAA,UAAA,EAAA;AAAQ,MAAA,2BAAA,iBAAA,SAAA,kEAAA,QAAA;AAAA,QAAA,6BAAA,IAAA,gBAAA,MAAA,MAAA,IAAA,iBAAA;AAAA,eAAA;MAAA,CAAA;AAA6B,MAAA,qBAAA,UAAA,SAAA,6DAAA;AAAA,eAAU,IAAA,eAAA;MAAgB,CAAA;AAC7D,MAAA,yBAAA,IAAA,UAAA,EAAA;AAAiB,MAAA,iBAAA,IAAA,kBAAA;AAAgB,MAAA,uBAAA;AACjC,MAAA,yBAAA,IAAA,UAAA,EAAA;AAAsB,MAAA,iBAAA,IAAA,QAAA;AAAM,MAAA,uBAAA;AAC5B,MAAA,yBAAA,IAAA,UAAA,EAAA;AAAwB,MAAA,iBAAA,IAAA,UAAA;AAAQ,MAAA,uBAAA,EAAS,EAClC,EACL;AAIR,MAAA,qBAAA,IAAA,yCAAA,GAAA,GAAA,OAAA,EAAA,EAA2C,IAAA,yCAAA,GAAA,GAAA,OAAA,EAAA,EAOQ,IAAA,yCAAA,IAAA,GAAA,OAAA,EAAA,EAMiH,IAAA,yCAAA,IAAA,GAAA,OAAA,EAAA,EAStG,IAAA,yCAAA,IAAA,GAAA,OAAA,EAAA;AA4GhE,MAAA,uBAAA;;;AA1K8D,MAAA,oBAAA,CAAA;AAAA,MAAA,qBAAA,YAAA,IAAA,UAAA,CAAA;AAoBtD,MAAA,oBAAA,CAAA;AAAA,MAAA,2BAAA,WAAA,IAAA,UAAA;AAMM,MAAA,oBAAA,CAAA;AAAA,MAAA,2BAAA,WAAA,IAAA,YAAA;AAEmB,MAAA,oBAAA,CAAA;AAAA,MAAA,qBAAA,WAAA,IAAA,KAAA;AAGnB,MAAA,oBAAA;AAAA,MAAA,2BAAA,WAAA,IAAA,cAAA;AASN,MAAA,oBAAA,CAAA;AAAA,MAAA,qBAAA,QAAA,IAAA,MAAA,CAAA;AAOA,MAAA,oBAAA;AAAA,MAAA,qBAAA,QAAA,IAAA,UAAA,CAAA;AAMA,MAAA,oBAAA;AAAA,MAAA,qBAAA,QAAA,CAAA,IAAA,UAAA,KAAA,CAAA,IAAA,MAAA,CAAA;AASA,MAAA,oBAAA;AAAA,MAAA,qBAAA,QAAA,CAAA,IAAA,UAAA,KAAA,CAAA,IAAA,MAAA,CAAA;AAkFA,MAAA,oBAAA;AAAA,MAAA,qBAAA,QAAA,IAAA,WAAA,IAAA,CAAA;;oBDjJI,cAAY,SAAA,SAAA,MAAE,aAAW,gBAAA,8BAAA,sBAAA,4BAAA,iBAAA,OAAA,GAAA,QAAA,CAAA,qpRAAA,EAAA,CAAA;;;sEAIxB,yBAAuB,CAAA;UAPnC;uBACW,uBAAqB,YACnB,MAAI,SACP,CAAC,cAAc,WAAW,GAAC,UAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;GAAA,QAAA,CAAA,ihOAAA,EAAA,CAAA;;;;6EAIzB,yBAAuB,EAAA,WAAA,2BAAA,UAAA,yEAAA,YAAA,GAAA,CAAA;AAAA,GAAA;", "names": []}
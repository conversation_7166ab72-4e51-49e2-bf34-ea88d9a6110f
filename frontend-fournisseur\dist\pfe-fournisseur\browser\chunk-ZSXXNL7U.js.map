{"version": 3, "sources": ["src/app/services/auth.service.ts"], "sourcesContent": ["import { Injectable, signal, computed, effect } from '@angular/core';\nimport { HttpClient, HttpHeaders, HttpContext, HttpContextToken } from '@angular/common/http';\nimport { BehaviorSubject, Observable, tap, catchError } from 'rxjs';\nimport { LoginRequest, LoginResponse, User } from '../models';\n\n@Injectable({\n  providedIn: 'root'\n})\nexport class AuthService {\n  // URLs possibles du backend\n  private readonly POSSIBLE_URLS = [\n    'http://localhost:5014/api',\n    'http://localhost:7264/api',\n    'https://localhost:7264/api',\n    'http://localhost:5000/api',\n    'https://localhost:5001/api',\n    'http://localhost:7265/api',\n    'https://localhost:7265/api'\n  ];\n\n  private readonly API_URL = 'http://localhost:5014/api'; // Backend HTTP\n  private readonly TOKEN_KEY = 'auth_token';\n  private readonly USER_KEY = 'current_user';\n\n  // Angular 19: Signals pour l'état d'authentification\n  currentUser = signal<User | null>(this.getCurrentUser());\n  isAuthenticated = signal<boolean>(this.hasValidToken());\n\n  // Angular 19: Computed signals pour des propriétés dérivées\n  userRole = computed(() => this.currentUser()?.role || null);\n  isFournisseur = computed(() => this.userRole() === 'Fournisseur');\n  isAdmin = computed(() => this.userRole() === 'Admin');\n  userName = computed(() => {\n    const user = this.currentUser();\n    return user ? `${user.prenom} ${user.nom}` : null;\n  });\n\n  // Compatibilité avec l'ancien système (pour migration progressive)\n  private currentUserSubject = new BehaviorSubject<User | null>(this.getCurrentUser());\n  public currentUser$ = this.currentUserSubject.asObservable();\n\n  private isAuthenticatedSubject = new BehaviorSubject<boolean>(this.hasValidToken());\n  public isAuthenticated$ = this.isAuthenticatedSubject.asObservable();\n\n  constructor(private http: HttpClient) {\n    // Angular 19: Effect pour synchroniser signals et observables\n    effect(() => {\n      const user = this.currentUser();\n      this.currentUserSubject.next(user);\n      console.log('👤 Utilisateur connecté:', user ? this.userName() : 'Aucun');\n    });\n\n    effect(() => {\n      const authenticated = this.isAuthenticated();\n      this.isAuthenticatedSubject.next(authenticated);\n      console.log('🔐 État d\\'authentification:', authenticated ? 'Connecté' : 'Déconnecté');\n    });\n  }\n\n  /**\n   * Connexion utilisateur\n   */\n  login(credentials: LoginRequest): Observable<LoginResponse> {\n    console.log('🔐 Tentative de connexion pour:', credentials.email);\n    console.log('🌐 URL API utilisée:', this.API_URL);\n    console.log('🌐 URL complète:', `${this.API_URL}/Auth/login`);\n\n    const headers = new HttpHeaders({\n      'Content-Type': 'application/json',\n      'Accept': 'application/json'\n    });\n\n    return this.http.post<LoginResponse>(`${this.API_URL}/Auth/login`, credentials, { headers })\n      .pipe(\n        tap(response => {\n          console.log('✅ Connexion réussie');\n          this.setSession(response);\n        })\n      );\n  }\n\n  /**\n   * Inscription fournisseur\n   */\n  registerFournisseur(formData: FormData): Observable<any> {\n    const url = `${this.API_URL}/Auth/register/fournisseur`;\n    console.log('📝 Tentative d\\'inscription à l\\'URL:', url);\n    console.log('📋 FormData contents:');\n\n    // Debug détaillé du FormData\n    const formDataEntries: any = {};\n    for (let pair of formData.entries()) {\n      const key = pair[0];\n      const value = pair[1];\n      formDataEntries[key] = value;\n\n      if (value instanceof File) {\n        console.log(`${key}: [File] ${value.name} (${value.size} bytes, ${value.type})`);\n      } else {\n        console.log(`${key}: ${value}`);\n      }\n    }\n\n    console.log('📊 FormData summary:', formDataEntries);\n\n    // Headers pour multipart/form-data\n    const headers = new HttpHeaders();\n    // Ne pas définir Content-Type pour multipart/form-data - le navigateur le fait automatiquement\n\n    console.log('🚀 Envoi de la requête...');\n\n    return this.http.post<any>(url, formData, {\n      headers: headers,\n      reportProgress: true,\n      observe: 'response'\n    }).pipe(\n      tap(response => {\n        console.log('✅ Inscription réussie - Response complète:', response);\n        console.log('✅ Status:', response.status);\n        console.log('✅ Body:', response.body);\n        console.log('✅ Headers:', response.headers);\n      }),\n      catchError(error => {\n        console.error('❌ Erreur inscription détaillée:');\n        console.error('❌ Status:', error.status);\n        console.error('❌ Status Text:', error.statusText);\n        console.error('❌ Error body:', error.error);\n        console.error('❌ Error message:', error.message);\n        console.error('❌ URL:', error.url);\n\n        if (error.headers) {\n          console.error('❌ Response headers:');\n          error.headers.keys().forEach((key: string) => {\n            console.error(`   ${key}: ${error.headers.get(key)}`);\n          });\n        }\n\n        // Essayer de parser l'erreur\n        if (error.error) {\n          try {\n            const errorDetails = typeof error.error === 'string' ? JSON.parse(error.error) : error.error;\n            console.error('❌ Parsed error details:', errorDetails);\n          } catch (e) {\n            console.error('❌ Raw error:', error.error);\n          }\n        }\n\n        throw error;\n      })\n    );\n  }\n\n  /**\n   * Test d'inscription fournisseur avec FormData (pour debug)\n   */\n  registerFournisseurJSON(data: any): Observable<any> {\n    const url = `${this.API_URL}/Fournisseurs`;\n    console.log('🧪 Test inscription avec FormData à l\\'URL:', url);\n    console.log('🧪 Data:', data);\n\n    // Créer un FormData avec les données et un fichier logo par défaut\n    const formData = new FormData();\n    \n    // Créer un fichier logo par défaut (1x1 pixel PNG transparent)\n    const defaultLogoBlob = new Blob([\n      new Uint8Array([\n        0x89, 0x50, 0x4E, 0x47, 0x0D, 0x0A, 0x1A, 0x0A, 0x00, 0x00, 0x00, 0x0D,\n        0x49, 0x48, 0x44, 0x52, 0x00, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, 0x01,\n        0x08, 0x06, 0x00, 0x00, 0x00, 0x1F, 0x15, 0xC4, 0x89, 0x00, 0x00, 0x00,\n        0x0A, 0x49, 0x44, 0x41, 0x54, 0x78, 0x9C, 0x63, 0x00, 0x01, 0x00, 0x00,\n        0x05, 0x00, 0x01, 0x0D, 0x0A, 0x2D, 0xB4, 0x00, 0x00, 0x00, 0x00, 0x49,\n        0x45, 0x4E, 0x44, 0xAE, 0x42, 0x60, 0x82\n      ])\n    ], { type: 'image/png' });\n    \n    const defaultLogoFile = new File([defaultLogoBlob], 'default-logo.png', { type: 'image/png' });\n\n    // Ajouter toutes les données au FormData\n    Object.keys(data).forEach(key => {\n      if (data[key] !== null && data[key] !== undefined) {\n        formData.append(key, data[key].toString());\n      }\n    });\n\n    // Ajouter le fichier logo\n    formData.append('LogoFile', defaultLogoFile);\n\n    console.log('🧪 FormData avec logo par défaut:');\n    for (let pair of formData.entries()) {\n      const key = pair[0];\n      const value = pair[1];\n      if (value instanceof File) {\n        console.log(`${key}: [File] ${value.name} (${value.size} bytes, ${value.type})`);\n      } else {\n        console.log(`${key}: ${value}`);\n      }\n    }\n\n    const headers = new HttpHeaders();\n    // Ne pas définir Content-Type pour multipart/form-data\n\n    return this.http.post<any>(url, formData, { headers }).pipe(\n      tap(response => {\n        console.log('✅ Test FormData réussi:', response);\n      }),\n      catchError(error => {\n        console.error('❌ Test FormData échoué:', error);\n        console.error('❌ Status:', error.status);\n        console.error('❌ Error body:', error.error);\n        throw error;\n      })\n    );\n  }\n\n  /**\n   * Version alternative sans fichier pour test\n   */\n  registerFournisseurWithoutFile(data: any): Observable<any> {\n    const url = `${this.API_URL}/Fournisseurs`;\n    console.log('🧪 Test inscription sans fichier à l\\'URL:', url);\n\n    const formData = new FormData();\n    Object.keys(data).forEach(key => {\n      if (data[key] !== null && data[key] !== undefined) {\n        formData.append(key, data[key].toString());\n      }\n    });\n\n    console.log('🧪 FormData sans fichier:');\n    for (let pair of formData.entries()) {\n      console.log(`${pair[0]}: ${pair[1]}`);\n    }\n\n    const headers = new HttpHeaders();\n    // Ne pas définir Content-Type pour multipart/form-data\n\n    return this.http.post<any>(url, formData, { headers }).pipe(\n      tap(response => {\n        console.log('✅ Test sans fichier réussi:', response);\n      }),\n      catchError(error => {\n        console.error('❌ Test sans fichier échoué:', error);\n        throw error;\n      })\n    );\n  }\n\n  /**\n   * Test simple de l'endpoint avec des données minimales\n   */\n  testEndpointWithMinimalData(): Observable<any> {\n    const url = `${this.API_URL}/Fournisseurs`;\n    console.log('🧪 Test endpoint avec données minimales à l\\'URL:', url);\n\n    // Créer un FormData avec un fichier logo par défaut\n    const formData = new FormData();\n    \n    // Créer un fichier logo par défaut (1x1 pixel PNG transparent)\n    const defaultLogoBlob = new Blob([\n      new Uint8Array([\n        0x89, 0x50, 0x4E, 0x47, 0x0D, 0x0A, 0x1A, 0x0A, 0x00, 0x00, 0x00, 0x0D,\n        0x49, 0x48, 0x44, 0x52, 0x00, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, 0x01,\n        0x08, 0x06, 0x00, 0x00, 0x00, 0x1F, 0x15, 0xC4, 0x89, 0x00, 0x00, 0x00,\n        0x0A, 0x49, 0x44, 0x41, 0x54, 0x78, 0x9C, 0x63, 0x00, 0x01, 0x00, 0x00,\n        0x05, 0x00, 0x01, 0x0D, 0x0A, 0x2D, 0xB4, 0x00, 0x00, 0x00, 0x00, 0x49,\n        0x45, 0x4E, 0x44, 0xAE, 0x42, 0x60, 0x82\n      ])\n    ], { type: 'image/png' });\n    \n    const defaultLogoFile = new File([defaultLogoBlob], 'default-logo.png', { type: 'image/png' });\n\n    // Ajouter les données au FormData\n    formData.append('Email', '<EMAIL>');\n    formData.append('Password', 'Test123!');\n    formData.append('Nom', 'Test');\n    formData.append('Prenom', 'User');\n    formData.append('Telephone', '1234567890');\n    formData.append('DateNaissance', '1990-01-01');\n    formData.append('MatriculeFiscale', '12345678');\n    formData.append('RaisonSociale', 'Test Company');\n    formData.append('Rib', '12345678901234567890');\n    formData.append('CodeBanque', '123');\n    formData.append('Commission', '0.15');\n    formData.append('DelaiPreparationJours', '2');\n    formData.append('FraisLivraisonBase', '9.99');\n    formData.append('EstActif', 'true');\n    formData.append('LogoFile', defaultLogoFile);\n\n    console.log('🧪 FormData avec logo par défaut:');\n    for (let pair of formData.entries()) {\n      const key = pair[0];\n      const value = pair[1];\n      if (value instanceof File) {\n        console.log(`${key}: [File] ${value.name} (${value.size} bytes, ${value.type})`);\n      } else {\n        console.log(`${key}: ${value}`);\n      }\n    }\n\n    const headers = new HttpHeaders();\n    // Ne pas définir Content-Type pour multipart/form-data\n\n    return this.http.post<any>(url, formData, { headers }).pipe(\n      tap(response => {\n        console.log('✅ Test minimal réussi:', response);\n      }),\n      catchError(error => {\n        console.error('❌ Test minimal échoué:', error);\n        console.error('❌ Status:', error.status);\n        console.error('❌ Error body:', error.error);\n        throw error;\n      })\n    );\n  }\n\n  /**\n   * Déconnexion avec Angular 19 signals et nettoyage complet\n   */\n  logout(): void {\n    console.log('🚪 Déconnexion et nettoyage du localStorage...');\n\n    // Suppression de toutes les données de session\n    localStorage.removeItem(this.TOKEN_KEY);\n    localStorage.removeItem(this.USER_KEY);\n    localStorage.removeItem('fournisseur_profile');\n    localStorage.removeItem('supplierId');\n    localStorage.removeItem('currentUser'); // Au cas où\n\n    console.log('✅ localStorage nettoyé');\n\n    // Angular 19: Mise à jour des signals\n    this.currentUser.set(null);\n    this.isAuthenticated.set(false);\n  }\n\n  /**\n   * Obtenir le token actuel\n   */\n  getToken(): string | null {\n    const token = localStorage.getItem(this.TOKEN_KEY);\n    console.log('🔍 AuthService.getToken() appelé:', {\n      hasToken: !!token,\n      tokenPreview: token ? token.substring(0, 20) + '...' : 'Aucun',\n      tokenKey: this.TOKEN_KEY\n    });\n    return token;\n  }\n\n  /**\n   * Obtenir l'utilisateur actuel\n   */\n  getCurrentUser(): User | null {\n    const userStr = localStorage.getItem(this.USER_KEY);\n    if (userStr) {\n      try {\n        return JSON.parse(userStr);\n      } catch {\n        return null;\n      }\n    }\n    return null;\n  }\n\n  /**\n   * Vérifier si l'utilisateur est connecté (méthode legacy)\n   */\n  isAuthenticatedLegacy(): boolean {\n    return this.hasValidToken();\n  }\n\n  /**\n   * Vérifier si l'utilisateur est un fournisseur (méthode legacy)\n   */\n  isFournisseurLegacy(): boolean {\n    const user = this.getCurrentUser();\n    return user?.role === 'Fournisseur';\n  }\n\n  /**\n   * Rafraîchir les informations utilisateur\n   */\n  refreshUser(): Observable<User> {\n    const currentUser = this.getCurrentUser();\n    if (!currentUser) {\n      throw new Error('Aucun utilisateur connecté');\n    }\n\n    return this.http.get<User>(`${this.API_URL}/Utilisateurs/${currentUser.id}`)\n      .pipe(\n        tap(user => {\n          localStorage.setItem(this.USER_KEY, JSON.stringify(user));\n          this.currentUserSubject.next(user);\n        })\n      );\n  }\n\n  /**\n   * Définir la session utilisateur avec Angular 19 signals et stockage complet\n   */\n  private setSession(authResult: LoginResponse): void {\n    console.log('💾 Stockage des données de session complètes...');\n    console.log('📦 Données utilisateur reçues:', authResult.utilisateur);\n\n    // Stockage standard\n    localStorage.setItem(this.TOKEN_KEY, authResult.token);\n    localStorage.setItem(this.USER_KEY, JSON.stringify(authResult.utilisateur));\n\n    // Stockage des données complètes du fournisseur pour le profil\n    const fournisseurData = {\n      // Données de base\n      id: authResult.utilisateur.id,\n      email: authResult.utilisateur.email,\n      nom: authResult.utilisateur.nom,\n      prenom: authResult.utilisateur.prenom,\n      role: authResult.utilisateur.role,\n\n      // Données étendues (si disponibles dans la réponse)\n      phoneNumber: (authResult.utilisateur as any).phoneNumber ||\n                   (authResult.utilisateur as any).telephone || '',\n      dateNaissance: (authResult.utilisateur as any).dateNaissance || '',\n      dateInscription: (authResult.utilisateur as any).dateInscription || new Date().toISOString(),\n      derniereConnexion: new Date().toISOString(),\n      estActif: (authResult.utilisateur as any).estActif !== undefined ?\n                (authResult.utilisateur as any).estActif : true,\n\n      // Données entreprise\n      matriculeFiscale: (authResult.utilisateur as any).matriculeFiscale || '',\n      raisonSociale: (authResult.utilisateur as any).raisonSociale || '',\n      description: (authResult.utilisateur as any).description || '',\n\n      // Données bancaires\n      ribMasque: (authResult.utilisateur as any).ribMasque ||\n                 (authResult.utilisateur as any).rib || '',\n      codeBanque: (authResult.utilisateur as any).codeBanque || '',\n\n      // Paramètres commerciaux\n      commission: (authResult.utilisateur as any).commission || 0,\n      delaiPreparationJours: (authResult.utilisateur as any).delaiPreparationJours || 3,\n      fraisLivraisonBase: (authResult.utilisateur as any).fraisLivraisonBase || 5.00,\n\n      // Autres données\n      logoFile: (authResult.utilisateur as any).logoFile || '',\n      adresses: (authResult.utilisateur as any).adresses || [],\n\n      // Métadonnées de session\n      sessionTimestamp: new Date().toISOString(),\n      tokenExpiry: this.calculateTokenExpiry(authResult.token)\n    };\n\n    // Stockage des données complètes du fournisseur\n    localStorage.setItem('fournisseur_profile', JSON.stringify(fournisseurData));\n    localStorage.setItem('supplierId', authResult.utilisateur.id.toString());\n\n    console.log('✅ Données complètes stockées:', fournisseurData);\n    console.log('📊 Clés localStorage créées:');\n    console.log('- token:', !!localStorage.getItem(this.TOKEN_KEY));\n    console.log('- user:', !!localStorage.getItem(this.USER_KEY));\n    console.log('- fournisseur_profile:', !!localStorage.getItem('fournisseur_profile'));\n    console.log('- supplierId:', localStorage.getItem('supplierId'));\n\n    // Angular 19: Mise à jour des signals\n    this.currentUser.set(authResult.utilisateur);\n    this.isAuthenticated.set(true);\n  }\n\n  /**\n   * Calculer l'expiration du token (estimation)\n   */\n  private calculateTokenExpiry(token: string): string {\n    try {\n      // Décoder le JWT pour obtenir l'expiration\n      const payload = JSON.parse(atob(token.split('.')[1]));\n      if (payload.exp) {\n        return new Date(payload.exp * 1000).toISOString();\n      }\n    } catch (error) {\n      console.warn('⚠️ Impossible de décoder le token JWT:', error);\n    }\n\n    // Fallback: 24 heures par défaut\n    const expiry = new Date();\n    expiry.setHours(expiry.getHours() + 24);\n    return expiry.toISOString();\n  }\n\n  /**\n   * Vérifier si le token est valide\n   */\n  private hasValidToken(): boolean {\n    const token = this.getToken();\n    if (!token) {\n      return false;\n    }\n\n    try {\n      // Décoder le JWT pour vérifier l'expiration\n      const payload = JSON.parse(atob(token.split('.')[1]));\n      const currentTime = Math.floor(Date.now() / 1000);\n      \n      return payload.exp > currentTime;\n    } catch {\n      return false;\n    }\n  }\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;AAQM,IAAO,cAAP,MAAO,aAAW;EAoCF;;EAlCH,gBAAgB;IAC/B;IACA;IACA;IACA;IACA;IACA;IACA;;EAGe,UAAU;;EACV,YAAY;EACZ,WAAW;;EAG5B,cAAc,OAAoB,KAAK,eAAc,CAAE;EACvD,kBAAkB,OAAgB,KAAK,cAAa,CAAE;;EAGtD,WAAW,SAAS,MAAM,KAAK,YAAW,GAAI,QAAQ,IAAI;EAC1D,gBAAgB,SAAS,MAAM,KAAK,SAAQ,MAAO,aAAa;EAChE,UAAU,SAAS,MAAM,KAAK,SAAQ,MAAO,OAAO;EACpD,WAAW,SAAS,MAAK;AACvB,UAAM,OAAO,KAAK,YAAW;AAC7B,WAAO,OAAO,GAAG,KAAK,MAAM,IAAI,KAAK,GAAG,KAAK;EAC/C,CAAC;;EAGO,qBAAqB,IAAI,gBAA6B,KAAK,eAAc,CAAE;EAC5E,eAAe,KAAK,mBAAmB,aAAY;EAElD,yBAAyB,IAAI,gBAAyB,KAAK,cAAa,CAAE;EAC3E,mBAAmB,KAAK,uBAAuB,aAAY;EAElE,YAAoB,MAAgB;AAAhB,SAAA,OAAA;AAElB,WAAO,MAAK;AACV,YAAM,OAAO,KAAK,YAAW;AAC7B,WAAK,mBAAmB,KAAK,IAAI;AACjC,cAAQ,IAAI,sCAA4B,OAAO,KAAK,SAAQ,IAAK,OAAO;IAC1E,CAAC;AAED,WAAO,MAAK;AACV,YAAM,gBAAgB,KAAK,gBAAe;AAC1C,WAAK,uBAAuB,KAAK,aAAa;AAC9C,cAAQ,IAAI,yCAAgC,gBAAgB,gBAAa,kBAAY;IACvF,CAAC;EACH;;;;EAKA,MAAM,aAAyB;AAC7B,YAAQ,IAAI,0CAAmC,YAAY,KAAK;AAChE,YAAQ,IAAI,kCAAwB,KAAK,OAAO;AAChD,YAAQ,IAAI,8BAAoB,GAAG,KAAK,OAAO,aAAa;AAE5D,UAAM,UAAU,IAAI,YAAY;MAC9B,gBAAgB;MAChB,UAAU;KACX;AAED,WAAO,KAAK,KAAK,KAAoB,GAAG,KAAK,OAAO,eAAe,aAAa,EAAE,QAAO,CAAE,EACxF,KACC,IAAI,cAAW;AACb,cAAQ,IAAI,6BAAqB;AACjC,WAAK,WAAW,QAAQ;IAC1B,CAAC,CAAC;EAER;;;;EAKA,oBAAoB,UAAkB;AACpC,UAAM,MAAM,GAAG,KAAK,OAAO;AAC3B,YAAQ,IAAI,iDAAyC,GAAG;AACxD,YAAQ,IAAI,8BAAuB;AAGnC,UAAM,kBAAuB,CAAA;AAC7B,aAAS,QAAQ,SAAS,QAAO,GAAI;AACnC,YAAM,MAAM,KAAK,CAAC;AAClB,YAAM,QAAQ,KAAK,CAAC;AACpB,sBAAgB,GAAG,IAAI;AAEvB,UAAI,iBAAiB,MAAM;AACzB,gBAAQ,IAAI,GAAG,GAAG,YAAY,MAAM,IAAI,KAAK,MAAM,IAAI,WAAW,MAAM,IAAI,GAAG;MACjF,OAAO;AACL,gBAAQ,IAAI,GAAG,GAAG,KAAK,KAAK,EAAE;MAChC;IACF;AAEA,YAAQ,IAAI,+BAAwB,eAAe;AAGnD,UAAM,UAAU,IAAI,YAAW;AAG/B,YAAQ,IAAI,qCAA2B;AAEvC,WAAO,KAAK,KAAK,KAAU,KAAK,UAAU;MACxC;MACA,gBAAgB;MAChB,SAAS;KACV,EAAE,KACD,IAAI,cAAW;AACb,cAAQ,IAAI,yDAA8C,QAAQ;AAClE,cAAQ,IAAI,kBAAa,SAAS,MAAM;AACxC,cAAQ,IAAI,gBAAW,SAAS,IAAI;AACpC,cAAQ,IAAI,mBAAc,SAAS,OAAO;IAC5C,CAAC,GACD,WAAW,WAAQ;AACjB,cAAQ,MAAM,4CAAiC;AAC/C,cAAQ,MAAM,kBAAa,MAAM,MAAM;AACvC,cAAQ,MAAM,uBAAkB,MAAM,UAAU;AAChD,cAAQ,MAAM,sBAAiB,MAAM,KAAK;AAC1C,cAAQ,MAAM,yBAAoB,MAAM,OAAO;AAC/C,cAAQ,MAAM,eAAU,MAAM,GAAG;AAEjC,UAAI,MAAM,SAAS;AACjB,gBAAQ,MAAM,0BAAqB;AACnC,cAAM,QAAQ,KAAI,EAAG,QAAQ,CAAC,QAAe;AAC3C,kBAAQ,MAAM,MAAM,GAAG,KAAK,MAAM,QAAQ,IAAI,GAAG,CAAC,EAAE;QACtD,CAAC;MACH;AAGA,UAAI,MAAM,OAAO;AACf,YAAI;AACF,gBAAM,eAAe,OAAO,MAAM,UAAU,WAAW,KAAK,MAAM,MAAM,KAAK,IAAI,MAAM;AACvF,kBAAQ,MAAM,gCAA2B,YAAY;QACvD,SAAS,GAAG;AACV,kBAAQ,MAAM,qBAAgB,MAAM,KAAK;QAC3C;MACF;AAEA,YAAM;IACR,CAAC,CAAC;EAEN;;;;EAKA,wBAAwB,MAAS;AAC/B,UAAM,MAAM,GAAG,KAAK,OAAO;AAC3B,YAAQ,IAAI,wDAA+C,GAAG;AAC9D,YAAQ,IAAI,mBAAY,IAAI;AAG5B,UAAM,WAAW,IAAI,SAAQ;AAG7B,UAAM,kBAAkB,IAAI,KAAK;MAC/B,IAAI,WAAW;QACb;QAAM;QAAM;QAAM;QAAM;QAAM;QAAM;QAAM;QAAM;QAAM;QAAM;QAAM;QAClE;QAAM;QAAM;QAAM;QAAM;QAAM;QAAM;QAAM;QAAM;QAAM;QAAM;QAAM;QAClE;QAAM;QAAM;QAAM;QAAM;QAAM;QAAM;QAAM;QAAM;QAAM;QAAM;QAAM;QAClE;QAAM;QAAM;QAAM;QAAM;QAAM;QAAM;QAAM;QAAM;QAAM;QAAM;QAAM;QAClE;QAAM;QAAM;QAAM;QAAM;QAAM;QAAM;QAAM;QAAM;QAAM;QAAM;QAAM;QAClE;QAAM;QAAM;QAAM;QAAM;QAAM;QAAM;OACrC;OACA,EAAE,MAAM,YAAW,CAAE;AAExB,UAAM,kBAAkB,IAAI,KAAK,CAAC,eAAe,GAAG,oBAAoB,EAAE,MAAM,YAAW,CAAE;AAG7F,WAAO,KAAK,IAAI,EAAE,QAAQ,SAAM;AAC9B,UAAI,KAAK,GAAG,MAAM,QAAQ,KAAK,GAAG,MAAM,QAAW;AACjD,iBAAS,OAAO,KAAK,KAAK,GAAG,EAAE,SAAQ,CAAE;MAC3C;IACF,CAAC;AAGD,aAAS,OAAO,YAAY,eAAe;AAE3C,YAAQ,IAAI,6CAAmC;AAC/C,aAAS,QAAQ,SAAS,QAAO,GAAI;AACnC,YAAM,MAAM,KAAK,CAAC;AAClB,YAAM,QAAQ,KAAK,CAAC;AACpB,UAAI,iBAAiB,MAAM;AACzB,gBAAQ,IAAI,GAAG,GAAG,YAAY,MAAM,IAAI,KAAK,MAAM,IAAI,WAAW,MAAM,IAAI,GAAG;MACjF,OAAO;AACL,gBAAQ,IAAI,GAAG,GAAG,KAAK,KAAK,EAAE;MAChC;IACF;AAEA,UAAM,UAAU,IAAI,YAAW;AAG/B,WAAO,KAAK,KAAK,KAAU,KAAK,UAAU,EAAE,QAAO,CAAE,EAAE,KACrD,IAAI,cAAW;AACb,cAAQ,IAAI,mCAA2B,QAAQ;IACjD,CAAC,GACD,WAAW,WAAQ;AACjB,cAAQ,MAAM,sCAA2B,KAAK;AAC9C,cAAQ,MAAM,kBAAa,MAAM,MAAM;AACvC,cAAQ,MAAM,sBAAiB,MAAM,KAAK;AAC1C,YAAM;IACR,CAAC,CAAC;EAEN;;;;EAKA,+BAA+B,MAAS;AACtC,UAAM,MAAM,GAAG,KAAK,OAAO;AAC3B,YAAQ,IAAI,uDAA8C,GAAG;AAE7D,UAAM,WAAW,IAAI,SAAQ;AAC7B,WAAO,KAAK,IAAI,EAAE,QAAQ,SAAM;AAC9B,UAAI,KAAK,GAAG,MAAM,QAAQ,KAAK,GAAG,MAAM,QAAW;AACjD,iBAAS,OAAO,KAAK,KAAK,GAAG,EAAE,SAAQ,CAAE;MAC3C;IACF,CAAC;AAED,YAAQ,IAAI,kCAA2B;AACvC,aAAS,QAAQ,SAAS,QAAO,GAAI;AACnC,cAAQ,IAAI,GAAG,KAAK,CAAC,CAAC,KAAK,KAAK,CAAC,CAAC,EAAE;IACtC;AAEA,UAAM,UAAU,IAAI,YAAW;AAG/B,WAAO,KAAK,KAAK,KAAU,KAAK,UAAU,EAAE,QAAO,CAAE,EAAE,KACrD,IAAI,cAAW;AACb,cAAQ,IAAI,uCAA+B,QAAQ;IACrD,CAAC,GACD,WAAW,WAAQ;AACjB,cAAQ,MAAM,0CAA+B,KAAK;AAClD,YAAM;IACR,CAAC,CAAC;EAEN;;;;EAKA,8BAA2B;AACzB,UAAM,MAAM,GAAG,KAAK,OAAO;AAC3B,YAAQ,IAAI,iEAAqD,GAAG;AAGpE,UAAM,WAAW,IAAI,SAAQ;AAG7B,UAAM,kBAAkB,IAAI,KAAK;MAC/B,IAAI,WAAW;QACb;QAAM;QAAM;QAAM;QAAM;QAAM;QAAM;QAAM;QAAM;QAAM;QAAM;QAAM;QAClE;QAAM;QAAM;QAAM;QAAM;QAAM;QAAM;QAAM;QAAM;QAAM;QAAM;QAAM;QAClE;QAAM;QAAM;QAAM;QAAM;QAAM;QAAM;QAAM;QAAM;QAAM;QAAM;QAAM;QAClE;QAAM;QAAM;QAAM;QAAM;QAAM;QAAM;QAAM;QAAM;QAAM;QAAM;QAAM;QAClE;QAAM;QAAM;QAAM;QAAM;QAAM;QAAM;QAAM;QAAM;QAAM;QAAM;QAAM;QAClE;QAAM;QAAM;QAAM;QAAM;QAAM;QAAM;OACrC;OACA,EAAE,MAAM,YAAW,CAAE;AAExB,UAAM,kBAAkB,IAAI,KAAK,CAAC,eAAe,GAAG,oBAAoB,EAAE,MAAM,YAAW,CAAE;AAG7F,aAAS,OAAO,SAAS,kBAAkB;AAC3C,aAAS,OAAO,YAAY,UAAU;AACtC,aAAS,OAAO,OAAO,MAAM;AAC7B,aAAS,OAAO,UAAU,MAAM;AAChC,aAAS,OAAO,aAAa,YAAY;AACzC,aAAS,OAAO,iBAAiB,YAAY;AAC7C,aAAS,OAAO,oBAAoB,UAAU;AAC9C,aAAS,OAAO,iBAAiB,cAAc;AAC/C,aAAS,OAAO,OAAO,sBAAsB;AAC7C,aAAS,OAAO,cAAc,KAAK;AACnC,aAAS,OAAO,cAAc,MAAM;AACpC,aAAS,OAAO,yBAAyB,GAAG;AAC5C,aAAS,OAAO,sBAAsB,MAAM;AAC5C,aAAS,OAAO,YAAY,MAAM;AAClC,aAAS,OAAO,YAAY,eAAe;AAE3C,YAAQ,IAAI,6CAAmC;AAC/C,aAAS,QAAQ,SAAS,QAAO,GAAI;AACnC,YAAM,MAAM,KAAK,CAAC;AAClB,YAAM,QAAQ,KAAK,CAAC;AACpB,UAAI,iBAAiB,MAAM;AACzB,gBAAQ,IAAI,GAAG,GAAG,YAAY,MAAM,IAAI,KAAK,MAAM,IAAI,WAAW,MAAM,IAAI,GAAG;MACjF,OAAO;AACL,gBAAQ,IAAI,GAAG,GAAG,KAAK,KAAK,EAAE;MAChC;IACF;AAEA,UAAM,UAAU,IAAI,YAAW;AAG/B,WAAO,KAAK,KAAK,KAAU,KAAK,UAAU,EAAE,QAAO,CAAE,EAAE,KACrD,IAAI,cAAW;AACb,cAAQ,IAAI,kCAA0B,QAAQ;IAChD,CAAC,GACD,WAAW,WAAQ;AACjB,cAAQ,MAAM,qCAA0B,KAAK;AAC7C,cAAQ,MAAM,kBAAa,MAAM,MAAM;AACvC,cAAQ,MAAM,sBAAiB,MAAM,KAAK;AAC1C,YAAM;IACR,CAAC,CAAC;EAEN;;;;EAKA,SAAM;AACJ,YAAQ,IAAI,0DAAgD;AAG5D,iBAAa,WAAW,KAAK,SAAS;AACtC,iBAAa,WAAW,KAAK,QAAQ;AACrC,iBAAa,WAAW,qBAAqB;AAC7C,iBAAa,WAAW,YAAY;AACpC,iBAAa,WAAW,aAAa;AAErC,YAAQ,IAAI,gCAAwB;AAGpC,SAAK,YAAY,IAAI,IAAI;AACzB,SAAK,gBAAgB,IAAI,KAAK;EAChC;;;;EAKA,WAAQ;AACN,UAAM,QAAQ,aAAa,QAAQ,KAAK,SAAS;AACjD,YAAQ,IAAI,+CAAqC;MAC/C,UAAU,CAAC,CAAC;MACZ,cAAc,QAAQ,MAAM,UAAU,GAAG,EAAE,IAAI,QAAQ;MACvD,UAAU,KAAK;KAChB;AACD,WAAO;EACT;;;;EAKA,iBAAc;AACZ,UAAM,UAAU,aAAa,QAAQ,KAAK,QAAQ;AAClD,QAAI,SAAS;AACX,UAAI;AACF,eAAO,KAAK,MAAM,OAAO;MAC3B,QAAQ;AACN,eAAO;MACT;IACF;AACA,WAAO;EACT;;;;EAKA,wBAAqB;AACnB,WAAO,KAAK,cAAa;EAC3B;;;;EAKA,sBAAmB;AACjB,UAAM,OAAO,KAAK,eAAc;AAChC,WAAO,MAAM,SAAS;EACxB;;;;EAKA,cAAW;AACT,UAAM,cAAc,KAAK,eAAc;AACvC,QAAI,CAAC,aAAa;AAChB,YAAM,IAAI,MAAM,+BAA4B;IAC9C;AAEA,WAAO,KAAK,KAAK,IAAU,GAAG,KAAK,OAAO,iBAAiB,YAAY,EAAE,EAAE,EACxE,KACC,IAAI,UAAO;AACT,mBAAa,QAAQ,KAAK,UAAU,KAAK,UAAU,IAAI,CAAC;AACxD,WAAK,mBAAmB,KAAK,IAAI;IACnC,CAAC,CAAC;EAER;;;;EAKQ,WAAW,YAAyB;AAC1C,YAAQ,IAAI,8DAAiD;AAC7D,YAAQ,IAAI,+CAAkC,WAAW,WAAW;AAGpE,iBAAa,QAAQ,KAAK,WAAW,WAAW,KAAK;AACrD,iBAAa,QAAQ,KAAK,UAAU,KAAK,UAAU,WAAW,WAAW,CAAC;AAG1E,UAAM,kBAAkB;;MAEtB,IAAI,WAAW,YAAY;MAC3B,OAAO,WAAW,YAAY;MAC9B,KAAK,WAAW,YAAY;MAC5B,QAAQ,WAAW,YAAY;MAC/B,MAAM,WAAW,YAAY;;MAG7B,aAAc,WAAW,YAAoB,eAC/B,WAAW,YAAoB,aAAa;MAC1D,eAAgB,WAAW,YAAoB,iBAAiB;MAChE,iBAAkB,WAAW,YAAoB,oBAAmB,oBAAI,KAAI,GAAG,YAAW;MAC1F,oBAAmB,oBAAI,KAAI,GAAG,YAAW;MACzC,UAAW,WAAW,YAAoB,aAAa,SAC5C,WAAW,YAAoB,WAAW;;MAGrD,kBAAmB,WAAW,YAAoB,oBAAoB;MACtE,eAAgB,WAAW,YAAoB,iBAAiB;MAChE,aAAc,WAAW,YAAoB,eAAe;;MAG5D,WAAY,WAAW,YAAoB,aAC/B,WAAW,YAAoB,OAAO;MAClD,YAAa,WAAW,YAAoB,cAAc;;MAG1D,YAAa,WAAW,YAAoB,cAAc;MAC1D,uBAAwB,WAAW,YAAoB,yBAAyB;MAChF,oBAAqB,WAAW,YAAoB,sBAAsB;;MAG1E,UAAW,WAAW,YAAoB,YAAY;MACtD,UAAW,WAAW,YAAoB,YAAY,CAAA;;MAGtD,mBAAkB,oBAAI,KAAI,GAAG,YAAW;MACxC,aAAa,KAAK,qBAAqB,WAAW,KAAK;;AAIzD,iBAAa,QAAQ,uBAAuB,KAAK,UAAU,eAAe,CAAC;AAC3E,iBAAa,QAAQ,cAAc,WAAW,YAAY,GAAG,SAAQ,CAAE;AAEvE,YAAQ,IAAI,+CAAiC,eAAe;AAC5D,YAAQ,IAAI,8CAA8B;AAC1C,YAAQ,IAAI,YAAY,CAAC,CAAC,aAAa,QAAQ,KAAK,SAAS,CAAC;AAC9D,YAAQ,IAAI,WAAW,CAAC,CAAC,aAAa,QAAQ,KAAK,QAAQ,CAAC;AAC5D,YAAQ,IAAI,0BAA0B,CAAC,CAAC,aAAa,QAAQ,qBAAqB,CAAC;AACnF,YAAQ,IAAI,iBAAiB,aAAa,QAAQ,YAAY,CAAC;AAG/D,SAAK,YAAY,IAAI,WAAW,WAAW;AAC3C,SAAK,gBAAgB,IAAI,IAAI;EAC/B;;;;EAKQ,qBAAqB,OAAa;AACxC,QAAI;AAEF,YAAM,UAAU,KAAK,MAAM,KAAK,MAAM,MAAM,GAAG,EAAE,CAAC,CAAC,CAAC;AACpD,UAAI,QAAQ,KAAK;AACf,eAAO,IAAI,KAAK,QAAQ,MAAM,GAAI,EAAE,YAAW;MACjD;IACF,SAAS,OAAO;AACd,cAAQ,KAAK,uDAA0C,KAAK;IAC9D;AAGA,UAAM,SAAS,oBAAI,KAAI;AACvB,WAAO,SAAS,OAAO,SAAQ,IAAK,EAAE;AACtC,WAAO,OAAO,YAAW;EAC3B;;;;EAKQ,gBAAa;AACnB,UAAM,QAAQ,KAAK,SAAQ;AAC3B,QAAI,CAAC,OAAO;AACV,aAAO;IACT;AAEA,QAAI;AAEF,YAAM,UAAU,KAAK,MAAM,KAAK,MAAM,MAAM,GAAG,EAAE,CAAC,CAAC,CAAC;AACpD,YAAM,cAAc,KAAK,MAAM,KAAK,IAAG,IAAK,GAAI;AAEhD,aAAO,QAAQ,MAAM;IACvB,QAAQ;AACN,aAAO;IACT;EACF;;qCA/eW,cAAW,mBAAA,UAAA,CAAA;EAAA;4EAAX,cAAW,SAAX,aAAW,WAAA,YAFV,OAAM,CAAA;;;sEAEP,aAAW,CAAA;UAHvB;WAAW;MACV,YAAY;KACb;;;", "names": []}
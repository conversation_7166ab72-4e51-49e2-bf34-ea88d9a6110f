-- <PERSON><PERSON>t pour corriger les images des produits du fournisseur 2070
-- Remplacer les images manquantes par des images existantes

-- Mettre à jour les images des produits pour pointer vers des fichiers existants
UPDATE ImagesProduit 
SET ImageUrl = '/uploads/produits/20250713170145_0d9e861e-c0cd-4b72-a6d3-37fa42a70032.jpg'
WHERE ProduitId IN (
    SELECT Id FROM Produits WHERE FournisseurId = 2070
) AND Id % 3 = 0;

UPDATE ImagesProduit 
SET ImageUrl = '/uploads/produits/20250713170145_0f055e53-85b4-45a0-978d-0ebcfd02e993.jpg'
WHERE ProduitId IN (
    SELECT Id FROM Produits WHERE FournisseurId = 2070
) AND Id % 3 = 1;

UPDATE ImagesProduit 
SET ImageUrl = '/uploads/produits/20250713170145_739e059c-77bb-4276-a7b7-aafa01eb32b8.jpg'
WHERE ProduitId IN (
    SELECT Id FROM Produits WHERE FournisseurId = 2070
) AND Id % 3 = 2;

-- Vérifier les résultats
SELECT p.Nom, ip.ImageUrl, ip.IsMain
FROM Produits p
INNER JOIN ImagesProduit ip ON p.Id = ip.ProduitId
WHERE p.FournisseurId = 2070
ORDER BY p.Id, ip.Ordre;

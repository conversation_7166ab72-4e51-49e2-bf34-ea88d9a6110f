{"version": 3, "sources": ["src/app/components/admin/layout/admin-layout/admin-layout.component.ts"], "sourcesContent": ["import { Component, OnInit } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { RouterOutlet, Router, RouterLink, RouterLinkActive } from '@angular/router';\nimport { AdminAuthService } from '../../../../services/admin-auth.service';\nimport { AdminUser } from '../../../../models/admin.model';\nimport { NotificationIconComponent } from '../../../notification-icon/notification-icon.component';\n\n@Component({\n  selector: 'app-admin-layout',\n  standalone: true,\n  imports: [CommonModule, RouterOutlet, RouterLink, RouterLinkActive, NotificationIconComponent],\n  template: `\n    <div class=\"admin-layout\">\n      <!-- Header sur toute la largeur -->\n      <header class=\"admin-header\">\n        <h1>Administration - OptiLet</h1>\n        <div class=\"header-actions\">\n          <app-notification-icon></app-notification-icon>\n          <span class=\"user-role\">{{ currentUser?.role }}</span>\n        </div>\n      </header>\n\n      <!-- Container pour sidebar et contenu -->\n      <div class=\"admin-body\">\n        <div class=\"admin-sidebar\">\n          <nav class=\"sidebar-nav\">\n            <a routerLink=\"/admin/dashboard\" routerLinkActive=\"active\" class=\"nav-item\">🏠 Dashboard</a>\n            <a routerLink=\"/admin/dashboard/users\" routerLinkActive=\"active\" class=\"nav-item\">👥 Utilisateurs</a>\n            <a routerLink=\"/admin/dashboard/products\" routerLinkActive=\"active\" class=\"nav-item\">📦 Produits</a>\n            <a routerLink=\"/admin/dashboard/demandes\" routerLinkActive=\"active\" class=\"nav-item\">📋 Demandes</a>\n            <a routerLink=\"/admin/dashboard/categories\" routerLinkActive=\"active\" class=\"nav-item\">📁 Catégories</a>\n            <a routerLink=\"/admin/dashboard/orders\" routerLinkActive=\"active\" class=\"nav-item\">🛒 Commandes</a>\n            <a routerLink=\"/admin/dashboard/avis-moderation\" routerLinkActive=\"active\" class=\"nav-item\">⭐ Modération Avis</a>\n            <a routerLink=\"/admin/dashboard/reports\" routerLinkActive=\"active\" class=\"nav-item\">📈 Rapports</a>\n          </nav>\n        </div>\n\n        <main class=\"admin-content\">\n          <router-outlet></router-outlet>\n        </main>\n      </div>\n    </div>\n  `,\n  styles: [`\n    .admin-layout {\n      display: flex;\n      flex-direction: column;\n      min-height: 100vh;\n      background: #f8fafc;\n    }\n    \n    .admin-body {\n      display: flex;\n      flex: 1;\n    }\n\n    .admin-sidebar {\n      width: 280px;\n      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n      color: white;\n      display: flex;\n      flex-direction: column;\n      box-shadow: 4px 0 20px rgba(102, 126, 234, 0.2);\n    }\n    \n\n    \n    .sidebar-nav {\n      flex: 1;\n      padding: 1rem 0;\n      display: flex;\n      flex-direction: column;\n    }\n    \n    .nav-item {\n      display: flex;\n      align-items: center;\n      padding: 1rem 2rem;\n      color: rgba(255, 255, 255, 0.9);\n      text-decoration: none;\n      transition: all 0.3s ease;\n      border-left: 3px solid transparent;\n      font-weight: 500;\n      font-size: 0.95rem;\n      margin: 0;\n      border-bottom: 1px solid rgba(255, 255, 255, 0.1);\n    }\n\n    .nav-item:hover {\n      background: rgba(255, 255, 255, 0.15);\n      color: white;\n      border-left-color: #667eea;\n      transform: translateX(4px);\n      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);\n    }\n\n    .nav-item.active {\n      background: rgba(102, 126, 234, 0.2);\n      color: white;\n      border-left-color: #667eea;\n      font-weight: 600;\n    }\n    \n\n    \n    .admin-main {\n      flex: 1;\n      display: flex;\n      flex-direction: column;\n    }\n    \n    .admin-header {\n      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n      padding: 1.5rem 2rem;\n      border-bottom: none;\n      display: flex;\n      justify-content: space-between;\n      align-items: center;\n      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);\n      width: 100%;\n      position: sticky;\n      top: 0;\n      z-index: 1000;\n    }\n\n    .header-actions {\n      display: flex;\n      align-items: center;\n      gap: 1.5rem;\n    }\n\n    .admin-header h1 {\n      margin: 0;\n      font-size: 1.75rem;\n      color: white;\n      font-weight: 600;\n      text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);\n    }\n\n    .user-role {\n      padding: 0.75rem 1.25rem;\n      background: rgba(255, 255, 255, 0.2);\n      color: white;\n      border-radius: 25px;\n      font-size: 0.875rem;\n      font-weight: 500;\n      border: 1px solid rgba(255, 255, 255, 0.3);\n      backdrop-filter: blur(10px);\n      transition: all 0.3s ease;\n    }\n\n    .user-role:hover {\n      background: rgba(255, 255, 255, 0.3);\n      transform: translateY(-1px);\n    }\n    \n    .admin-content {\n      flex: 1;\n      padding: 0;\n      overflow-y: auto;\n      background: #f8fafc;\n    }\n    \n    @media (max-width: 768px) {\n      .admin-header {\n        padding: 1rem;\n      }\n\n      .admin-header h1 {\n        font-size: 1.25rem;\n      }\n\n      .admin-body {\n        flex-direction: column;\n      }\n\n      .admin-sidebar {\n        width: 100%;\n        height: auto;\n        order: 2;\n      }\n\n      .admin-content {\n        order: 1;\n      }\n\n      .sidebar-nav {\n        display: flex;\n        overflow-x: auto;\n        padding: 1rem;\n        gap: 0.5rem;\n      }\n\n      .nav-item {\n        white-space: nowrap;\n        min-width: 120px;\n        text-align: center;\n        padding: 0.75rem 1rem;\n        border-radius: 12px;\n        border-left: none;\n        border-bottom: 3px solid transparent;\n      }\n\n      .nav-item:hover {\n        border-left: none;\n        border-bottom-color: #667eea;\n        transform: translateX(0);\n        transform: translateY(-2px);\n      }\n    }\n  `]\n})\nexport class AdminLayoutComponent implements OnInit {\n  currentUser: AdminUser | null = null;\n\n  constructor(\n    private adminAuthService: AdminAuthService,\n    private router: Router\n  ) {}\n\n  ngOnInit(): void {\n    this.adminAuthService.currentUser$.subscribe(user => {\n      this.currentUser = user;\n    });\n  }\n\n  logout(): void {\n    this.adminAuthService.logout().subscribe(() => {\n      this.router.navigate(['/admin/login']);\n    });\n  }\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAoNM,IAAO,uBAAP,MAAO,sBAAoB;EAIrB;EACA;EAJV,cAAgC;EAEhC,YACU,kBACA,QAAc;AADd,SAAA,mBAAA;AACA,SAAA,SAAA;EACP;EAEH,WAAQ;AACN,SAAK,iBAAiB,aAAa,UAAU,UAAO;AAClD,WAAK,cAAc;IACrB,CAAC;EACH;EAEA,SAAM;AACJ,SAAK,iBAAiB,OAAM,EAAG,UAAU,MAAK;AAC5C,WAAK,OAAO,SAAS,CAAC,cAAc,CAAC;IACvC,CAAC;EACH;;qCAlBW,uBAAoB,4BAAA,gBAAA,GAAA,4BAAA,MAAA,CAAA;EAAA;yEAApB,uBAAoB,WAAA,CAAA,CAAA,kBAAA,CAAA,GAAA,OAAA,IAAA,MAAA,GAAA,QAAA,CAAA,CAAA,GAAA,cAAA,GAAA,CAAA,GAAA,cAAA,GAAA,CAAA,GAAA,gBAAA,GAAA,CAAA,GAAA,WAAA,GAAA,CAAA,GAAA,YAAA,GAAA,CAAA,GAAA,eAAA,GAAA,CAAA,GAAA,aAAA,GAAA,CAAA,cAAA,oBAAA,oBAAA,UAAA,GAAA,UAAA,GAAA,CAAA,cAAA,0BAAA,oBAAA,UAAA,GAAA,UAAA,GAAA,CAAA,cAAA,6BAAA,oBAAA,UAAA,GAAA,UAAA,GAAA,CAAA,cAAA,6BAAA,oBAAA,UAAA,GAAA,UAAA,GAAA,CAAA,cAAA,+BAAA,oBAAA,UAAA,GAAA,UAAA,GAAA,CAAA,cAAA,2BAAA,oBAAA,UAAA,GAAA,UAAA,GAAA,CAAA,cAAA,oCAAA,oBAAA,UAAA,GAAA,UAAA,GAAA,CAAA,cAAA,4BAAA,oBAAA,UAAA,GAAA,UAAA,GAAA,CAAA,GAAA,eAAA,CAAA,GAAA,UAAA,SAAA,8BAAA,IAAA,KAAA;AAAA,QAAA,KAAA,GAAA;AAxM7B,MAAA,yBAAA,GAAA,OAAA,CAAA,EAA0B,GAAA,UAAA,CAAA,EAEK,GAAA,IAAA;AACvB,MAAA,iBAAA,GAAA,0BAAA;AAAwB,MAAA,uBAAA;AAC5B,MAAA,yBAAA,GAAA,OAAA,CAAA;AACE,MAAA,oBAAA,GAAA,uBAAA;AACA,MAAA,yBAAA,GAAA,QAAA,CAAA;AAAwB,MAAA,iBAAA,CAAA;AAAuB,MAAA,uBAAA,EAAO,EAClD;AAIR,MAAA,yBAAA,GAAA,OAAA,CAAA,EAAwB,GAAA,OAAA,CAAA,EACK,IAAA,OAAA,CAAA,EACA,IAAA,KAAA,CAAA;AACqD,MAAA,iBAAA,IAAA,qBAAA;AAAY,MAAA,uBAAA;AACxF,MAAA,yBAAA,IAAA,KAAA,CAAA;AAAkF,MAAA,iBAAA,IAAA,wBAAA;AAAe,MAAA,uBAAA;AACjG,MAAA,yBAAA,IAAA,KAAA,CAAA;AAAqF,MAAA,iBAAA,IAAA,oBAAA;AAAW,MAAA,uBAAA;AAChG,MAAA,yBAAA,IAAA,KAAA,EAAA;AAAqF,MAAA,iBAAA,IAAA,oBAAA;AAAW,MAAA,uBAAA;AAChG,MAAA,yBAAA,IAAA,KAAA,EAAA;AAAuF,MAAA,iBAAA,IAAA,yBAAA;AAAa,MAAA,uBAAA;AACpG,MAAA,yBAAA,IAAA,KAAA,EAAA;AAAmF,MAAA,iBAAA,IAAA,qBAAA;AAAY,MAAA,uBAAA;AAC/F,MAAA,yBAAA,IAAA,KAAA,EAAA;AAA4F,MAAA,iBAAA,IAAA,2BAAA;AAAiB,MAAA,uBAAA;AAC7G,MAAA,yBAAA,IAAA,KAAA,EAAA;AAAoF,MAAA,iBAAA,IAAA,oBAAA;AAAW,MAAA,uBAAA,EAAI,EAC/F;AAGR,MAAA,yBAAA,IAAA,QAAA,EAAA;AACE,MAAA,oBAAA,IAAA,eAAA;AACF,MAAA,uBAAA,EAAO,EACH;;;AAtBsB,MAAA,oBAAA,CAAA;AAAA,MAAA,4BAAA,IAAA,eAAA,OAAA,OAAA,IAAA,YAAA,IAAA;;oBARtB,cAAc,cAAc,YAAY,kBAAkB,yBAAyB,GAAA,QAAA,CAAA,wgHAAA,EAAA,CAAA;;;sEA0MlF,sBAAoB,CAAA;UA7MhC;uBACW,oBAAkB,YAChB,MAAI,SACP,CAAC,cAAc,cAAc,YAAY,kBAAkB,yBAAyB,GAAC,UACpF;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;KA+BT,QAAA,CAAA,6xGAAA,EAAA,CAAA;;;;6EA0KU,sBAAoB,EAAA,WAAA,wBAAA,UAAA,0EAAA,YAAA,IAAA,CAAA;AAAA,GAAA;", "names": []}
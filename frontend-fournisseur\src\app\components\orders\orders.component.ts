import { Component, OnInit, signal, computed } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { CommandeService, UpdateStatutCommandeFournisseurRequest } from '../../services/commande.service';
import { AuthService } from '../../services/auth.service';
import { CommandeFournisseur, StatutCommandeFournisseur } from '../../models/commande.model';

@Component({
  selector: 'app-orders',
  standalone: true,
  imports: [CommonModule, FormsModule],
  templateUrl: './orders.component.html',
  styleUrls: ['./orders.component.css']
})
export class OrdersComponent implements OnInit {
  // Angular 19: Signals
  orders = signal<CommandeFournisseur[]>([]);
  isLoading = signal(false);
  error = signal('');
  searchTerm = signal('');
  selectedStatus = signal('');
  currentPage = signal(1);
  pageSize = signal(10);
  expandedOrderId = signal<number | null>(null);

  // Computed signals
  filteredOrders = computed(() => {
    const orders = this.orders();
    const search = this.searchTerm().toLowerCase();
    const status = this.selectedStatus();

    return orders.filter(order => {
      const matchesSearch = !search ||
        order.reference.toLowerCase().includes(search) ||
        order.nomFournisseur?.toLowerCase().includes(search);

      const matchesStatus = !status || order.statut === status;

      return matchesSearch && matchesStatus;
    });
  });

  paginatedOrders = computed(() => {
    const filtered = this.filteredOrders();
    const page = this.currentPage();
    const size = this.pageSize();
    const start = (page - 1) * size;
    return filtered.slice(start, start + size);
  });

  totalPages = computed(() => {
    return Math.ceil(this.filteredOrders().length / this.pageSize());
  });

  constructor(
    private commandeService: CommandeService,
    private authService: AuthService
  ) {}

  ngOnInit(): void {
    console.log('🔄 OrdersComponent - ngOnInit appelé');
    this.loadOrders();
  }

  loadOrders(): void {
    console.log('🔄 loadOrders - Début du chargement');
    this.isLoading.set(true);
    this.error.set('');

    const currentUser = this.authService.getCurrentUser();
    console.log('👤 Utilisateur récupéré:', currentUser);

    if (!currentUser?.id) {
      console.error('❌ Aucun utilisateur connecté');
      this.error.set('Utilisateur non connecté');
      this.isLoading.set(false);
      return;
    }

    console.log('🔍 Appel API getCommandesByFournisseur avec ID:', currentUser.id);
    this.commandeService.getCommandesByFournisseur(currentUser.id).subscribe({
      next: (orders) => {
        console.log('📦 Commandes fournisseur récupérées:', orders);
        this.orders.set(orders);
        this.isLoading.set(false);
      },
      error: (error) => {
        console.error('❌ Erreur lors du chargement des commandes:', error);
        this.error.set('Erreur lors du chargement des commandes');
        this.isLoading.set(false);
      }
    });
  }

  // Méthodes utilitaires
  formatDate(date: string | Date): string {
    return new Date(date).toLocaleDateString('fr-FR', {
      day: '2-digit',
      month: '2-digit',
      year: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  }

  formatPrice(price: number): string {
    return new Intl.NumberFormat('fr-FR', {
      style: 'currency',
      currency: 'EUR'
    }).format(price);
  }

  getStatusClass(status: string): string {
    const statusClasses: { [key: string]: string } = {
      'Nouvelle': 'status-pending',
      'EnAttente': 'status-pending',
      'Acceptee': 'status-confirmed',
      'Confirmee': 'status-confirmed',
      'EnPreparation': 'status-preparing',
      'Prete': 'status-preparing',
      'Expediee': 'status-shipped',
      'Livree': 'status-delivered',
      'Refusee': 'status-cancelled',
      'Annulee': 'status-cancelled'
    };
    return statusClasses[status] || 'status-default';
  }

  getStatusText(status: string): string {
    const statusTexts: { [key: string]: string } = {
      'Nouvelle': 'Nouvelle',
      'EnAttente': 'En attente',
      'Acceptee': 'Acceptée',
      'Confirmee': 'Confirmée',
      'EnPreparation': 'En préparation',
      'Prete': 'Prête',
      'Expediee': 'Expédiée',
      'Livree': 'Livrée',
      'Refusee': 'Refusée',
      'Annulee': 'Annulée'
    };
    return statusTexts[status] || status;
  }

  // Actions sur les commandes
  toggleOrderDetails(orderId: number): void {
    const current = this.expandedOrderId();
    this.expandedOrderId.set(current === orderId ? null : orderId);
  }

  isOrderExpanded(orderId: number): boolean {
    return this.expandedOrderId() === orderId;
  }

  updateOrderStatus(orderId: number, newStatus: string): void {
    const request: UpdateStatutCommandeFournisseurRequest = {
      statut: newStatus as StatutCommandeFournisseur,
      numeroBonLivraison: newStatus === 'Livree' ? `BL-${Date.now()}` : undefined
    };

    this.commandeService.updateStatutCommandeFournisseur(orderId, request).subscribe({
      next: () => {
        console.log('✅ Statut de commande mis à jour');
        this.loadOrders(); // Recharger les commandes
      },
      error: (error) => {
        console.error('❌ Erreur lors de la mise à jour du statut:', error);
        this.error.set('Erreur lors de la mise à jour du statut');
      }
    });
  }

  // Pagination
  onPageChange(page: number): void {
    if (page >= 1 && page <= this.totalPages()) {
      this.currentPage.set(page);
    }
  }

  // Filtres
  onSearchChange(event: Event): void {
    const target = event.target as HTMLInputElement;
    this.searchTerm.set(target.value);
    this.currentPage.set(1); // Reset to first page
  }

  onStatusChange(event: Event): void {
    const target = event.target as HTMLSelectElement;
    this.selectedStatus.set(target.value);
    this.currentPage.set(1); // Reset to first page
  }

  clearFilters(): void {
    this.searchTerm.set('');
    this.selectedStatus.set('');
    this.currentPage.set(1);
  }

  // Méthodes pour les actions sur les commandes
  canConfirmOrder(status: string): boolean {
    return status === 'EnAttente' || status === 'Nouvelle';
  }

  canPrepareOrder(status: string): boolean {
    return status === 'Confirmee' || status === 'Acceptee';
  }

  canShipOrder(status: string): boolean {
    return status === 'EnPreparation' || status === 'Prete';
  }

  canDeliverOrder(status: string): boolean {
    return status === 'Expediee';
  }

  canCancelOrder(status: string): boolean {
    return ['EnAttente', 'Nouvelle', 'Confirmee', 'Acceptee'].includes(status);
  }

  // Math object for template
  Math = Math;
}

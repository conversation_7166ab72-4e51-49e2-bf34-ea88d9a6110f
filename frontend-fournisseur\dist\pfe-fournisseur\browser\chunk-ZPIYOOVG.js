import {
  DefaultValueAccessor,
  FormsModule,
  NgControlStatus,
  NgModel,
  NgSelectOption,
  SelectControlValueAccessor,
  ɵNgSelectMultipleOption
} from "./chunk-HQBVYEOO.js";
import {
  CommonModule,
  Component,
  NgForOf,
  NgIf,
  setClassMetadata,
  ɵsetClassDebugInfo,
  ɵɵadvance,
  ɵɵclassMap,
  ɵɵdefineComponent,
  ɵɵelement,
  ɵɵelementEnd,
  ɵɵelementStart,
  ɵɵgetCurrentView,
  ɵɵlistener,
  ɵɵnextContext,
  ɵɵproperty,
  ɵɵresetView,
  ɵɵrestoreView,
  ɵɵstyleProp,
  ɵɵtemplate,
  ɵɵtext,
  ɵɵtextInterpolate,
  ɵɵtextInterpolate1,
  ɵɵtwoWayBindingSet,
  ɵɵtwoWayListener,
  ɵɵtwoWayProperty
} from "./chunk-UBZQS7JS.js";

// src/app/components/admin/reports/reports.component.ts
function ReportsComponent_div_26_Template(rf, ctx) {
  if (rf & 1) {
    const _r1 = \u0275\u0275getCurrentView();
    \u0275\u0275elementStart(0, "div", 4)(1, "label");
    \u0275\u0275text(2, "Date de d\xE9but");
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(3, "input", 25);
    \u0275\u0275twoWayListener("ngModelChange", function ReportsComponent_div_26_Template_input_ngModelChange_3_listener($event) {
      \u0275\u0275restoreView(_r1);
      const ctx_r1 = \u0275\u0275nextContext();
      \u0275\u0275twoWayBindingSet(ctx_r1.startDate, $event) || (ctx_r1.startDate = $event);
      return \u0275\u0275resetView($event);
    });
    \u0275\u0275elementEnd()();
  }
  if (rf & 2) {
    const ctx_r1 = \u0275\u0275nextContext();
    \u0275\u0275advance(3);
    \u0275\u0275twoWayProperty("ngModel", ctx_r1.startDate);
  }
}
function ReportsComponent_div_27_Template(rf, ctx) {
  if (rf & 1) {
    const _r3 = \u0275\u0275getCurrentView();
    \u0275\u0275elementStart(0, "div", 4)(1, "label");
    \u0275\u0275text(2, "Date de fin");
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(3, "input", 25);
    \u0275\u0275twoWayListener("ngModelChange", function ReportsComponent_div_27_Template_input_ngModelChange_3_listener($event) {
      \u0275\u0275restoreView(_r3);
      const ctx_r1 = \u0275\u0275nextContext();
      \u0275\u0275twoWayBindingSet(ctx_r1.endDate, $event) || (ctx_r1.endDate = $event);
      return \u0275\u0275resetView($event);
    });
    \u0275\u0275elementEnd()();
  }
  if (rf & 2) {
    const ctx_r1 = \u0275\u0275nextContext();
    \u0275\u0275advance(3);
    \u0275\u0275twoWayProperty("ngModel", ctx_r1.endDate);
  }
}
function ReportsComponent_div_33_Template(rf, ctx) {
  if (rf & 1) {
    const _r4 = \u0275\u0275getCurrentView();
    \u0275\u0275elementStart(0, "div", 26);
    \u0275\u0275listener("click", function ReportsComponent_div_33_Template_div_click_0_listener() {
      const report_r5 = \u0275\u0275restoreView(_r4).$implicit;
      const ctx_r1 = \u0275\u0275nextContext();
      return \u0275\u0275resetView(ctx_r1.selectReport(report_r5));
    });
    \u0275\u0275elementStart(1, "div", 27);
    \u0275\u0275text(2);
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(3, "div", 28)(4, "h3");
    \u0275\u0275text(5);
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(6, "p");
    \u0275\u0275text(7);
    \u0275\u0275elementEnd()();
    \u0275\u0275elementStart(8, "div", 29)(9, "button", 30);
    \u0275\u0275text(10, "\u2B07\uFE0F");
    \u0275\u0275elementEnd()()();
  }
  if (rf & 2) {
    const report_r5 = ctx.$implicit;
    \u0275\u0275advance(2);
    \u0275\u0275textInterpolate(report_r5.icon);
    \u0275\u0275advance(3);
    \u0275\u0275textInterpolate(report_r5.name);
    \u0275\u0275advance(2);
    \u0275\u0275textInterpolate(report_r5.description);
  }
}
function ReportsComponent_div_41_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "div", 31)(1, "span", 32);
    \u0275\u0275text(2);
    \u0275\u0275elementEnd()();
  }
  if (rf & 2) {
    const data_r6 = ctx.$implicit;
    \u0275\u0275styleProp("height", data_r6.percentage, "%");
    \u0275\u0275advance(2);
    \u0275\u0275textInterpolate(data_r6.value);
  }
}
function ReportsComponent_div_47_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275element(0, "div", 33);
  }
  if (rf & 2) {
    const point_r7 = ctx.$implicit;
    \u0275\u0275styleProp("left", point_r7.x, "%")("bottom", point_r7.y, "%");
  }
}
function ReportsComponent_div_52_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "div", 34)(1, "div", 35);
    \u0275\u0275text(2);
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(3, "div", 36)(4, "div", 37);
    \u0275\u0275text(5);
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(6, "div", 38);
    \u0275\u0275text(7);
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(8, "div", 39);
    \u0275\u0275text(9);
    \u0275\u0275elementEnd()()();
  }
  if (rf & 2) {
    const metric_r8 = ctx.$implicit;
    \u0275\u0275advance(2);
    \u0275\u0275textInterpolate(metric_r8.icon);
    \u0275\u0275advance(3);
    \u0275\u0275textInterpolate(metric_r8.value);
    \u0275\u0275advance(2);
    \u0275\u0275textInterpolate(metric_r8.label);
    \u0275\u0275advance();
    \u0275\u0275classMap(metric_r8.changeType);
    \u0275\u0275advance();
    \u0275\u0275textInterpolate1(" ", metric_r8.change, " ");
  }
}
var ReportsComponent = class _ReportsComponent {
  selectedPeriod = "month";
  startDate = "";
  endDate = "";
  reportTypes = [
    {
      id: "sales",
      name: "Rapport des ventes",
      description: "Analyse d\xE9taill\xE9e des ventes par p\xE9riode",
      icon: "\u{1F4B0}"
    },
    {
      id: "users",
      name: "Rapport utilisateurs",
      description: "Statistiques d'inscription et d'activit\xE9",
      icon: "\u{1F465}"
    },
    {
      id: "products",
      name: "Rapport produits",
      description: "Performance des produits et stock",
      icon: "\u{1F4E6}"
    },
    {
      id: "orders",
      name: "Rapport commandes",
      description: "Analyse des commandes et livraisons",
      icon: "\u{1F6D2}"
    }
  ];
  salesData = [
    { value: 1200, percentage: 60 },
    { value: 1800, percentage: 90 },
    { value: 1500, percentage: 75 },
    { value: 2e3, percentage: 100 },
    { value: 1700, percentage: 85 },
    { value: 2200, percentage: 110 },
    { value: 1900, percentage: 95 }
  ];
  userGrowth = [
    { x: 10, y: 20 },
    { x: 25, y: 35 },
    { x: 40, y: 45 },
    { x: 55, y: 60 },
    { x: 70, y: 55 },
    { x: 85, y: 75 }
  ];
  keyMetrics = [
    {
      icon: "\u{1F4B0}",
      value: "\u20AC45,230",
      label: "Revenus totaux",
      change: "+12.5%",
      changeType: "positive"
    },
    {
      icon: "\u{1F6D2}",
      value: "1,234",
      label: "Commandes",
      change: "+8.2%",
      changeType: "positive"
    },
    {
      icon: "\u{1F465}",
      value: "856",
      label: "Nouveaux clients",
      change: "-2.1%",
      changeType: "negative"
    },
    {
      icon: "\u{1F4E6}",
      value: "2,456",
      label: "Produits vendus",
      change: "+15.3%",
      changeType: "positive"
    }
  ];
  ngOnInit() {
    this.loadReportsData();
  }
  loadReportsData() {
    console.log("Chargement des donn\xE9es de rapports...");
  }
  generateReports() {
    console.log("G\xE9n\xE9ration des rapports pour la p\xE9riode:", this.selectedPeriod);
  }
  selectReport(report) {
    console.log("Rapport s\xE9lectionn\xE9:", report);
  }
  static \u0275fac = function ReportsComponent_Factory(__ngFactoryType__) {
    return new (__ngFactoryType__ || _ReportsComponent)();
  };
  static \u0275cmp = /* @__PURE__ */ \u0275\u0275defineComponent({ type: _ReportsComponent, selectors: [["app-admin-reports"]], decls: 53, vars: 7, consts: [[1, "reports-page"], [1, "page-header"], [1, "reports-grid"], [1, "filters-section"], [1, "filter-group"], [1, "form-select", 3, "ngModelChange", "ngModel"], ["value", "today"], ["value", "week"], ["value", "month"], ["value", "quarter"], ["value", "year"], ["value", "custom"], ["class", "filter-group", 4, "ngIf"], [1, "btn-generate", 3, "click"], [1, "report-types"], ["class", "report-card", 3, "click", 4, "ngFor", "ngForOf"], [1, "charts-section"], [1, "chart-container"], [1, "chart-placeholder"], ["class", "chart-bar", 3, "height", 4, "ngFor", "ngForOf"], [1, "chart-line"], ["class", "line-point", 3, "left", "bottom", 4, "ngFor", "ngForOf"], [1, "metrics-section"], [1, "metrics-grid"], ["class", "metric-card", 4, "ngFor", "ngForOf"], ["type", "date", 1, "form-input", 3, "ngModelChange", "ngModel"], [1, "report-card", 3, "click"], [1, "report-icon"], [1, "report-info"], [1, "report-action"], [1, "btn-download"], [1, "chart-bar"], [1, "chart-value"], [1, "line-point"], [1, "metric-card"], [1, "metric-icon"], [1, "metric-content"], [1, "metric-value"], [1, "metric-label"], [1, "metric-change"]], template: function ReportsComponent_Template(rf, ctx) {
    if (rf & 1) {
      \u0275\u0275elementStart(0, "div", 0)(1, "div", 1)(2, "h1");
      \u0275\u0275text(3, "\u{1F4C8} Rapports et Analyses");
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(4, "p");
      \u0275\u0275text(5, "G\xE9n\xE9rer et consulter les rapports de performance");
      \u0275\u0275elementEnd()();
      \u0275\u0275elementStart(6, "div", 2)(7, "div", 3)(8, "h2");
      \u0275\u0275text(9, "\u{1F50D} Filtres");
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(10, "div", 4)(11, "label");
      \u0275\u0275text(12, "P\xE9riode");
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(13, "select", 5);
      \u0275\u0275twoWayListener("ngModelChange", function ReportsComponent_Template_select_ngModelChange_13_listener($event) {
        \u0275\u0275twoWayBindingSet(ctx.selectedPeriod, $event) || (ctx.selectedPeriod = $event);
        return $event;
      });
      \u0275\u0275elementStart(14, "option", 6);
      \u0275\u0275text(15, "Aujourd'hui");
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(16, "option", 7);
      \u0275\u0275text(17, "Cette semaine");
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(18, "option", 8);
      \u0275\u0275text(19, "Ce mois");
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(20, "option", 9);
      \u0275\u0275text(21, "Ce trimestre");
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(22, "option", 10);
      \u0275\u0275text(23, "Cette ann\xE9e");
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(24, "option", 11);
      \u0275\u0275text(25, "Personnalis\xE9");
      \u0275\u0275elementEnd()()();
      \u0275\u0275template(26, ReportsComponent_div_26_Template, 4, 1, "div", 12)(27, ReportsComponent_div_27_Template, 4, 1, "div", 12);
      \u0275\u0275elementStart(28, "button", 13);
      \u0275\u0275listener("click", function ReportsComponent_Template_button_click_28_listener() {
        return ctx.generateReports();
      });
      \u0275\u0275text(29, " \u{1F4CA} G\xE9n\xE9rer les rapports ");
      \u0275\u0275elementEnd()();
      \u0275\u0275elementStart(30, "div", 14)(31, "h2");
      \u0275\u0275text(32, "\u{1F4CB} Types de rapports");
      \u0275\u0275elementEnd();
      \u0275\u0275template(33, ReportsComponent_div_33_Template, 11, 3, "div", 15);
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(34, "div", 16)(35, "h2");
      \u0275\u0275text(36, "\u{1F4CA} Visualisations");
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(37, "div", 17)(38, "h3");
      \u0275\u0275text(39, "\xC9volution des ventes");
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(40, "div", 18);
      \u0275\u0275template(41, ReportsComponent_div_41_Template, 3, 3, "div", 19);
      \u0275\u0275elementEnd()();
      \u0275\u0275elementStart(42, "div", 17)(43, "h3");
      \u0275\u0275text(44, "Nouveaux utilisateurs");
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(45, "div", 18)(46, "div", 20);
      \u0275\u0275template(47, ReportsComponent_div_47_Template, 1, 4, "div", 21);
      \u0275\u0275elementEnd()()()();
      \u0275\u0275elementStart(48, "div", 22)(49, "h2");
      \u0275\u0275text(50, "\u{1F3AF} M\xE9triques cl\xE9s");
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(51, "div", 23);
      \u0275\u0275template(52, ReportsComponent_div_52_Template, 10, 6, "div", 24);
      \u0275\u0275elementEnd()()()();
    }
    if (rf & 2) {
      \u0275\u0275advance(13);
      \u0275\u0275twoWayProperty("ngModel", ctx.selectedPeriod);
      \u0275\u0275advance(13);
      \u0275\u0275property("ngIf", ctx.selectedPeriod === "custom");
      \u0275\u0275advance();
      \u0275\u0275property("ngIf", ctx.selectedPeriod === "custom");
      \u0275\u0275advance(6);
      \u0275\u0275property("ngForOf", ctx.reportTypes);
      \u0275\u0275advance(8);
      \u0275\u0275property("ngForOf", ctx.salesData);
      \u0275\u0275advance(6);
      \u0275\u0275property("ngForOf", ctx.userGrowth);
      \u0275\u0275advance(5);
      \u0275\u0275property("ngForOf", ctx.keyMetrics);
    }
  }, dependencies: [CommonModule, NgForOf, NgIf, FormsModule, NgSelectOption, \u0275NgSelectMultipleOption, DefaultValueAccessor, SelectControlValueAccessor, NgControlStatus, NgModel], styles: ["\n\n.reports-page[_ngcontent-%COMP%] {\n  padding: 2rem;\n  background: #f8fafc;\n  min-height: 100vh;\n}\n.page-header[_ngcontent-%COMP%] {\n  margin-bottom: 2rem;\n}\n.page-header[_ngcontent-%COMP%]   h1[_ngcontent-%COMP%] {\n  font-size: 2rem;\n  font-weight: 700;\n  color: #1e293b;\n  margin: 0 0 0.5rem 0;\n}\n.page-header[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\n  color: #64748b;\n  margin: 0;\n}\n.reports-grid[_ngcontent-%COMP%] {\n  display: grid;\n  grid-template-columns: 300px 1fr;\n  gap: 2rem;\n}\n.filters-section[_ngcontent-%COMP%], \n.report-types[_ngcontent-%COMP%], \n.charts-section[_ngcontent-%COMP%], \n.metrics-section[_ngcontent-%COMP%] {\n  background: white;\n  padding: 2rem;\n  border-radius: 16px;\n  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);\n  margin-bottom: 2rem;\n}\n.filters-section[_ngcontent-%COMP%]   h2[_ngcontent-%COMP%], \n.report-types[_ngcontent-%COMP%]   h2[_ngcontent-%COMP%], \n.charts-section[_ngcontent-%COMP%]   h2[_ngcontent-%COMP%], \n.metrics-section[_ngcontent-%COMP%]   h2[_ngcontent-%COMP%] {\n  font-size: 1.25rem;\n  font-weight: 600;\n  color: #1e293b;\n  margin: 0 0 1.5rem 0;\n}\n.filter-group[_ngcontent-%COMP%] {\n  margin-bottom: 1rem;\n}\n.filter-group[_ngcontent-%COMP%]   label[_ngcontent-%COMP%] {\n  display: block;\n  font-weight: 500;\n  color: #374151;\n  margin-bottom: 0.5rem;\n}\n.form-select[_ngcontent-%COMP%], \n.form-input[_ngcontent-%COMP%] {\n  width: 100%;\n  padding: 0.75rem;\n  border: 1px solid #d1d5db;\n  border-radius: 8px;\n  font-size: 0.875rem;\n}\n.btn-generate[_ngcontent-%COMP%] {\n  width: 100%;\n  padding: 1rem;\n  background:\n    linear-gradient(\n      135deg,\n      #667eea,\n      #764ba2);\n  color: white;\n  border: none;\n  border-radius: 12px;\n  font-weight: 600;\n  cursor: pointer;\n  margin-top: 1rem;\n}\n.report-card[_ngcontent-%COMP%] {\n  display: flex;\n  align-items: center;\n  padding: 1rem;\n  border: 1px solid #e5e7eb;\n  border-radius: 12px;\n  margin-bottom: 1rem;\n  cursor: pointer;\n  transition: all 0.3s ease;\n}\n.report-card[_ngcontent-%COMP%]:hover {\n  border-color: #667eea;\n  transform: translateY(-2px);\n  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);\n}\n.report-icon[_ngcontent-%COMP%] {\n  font-size: 2rem;\n  margin-right: 1rem;\n}\n.report-info[_ngcontent-%COMP%] {\n  flex: 1;\n}\n.report-info[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%] {\n  font-size: 1rem;\n  font-weight: 600;\n  color: #1e293b;\n  margin: 0 0 0.25rem 0;\n}\n.report-info[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\n  color: #64748b;\n  font-size: 0.875rem;\n  margin: 0;\n}\n.btn-download[_ngcontent-%COMP%] {\n  background: #f1f5f9;\n  border: 1px solid #e2e8f0;\n  border-radius: 8px;\n  padding: 0.5rem;\n  cursor: pointer;\n}\n.chart-container[_ngcontent-%COMP%] {\n  margin-bottom: 2rem;\n}\n.chart-container[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%] {\n  font-size: 1.125rem;\n  font-weight: 600;\n  color: #1e293b;\n  margin-bottom: 1rem;\n}\n.chart-placeholder[_ngcontent-%COMP%] {\n  height: 200px;\n  background: #f8fafc;\n  border-radius: 8px;\n  display: flex;\n  align-items: end;\n  justify-content: space-around;\n  padding: 1rem;\n  position: relative;\n}\n.chart-bar[_ngcontent-%COMP%] {\n  width: 40px;\n  background:\n    linear-gradient(\n      135deg,\n      #667eea,\n      #764ba2);\n  border-radius: 4px 4px 0 0;\n  position: relative;\n  min-height: 20px;\n}\n.chart-value[_ngcontent-%COMP%] {\n  position: absolute;\n  top: -25px;\n  left: 50%;\n  transform: translateX(-50%);\n  font-size: 0.75rem;\n  font-weight: 600;\n  color: #374151;\n}\n.chart-line[_ngcontent-%COMP%] {\n  width: 100%;\n  height: 100%;\n  position: relative;\n}\n.line-point[_ngcontent-%COMP%] {\n  position: absolute;\n  width: 8px;\n  height: 8px;\n  background: #667eea;\n  border-radius: 50%;\n  transform: translate(-50%, 50%);\n}\n.metrics-grid[_ngcontent-%COMP%] {\n  display: grid;\n  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));\n  gap: 1rem;\n}\n.metric-card[_ngcontent-%COMP%] {\n  display: flex;\n  align-items: center;\n  padding: 1.5rem;\n  background: #f8fafc;\n  border-radius: 12px;\n  gap: 1rem;\n}\n.metric-icon[_ngcontent-%COMP%] {\n  font-size: 2rem;\n}\n.metric-value[_ngcontent-%COMP%] {\n  font-size: 1.5rem;\n  font-weight: 700;\n  color: #1e293b;\n}\n.metric-label[_ngcontent-%COMP%] {\n  color: #64748b;\n  font-size: 0.875rem;\n}\n.metric-change[_ngcontent-%COMP%] {\n  font-size: 0.75rem;\n  font-weight: 600;\n  padding: 0.25rem 0.5rem;\n  border-radius: 12px;\n}\n.metric-change.positive[_ngcontent-%COMP%] {\n  background: #dcfce7;\n  color: #166534;\n}\n.metric-change.negative[_ngcontent-%COMP%] {\n  background: #fecaca;\n  color: #991b1b;\n}\n@media (max-width: 768px) {\n  .reports-page[_ngcontent-%COMP%] {\n    padding: 1rem;\n  }\n  .reports-grid[_ngcontent-%COMP%] {\n    grid-template-columns: 1fr;\n  }\n  .metrics-grid[_ngcontent-%COMP%] {\n    grid-template-columns: 1fr;\n  }\n}\n/*# sourceMappingURL=reports.component.css.map */"] });
};
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && setClassMetadata(ReportsComponent, [{
    type: Component,
    args: [{ selector: "app-admin-reports", standalone: true, imports: [CommonModule, FormsModule], template: `
    <div class="reports-page">
      <div class="page-header">
        <h1>\u{1F4C8} Rapports et Analyses</h1>
        <p>G\xE9n\xE9rer et consulter les rapports de performance</p>
      </div>

      <div class="reports-grid">
        <!-- Filtres -->
        <div class="filters-section">
          <h2>\u{1F50D} Filtres</h2>
          <div class="filter-group">
            <label>P\xE9riode</label>
            <select [(ngModel)]="selectedPeriod" class="form-select">
              <option value="today">Aujourd'hui</option>
              <option value="week">Cette semaine</option>
              <option value="month">Ce mois</option>
              <option value="quarter">Ce trimestre</option>
              <option value="year">Cette ann\xE9e</option>
              <option value="custom">Personnalis\xE9</option>
            </select>
          </div>
          
          <div class="filter-group" *ngIf="selectedPeriod === 'custom'">
            <label>Date de d\xE9but</label>
            <input type="date" [(ngModel)]="startDate" class="form-input">
          </div>
          
          <div class="filter-group" *ngIf="selectedPeriod === 'custom'">
            <label>Date de fin</label>
            <input type="date" [(ngModel)]="endDate" class="form-input">
          </div>

          <button class="btn-generate" (click)="generateReports()">
            \u{1F4CA} G\xE9n\xE9rer les rapports
          </button>
        </div>

        <!-- Types de rapports -->
        <div class="report-types">
          <h2>\u{1F4CB} Types de rapports</h2>
          <div class="report-card" *ngFor="let report of reportTypes" (click)="selectReport(report)">
            <div class="report-icon">{{ report.icon }}</div>
            <div class="report-info">
              <h3>{{ report.name }}</h3>
              <p>{{ report.description }}</p>
            </div>
            <div class="report-action">
              <button class="btn-download">\u2B07\uFE0F</button>
            </div>
          </div>
        </div>

        <!-- Graphiques -->
        <div class="charts-section">
          <h2>\u{1F4CA} Visualisations</h2>
          
          <!-- Graphique des ventes -->
          <div class="chart-container">
            <h3>\xC9volution des ventes</h3>
            <div class="chart-placeholder">
              <div class="chart-bar" *ngFor="let data of salesData" [style.height.%]="data.percentage">
                <span class="chart-value">{{ data.value }}</span>
              </div>
            </div>
          </div>

          <!-- Graphique des utilisateurs -->
          <div class="chart-container">
            <h3>Nouveaux utilisateurs</h3>
            <div class="chart-placeholder">
              <div class="chart-line">
                <div class="line-point" *ngFor="let point of userGrowth" 
                     [style.left.%]="point.x" 
                     [style.bottom.%]="point.y">
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- M\xE9triques cl\xE9s -->
        <div class="metrics-section">
          <h2>\u{1F3AF} M\xE9triques cl\xE9s</h2>
          <div class="metrics-grid">
            <div class="metric-card" *ngFor="let metric of keyMetrics">
              <div class="metric-icon">{{ metric.icon }}</div>
              <div class="metric-content">
                <div class="metric-value">{{ metric.value }}</div>
                <div class="metric-label">{{ metric.label }}</div>
                <div class="metric-change" [class]="metric.changeType">
                  {{ metric.change }}
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  `, styles: ["/* angular:styles/component:css;97ce423777d21896fbd7a372e87d89acd3ca052ab700ab6b1d8face311cdf750;C:/Users/<USER>/Desktop/STE/PFE/PFE/frontend-fournisseur/src/app/components/admin/reports/reports.component.ts */\n.reports-page {\n  padding: 2rem;\n  background: #f8fafc;\n  min-height: 100vh;\n}\n.page-header {\n  margin-bottom: 2rem;\n}\n.page-header h1 {\n  font-size: 2rem;\n  font-weight: 700;\n  color: #1e293b;\n  margin: 0 0 0.5rem 0;\n}\n.page-header p {\n  color: #64748b;\n  margin: 0;\n}\n.reports-grid {\n  display: grid;\n  grid-template-columns: 300px 1fr;\n  gap: 2rem;\n}\n.filters-section,\n.report-types,\n.charts-section,\n.metrics-section {\n  background: white;\n  padding: 2rem;\n  border-radius: 16px;\n  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);\n  margin-bottom: 2rem;\n}\n.filters-section h2,\n.report-types h2,\n.charts-section h2,\n.metrics-section h2 {\n  font-size: 1.25rem;\n  font-weight: 600;\n  color: #1e293b;\n  margin: 0 0 1.5rem 0;\n}\n.filter-group {\n  margin-bottom: 1rem;\n}\n.filter-group label {\n  display: block;\n  font-weight: 500;\n  color: #374151;\n  margin-bottom: 0.5rem;\n}\n.form-select,\n.form-input {\n  width: 100%;\n  padding: 0.75rem;\n  border: 1px solid #d1d5db;\n  border-radius: 8px;\n  font-size: 0.875rem;\n}\n.btn-generate {\n  width: 100%;\n  padding: 1rem;\n  background:\n    linear-gradient(\n      135deg,\n      #667eea,\n      #764ba2);\n  color: white;\n  border: none;\n  border-radius: 12px;\n  font-weight: 600;\n  cursor: pointer;\n  margin-top: 1rem;\n}\n.report-card {\n  display: flex;\n  align-items: center;\n  padding: 1rem;\n  border: 1px solid #e5e7eb;\n  border-radius: 12px;\n  margin-bottom: 1rem;\n  cursor: pointer;\n  transition: all 0.3s ease;\n}\n.report-card:hover {\n  border-color: #667eea;\n  transform: translateY(-2px);\n  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);\n}\n.report-icon {\n  font-size: 2rem;\n  margin-right: 1rem;\n}\n.report-info {\n  flex: 1;\n}\n.report-info h3 {\n  font-size: 1rem;\n  font-weight: 600;\n  color: #1e293b;\n  margin: 0 0 0.25rem 0;\n}\n.report-info p {\n  color: #64748b;\n  font-size: 0.875rem;\n  margin: 0;\n}\n.btn-download {\n  background: #f1f5f9;\n  border: 1px solid #e2e8f0;\n  border-radius: 8px;\n  padding: 0.5rem;\n  cursor: pointer;\n}\n.chart-container {\n  margin-bottom: 2rem;\n}\n.chart-container h3 {\n  font-size: 1.125rem;\n  font-weight: 600;\n  color: #1e293b;\n  margin-bottom: 1rem;\n}\n.chart-placeholder {\n  height: 200px;\n  background: #f8fafc;\n  border-radius: 8px;\n  display: flex;\n  align-items: end;\n  justify-content: space-around;\n  padding: 1rem;\n  position: relative;\n}\n.chart-bar {\n  width: 40px;\n  background:\n    linear-gradient(\n      135deg,\n      #667eea,\n      #764ba2);\n  border-radius: 4px 4px 0 0;\n  position: relative;\n  min-height: 20px;\n}\n.chart-value {\n  position: absolute;\n  top: -25px;\n  left: 50%;\n  transform: translateX(-50%);\n  font-size: 0.75rem;\n  font-weight: 600;\n  color: #374151;\n}\n.chart-line {\n  width: 100%;\n  height: 100%;\n  position: relative;\n}\n.line-point {\n  position: absolute;\n  width: 8px;\n  height: 8px;\n  background: #667eea;\n  border-radius: 50%;\n  transform: translate(-50%, 50%);\n}\n.metrics-grid {\n  display: grid;\n  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));\n  gap: 1rem;\n}\n.metric-card {\n  display: flex;\n  align-items: center;\n  padding: 1.5rem;\n  background: #f8fafc;\n  border-radius: 12px;\n  gap: 1rem;\n}\n.metric-icon {\n  font-size: 2rem;\n}\n.metric-value {\n  font-size: 1.5rem;\n  font-weight: 700;\n  color: #1e293b;\n}\n.metric-label {\n  color: #64748b;\n  font-size: 0.875rem;\n}\n.metric-change {\n  font-size: 0.75rem;\n  font-weight: 600;\n  padding: 0.25rem 0.5rem;\n  border-radius: 12px;\n}\n.metric-change.positive {\n  background: #dcfce7;\n  color: #166534;\n}\n.metric-change.negative {\n  background: #fecaca;\n  color: #991b1b;\n}\n@media (max-width: 768px) {\n  .reports-page {\n    padding: 1rem;\n  }\n  .reports-grid {\n    grid-template-columns: 1fr;\n  }\n  .metrics-grid {\n    grid-template-columns: 1fr;\n  }\n}\n/*# sourceMappingURL=reports.component.css.map */\n"] }]
  }], null, null);
})();
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && \u0275setClassDebugInfo(ReportsComponent, { className: "ReportsComponent", filePath: "src/app/components/admin/reports/reports.component.ts", lineNumber: 348 });
})();
export {
  ReportsComponent
};
//# sourceMappingURL=chunk-ZPIYOOVG.js.map

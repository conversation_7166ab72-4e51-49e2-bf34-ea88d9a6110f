import {
  CommonModule,
  Component,
  setClassMetadata,
  ɵsetClassDebugInfo,
  ɵɵdefineComponent,
  ɵɵelementEnd,
  ɵɵelementStart,
  ɵɵtext
} from "./chunk-UBZQS7JS.js";

// src/app/components/admin/business/promotion-engine/promotion-engine.component.ts
var PromotionEngineComponent = class _PromotionEngineComponent {
  static \u0275fac = function PromotionEngineComponent_Factory(__ngFactoryType__) {
    return new (__ngFactoryType__ || _PromotionEngineComponent)();
  };
  static \u0275cmp = /* @__PURE__ */ \u0275\u0275defineComponent({ type: _PromotionEngineComponent, selectors: [["app-promotion-engine"]], decls: 5, vars: 0, template: function PromotionEngineComponent_Template(rf, ctx) {
    if (rf & 1) {
      \u0275\u0275elementStart(0, "div")(1, "h1");
      \u0275\u0275text(2, "\u{1F3AF} Moteur de promotions");
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(3, "p");
      \u0275\u0275text(4, "En d\xE9veloppement...");
      \u0275\u0275elementEnd()();
    }
  }, dependencies: [CommonModule], encapsulation: 2 });
};
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && setClassMetadata(PromotionEngineComponent, [{
    type: Component,
    args: [{
      selector: "app-promotion-engine",
      standalone: true,
      imports: [CommonModule],
      template: `<div><h1>\u{1F3AF} Moteur de promotions</h1><p>En d\xE9veloppement...</p></div>`
    }]
  }], null, null);
})();
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && \u0275setClassDebugInfo(PromotionEngineComponent, { className: "PromotionEngineComponent", filePath: "src/app/components/admin/business/promotion-engine/promotion-engine.component.ts", lineNumber: 10 });
})();
export {
  PromotionEngineComponent
};
//# sourceMappingURL=chunk-WUG5RC2A.js.map

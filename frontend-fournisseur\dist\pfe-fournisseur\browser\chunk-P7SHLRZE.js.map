{"version": 3, "sources": ["src/app/components/admin/users/role-assignment/role-assignment.component.ts"], "sourcesContent": ["import { Component } from '@angular/core';\nimport { CommonModule } from '@angular/common';\n\n@Component({\n  selector: 'app-role-assignment',\n  standalone: true,\n  imports: [CommonModule],\n  template: `\n    <div class=\"role-assignment\">\n      <h1>🛡️ Attribution des rôles</h1>\n      <p>Gestion des rôles et permissions - Réservé aux Super Administrateurs</p>\n      \n      <div class=\"coming-soon\">\n        <div class=\"icon\">👑</div>\n        <h2>Gestion avancée des rôles</h2>\n        <p>Fonctionnalités à venir :</p>\n        <ul>\n          <li>Attribution granulaire des permissions</li>\n          <li>Création de rôles personnalisés</li>\n          <li>Audit des changements de permissions</li>\n          <li>Workflow d'approbation</li>\n        </ul>\n      </div>\n    </div>\n  `,\n  styles: [`\n    .role-assignment {\n      max-width: 800px;\n    }\n    \n    h1 {\n      color: #1e293b;\n      margin-bottom: 0.5rem;\n    }\n    \n    .coming-soon {\n      background: white;\n      padding: 3rem;\n      border-radius: 12px;\n      text-align: center;\n      margin-top: 2rem;\n      box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);\n    }\n    \n    .icon {\n      font-size: 4rem;\n      margin-bottom: 1rem;\n    }\n    \n    .coming-soon h2 {\n      color: #1e293b;\n      margin-bottom: 1rem;\n    }\n    \n    .coming-soon ul {\n      text-align: left;\n      max-width: 300px;\n      margin: 1rem auto;\n    }\n    \n    .coming-soon li {\n      margin-bottom: 0.5rem;\n      color: #64748b;\n    }\n  `]\n})\nexport class RoleAssignmentComponent {}\n"], "mappings": ";;;;;;;;;;;;AAkEM,IAAO,0BAAP,MAAO,yBAAuB;;qCAAvB,0BAAuB;EAAA;yEAAvB,0BAAuB,WAAA,CAAA,CAAA,qBAAA,CAAA,GAAA,OAAA,IAAA,MAAA,GAAA,QAAA,CAAA,CAAA,GAAA,iBAAA,GAAA,CAAA,GAAA,aAAA,GAAA,CAAA,GAAA,MAAA,CAAA,GAAA,UAAA,SAAA,iCAAA,IAAA,KAAA;AAAA,QAAA,KAAA,GAAA;AA1DhC,MAAA,yBAAA,GAAA,OAAA,CAAA,EAA6B,GAAA,IAAA;AACvB,MAAA,iBAAA,GAAA,0CAAA;AAAyB,MAAA,uBAAA;AAC7B,MAAA,yBAAA,GAAA,GAAA;AAAG,MAAA,iBAAA,GAAA,+EAAA;AAAoE,MAAA,uBAAA;AAEvE,MAAA,yBAAA,GAAA,OAAA,CAAA,EAAyB,GAAA,OAAA,CAAA;AACL,MAAA,iBAAA,GAAA,WAAA;AAAE,MAAA,uBAAA;AACpB,MAAA,yBAAA,GAAA,IAAA;AAAI,MAAA,iBAAA,GAAA,iCAAA;AAAyB,MAAA,uBAAA;AAC7B,MAAA,yBAAA,IAAA,GAAA;AAAG,MAAA,iBAAA,IAAA,iCAAA;AAAyB,MAAA,uBAAA;AAC5B,MAAA,yBAAA,IAAA,IAAA,EAAI,IAAA,IAAA;AACE,MAAA,iBAAA,IAAA,wCAAA;AAAsC,MAAA,uBAAA;AAC1C,MAAA,yBAAA,IAAA,IAAA;AAAI,MAAA,iBAAA,IAAA,0CAAA;AAA+B,MAAA,uBAAA;AACnC,MAAA,yBAAA,IAAA,IAAA;AAAI,MAAA,iBAAA,IAAA,sCAAA;AAAoC,MAAA,uBAAA;AACxC,MAAA,yBAAA,IAAA,IAAA;AAAI,MAAA,iBAAA,IAAA,wBAAA;AAAsB,MAAA,uBAAA,EAAK,EAC5B,EACD;;oBAhBA,YAAY,GAAA,QAAA,CAAA,wyBAAA,EAAA,CAAA;;;sEA4DX,yBAAuB,CAAA;UA/DnC;uBACW,uBAAqB,YACnB,MAAI,SACP,CAAC,YAAY,GAAC,UACb;;;;;;;;;;;;;;;;;KAiBT,QAAA,CAAA,00BAAA,EAAA,CAAA;;;;6EA0CU,yBAAuB,EAAA,WAAA,2BAAA,UAAA,+EAAA,YAAA,GAAA,CAAA;AAAA,GAAA;", "names": []}
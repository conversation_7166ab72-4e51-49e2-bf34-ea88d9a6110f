{"version": 3, "sources": ["angular:styles/component:css;97ce423777d21896fbd7a372e87d89acd3ca052ab700ab6b1d8face311cdf750;C:/Users/<USER>/Desktop/STE/PFE/PFE/frontend-fournisseur/src/app/components/admin/reports/reports.component.ts"], "sourcesContent": ["\n    .reports-page {\n      padding: 2rem;\n      background: #f8fafc;\n      min-height: 100vh;\n    }\n\n    .page-header {\n      margin-bottom: 2rem;\n    }\n\n    .page-header h1 {\n      font-size: 2rem;\n      font-weight: 700;\n      color: #1e293b;\n      margin: 0 0 0.5rem 0;\n    }\n\n    .page-header p {\n      color: #64748b;\n      margin: 0;\n    }\n\n    .reports-grid {\n      display: grid;\n      grid-template-columns: 300px 1fr;\n      gap: 2rem;\n    }\n\n    .filters-section, .report-types, .charts-section, .metrics-section {\n      background: white;\n      padding: 2rem;\n      border-radius: 16px;\n      box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);\n      margin-bottom: 2rem;\n    }\n\n    .filters-section h2, .report-types h2, .charts-section h2, .metrics-section h2 {\n      font-size: 1.25rem;\n      font-weight: 600;\n      color: #1e293b;\n      margin: 0 0 1.5rem 0;\n    }\n\n    .filter-group {\n      margin-bottom: 1rem;\n    }\n\n    .filter-group label {\n      display: block;\n      font-weight: 500;\n      color: #374151;\n      margin-bottom: 0.5rem;\n    }\n\n    .form-select, .form-input {\n      width: 100%;\n      padding: 0.75rem;\n      border: 1px solid #d1d5db;\n      border-radius: 8px;\n      font-size: 0.875rem;\n    }\n\n    .btn-generate {\n      width: 100%;\n      padding: 1rem;\n      background: linear-gradient(135deg, #667eea, #764ba2);\n      color: white;\n      border: none;\n      border-radius: 12px;\n      font-weight: 600;\n      cursor: pointer;\n      margin-top: 1rem;\n    }\n\n    .report-card {\n      display: flex;\n      align-items: center;\n      padding: 1rem;\n      border: 1px solid #e5e7eb;\n      border-radius: 12px;\n      margin-bottom: 1rem;\n      cursor: pointer;\n      transition: all 0.3s ease;\n    }\n\n    .report-card:hover {\n      border-color: #667eea;\n      transform: translateY(-2px);\n      box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);\n    }\n\n    .report-icon {\n      font-size: 2rem;\n      margin-right: 1rem;\n    }\n\n    .report-info {\n      flex: 1;\n    }\n\n    .report-info h3 {\n      font-size: 1rem;\n      font-weight: 600;\n      color: #1e293b;\n      margin: 0 0 0.25rem 0;\n    }\n\n    .report-info p {\n      color: #64748b;\n      font-size: 0.875rem;\n      margin: 0;\n    }\n\n    .btn-download {\n      background: #f1f5f9;\n      border: 1px solid #e2e8f0;\n      border-radius: 8px;\n      padding: 0.5rem;\n      cursor: pointer;\n    }\n\n    .chart-container {\n      margin-bottom: 2rem;\n    }\n\n    .chart-container h3 {\n      font-size: 1.125rem;\n      font-weight: 600;\n      color: #1e293b;\n      margin-bottom: 1rem;\n    }\n\n    .chart-placeholder {\n      height: 200px;\n      background: #f8fafc;\n      border-radius: 8px;\n      display: flex;\n      align-items: end;\n      justify-content: space-around;\n      padding: 1rem;\n      position: relative;\n    }\n\n    .chart-bar {\n      width: 40px;\n      background: linear-gradient(135deg, #667eea, #764ba2);\n      border-radius: 4px 4px 0 0;\n      position: relative;\n      min-height: 20px;\n    }\n\n    .chart-value {\n      position: absolute;\n      top: -25px;\n      left: 50%;\n      transform: translateX(-50%);\n      font-size: 0.75rem;\n      font-weight: 600;\n      color: #374151;\n    }\n\n    .chart-line {\n      width: 100%;\n      height: 100%;\n      position: relative;\n    }\n\n    .line-point {\n      position: absolute;\n      width: 8px;\n      height: 8px;\n      background: #667eea;\n      border-radius: 50%;\n      transform: translate(-50%, 50%);\n    }\n\n    .metrics-grid {\n      display: grid;\n      grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));\n      gap: 1rem;\n    }\n\n    .metric-card {\n      display: flex;\n      align-items: center;\n      padding: 1.5rem;\n      background: #f8fafc;\n      border-radius: 12px;\n      gap: 1rem;\n    }\n\n    .metric-icon {\n      font-size: 2rem;\n    }\n\n    .metric-value {\n      font-size: 1.5rem;\n      font-weight: 700;\n      color: #1e293b;\n    }\n\n    .metric-label {\n      color: #64748b;\n      font-size: 0.875rem;\n    }\n\n    .metric-change {\n      font-size: 0.75rem;\n      font-weight: 600;\n      padding: 0.25rem 0.5rem;\n      border-radius: 12px;\n    }\n\n    .metric-change.positive {\n      background: #dcfce7;\n      color: #166534;\n    }\n\n    .metric-change.negative {\n      background: #fecaca;\n      color: #991b1b;\n    }\n\n    @media (max-width: 768px) {\n      .reports-page {\n        padding: 1rem;\n      }\n      \n      .reports-grid {\n        grid-template-columns: 1fr;\n      }\n      \n      .metrics-grid {\n        grid-template-columns: 1fr;\n      }\n    }\n  "], "mappings": ";AACI,CAAC;AACC,WAAS;AACT,cAAY;AACZ,cAAY;AACd;AAEA,CAAC;AACC,iBAAe;AACjB;AAEA,CAJC,YAIY;AACX,aAAW;AACX,eAAa;AACb,SAAO;AACP,UAAQ,EAAE,EAAE,OAAO;AACrB;AAEA,CAXC,YAWY;AACX,SAAO;AACP,UAAQ;AACV;AAEA,CAAC;AACC,WAAS;AACT,yBAAuB,MAAM;AAC7B,OAAK;AACP;AAEA,CAAC;AAAiB,CAAC;AAAc,CAAC;AAAgB,CAAC;AACjD,cAAY;AACZ,WAAS;AACT,iBAAe;AACf,cAAY,EAAE,IAAI,IAAI,KAAK,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE;AACpC,iBAAe;AACjB;AAEA,CARC,gBAQgB;AAAI,CARF,aAQgB;AAAI,CARL,eAQqB;AAAI,CARR,gBAQyB;AAC1E,aAAW;AACX,eAAa;AACb,SAAO;AACP,UAAQ,EAAE,EAAE,OAAO;AACrB;AAEA,CAAC;AACC,iBAAe;AACjB;AAEA,CAJC,aAIa;AACZ,WAAS;AACT,eAAa;AACb,SAAO;AACP,iBAAe;AACjB;AAEA,CAAC;AAAa,CAAC;AACb,SAAO;AACP,WAAS;AACT,UAAQ,IAAI,MAAM;AAClB,iBAAe;AACf,aAAW;AACb;AAEA,CAAC;AACC,SAAO;AACP,WAAS;AACT;AAAA,IAAY;AAAA,MAAgB,MAAhB;AAAA,MAAwB,OAAxB;AAAA,MAAiC;AAC7C,SAAO;AACP,UAAQ;AACR,iBAAe;AACf,eAAa;AACb,UAAQ;AACR,cAAY;AACd;AAEA,CAAC;AACC,WAAS;AACT,eAAa;AACb,WAAS;AACT,UAAQ,IAAI,MAAM;AAClB,iBAAe;AACf,iBAAe;AACf,UAAQ;AACR,cAAY,IAAI,KAAK;AACvB;AAEA,CAXC,WAWW;AACV,gBAAc;AACd,aAAW,WAAW;AACtB,cAAY,EAAE,IAAI,KAAK,KAAK,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE;AACvC;AAEA,CAAC;AACC,aAAW;AACX,gBAAc;AAChB;AAEA,CAAC;AACC,QAAM;AACR;AAEA,CAJC,YAIY;AACX,aAAW;AACX,eAAa;AACb,SAAO;AACP,UAAQ,EAAE,EAAE,QAAQ;AACtB;AAEA,CAXC,YAWY;AACX,SAAO;AACP,aAAW;AACX,UAAQ;AACV;AAEA,CAAC;AACC,cAAY;AACZ,UAAQ,IAAI,MAAM;AAClB,iBAAe;AACf,WAAS;AACT,UAAQ;AACV;AAEA,CAAC;AACC,iBAAe;AACjB;AAEA,CAJC,gBAIgB;AACf,aAAW;AACX,eAAa;AACb,SAAO;AACP,iBAAe;AACjB;AAEA,CAAC;AACC,UAAQ;AACR,cAAY;AACZ,iBAAe;AACf,WAAS;AACT,eAAa;AACb,mBAAiB;AACjB,WAAS;AACT,YAAU;AACZ;AAEA,CAAC;AACC,SAAO;AACP;AAAA,IAAY;AAAA,MAAgB,MAAhB;AAAA,MAAwB,OAAxB;AAAA,MAAiC;AAC7C,iBAAe,IAAI,IAAI,EAAE;AACzB,YAAU;AACV,cAAY;AACd;AAEA,CAAC;AACC,YAAU;AACV,OAAK;AACL,QAAM;AACN,aAAW,WAAW;AACtB,aAAW;AACX,eAAa;AACb,SAAO;AACT;AAEA,CAAC;AACC,SAAO;AACP,UAAQ;AACR,YAAU;AACZ;AAEA,CAAC;AACC,YAAU;AACV,SAAO;AACP,UAAQ;AACR,cAAY;AACZ,iBAAe;AACf,aAAW,UAAU,IAAI,EAAE;AAC7B;AAEA,CAAC;AACC,WAAS;AACT,yBAAuB,OAAO,QAAQ,EAAE,OAAO,KAAK,EAAE;AACtD,OAAK;AACP;AAEA,CAAC;AACC,WAAS;AACT,eAAa;AACb,WAAS;AACT,cAAY;AACZ,iBAAe;AACf,OAAK;AACP;AAEA,CAAC;AACC,aAAW;AACb;AAEA,CAAC;AACC,aAAW;AACX,eAAa;AACb,SAAO;AACT;AAEA,CAAC;AACC,SAAO;AACP,aAAW;AACb;AAEA,CAAC;AACC,aAAW;AACX,eAAa;AACb,WAAS,QAAQ;AACjB,iBAAe;AACjB;AAEA,CAPC,aAOa,CAAC;AACb,cAAY;AACZ,SAAO;AACT;AAEA,CAZC,aAYa,CAAC;AACb,cAAY;AACZ,SAAO;AACT;AAEA,OAAO,CAAC,SAAS,EAAE;AACjB,GAhOD;AAiOG,aAAS;AACX;AAEA,GA9MD;AA+MG,2BAAuB;AACzB;AAEA,GAxDD;AAyDG,2BAAuB;AACzB;AACF;", "names": []}
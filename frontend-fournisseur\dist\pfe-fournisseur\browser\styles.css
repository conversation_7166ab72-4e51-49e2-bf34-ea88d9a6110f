/* src/styles/variables.css */
:root {
  --primary-50: #eff6ff;
  --primary-100: #dbeafe;
  --primary-200: #bfdbfe;
  --primary-300: #93c5fd;
  --primary-400: #60a5fa;
  --primary-500: #3b82f6;
  --primary-600: #2563eb;
  --primary-700: #1d4ed8;
  --primary-800: #1e40af;
  --primary-900: #1e3a8a;
  --primary-blue: var(--primary-600);
  --primary-blue-light: var(--primary-500);
  --primary-blue-dark: var(--primary-800);
  --secondary-blue: #0ea5e9;
  --secondary-blue-light: #38bdf8;
  --accent-blue: #06b6d4;
  --accent-blue-light: #67e8f9;
  --gradient-primary:
    linear-gradient(
      135deg,
      #667eea 0%,
      #764ba2 100%);
  --gradient-secondary:
    linear-gradient(
      135deg,
      #0ea5e9 0%,
      #38bdf8 50%,
      #06b6d4 100%);
  --gradient-accent:
    linear-gradient(
      135deg,
      #06b6d4 0%,
      #67e8f9 50%,
      #a5f3fc 100%);
  --gradient-dark:
    linear-gradient(
      135deg,
      #1e3a8a 0%,
      #1e40af 50%,
      #2563eb 100%);
  --gradient-light:
    linear-gradient(
      135deg,
      #dbeafe 0%,
      #bfdbfe 50%,
      #93c5fd 100%);
  --white: #ffffff;
  --gray-50: #f8fafc;
  --gray-100: #f1f5f9;
  --gray-200: #e2e8f0;
  --gray-300: #cbd5e1;
  --gray-400: #94a3b8;
  --gray-500: #64748b;
  --gray-600: #475569;
  --gray-700: #334155;
  --gray-800: #1e293b;
  --gray-900: #0f172a;
  --success-50: #f0fdf4;
  --success-100: #dcfce7;
  --success-200: #bbf7d0;
  --success-300: #86efac;
  --success-400: #4ade80;
  --success-500: #22c55e;
  --success-600: #16a34a;
  --success-700: #15803d;
  --success-800: #166534;
  --success-900: #14532d;
  --warning-50: #fffbeb;
  --warning-100: #fef3c7;
  --warning-200: #fde68a;
  --warning-300: #fcd34d;
  --warning-400: #fbbf24;
  --warning-500: #f59e0b;
  --warning-600: #d97706;
  --warning-700: #b45309;
  --warning-800: #92400e;
  --warning-900: #78350f;
  --error-50: #fef2f2;
  --error-100: #fee2e2;
  --error-200: #fecaca;
  --error-300: #fca5a5;
  --error-400: #f87171;
  --error-500: #ef4444;
  --error-600: #dc2626;
  --error-700: #b91c1c;
  --error-800: #991b1b;
  --error-900: #7f1d1d;
  --success: var(--success-500);
  --success-light: var(--success-400);
  --warning: var(--warning-500);
  --warning-light: var(--warning-400);
  --error: var(--error-500);
  --error-light: var(--error-400);
  --info: var(--secondary-blue);
  --info-light: var(--secondary-blue-light);
  --spacing-0: 0;
  --spacing-1: 0.25rem;
  --spacing-2: 0.5rem;
  --spacing-3: 0.75rem;
  --spacing-4: 1rem;
  --spacing-5: 1.25rem;
  --spacing-6: 1.5rem;
  --spacing-7: 1.75rem;
  --spacing-8: 2rem;
  --spacing-9: 2.25rem;
  --spacing-10: 2.5rem;
  --spacing-11: 2.75rem;
  --spacing-12: 3rem;
  --spacing-14: 3.5rem;
  --spacing-16: 4rem;
  --spacing-20: 5rem;
  --spacing-24: 6rem;
  --spacing-32: 8rem;
  --spacing-xs: var(--spacing-1);
  --spacing-sm: var(--spacing-2);
  --spacing-md: var(--spacing-4);
  --spacing-lg: var(--spacing-6);
  --spacing-xl: var(--spacing-8);
  --spacing-2xl: var(--spacing-12);
  --spacing-3xl: var(--spacing-16);
  --border-radius-none: 0;
  --border-radius-sm: 0.125rem;
  --border-radius-md: 0.375rem;
  --border-radius-lg: 0.5rem;
  --border-radius-xl: 0.75rem;
  --border-radius-2xl: 1rem;
  --border-radius-3xl: 1.5rem;
  --border-radius-full: 9999px;
  --radius-sm: var(--border-radius-md);
  --radius-md: var(--border-radius-lg);
  --radius-lg: var(--border-radius-xl);
  --radius-xl: var(--border-radius-2xl);
  --radius-2xl: var(--border-radius-3xl);
  --radius-full: var(--border-radius-full);
  --shadow-none: none;
  --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  --shadow-base: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
  --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
  --shadow-2xl: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
  --shadow-inner: inset 0 2px 4px 0 rgba(0, 0, 0, 0.06);
  --shadow-blue: 0 10px 25px -5px rgba(59, 130, 246, 0.3);
  --shadow-blue-lg: 0 20px 40px -10px rgba(59, 130, 246, 0.4);
  --shadow-blue-xl: 0 25px 50px -12px rgba(59, 130, 246, 0.5);
  --shadow-success: 0 10px 25px -5px rgba(34, 197, 94, 0.3);
  --shadow-warning: 0 10px 25px -5px rgba(245, 158, 11, 0.3);
  --shadow-error: 0 10px 25px -5px rgba(239, 68, 68, 0.3);
  --font-family-sans:
    "Inter",
    "Segoe UI",
    "Roboto",
    "Helvetica Neue",
    Arial,
    sans-serif;
  --font-family-mono:
    "JetBrains Mono",
    "Fira Code",
    "Monaco",
    "Consolas",
    monospace;
  --font-family: var(--font-family-sans);
  --font-size-xs: 0.75rem;
  --font-size-sm: 0.875rem;
  --font-size-base: 1rem;
  --font-size-lg: 1.125rem;
  --font-size-xl: 1.25rem;
  --font-size-2xl: 1.5rem;
  --font-size-3xl: 1.875rem;
  --font-size-4xl: 2.25rem;
  --font-size-5xl: 3rem;
  --font-size-6xl: 3.75rem;
  --font-size-7xl: 4.5rem;
  --font-size-8xl: 6rem;
  --font-size-9xl: 8rem;
  --font-weight-thin: 100;
  --font-weight-extralight: 200;
  --font-weight-light: 300;
  --font-weight-normal: 400;
  --font-weight-medium: 500;
  --font-weight-semibold: 600;
  --font-weight-bold: 700;
  --font-weight-extrabold: 800;
  --font-weight-black: 900;
  --line-height-none: 1;
  --line-height-tight: 1.25;
  --line-height-snug: 1.375;
  --line-height-normal: 1.5;
  --line-height-relaxed: 1.625;
  --line-height-loose: 2;
  --z-auto: auto;
  --z-0: 0;
  --z-10: 10;
  --z-20: 20;
  --z-30: 30;
  --z-40: 40;
  --z-50: 50;
  --z-dropdown: 1000;
  --z-sticky: 1020;
  --z-fixed: 1030;
  --z-modal-backdrop: 1040;
  --z-modal: 1050;
  --z-popover: 1060;
  --z-tooltip: 1070;
  --z-toast: 1080;
  --transition-none: none;
  --transition-all: all 150ms cubic-bezier(0.4, 0, 0.2, 1);
  --transition-fast: all 150ms ease-in-out;
  --transition-base: all 300ms ease-in-out;
  --transition-slow: all 500ms ease-in-out;
  --transition-bounce: all 300ms cubic-bezier(0.68, -0.55, 0.265, 1.55);
  --transition-normal: var(--transition-base);
  --glass-bg: rgba(255, 255, 255, 0.1);
  --glass-border: rgba(255, 255, 255, 0.2);
  --glass-backdrop: blur(20px);
  --breakpoint-sm: 640px;
  --breakpoint-md: 768px;
  --breakpoint-lg: 1024px;
  --breakpoint-xl: 1280px;
  --breakpoint-2xl: 1536px;
}
.gradient-primary {
  background: var(--gradient-primary);
}
.gradient-secondary {
  background: var(--gradient-secondary);
}
.gradient-accent {
  background: var(--gradient-accent);
}
.gradient-dark {
  background: var(--gradient-dark);
}
.gradient-light {
  background: var(--gradient-light);
}
.text-gradient-primary {
  background: var(--gradient-primary);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}
.text-gradient-secondary {
  background: var(--gradient-secondary);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}
.shadow-blue {
  box-shadow: var(--shadow-blue);
}
.shadow-blue-lg {
  box-shadow: var(--shadow-blue-lg);
}
.glass {
  background: var(--glass-bg);
  -webkit-backdrop-filter: var(--glass-backdrop);
  backdrop-filter: var(--glass-backdrop);
  border: 1px solid var(--glass-border);
}
.transition-fast {
  transition: all var(--transition-fast);
}
.transition-normal {
  transition: all var(--transition-normal);
}
.transition-slow {
  transition: all var(--transition-slow);
}
.transition-bounce {
  transition: all var(--transition-bounce);
}
.btn-gradient {
  background: var(--gradient-primary);
  color: var(--white);
  border: none;
  border-radius: var(--radius-lg);
  padding: var(--spacing-md) var(--spacing-xl);
  font-weight: 600;
  cursor: pointer;
  transition: all var(--transition-normal);
  box-shadow: var(--shadow-blue);
}
.btn-gradient:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-blue-lg);
}
.btn-gradient:active {
  transform: translateY(0);
}
.btn-gradient:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
}
.card-modern {
  background: var(--white);
  border-radius: var(--radius-2xl);
  box-shadow: var(--shadow-lg);
  border: 1px solid var(--gray-200);
  transition: all var(--transition-normal);
}
.card-modern:hover {
  transform: translateY(-4px);
  box-shadow: var(--shadow-xl);
}
.card-glass {
  background: var(--glass-bg);
  -webkit-backdrop-filter: var(--glass-backdrop);
  backdrop-filter: var(--glass-backdrop);
  border: 1px solid var(--glass-border);
  border-radius: var(--radius-2xl);
  transition: all var(--transition-normal);
}
@media (max-width: 640px) {
  :root {
    --spacing-xl: 1.5rem;
    --spacing-2xl: 2rem;
    --spacing-3xl: 2.5rem;
  }
}

/* src/styles/components.css */
.card {
  background: var(--white);
  border: 1px solid var(--gray-200);
  border-radius: var(--border-radius-xl);
  box-shadow: var(--shadow-base);
  overflow: hidden;
  transition: all var(--transition-base);
  position: relative;
}
.card:hover {
  box-shadow: var(--shadow-lg);
  transform: translateY(-2px);
}
.card::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background:
    linear-gradient(
      90deg,
      var(--primary-500),
      var(--primary-400),
      var(--primary-600));
}
.card-header {
  padding: var(--spacing-6);
  background:
    linear-gradient(
      135deg,
      var(--gray-50) 0%,
      var(--white) 100%);
  border-bottom: 1px solid var(--gray-200);
}
.card-body {
  padding: var(--spacing-6);
}
.card-footer {
  padding: var(--spacing-6);
  background: var(--gray-50);
  border-top: 1px solid var(--gray-200);
}
.form-group {
  margin-bottom: var(--spacing-6);
  position: relative;
}
.form-label {
  display: block;
  margin-bottom: var(--spacing-2);
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
  color: var(--gray-700);
  transition: var(--transition-fast);
}
.form-control {
  display: block;
  width: 100%;
  padding: var(--spacing-4) var(--spacing-4);
  font-size: var(--font-size-base);
  font-weight: var(--font-weight-normal);
  line-height: var(--line-height-normal);
  color: var(--gray-900);
  background: var(--white);
  border: 2px solid var(--gray-300);
  border-radius: var(--border-radius-xl);
  transition: all var(--transition-base);
  box-shadow: var(--shadow-sm);
}
.form-control:focus {
  outline: none;
  border-color: var(--primary-500);
  box-shadow: 0 0 0 4px rgba(59, 130, 246, 0.15);
  transform: translateY(-2px);
  background: var(--white);
}
.form-control:focus + .form-label {
  color: var(--primary-600);
}
.form-control::placeholder {
  color: var(--gray-400);
  font-style: italic;
  font-weight: var(--font-weight-normal);
}
.form-control:disabled {
  background: var(--gray-100);
  color: var(--gray-500);
  border-color: var(--gray-200);
  cursor: not-allowed;
  box-shadow: none;
}
.form-control.is-invalid {
  border-color: var(--error-500);
  box-shadow: 0 0 0 4px rgba(239, 68, 68, 0.15);
  background: var(--error-50);
}
.form-control.is-valid {
  border-color: var(--success-500);
  box-shadow: 0 0 0 4px rgba(34, 197, 94, 0.15);
  background: var(--success-50);
}
.form-select {
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='m6 8 4 4 4-4'/%3e%3c/svg%3e");
  background-position: right var(--spacing-3) center;
  background-repeat: no-repeat;
  background-size: 16px 12px;
  padding-right: var(--spacing-10);
  appearance: none;
}
.form-select:focus {
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%233b82f6' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='m6 8 4 4 4-4'/%3e%3c/svg%3e");
}
.form-textarea {
  min-height: 120px;
  resize: vertical;
}
.form-text {
  margin-top: var(--spacing-1);
  font-size: var(--font-size-sm);
  color: var(--gray-600);
}
.form-error {
  margin-top: var(--spacing-1);
  font-size: var(--font-size-sm);
  color: var(--error-600);
  font-weight: var(--font-weight-medium);
}
.form-success {
  margin-top: var(--spacing-1);
  font-size: var(--font-size-sm);
  color: var(--success-600);
  font-weight: var(--font-weight-medium);
}
.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: var(--spacing-2);
  padding: var(--spacing-3) var(--spacing-6);
  font-size: var(--font-size-base);
  font-weight: var(--font-weight-semibold);
  line-height: var(--line-height-none);
  text-decoration: none;
  border: 2px solid transparent;
  border-radius: var(--border-radius-xl);
  cursor: pointer;
  transition: all var(--transition-base);
  position: relative;
  overflow: hidden;
  -webkit-user-select: none;
  user-select: none;
}
.btn::before {
  content: "";
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background:
    linear-gradient(
      90deg,
      transparent,
      rgba(255, 255, 255, 0.2),
      transparent);
  transition: var(--transition-slow);
}
.btn:hover::before {
  left: 100%;
}
.btn:focus {
  outline: 2px solid var(--primary-500);
  outline-offset: 2px;
}
.btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
}
.btn:disabled:hover {
  transform: none;
  box-shadow: none;
}
.btn-primary {
  background: var(--gradient-primary);
  color: var(--white);
  border-color: transparent;
  box-shadow: var(--shadow-blue);
}
.btn-primary:hover {
  background:
    linear-gradient(
      135deg,
      #5a67d8 0%,
      #667eea 100%);
  transform: translateY(-2px);
  box-shadow: var(--shadow-blue-lg);
}
.btn-secondary {
  background: var(--white);
  color: var(--gray-700);
  border-color: var(--gray-300);
  box-shadow: var(--shadow-sm);
}
.btn-secondary:hover {
  background: var(--gray-50);
  border-color: var(--gray-400);
  transform: translateY(-1px);
  box-shadow: var(--shadow-md);
}
.btn-success {
  background:
    linear-gradient(
      135deg,
      var(--success-600),
      var(--success-500));
  color: var(--white);
  border-color: transparent;
  box-shadow: var(--shadow-md);
}
.btn-success:hover {
  background:
    linear-gradient(
      135deg,
      var(--success-700),
      var(--success-600));
  transform: translateY(-2px);
  box-shadow: var(--shadow-success);
}
.btn-warning {
  background:
    linear-gradient(
      135deg,
      var(--warning-600),
      var(--warning-500));
  color: var(--white);
  border-color: transparent;
  box-shadow: var(--shadow-md);
}
.btn-warning:hover {
  background:
    linear-gradient(
      135deg,
      var(--warning-700),
      var(--warning-600));
  transform: translateY(-2px);
  box-shadow: var(--shadow-warning);
}
.btn-danger {
  background:
    linear-gradient(
      135deg,
      var(--error-600),
      var(--error-500));
  color: var(--white);
  border-color: transparent;
  box-shadow: var(--shadow-md);
}
.btn-danger:hover {
  background:
    linear-gradient(
      135deg,
      var(--error-700),
      var(--error-600));
  transform: translateY(-2px);
  box-shadow: var(--shadow-error);
}
.btn-sm {
  padding: var(--spacing-2) var(--spacing-4);
  font-size: var(--font-size-sm);
}
.btn-lg {
  padding: var(--spacing-4) var(--spacing-8);
  font-size: var(--font-size-lg);
}
.btn-xl {
  padding: var(--spacing-5) var(--spacing-10);
  font-size: var(--font-size-xl);
}
.btn-outline-primary {
  background: transparent;
  color: var(--primary-600);
  border-color: var(--primary-600);
}
.btn-outline-primary:hover {
  background: var(--primary-600);
  color: var(--white);
  transform: translateY(-1px);
}
.btn-outline-secondary {
  background: transparent;
  color: var(--gray-600);
  border-color: var(--gray-400);
}
.btn-outline-secondary:hover {
  background: var(--gray-600);
  color: var(--white);
  transform: translateY(-1px);
}
.btn-block {
  width: 100%;
}
.form-group-icon {
  position: relative;
}
.form-group-icon .form-control {
  padding-left: var(--spacing-12);
}
.form-group-icon .form-icon {
  position: absolute;
  left: var(--spacing-4);
  top: 50%;
  transform: translateY(-50%);
  color: var(--gray-400);
  font-size: var(--font-size-lg);
  pointer-events: none;
  transition: var(--transition-fast);
}
.form-group-icon .form-control:focus ~ .form-icon {
  color: var(--primary-500);
}
.badge {
  display: inline-flex;
  align-items: center;
  gap: var(--spacing-1);
  padding: var(--spacing-1) var(--spacing-3);
  font-size: var(--font-size-xs);
  font-weight: var(--font-weight-medium);
  line-height: 1;
  border-radius: var(--border-radius-full);
  text-transform: uppercase;
  letter-spacing: 0.025em;
}
.badge-success {
  background: var(--success-100);
  color: var(--success-800);
  border: 1px solid var(--success-200);
}
.badge-warning {
  background: var(--warning-100);
  color: var(--warning-800);
  border: 1px solid var(--warning-200);
}
.badge-error {
  background: var(--error-100);
  color: var(--error-800);
  border: 1px solid var(--error-200);
}
.badge-primary {
  background: var(--primary-100);
  color: var(--primary-800);
  border: 1px solid var(--primary-200);
}
.badge-secondary {
  background: var(--gray-100);
  color: var(--gray-800);
  border: 1px solid var(--gray-200);
}
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.6);
  -webkit-backdrop-filter: blur(4px);
  backdrop-filter: blur(4px);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: var(--z-modal);
  animation: fadeIn 0.2s ease-out;
}
@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}
.modal-content {
  background: var(--white);
  border-radius: var(--border-radius-2xl);
  box-shadow: var(--shadow-2xl);
  max-width: 90vw;
  max-height: 90vh;
  overflow-y: auto;
  position: relative;
  animation: slideUp 0.3s ease-out;
}
@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(20px) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}
.modal-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: var(--spacing-6);
  border-bottom: 1px solid var(--gray-200);
  background:
    linear-gradient(
      135deg,
      var(--gray-50) 0%,
      var(--white) 100%);
}
.modal-title {
  font-size: var(--font-size-xl);
  font-weight: var(--font-weight-bold);
  color: var(--gray-900);
  margin: 0;
}
.modal-close {
  background: none;
  border: none;
  font-size: var(--font-size-xl);
  color: var(--gray-400);
  cursor: pointer;
  padding: var(--spacing-2);
  border-radius: var(--border-radius-lg);
  transition: var(--transition-fast);
}
.modal-close:hover {
  background: var(--gray-100);
  color: var(--gray-600);
}
.modal-body {
  padding: var(--spacing-6);
}
.modal-footer {
  display: flex;
  gap: var(--spacing-3);
  justify-content: flex-end;
  padding: var(--spacing-6);
  border-top: 1px solid var(--gray-200);
  background: var(--gray-50);
}
.grid {
  display: grid;
  gap: var(--spacing-6);
}
.grid-cols-1 {
  grid-template-columns: repeat(1, 1fr);
}
.grid-cols-2 {
  grid-template-columns: repeat(2, 1fr);
}
.grid-cols-3 {
  grid-template-columns: repeat(3, 1fr);
}
.grid-cols-4 {
  grid-template-columns: repeat(4, 1fr);
}
.grid-auto-fit {
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
}
.grid-auto-fill {
  grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
}
.nav-tabs {
  display: flex;
  border-bottom: 2px solid var(--gray-200);
  margin-bottom: var(--spacing-6);
  overflow-x: auto;
}
.nav-tab {
  padding: var(--spacing-4) var(--spacing-6);
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
  color: var(--gray-600);
  background: none;
  border: none;
  border-bottom: 3px solid transparent;
  cursor: pointer;
  transition: all var(--transition-fast);
  white-space: nowrap;
  position: relative;
}
.nav-tab:hover {
  color: var(--primary-600);
  background: var(--primary-50);
}
.nav-tab.active {
  color: var(--primary-600);
  border-bottom-color: var(--primary-600);
  background: var(--primary-50);
}
.nav-tab.active::after {
  content: "";
  position: absolute;
  bottom: -2px;
  left: 0;
  right: 0;
  height: 2px;
  background: var(--primary-600);
}
.alert {
  padding: var(--spacing-4) var(--spacing-6);
  border-radius: var(--border-radius-lg);
  border-left: 4px solid;
  margin-bottom: var(--spacing-4);
  display: flex;
  align-items: flex-start;
  gap: var(--spacing-3);
}
.alert-success {
  background: var(--success-50);
  border-left-color: var(--success-500);
  color: var(--success-800);
}
.alert-warning {
  background: var(--warning-50);
  border-left-color: var(--warning-500);
  color: var(--warning-800);
}
.alert-error {
  background: var(--error-50);
  border-left-color: var(--error-500);
  color: var(--error-800);
}
.alert-info {
  background: var(--primary-50);
  border-left-color: var(--primary-500);
  color: var(--primary-800);
}
.loading {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: var(--spacing-8);
  color: var(--gray-500);
}
.spinner {
  width: 32px;
  height: 32px;
  border: 3px solid var(--gray-200);
  border-top: 3px solid var(--primary-500);
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-right: var(--spacing-3);
}
@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}
@media (max-width: 768px) {
  .grid-cols-2 {
    grid-template-columns: 1fr;
  }
  .grid-cols-3 {
    grid-template-columns: 1fr;
  }
  .grid-cols-4 {
    grid-template-columns: repeat(2, 1fr);
  }
  .modal-content {
    margin: var(--spacing-4);
    max-width: calc(100vw - 2rem);
  }
  .nav-tabs {
    flex-wrap: wrap;
  }
}

/* src/styles.css */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}
html,
body {
  height: 100%;
  font-family: var(--font-family-sans);
  background-color: var(--gray-50);
  color: var(--gray-900);
  line-height: var(--line-height-normal);
}
a {
  color: var(--primary-600);
  text-decoration: none;
  transition: var(--transition-fast);
}
a:hover {
  color: var(--primary-700);
  text-decoration: underline;
}
h1,
h2,
h3,
h4,
h5,
h6 {
  font-weight: var(--font-weight-semibold);
  line-height: var(--line-height-tight);
  margin-bottom: var(--spacing-4);
  color: var(--gray-900);
}
h1 {
  font-size: var(--font-size-3xl);
}
h2 {
  font-size: var(--font-size-2xl);
}
h3 {
  font-size: var(--font-size-xl);
}
h4 {
  font-size: var(--font-size-lg);
}
h5 {
  font-size: var(--font-size-base);
}
h6 {
  font-size: var(--font-size-sm);
}
p {
  margin-bottom: var(--spacing-4);
  color: var(--gray-700);
}
ul,
ol {
  margin-bottom: var(--spacing-4);
  padding-left: var(--spacing-6);
}
li {
  margin-bottom: var(--spacing-1);
}
img {
  max-width: 100%;
  height: auto;
}
table {
  width: 100%;
  border-collapse: collapse;
  margin-bottom: var(--spacing-6);
}
th,
td {
  padding: var(--spacing-3);
  text-align: left;
  border-bottom: 1px solid var(--gray-200);
}
th {
  background-color: var(--gray-100);
  font-weight: var(--font-weight-semibold);
  color: var(--gray-900);
}
input,
textarea,
select {
  width: 100%;
  padding: var(--spacing-3);
  border: 1px solid var(--gray-300);
  border-radius: var(--border-radius-md);
  font-size: var(--font-size-base);
  transition: var(--transition-fast);
}
input:focus,
textarea:focus,
select:focus {
  outline: none;
  border-color: var(--primary-500);
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}
button {
  cursor: pointer;
  border: none;
  border-radius: var(--border-radius-md);
  padding: var(--spacing-3) var(--spacing-6);
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
  transition: var(--transition-fast);
}
button:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}
.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 var(--spacing-4);
}
.text-center {
  text-align: center;
}
.text-left {
  text-align: left;
}
.text-right {
  text-align: right;
}
.mb-0 {
  margin-bottom: 0;
}
.mb-1 {
  margin-bottom: var(--spacing-1);
}
.mb-2 {
  margin-bottom: var(--spacing-2);
}
.mb-3 {
  margin-bottom: var(--spacing-3);
}
.mb-4 {
  margin-bottom: var(--spacing-4);
}
.mb-6 {
  margin-bottom: var(--spacing-6);
}
.mb-8 {
  margin-bottom: var(--spacing-8);
}
.mt-0 {
  margin-top: 0;
}
.mt-1 {
  margin-top: var(--spacing-1);
}
.mt-2 {
  margin-top: var(--spacing-2);
}
.mt-3 {
  margin-top: var(--spacing-3);
}
.mt-4 {
  margin-top: var(--spacing-4);
}
.mt-6 {
  margin-top: var(--spacing-6);
}
.mt-8 {
  margin-top: var(--spacing-8);
}
.p-0 {
  padding: 0;
}
.p-1 {
  padding: var(--spacing-1);
}
.p-2 {
  padding: var(--spacing-2);
}
.p-3 {
  padding: var(--spacing-3);
}
.p-4 {
  padding: var(--spacing-4);
}
.p-6 {
  padding: var(--spacing-6);
}
.p-8 {
  padding: var(--spacing-8);
}
@media (max-width: 768px) {
  .container {
    padding: 0 var(--spacing-2);
  }
  h1 {
    font-size: var(--font-size-2xl);
  }
  h2 {
    font-size: var(--font-size-xl);
  }
  h3 {
    font-size: var(--font-size-lg);
  }
}
::-webkit-scrollbar {
  width: 8px;
}
::-webkit-scrollbar-track {
  background: var(--gray-100);
}
::-webkit-scrollbar-thumb {
  background: var(--gray-400);
  border-radius: 4px;
}
::-webkit-scrollbar-thumb:hover {
  background: var(--gray-500);
}
@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}
@keyframes slideIn {
  from {
    transform: translateY(-10px);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}
.fade-in {
  animation: fadeIn 0.3s ease-in-out;
}
.slide-in {
  animation: slideIn 0.3s ease-in-out;
}
*:focus-visible {
  outline: 2px solid var(--primary-500);
  outline-offset: 2px;
}

/* angular:styles/global:styles */
/*# sourceMappingURL=styles.css.map */

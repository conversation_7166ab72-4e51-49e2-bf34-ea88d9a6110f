{"version": 3, "sources": ["src/app/components/orders/orders.component.css"], "sourcesContent": [".orders-container {\n  padding: 20px;\n  max-width: 1200px;\n  margin: 0 auto;\n}\n\n/* Header */\n.orders-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 30px;\n  padding-bottom: 20px;\n  border-bottom: 2px solid #e9ecef;\n}\n\n.orders-header h1 {\n  color: #2c3e50;\n  margin: 0;\n  font-size: 2rem;\n}\n\n.header-actions {\n  display: flex;\n  gap: 10px;\n}\n\n/* Filters */\n.filters-section {\n  background: #f8f9fa;\n  padding: 20px;\n  border-radius: 8px;\n  margin-bottom: 20px;\n}\n\n.filters-row {\n  display: flex;\n  gap: 20px;\n  align-items: end;\n  flex-wrap: wrap;\n}\n\n.filter-group {\n  display: flex;\n  flex-direction: column;\n  gap: 5px;\n}\n\n.filter-group label {\n  font-weight: 600;\n  color: #495057;\n  font-size: 0.9rem;\n}\n\n.form-control {\n  padding: 8px 12px;\n  border: 1px solid #ced4da;\n  border-radius: 4px;\n  font-size: 0.9rem;\n  min-width: 200px;\n}\n\n.form-control:focus {\n  outline: none;\n  border-color: #007bff;\n  box-shadow: 0 0 0 2px rgba(0, 123, 255, 0.25);\n}\n\n/* Loading */\n.loading-container {\n  text-align: center;\n  padding: 40px;\n}\n\n.spinner {\n  border: 4px solid #f3f3f3;\n  border-top: 4px solid #007bff;\n  border-radius: 50%;\n  width: 40px;\n  height: 40px;\n  animation: spin 1s linear infinite;\n  margin: 0 auto 20px;\n}\n\n@keyframes spin {\n  0% { transform: rotate(0deg); }\n  100% { transform: rotate(360deg); }\n}\n\n/* Orders List */\n.orders-list {\n  display: flex;\n  flex-direction: column;\n  gap: 15px;\n}\n\n.no-orders {\n  text-align: center;\n  padding: 40px;\n  color: #6c757d;\n}\n\n.no-orders p {\n  font-size: 1.2rem;\n  margin-bottom: 20px;\n}\n\n/* Order Card */\n.order-card {\n  background: white;\n  border: 1px solid #dee2e6;\n  border-radius: 8px;\n  overflow: hidden;\n  box-shadow: 0 2px 4px rgba(0,0,0,0.1);\n  transition: box-shadow 0.2s ease;\n}\n\n.order-card:hover {\n  box-shadow: 0 4px 8px rgba(0,0,0,0.15);\n}\n\n.order-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  padding: 20px;\n  cursor: pointer;\n  background: #f8f9fa;\n  border-bottom: 1px solid #dee2e6;\n}\n\n.order-header:hover {\n  background: #e9ecef;\n}\n\n.order-info {\n  flex: 1;\n}\n\n.order-reference {\n  font-size: 1.1rem;\n  margin-bottom: 8px;\n}\n\n.order-meta {\n  display: flex;\n  gap: 20px;\n  color: #6c757d;\n  font-size: 0.9rem;\n}\n\n.order-status {\n  display: flex;\n  align-items: center;\n  gap: 15px;\n}\n\n.status-badge {\n  padding: 6px 12px;\n  border-radius: 20px;\n  font-size: 0.8rem;\n  font-weight: 600;\n  text-transform: uppercase;\n}\n\n.status-pending { background: #fff3cd; color: #856404; }\n.status-confirmed { background: #d4edda; color: #155724; }\n.status-preparing { background: #cce5ff; color: #004085; }\n.status-shipped { background: #e2e3e5; color: #383d41; }\n.status-delivered { background: #d1ecf1; color: #0c5460; }\n.status-cancelled { background: #f8d7da; color: #721c24; }\n.status-default { background: #e9ecef; color: #495057; }\n\n.expand-icon {\n  transition: transform 0.2s ease;\n  font-size: 0.8rem;\n  color: #6c757d;\n}\n\n.expand-icon.expanded {\n  transform: rotate(180deg);\n}\n\n/* Order Details */\n.order-details {\n  padding: 20px;\n  background: white;\n}\n\n.order-details h4 {\n  color: #495057;\n  margin-bottom: 15px;\n  font-size: 1rem;\n  border-bottom: 1px solid #dee2e6;\n  padding-bottom: 8px;\n}\n\n/* Client Info */\n.client-info, .shipping-info {\n  margin-bottom: 25px;\n}\n\n.info-grid {\n  display: grid;\n  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));\n  gap: 10px;\n}\n\n.info-item {\n  font-size: 0.9rem;\n}\n\n.info-item strong {\n  color: #495057;\n}\n\n.address {\n  background: #f8f9fa;\n  padding: 15px;\n  border-radius: 4px;\n  border-left: 4px solid #007bff;\n}\n\n.address p {\n  margin: 0;\n  line-height: 1.4;\n}\n\n/* Products */\n.products-section {\n  margin-bottom: 25px;\n}\n\n.products-list {\n  display: flex;\n  flex-direction: column;\n  gap: 10px;\n}\n\n.product-item {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  padding: 15px;\n  background: #f8f9fa;\n  border-radius: 4px;\n  border: 1px solid #dee2e6;\n}\n\n.product-info {\n  flex: 1;\n}\n\n.product-name {\n  font-size: 1rem;\n  margin-bottom: 5px;\n}\n\n.product-details {\n  display: flex;\n  gap: 15px;\n  font-size: 0.8rem;\n  color: #6c757d;\n}\n\n.product-quantity {\n  text-align: center;\n  min-width: 80px;\n}\n\n.quantity {\n  font-weight: 600;\n  color: #495057;\n}\n\n.product-price {\n  text-align: right;\n  min-width: 120px;\n}\n\n.unit-price {\n  font-size: 0.8rem;\n  color: #6c757d;\n}\n\n.total-price {\n  font-size: 1rem;\n  color: #28a745;\n}\n\n.no-products {\n  text-align: center;\n  color: #6c757d;\n  font-style: italic;\n  padding: 20px;\n}\n\n/* Actions */\n.order-actions {\n  border-top: 1px solid #dee2e6;\n  padding-top: 20px;\n}\n\n.actions-buttons {\n  display: flex;\n  gap: 10px;\n  flex-wrap: wrap;\n}\n\n/* Buttons */\n.btn {\n  padding: 8px 16px;\n  border: none;\n  border-radius: 4px;\n  cursor: pointer;\n  font-size: 0.9rem;\n  font-weight: 500;\n  text-decoration: none;\n  display: inline-flex;\n  align-items: center;\n  gap: 5px;\n  transition: all 0.2s ease;\n}\n\n.btn:disabled {\n  opacity: 0.6;\n  cursor: not-allowed;\n}\n\n.btn-primary { background: #007bff; color: white; }\n.btn-primary:hover:not(:disabled) { background: #0056b3; }\n\n.btn-success { background: #28a745; color: white; }\n.btn-success:hover:not(:disabled) { background: #1e7e34; }\n\n.btn-warning { background: #ffc107; color: #212529; }\n.btn-warning:hover:not(:disabled) { background: #e0a800; }\n\n.btn-info { background: #17a2b8; color: white; }\n.btn-info:hover:not(:disabled) { background: #117a8b; }\n\n.btn-danger { background: #dc3545; color: white; }\n.btn-danger:hover:not(:disabled) { background: #c82333; }\n\n.btn-secondary { background: #6c757d; color: white; }\n.btn-secondary:hover:not(:disabled) { background: #545b62; }\n\n.btn-outline-primary { \n  background: transparent; \n  color: #007bff; \n  border: 1px solid #007bff; \n}\n.btn-outline-primary:hover:not(:disabled) { \n  background: #007bff; \n  color: white; \n}\n\n.btn-outline-secondary { \n  background: transparent; \n  color: #6c757d; \n  border: 1px solid #6c757d; \n}\n.btn-outline-secondary:hover:not(:disabled) { \n  background: #6c757d; \n  color: white; \n}\n\n/* Pagination */\n.pagination-container {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-top: 30px;\n  padding-top: 20px;\n  border-top: 1px solid #dee2e6;\n}\n\n.pagination-info {\n  color: #6c757d;\n  font-size: 0.9rem;\n}\n\n.pagination {\n  display: flex;\n  gap: 5px;\n}\n\n/* Alert */\n.alert {\n  padding: 15px;\n  border-radius: 4px;\n  margin-bottom: 20px;\n}\n\n.alert-danger {\n  background: #f8d7da;\n  color: #721c24;\n  border: 1px solid #f5c6cb;\n}\n\n/* Responsive */\n@media (max-width: 768px) {\n  .orders-container {\n    padding: 10px;\n  }\n  \n  .orders-header {\n    flex-direction: column;\n    gap: 15px;\n    align-items: stretch;\n  }\n  \n  .filters-row {\n    flex-direction: column;\n    gap: 15px;\n  }\n  \n  .form-control {\n    min-width: auto;\n  }\n  \n  .order-header {\n    flex-direction: column;\n    gap: 15px;\n    align-items: stretch;\n  }\n  \n  .order-meta {\n    flex-direction: column;\n    gap: 5px;\n  }\n  \n  .product-item {\n    flex-direction: column;\n    gap: 10px;\n    align-items: stretch;\n  }\n  \n  .actions-buttons {\n    flex-direction: column;\n  }\n  \n  .pagination-container {\n    flex-direction: column;\n    gap: 15px;\n  }\n}\n"], "mappings": ";AAAA,CAAC;AACC,WAAS;AACT,aAAW;AACX,UAAQ,EAAE;AACZ;AAGA,CAAC;AACC,WAAS;AACT,mBAAiB;AACjB,eAAa;AACb,iBAAe;AACf,kBAAgB;AAChB,iBAAe,IAAI,MAAM;AAC3B;AAEA,CATC,cASc;AACb,SAAO;AACP,UAAQ;AACR,aAAW;AACb;AAEA,CAAC;AACC,WAAS;AACT,OAAK;AACP;AAGA,CAAC;AACC,cAAY;AACZ,WAAS;AACT,iBAAe;AACf,iBAAe;AACjB;AAEA,CAAC;AACC,WAAS;AACT,OAAK;AACL,eAAa;AACb,aAAW;AACb;AAEA,CAAC;AACC,WAAS;AACT,kBAAgB;AAChB,OAAK;AACP;AAEA,CANC,aAMa;AACZ,eAAa;AACb,SAAO;AACP,aAAW;AACb;AAEA,CAAC;AACC,WAAS,IAAI;AACb,UAAQ,IAAI,MAAM;AAClB,iBAAe;AACf,aAAW;AACX,aAAW;AACb;AAEA,CARC,YAQY;AACX,WAAS;AACT,gBAAc;AACd,cAAY,EAAE,EAAE,EAAE,IAAI,KAAK,CAAC,EAAE,GAAG,EAAE,GAAG,EAAE;AAC1C;AAGA,CAAC;AACC,cAAY;AACZ,WAAS;AACX;AAEA,CAAC;AACC,UAAQ,IAAI,MAAM;AAClB,cAAY,IAAI,MAAM;AACtB,iBAAe;AACf,SAAO;AACP,UAAQ;AACR,aAAW,KAAK,GAAG,OAAO;AAC1B,UAAQ,EAAE,KAAK;AACjB;AAEA,WAJa;AAKX;AAAK,eAAW,OAAO;AAAO;AAC9B;AAAO,eAAW,OAAO;AAAS;AACpC;AAGA,CAAC;AACC,WAAS;AACT,kBAAgB;AAChB,OAAK;AACP;AAEA,CAAC;AACC,cAAY;AACZ,WAAS;AACT,SAAO;AACT;AAEA,CANC,UAMU;AACT,aAAW;AACX,iBAAe;AACjB;AAGA,CAAC;AACC,cAAY;AACZ,UAAQ,IAAI,MAAM;AAClB,iBAAe;AACf,YAAU;AACV,cAAY,EAAE,IAAI,IAAI,KAAK,CAAC,EAAC,CAAC,EAAC,CAAC,EAAC;AACjC,cAAY,WAAW,KAAK;AAC9B;AAEA,CATC,UASU;AACT,cAAY,EAAE,IAAI,IAAI,KAAK,CAAC,EAAC,CAAC,EAAC,CAAC,EAAC;AACnC;AAEA,CAAC;AACC,WAAS;AACT,mBAAiB;AACjB,eAAa;AACb,WAAS;AACT,UAAQ;AACR,cAAY;AACZ,iBAAe,IAAI,MAAM;AAC3B;AAEA,CAVC,YAUY;AACX,cAAY;AACd;AAEA,CAAC;AACC,QAAM;AACR;AAEA,CAAC;AACC,aAAW;AACX,iBAAe;AACjB;AAEA,CAAC;AACC,WAAS;AACT,OAAK;AACL,SAAO;AACP,aAAW;AACb;AAEA,CAAC;AACC,WAAS;AACT,eAAa;AACb,OAAK;AACP;AAEA,CAAC;AACC,WAAS,IAAI;AACb,iBAAe;AACf,aAAW;AACX,eAAa;AACb,kBAAgB;AAClB;AAEA,CAAC;AAAiB,cAAY;AAAS,SAAO;AAAS;AACvD,CAAC;AAAmB,cAAY;AAAS,SAAO;AAAS;AACzD,CAAC;AAAmB,cAAY;AAAS,SAAO;AAAS;AACzD,CAAC;AAAiB,cAAY;AAAS,SAAO;AAAS;AACvD,CAAC;AAAmB,cAAY;AAAS,SAAO;AAAS;AACzD,CAAC;AAAmB,cAAY;AAAS,SAAO;AAAS;AACzD,CAAC;AAAiB,cAAY;AAAS,SAAO;AAAS;AAEvD,CAAC;AACC,cAAY,UAAU,KAAK;AAC3B,aAAW;AACX,SAAO;AACT;AAEA,CANC,WAMW,CAAC;AACX,aAAW,OAAO;AACpB;AAGA,CAAC;AACC,WAAS;AACT,cAAY;AACd;AAEA,CALC,cAKc;AACb,SAAO;AACP,iBAAe;AACf,aAAW;AACX,iBAAe,IAAI,MAAM;AACzB,kBAAgB;AAClB;AAGA,CAAC;AAAa,CAAC;AACb,iBAAe;AACjB;AAEA,CAAC;AACC,WAAS;AACT,yBAAuB,OAAO,QAAQ,EAAE,OAAO,KAAK,EAAE;AACtD,OAAK;AACP;AAEA,CAAC;AACC,aAAW;AACb;AAEA,CAJC,UAIU;AACT,SAAO;AACT;AAEA,CAAC;AACC,cAAY;AACZ,WAAS;AACT,iBAAe;AACf,eAAa,IAAI,MAAM;AACzB;AAEA,CAPC,QAOQ;AACP,UAAQ;AACR,eAAa;AACf;AAGA,CAAC;AACC,iBAAe;AACjB;AAEA,CAAC;AACC,WAAS;AACT,kBAAgB;AAChB,OAAK;AACP;AAEA,CAAC;AACC,WAAS;AACT,mBAAiB;AACjB,eAAa;AACb,WAAS;AACT,cAAY;AACZ,iBAAe;AACf,UAAQ,IAAI,MAAM;AACpB;AAEA,CAAC;AACC,QAAM;AACR;AAEA,CAAC;AACC,aAAW;AACX,iBAAe;AACjB;AAEA,CAAC;AACC,WAAS;AACT,OAAK;AACL,aAAW;AACX,SAAO;AACT;AAEA,CAAC;AACC,cAAY;AACZ,aAAW;AACb;AAEA,CAAC;AACC,eAAa;AACb,SAAO;AACT;AAEA,CAAC;AACC,cAAY;AACZ,aAAW;AACb;AAEA,CAAC;AACC,aAAW;AACX,SAAO;AACT;AAEA,CAAC;AACC,aAAW;AACX,SAAO;AACT;AAEA,CAAC;AACC,cAAY;AACZ,SAAO;AACP,cAAY;AACZ,WAAS;AACX;AAGA,CAAC;AACC,cAAY,IAAI,MAAM;AACtB,eAAa;AACf;AAEA,CAAC;AACC,WAAS;AACT,OAAK;AACL,aAAW;AACb;AAGA,CAAC;AACC,WAAS,IAAI;AACb,UAAQ;AACR,iBAAe;AACf,UAAQ;AACR,aAAW;AACX,eAAa;AACb,mBAAiB;AACjB,WAAS;AACT,eAAa;AACb,OAAK;AACL,cAAY,IAAI,KAAK;AACvB;AAEA,CAdC,GAcG;AACF,WAAS;AACT,UAAQ;AACV;AAEA,CAAC;AAAc,cAAY;AAAS,SAAO;AAAO;AAClD,CADC,WACW,MAAM,KAAK;AAAa,cAAY;AAAS;AAEzD,CAAC;AAAc,cAAY;AAAS,SAAO;AAAO;AAClD,CADC,WACW,MAAM,KAAK;AAAa,cAAY;AAAS;AAEzD,CAAC;AAAc,cAAY;AAAS,SAAO;AAAS;AACpD,CADC,WACW,MAAM,KAAK;AAAa,cAAY;AAAS;AAEzD,CAAC;AAAW,cAAY;AAAS,SAAO;AAAO;AAC/C,CADC,QACQ,MAAM,KAAK;AAAa,cAAY;AAAS;AAEtD,CAAC;AAAa,cAAY;AAAS,SAAO;AAAO;AACjD,CADC,UACU,MAAM,KAAK;AAAa,cAAY;AAAS;AAExD,CAAC;AAAgB,cAAY;AAAS,SAAO;AAAO;AACpD,CADC,aACa,MAAM,KAAK;AAAa,cAAY;AAAS;AAE3D,CAAC;AACC,cAAY;AACZ,SAAO;AACP,UAAQ,IAAI,MAAM;AACpB;AACA,CALC,mBAKmB,MAAM,KAAK;AAC7B,cAAY;AACZ,SAAO;AACT;AAEA,CAAC;AACC,cAAY;AACZ,SAAO;AACP,UAAQ,IAAI,MAAM;AACpB;AACA,CALC,qBAKqB,MAAM,KAAK;AAC/B,cAAY;AACZ,SAAO;AACT;AAGA,CAAC;AACC,WAAS;AACT,mBAAiB;AACjB,eAAa;AACb,cAAY;AACZ,eAAa;AACb,cAAY,IAAI,MAAM;AACxB;AAEA,CAAC;AACC,SAAO;AACP,aAAW;AACb;AAEA,CAAC;AACC,WAAS;AACT,OAAK;AACP;AAGA,CAAC;AACC,WAAS;AACT,iBAAe;AACf,iBAAe;AACjB;AAEA,CAAC;AACC,cAAY;AACZ,SAAO;AACP,UAAQ,IAAI,MAAM;AACpB;AAGA,OAAO,CAAC,SAAS,EAAE;AACjB,GAlZD;AAmZG,aAAS;AACX;AAEA,GA/YD;AAgZG,oBAAgB;AAChB,SAAK;AACL,iBAAa;AACf;AAEA,GAzXD;AA0XG,oBAAgB;AAChB,SAAK;AACP;AAEA,GA3WD;AA4WG,eAAW;AACb;AAEA,GA5SD;AA6SG,oBAAgB;AAChB,SAAK;AACL,iBAAa;AACf;AAEA,GA3RD;AA4RG,oBAAgB;AAChB,SAAK;AACP;AAEA,GAjMD;AAkMG,oBAAgB;AAChB,SAAK;AACL,iBAAa;AACf;AAEA,GAvID;AAwIG,oBAAgB;AAClB;AAEA,GA1ED;AA2EG,oBAAgB;AAChB,SAAK;AACP;AACF;", "names": []}
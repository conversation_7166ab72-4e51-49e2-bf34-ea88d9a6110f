﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace WebApiPfe.Migrations
{
    /// <inheritdoc />
    public partial class AddPromotionGestionEntities : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.CreateTable(
                name: "PromotionGestions",
                columns: table => new
                {
                    Id = table.Column<int>(type: "int", nullable: false)
                        .Annotation("SqlServer:Identity", "1, 1"),
                    Code = table.Column<string>(type: "nvarchar(100)", maxLength: 100, nullable: false),
                    Nom = table.Column<string>(type: "nvarchar(200)", maxLength: 200, nullable: false),
                    Description = table.Column<string>(type: "nvarchar(1000)", maxLength: 1000, nullable: true),
                    Type = table.Column<int>(type: "int", nullable: false),
                    Valeur = table.Column<decimal>(type: "decimal(10,2)", nullable: false),
                    DateDebut = table.Column<DateTime>(type: "datetime2", nullable: false),
                    DateFin = table.Column<DateTime>(type: "datetime2", nullable: false),
                    EstActive = table.Column<bool>(type: "bit", nullable: false),
                    UtilisationsMax = table.Column<int>(type: "int", nullable: true),
                    UtilisationsActuelles = table.Column<int>(type: "int", nullable: false),
                    MontantMinimum = table.Column<decimal>(type: "decimal(10,2)", nullable: true),
                    DateCreation = table.Column<DateTime>(type: "datetime2", nullable: false),
                    FournisseurId = table.Column<int>(type: "int", nullable: true),
                    ProduitId = table.Column<int>(type: "int", nullable: true),
                    MontantTotalEconomise = table.Column<decimal>(type: "decimal(15,2)", nullable: false),
                    NombreCommandesImpactees = table.Column<int>(type: "int", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_PromotionGestions", x => x.Id);
                    table.ForeignKey(
                        name: "FK_PromotionGestions_AspNetUsers_FournisseurId",
                        column: x => x.FournisseurId,
                        principalTable: "AspNetUsers",
                        principalColumn: "Id");
                    table.ForeignKey(
                        name: "FK_PromotionGestions_Produits_ProduitId",
                        column: x => x.ProduitId,
                        principalTable: "Produits",
                        principalColumn: "Id");
                });

            migrationBuilder.CreateTable(
                name: "PromotionUtiliseesGestion",
                columns: table => new
                {
                    Id = table.Column<int>(type: "int", nullable: false)
                        .Annotation("SqlServer:Identity", "1, 1"),
                    PromotionId = table.Column<int>(type: "int", nullable: false),
                    CommandeId = table.Column<int>(type: "int", nullable: false),
                    CodePromoUtilise = table.Column<string>(type: "nvarchar(100)", maxLength: 100, nullable: false),
                    MontantReduction = table.Column<decimal>(type: "decimal(10,2)", nullable: false),
                    DateUtilisation = table.Column<DateTime>(type: "datetime2", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_PromotionUtiliseesGestion", x => x.Id);
                    table.ForeignKey(
                        name: "FK_PromotionUtiliseesGestion_Commandes_CommandeId",
                        column: x => x.CommandeId,
                        principalTable: "Commandes",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "FK_PromotionUtiliseesGestion_PromotionGestions_PromotionId",
                        column: x => x.PromotionId,
                        principalTable: "PromotionGestions",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateIndex(
                name: "IX_PromotionGestions_FournisseurId",
                table: "PromotionGestions",
                column: "FournisseurId");

            migrationBuilder.CreateIndex(
                name: "IX_PromotionGestions_ProduitId",
                table: "PromotionGestions",
                column: "ProduitId");

            migrationBuilder.CreateIndex(
                name: "IX_PromotionUtiliseesGestion_CommandeId",
                table: "PromotionUtiliseesGestion",
                column: "CommandeId");

            migrationBuilder.CreateIndex(
                name: "IX_PromotionUtiliseesGestion_PromotionId",
                table: "PromotionUtiliseesGestion",
                column: "PromotionId");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropTable(
                name: "PromotionUtiliseesGestion");

            migrationBuilder.DropTable(
                name: "PromotionGestions");
        }
    }
}

.stock-management-page {
  padding: 24px;
  max-width: 1400px;
  margin: 0 auto;
}

/* Header */
.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 32px;
}

.page-header h1 {
  margin: 0;
  font-size: 28px;
  font-weight: 600;
  color: #111827;
}

.header-stats {
  display: flex;
  gap: 24px;
}

.stat-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 12px 16px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  min-width: 80px;
}

.stat-value {
  font-size: 20px;
  font-weight: 700;
  color: #111827;
}

.stat-label {
  font-size: 12px;
  color: #6b7280;
  text-align: center;
}

.stat-item.warning .stat-value {
  color: #d97706;
}

.stat-item.danger .stat-value {
  color: #dc2626;
}

.stat-item.success .stat-value {
  color: #059669;
}

/* Alertes */
.alerts-section {
  margin-bottom: 32px;
}

.alerts-section h2 {
  margin: 0 0 16px 0;
  font-size: 20px;
  font-weight: 600;
  color: #111827;
}

.alerts-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
  gap: 16px;
}

.alert-card {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px;
  border-radius: 8px;
  border-left: 4px solid;
}

.alert-warning {
  background: #fef3c7;
  border-left-color: #d97706;
}

.alert-danger {
  background: #fef2f2;
  border-left-color: #dc2626;
}

.alert-product {
  display: flex;
  align-items: center;
  gap: 12px;
  flex: 1;
}

.alert-product img {
  width: 48px;
  height: 48px;
  object-fit: cover;
  border-radius: 6px;
}

.alert-info h3 {
  margin: 0 0 4px 0;
  font-size: 14px;
  font-weight: 600;
  color: #111827;
}

.alert-info p {
  margin: 0;
  font-size: 12px;
  color: #6b7280;
}

/* Filtres */
.filters-section {
  background: white;
  border-radius: 12px;
  padding: 20px;
  margin-bottom: 24px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  display: flex;
  gap: 16px;
  align-items: center;
}

.search-input {
  flex: 1;
  padding: 10px 16px;
  border: 1px solid #d1d5db;
  border-radius: 8px;
  font-size: 14px;
}

.filter-select {
  padding: 10px 12px;
  border: 1px solid #d1d5db;
  border-radius: 8px;
  font-size: 14px;
  min-width: 180px;
}

/* Sections */
.products-section,
.movements-section {
  background: white;
  border-radius: 12px;
  padding: 24px;
  margin-bottom: 24px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.products-section h2,
.movements-section h2 {
  margin: 0 0 20px 0;
  font-size: 20px;
  font-weight: 600;
  color: #111827;
}

/* Tables */
.products-table,
.movements-table {
  width: 100%;
}

.table-header {
  display: grid;
  padding: 12px 16px;
  background: #f9fafb;
  border-radius: 8px 8px 0 0;
  font-weight: 600;
  color: #374151;
  font-size: 14px;
  border-bottom: 1px solid #e5e7eb;
}

.products-table .table-header {
  grid-template-columns: 2fr 1fr 1fr 1fr 1fr 1fr;
  gap: 16px;
}

.movements-table .table-header {
  grid-template-columns: 1.5fr 2fr 1fr 1fr 1.5fr 1fr;
  gap: 16px;
}

.table-row {
  display: grid;
  padding: 16px;
  border-bottom: 1px solid #f3f4f6;
  align-items: center;
  transition: background 0.2s;
}

.table-row:hover {
  background: #f9fafb;
}

.products-table .table-row {
  grid-template-columns: 2fr 1fr 1fr 1fr 1fr 1fr;
  gap: 16px;
}

.movements-table .table-row {
  grid-template-columns: 1.5fr 2fr 1fr 1fr 1.5fr 1fr;
  gap: 16px;
}

/* Product info */
.product-info {
  display: flex;
  align-items: center;
  gap: 12px;
}

.product-info img {
  width: 48px;
  height: 48px;
  object-fit: cover;
  border-radius: 6px;
}

.product-details h3 {
  margin: 0 0 4px 0;
  font-size: 14px;
  font-weight: 600;
  color: #111827;
}

.product-details p {
  margin: 0;
  font-size: 12px;
  color: #6b7280;
}

/* Stock display */
.stock-display {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 8px 12px;
  border-radius: 6px;
  font-weight: 600;
}

.stock-display.stock-ok {
  background: #dcfce7;
  color: #166534;
}

.stock-display.stock-low {
  background: #fef3c7;
  color: #92400e;
}

.stock-display.stock-empty {
  background: #fef2f2;
  color: #dc2626;
}

.stock-number {
  font-size: 18px;
}

.stock-unit {
  font-size: 10px;
  opacity: 0.8;
}

/* Status badges */
.status-badge {
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 500;
}

.status-badge.stock-ok {
  background: #dcfce7;
  color: #166534;
}

.status-badge.stock-low {
  background: #fef3c7;
  color: #92400e;
}

.status-badge.stock-empty {
  background: #fef2f2;
  color: #dc2626;
}

/* Movement types */
.movement-type {
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 500;
}

.movement-type.type-in {
  background: #dcfce7;
  color: #166534;
}

.movement-type.type-out {
  background: #fef2f2;
  color: #dc2626;
}

.quantity-in {
  color: #059669;
  font-weight: 600;
}

.quantity-out {
  color: #dc2626;
  font-weight: 600;
}

/* Buttons */
.btn {
  padding: 8px 16px;
  border: none;
  border-radius: 6px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s;
}

.btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.btn-primary {
  background: #3b82f6;
  color: white;
}

.btn-primary:hover:not(:disabled) {
  background: #2563eb;
}

.btn-secondary {
  background: #6b7280;
  color: white;
}

.btn-secondary:hover:not(:disabled) {
  background: #4b5563;
}

.btn-sm {
  padding: 6px 12px;
  font-size: 12px;
}

/* Modal */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.modal-content {
  background: white;
  border-radius: 12px;
  width: 90%;
  max-width: 500px;
  max-height: 90vh;
  overflow: hidden;
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1);
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px;
  border-bottom: 1px solid #e5e7eb;
}

.modal-header h2 {
  margin: 0;
  font-size: 20px;
  font-weight: 600;
  color: #111827;
}

.btn-close {
  width: 32px;
  height: 32px;
  border: none;
  background: #e5e7eb;
  border-radius: 50%;
  cursor: pointer;
  font-size: 16px;
  color: #6b7280;
}

.modal-body {
  padding: 20px;
  max-height: 60vh;
  overflow-y: auto;
}

.product-summary {
  display: flex;
  align-items: center;
  gap: 16px;
  padding: 16px;
  background: #f9fafb;
  border-radius: 8px;
  margin-bottom: 20px;
}

.product-summary img {
  width: 64px;
  height: 64px;
  object-fit: cover;
  border-radius: 8px;
}

.product-summary h3 {
  margin: 0 0 8px 0;
  font-size: 16px;
  font-weight: 600;
  color: #111827;
}

.product-summary p {
  margin: 0 0 4px 0;
  font-size: 14px;
  color: #6b7280;
}

/* Form */
.stock-form {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.form-group {
  display: flex;
  flex-direction: column;
  gap: 6px;
}

.form-group label {
  font-size: 14px;
  font-weight: 500;
  color: #374151;
}

.form-control {
  padding: 10px 12px;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  font-size: 14px;
}

.radio-group {
  display: flex;
  gap: 16px;
}

.radio-label {
  display: flex;
  align-items: center;
  gap: 8px;
  cursor: pointer;
  font-size: 14px;
  color: #374151;
}

.stock-preview {
  padding: 12px;
  background: #f0f9ff;
  border: 1px solid #bfdbfe;
  border-radius: 6px;
}

.text-success {
  color: #059669;
}

.text-danger {
  color: #dc2626;
}

.modal-actions {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
  padding: 20px;
  border-top: 1px solid #e5e7eb;
}

/* Loading et messages */
.loading {
  text-align: center;
  padding: 60px 20px;
}

.spinner {
  width: 40px;
  height: 40px;
  border: 4px solid #f3f4f6;
  border-top: 4px solid #3b82f6;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin: 0 auto 16px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.error-message {
  background: #fef2f2;
  color: #dc2626;
  padding: 16px;
  border-radius: 8px;
  margin-bottom: 24px;
}

.no-products {
  text-align: center;
  padding: 60px 20px;
}

.no-products-icon {
  font-size: 64px;
  margin-bottom: 16px;
}

.no-products h3 {
  font-size: 20px;
  color: #111827;
  margin-bottom: 8px;
}

.no-products p {
  color: #6b7280;
}

/* Responsive */
@media (max-width: 768px) {
  .stock-management-page {
    padding: 16px;
  }
  
  .page-header {
    flex-direction: column;
    gap: 16px;
    align-items: stretch;
  }
  
  .header-stats {
    justify-content: space-between;
  }
  
  .alerts-grid {
    grid-template-columns: 1fr;
  }
  
  .filters-section {
    flex-direction: column;
    align-items: stretch;
  }
  
  .table-header,
  .table-row {
    display: block;
    padding: 12px;
  }
  
  .table-header {
    display: none;
  }
  
  .table-row > div {
    margin-bottom: 8px;
    display: flex;
    justify-content: space-between;
    align-items: center;
  }

  .table-row > div:before {
    content: attr(class);
    font-weight: 600;
    color: #6b7280;
    font-size: 12px;
    text-transform: uppercase;
  }
}

/* Styles pour les mouvements de stock */
.movement-in {
  color: #059669;
  font-weight: 600;
}

.movement-out {
  color: #dc2626;
  font-weight: 600;
}

.movement-in .movement-icon {
  color: #059669;
}

.movement-out .movement-icon {
  color: #dc2626;
}

/* Amélioration de l'historique des mouvements */
.movements-history .table-row {
  transition: background-color 0.2s ease;
}

.movements-history .table-row:hover {
  background-color: #f9fafb;
}

.movement-type {
  display: flex;
  align-items: center;
  gap: 8px;
}

.movement-icon {
  font-size: 16px;
  font-weight: bold;
}

.movement-quantity {
  font-weight: 600;
}

.movement-date {
  color: #6b7280;
  font-size: 14px;
}

.movement-user {
  color: #6b7280;
  font-size: 14px;
  font-style: italic;
}

/* Message aucun mouvement */
.no-movements {
  text-align: center;
  padding: 40px 20px;
  color: #6b7280;
}

.no-movements-icon {
  font-size: 48px;
  margin-bottom: 16px;
}

.no-movements h3 {
  margin: 0 0 8px 0;
  font-size: 18px;
  font-weight: 600;
  color: #374151;
}

.no-movements p {
  margin: 0;
  font-size: 14px;
}

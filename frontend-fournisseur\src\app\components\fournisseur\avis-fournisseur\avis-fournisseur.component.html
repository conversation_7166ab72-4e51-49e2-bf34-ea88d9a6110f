<div class="container-fluid">
  <!-- En-tête moderne -->
  <div class="page-header mb-4">
    <div class="header-content">
      <div class="header-title">
        <div class="title-icon">
          <i class="bi bi-star-fill"></i>
        </div>
        <div class="title-text">
          <h1 class="page-title">Mes Avis Clients</h1>
          <p class="page-subtitle">Consultez et répondez aux avis de vos clients</p>
        </div>
      </div>
      <div class="header-actions">
        <button class="btn btn-outline-primary" (click)="loadAvis()" title="Actualiser">
          <i class="bi bi-arrow-clockwise me-2"></i>
          Actualiser
        </button>
      </div>
    </div>
  </div>

  <!-- Statistiques en ligne -->
  <div class="stats-container mb-4" *ngIf="stats">
    <div class="stats-card stats-primary">
      <div class="stats-icon">
        <i class="bi bi-chat-square-text"></i>
      </div>
      <div class="stats-content">
        <div class="stats-number">{{ stats.totalAvis }}</div>
        <div class="stats-label">Total Avis</div>
      </div>
    </div>

    <div class="stats-card stats-success">
      <div class="stats-icon">
        <i class="bi bi-check-circle"></i>
      </div>
      <div class="stats-content">
        <div class="stats-number">{{ stats.avisPublies }}</div>
        <div class="stats-label">Publiés</div>
      </div>
    </div>

    <div class="stats-card stats-warning">
      <div class="stats-icon">
        <i class="bi bi-exclamation-triangle"></i>
      </div>
      <div class="stats-content">
        <div class="stats-number">{{ stats.avisSignales || 0 }}</div>
        <div class="stats-label">Signalés</div>
      </div>
    </div>

    <div class="stats-card stats-info">
      <div class="stats-icon">
        <i class="bi bi-star-fill"></i>
      </div>
      <div class="stats-content">
        <div class="stats-number">{{ stats.noteMoyenneGlobale | number:'1.1-1' }}</div>
        <div class="stats-label">Note Moyenne</div>
      </div>
    </div>
  </div>

  <!-- Filtres et recherche -->
  <div class="filters-section mb-4">
    <div class="filters-card">
      <div class="filters-header">
        <h5><i class="bi bi-funnel me-2"></i>Filtres</h5>
      </div>
      <div class="filters-content">
        <div class="row g-3">
          <div class="col-md-3">
            <label class="form-label">Statut</label>
            <select class="form-select" [(ngModel)]="filter.statut" (change)="onFilterChange()">
              <option [value]="undefined">Tous les statuts</option>
              <option [value]="StatutAvis.Publie">Publiés</option>
              <option [value]="StatutAvis.Signale">Signalés</option>
              <option [value]="StatutAvis.CommentaireSupprime">Commentaire supprimé</option>
            </select>
          </div>
          
          <div class="col-md-3">
            <label class="form-label">Recherche</label>
            <div class="input-group">
              <span class="input-group-text"><i class="bi bi-search"></i></span>
              <input type="text" class="form-control" placeholder="Rechercher..." 
                     [(ngModel)]="filter.recherche" (keyup.enter)="onFilterChange()">
            </div>
          </div>
          
          <div class="col-md-2">
            <label class="form-label">Trier par</label>
            <select class="form-select" [(ngModel)]="filter.sortBy" (change)="onFilterChange()">
              <option value="datePublication">Date</option>
              <option value="note">Note</option>
              <option value="produitNom">Produit</option>
            </select>
          </div>
          
          <div class="col-md-2">
            <label class="form-label">Ordre</label>
            <select class="form-select" [(ngModel)]="filter.sortDesc" (change)="onFilterChange()">
              <option [value]="true">Décroissant</option>
              <option [value]="false">Croissant</option>
            </select>
          </div>
          
          <div class="col-md-2 d-flex align-items-end">
            <button class="btn btn-outline-secondary w-100" (click)="resetFilters()">
              <i class="bi bi-x-circle me-2"></i>Reset
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Message d'erreur -->
  <div *ngIf="error" class="alert alert-danger alert-dismissible fade show">
    <i class="bi bi-exclamation-triangle me-2"></i>
    {{ error }}
    <button type="button" class="btn-close" (click)="error = null"></button>
  </div>

  <!-- Loading -->
  <div *ngIf="loading" class="loading-container">
    <div class="loading-spinner">
      <div class="spinner-border text-primary" role="status">
        <span class="visually-hidden">Chargement...</span>
      </div>
      <p class="loading-text">Chargement des avis...</p>
    </div>
  </div>

  <!-- Liste des avis -->
  <div *ngIf="!loading" class="avis-container">
    <div *ngIf="avis.length === 0" class="empty-state">
      <div class="empty-icon">
        <i class="bi bi-chat-square-text"></i>
      </div>
      <h3>Aucun avis trouvé</h3>
      <p>Il n'y a pas encore d'avis correspondant à vos critères.</p>
    </div>

    <div *ngIf="avis.length > 0" class="avis-list">
      <div *ngFor="let avisItem of avis" class="avis-card">
        <div class="avis-header">
          <div class="avis-product">
            <h6 class="product-name">{{ avisItem.produitNom }}</h6>
            <small class="product-ref">Réf: {{ avisItem.produitReference }}</small>
          </div>
          <div class="avis-date">
            <small class="text-muted">{{ avisItem.datePublication | date:'dd/MM/yyyy HH:mm' }}</small>
          </div>
        </div>

        <div class="avis-content">
          <div class="avis-client">
            <div class="client-info">
              <i class="bi bi-person-circle me-2"></i>
              <span>{{ avisItem.clientPrenom }} {{ avisItem.clientNom }}</span>
            </div>
            <div class="avis-rating">
              <div class="stars">
                <span *ngFor="let star of getStars(avisItem.note)" 
                      class="bi" [class]="'bi-' + star"></span>
              </div>
              <span class="rating-value">{{ avisItem.note }}/5</span>
            </div>
          </div>

          <!-- Commentaire -->
          <div class="avis-comment">
            <!-- Commentaire présent et non supprimé -->
            <div *ngIf="avisItem.commentaire && avisItem.commentaire.trim() && !avisItem.commentaireSupprime"
                 class="comment-content">
              <i class="bi bi-chat-quote me-2"></i>
              <span>{{ avisItem.commentaire }}</span>
            </div>
            <!-- Commentaire supprimé par l'admin -->
            <div *ngIf="avisItem.commentaireSupprime"
                 class="comment-deleted">
              <i class="bi bi-chat-square-x me-2"></i>
              <span>Commentaire supprimé</span>
            </div>
            <!-- Avis sans commentaire (note seule) -->
            <div *ngIf="(!avisItem.commentaire || !avisItem.commentaire.trim()) && !avisItem.commentaireSupprime"
                 class="comment-none">
              <i class="bi bi-star me-2"></i>
              <span>Note sans commentaire</span>
            </div>
          </div>

          <!-- Statut et actions -->
          <div class="avis-footer">
            <div class="avis-status">
              <span class="badge" [class]="'badge-' + getStatutClass(avisItem.statut)">
                <i class="bi" [class]="getStatutIcon(avisItem.statut)"></i>
                {{ avisItem.statutLibelle }}
              </span>
            </div>
            <div class="avis-actions">
              <button class="btn btn-sm btn-outline-primary" (click)="voirDetails(avisItem)" title="Voir détails">
                <i class="bi bi-eye"></i>
              </button>
              <button class="btn btn-sm btn-outline-success" (click)="repondreAvis(avisItem)" title="Répondre">
                <i class="bi bi-reply"></i>
              </button>
              <button class="btn btn-sm btn-outline-warning" (click)="signalerAvis(avisItem)" title="Signaler">
                <i class="bi bi-flag"></i>
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

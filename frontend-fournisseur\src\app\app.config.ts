import { ApplicationConfig, provideZoneChangeDetection } from '@angular/core';
import { provideRouter, withEnabledBlockingInitialNavigation, withInMemoryScrolling, withRouterConfig } from '@angular/router';
import { provideHttpClient, withInterceptors, withFetch, HTTP_INTERCEPTORS } from '@angular/common/http';
import { provideAnimationsAsync } from '@angular/platform-browser/animations/async';

import { routes } from './app.routes';
import { HttpInterceptorService } from './services';
import { HttpErrorInterceptor } from './interceptors/http-error.interceptor';
import { CorsInterceptor } from './interceptors/cors.interceptor';

export const appConfig: ApplicationConfig = {
  providers: [
    // Angular 19: Nouvelle détection de changement optimisée
    provideZoneChangeDetection({
      eventCoalescing: true,
      runCoalescing: true
    }),

    // Angular 19: Router amélioré avec nouvelles options
    provideRouter(
      routes,
      withEnabledBlockingInitialNavigation(),
      withInMemoryScrolling({
        scrollPositionRestoration: 'top',
        anchorScrolling: 'enabled'
      }),
      withRouterConfig({
        onSameUrlNavigation: 'reload'
      })
    ),

    // Angular 19: HTTP Client avec fetch API
    provideHttpClient(
      withFetch() // Nouvelle API fetch native
      // Suppression de withInterceptors([]) pour permettre aux intercepteurs legacy de fonctionner
    ),

    // Angular 19: Animations asynchrones pour de meilleures performances
    provideAnimationsAsync(),

    // Intercepteurs HTTP legacy (à migrer vers les nouveaux intercepteurs fonctionnels)
    {
      provide: HTTP_INTERCEPTORS,
      useClass: HttpInterceptorService,
      multi: true
    },
    {
      provide: HTTP_INTERCEPTORS,
      useClass: HttpErrorInterceptor,
      multi: true
    },
    {
      provide: HTTP_INTERCEPTORS,
      useClass: CorsInterceptor,
      multi: true
    }
  ]
};

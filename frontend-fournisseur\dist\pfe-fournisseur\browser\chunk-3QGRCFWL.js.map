{"version": 3, "sources": ["src/app/models/commande.model.ts", "src/app/services/commande.service.ts", "src/app/components/orders/orders.component.ts", "src/app/components/orders/orders.component.html"], "sourcesContent": ["export interface Commande {\n  id: number;\n  numeroCommande: string;\n  dateCommande: Date;\n  statut: StatutCommande;\n  montantHT: number;\n  montantTTC: number;\n  montantTVA: number;\n  fraisLivraison: number;\n  dateExpedition?: Date;\n  dateLivraison?: Date;\n  adresseLivraisonId: number;\n  clientId: number;\n  \n  // Navigation properties\n  client?: any;\n  adresseLivraison?: any;\n  detailsCommande?: DetailCommande[];\n  livraison?: Livraison;\n  paiement?: any;\n}\n\nexport interface DetailCommande {\n  id: number;\n  commandeId: number;\n  produitId: number;\n  quantite: number;\n  prixUnitaire: number;\n  montantHT: number;\n  montantTTC: number;\n  tauxTVA: number;\n  \n  // Navigation properties\n  produit?: any;\n}\n\nexport interface CommandeFournisseur {\n  id: number;\n  reference: string;\n  commandeClientId: number;\n  fournisseurId: number;\n  nomFournisseur: string;\n  matriculeFiscale: string;\n  dateCreation: Date;\n  dateLivraison?: Date;\n  fraisLivraison: number;\n  statut: string;\n  numeroBonLivraison?: string;\n  lignesCommande: LigneCommandeFournisseurDto[];\n  montantTotal: number;\n\n  // Propriétés calculées pour l'affichage\n  dateCommande?: Date; // Alias pour dateCreation\n  clientNom?: string;\n  clientEmail?: string;\n  clientTelephone?: string;\n  adresseLivraison?: any;\n}\n\nexport interface LigneCommandeFournisseur {\n  id: number;\n  commandeFournisseurId: number;\n  produitId: number;\n  quantite: number;\n  prixUnitaire: number;\n  montantTotal: number;\n\n  // Navigation properties\n  produit?: any;\n}\n\nexport interface LigneCommandeFournisseurDto {\n  id: number;\n  commandeId: number;\n  produitId: number;\n  nomProduit: string;\n  referenceProduit: string;\n  quantite: number;\n  prixUnitaire: number;\n  totalLigne: number;\n  imagePrincipale?: string;\n\n  // Alias pour compatibilité\n  produitNom?: string;\n  produitReference?: string;\n  produitDescription?: string;\n}\n\nexport interface Livraison {\n  id: number;\n  numeroSuivi: string;\n  statut: StatutLivraison;\n  dateExpedition?: Date;\n  dateLivraisonPrevue?: Date;\n  dateLivraisonReelle?: Date;\n  transporteur?: string;\n  commentaires?: string;\n  commandeId: number;\n  fournisseurId: number;\n  \n  // Navigation properties\n  commande?: Commande;\n  fournisseur?: any;\n}\n\nexport enum StatutCommande {\n  EnAttente = 'EnAttente',\n  Confirmee = 'Confirmee',\n  EnPreparation = 'EnPreparation',\n  Expediee = 'Expediee',\n  Livree = 'Livree',\n  Annulee = 'Annulee'\n}\n\nexport enum StatutCommandeFournisseur {\n  Nouvelle = 'Nouvelle',\n  Acceptee = 'Acceptee',\n  EnPreparation = 'EnPreparation',\n  Prete = 'Prete',\n  Expediee = 'Expediee',\n  Livree = 'Livree',\n  Refusee = 'Refusee'\n}\n\nexport enum StatutLivraison {\n  EnAttente = 'EnAttente',\n  EnCours = 'EnCours',\n  Livree = 'Livree',\n  Echec = 'Echec',\n  Retournee = 'Retournee'\n}\n", "import { Injectable } from '@angular/core';\nimport { HttpClient, HttpParams } from '@angular/common/http';\nimport { Observable } from 'rxjs';\nimport { tap, map } from 'rxjs/operators';\nimport {\n  Commande,\n  CommandeFournisseur,\n  LigneCommandeFournisseur,\n  Livraison,\n  StatutCommandeFournisseur,\n  StatutLivraison,\n  StatutCommande\n} from '../models';\nimport { environment } from '../../environments/environment';\n\n// Interfaces pour les réponses API\nexport interface CommandeResponse {\n  success: boolean;\n  data?: Commande;\n  message?: string;\n}\n\nexport interface CommandeListResponse {\n  success: boolean;\n  data?: Commande[];\n  total?: number;\n  page?: number;\n  pageSize?: number;\n  message?: string;\n}\n\nexport interface CommandeFournisseurResponse {\n  success: boolean;\n  data?: CommandeFournisseur;\n  message?: string;\n}\n\nexport interface CommandeFournisseurListResponse {\n  success: boolean;\n  data?: CommandeFournisseur[];\n  total?: number;\n  page?: number;\n  pageSize?: number;\n  message?: string;\n}\n\nexport interface LivraisonResponse {\n  success: boolean;\n  data?: Livraison;\n  message?: string;\n}\n\nexport interface StatutUpdateRequest {\n  statut: StatutCommande | StatutLivraison;\n  motif?: string;\n  commentaire?: string;\n}\n\nexport interface UpdateStatutCommandeFournisseurRequest {\n  statut: StatutCommandeFournisseur;\n  numeroBonLivraison?: string;\n}\n\nexport interface CommandeFournisseurApiResponse {\n  value: CommandeFournisseur[];\n  count: number;\n}\n\n@Injectable({\n  providedIn: 'root'\n})\nexport class CommandeService {\n  private readonly API_URL = environment.apiUrl || 'https://localhost:7264/api';\n\n  constructor(private http: HttpClient) {}\n\n  // ===== COMMANDES FOURNISSEUR =====\n\n  /**\n   * GET /api/CommandeFournisseur - Obtenir toutes les commandes fournisseur avec filtres\n   */\n  getCommandesFournisseur(filters?: any): Observable<CommandeFournisseurListResponse> {\n    let params = new HttpParams();\n\n    if (filters) {\n      Object.keys(filters).forEach(key => {\n        if (filters[key] !== null && filters[key] !== undefined) {\n          params = params.set(key, filters[key].toString());\n        }\n      });\n    }\n\n    console.log('📋 Récupération des commandes fournisseur avec filtres:', filters);\n\n    return this.http.get<CommandeFournisseurListResponse>(`${this.API_URL}/CommandeFournisseur`, { params })\n      .pipe(\n        tap(response => console.log('✅ Commandes fournisseur récupérées:', response))\n      );\n  }\n\n  /**\n   * POST /api/CommandeFournisseur - Créer une commande fournisseur\n   */\n  createCommandeFournisseur(commande: any): Observable<CommandeFournisseurResponse> {\n    console.log('➕ Création d\\'une nouvelle commande fournisseur:', commande);\n\n    return this.http.post<CommandeFournisseurResponse>(`${this.API_URL}/CommandeFournisseur`, commande)\n      .pipe(\n        tap(response => console.log('✅ Commande fournisseur créée:', response))\n      );\n  }\n\n  /**\n   * GET /api/CommandeFournisseur/{id} - Obtenir une commande fournisseur par ID\n   */\n  getCommandeFournisseurById(id: number): Observable<CommandeFournisseurResponse> {\n    console.log('🔍 Récupération de la commande fournisseur ID:', id);\n\n    return this.http.get<CommandeFournisseurResponse>(`${this.API_URL}/CommandeFournisseur/${id}`)\n      .pipe(\n        tap(response => console.log('✅ Commande fournisseur récupérée:', response))\n      );\n  }\n\n  /**\n   * PUT /api/CommandeFournisseur/{id}/statut - Mettre à jour le statut d'une commande fournisseur\n   */\n  updateStatutCommandeFournisseur(id: number, request: UpdateStatutCommandeFournisseurRequest): Observable<CommandeFournisseurResponse> {\n    console.log('🔄 Mise à jour du statut de la commande fournisseur ID:', id, request);\n\n    return this.http.put<CommandeFournisseurResponse>(`${this.API_URL}/CommandeFournisseur/${id}/statut`, request)\n      .pipe(\n        tap(response => console.log('✅ Statut de la commande fournisseur mis à jour:', response))\n      );\n  }\n\n  // ===== LIGNES COMMANDE FOURNISSEUR =====\n\n  /**\n   * Obtenir toutes les lignes de commande\n   */\n  getLignesCommandeFournisseur(): Observable<LigneCommandeFournisseur[]> {\n    return this.http.get<LigneCommandeFournisseur[]>(`${this.API_URL}/LignesCommandeFournisseur`);\n  }\n\n  /**\n   * Créer une ligne de commande\n   */\n  createLigneCommandeFournisseur(ligne: any): Observable<LigneCommandeFournisseur> {\n    return this.http.post<LigneCommandeFournisseur>(`${this.API_URL}/LignesCommandeFournisseur`, ligne);\n  }\n\n  /**\n   * Obtenir une ligne de commande par ID\n   */\n  getLigneCommandeFournisseurById(id: number): Observable<LigneCommandeFournisseur> {\n    return this.http.get<LigneCommandeFournisseur>(`${this.API_URL}/LignesCommandeFournisseur/${id}`);\n  }\n\n  /**\n   * Mettre à jour une ligne de commande\n   */\n  updateLigneCommandeFournisseur(id: number, ligne: any): Observable<void> {\n    return this.http.put<void>(`${this.API_URL}/LignesCommandeFournisseur/${id}`, ligne);\n  }\n\n  /**\n   * Supprimer une ligne de commande\n   */\n  deleteLigneCommandeFournisseur(id: number): Observable<void> {\n    return this.http.delete<void>(`${this.API_URL}/LignesCommandeFournisseur/${id}`);\n  }\n\n  // ===== LIVRAISONS =====\n\n  /**\n   * Obtenir une livraison par ID\n   */\n  getLivraisonById(id: number): Observable<Livraison> {\n    return this.http.get<Livraison>(`${this.API_URL}/Livraison/${id}`);\n  }\n\n  /**\n   * Mettre à jour une livraison\n   */\n  updateLivraison(id: number, livraison: any): Observable<void> {\n    return this.http.put<void>(`${this.API_URL}/Livraison/${id}`, livraison);\n  }\n\n  /**\n   * Obtenir la livraison d'une commande\n   */\n  getLivraisonByCommande(commandeId: number): Observable<Livraison> {\n    return this.http.get<Livraison>(`${this.API_URL}/Livraison/commande/${commandeId}`);\n  }\n\n  /**\n   * Créer une livraison\n   */\n  createLivraison(livraison: any): Observable<Livraison> {\n    return this.http.post<Livraison>(`${this.API_URL}/Livraison`, livraison);\n  }\n\n  /**\n   * Mettre à jour le statut d'une livraison\n   */\n  updateStatutLivraison(livraisonId: number, statut: StatutLivraison): Observable<void> {\n    return this.http.post<void>(`${this.API_URL}/Livraison/${livraisonId}/statut`, { statut });\n  }\n\n  /**\n   * Obtenir les statuts disponibles pour une livraison\n   */\n  getStatutsDisponibles(livraisonId: number): Observable<StatutLivraison[]> {\n    return this.http.get<StatutLivraison[]>(`${this.API_URL}/Livraison/${livraisonId}/statuts-disponibles`);\n  }\n\n  /**\n   * Obtenir l'étiquette d'une livraison\n   */\n  getEtiquetteLivraison(livraisonId: number): Observable<Blob> {\n    return this.http.get(`${this.API_URL}/Livraison/${livraisonId}/etiquette`, { \n      responseType: 'blob' \n    });\n  }\n\n  // ===== MÉTHODES UTILITAIRES =====\n\n  /**\n   * Obtenir les commandes d'un fournisseur spécifique\n   */\n  getCommandesByFournisseur(fournisseurId: number, statut?: StatutCommandeFournisseur): Observable<CommandeFournisseur[]> {\n    let params = new HttpParams();\n    if (statut) {\n      params = params.set('statut', statut);\n    }\n\n    console.log('🔍 Appel API getCommandesByFournisseur avec fournisseurId:', fournisseurId);\n\n    return this.http.get<CommandeFournisseurApiResponse>(`${this.API_URL}/CommandeFournisseur`, {\n      params: params.set('fournisseurId', fournisseurId.toString())\n    }).pipe(\n      map(response => {\n        console.log('📦 Réponse API brute:', response);\n        const commandes = response.value || [];\n        console.log('📋 Commandes extraites:', commandes);\n        commandes.forEach(commande => {\n          console.log(`📦 Commande ${commande.id}: ${commande.lignesCommande?.length || 0} lignes`);\n        });\n        return commandes;\n      }),\n      tap(commandes => console.log('✅ Commandes finales retournées:', commandes))\n    );\n  }\n\n  /**\n   * Obtenir les livraisons d'un fournisseur\n   */\n  getLivraisonsByFournisseur(fournisseurId: number): Observable<Livraison[]> {\n    return this.http.get<Livraison[]>(`${this.API_URL}/Livraison`, {\n      params: new HttpParams().set('fournisseurId', fournisseurId.toString())\n    });\n  }\n\n  /**\n   * Calculer le total d'une commande\n   */\n  calculateTotal(lignes: LigneCommandeFournisseur[]): number {\n    return lignes.reduce((total, ligne) => total + ligne.montantTotal, 0);\n  }\n\n  /**\n   * Vérifier si une commande peut être modifiée\n   */\n  canModifyCommande(statut: StatutCommandeFournisseur): boolean {\n    return [StatutCommandeFournisseur.Nouvelle, StatutCommandeFournisseur.Acceptee].includes(statut);\n  }\n\n}\n", "import { Component, OnInit, signal, computed } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { FormsModule } from '@angular/forms';\nimport { CommandeService, UpdateStatutCommandeFournisseurRequest } from '../../services/commande.service';\nimport { AuthService } from '../../services/auth.service';\nimport { CommandeFournisseur, StatutCommandeFournisseur } from '../../models/commande.model';\n\n@Component({\n  selector: 'app-orders',\n  standalone: true,\n  imports: [CommonModule, FormsModule],\n  templateUrl: './orders.component.html',\n  styleUrls: ['./orders.component.css']\n})\nexport class OrdersComponent implements OnInit {\n  // Angular 19: Signals\n  orders = signal<CommandeFournisseur[]>([]);\n  isLoading = signal(false);\n  error = signal('');\n  searchTerm = signal('');\n  selectedStatus = signal('');\n  currentPage = signal(1);\n  pageSize = signal(10);\n  expandedOrderId = signal<number | null>(null);\n\n  // Computed signals\n  filteredOrders = computed(() => {\n    const orders = this.orders();\n    const search = this.searchTerm().toLowerCase();\n    const status = this.selectedStatus();\n\n    return orders.filter(order => {\n      const matchesSearch = !search ||\n        order.reference.toLowerCase().includes(search) ||\n        order.nomFournisseur?.toLowerCase().includes(search);\n\n      const matchesStatus = !status || order.statut === status;\n\n      return matchesSearch && matchesStatus;\n    });\n  });\n\n  paginatedOrders = computed(() => {\n    const filtered = this.filteredOrders();\n    const page = this.currentPage();\n    const size = this.pageSize();\n    const start = (page - 1) * size;\n    return filtered.slice(start, start + size);\n  });\n\n  totalPages = computed(() => {\n    return Math.ceil(this.filteredOrders().length / this.pageSize());\n  });\n\n  constructor(\n    private commandeService: CommandeService,\n    private authService: AuthService\n  ) {}\n\n  ngOnInit(): void {\n    console.log('🔄 OrdersComponent - ngOnInit appelé');\n    this.loadOrders();\n  }\n\n  loadOrders(): void {\n    console.log('🔄 loadOrders - Début du chargement');\n    this.isLoading.set(true);\n    this.error.set('');\n\n    const currentUser = this.authService.getCurrentUser();\n    console.log('👤 Utilisateur récupéré:', currentUser);\n\n    if (!currentUser?.id) {\n      console.error('❌ Aucun utilisateur connecté');\n      this.error.set('Utilisateur non connecté');\n      this.isLoading.set(false);\n      return;\n    }\n\n    console.log('🔍 Appel API getCommandesByFournisseur avec ID:', currentUser.id);\n    this.commandeService.getCommandesByFournisseur(currentUser.id).subscribe({\n      next: (orders) => {\n        console.log('📦 Commandes fournisseur récupérées:', orders);\n        this.orders.set(orders);\n        this.isLoading.set(false);\n      },\n      error: (error) => {\n        console.error('❌ Erreur lors du chargement des commandes:', error);\n        this.error.set('Erreur lors du chargement des commandes');\n        this.isLoading.set(false);\n      }\n    });\n  }\n\n  // Méthodes utilitaires\n  formatDate(date: string | Date): string {\n    return new Date(date).toLocaleDateString('fr-FR', {\n      day: '2-digit',\n      month: '2-digit',\n      year: 'numeric',\n      hour: '2-digit',\n      minute: '2-digit'\n    });\n  }\n\n  formatPrice(price: number): string {\n    return new Intl.NumberFormat('fr-FR', {\n      style: 'currency',\n      currency: 'EUR'\n    }).format(price);\n  }\n\n  getStatusClass(status: string): string {\n    const statusClasses: { [key: string]: string } = {\n      'Nouvelle': 'status-pending',\n      'EnAttente': 'status-pending',\n      'Acceptee': 'status-confirmed',\n      'Confirmee': 'status-confirmed',\n      'EnPreparation': 'status-preparing',\n      'Prete': 'status-preparing',\n      'Expediee': 'status-shipped',\n      'Livree': 'status-delivered',\n      'Refusee': 'status-cancelled',\n      'Annulee': 'status-cancelled'\n    };\n    return statusClasses[status] || 'status-default';\n  }\n\n  getStatusText(status: string): string {\n    const statusTexts: { [key: string]: string } = {\n      'Nouvelle': 'Nouvelle',\n      'EnAttente': 'En attente',\n      'Acceptee': 'Acceptée',\n      'Confirmee': 'Confirmée',\n      'EnPreparation': 'En préparation',\n      'Prete': 'Prête',\n      'Expediee': 'Expédiée',\n      'Livree': 'Livrée',\n      'Refusee': 'Refusée',\n      'Annulee': 'Annulée'\n    };\n    return statusTexts[status] || status;\n  }\n\n  // Actions sur les commandes\n  toggleOrderDetails(orderId: number): void {\n    const current = this.expandedOrderId();\n    this.expandedOrderId.set(current === orderId ? null : orderId);\n  }\n\n  isOrderExpanded(orderId: number): boolean {\n    return this.expandedOrderId() === orderId;\n  }\n\n  updateOrderStatus(orderId: number, newStatus: string): void {\n    const request: UpdateStatutCommandeFournisseurRequest = {\n      statut: newStatus as StatutCommandeFournisseur,\n      numeroBonLivraison: newStatus === 'Livree' ? `BL-${Date.now()}` : undefined\n    };\n\n    this.commandeService.updateStatutCommandeFournisseur(orderId, request).subscribe({\n      next: () => {\n        console.log('✅ Statut de commande mis à jour');\n        this.loadOrders(); // Recharger les commandes\n      },\n      error: (error) => {\n        console.error('❌ Erreur lors de la mise à jour du statut:', error);\n        this.error.set('Erreur lors de la mise à jour du statut');\n      }\n    });\n  }\n\n  // Pagination\n  onPageChange(page: number): void {\n    if (page >= 1 && page <= this.totalPages()) {\n      this.currentPage.set(page);\n    }\n  }\n\n  // Filtres\n  onSearchChange(event: Event): void {\n    const target = event.target as HTMLInputElement;\n    this.searchTerm.set(target.value);\n    this.currentPage.set(1); // Reset to first page\n  }\n\n  onStatusChange(event: Event): void {\n    const target = event.target as HTMLSelectElement;\n    this.selectedStatus.set(target.value);\n    this.currentPage.set(1); // Reset to first page\n  }\n\n  clearFilters(): void {\n    this.searchTerm.set('');\n    this.selectedStatus.set('');\n    this.currentPage.set(1);\n  }\n\n  // Méthodes pour les actions sur les commandes\n  canConfirmOrder(status: string): boolean {\n    return status === 'EnAttente' || status === 'Nouvelle';\n  }\n\n  canPrepareOrder(status: string): boolean {\n    return status === 'Confirmee' || status === 'Acceptee';\n  }\n\n  canShipOrder(status: string): boolean {\n    return status === 'EnPreparation' || status === 'Prete';\n  }\n\n  canDeliverOrder(status: string): boolean {\n    return status === 'Expediee';\n  }\n\n  canCancelOrder(status: string): boolean {\n    return ['EnAttente', 'Nouvelle', 'Confirmee', 'Acceptee'].includes(status);\n  }\n\n  // Math object for template\n  Math = Math;\n}\n", "<div class=\"orders-container\">\n  <!-- Header -->\n  <div class=\"orders-header\">\n    <h1>📦 Gestion des Commandes</h1>\n    <div class=\"header-actions\">\n      <button class=\"btn btn-secondary\" (click)=\"loadOrders()\">\n        🔄 Actualiser\n      </button>\n    </div>\n  </div>\n\n  <!-- Filtres -->\n  <div class=\"filters-section\">\n    <div class=\"filters-row\">\n      <div class=\"filter-group\">\n        <label for=\"search\">🔍 Rechercher:</label>\n        <input\n          id=\"search\"\n          type=\"text\"\n          class=\"form-control\"\n          placeholder=\"Référence, client...\"\n          [value]=\"searchTerm()\"\n          (input)=\"onSearchChange($event)\"\n        />\n      </div>\n\n      <div class=\"filter-group\">\n        <label for=\"status\">📊 Statut:</label>\n        <select\n          id=\"status\"\n          class=\"form-control\"\n          [value]=\"selectedStatus()\"\n          (change)=\"onStatusChange($event)\"\n        >\n          <option value=\"\">Tous les statuts</option>\n          <option value=\"Nouvelle\">Nouvelle</option>\n          <option value=\"Acceptee\">Acceptée</option>\n          <option value=\"EnPreparation\">En préparation</option>\n          <option value=\"Prete\">Prête</option>\n          <option value=\"Expediee\">Expédiée</option>\n          <option value=\"Livree\">Livrée</option>\n          <option value=\"Refusee\">Refusée</option>\n        </select>\n      </div>\n\n      <div class=\"filter-group\">\n        <button class=\"btn btn-outline-secondary\" (click)=\"clearFilters()\">\n          🗑️ Effacer filtres\n        </button>\n      </div>\n    </div>\n  </div>\n\n  <!-- Loading -->\n  @if (isLoading()) {\n    <div class=\"loading-container\">\n      <div class=\"spinner\"></div>\n      <p>Chargement des commandes...</p>\n    </div>\n  }\n\n  <!-- Error -->\n  @if (error()) {\n    <div class=\"alert alert-danger\">\n      <strong>❌ Erreur:</strong> {{ error() }}\n    </div>\n  }\n\n  <!-- Orders List -->\n  @if (!isLoading() && !error()) {\n    <div class=\"orders-list\">\n      @if (paginatedOrders().length === 0) {\n        <div class=\"no-orders\">\n          <p>📭 Aucune commande trouvée</p>\n          @if (searchTerm() || selectedStatus()) {\n            <button class=\"btn btn-primary\" (click)=\"clearFilters()\">\n              Voir toutes les commandes\n            </button>\n          }\n        </div>\n      } @else {\n        @for (order of paginatedOrders(); track order.id) {\n          <div class=\"order-card\">\n            <!-- Order Header -->\n            <div class=\"order-header\" (click)=\"toggleOrderDetails(order.id)\">\n              <div class=\"order-info\">\n                <div class=\"order-reference\">\n                  <strong>📋 {{ order.reference }}</strong>\n                </div>\n                <div class=\"order-meta\">\n                  <span class=\"order-date\">📅 {{ formatDate(order.dateCreation) }}</span>\n                  <span class=\"order-total\">💰 {{ formatPrice(order.montantTotal) }}</span>\n                </div>\n              </div>\n              \n              <div class=\"order-status\">\n                <span class=\"status-badge\" [class]=\"getStatusClass(order.statut)\">\n                  {{ getStatusText(order.statut) }}\n                </span>\n                <span class=\"expand-icon\" [class.expanded]=\"isOrderExpanded(order.id)\">\n                  ▼\n                </span>\n              </div>\n            </div>\n\n            <!-- Order Details (Expandable) -->\n            @if (isOrderExpanded(order.id)) {\n              <div class=\"order-details\">\n                <!-- Order Info -->\n                <div class=\"client-info\">\n                  <h4>📋 Informations Commande</h4>\n                  <div class=\"info-grid\">\n                    <div class=\"info-item\">\n                      <strong>Référence:</strong> {{ order.reference }}\n                    </div>\n                    <div class=\"info-item\">\n                      <strong>Fournisseur:</strong> {{ order.nomFournisseur }}\n                    </div>\n                    <div class=\"info-item\">\n                      <strong>Matricule:</strong> {{ order.matriculeFiscale || 'Non renseigné' }}\n                    </div>\n                    <div class=\"info-item\">\n                      <strong>Frais livraison:</strong> {{ formatPrice(order.fraisLivraison) }}\n                    </div>\n                    @if (order.numeroBonLivraison) {\n                      <div class=\"info-item\">\n                        <strong>Bon de livraison:</strong> {{ order.numeroBonLivraison }}\n                      </div>\n                    }\n                  </div>\n                </div>\n\n\n\n                <!-- Products -->\n                <div class=\"products-section\">\n                  <h4>🛍️ Produits Commandés</h4>\n                  @if (order.lignesCommande && order.lignesCommande.length > 0) {\n                    <div class=\"products-list\">\n                      @for (ligne of order.lignesCommande; track ligne.id) {\n                        <div class=\"product-item\">\n                          <div class=\"product-info\">\n                            <div class=\"product-name\">\n                              <strong>{{ ligne.nomProduit }}</strong>\n                            </div>\n                            <div class=\"product-details\">\n                              <span class=\"product-ref\">Réf: {{ ligne.referenceProduit || 'N/A' }}</span>\n                              @if (ligne.imagePrincipale) {\n                                <span class=\"product-image\">🖼️ Image disponible</span>\n                              }\n                            </div>\n                          </div>\n                          <div class=\"product-quantity\">\n                            <span class=\"quantity\">Qté: {{ ligne.quantite }}</span>\n                          </div>\n                          <div class=\"product-price\">\n                            <div class=\"unit-price\">{{ formatPrice(ligne.prixUnitaire) }}/u</div>\n                            <div class=\"total-price\"><strong>{{ formatPrice(ligne.totalLigne) }}</strong></div>\n                          </div>\n                        </div>\n                      }\n                    </div>\n                  } @else {\n                    <p class=\"no-products\">Aucun produit trouvé pour cette commande</p>\n                  }\n                </div>\n\n                <!-- Order Actions -->\n                <div class=\"order-actions\">\n                  <h4>⚡ Actions</h4>\n                  <div class=\"actions-buttons\">\n                    @if (canConfirmOrder(order.statut)) {\n                      <button\n                        class=\"btn btn-success\"\n                        (click)=\"updateOrderStatus(order.id, 'Acceptee')\"\n                      >\n                        ✅ Accepter\n                      </button>\n                    }\n\n                    @if (canPrepareOrder(order.statut)) {\n                      <button\n                        class=\"btn btn-warning\"\n                        (click)=\"updateOrderStatus(order.id, 'EnPreparation')\"\n                      >\n                        📦 Préparer\n                      </button>\n                    }\n\n                    @if (canShipOrder(order.statut)) {\n                      <button\n                        class=\"btn btn-info\"\n                        (click)=\"updateOrderStatus(order.id, 'Expediee')\"\n                      >\n                        🚚 Expédier\n                      </button>\n                    }\n\n                    @if (canDeliverOrder(order.statut)) {\n                      <button\n                        class=\"btn btn-primary\"\n                        (click)=\"updateOrderStatus(order.id, 'Livree')\"\n                      >\n                        ✅ Marquer comme livrée\n                      </button>\n                    }\n\n                    @if (canCancelOrder(order.statut)) {\n                      <button\n                        class=\"btn btn-danger\"\n                        (click)=\"updateOrderStatus(order.id, 'Refusee')\"\n                      >\n                        ❌ Refuser\n                      </button>\n                    }\n                  </div>\n                </div>\n              </div>\n            }\n          </div>\n        }\n      }\n    </div>\n\n    <!-- Pagination -->\n    @if (totalPages() > 1) {\n      <div class=\"pagination-container\">\n        <div class=\"pagination-info\">\n          Affichage {{ (currentPage() - 1) * pageSize() + 1 }} à \n          {{ Math.min(currentPage() * pageSize(), filteredOrders().length) }} \n          sur {{ filteredOrders().length }} commandes\n        </div>\n        \n        <div class=\"pagination\">\n          <button \n            class=\"btn btn-outline-primary\"\n            [disabled]=\"currentPage() === 1\"\n            (click)=\"onPageChange(currentPage() - 1)\"\n          >\n            ← Précédent\n          </button>\n          \n          @for (page of [].constructor(totalPages()); track $index) {\n            <button \n              class=\"btn\"\n              [class.btn-primary]=\"$index + 1 === currentPage()\"\n              [class.btn-outline-primary]=\"$index + 1 !== currentPage()\"\n              (click)=\"onPageChange($index + 1)\"\n            >\n              {{ $index + 1 }}\n            </button>\n          }\n          \n          <button \n            class=\"btn btn-outline-primary\"\n            [disabled]=\"currentPage() === totalPages()\"\n            (click)=\"onPageChange(currentPage() + 1)\"\n          >\n            Suivant →\n          </button>\n        </div>\n      </div>\n    }\n  }\n</div>\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAyGA,IAAY;CAAZ,SAAYA,iBAAc;AACxB,EAAAA,gBAAA,WAAA,IAAA;AACA,EAAAA,gBAAA,WAAA,IAAA;AACA,EAAAA,gBAAA,eAAA,IAAA;AACA,EAAAA,gBAAA,UAAA,IAAA;AACA,EAAAA,gBAAA,QAAA,IAAA;AACA,EAAAA,gBAAA,SAAA,IAAA;AACF,GAPY,mBAAA,iBAAc,CAAA,EAAA;AAS1B,IAAY;CAAZ,SAAYC,4BAAyB;AACnC,EAAAA,2BAAA,UAAA,IAAA;AACA,EAAAA,2BAAA,UAAA,IAAA;AACA,EAAAA,2BAAA,eAAA,IAAA;AACA,EAAAA,2BAAA,OAAA,IAAA;AACA,EAAAA,2BAAA,UAAA,IAAA;AACA,EAAAA,2BAAA,QAAA,IAAA;AACA,EAAAA,2BAAA,SAAA,IAAA;AACF,GARY,8BAAA,4BAAyB,CAAA,EAAA;AAUrC,IAAY;CAAZ,SAAYC,kBAAe;AACzB,EAAAA,iBAAA,WAAA,IAAA;AACA,EAAAA,iBAAA,SAAA,IAAA;AACA,EAAAA,iBAAA,QAAA,IAAA;AACA,EAAAA,iBAAA,OAAA,IAAA;AACA,EAAAA,iBAAA,WAAA,IAAA;AACF,GANY,oBAAA,kBAAe,CAAA,EAAA;;;ACrDrB,IAAO,kBAAP,MAAO,iBAAe;EAGN;EAFH,UAAU,YAAY,UAAU;EAEjD,YAAoB,MAAgB;AAAhB,SAAA,OAAA;EAAmB;;;;;EAOvC,wBAAwB,SAAa;AACnC,QAAI,SAAS,IAAI,WAAU;AAE3B,QAAI,SAAS;AACX,aAAO,KAAK,OAAO,EAAE,QAAQ,SAAM;AACjC,YAAI,QAAQ,GAAG,MAAM,QAAQ,QAAQ,GAAG,MAAM,QAAW;AACvD,mBAAS,OAAO,IAAI,KAAK,QAAQ,GAAG,EAAE,SAAQ,CAAE;QAClD;MACF,CAAC;IACH;AAEA,YAAQ,IAAI,wEAA2D,OAAO;AAE9E,WAAO,KAAK,KAAK,IAAqC,GAAG,KAAK,OAAO,wBAAwB,EAAE,OAAM,CAAE,EACpG,KACC,IAAI,cAAY,QAAQ,IAAI,qDAAuC,QAAQ,CAAC,CAAC;EAEnF;;;;EAKA,0BAA0B,UAAa;AACrC,YAAQ,IAAI,2DAAoD,QAAQ;AAExE,WAAO,KAAK,KAAK,KAAkC,GAAG,KAAK,OAAO,wBAAwB,QAAQ,EAC/F,KACC,IAAI,cAAY,QAAQ,IAAI,4CAAiC,QAAQ,CAAC,CAAC;EAE7E;;;;EAKA,2BAA2B,IAAU;AACnC,YAAQ,IAAI,+DAAkD,EAAE;AAEhE,WAAO,KAAK,KAAK,IAAiC,GAAG,KAAK,OAAO,wBAAwB,EAAE,EAAE,EAC1F,KACC,IAAI,cAAY,QAAQ,IAAI,mDAAqC,QAAQ,CAAC,CAAC;EAEjF;;;;EAKA,gCAAgC,IAAY,SAA+C;AACzF,YAAQ,IAAI,qEAA2D,IAAI,OAAO;AAElF,WAAO,KAAK,KAAK,IAAiC,GAAG,KAAK,OAAO,wBAAwB,EAAE,WAAW,OAAO,EAC1G,KACC,IAAI,cAAY,QAAQ,IAAI,2DAAmD,QAAQ,CAAC,CAAC;EAE/F;;;;;EAOA,+BAA4B;AAC1B,WAAO,KAAK,KAAK,IAAgC,GAAG,KAAK,OAAO,4BAA4B;EAC9F;;;;EAKA,+BAA+B,OAAU;AACvC,WAAO,KAAK,KAAK,KAA+B,GAAG,KAAK,OAAO,8BAA8B,KAAK;EACpG;;;;EAKA,gCAAgC,IAAU;AACxC,WAAO,KAAK,KAAK,IAA8B,GAAG,KAAK,OAAO,8BAA8B,EAAE,EAAE;EAClG;;;;EAKA,+BAA+B,IAAY,OAAU;AACnD,WAAO,KAAK,KAAK,IAAU,GAAG,KAAK,OAAO,8BAA8B,EAAE,IAAI,KAAK;EACrF;;;;EAKA,+BAA+B,IAAU;AACvC,WAAO,KAAK,KAAK,OAAa,GAAG,KAAK,OAAO,8BAA8B,EAAE,EAAE;EACjF;;;;;EAOA,iBAAiB,IAAU;AACzB,WAAO,KAAK,KAAK,IAAe,GAAG,KAAK,OAAO,cAAc,EAAE,EAAE;EACnE;;;;EAKA,gBAAgB,IAAY,WAAc;AACxC,WAAO,KAAK,KAAK,IAAU,GAAG,KAAK,OAAO,cAAc,EAAE,IAAI,SAAS;EACzE;;;;EAKA,uBAAuB,YAAkB;AACvC,WAAO,KAAK,KAAK,IAAe,GAAG,KAAK,OAAO,uBAAuB,UAAU,EAAE;EACpF;;;;EAKA,gBAAgB,WAAc;AAC5B,WAAO,KAAK,KAAK,KAAgB,GAAG,KAAK,OAAO,cAAc,SAAS;EACzE;;;;EAKA,sBAAsB,aAAqB,QAAuB;AAChE,WAAO,KAAK,KAAK,KAAW,GAAG,KAAK,OAAO,cAAc,WAAW,WAAW,EAAE,OAAM,CAAE;EAC3F;;;;EAKA,sBAAsB,aAAmB;AACvC,WAAO,KAAK,KAAK,IAAuB,GAAG,KAAK,OAAO,cAAc,WAAW,sBAAsB;EACxG;;;;EAKA,sBAAsB,aAAmB;AACvC,WAAO,KAAK,KAAK,IAAI,GAAG,KAAK,OAAO,cAAc,WAAW,cAAc;MACzE,cAAc;KACf;EACH;;;;;EAOA,0BAA0B,eAAuB,QAAkC;AACjF,QAAI,SAAS,IAAI,WAAU;AAC3B,QAAI,QAAQ;AACV,eAAS,OAAO,IAAI,UAAU,MAAM;IACtC;AAEA,YAAQ,IAAI,qEAA8D,aAAa;AAEvF,WAAO,KAAK,KAAK,IAAoC,GAAG,KAAK,OAAO,wBAAwB;MAC1F,QAAQ,OAAO,IAAI,iBAAiB,cAAc,SAAQ,CAAE;KAC7D,EAAE,KACD,IAAI,cAAW;AACb,cAAQ,IAAI,mCAAyB,QAAQ;AAC7C,YAAM,YAAY,SAAS,SAAS,CAAA;AACpC,cAAQ,IAAI,kCAA2B,SAAS;AAChD,gBAAU,QAAQ,cAAW;AAC3B,gBAAQ,IAAI,sBAAe,SAAS,EAAE,KAAK,SAAS,gBAAgB,UAAU,CAAC,SAAS;MAC1F,CAAC;AACD,aAAO;IACT,CAAC,GACD,IAAI,eAAa,QAAQ,IAAI,2CAAmC,SAAS,CAAC,CAAC;EAE/E;;;;EAKA,2BAA2B,eAAqB;AAC9C,WAAO,KAAK,KAAK,IAAiB,GAAG,KAAK,OAAO,cAAc;MAC7D,QAAQ,IAAI,WAAU,EAAG,IAAI,iBAAiB,cAAc,SAAQ,CAAE;KACvE;EACH;;;;EAKA,eAAe,QAAkC;AAC/C,WAAO,OAAO,OAAO,CAAC,OAAO,UAAU,QAAQ,MAAM,cAAc,CAAC;EACtE;;;;EAKA,kBAAkB,QAAiC;AACjD,WAAO,CAAC,0BAA0B,UAAU,0BAA0B,QAAQ,EAAE,SAAS,MAAM;EACjG;;qCA7MW,kBAAe,mBAAA,UAAA,CAAA;EAAA;4EAAf,kBAAe,SAAf,iBAAe,WAAA,YAFd,OAAM,CAAA;;;sEAEP,iBAAe,CAAA;UAH3B;WAAW;MACV,YAAY;KACb;;;;;;;;;AEfG,IAAA,yBAAA,GAAA,OAAA,EAAA;AACE,IAAA,oBAAA,GAAA,OAAA,EAAA;AACA,IAAA,yBAAA,GAAA,GAAA;AAAG,IAAA,iBAAA,GAAA,6BAAA;AAA2B,IAAA,uBAAA,EAAI;;;;;AAMpC,IAAA,yBAAA,GAAA,OAAA,EAAA,EAAgC,GAAA,QAAA;AACtB,IAAA,iBAAA,GAAA,gBAAA;AAAS,IAAA,uBAAA;AAAU,IAAA,iBAAA,CAAA;AAC7B,IAAA,uBAAA;;;;AAD6B,IAAA,oBAAA,CAAA;AAAA,IAAA,6BAAA,KAAA,OAAA,MAAA,GAAA,GAAA;;;;;;AAWrB,IAAA,yBAAA,GAAA,UAAA,EAAA;AAAgC,IAAA,qBAAA,SAAA,SAAA,8FAAA;AAAA,MAAA,wBAAA,GAAA;AAAA,YAAA,SAAA,wBAAA,CAAA;AAAA,aAAA,sBAAS,OAAA,aAAA,CAAc;IAAA,CAAA;AACrD,IAAA,iBAAA,GAAA,6BAAA;AACF,IAAA,uBAAA;;;;;AALJ,IAAA,yBAAA,GAAA,OAAA,EAAA,EAAuB,GAAA,GAAA;AAClB,IAAA,iBAAA,GAAA,sCAAA;AAA0B,IAAA,uBAAA;AAC7B,IAAA,qBAAA,GAAA,qEAAA,GAAA,GAAA,UAAA,EAAA;AAKF,IAAA,uBAAA;;;;AALE,IAAA,oBAAA,CAAA;AAAA,IAAA,wBAAA,OAAA,WAAA,KAAA,OAAA,eAAA,IAAA,IAAA,EAAA;;;;;AAmDY,IAAA,yBAAA,GAAA,OAAA,EAAA,EAAuB,GAAA,QAAA;AACb,IAAA,iBAAA,GAAA,mBAAA;AAAiB,IAAA,uBAAA;AAAU,IAAA,iBAAA,CAAA;AACrC,IAAA,uBAAA;;;;AADqC,IAAA,oBAAA,CAAA;AAAA,IAAA,6BAAA,KAAA,SAAA,oBAAA,GAAA;;;;;AAsB3B,IAAA,yBAAA,GAAA,QAAA,EAAA;AAA4B,IAAA,iBAAA,GAAA,kCAAA;AAAoB,IAAA,uBAAA;;;;;AARxD,IAAA,yBAAA,GAAA,OAAA,EAAA,EAA0B,GAAA,OAAA,EAAA,EACE,GAAA,OAAA,EAAA,EACE,GAAA,QAAA;AAChB,IAAA,iBAAA,CAAA;AAAsB,IAAA,uBAAA,EAAS;AAEzC,IAAA,yBAAA,GAAA,OAAA,EAAA,EAA6B,GAAA,QAAA,EAAA;AACD,IAAA,iBAAA,CAAA;AAA0C,IAAA,uBAAA;AACpE,IAAA,qBAAA,GAAA,+GAAA,GAAA,GAAA,QAAA,EAAA;AAGF,IAAA,uBAAA,EAAM;AAER,IAAA,yBAAA,GAAA,OAAA,EAAA,EAA8B,IAAA,QAAA,EAAA;AACL,IAAA,iBAAA,EAAA;AAAyB,IAAA,uBAAA,EAAO;AAEzD,IAAA,yBAAA,IAAA,OAAA,EAAA,EAA2B,IAAA,OAAA,EAAA;AACD,IAAA,iBAAA,EAAA;AAAuC,IAAA,uBAAA;AAC/D,IAAA,yBAAA,IAAA,OAAA,EAAA,EAAyB,IAAA,QAAA;AAAQ,IAAA,iBAAA,EAAA;AAAmC,IAAA,uBAAA,EAAS,EAAM,EAC/E;;;;;AAfM,IAAA,oBAAA,CAAA;AAAA,IAAA,4BAAA,SAAA,UAAA;AAGkB,IAAA,oBAAA,CAAA;AAAA,IAAA,6BAAA,YAAA,SAAA,oBAAA,OAAA,EAAA;AAC1B,IAAA,oBAAA;AAAA,IAAA,wBAAA,SAAA,kBAAA,IAAA,EAAA;AAMqB,IAAA,oBAAA,CAAA;AAAA,IAAA,6BAAA,YAAA,SAAA,UAAA,EAAA;AAGC,IAAA,oBAAA,CAAA;AAAA,IAAA,6BAAA,IAAA,OAAA,YAAA,SAAA,YAAA,GAAA,IAAA;AACS,IAAA,oBAAA,CAAA;AAAA,IAAA,4BAAA,OAAA,YAAA,SAAA,UAAA,CAAA;;;;;AAnBzC,IAAA,yBAAA,GAAA,OAAA,EAAA;AACE,IAAA,2BAAA,GAAA,iGAAA,IAAA,GAAA,OAAA,IAAA,UAAA;AAsBF,IAAA,uBAAA;;;;AAtBE,IAAA,oBAAA;AAAA,IAAA,qBAAA,SAAA,cAAA;;;;;AAwBF,IAAA,yBAAA,GAAA,KAAA,EAAA;AAAuB,IAAA,iBAAA,GAAA,6CAAA;AAAwC,IAAA,uBAAA;;;;;;AAS7D,IAAA,yBAAA,GAAA,UAAA,EAAA;AAEE,IAAA,qBAAA,SAAA,SAAA,oHAAA;AAAA,MAAA,wBAAA,GAAA;AAAA,YAAA,WAAA,wBAAA,CAAA,EAAA;AAAA,YAAA,SAAA,wBAAA,CAAA;AAAA,aAAA,sBAAS,OAAA,kBAAA,SAAA,IAA4B,UAAU,CAAC;IAAA,CAAA;AAEhD,IAAA,iBAAA,GAAA,mBAAA;AACF,IAAA,uBAAA;;;;;;AAIA,IAAA,yBAAA,GAAA,UAAA,EAAA;AAEE,IAAA,qBAAA,SAAA,SAAA,oHAAA;AAAA,MAAA,wBAAA,GAAA;AAAA,YAAA,WAAA,wBAAA,CAAA,EAAA;AAAA,YAAA,SAAA,wBAAA,CAAA;AAAA,aAAA,sBAAS,OAAA,kBAAA,SAAA,IAA4B,eAAe,CAAC;IAAA,CAAA;AAErD,IAAA,iBAAA,GAAA,yBAAA;AACF,IAAA,uBAAA;;;;;;AAIA,IAAA,yBAAA,GAAA,UAAA,EAAA;AAEE,IAAA,qBAAA,SAAA,SAAA,oHAAA;AAAA,MAAA,wBAAA,GAAA;AAAA,YAAA,WAAA,wBAAA,CAAA,EAAA;AAAA,YAAA,SAAA,wBAAA,CAAA;AAAA,aAAA,sBAAS,OAAA,kBAAA,SAAA,IAA4B,UAAU,CAAC;IAAA,CAAA;AAEhD,IAAA,iBAAA,GAAA,yBAAA;AACF,IAAA,uBAAA;;;;;;AAIA,IAAA,yBAAA,GAAA,UAAA,EAAA;AAEE,IAAA,qBAAA,SAAA,SAAA,oHAAA;AAAA,MAAA,wBAAA,GAAA;AAAA,YAAA,WAAA,wBAAA,CAAA,EAAA;AAAA,YAAA,SAAA,wBAAA,CAAA;AAAA,aAAA,sBAAS,OAAA,kBAAA,SAAA,IAA4B,QAAQ,CAAC;IAAA,CAAA;AAE9C,IAAA,iBAAA,GAAA,kCAAA;AACF,IAAA,uBAAA;;;;;;AAIA,IAAA,yBAAA,GAAA,UAAA,EAAA;AAEE,IAAA,qBAAA,SAAA,SAAA,oHAAA;AAAA,MAAA,wBAAA,IAAA;AAAA,YAAA,WAAA,wBAAA,CAAA,EAAA;AAAA,YAAA,SAAA,wBAAA,CAAA;AAAA,aAAA,sBAAS,OAAA,kBAAA,SAAA,IAA4B,SAAS,CAAC;IAAA,CAAA;AAE/C,IAAA,iBAAA,GAAA,kBAAA;AACF,IAAA,uBAAA;;;;;AA1GR,IAAA,yBAAA,GAAA,OAAA,EAAA,EAA2B,GAAA,OAAA,EAAA,EAEA,GAAA,IAAA;AACnB,IAAA,iBAAA,GAAA,iCAAA;AAAwB,IAAA,uBAAA;AAC5B,IAAA,yBAAA,GAAA,OAAA,EAAA,EAAuB,GAAA,OAAA,EAAA,EACE,GAAA,QAAA;AACb,IAAA,iBAAA,GAAA,kBAAA;AAAU,IAAA,uBAAA;AAAU,IAAA,iBAAA,CAAA;AAC9B,IAAA,uBAAA;AACA,IAAA,yBAAA,GAAA,OAAA,EAAA,EAAuB,IAAA,QAAA;AACb,IAAA,iBAAA,IAAA,cAAA;AAAY,IAAA,uBAAA;AAAU,IAAA,iBAAA,EAAA;AAChC,IAAA,uBAAA;AACA,IAAA,yBAAA,IAAA,OAAA,EAAA,EAAuB,IAAA,QAAA;AACb,IAAA,iBAAA,IAAA,YAAA;AAAU,IAAA,uBAAA;AAAU,IAAA,iBAAA,EAAA;AAC9B,IAAA,uBAAA;AACA,IAAA,yBAAA,IAAA,OAAA,EAAA,EAAuB,IAAA,QAAA;AACb,IAAA,iBAAA,IAAA,kBAAA;AAAgB,IAAA,uBAAA;AAAU,IAAA,iBAAA,EAAA;AACpC,IAAA,uBAAA;AACA,IAAA,qBAAA,IAAA,2FAAA,GAAA,GAAA,OAAA,EAAA;AAKF,IAAA,uBAAA,EAAM;AAMR,IAAA,yBAAA,IAAA,OAAA,EAAA,EAA8B,IAAA,IAAA;AACxB,IAAA,iBAAA,IAAA,uCAAA;AAAsB,IAAA,uBAAA;AAC1B,IAAA,qBAAA,IAAA,2FAAA,GAAA,GAAA,OAAA,EAAA,EAA+D,IAAA,2FAAA,GAAA,GAAA,KAAA,EAAA;AA4BjE,IAAA,uBAAA;AAGA,IAAA,yBAAA,IAAA,OAAA,EAAA,EAA2B,IAAA,IAAA;AACrB,IAAA,iBAAA,IAAA,gBAAA;AAAS,IAAA,uBAAA;AACb,IAAA,yBAAA,IAAA,OAAA,EAAA;AACE,IAAA,qBAAA,IAAA,2FAAA,GAAA,GAAA,UAAA,EAAA,EAAqC,IAAA,2FAAA,GAAA,GAAA,UAAA,EAAA,EASA,IAAA,2FAAA,GAAA,GAAA,UAAA,EAAA,EASH,IAAA,2FAAA,GAAA,GAAA,UAAA,EAAA,EASG,IAAA,2FAAA,GAAA,GAAA,UAAA,EAAA;AAiBvC,IAAA,uBAAA,EAAM,EACF;;;;;AAvG4B,IAAA,oBAAA,CAAA;AAAA,IAAA,6BAAA,KAAA,SAAA,WAAA,GAAA;AAGE,IAAA,oBAAA,CAAA;AAAA,IAAA,6BAAA,KAAA,SAAA,gBAAA,GAAA;AAGF,IAAA,oBAAA,CAAA;AAAA,IAAA,6BAAA,KAAA,SAAA,oBAAA,oBAAA,GAAA;AAGM,IAAA,oBAAA,CAAA;AAAA,IAAA,6BAAA,KAAA,OAAA,YAAA,SAAA,cAAA,GAAA,GAAA;AAEpC,IAAA,oBAAA;AAAA,IAAA,wBAAA,SAAA,qBAAA,KAAA,EAAA;AAaF,IAAA,oBAAA,CAAA;AAAA,IAAA,wBAAA,SAAA,kBAAA,SAAA,eAAA,SAAA,IAAA,KAAA,EAAA;AAkCE,IAAA,oBAAA,CAAA;AAAA,IAAA,wBAAA,OAAA,gBAAA,SAAA,MAAA,IAAA,KAAA,EAAA;AASA,IAAA,oBAAA;AAAA,IAAA,wBAAA,OAAA,gBAAA,SAAA,MAAA,IAAA,KAAA,EAAA;AASA,IAAA,oBAAA;AAAA,IAAA,wBAAA,OAAA,aAAA,SAAA,MAAA,IAAA,KAAA,EAAA;AASA,IAAA,oBAAA;AAAA,IAAA,wBAAA,OAAA,gBAAA,SAAA,MAAA,IAAA,KAAA,EAAA;AASA,IAAA,oBAAA;AAAA,IAAA,wBAAA,OAAA,eAAA,SAAA,MAAA,IAAA,KAAA,EAAA;;;;;;AA7HV,IAAA,yBAAA,GAAA,OAAA,EAAA,EAAwB,GAAA,OAAA,EAAA;AAEI,IAAA,qBAAA,SAAA,SAAA,mFAAA;AAAA,YAAA,WAAA,wBAAA,GAAA,EAAA;AAAA,YAAA,SAAA,wBAAA,CAAA;AAAA,aAAA,sBAAS,OAAA,mBAAA,SAAA,EAAA,CAA4B;IAAA,CAAA;AAC7D,IAAA,yBAAA,GAAA,OAAA,EAAA,EAAwB,GAAA,OAAA,EAAA,EACO,GAAA,QAAA;AACnB,IAAA,iBAAA,CAAA;AAAwB,IAAA,uBAAA,EAAS;AAE3C,IAAA,yBAAA,GAAA,OAAA,EAAA,EAAwB,GAAA,QAAA,EAAA;AACG,IAAA,iBAAA,CAAA;AAAuC,IAAA,uBAAA;AAChE,IAAA,yBAAA,GAAA,QAAA,EAAA;AAA0B,IAAA,iBAAA,EAAA;AAAwC,IAAA,uBAAA,EAAO,EACrE;AAGR,IAAA,yBAAA,IAAA,OAAA,EAAA,EAA0B,IAAA,QAAA,EAAA;AAEtB,IAAA,iBAAA,EAAA;AACF,IAAA,uBAAA;AACA,IAAA,yBAAA,IAAA,QAAA,EAAA;AACE,IAAA,iBAAA,IAAA,UAAA;AACF,IAAA,uBAAA,EAAO,EACH;AAIR,IAAA,qBAAA,IAAA,4EAAA,IAAA,IAAA,OAAA,EAAA;AAiHF,IAAA,uBAAA;;;;;AApIgB,IAAA,oBAAA,CAAA;AAAA,IAAA,6BAAA,cAAA,SAAA,WAAA,EAAA;AAGiB,IAAA,oBAAA,CAAA;AAAA,IAAA,6BAAA,cAAA,OAAA,WAAA,SAAA,YAAA,GAAA,EAAA;AACC,IAAA,oBAAA,CAAA;AAAA,IAAA,6BAAA,cAAA,OAAA,YAAA,SAAA,YAAA,GAAA,EAAA;AAKD,IAAA,oBAAA,CAAA;AAAA,IAAA,qBAAA,OAAA,eAAA,SAAA,MAAA,CAAA;AACzB,IAAA,oBAAA;AAAA,IAAA,6BAAA,KAAA,OAAA,cAAA,SAAA,MAAA,GAAA,GAAA;AAEwB,IAAA,oBAAA;AAAA,IAAA,sBAAA,YAAA,OAAA,gBAAA,SAAA,EAAA,CAAA;AAO9B,IAAA,oBAAA,CAAA;AAAA,IAAA,wBAAA,OAAA,gBAAA,SAAA,EAAA,IAAA,KAAA,EAAA;;;;;AAzBJ,IAAA,2BAAA,GAAA,6DAAA,IAAA,GAAA,OAAA,IAAA,UAAA;;;;AAAA,IAAA,qBAAA,OAAA,gBAAA,CAAiB;;;;;;AAkKb,IAAA,yBAAA,GAAA,UAAA,EAAA;AAIE,IAAA,qBAAA,SAAA,SAAA,sFAAA;AAAA,YAAA,aAAA,wBAAA,IAAA,EAAA;AAAA,YAAA,SAAA,wBAAA,CAAA;AAAA,aAAA,sBAAS,OAAA,aAAA,aAAsB,CAAC,CAAC;IAAA,CAAA;AAEjC,IAAA,iBAAA,CAAA;AACF,IAAA,uBAAA;;;;;AALE,IAAA,sBAAA,eAAA,aAAA,MAAA,OAAA,YAAA,CAAA,EAAkD,uBAAA,aAAA,MAAA,OAAA,YAAA,CAAA;AAIlD,IAAA,oBAAA;AAAA,IAAA,6BAAA,KAAA,aAAA,GAAA,GAAA;;;;;;AAvBR,IAAA,yBAAA,GAAA,OAAA,EAAA,EAAkC,GAAA,OAAA,EAAA;AAE9B,IAAA,iBAAA,CAAA;AAGF,IAAA,uBAAA;AAEA,IAAA,yBAAA,GAAA,OAAA,EAAA,EAAwB,GAAA,UAAA,EAAA;AAIpB,IAAA,qBAAA,SAAA,SAAA,gFAAA;AAAA,MAAA,wBAAA,IAAA;AAAA,YAAA,SAAA,wBAAA,CAAA;AAAA,aAAA,sBAAS,OAAA,aAAa,OAAA,YAAA,IAAgB,CAAC,CAAC;IAAA,CAAA;AAExC,IAAA,iBAAA,GAAA,0BAAA;AACF,IAAA,uBAAA;AAEA,IAAA,2BAAA,GAAA,6DAAA,GAAA,GAAA,UAAA,IAAA,gCAAA;AAWA,IAAA,yBAAA,GAAA,UAAA,EAAA;AAGE,IAAA,qBAAA,SAAA,SAAA,gFAAA;AAAA,MAAA,wBAAA,IAAA;AAAA,YAAA,SAAA,wBAAA,CAAA;AAAA,aAAA,sBAAS,OAAA,aAAa,OAAA,YAAA,IAAgB,CAAC,CAAC;IAAA,CAAA;AAExC,IAAA,iBAAA,GAAA,kBAAA;AACF,IAAA,uBAAA,EAAS,EACL;;;;AAhCJ,IAAA,oBAAA,CAAA;AAAA,IAAA,6BAAA,gBAAA,OAAA,YAAA,IAAA,KAAA,OAAA,SAAA,IAAA,GAAA,UAAA,OAAA,KAAA,IAAA,OAAA,YAAA,IAAA,OAAA,SAAA,GAAA,OAAA,eAAA,EAAA,MAAA,GAAA,SAAA,OAAA,eAAA,EAAA,QAAA,aAAA;AAQE,IAAA,oBAAA,CAAA;AAAA,IAAA,qBAAA,YAAA,OAAA,YAAA,MAAA,CAAA;AAMF,IAAA,oBAAA,CAAA;AAAA,IAAA,qBAAA,0BAAA,GAAA,GAAA,EAAA,YAAe,OAAA,WAAA,CAAY,CAAC;AAa1B,IAAA,oBAAA,CAAA;AAAA,IAAA,qBAAA,YAAA,OAAA,YAAA,MAAA,OAAA,WAAA,CAAA;;;;;AAzLR,IAAA,yBAAA,GAAA,OAAA,EAAA;AACE,IAAA,qBAAA,GAAA,uDAAA,GAAA,GAAA,OAAA,EAAA,EAAsC,GAAA,uDAAA,GAAA,CAAA;AAuJxC,IAAA,uBAAA;AAGA,IAAA,qBAAA,GAAA,uDAAA,IAAA,GAAA,OAAA,EAAA;;;;AA1JE,IAAA,oBAAA;AAAA,IAAA,wBAAA,OAAA,gBAAA,EAAA,WAAA,IAAA,IAAA,CAAA;AA0JF,IAAA,oBAAA,CAAA;AAAA,IAAA,wBAAA,OAAA,WAAA,IAAA,IAAA,IAAA,EAAA;;;ADnNE,IAAO,kBAAP,MAAO,iBAAe;EAyChB;EACA;;EAxCV,SAAS,OAA8B,CAAA,CAAE;EACzC,YAAY,OAAO,KAAK;EACxB,QAAQ,OAAO,EAAE;EACjB,aAAa,OAAO,EAAE;EACtB,iBAAiB,OAAO,EAAE;EAC1B,cAAc,OAAO,CAAC;EACtB,WAAW,OAAO,EAAE;EACpB,kBAAkB,OAAsB,IAAI;;EAG5C,iBAAiB,SAAS,MAAK;AAC7B,UAAM,SAAS,KAAK,OAAM;AAC1B,UAAM,SAAS,KAAK,WAAU,EAAG,YAAW;AAC5C,UAAM,SAAS,KAAK,eAAc;AAElC,WAAO,OAAO,OAAO,WAAQ;AAC3B,YAAM,gBAAgB,CAAC,UACrB,MAAM,UAAU,YAAW,EAAG,SAAS,MAAM,KAC7C,MAAM,gBAAgB,YAAW,EAAG,SAAS,MAAM;AAErD,YAAM,gBAAgB,CAAC,UAAU,MAAM,WAAW;AAElD,aAAO,iBAAiB;IAC1B,CAAC;EACH,CAAC;EAED,kBAAkB,SAAS,MAAK;AAC9B,UAAM,WAAW,KAAK,eAAc;AACpC,UAAM,OAAO,KAAK,YAAW;AAC7B,UAAM,OAAO,KAAK,SAAQ;AAC1B,UAAM,SAAS,OAAO,KAAK;AAC3B,WAAO,SAAS,MAAM,OAAO,QAAQ,IAAI;EAC3C,CAAC;EAED,aAAa,SAAS,MAAK;AACzB,WAAO,KAAK,KAAK,KAAK,eAAc,EAAG,SAAS,KAAK,SAAQ,CAAE;EACjE,CAAC;EAED,YACU,iBACA,aAAwB;AADxB,SAAA,kBAAA;AACA,SAAA,cAAA;EACP;EAEH,WAAQ;AACN,YAAQ,IAAI,gDAAsC;AAClD,SAAK,WAAU;EACjB;EAEA,aAAU;AACR,YAAQ,IAAI,+CAAqC;AACjD,SAAK,UAAU,IAAI,IAAI;AACvB,SAAK,MAAM,IAAI,EAAE;AAEjB,UAAM,cAAc,KAAK,YAAY,eAAc;AACnD,YAAQ,IAAI,4CAA4B,WAAW;AAEnD,QAAI,CAAC,aAAa,IAAI;AACpB,cAAQ,MAAM,sCAA8B;AAC5C,WAAK,MAAM,IAAI,6BAA0B;AACzC,WAAK,UAAU,IAAI,KAAK;AACxB;IACF;AAEA,YAAQ,IAAI,0DAAmD,YAAY,EAAE;AAC7E,SAAK,gBAAgB,0BAA0B,YAAY,EAAE,EAAE,UAAU;MACvE,MAAM,CAAC,WAAU;AACf,gBAAQ,IAAI,wDAAwC,MAAM;AAC1D,aAAK,OAAO,IAAI,MAAM;AACtB,aAAK,UAAU,IAAI,KAAK;MAC1B;MACA,OAAO,CAAC,UAAS;AACf,gBAAQ,MAAM,mDAA8C,KAAK;AACjE,aAAK,MAAM,IAAI,yCAAyC;AACxD,aAAK,UAAU,IAAI,KAAK;MAC1B;KACD;EACH;;EAGA,WAAW,MAAmB;AAC5B,WAAO,IAAI,KAAK,IAAI,EAAE,mBAAmB,SAAS;MAChD,KAAK;MACL,OAAO;MACP,MAAM;MACN,MAAM;MACN,QAAQ;KACT;EACH;EAEA,YAAY,OAAa;AACvB,WAAO,IAAI,KAAK,aAAa,SAAS;MACpC,OAAO;MACP,UAAU;KACX,EAAE,OAAO,KAAK;EACjB;EAEA,eAAe,QAAc;AAC3B,UAAM,gBAA2C;MAC/C,YAAY;MACZ,aAAa;MACb,YAAY;MACZ,aAAa;MACb,iBAAiB;MACjB,SAAS;MACT,YAAY;MACZ,UAAU;MACV,WAAW;MACX,WAAW;;AAEb,WAAO,cAAc,MAAM,KAAK;EAClC;EAEA,cAAc,QAAc;AAC1B,UAAM,cAAyC;MAC7C,YAAY;MACZ,aAAa;MACb,YAAY;MACZ,aAAa;MACb,iBAAiB;MACjB,SAAS;MACT,YAAY;MACZ,UAAU;MACV,WAAW;MACX,WAAW;;AAEb,WAAO,YAAY,MAAM,KAAK;EAChC;;EAGA,mBAAmB,SAAe;AAChC,UAAM,UAAU,KAAK,gBAAe;AACpC,SAAK,gBAAgB,IAAI,YAAY,UAAU,OAAO,OAAO;EAC/D;EAEA,gBAAgB,SAAe;AAC7B,WAAO,KAAK,gBAAe,MAAO;EACpC;EAEA,kBAAkB,SAAiB,WAAiB;AAClD,UAAM,UAAkD;MACtD,QAAQ;MACR,oBAAoB,cAAc,WAAW,MAAM,KAAK,IAAG,CAAE,KAAK;;AAGpE,SAAK,gBAAgB,gCAAgC,SAAS,OAAO,EAAE,UAAU;MAC/E,MAAM,MAAK;AACT,gBAAQ,IAAI,yCAAiC;AAC7C,aAAK,WAAU;MACjB;MACA,OAAO,CAAC,UAAS;AACf,gBAAQ,MAAM,sDAA8C,KAAK;AACjE,aAAK,MAAM,IAAI,4CAAyC;MAC1D;KACD;EACH;;EAGA,aAAa,MAAY;AACvB,QAAI,QAAQ,KAAK,QAAQ,KAAK,WAAU,GAAI;AAC1C,WAAK,YAAY,IAAI,IAAI;IAC3B;EACF;;EAGA,eAAe,OAAY;AACzB,UAAM,SAAS,MAAM;AACrB,SAAK,WAAW,IAAI,OAAO,KAAK;AAChC,SAAK,YAAY,IAAI,CAAC;EACxB;EAEA,eAAe,OAAY;AACzB,UAAM,SAAS,MAAM;AACrB,SAAK,eAAe,IAAI,OAAO,KAAK;AACpC,SAAK,YAAY,IAAI,CAAC;EACxB;EAEA,eAAY;AACV,SAAK,WAAW,IAAI,EAAE;AACtB,SAAK,eAAe,IAAI,EAAE;AAC1B,SAAK,YAAY,IAAI,CAAC;EACxB;;EAGA,gBAAgB,QAAc;AAC5B,WAAO,WAAW,eAAe,WAAW;EAC9C;EAEA,gBAAgB,QAAc;AAC5B,WAAO,WAAW,eAAe,WAAW;EAC9C;EAEA,aAAa,QAAc;AACzB,WAAO,WAAW,mBAAmB,WAAW;EAClD;EAEA,gBAAgB,QAAc;AAC5B,WAAO,WAAW;EACpB;EAEA,eAAe,QAAc;AAC3B,WAAO,CAAC,aAAa,YAAY,aAAa,UAAU,EAAE,SAAS,MAAM;EAC3E;;EAGA,OAAO;;qCA9MI,kBAAe,4BAAA,eAAA,GAAA,4BAAA,WAAA,CAAA;EAAA;yEAAf,kBAAe,WAAA,CAAA,CAAA,YAAA,CAAA,GAAA,OAAA,IAAA,MAAA,GAAA,QAAA,CAAA,CAAA,GAAA,kBAAA,GAAA,CAAA,GAAA,eAAA,GAAA,CAAA,GAAA,gBAAA,GAAA,CAAA,GAAA,OAAA,iBAAA,GAAA,OAAA,GAAA,CAAA,GAAA,iBAAA,GAAA,CAAA,GAAA,aAAA,GAAA,CAAA,GAAA,cAAA,GAAA,CAAA,OAAA,QAAA,GAAA,CAAA,MAAA,UAAA,QAAA,QAAA,eAAA,8BAAA,GAAA,gBAAA,GAAA,SAAA,OAAA,GAAA,CAAA,OAAA,QAAA,GAAA,CAAA,MAAA,UAAA,GAAA,gBAAA,GAAA,UAAA,OAAA,GAAA,CAAA,SAAA,EAAA,GAAA,CAAA,SAAA,UAAA,GAAA,CAAA,SAAA,UAAA,GAAA,CAAA,SAAA,eAAA,GAAA,CAAA,SAAA,OAAA,GAAA,CAAA,SAAA,UAAA,GAAA,CAAA,SAAA,QAAA,GAAA,CAAA,SAAA,SAAA,GAAA,CAAA,GAAA,OAAA,yBAAA,GAAA,OAAA,GAAA,CAAA,GAAA,mBAAA,GAAA,CAAA,GAAA,SAAA,cAAA,GAAA,CAAA,GAAA,SAAA,GAAA,CAAA,GAAA,aAAA,GAAA,CAAA,GAAA,WAAA,GAAA,CAAA,GAAA,sBAAA,GAAA,CAAA,GAAA,OAAA,aAAA,GAAA,CAAA,GAAA,OAAA,eAAA,GAAA,OAAA,GAAA,CAAA,GAAA,YAAA,GAAA,CAAA,GAAA,gBAAA,GAAA,OAAA,GAAA,CAAA,GAAA,YAAA,GAAA,CAAA,GAAA,iBAAA,GAAA,CAAA,GAAA,YAAA,GAAA,CAAA,GAAA,YAAA,GAAA,CAAA,GAAA,aAAA,GAAA,CAAA,GAAA,cAAA,GAAA,CAAA,GAAA,cAAA,GAAA,CAAA,GAAA,aAAA,GAAA,CAAA,GAAA,eAAA,GAAA,CAAA,GAAA,aAAA,GAAA,CAAA,GAAA,WAAA,GAAA,CAAA,GAAA,WAAA,GAAA,CAAA,GAAA,kBAAA,GAAA,CAAA,GAAA,eAAA,GAAA,CAAA,GAAA,aAAA,GAAA,CAAA,GAAA,eAAA,GAAA,CAAA,GAAA,iBAAA,GAAA,CAAA,GAAA,OAAA,aAAA,GAAA,CAAA,GAAA,OAAA,aAAA,GAAA,CAAA,GAAA,OAAA,UAAA,GAAA,CAAA,GAAA,OAAA,YAAA,GAAA,CAAA,GAAA,cAAA,GAAA,CAAA,GAAA,cAAA,GAAA,CAAA,GAAA,cAAA,GAAA,CAAA,GAAA,iBAAA,GAAA,CAAA,GAAA,aAAA,GAAA,CAAA,GAAA,eAAA,GAAA,CAAA,GAAA,kBAAA,GAAA,CAAA,GAAA,UAAA,GAAA,CAAA,GAAA,eAAA,GAAA,CAAA,GAAA,YAAA,GAAA,CAAA,GAAA,aAAA,GAAA,CAAA,GAAA,OAAA,eAAA,GAAA,OAAA,GAAA,CAAA,GAAA,OAAA,eAAA,GAAA,OAAA,GAAA,CAAA,GAAA,OAAA,YAAA,GAAA,OAAA,GAAA,CAAA,GAAA,OAAA,cAAA,GAAA,OAAA,GAAA,CAAA,GAAA,iBAAA,GAAA,CAAA,GAAA,YAAA,GAAA,CAAA,GAAA,OAAA,uBAAA,GAAA,SAAA,UAAA,GAAA,CAAA,GAAA,OAAA,GAAA,eAAA,qBAAA,GAAA,CAAA,GAAA,OAAA,GAAA,OAAA,CAAA,GAAA,UAAA,SAAA,yBAAA,IAAA,KAAA;AAAA,QAAA,KAAA,GAAA;ACd5B,MAAA,yBAAA,GAAA,OAAA,CAAA,EAA8B,GAAA,OAAA,CAAA,EAED,GAAA,IAAA;AACrB,MAAA,iBAAA,GAAA,iCAAA;AAAwB,MAAA,uBAAA;AAC5B,MAAA,yBAAA,GAAA,OAAA,CAAA,EAA4B,GAAA,UAAA,CAAA;AACQ,MAAA,qBAAA,SAAA,SAAA,mDAAA;AAAA,eAAS,IAAA,WAAA;MAAY,CAAA;AACrD,MAAA,iBAAA,GAAA,wBAAA;AACF,MAAA,uBAAA,EAAS,EACL;AAIR,MAAA,yBAAA,GAAA,OAAA,CAAA,EAA6B,GAAA,OAAA,CAAA,EACF,GAAA,OAAA,CAAA,EACG,IAAA,SAAA,CAAA;AACJ,MAAA,iBAAA,IAAA,uBAAA;AAAc,MAAA,uBAAA;AAClC,MAAA,yBAAA,IAAA,SAAA,CAAA;AAME,MAAA,qBAAA,SAAA,SAAA,iDAAA,QAAA;AAAA,eAAS,IAAA,eAAA,MAAA;MAAsB,CAAA;AANjC,MAAA,uBAAA,EAOE;AAGJ,MAAA,yBAAA,IAAA,OAAA,CAAA,EAA0B,IAAA,SAAA,CAAA;AACJ,MAAA,iBAAA,IAAA,mBAAA;AAAU,MAAA,uBAAA;AAC9B,MAAA,yBAAA,IAAA,UAAA,EAAA;AAIE,MAAA,qBAAA,UAAA,SAAA,mDAAA,QAAA;AAAA,eAAU,IAAA,eAAA,MAAA;MAAsB,CAAA;AAEhC,MAAA,yBAAA,IAAA,UAAA,EAAA;AAAiB,MAAA,iBAAA,IAAA,kBAAA;AAAgB,MAAA,uBAAA;AACjC,MAAA,yBAAA,IAAA,UAAA,EAAA;AAAyB,MAAA,iBAAA,IAAA,UAAA;AAAQ,MAAA,uBAAA;AACjC,MAAA,yBAAA,IAAA,UAAA,EAAA;AAAyB,MAAA,iBAAA,IAAA,aAAA;AAAQ,MAAA,uBAAA;AACjC,MAAA,yBAAA,IAAA,UAAA,EAAA;AAA8B,MAAA,iBAAA,IAAA,mBAAA;AAAc,MAAA,uBAAA;AAC5C,MAAA,yBAAA,IAAA,UAAA,EAAA;AAAsB,MAAA,iBAAA,IAAA,UAAA;AAAK,MAAA,uBAAA;AAC3B,MAAA,yBAAA,IAAA,UAAA,EAAA;AAAyB,MAAA,iBAAA,IAAA,gBAAA;AAAQ,MAAA,uBAAA;AACjC,MAAA,yBAAA,IAAA,UAAA,EAAA;AAAuB,MAAA,iBAAA,IAAA,WAAA;AAAM,MAAA,uBAAA;AAC7B,MAAA,yBAAA,IAAA,UAAA,EAAA;AAAwB,MAAA,iBAAA,IAAA,YAAA;AAAO,MAAA,uBAAA,EAAS,EACjC;AAGX,MAAA,yBAAA,IAAA,OAAA,CAAA,EAA0B,IAAA,UAAA,EAAA;AACkB,MAAA,qBAAA,SAAA,SAAA,oDAAA;AAAA,eAAS,IAAA,aAAA;MAAc,CAAA;AAC/D,MAAA,iBAAA,IAAA,mCAAA;AACF,MAAA,uBAAA,EAAS,EACL,EACF;AAIR,MAAA,qBAAA,IAAA,yCAAA,GAAA,GAAA,OAAA,EAAA,EAAmB,IAAA,yCAAA,GAAA,GAAA,OAAA,EAAA,EAQJ,IAAA,yCAAA,GAAA,CAAA;AA0MjB,MAAA,uBAAA;;;AAnPU,MAAA,oBAAA,EAAA;AAAA,MAAA,qBAAA,SAAA,IAAA,WAAA,CAAA;AAUA,MAAA,oBAAA,CAAA;AAAA,MAAA,qBAAA,SAAA,IAAA,eAAA,CAAA;AAuBR,MAAA,oBAAA,EAAA;AAAA,MAAA,wBAAA,IAAA,UAAA,IAAA,KAAA,EAAA;AAQA,MAAA,oBAAA;AAAA,MAAA,wBAAA,IAAA,MAAA,IAAA,KAAA,EAAA;AAOA,MAAA,oBAAA;AAAA,MAAA,wBAAA,CAAA,IAAA,UAAA,KAAA,CAAA,IAAA,MAAA,IAAA,KAAA,EAAA;;oBD3DU,cAAc,aAAW,gBAAA,4BAAA,GAAA,QAAA,CAAA,2nSAAA,EAAA,CAAA;;;sEAIxB,iBAAe,CAAA;UAP3B;uBACW,cAAY,YACV,MAAI,SACP,CAAC,cAAc,WAAW,GAAC,UAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;GAAA,QAAA,CAAA,o4OAAA,EAAA,CAAA;;;;6EAIzB,iBAAe,EAAA,WAAA,mBAAA,UAAA,iDAAA,YAAA,GAAA,CAAA;AAAA,GAAA;", "names": ["StatutCommande", "StatutCommandeFournisseur", "StatutLivraison"]}
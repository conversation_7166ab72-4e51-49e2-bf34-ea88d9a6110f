# 🧪 Guide de Test des Promotions

## 🚀 Démarrage Rapide

### **1. <PERSON><PERSON><PERSON><PERSON> les Services**

```bash
# Backend API
cd backend/WebApiPfe
dotnet run
# ➜ http://localhost:5014

# Frontend Client  
cd frontend-client
ng serve --port 4200
# ➜ http://localhost:4200

# Frontend Fournisseur
cd frontend-fournisseur  
ng serve --port 58548
# ➜ http://localhost:58548
```

---

## 🔧 **Tests API avec Swagger**

### **Accès Swagger UI**
🌐 **URL :** http://localhost:5014/swagger

### **Authentification Admin**
```json
{
  "email": "<EMAIL>",
  "password": "Admin123!"
}
```

### **Authentification Fournisseur**
```json
{
  "email": "<EMAIL>", 
  "password": "Fournisseur123!"
}
```

---

## 📋 **Scénarios de Test**

### **🎯 Test 1: Création de Promotion**

1. **Se connecter** via Swagger avec les credentials admin
2. **Utiliser l'endpoint** `POST /api/promotion-gestion`
3. **Payload de test :**

```json
{
  "code": "WELCOME10",
  "nom": "Promotion de bienvenue",
  "description": "10% de réduction pour les nouveaux clients",
  "type": 1,
  "valeur": 10,
  "dateDebut": "2025-07-20T00:00:00Z",
  "dateFin": "2025-12-31T23:59:59Z",
  "utilisationsMax": 100,
  "montantMinimum": 50
}
```

**Types disponibles :**
- `1` = Pourcentage
- `2` = Montant fixe  
- `3` = Outlet

---

### **🎯 Test 2: Validation de Code Promo**

1. **Endpoint :** `POST /api/promotion-gestion/validate`
2. **Payload :**

```json
{
  "code": "WELCOME10",
  "montantCommande": 75.00,
  "produitId": 1,
  "clientId": 1
}
```

**Résultats attendus :**
- ✅ Code valide → Détails de la promotion
- ❌ Code invalide → Erreur 400
- ❌ Montant insuffisant → Erreur 400

---

### **🎯 Test 3: Promotions Applicables**

1. **Endpoint :** `GET /api/promotion-gestion/applicable`
2. **Paramètres :**
   - `produitId=1`
   - `montantCommande=100`

**Résultat :** Liste des promotions applicables au produit/montant

---

### **🎯 Test 4: Statistiques (Admin)**

1. **Endpoint :** `GET /api/promotion-gestion/statistiques`
2. **Authentification :** Admin requis

**Résultat :** Statistiques d'utilisation des promotions

---

## 🖥️ **Tests Interface Utilisateur**

### **Frontend Client (Port 4200)**

**Scénarios :**
1. **Navigation** → Parcourir les produits
2. **Panier** → Ajouter des produits
3. **Code Promo** → Appliquer un code de réduction
4. **Validation** → Vérifier le calcul de remise

### **Frontend Fournisseur (Port 58548)**

**Scénarios :**
1. **Connexion** → Se connecter comme fournisseur
2. **Promotions** → Accéder à la gestion des promotions
3. **Création** → Créer une nouvelle promotion
4. **Modification** → Éditer une promotion existante
5. **Statistiques** → Consulter les performances

---

## 🔍 **Vérifications Techniques**

### **Base de Données**
```sql
-- Vérifier les promotions créées
SELECT * FROM PromotionGestions;

-- Vérifier les utilisations
SELECT * FROM UtilisationPromotions;
```

### **Logs Backend**
```bash
# Surveiller les logs en temps réel
cd backend/WebApiPfe
dotnet run --verbosity detailed
```

### **Console Navigateur**
- **F12** → Console
- Vérifier les erreurs JavaScript
- Surveiller les appels API

---

## 🐛 **Dépannage**

### **Erreurs Communes**

1. **401 Unauthorized**
   - ✅ Vérifier l'authentification
   - ✅ Token JWT valide

2. **404 Not Found**
   - ✅ Vérifier l'URL de l'endpoint
   - ✅ Route correcte : `/api/promotion-gestion`

3. **400 Bad Request**
   - ✅ Valider le format JSON
   - ✅ Vérifier les types de données

4. **CORS Errors**
   - ✅ Vérifier la configuration CORS backend
   - ✅ Ports autorisés : 4200, 58548

### **Commandes Utiles**

```bash
# Redémarrer le backend
cd backend/WebApiPfe && dotnet run

# Redémarrer frontend client
cd frontend-client && ng serve --port 4200

# Redémarrer frontend fournisseur  
cd frontend-fournisseur && ng serve --port 58548

# Vérifier les ports utilisés
netstat -an | findstr :5014
netstat -an | findstr :4200
netstat -an | findstr :58548
```

---

## ✅ **Checklist de Validation**

### **Backend**
- [ ] API démarre sans erreur
- [ ] Swagger UI accessible
- [ ] Endpoints répondent correctement
- [ ] Authentification fonctionne
- [ ] Base de données connectée

### **Frontend Client**
- [ ] Application se charge
- [ ] Navigation fonctionne
- [ ] Panier opérationnel
- [ ] Codes promo applicables

### **Frontend Fournisseur**
- [ ] Interface admin accessible
- [ ] Gestion promotions fonctionnelle
- [ ] Création/modification OK
- [ ] Statistiques affichées

### **Intégration**
- [ ] Communication API ↔ Frontend
- [ ] Données synchronisées
- [ ] Erreurs gérées correctement
- [ ] Performance acceptable

---

**🎉 Système prêt pour les tests utilisateur !**

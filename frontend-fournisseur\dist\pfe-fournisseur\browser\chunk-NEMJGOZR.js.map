{"version": 3, "sources": ["src/app/services/admin-notification.service.ts", "src/app/components/notification-icon/notification-icon.component.ts"], "sourcesContent": ["import { Injectable } from '@angular/core';\nimport { HttpClient } from '@angular/common/http';\nimport { Observable, BehaviorSubject, interval } from 'rxjs';\nimport { environment } from '../../environments/environment';\n\nexport interface AdminNotification {\n  id: number;\n  type: 'DEMANDE_CATEGORIE' | 'DEMANDE_SOUS_CATEGORIE' | 'NOUVEAU_PRODUIT' | 'COMMANDE' | 'AUTRE';\n  titre: string;\n  message: string;\n  fournisseurNom?: string;\n  fournisseurId?: number;\n  referenceId?: number; // ID de la demande/produit/commande\n  dateCreation: Date;\n  estLue: boolean;\n  priority: 'HIGH' | 'MEDIUM' | 'LOW';\n  actionUrl?: string; // URL pour rediriger vers l'action\n}\n\nexport interface DemandeNotificationDetail {\n  demandeId: number;\n  type: 'CATEGORIE' | 'SOUS_CATEGORIE';\n  nomDemande: string;\n  fournisseurRaisonSociale: string;\n  fournisseurId: number;\n  dateCreation: Date;\n  statut: 'EN_ATTENTE' | 'APPROUVEE' | 'REJETEE';\n}\n\n@Injectable({\n  providedIn: 'root'\n})\nexport class AdminNotificationService {\n  private apiUrl = `${environment.apiUrl}`;\n  \n  private notificationsSubject = new BehaviorSubject<AdminNotification[]>([]);\n  public notifications$ = this.notificationsSubject.asObservable();\n  \n  private unreadCountSubject = new BehaviorSubject<number>(0);\n  public unreadCount$ = this.unreadCountSubject.asObservable();\n\n  constructor(private http: HttpClient) {\n    // Actualiser les notifications toutes les 2 minutes au lieu de 30 secondes\n    // et seulement si l'utilisateur est actif\n    interval(120000).subscribe(() => {\n      if (document.visibilityState === 'visible') {\n        this.loadNotifications();\n      }\n    });\n  }\n\n  loadNotifications(): void {\n    this.getAdminNotifications().subscribe({\n      next: (notifications) => {\n        this.notificationsSubject.next(notifications);\n        const unreadCount = notifications.filter(n => !n.estLue).length;\n        this.unreadCountSubject.next(unreadCount);\n      },\n      error: (error) => {\n        console.error('Erreur lors du chargement des notifications admin:', error);\n      }\n    });\n  }\n\n  getAdminNotifications(): Observable<AdminNotification[]> {\n    return this.http.get<AdminNotification[]>(`${this.apiUrl}/admin/notifications`);\n  }\n\n  getDemandesNotifications(): Observable<DemandeNotificationDetail[]> {\n    return this.http.get<DemandeNotificationDetail[]>(`${this.apiUrl}/admin/notifications/demandes`);\n  }\n\n  markAsRead(notificationId: number): Observable<void> {\n    return this.http.put<void>(`${this.apiUrl}/admin/notifications/${notificationId}/read`, {});\n  }\n\n  markAllAsRead(): Observable<void> {\n    return this.http.put<void>(`${this.apiUrl}/admin/notifications/mark-all-read`, {});\n  }\n\n  deleteNotification(notificationId: number): Observable<void> {\n    return this.http.delete<void>(`${this.apiUrl}/admin/notifications/${notificationId}`);\n  }\n\n  // Méthodes pour créer des notifications spécifiques\n  createDemandeNotification(demande: any, type: 'CATEGORIE' | 'SOUS_CATEGORIE'): AdminNotification {\n    const typeText = type === 'CATEGORIE' ? 'catégorie' : 'sous-catégorie';\n    return {\n      id: Date.now(), // Temporaire\n      type: type === 'CATEGORIE' ? 'DEMANDE_CATEGORIE' : 'DEMANDE_SOUS_CATEGORIE',\n      titre: `Nouvelle demande de ${typeText}`,\n      message: `Le fournisseur ${demande.fournisseurRaisonSociale} souhaite créer une nouvelle ${typeText} nommée \"${demande.nom}\"`,\n      fournisseurNom: demande.fournisseurRaisonSociale,\n      fournisseurId: demande.fournisseurId,\n      referenceId: demande.id,\n      dateCreation: new Date(demande.dateCreation),\n      estLue: false,\n      priority: 'HIGH',\n      actionUrl: `/admin/dashboard/demandes`\n    };\n  }\n\n  // Simuler des notifications pour test (à supprimer en production)\n  getMockNotifications(): AdminNotification[] {\n    return [\n      {\n        id: 1,\n        type: 'DEMANDE_CATEGORIE',\n        titre: 'Nouvelle demande de catégorie',\n        message: 'Le fournisseur Optique Vision Plus souhaite créer une nouvelle catégorie nommée \"Lunettes de Sport\"',\n        fournisseurNom: 'Optique Vision Plus',\n        fournisseurId: 11,\n        referenceId: 1,\n        dateCreation: new Date(),\n        estLue: false,\n        priority: 'HIGH',\n        actionUrl: '/admin/dashboard/demandes'\n      },\n      {\n        id: 2,\n        type: 'DEMANDE_SOUS_CATEGORIE',\n        titre: 'Nouvelle demande de sous-catégorie',\n        message: 'Le fournisseur Optique El Manar souhaite créer une nouvelle sous-catégorie nommée \"Lunettes de Natation\"',\n        fournisseurNom: 'Optique El Manar',\n        fournisseurId: 12,\n        referenceId: 2,\n        dateCreation: new Date(Date.now() - 3600000), // Il y a 1 heure\n        estLue: false,\n        priority: 'HIGH',\n        actionUrl: '/admin/dashboard/demandes'\n      },\n      {\n        id: 3,\n        type: 'NOUVEAU_PRODUIT',\n        titre: 'Nouveau produit en attente',\n        message: 'Le fournisseur Optique Centrale a ajouté un nouveau produit \"Ray-Ban Aviator\" en attente de validation',\n        fournisseurNom: 'Optique Centrale',\n        fournisseurId: 13,\n        referenceId: 101,\n        dateCreation: new Date(Date.now() - 7200000), // Il y a 2 heures\n        estLue: true,\n        priority: 'MEDIUM',\n        actionUrl: '/admin/dashboard/products'\n      }\n    ];\n  }\n}\n", "import { Component, OnInit, OnDestroy } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { Router } from '@angular/router';\nimport { Subscription, interval, forkJoin } from 'rxjs';\nimport { NotificationService, NotificationDto } from '../../services/notification.service';\nimport { AuthService } from '../../services/auth.service';\nimport { DemandeService, StatutDemande } from '../../services/demande.service';\nimport { AdminAuthService } from '../../services/admin-auth.service';\nimport { AdminNotificationService, AdminNotification } from '../../services/admin-notification.service';\n\n\n@Component({\n  selector: 'app-notification-icon',\n  standalone: true,\n  imports: [CommonModule],\n  template: `\n    <div class=\"notification-wrapper\">\n      <button\n        type=\"button\"\n        class=\"notification-btn\"\n        [title]=\"'Notifications'\"\n        (click)=\"toggleMenu($event)\"\n        (mousedown)=\"onButtonMouseDown($event)\">\n        <svg width=\"20\" height=\"20\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" stroke-width=\"2\">\n          <path d=\"M18 8A6 6 0 0 0 6 8c0 7-3 9-3 9h18s-3-2-3-9\"></path>\n          <path d=\"M13.73 21a2 2 0 0 1-3.46 0\"></path>\n        </svg>\n        <span *ngIf=\"totalUnreadCount > 0\" class=\"notification-badge\">{{ totalUnreadCount }}</span>\n      </button>\n\n      <div *ngIf=\"showMenu\" class=\"notification-menu\" (click)=\"$event.stopPropagation()\">\n        <!-- Flèche pointant vers le bouton -->\n        <div class=\"notification-arrow\"></div>\n        <div class=\"notification-header\">\n          <h4>Notifications</h4>\n          <button\n            *ngIf=\"unreadCount > 0\"\n            (click)=\"markAllAsRead()\"\n            class=\"mark-all-read-btn\">\n            Tout marquer comme lu\n          </button>\n        </div>\n\n        <div class=\"notification-divider\"></div>\n\n        <!-- Section notifications admin détaillées -->\n        <div *ngIf=\"isAdmin() && adminNotifications.length > 0\" class=\"admin-notifications-section\">\n          <div class=\"section-header\">\n            <span>🔔 Notifications Admin</span>\n            <span class=\"admin-count\">{{ adminUnreadCount }}</span>\n          </div>\n\n          <div\n            *ngFor=\"let adminNotif of adminNotifications.slice(0, 3)\"\n            class=\"admin-notification-item\"\n            [class.unread]=\"!adminNotif.estLue\"\n            [class.high-priority]=\"adminNotif.priority === 'HIGH'\"\n            (click)=\"handleAdminNotificationClick(adminNotif)\">\n\n            <div class=\"admin-notification-icon\">\n              <span *ngIf=\"adminNotif.type === 'DEMANDE_CATEGORIE'\">📁</span>\n              <span *ngIf=\"adminNotif.type === 'DEMANDE_SOUS_CATEGORIE'\">📂</span>\n              <span *ngIf=\"adminNotif.type === 'NOUVEAU_PRODUIT'\">📦</span>\n              <span *ngIf=\"adminNotif.type === 'COMMANDE'\">🛒</span>\n              <span *ngIf=\"adminNotif.type === 'AUTRE'\">🔔</span>\n            </div>\n\n            <div class=\"admin-notification-content\">\n              <div class=\"admin-notification-title\">{{ adminNotif.titre }}</div>\n              <div class=\"admin-notification-message\">{{ adminNotif.message }}</div>\n              <div class=\"admin-notification-meta\">\n                <span class=\"admin-notification-date\">{{ formatDate(adminNotif.dateCreation) }}</span>\n                <span *ngIf=\"adminNotif.priority === 'HIGH'\" class=\"priority-badge high\">Urgent</span>\n                <span *ngIf=\"adminNotif.priority === 'MEDIUM'\" class=\"priority-badge medium\">Moyen</span>\n              </div>\n            </div>\n\n            <button\n              class=\"admin-delete-btn\"\n              (click)=\"deleteAdminNotification(adminNotif.id, $event)\">\n              <svg width=\"14\" height=\"14\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" stroke-width=\"2\">\n                <line x1=\"18\" y1=\"6\" x2=\"6\" y2=\"18\"></line>\n                <line x1=\"6\" y1=\"6\" x2=\"18\" y2=\"18\"></line>\n              </svg>\n            </button>\n          </div>\n\n          <div *ngIf=\"adminNotifications.length > 3\" class=\"view-all-admin\">\n            <button (click)=\"viewAllAdminNotifications()\">\n              Voir toutes les notifications admin ({{ adminNotifications.length }})\n            </button>\n          </div>\n        </div>\n\n        <!-- Section demandes pour les admins (version simplifiée) -->\n        <div *ngIf=\"isAdmin() && demandesEnAttente > 0 && adminNotifications.length === 0\" class=\"demandes-section\">\n          <div class=\"section-header\">\n            <span>🔔 Demandes en attente</span>\n            <span class=\"demandes-count\">{{ demandesEnAttente }}</span>\n          </div>\n          <div class=\"demande-item\" (click)=\"viewDemandes()\">\n            <div class=\"demande-content\">\n              <div class=\"demande-title\">Nouvelles demandes de catégories</div>\n              <div class=\"demande-message\">{{ demandesEnAttente }} demande(s) en attente de traitement</div>\n            </div>\n          </div>\n        </div>\n\n        <div *ngIf=\"isAdmin() && (adminNotifications.length > 0 || demandesEnAttente > 0)\" class=\"notification-divider\"></div>\n\n        <div class=\"notification-list\">\n          <!-- Message quand aucune notification pour utilisateurs normaux -->\n          <div *ngIf=\"!isAdmin() && notifications.length === 0\" class=\"no-notifications\">\n            <svg class=\"empty-icon\" width=\"48\" height=\"48\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" stroke-width=\"2\">\n              <path d=\"M20 21v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2\"></path>\n              <circle cx=\"12\" cy=\"7\" r=\"4\"></circle>\n            </svg>\n            <p>Aucune notification</p>\n            <span class=\"no-notifications-subtext\">Vous êtes à jour !</span>\n          </div>\n\n          <!-- Message quand aucune notification pour admins -->\n          <div *ngIf=\"isAdmin() && notifications.length === 0 && adminNotifications.length === 0 && demandesEnAttente === 0\" class=\"no-notifications\">\n            <svg class=\"empty-icon\" width=\"48\" height=\"48\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" stroke-width=\"2\">\n              <path d=\"M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z\"></path>\n            </svg>\n            <p>Aucune notification</p>\n            <span class=\"no-notifications-subtext\">Tout est sous contrôle !</span>\n          </div>\n\n          <div\n            *ngFor=\"let notification of notifications.slice(0, 5)\"\n            class=\"notification-item\"\n            [class.unread]=\"!notification.estLue\"\n            (click)=\"markAsRead(notification)\">\n\n            <div class=\"notification-content\">\n              <p class=\"notification-text\">{{ notification.contenu }}</p>\n              <span class=\"notification-date\">{{ formatDate(notification.dateEnvoi) }}</span>\n            </div>\n\n            <button\n              class=\"delete-btn\"\n              (click)=\"deleteNotification(notification.id, $event)\">\n              <svg width=\"16\" height=\"16\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" stroke-width=\"2\">\n                <line x1=\"18\" y1=\"6\" x2=\"6\" y2=\"18\"></line>\n                <line x1=\"6\" y1=\"6\" x2=\"18\" y2=\"18\"></line>\n              </svg>\n            </button>\n          </div>\n\n          <div *ngIf=\"notifications.length > 5\" class=\"view-all\">\n            <button (click)=\"viewAllNotifications()\">\n              Voir toutes les notifications\n            </button>\n          </div>\n        </div>\n      </div>\n    </div>\n  `,\n  styles: [`\n    .notification-wrapper {\n      position: relative;\n      display: inline-block;\n      z-index: 999;\n    }\n\n    .notification-btn {\n      position: relative;\n      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n      border: none;\n      cursor: pointer;\n      padding: 12px;\n      border-radius: 50%;\n      transition: all 0.3s ease;\n      color: white;\n      z-index: 1001;\n      outline: none;\n      font-size: 18px;\n      width: 48px;\n      height: 48px;\n      display: flex;\n      align-items: center;\n      justify-content: center;\n      box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);\n    }\n\n    .notification-btn:hover {\n      background: linear-gradient(135deg, #5a67d8 0%, #6b46c1 100%);\n      transform: translateY(-2px);\n      box-shadow: 0 6px 20px rgba(102, 126, 234, 0.4);\n    }\n\n    .notification-btn:focus {\n      outline: none;\n      box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.3);\n    }\n\n    .notification-btn:active {\n      transform: translateY(0) scale(0.95);\n      box-shadow: 0 2px 8px rgba(102, 126, 234, 0.3);\n    }\n\n    .notification-badge {\n      position: absolute;\n      top: -2px;\n      right: -2px;\n      background: linear-gradient(135deg, #ff4757 0%, #ff3742 100%);\n      color: white;\n      border: 2px solid white;\n      border-radius: 50%;\n      min-width: 22px;\n      height: 22px;\n      font-size: 11px;\n      display: flex;\n      align-items: center;\n      justify-content: center;\n      font-weight: bold;\n      box-shadow: 0 2px 8px rgba(255, 71, 87, 0.4);\n      animation: pulse 2s infinite;\n    }\n\n    @keyframes pulse {\n      0% {\n        box-shadow: 0 2px 8px rgba(255, 71, 87, 0.4);\n      }\n      50% {\n        box-shadow: 0 2px 8px rgba(255, 71, 87, 0.6), 0 0 0 4px rgba(255, 71, 87, 0.2);\n      }\n      100% {\n        box-shadow: 0 2px 8px rgba(255, 71, 87, 0.4);\n      }\n    }\n\n    .notification-menu {\n      position: absolute;\n      top: calc(100% + 8px);\n      right: 0;\n      width: 380px;\n      max-width: 90vw;\n      max-height: 500px;\n      background: white;\n      border: 1px solid #e5e7eb;\n      border-radius: 12px;\n      box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);\n      z-index: 1000;\n      overflow: hidden;\n      transform-origin: top right;\n      animation: slideDown 0.2s ease-out;\n    }\n\n    @keyframes slideDown {\n      from {\n        opacity: 0;\n        transform: translateY(-10px) scale(0.95);\n      }\n      to {\n        opacity: 1;\n        transform: translateY(0) scale(1);\n      }\n    }\n\n    /* Responsive pour mobile */\n    @media (max-width: 768px) {\n      .notification-menu {\n        position: fixed;\n        top: 60px;\n        left: 10px;\n        right: 10px;\n        width: auto;\n        max-width: none;\n        transform-origin: top center;\n      }\n    }\n\n    /* Ajustement pour les petits écrans */\n    @media (max-width: 480px) {\n      .notification-menu {\n        top: 50px;\n        left: 5px;\n        right: 5px;\n        max-height: 70vh;\n      }\n    }\n\n    /* Flèche pointant vers le bouton */\n    .notification-arrow {\n      position: absolute;\n      top: -8px;\n      right: 20px;\n      width: 0;\n      height: 0;\n      border-left: 8px solid transparent;\n      border-right: 8px solid transparent;\n      border-bottom: 8px solid white;\n      z-index: 1001;\n    }\n\n    .notification-arrow::before {\n      content: '';\n      position: absolute;\n      top: 1px;\n      left: -8px;\n      width: 0;\n      height: 0;\n      border-left: 8px solid transparent;\n      border-right: 8px solid transparent;\n      border-bottom: 8px solid #e5e7eb;\n    }\n\n    /* Masquer la flèche sur mobile */\n    @media (max-width: 768px) {\n      .notification-arrow {\n        display: none;\n      }\n    }\n\n    .notification-header {\n      display: flex;\n      justify-content: space-between;\n      align-items: center;\n      padding: 16px;\n      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n      color: white;\n    }\n\n    .notification-header h4 {\n      margin: 0;\n      font-size: 16px;\n      font-weight: 500;\n    }\n\n    .mark-all-read-btn {\n      background: none;\n      border: none;\n      color: white;\n      font-size: 12px;\n      cursor: pointer;\n      padding: 4px 8px;\n      border-radius: 4px;\n      transition: background-color 0.2s;\n    }\n\n    .mark-all-read-btn:hover {\n      background-color: rgba(255, 255, 255, 0.2);\n    }\n\n    .notification-divider {\n      height: 1px;\n      background-color: #e0e0e0;\n    }\n\n    .notification-list {\n      max-height: 300px;\n      overflow-y: auto;\n    }\n\n    .no-notifications {\n      text-align: center;\n      padding: 32px 16px;\n      color: #666;\n    }\n\n    .empty-icon {\n      margin-bottom: 16px;\n      color: #ccc;\n    }\n\n    .no-notifications p {\n      margin: 0;\n      font-size: 16px;\n      font-weight: 500;\n    }\n\n    .no-notifications-subtext {\n      display: block;\n      margin-top: 8px;\n      font-size: 14px;\n      color: #cbd5e1;\n      font-style: italic;\n    }\n\n    .notification-item {\n      display: flex;\n      align-items: flex-start;\n      padding: 12px 16px;\n      border-bottom: 1px solid #e0e0e0;\n      cursor: pointer;\n      transition: background-color 0.2s;\n    }\n\n    .notification-item:hover {\n      background-color: #f5f5f5;\n    }\n\n    .notification-item.unread {\n      background-color: #e3f2fd;\n      border-left: 3px solid #2196f3;\n    }\n\n    .notification-content {\n      flex: 1;\n      margin-right: 8px;\n    }\n\n    .notification-text {\n      margin: 0 0 4px 0;\n      font-size: 14px;\n      line-height: 1.4;\n    }\n\n    .notification-date {\n      font-size: 12px;\n      color: #666;\n    }\n\n    .delete-btn {\n      background: none;\n      border: none;\n      cursor: pointer;\n      opacity: 0;\n      transition: opacity 0.2s;\n      width: 32px;\n      height: 32px;\n      border-radius: 4px;\n      display: flex;\n      align-items: center;\n      justify-content: center;\n      color: #666;\n    }\n\n    .delete-btn:hover {\n      background-color: rgba(244, 67, 54, 0.1);\n      color: #f44336;\n    }\n\n    .notification-item:hover .delete-btn {\n      opacity: 1;\n    }\n\n    .view-all {\n      text-align: center;\n      padding: 16px;\n      border-top: 1px solid #e0e0e0;\n    }\n\n    .view-all button {\n      background: none;\n      border: none;\n      color: #2196f3;\n      cursor: pointer;\n      font-size: 14px;\n      padding: 8px 16px;\n      border-radius: 4px;\n      transition: background-color 0.2s;\n    }\n\n    .view-all button:hover {\n      background-color: rgba(33, 150, 243, 0.1);\n    }\n\n    .demandes-section {\n      padding: 12px 16px;\n      background: #f8f9ff;\n      border-left: 4px solid #3b82f6;\n    }\n\n    .section-header {\n      display: flex;\n      justify-content: space-between;\n      align-items: center;\n      margin-bottom: 8px;\n      font-weight: 500;\n      color: #1e293b;\n      font-size: 14px;\n    }\n\n    .demandes-count {\n      background: #3b82f6;\n      color: white;\n      border-radius: 12px;\n      padding: 2px 8px;\n      font-size: 12px;\n      font-weight: bold;\n    }\n\n    .demande-item {\n      cursor: pointer;\n      padding: 8px 0;\n      transition: opacity 0.2s;\n    }\n\n    .demande-item:hover {\n      opacity: 0.8;\n    }\n\n    .demande-content {\n      display: flex;\n      flex-direction: column;\n      gap: 4px;\n    }\n\n    .demande-title {\n      font-weight: 500;\n      color: #1e293b;\n      font-size: 14px;\n    }\n\n    .demande-message {\n      color: #64748b;\n      font-size: 12px;\n    }\n\n    /* Styles pour les notifications admin */\n    .admin-notifications-section {\n      padding: 12px 16px;\n      background: #f0f9ff;\n      border-left: 4px solid #3b82f6;\n      margin-bottom: 8px;\n    }\n\n    .admin-count {\n      background: #3b82f6;\n      color: white;\n      border-radius: 12px;\n      padding: 2px 8px;\n      font-size: 12px;\n      font-weight: bold;\n    }\n\n    .admin-notification-item {\n      display: flex;\n      align-items: flex-start;\n      gap: 12px;\n      padding: 12px;\n      margin: 8px 0;\n      background: white;\n      border-radius: 8px;\n      cursor: pointer;\n      transition: all 0.2s ease;\n      border-left: 3px solid transparent;\n    }\n\n    .admin-notification-item:hover {\n      background: #f8fafc;\n      transform: translateX(2px);\n    }\n\n    .admin-notification-item.unread {\n      border-left-color: #3b82f6;\n      background: #f0f9ff;\n    }\n\n    .admin-notification-item.high-priority {\n      border-left-color: #ef4444;\n      background: #fef2f2;\n    }\n\n    .admin-notification-icon {\n      font-size: 18px;\n      min-width: 24px;\n      text-align: center;\n    }\n\n    .admin-notification-content {\n      flex: 1;\n      min-width: 0;\n    }\n\n    .admin-notification-title {\n      font-weight: 600;\n      color: #1e293b;\n      margin-bottom: 4px;\n      font-size: 14px;\n    }\n\n    .admin-notification-message {\n      color: #64748b;\n      font-size: 12px;\n      line-height: 1.4;\n      margin-bottom: 8px;\n    }\n\n    .admin-notification-meta {\n      display: flex;\n      align-items: center;\n      gap: 8px;\n    }\n\n    .admin-notification-date {\n      color: #94a3b8;\n      font-size: 11px;\n    }\n\n    .priority-badge {\n      padding: 2px 6px;\n      border-radius: 10px;\n      font-size: 10px;\n      font-weight: 600;\n      text-transform: uppercase;\n    }\n\n    .priority-badge.high {\n      background: #fee2e2;\n      color: #dc2626;\n    }\n\n    .priority-badge.medium {\n      background: #fef3c7;\n      color: #d97706;\n    }\n\n    .admin-delete-btn {\n      background: none;\n      border: none;\n      cursor: pointer;\n      padding: 4px;\n      border-radius: 4px;\n      color: #94a3b8;\n      transition: all 0.2s ease;\n    }\n\n    .admin-delete-btn:hover {\n      background: #fee2e2;\n      color: #dc2626;\n    }\n\n    .view-all-admin {\n      text-align: center;\n      margin-top: 12px;\n      padding-top: 12px;\n      border-top: 1px solid rgba(59, 130, 246, 0.2);\n    }\n\n    .view-all-admin button {\n      background: none;\n      border: 1px solid #3b82f6;\n      color: #3b82f6;\n      padding: 8px 16px;\n      border-radius: 6px;\n      cursor: pointer;\n      font-size: 12px;\n      transition: all 0.2s ease;\n    }\n\n    .view-all-admin button:hover {\n      background: #3b82f6;\n      color: white;\n    }\n  `]\n})\nexport class NotificationIconComponent implements OnInit, OnDestroy {\n  notifications: NotificationDto[] = [];\n  adminNotifications: AdminNotification[] = [];\n  unreadCount = 0;\n  showMenu = false;\n  demandesEnAttente = 0;\n  private subscriptions = new Subscription();\n\n  get adminUnreadCount(): number {\n    const count = this.adminNotifications.filter(n => !n.estLue).length;\n    console.log('📊 adminUnreadCount:', count, 'sur', this.adminNotifications.length, 'notifications admin');\n    return count;\n  }\n\n  constructor(\n    private notificationService: NotificationService,\n    private authService: AuthService,\n    private router: Router,\n    private demandeService: DemandeService,\n    private adminAuthService: AdminAuthService,\n    private adminNotificationService: AdminNotificationService\n  ) {}\n\n  ngOnInit() {\n    this.subscriptions.add(\n      this.notificationService.notifications$.subscribe(notifications => {\n        this.notifications = notifications;\n      })\n    );\n\n    this.subscriptions.add(\n      this.notificationService.unreadCount$.subscribe(count => {\n        this.unreadCount = count;\n      })\n    );\n\n    // Pour les admins, charger les notifications spécifiques\n    if (this.isAdmin()) {\n      this.subscriptions.add(\n        this.adminNotificationService.notifications$.subscribe(adminNotifications => {\n          this.adminNotifications = adminNotifications;\n        })\n      );\n\n      // Ne pas s'abonner directement au unreadCount admin ici\n      // car cela cause une duplication dans le calcul totalUnreadCount\n\n      this.loadAdminNotifications();\n    }\n\n    this.loadNotifications();\n    this.loadDemandesEnAttente();\n\n    // Actualiser les demandes toutes les 30 secondes pour les admins\n    if (this.isAdmin()) {\n      this.subscriptions.add(\n        interval(30000).subscribe(() => {\n          this.loadDemandesEnAttente();\n          this.loadAdminNotifications();\n        })\n      );\n    }\n  }\n\n  ngOnDestroy() {\n    this.subscriptions.unsubscribe();\n  }\n\n  toggleMenu(event?: Event) {\n    console.log('🔄 toggleMenu appelé, showMenu avant:', this.showMenu);\n\n    // Empêcher la propagation de l'événement\n    if (event) {\n      event.stopPropagation();\n      event.preventDefault();\n    }\n\n    this.showMenu = !this.showMenu;\n    console.log('🔄 showMenu après:', this.showMenu);\n\n    if (this.showMenu) {\n      console.log('📂 Menu ouvert, chargement des notifications...');\n      this.loadNotifications();\n\n      // Charger les notifications admin si c'est un admin\n      if (this.isAdmin()) {\n        console.log('👑 Utilisateur admin détecté, chargement des notifications admin...');\n        this.loadAdminNotifications();\n      }\n\n      // Fermer le menu si on clique ailleurs\n      setTimeout(() => {\n        document.addEventListener('click', this.closeMenu.bind(this));\n      }, 100);\n    } else {\n      console.log('📂 Menu fermé');\n      document.removeEventListener('click', this.closeMenu.bind(this));\n    }\n  }\n\n  closeMenu(event?: Event) {\n    console.log('🔄 closeMenu appelé');\n    this.showMenu = false;\n    document.removeEventListener('click', this.closeMenu.bind(this));\n  }\n\n  onButtonMouseDown(event: Event) {\n    console.log('🖱️ Bouton notification mousedown détecté !');\n    event.stopPropagation();\n  }\n\n  convertDemandestoNotifications(demandesCategories: any[], demandesSousCategories: any[]): AdminNotification[] {\n    const notifications: AdminNotification[] = [];\n    console.log('🔄 Conversion des demandes en notifications...');\n\n    // Convertir les demandes de catégories\n    console.log('📁 Traitement des demandes de catégories:', demandesCategories.length);\n    demandesCategories.forEach((demande, index) => {\n      console.log(`🔍 Demande catégorie ${index + 1}:`, {\n        id: demande.id,\n        nom: demande.nom,\n        statut: demande.statut,\n        fournisseurId: demande.fournisseurId,\n        fournisseurRaisonSociale: demande.fournisseurRaisonSociale,\n        dateCreation: demande.dateCreation,\n        dateDemande: demande.dateDemande\n      });\n\n      if (demande.statut === 'EN_ATTENTE' || demande.statut === 0) { // 0 = EN_ATTENTE\n        const fournisseurNom = demande.fournisseurRaisonSociale ||\n                              demande.fournisseur?.raisonSociale ||\n                              demande.nomFournisseur ||\n                              `Fournisseur #${demande.fournisseurId}`;\n\n        notifications.push({\n          id: parseInt(`1${demande.id}`), // Préfixe 1 pour catégories\n          type: 'DEMANDE_CATEGORIE',\n          titre: 'Nouvelle demande de catégorie',\n          message: `Le fournisseur ${fournisseurNom} souhaite créer une nouvelle catégorie nommée \"${demande.nom}\"`,\n          fournisseurNom: fournisseurNom,\n          fournisseurId: demande.fournisseurId,\n          referenceId: demande.id,\n          dateCreation: new Date(demande.dateCreation || demande.dateDemande || Date.now()),\n          estLue: false,\n          priority: 'HIGH',\n          actionUrl: '/admin/dashboard/demandes'\n        });\n        console.log(`✅ Notification créée pour catégorie: ${demande.nom}`);\n      } else {\n        console.log(`⏭️ Demande catégorie ignorée (statut: ${demande.statut})`);\n      }\n    });\n\n    // Convertir les demandes de sous-catégories\n    console.log('📂 Traitement des demandes de sous-catégories:', demandesSousCategories.length);\n    demandesSousCategories.forEach((demande, index) => {\n      console.log(`🔍 Demande sous-catégorie ${index + 1}:`, {\n        id: demande.id,\n        nom: demande.nom,\n        statut: demande.statut,\n        fournisseurId: demande.fournisseurId,\n        fournisseurRaisonSociale: demande.fournisseurRaisonSociale\n      });\n\n      if (demande.statut === 'EN_ATTENTE' || demande.statut === 0) { // 0 = EN_ATTENTE\n        const fournisseurNom = demande.fournisseurRaisonSociale ||\n                              demande.fournisseur?.raisonSociale ||\n                              demande.nomFournisseur ||\n                              `Fournisseur #${demande.fournisseurId}`;\n\n        notifications.push({\n          id: parseInt(`2${demande.id}`), // Préfixe 2 pour sous-catégories\n          type: 'DEMANDE_SOUS_CATEGORIE',\n          titre: 'Nouvelle demande de sous-catégorie',\n          message: `Le fournisseur ${fournisseurNom} souhaite créer une nouvelle sous-catégorie nommée \"${demande.nom}\"`,\n          fournisseurNom: fournisseurNom,\n          fournisseurId: demande.fournisseurId,\n          referenceId: demande.id,\n          dateCreation: new Date(demande.dateCreation || demande.dateDemande || Date.now()),\n          estLue: false,\n          priority: 'HIGH',\n          actionUrl: '/admin/dashboard/demandes'\n        });\n        console.log(`✅ Notification créée pour sous-catégorie: ${demande.nom}`);\n      } else {\n        console.log(`⏭️ Demande sous-catégorie ignorée (statut: ${demande.statut})`);\n      }\n    });\n\n    // Trier par date (plus récent en premier)\n    notifications.sort((a, b) => new Date(b.dateCreation).getTime() - new Date(a.dateCreation).getTime());\n\n    console.log(`📊 Total notifications créées: ${notifications.length}`);\n    return notifications;\n  }\n\n  loadNotifications() {\n    console.log('📥 loadNotifications appelé');\n    const currentUser = this.authService.getCurrentUser();\n    console.log('👤 Utilisateur actuel:', currentUser);\n\n    if (currentUser?.id) {\n      console.log('🔄 Chargement des notifications pour utilisateur ID:', currentUser.id);\n      this.notificationService.getUserNotifications(currentUser.id).subscribe({\n        next: (notifications) => {\n          console.log('✅ Notifications reçues:', notifications);\n        },\n        error: (error) => {\n          console.error('❌ Erreur lors du chargement des notifications:', error);\n        }\n      });\n    } else {\n      console.log('❌ Aucun utilisateur connecté');\n    }\n  }\n\n  loadAdminNotifications() {\n    console.log('📥 loadAdminNotifications appelé');\n    if (this.isAdmin()) {\n      console.log('👑 Chargement des notifications admin depuis la base de données...');\n\n      // Charger les demandes de catégories et sous-catégories\n      forkJoin({\n        demandesCategories: this.demandeService.getMesDemandesCategories(),\n        demandesSousCategories: this.demandeService.getMesDemandesSousCategories()\n      }).subscribe({\n        next: (result) => {\n          console.log('✅ Demandes récupérées:', result);\n          console.log('📊 Nombre de demandes catégories:', result.demandesCategories?.length || 0);\n          console.log('📊 Nombre de demandes sous-catégories:', result.demandesSousCategories?.length || 0);\n\n          // Afficher un échantillon des données pour debug\n          if (result.demandesCategories?.length > 0) {\n            console.log('🔍 Exemple demande catégorie:', result.demandesCategories[0]);\n          }\n          if (result.demandesSousCategories?.length > 0) {\n            console.log('🔍 Exemple demande sous-catégorie:', result.demandesSousCategories[0]);\n          }\n\n          // Convertir les demandes en notifications admin\n          this.adminNotifications = this.convertDemandestoNotifications(\n            result.demandesCategories || [],\n            result.demandesSousCategories || []\n          );\n\n          console.log('✅ Notifications admin créées:', this.adminNotifications);\n          console.log('📊 Nombre total de notifications admin:', this.adminNotifications.length);\n        },\n        error: (error) => {\n          console.error('❌ Erreur lors du chargement des demandes:', error);\n          // En cas d'erreur, utiliser les notifications mock\n          this.adminNotifications = this.adminNotificationService.getMockNotifications();\n        }\n      });\n    }\n  }\n\n  markAsRead(notification: NotificationDto) {\n    if (!notification.estLue) {\n      this.notificationService.markAsRead(notification.id).subscribe();\n    }\n  }\n\n  markAllAsRead() {\n    const unreadNotifications = this.notifications.filter(n => !n.estLue);\n    unreadNotifications.forEach(notification => {\n      this.notificationService.markAsRead(notification.id).subscribe();\n    });\n  }\n\n  deleteNotification(notificationId: number, event: Event) {\n    event.stopPropagation();\n    this.notificationService.deleteNotification(notificationId).subscribe();\n  }\n\n  viewAllNotifications() {\n    this.router.navigate(['/notifications']);\n  }\n\n  viewDemandes() {\n    this.router.navigate(['/admin/dashboard/demandes']);\n    this.closeMenu();\n  }\n\n  isAdmin(): boolean {\n    // Vérifier si l'utilisateur est admin via le service principal\n    const isAdminViaAuth = this.authService.isAdmin();\n    // Vérifier si l'utilisateur est admin via le service admin\n    const isAdminViaAdminAuth = this.adminAuthService.getCurrentUser() !== null;\n\n    console.log('🔍 Vérification admin - AuthService:', isAdminViaAuth);\n    console.log('🔍 Vérification admin - AdminAuthService:', isAdminViaAdminAuth);\n\n    const result = isAdminViaAuth || isAdminViaAdminAuth;\n    console.log('🔍 Résultat final isAdmin:', result);\n\n    return result;\n  }\n\n  handleAdminNotificationClick(notification: AdminNotification) {\n    console.log('🔔 Clic sur notification admin:', notification);\n\n    // Marquer comme lue localement\n    notification.estLue = true;\n\n    // Rediriger vers l'action appropriée\n    if (notification.actionUrl) {\n      this.router.navigate([notification.actionUrl]);\n    }\n\n    this.closeMenu();\n  }\n\n  deleteAdminNotification(notificationId: number, event: Event) {\n    event.stopPropagation();\n    console.log('🗑️ Suppression notification admin:', notificationId);\n\n    // Supprimer de la liste locale immédiatement\n    this.adminNotifications = this.adminNotifications.filter(n => n.id !== notificationId);\n    console.log('✅ Notification supprimée localement');\n  }\n\n  viewAllAdminNotifications() {\n    this.router.navigate(['/admin/notifications']);\n    this.closeMenu();\n  }\n\n  loadDemandesEnAttente() {\n    console.log('🔍 loadDemandesEnAttente appelé');\n    console.log('🔍 isAdmin():', this.isAdmin());\n    console.log('🔍 authService.isAuthenticated():', this.authService.isAuthenticated());\n    console.log('🔍 authService.getToken():', !!this.authService.getToken());\n    console.log('🔍 adminAuthService.getCurrentUser():', !!this.adminAuthService.getCurrentUser());\n\n    if (this.isAdmin()) {\n      console.log('✅ Utilisateur admin détecté, chargement des demandes...');\n      forkJoin({\n        categories: this.demandeService.getDemandesCategoriesByStatut(StatutDemande.EnAttente),\n        sousCategories: this.demandeService.getDemandesSousCategoriesByStatut(StatutDemande.EnAttente)\n      }).subscribe({\n        next: (result) => {\n          console.log('✅ Demandes chargées:', result);\n          this.demandesEnAttente = result.categories.length + result.sousCategories.length;\n        },\n        error: (error) => {\n          console.error('❌ Erreur lors du chargement des demandes en attente:', error);\n        }\n      });\n    } else {\n      console.log('❌ Utilisateur non admin, pas de chargement des demandes');\n    }\n  }\n\n  get totalUnreadCount(): number {\n    const adminUnreadCount = this.isAdmin() ? this.adminUnreadCount : 0;\n    const normalUnreadCount = this.unreadCount || 0;\n\n    // Pour les admins, utiliser seulement les notifications admin (pas de duplication)\n    // Pour les fournisseurs, utiliser les notifications normales\n    const total = this.isAdmin() ? adminUnreadCount : normalUnreadCount;\n\n    console.log('🔢 Calcul badge:', {\n      isAdmin: this.isAdmin(),\n      normalUnreadCount,\n      adminUnreadCount,\n      total\n    });\n\n    return Math.max(0, total); // S'assurer que le total n'est jamais négatif\n  }\n\n  formatDate(date: Date): string {\n    const now = new Date();\n    const notifDate = new Date(date);\n    const diffInMinutes = Math.floor((now.getTime() - notifDate.getTime()) / (1000 * 60));\n\n    if (diffInMinutes < 1) return 'À l\\'instant';\n    if (diffInMinutes < 60) return `Il y a ${diffInMinutes} min`;\n\n    const diffInHours = Math.floor(diffInMinutes / 60);\n    if (diffInHours < 24) return `Il y a ${diffInHours}h`;\n\n    const diffInDays = Math.floor(diffInHours / 24);\n    if (diffInDays < 7) return `Il y a ${diffInDays}j`;\n\n    return notifDate.toLocaleDateString('fr-FR');\n  }\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAgCM,IAAO,2BAAP,MAAO,0BAAwB;EASf;EARZ,SAAS,GAAG,YAAY,MAAM;EAE9B,uBAAuB,IAAI,gBAAqC,CAAA,CAAE;EACnE,iBAAiB,KAAK,qBAAqB,aAAY;EAEtD,qBAAqB,IAAI,gBAAwB,CAAC;EACnD,eAAe,KAAK,mBAAmB,aAAY;EAE1D,YAAoB,MAAgB;AAAhB,SAAA,OAAA;AAGlB,aAAS,IAAM,EAAE,UAAU,MAAK;AAC9B,UAAI,SAAS,oBAAoB,WAAW;AAC1C,aAAK,kBAAiB;MACxB;IACF,CAAC;EACH;EAEA,oBAAiB;AACf,SAAK,sBAAqB,EAAG,UAAU;MACrC,MAAM,CAAC,kBAAiB;AACtB,aAAK,qBAAqB,KAAK,aAAa;AAC5C,cAAM,cAAc,cAAc,OAAO,OAAK,CAAC,EAAE,MAAM,EAAE;AACzD,aAAK,mBAAmB,KAAK,WAAW;MAC1C;MACA,OAAO,CAAC,UAAS;AACf,gBAAQ,MAAM,sDAAsD,KAAK;MAC3E;KACD;EACH;EAEA,wBAAqB;AACnB,WAAO,KAAK,KAAK,IAAyB,GAAG,KAAK,MAAM,sBAAsB;EAChF;EAEA,2BAAwB;AACtB,WAAO,KAAK,KAAK,IAAiC,GAAG,KAAK,MAAM,+BAA+B;EACjG;EAEA,WAAW,gBAAsB;AAC/B,WAAO,KAAK,KAAK,IAAU,GAAG,KAAK,MAAM,wBAAwB,cAAc,SAAS,CAAA,CAAE;EAC5F;EAEA,gBAAa;AACX,WAAO,KAAK,KAAK,IAAU,GAAG,KAAK,MAAM,sCAAsC,CAAA,CAAE;EACnF;EAEA,mBAAmB,gBAAsB;AACvC,WAAO,KAAK,KAAK,OAAa,GAAG,KAAK,MAAM,wBAAwB,cAAc,EAAE;EACtF;;EAGA,0BAA0B,SAAc,MAAoC;AAC1E,UAAM,WAAW,SAAS,cAAc,iBAAc;AACtD,WAAO;MACL,IAAI,KAAK,IAAG;;MACZ,MAAM,SAAS,cAAc,sBAAsB;MACnD,OAAO,uBAAuB,QAAQ;MACtC,SAAS,kBAAkB,QAAQ,wBAAwB,mCAAgC,QAAQ,eAAY,QAAQ,GAAG;MAC1H,gBAAgB,QAAQ;MACxB,eAAe,QAAQ;MACvB,aAAa,QAAQ;MACrB,cAAc,IAAI,KAAK,QAAQ,YAAY;MAC3C,QAAQ;MACR,UAAU;MACV,WAAW;;EAEf;;EAGA,uBAAoB;AAClB,WAAO;MACL;QACE,IAAI;QACJ,MAAM;QACN,OAAO;QACP,SAAS;QACT,gBAAgB;QAChB,eAAe;QACf,aAAa;QACb,cAAc,oBAAI,KAAI;QACtB,QAAQ;QACR,UAAU;QACV,WAAW;;MAEb;QACE,IAAI;QACJ,MAAM;QACN,OAAO;QACP,SAAS;QACT,gBAAgB;QAChB,eAAe;QACf,aAAa;QACb,cAAc,IAAI,KAAK,KAAK,IAAG,IAAK,IAAO;;QAC3C,QAAQ;QACR,UAAU;QACV,WAAW;;MAEb;QACE,IAAI;QACJ,MAAM;QACN,OAAO;QACP,SAAS;QACT,gBAAgB;QAChB,eAAe;QACf,aAAa;QACb,cAAc,IAAI,KAAK,KAAK,IAAG,IAAK,IAAO;;QAC3C,QAAQ;QACR,UAAU;QACV,WAAW;;;EAGjB;;qCAjHW,2BAAwB,mBAAA,UAAA,CAAA;EAAA;4EAAxB,2BAAwB,SAAxB,0BAAwB,WAAA,YAFvB,OAAM,CAAA;;;sEAEP,0BAAwB,CAAA;UAHpC;WAAW;MACV,YAAY;KACb;;;;;;;ACJO,IAAA,yBAAA,GAAA,QAAA,CAAA;AAA8D,IAAA,iBAAA,CAAA;AAAsB,IAAA,uBAAA;;;;AAAtB,IAAA,oBAAA;AAAA,IAAA,4BAAA,OAAA,gBAAA;;;;;;AAQ5D,IAAA,yBAAA,GAAA,UAAA,EAAA;AAEE,IAAA,qBAAA,SAAA,SAAA,4EAAA;AAAA,MAAA,wBAAA,GAAA;AAAA,YAAA,SAAA,wBAAA,CAAA;AAAA,aAAA,sBAAS,OAAA,cAAA,CAAe;IAAA,CAAA;AAExB,IAAA,iBAAA,GAAA,yBAAA;AACF,IAAA,uBAAA;;;;;AAoBI,IAAA,yBAAA,GAAA,MAAA;AAAsD,IAAA,iBAAA,GAAA,WAAA;AAAE,IAAA,uBAAA;;;;;AACxD,IAAA,yBAAA,GAAA,MAAA;AAA2D,IAAA,iBAAA,GAAA,WAAA;AAAE,IAAA,uBAAA;;;;;AAC7D,IAAA,yBAAA,GAAA,MAAA;AAAoD,IAAA,iBAAA,GAAA,WAAA;AAAE,IAAA,uBAAA;;;;;AACtD,IAAA,yBAAA,GAAA,MAAA;AAA6C,IAAA,iBAAA,GAAA,WAAA;AAAE,IAAA,uBAAA;;;;;AAC/C,IAAA,yBAAA,GAAA,MAAA;AAA0C,IAAA,iBAAA,GAAA,WAAA;AAAE,IAAA,uBAAA;;;;;AAQ1C,IAAA,yBAAA,GAAA,QAAA,EAAA;AAAyE,IAAA,iBAAA,GAAA,QAAA;AAAM,IAAA,uBAAA;;;;;AAC/E,IAAA,yBAAA,GAAA,QAAA,EAAA;AAA6E,IAAA,iBAAA,GAAA,OAAA;AAAK,IAAA,uBAAA;;;;;;AArBxF,IAAA,yBAAA,GAAA,OAAA,EAAA;AAKE,IAAA,qBAAA,SAAA,SAAA,4EAAA;AAAA,YAAA,gBAAA,wBAAA,GAAA,EAAA;AAAA,YAAA,SAAA,wBAAA,CAAA;AAAA,aAAA,sBAAS,OAAA,6BAAA,aAAA,CAAwC;IAAA,CAAA;AAEjD,IAAA,yBAAA,GAAA,OAAA,EAAA;AACE,IAAA,qBAAA,GAAA,6DAAA,GAAA,GAAA,QAAA,EAAA,EAAsD,GAAA,6DAAA,GAAA,GAAA,QAAA,EAAA,EACK,GAAA,6DAAA,GAAA,GAAA,QAAA,EAAA,EACP,GAAA,6DAAA,GAAA,GAAA,QAAA,EAAA,EACP,GAAA,6DAAA,GAAA,GAAA,QAAA,EAAA;AAE/C,IAAA,uBAAA;AAEA,IAAA,yBAAA,GAAA,OAAA,EAAA,EAAwC,GAAA,OAAA,EAAA;AACA,IAAA,iBAAA,CAAA;AAAsB,IAAA,uBAAA;AAC5D,IAAA,yBAAA,IAAA,OAAA,EAAA;AAAwC,IAAA,iBAAA,EAAA;AAAwB,IAAA,uBAAA;AAChE,IAAA,yBAAA,IAAA,OAAA,EAAA,EAAqC,IAAA,QAAA,EAAA;AACG,IAAA,iBAAA,EAAA;AAAyC,IAAA,uBAAA;AAC/E,IAAA,qBAAA,IAAA,8DAAA,GAAA,GAAA,QAAA,EAAA,EAAyE,IAAA,8DAAA,GAAA,GAAA,QAAA,EAAA;AAE3E,IAAA,uBAAA,EAAM;AAGR,IAAA,yBAAA,IAAA,UAAA,EAAA;AAEE,IAAA,qBAAA,SAAA,SAAA,8EAAA,QAAA;AAAA,YAAA,gBAAA,wBAAA,GAAA,EAAA;AAAA,YAAA,SAAA,wBAAA,CAAA;AAAA,aAAA,sBAAS,OAAA,wBAAA,cAAA,IAAA,MAAA,CAA8C;IAAA,CAAA;;AACvD,IAAA,yBAAA,IAAA,OAAA,EAAA;AACE,IAAA,oBAAA,IAAA,QAAA,EAAA,EAA2C,IAAA,QAAA,EAAA;AAE7C,IAAA,uBAAA,EAAM,EACC;;;;;AA7BT,IAAA,sBAAA,UAAA,CAAA,cAAA,MAAA,EAAmC,iBAAA,cAAA,aAAA,MAAA;AAK1B,IAAA,oBAAA,CAAA;AAAA,IAAA,qBAAA,QAAA,cAAA,SAAA,mBAAA;AACA,IAAA,oBAAA;AAAA,IAAA,qBAAA,QAAA,cAAA,SAAA,wBAAA;AACA,IAAA,oBAAA;AAAA,IAAA,qBAAA,QAAA,cAAA,SAAA,iBAAA;AACA,IAAA,oBAAA;AAAA,IAAA,qBAAA,QAAA,cAAA,SAAA,UAAA;AACA,IAAA,oBAAA;AAAA,IAAA,qBAAA,QAAA,cAAA,SAAA,OAAA;AAI+B,IAAA,oBAAA,CAAA;AAAA,IAAA,4BAAA,cAAA,KAAA;AACE,IAAA,oBAAA,CAAA;AAAA,IAAA,4BAAA,cAAA,OAAA;AAEA,IAAA,oBAAA,CAAA;AAAA,IAAA,4BAAA,OAAA,WAAA,cAAA,YAAA,CAAA;AAC/B,IAAA,oBAAA;AAAA,IAAA,qBAAA,QAAA,cAAA,aAAA,MAAA;AACA,IAAA,oBAAA;AAAA,IAAA,qBAAA,QAAA,cAAA,aAAA,QAAA;;;;;;AAcb,IAAA,yBAAA,GAAA,OAAA,EAAA,EAAkE,GAAA,UAAA,EAAA;AACxD,IAAA,qBAAA,SAAA,SAAA,+EAAA;AAAA,MAAA,wBAAA,GAAA;AAAA,YAAA,SAAA,wBAAA,CAAA;AAAA,aAAA,sBAAS,OAAA,0BAAA,CAA2B;IAAA,CAAA;AAC1C,IAAA,iBAAA,CAAA;AACF,IAAA,uBAAA,EAAS;;;;AADP,IAAA,oBAAA,CAAA;AAAA,IAAA,6BAAA,0CAAA,OAAA,mBAAA,QAAA,IAAA;;;;;AA3CN,IAAA,yBAAA,GAAA,OAAA,EAAA,EAA4F,GAAA,OAAA,EAAA,EAC9D,GAAA,MAAA;AACpB,IAAA,iBAAA,GAAA,+BAAA;AAAsB,IAAA,uBAAA;AAC5B,IAAA,yBAAA,GAAA,QAAA,EAAA;AAA0B,IAAA,iBAAA,CAAA;AAAsB,IAAA,uBAAA,EAAO;AAGzD,IAAA,qBAAA,GAAA,sDAAA,IAAA,IAAA,OAAA,EAAA,EAKqD,GAAA,sDAAA,GAAA,GAAA,OAAA,EAAA;AAmCvD,IAAA,uBAAA;;;;AA3C8B,IAAA,oBAAA,CAAA;AAAA,IAAA,4BAAA,OAAA,gBAAA;AAIH,IAAA,oBAAA;AAAA,IAAA,qBAAA,WAAA,OAAA,mBAAA,MAAA,GAAA,CAAA,CAAA;AAkCnB,IAAA,oBAAA;AAAA,IAAA,qBAAA,QAAA,OAAA,mBAAA,SAAA,CAAA;;;;;;AAQR,IAAA,yBAAA,GAAA,OAAA,EAAA,EAA4G,GAAA,OAAA,EAAA,EAC9E,GAAA,MAAA;AACpB,IAAA,iBAAA,GAAA,+BAAA;AAAsB,IAAA,uBAAA;AAC5B,IAAA,yBAAA,GAAA,QAAA,EAAA;AAA6B,IAAA,iBAAA,CAAA;AAAuB,IAAA,uBAAA,EAAO;AAE7D,IAAA,yBAAA,GAAA,OAAA,EAAA;AAA0B,IAAA,qBAAA,SAAA,SAAA,sEAAA;AAAA,MAAA,wBAAA,GAAA;AAAA,YAAA,SAAA,wBAAA,CAAA;AAAA,aAAA,sBAAS,OAAA,aAAA,CAAc;IAAA,CAAA;AAC/C,IAAA,yBAAA,GAAA,OAAA,EAAA,EAA6B,GAAA,OAAA,EAAA;AACA,IAAA,iBAAA,GAAA,qCAAA;AAAgC,IAAA,uBAAA;AAC3D,IAAA,yBAAA,IAAA,OAAA,EAAA;AAA6B,IAAA,iBAAA,EAAA;AAA2D,IAAA,uBAAA,EAAM,EAC1F,EACF;;;;AAPyB,IAAA,oBAAA,CAAA;AAAA,IAAA,4BAAA,OAAA,iBAAA;AAKE,IAAA,oBAAA,CAAA;AAAA,IAAA,6BAAA,IAAA,OAAA,mBAAA,sCAAA;;;;;AAKnC,IAAA,oBAAA,GAAA,OAAA,EAAA;;;;;AAIE,IAAA,yBAAA,GAAA,OAAA,EAAA;;AACE,IAAA,yBAAA,GAAA,OAAA,EAAA;AACE,IAAA,oBAAA,GAAA,QAAA,EAAA,EAA2D,GAAA,UAAA,EAAA;AAE7D,IAAA,uBAAA;;AACA,IAAA,yBAAA,GAAA,GAAA;AAAG,IAAA,iBAAA,GAAA,qBAAA;AAAmB,IAAA,uBAAA;AACtB,IAAA,yBAAA,GAAA,QAAA,EAAA;AAAuC,IAAA,iBAAA,GAAA,0BAAA;AAAkB,IAAA,uBAAA,EAAO;;;;;AAIlE,IAAA,yBAAA,GAAA,OAAA,EAAA;;AACE,IAAA,yBAAA,GAAA,OAAA,EAAA;AACE,IAAA,oBAAA,GAAA,QAAA,EAAA;AACF,IAAA,uBAAA;;AACA,IAAA,yBAAA,GAAA,GAAA;AAAG,IAAA,iBAAA,GAAA,qBAAA;AAAmB,IAAA,uBAAA;AACtB,IAAA,yBAAA,GAAA,QAAA,EAAA;AAAuC,IAAA,iBAAA,GAAA,6BAAA;AAAwB,IAAA,uBAAA,EAAO;;;;;;AAGxE,IAAA,yBAAA,GAAA,OAAA,EAAA;AAIE,IAAA,qBAAA,SAAA,SAAA,uEAAA;AAAA,YAAA,kBAAA,wBAAA,GAAA,EAAA;AAAA,YAAA,SAAA,wBAAA,CAAA;AAAA,aAAA,sBAAS,OAAA,WAAA,eAAA,CAAwB;IAAA,CAAA;AAEjC,IAAA,yBAAA,GAAA,OAAA,EAAA,EAAkC,GAAA,KAAA,EAAA;AACH,IAAA,iBAAA,CAAA;AAA0B,IAAA,uBAAA;AACvD,IAAA,yBAAA,GAAA,QAAA,EAAA;AAAgC,IAAA,iBAAA,CAAA;AAAwC,IAAA,uBAAA,EAAO;AAGjF,IAAA,yBAAA,GAAA,UAAA,EAAA;AAEE,IAAA,qBAAA,SAAA,SAAA,wEAAA,QAAA;AAAA,YAAA,kBAAA,wBAAA,GAAA,EAAA;AAAA,YAAA,SAAA,wBAAA,CAAA;AAAA,aAAA,sBAAS,OAAA,mBAAA,gBAAA,IAAA,MAAA,CAA2C;IAAA,CAAA;;AACpD,IAAA,yBAAA,GAAA,OAAA,EAAA;AACE,IAAA,oBAAA,GAAA,QAAA,EAAA,EAA2C,GAAA,QAAA,EAAA;AAE7C,IAAA,uBAAA,EAAM,EACC;;;;;AAfT,IAAA,sBAAA,UAAA,CAAA,gBAAA,MAAA;AAI+B,IAAA,oBAAA,CAAA;AAAA,IAAA,4BAAA,gBAAA,OAAA;AACG,IAAA,oBAAA,CAAA;AAAA,IAAA,4BAAA,OAAA,WAAA,gBAAA,SAAA,CAAA;;;;;;AAapC,IAAA,yBAAA,GAAA,OAAA,EAAA,EAAuD,GAAA,UAAA,EAAA;AAC7C,IAAA,qBAAA,SAAA,SAAA,0EAAA;AAAA,MAAA,wBAAA,IAAA;AAAA,YAAA,SAAA,wBAAA,CAAA;AAAA,aAAA,sBAAS,OAAA,qBAAA,CAAsB;IAAA,CAAA;AACrC,IAAA,iBAAA,GAAA,iCAAA;AACF,IAAA,uBAAA,EAAS;;;;;;AA5Hf,IAAA,yBAAA,GAAA,OAAA,CAAA;AAAgD,IAAA,qBAAA,SAAA,SAAA,8DAAA,QAAA;AAAA,MAAA,wBAAA,GAAA;AAAA,aAAA,sBAAS,OAAA,gBAAA,CAAwB;IAAA,CAAA;AAE/E,IAAA,oBAAA,GAAA,OAAA,CAAA;AACA,IAAA,yBAAA,GAAA,OAAA,EAAA,EAAiC,GAAA,IAAA;AAC3B,IAAA,iBAAA,GAAA,eAAA;AAAa,IAAA,uBAAA;AACjB,IAAA,qBAAA,GAAA,mDAAA,GAAA,GAAA,UAAA,EAAA;AAMF,IAAA,uBAAA;AAEA,IAAA,oBAAA,GAAA,OAAA,EAAA;AAGA,IAAA,qBAAA,GAAA,gDAAA,GAAA,GAAA,OAAA,EAAA,EAA4F,GAAA,gDAAA,IAAA,GAAA,OAAA,EAAA,EAiDgB,GAAA,gDAAA,GAAA,GAAA,OAAA,EAAA;AAe5G,IAAA,yBAAA,IAAA,OAAA,EAAA;AAEE,IAAA,qBAAA,IAAA,iDAAA,GAAA,GAAA,OAAA,EAAA,EAA+E,IAAA,iDAAA,GAAA,GAAA,OAAA,EAAA,EAU6D,IAAA,iDAAA,IAAA,GAAA,OAAA,EAAA,EAYvG,IAAA,iDAAA,GAAA,GAAA,OAAA,EAAA;AAsBvC,IAAA,uBAAA,EAAM;;;;AAxHD,IAAA,oBAAA,CAAA;AAAA,IAAA,qBAAA,QAAA,OAAA,cAAA,CAAA;AAUC,IAAA,oBAAA,CAAA;AAAA,IAAA,qBAAA,QAAA,OAAA,QAAA,KAAA,OAAA,mBAAA,SAAA,CAAA;AAiDA,IAAA,oBAAA;AAAA,IAAA,qBAAA,QAAA,OAAA,QAAA,KAAA,OAAA,oBAAA,KAAA,OAAA,mBAAA,WAAA,CAAA;AAaA,IAAA,oBAAA;AAAA,IAAA,qBAAA,QAAA,OAAA,QAAA,MAAA,OAAA,mBAAA,SAAA,KAAA,OAAA,oBAAA,EAAA;AAIE,IAAA,oBAAA,CAAA;AAAA,IAAA,qBAAA,QAAA,CAAA,OAAA,QAAA,KAAA,OAAA,cAAA,WAAA,CAAA;AAUA,IAAA,oBAAA;AAAA,IAAA,qBAAA,QAAA,OAAA,QAAA,KAAA,OAAA,cAAA,WAAA,KAAA,OAAA,mBAAA,WAAA,KAAA,OAAA,sBAAA,CAAA;AASqB,IAAA,oBAAA;AAAA,IAAA,qBAAA,WAAA,OAAA,cAAA,MAAA,GAAA,CAAA,CAAA;AAoBrB,IAAA,oBAAA;AAAA,IAAA,qBAAA,QAAA,OAAA,cAAA,SAAA,CAAA;;;AAofV,IAAO,4BAAP,MAAO,2BAAyB;EAe1B;EACA;EACA;EACA;EACA;EACA;EAnBV,gBAAmC,CAAA;EACnC,qBAA0C,CAAA;EAC1C,cAAc;EACd,WAAW;EACX,oBAAoB;EACZ,gBAAgB,IAAI,aAAY;EAExC,IAAI,mBAAgB;AAClB,UAAM,QAAQ,KAAK,mBAAmB,OAAO,OAAK,CAAC,EAAE,MAAM,EAAE;AAC7D,YAAQ,IAAI,+BAAwB,OAAO,OAAO,KAAK,mBAAmB,QAAQ,qBAAqB;AACvG,WAAO;EACT;EAEA,YACU,qBACA,aACA,QACA,gBACA,kBACA,0BAAkD;AALlD,SAAA,sBAAA;AACA,SAAA,cAAA;AACA,SAAA,SAAA;AACA,SAAA,iBAAA;AACA,SAAA,mBAAA;AACA,SAAA,2BAAA;EACP;EAEH,WAAQ;AACN,SAAK,cAAc,IACjB,KAAK,oBAAoB,eAAe,UAAU,mBAAgB;AAChE,WAAK,gBAAgB;IACvB,CAAC,CAAC;AAGJ,SAAK,cAAc,IACjB,KAAK,oBAAoB,aAAa,UAAU,WAAQ;AACtD,WAAK,cAAc;IACrB,CAAC,CAAC;AAIJ,QAAI,KAAK,QAAO,GAAI;AAClB,WAAK,cAAc,IACjB,KAAK,yBAAyB,eAAe,UAAU,wBAAqB;AAC1E,aAAK,qBAAqB;MAC5B,CAAC,CAAC;AAMJ,WAAK,uBAAsB;IAC7B;AAEA,SAAK,kBAAiB;AACtB,SAAK,sBAAqB;AAG1B,QAAI,KAAK,QAAO,GAAI;AAClB,WAAK,cAAc,IACjB,SAAS,GAAK,EAAE,UAAU,MAAK;AAC7B,aAAK,sBAAqB;AAC1B,aAAK,uBAAsB;MAC7B,CAAC,CAAC;IAEN;EACF;EAEA,cAAW;AACT,SAAK,cAAc,YAAW;EAChC;EAEA,WAAW,OAAa;AACtB,YAAQ,IAAI,mDAAyC,KAAK,QAAQ;AAGlE,QAAI,OAAO;AACT,YAAM,gBAAe;AACrB,YAAM,eAAc;IACtB;AAEA,SAAK,WAAW,CAAC,KAAK;AACtB,YAAQ,IAAI,gCAAsB,KAAK,QAAQ;AAE/C,QAAI,KAAK,UAAU;AACjB,cAAQ,IAAI,wDAAiD;AAC7D,WAAK,kBAAiB;AAGtB,UAAI,KAAK,QAAO,GAAI;AAClB,gBAAQ,IAAI,kFAAqE;AACjF,aAAK,uBAAsB;MAC7B;AAGA,iBAAW,MAAK;AACd,iBAAS,iBAAiB,SAAS,KAAK,UAAU,KAAK,IAAI,CAAC;MAC9D,GAAG,GAAG;IACR,OAAO;AACL,cAAQ,IAAI,yBAAe;AAC3B,eAAS,oBAAoB,SAAS,KAAK,UAAU,KAAK,IAAI,CAAC;IACjE;EACF;EAEA,UAAU,OAAa;AACrB,YAAQ,IAAI,+BAAqB;AACjC,SAAK,WAAW;AAChB,aAAS,oBAAoB,SAAS,KAAK,UAAU,KAAK,IAAI,CAAC;EACjE;EAEA,kBAAkB,OAAY;AAC5B,YAAQ,IAAI,+DAA6C;AACzD,UAAM,gBAAe;EACvB;EAEA,+BAA+B,oBAA2B,wBAA6B;AACrF,UAAM,gBAAqC,CAAA;AAC3C,YAAQ,IAAI,uDAAgD;AAG5D,YAAQ,IAAI,uDAA6C,mBAAmB,MAAM;AAClF,uBAAmB,QAAQ,CAAC,SAAS,UAAS;AAC5C,cAAQ,IAAI,kCAAwB,QAAQ,CAAC,KAAK;QAChD,IAAI,QAAQ;QACZ,KAAK,QAAQ;QACb,QAAQ,QAAQ;QAChB,eAAe,QAAQ;QACvB,0BAA0B,QAAQ;QAClC,cAAc,QAAQ;QACtB,aAAa,QAAQ;OACtB;AAED,UAAI,QAAQ,WAAW,gBAAgB,QAAQ,WAAW,GAAG;AAC3D,cAAM,iBAAiB,QAAQ,4BACT,QAAQ,aAAa,iBACrB,QAAQ,kBACR,gBAAgB,QAAQ,aAAa;AAE3D,sBAAc,KAAK;UACjB,IAAI,SAAS,IAAI,QAAQ,EAAE,EAAE;;UAC7B,MAAM;UACN,OAAO;UACP,SAAS,kBAAkB,cAAc,2DAAkD,QAAQ,GAAG;UACtG;UACA,eAAe,QAAQ;UACvB,aAAa,QAAQ;UACrB,cAAc,IAAI,KAAK,QAAQ,gBAAgB,QAAQ,eAAe,KAAK,IAAG,CAAE;UAChF,QAAQ;UACR,UAAU;UACV,WAAW;SACZ;AACD,gBAAQ,IAAI,sDAAwC,QAAQ,GAAG,EAAE;MACnE,OAAO;AACL,gBAAQ,IAAI,yDAAyC,QAAQ,MAAM,GAAG;MACxE;IACF,CAAC;AAGD,YAAQ,IAAI,4DAAkD,uBAAuB,MAAM;AAC3F,2BAAuB,QAAQ,CAAC,SAAS,UAAS;AAChD,cAAQ,IAAI,uCAA6B,QAAQ,CAAC,KAAK;QACrD,IAAI,QAAQ;QACZ,KAAK,QAAQ;QACb,QAAQ,QAAQ;QAChB,eAAe,QAAQ;QACvB,0BAA0B,QAAQ;OACnC;AAED,UAAI,QAAQ,WAAW,gBAAgB,QAAQ,WAAW,GAAG;AAC3D,cAAM,iBAAiB,QAAQ,4BACT,QAAQ,aAAa,iBACrB,QAAQ,kBACR,gBAAgB,QAAQ,aAAa;AAE3D,sBAAc,KAAK;UACjB,IAAI,SAAS,IAAI,QAAQ,EAAE,EAAE;;UAC7B,MAAM;UACN,OAAO;UACP,SAAS,kBAAkB,cAAc,gEAAuD,QAAQ,GAAG;UAC3G;UACA,eAAe,QAAQ;UACvB,aAAa,QAAQ;UACrB,cAAc,IAAI,KAAK,QAAQ,gBAAgB,QAAQ,eAAe,KAAK,IAAG,CAAE;UAChF,QAAQ;UACR,UAAU;UACV,WAAW;SACZ;AACD,gBAAQ,IAAI,2DAA6C,QAAQ,GAAG,EAAE;MACxE,OAAO;AACL,gBAAQ,IAAI,8DAA8C,QAAQ,MAAM,GAAG;MAC7E;IACF,CAAC;AAGD,kBAAc,KAAK,CAAC,GAAG,MAAM,IAAI,KAAK,EAAE,YAAY,EAAE,QAAO,IAAK,IAAI,KAAK,EAAE,YAAY,EAAE,QAAO,CAAE;AAEpG,YAAQ,IAAI,+CAAkC,cAAc,MAAM,EAAE;AACpE,WAAO;EACT;EAEA,oBAAiB;AACf,YAAQ,IAAI,uCAA6B;AACzC,UAAM,cAAc,KAAK,YAAY,eAAc;AACnD,YAAQ,IAAI,iCAA0B,WAAW;AAEjD,QAAI,aAAa,IAAI;AACnB,cAAQ,IAAI,+DAAwD,YAAY,EAAE;AAClF,WAAK,oBAAoB,qBAAqB,YAAY,EAAE,EAAE,UAAU;QACtE,MAAM,CAAC,kBAAiB;AACtB,kBAAQ,IAAI,mCAA2B,aAAa;QACtD;QACA,OAAO,CAAC,UAAS;AACf,kBAAQ,MAAM,uDAAkD,KAAK;QACvE;OACD;IACH,OAAO;AACL,cAAQ,IAAI,sCAA8B;IAC5C;EACF;EAEA,yBAAsB;AACpB,YAAQ,IAAI,4CAAkC;AAC9C,QAAI,KAAK,QAAO,GAAI;AAClB,cAAQ,IAAI,8EAAoE;AAGhF,eAAS;QACP,oBAAoB,KAAK,eAAe,yBAAwB;QAChE,wBAAwB,KAAK,eAAe,6BAA4B;OACzE,EAAE,UAAU;QACX,MAAM,CAAC,WAAU;AACf,kBAAQ,IAAI,wCAA0B,MAAM;AAC5C,kBAAQ,IAAI,+CAAqC,OAAO,oBAAoB,UAAU,CAAC;AACvF,kBAAQ,IAAI,oDAA0C,OAAO,wBAAwB,UAAU,CAAC;AAGhG,cAAI,OAAO,oBAAoB,SAAS,GAAG;AACzC,oBAAQ,IAAI,2CAAiC,OAAO,mBAAmB,CAAC,CAAC;UAC3E;AACA,cAAI,OAAO,wBAAwB,SAAS,GAAG;AAC7C,oBAAQ,IAAI,gDAAsC,OAAO,uBAAuB,CAAC,CAAC;UACpF;AAGA,eAAK,qBAAqB,KAAK,+BAC7B,OAAO,sBAAsB,CAAA,GAC7B,OAAO,0BAA0B,CAAA,CAAE;AAGrC,kBAAQ,IAAI,4CAAiC,KAAK,kBAAkB;AACpE,kBAAQ,IAAI,kDAA2C,KAAK,mBAAmB,MAAM;QACvF;QACA,OAAO,CAAC,UAAS;AACf,kBAAQ,MAAM,kDAA6C,KAAK;AAEhE,eAAK,qBAAqB,KAAK,yBAAyB,qBAAoB;QAC9E;OACD;IACH;EACF;EAEA,WAAW,cAA6B;AACtC,QAAI,CAAC,aAAa,QAAQ;AACxB,WAAK,oBAAoB,WAAW,aAAa,EAAE,EAAE,UAAS;IAChE;EACF;EAEA,gBAAa;AACX,UAAM,sBAAsB,KAAK,cAAc,OAAO,OAAK,CAAC,EAAE,MAAM;AACpE,wBAAoB,QAAQ,kBAAe;AACzC,WAAK,oBAAoB,WAAW,aAAa,EAAE,EAAE,UAAS;IAChE,CAAC;EACH;EAEA,mBAAmB,gBAAwB,OAAY;AACrD,UAAM,gBAAe;AACrB,SAAK,oBAAoB,mBAAmB,cAAc,EAAE,UAAS;EACvE;EAEA,uBAAoB;AAClB,SAAK,OAAO,SAAS,CAAC,gBAAgB,CAAC;EACzC;EAEA,eAAY;AACV,SAAK,OAAO,SAAS,CAAC,2BAA2B,CAAC;AAClD,SAAK,UAAS;EAChB;EAEA,UAAO;AAEL,UAAM,iBAAiB,KAAK,YAAY,QAAO;AAE/C,UAAM,sBAAsB,KAAK,iBAAiB,eAAc,MAAO;AAEvE,YAAQ,IAAI,kDAAwC,cAAc;AAClE,YAAQ,IAAI,uDAA6C,mBAAmB;AAE5E,UAAM,SAAS,kBAAkB;AACjC,YAAQ,IAAI,wCAA8B,MAAM;AAEhD,WAAO;EACT;EAEA,6BAA6B,cAA+B;AAC1D,YAAQ,IAAI,0CAAmC,YAAY;AAG3D,iBAAa,SAAS;AAGtB,QAAI,aAAa,WAAW;AAC1B,WAAK,OAAO,SAAS,CAAC,aAAa,SAAS,CAAC;IAC/C;AAEA,SAAK,UAAS;EAChB;EAEA,wBAAwB,gBAAwB,OAAY;AAC1D,UAAM,gBAAe;AACrB,YAAQ,IAAI,mDAAuC,cAAc;AAGjE,SAAK,qBAAqB,KAAK,mBAAmB,OAAO,OAAK,EAAE,OAAO,cAAc;AACrF,YAAQ,IAAI,6CAAqC;EACnD;EAEA,4BAAyB;AACvB,SAAK,OAAO,SAAS,CAAC,sBAAsB,CAAC;AAC7C,SAAK,UAAS;EAChB;EAEA,wBAAqB;AACnB,YAAQ,IAAI,2CAAiC;AAC7C,YAAQ,IAAI,wBAAiB,KAAK,QAAO,CAAE;AAC3C,YAAQ,IAAI,4CAAqC,KAAK,YAAY,gBAAe,CAAE;AACnF,YAAQ,IAAI,qCAA8B,CAAC,CAAC,KAAK,YAAY,SAAQ,CAAE;AACvE,YAAQ,IAAI,gDAAyC,CAAC,CAAC,KAAK,iBAAiB,eAAc,CAAE;AAE7F,QAAI,KAAK,QAAO,GAAI;AAClB,cAAQ,IAAI,oEAAyD;AACrE,eAAS;QACP,YAAY,KAAK,eAAe,8BAA8B,cAAc,SAAS;QACrF,gBAAgB,KAAK,eAAe,kCAAkC,cAAc,SAAS;OAC9F,EAAE,UAAU;QACX,MAAM,CAAC,WAAU;AACf,kBAAQ,IAAI,gCAAwB,MAAM;AAC1C,eAAK,oBAAoB,OAAO,WAAW,SAAS,OAAO,eAAe;QAC5E;QACA,OAAO,CAAC,UAAS;AACf,kBAAQ,MAAM,6DAAwD,KAAK;QAC7E;OACD;IACH,OAAO;AACL,cAAQ,IAAI,8DAAyD;IACvE;EACF;EAEA,IAAI,mBAAgB;AAClB,UAAM,mBAAmB,KAAK,QAAO,IAAK,KAAK,mBAAmB;AAClE,UAAM,oBAAoB,KAAK,eAAe;AAI9C,UAAM,QAAQ,KAAK,QAAO,IAAK,mBAAmB;AAElD,YAAQ,IAAI,2BAAoB;MAC9B,SAAS,KAAK,QAAO;MACrB;MACA;MACA;KACD;AAED,WAAO,KAAK,IAAI,GAAG,KAAK;EAC1B;EAEA,WAAW,MAAU;AACnB,UAAM,MAAM,oBAAI,KAAI;AACpB,UAAM,YAAY,IAAI,KAAK,IAAI;AAC/B,UAAM,gBAAgB,KAAK,OAAO,IAAI,QAAO,IAAK,UAAU,QAAO,MAAO,MAAO,GAAG;AAEpF,QAAI,gBAAgB;AAAG,aAAO;AAC9B,QAAI,gBAAgB;AAAI,aAAO,UAAU,aAAa;AAEtD,UAAM,cAAc,KAAK,MAAM,gBAAgB,EAAE;AACjD,QAAI,cAAc;AAAI,aAAO,UAAU,WAAW;AAElD,UAAM,aAAa,KAAK,MAAM,cAAc,EAAE;AAC9C,QAAI,aAAa;AAAG,aAAO,UAAU,UAAU;AAE/C,WAAO,UAAU,mBAAmB,OAAO;EAC7C;;qCAlYW,4BAAyB,4BAAA,mBAAA,GAAA,4BAAA,WAAA,GAAA,4BAAA,MAAA,GAAA,4BAAA,cAAA,GAAA,4BAAA,gBAAA,GAAA,4BAAA,wBAAA,CAAA;EAAA;yEAAzB,4BAAyB,WAAA,CAAA,CAAA,uBAAA,CAAA,GAAA,OAAA,GAAA,MAAA,GAAA,QAAA,CAAA,CAAA,GAAA,sBAAA,GAAA,CAAA,QAAA,UAAA,GAAA,oBAAA,GAAA,SAAA,aAAA,OAAA,GAAA,CAAA,SAAA,MAAA,UAAA,MAAA,WAAA,aAAA,QAAA,QAAA,UAAA,gBAAA,gBAAA,GAAA,GAAA,CAAA,KAAA,6CAAA,GAAA,CAAA,KAAA,4BAAA,GAAA,CAAA,SAAA,sBAAA,GAAA,MAAA,GAAA,CAAA,SAAA,qBAAA,GAAA,SAAA,GAAA,MAAA,GAAA,CAAA,GAAA,oBAAA,GAAA,CAAA,GAAA,qBAAA,GAAA,OAAA,GAAA,CAAA,GAAA,oBAAA,GAAA,CAAA,GAAA,qBAAA,GAAA,CAAA,SAAA,qBAAA,GAAA,SAAA,GAAA,MAAA,GAAA,CAAA,GAAA,sBAAA,GAAA,CAAA,SAAA,+BAAA,GAAA,MAAA,GAAA,CAAA,SAAA,oBAAA,GAAA,MAAA,GAAA,CAAA,SAAA,wBAAA,GAAA,MAAA,GAAA,CAAA,GAAA,mBAAA,GAAA,CAAA,SAAA,oBAAA,GAAA,MAAA,GAAA,CAAA,SAAA,qBAAA,GAAA,UAAA,SAAA,GAAA,SAAA,SAAA,GAAA,CAAA,SAAA,YAAA,GAAA,MAAA,GAAA,CAAA,GAAA,qBAAA,GAAA,OAAA,GAAA,CAAA,GAAA,6BAAA,GAAA,CAAA,GAAA,gBAAA,GAAA,CAAA,GAAA,aAAA,GAAA,CAAA,SAAA,2BAAA,GAAA,UAAA,iBAAA,SAAA,GAAA,SAAA,SAAA,GAAA,CAAA,SAAA,kBAAA,GAAA,MAAA,GAAA,CAAA,GAAA,2BAAA,GAAA,OAAA,GAAA,CAAA,GAAA,yBAAA,GAAA,CAAA,GAAA,MAAA,GAAA,CAAA,GAAA,4BAAA,GAAA,CAAA,GAAA,0BAAA,GAAA,CAAA,GAAA,4BAAA,GAAA,CAAA,GAAA,yBAAA,GAAA,CAAA,GAAA,yBAAA,GAAA,CAAA,SAAA,uBAAA,GAAA,MAAA,GAAA,CAAA,SAAA,yBAAA,GAAA,MAAA,GAAA,CAAA,GAAA,oBAAA,GAAA,OAAA,GAAA,CAAA,SAAA,MAAA,UAAA,MAAA,WAAA,aAAA,QAAA,QAAA,UAAA,gBAAA,gBAAA,GAAA,GAAA,CAAA,MAAA,MAAA,MAAA,KAAA,MAAA,KAAA,MAAA,IAAA,GAAA,CAAA,MAAA,KAAA,MAAA,KAAA,MAAA,MAAA,MAAA,IAAA,GAAA,CAAA,GAAA,kBAAA,MAAA,GAAA,CAAA,GAAA,kBAAA,QAAA,GAAA,CAAA,GAAA,gBAAA,GAAA,CAAA,GAAA,OAAA,GAAA,CAAA,GAAA,kBAAA,GAAA,CAAA,GAAA,gBAAA,GAAA,CAAA,GAAA,gBAAA,GAAA,OAAA,GAAA,CAAA,GAAA,iBAAA,GAAA,CAAA,GAAA,eAAA,GAAA,CAAA,GAAA,iBAAA,GAAA,CAAA,GAAA,kBAAA,GAAA,CAAA,SAAA,MAAA,UAAA,MAAA,WAAA,aAAA,QAAA,QAAA,UAAA,gBAAA,gBAAA,KAAA,GAAA,YAAA,GAAA,CAAA,KAAA,2CAAA,GAAA,CAAA,MAAA,MAAA,MAAA,KAAA,KAAA,GAAA,GAAA,CAAA,GAAA,0BAAA,GAAA,CAAA,KAAA,+CAAA,GAAA,CAAA,GAAA,qBAAA,GAAA,OAAA,GAAA,CAAA,GAAA,sBAAA,GAAA,CAAA,GAAA,mBAAA,GAAA,CAAA,GAAA,mBAAA,GAAA,CAAA,GAAA,cAAA,GAAA,OAAA,GAAA,CAAA,SAAA,MAAA,UAAA,MAAA,WAAA,aAAA,QAAA,QAAA,UAAA,gBAAA,gBAAA,GAAA,GAAA,CAAA,GAAA,UAAA,CAAA,GAAA,UAAA,SAAA,mCAAA,IAAA,KAAA;AAAA,QAAA,KAAA,GAAA;AA3nBlC,MAAA,yBAAA,GAAA,OAAA,CAAA,EAAkC,GAAA,UAAA,CAAA;AAK9B,MAAA,qBAAA,SAAA,SAAA,2DAAA,QAAA;AAAA,eAAS,IAAA,WAAA,MAAA;MAAkB,CAAA,EAAC,aAAA,SAAA,+DAAA,QAAA;AAAA,eACf,IAAA,kBAAA,MAAA;MAAyB,CAAA;;AACtC,MAAA,yBAAA,GAAA,OAAA,CAAA;AACE,MAAA,oBAAA,GAAA,QAAA,CAAA,EAA6D,GAAA,QAAA,CAAA;AAE/D,MAAA,uBAAA;AACA,MAAA,qBAAA,GAAA,2CAAA,GAAA,GAAA,QAAA,CAAA;AACF,MAAA,uBAAA;AAEA,MAAA,qBAAA,GAAA,0CAAA,IAAA,GAAA,OAAA,CAAA;AAgIF,MAAA,uBAAA;;;AA1II,MAAA,oBAAA;AAAA,MAAA,qBAAA,SAAA,eAAA;AAOO,MAAA,oBAAA,CAAA;AAAA,MAAA,qBAAA,QAAA,IAAA,mBAAA,CAAA;AAGH,MAAA,oBAAA;AAAA,MAAA,qBAAA,QAAA,IAAA,QAAA;;oBAhBA,cAAY,SAAA,IAAA,GAAA,QAAA,CAAA,y9TAAA,EAAA,CAAA;;;sEA6nBX,2BAAyB,CAAA;UAhoBrC;uBACW,yBAAuB,YACrB,MAAI,SACP,CAAC,YAAY,GAAC,UACb;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;KAgJT,QAAA,CAAA,k0RAAA,EAAA,CAAA;;;;6EA4eU,2BAAyB,EAAA,WAAA,6BAAA,UAAA,uEAAA,YAAA,IAAA,CAAA;AAAA,GAAA;", "names": []}
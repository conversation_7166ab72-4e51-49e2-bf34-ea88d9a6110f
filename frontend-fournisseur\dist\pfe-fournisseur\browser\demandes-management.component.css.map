{"version": 3, "sources": ["angular:styles/component:css;67ff0ed633472062b1408654866082fd251674b668e604c3aa692abb6f2b868f;C:/Users/<USER>/Desktop/STE/PFE/PFE/frontend-fournisseur/src/app/components/admin/demandes-management/demandes-management.component.ts"], "sourcesContent": ["\n    .demandes-management {\n      padding: 2rem;\n    }\n\n    .page-header {\n      margin-bottom: 2rem;\n    }\n\n    .page-header h1 {\n      margin: 0 0 0.5rem 0;\n      color: #1e293b;\n    }\n\n    .page-header p {\n      margin: 0;\n      color: #64748b;\n    }\n\n    .tabs {\n      display: flex;\n      border-bottom: 2px solid #e2e8f0;\n      margin-bottom: 2rem;\n    }\n\n    .tab-btn {\n      padding: 1rem 2rem;\n      background: none;\n      border: none;\n      cursor: pointer;\n      font-size: 1rem;\n      color: #64748b;\n      border-bottom: 2px solid transparent;\n      transition: all 0.3s ease;\n      position: relative;\n    }\n\n    .tab-btn.active {\n      color: #3b82f6;\n      border-bottom-color: #3b82f6;\n    }\n\n    .badge {\n      background: #ef4444;\n      color: white;\n      border-radius: 50%;\n      padding: 0.25rem 0.5rem;\n      font-size: 0.75rem;\n      margin-left: 0.5rem;\n    }\n\n    .filters {\n      margin-bottom: 1.5rem;\n    }\n\n    .filters select {\n      padding: 0.5rem 1rem;\n      border: 1px solid #d1d5db;\n      border-radius: 8px;\n      background: white;\n    }\n\n    .empty-state {\n      text-align: center;\n      padding: 3rem;\n      color: #64748b;\n    }\n\n    .demandes-grid {\n      display: grid;\n      grid-template-columns: repeat(auto-fill, minmax(400px, 1fr));\n      gap: 1.5rem;\n    }\n\n    .demande-card {\n      background: white;\n      border-radius: 12px;\n      padding: 1.5rem;\n      box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);\n      border: 1px solid #e2e8f0;\n    }\n\n    .demande-header {\n      display: flex;\n      justify-content: space-between;\n      align-items: center;\n      margin-bottom: 1rem;\n    }\n\n    .demande-header h3 {\n      margin: 0;\n      color: #1e293b;\n    }\n\n    .statut-badge {\n      padding: 0.25rem 0.75rem;\n      border-radius: 20px;\n      color: white;\n      font-size: 0.875rem;\n      font-weight: 500;\n    }\n\n    .demande-content p {\n      margin: 0.5rem 0;\n      color: #475569;\n    }\n\n    .image-preview {\n      margin: 1rem 0;\n    }\n\n    .image-preview img {\n      max-width: 100px;\n      max-height: 100px;\n      border-radius: 8px;\n      object-fit: cover;\n    }\n\n    .demande-actions {\n      display: flex;\n      gap: 0.5rem;\n      margin-top: 1rem;\n      padding-top: 1rem;\n      border-top: 1px solid #e2e8f0;\n    }\n\n    .admin-comment {\n      margin-top: 1rem;\n      padding-top: 1rem;\n      border-top: 1px solid #e2e8f0;\n      background: #f8fafc;\n      padding: 1rem;\n      border-radius: 8px;\n    }\n\n    .btn {\n      padding: 0.5rem 1rem;\n      border: none;\n      border-radius: 6px;\n      cursor: pointer;\n      font-size: 0.875rem;\n      transition: all 0.3s ease;\n    }\n\n    .btn-success {\n      background: #10b981;\n      color: white;\n    }\n\n    .btn-success:hover {\n      background: #059669;\n    }\n\n    .btn-danger {\n      background: #ef4444;\n      color: white;\n    }\n\n    .btn-danger:hover {\n      background: #dc2626;\n    }\n\n    .btn-secondary {\n      background: #6b7280;\n      color: white;\n    }\n\n    .btn-secondary:hover {\n      background: #4b5563;\n    }\n\n    .modal-overlay {\n      position: fixed;\n      top: 0;\n      left: 0;\n      right: 0;\n      bottom: 0;\n      background: rgba(0, 0, 0, 0.5);\n      display: flex;\n      align-items: center;\n      justify-content: center;\n      z-index: 1000;\n    }\n\n    .modal {\n      background: white;\n      border-radius: 12px;\n      width: 90%;\n      max-width: 500px;\n      max-height: 90vh;\n      overflow-y: auto;\n    }\n\n    .modal-header {\n      display: flex;\n      justify-content: space-between;\n      align-items: center;\n      padding: 1.5rem;\n      border-bottom: 1px solid #e2e8f0;\n    }\n\n    .modal-header h3 {\n      margin: 0;\n    }\n\n    .close-btn {\n      background: none;\n      border: none;\n      font-size: 1.5rem;\n      cursor: pointer;\n      color: #6b7280;\n    }\n\n    .modal-content {\n      padding: 1.5rem;\n    }\n\n    .modal-content textarea {\n      width: 100%;\n      padding: 0.75rem;\n      border: 1px solid #d1d5db;\n      border-radius: 8px;\n      margin-top: 1rem;\n      resize: vertical;\n    }\n\n    .modal-actions {\n      display: flex;\n      justify-content: flex-end;\n      gap: 0.5rem;\n      padding: 1.5rem;\n      border-top: 1px solid #e2e8f0;\n    }\n  "], "mappings": ";AACI,CAAC;AACC,WAAS;AACX;AAEA,CAAC;AACC,iBAAe;AACjB;AAEA,CAJC,YAIY;AACX,UAAQ,EAAE,EAAE,OAAO;AACnB,SAAO;AACT;AAEA,CATC,YASY;AACX,UAAQ;AACR,SAAO;AACT;AAEA,CAAC;AACC,WAAS;AACT,iBAAe,IAAI,MAAM;AACzB,iBAAe;AACjB;AAEA,CAAC;AACC,WAAS,KAAK;AACd,cAAY;AACZ,UAAQ;AACR,UAAQ;AACR,aAAW;AACX,SAAO;AACP,iBAAe,IAAI,MAAM;AACzB,cAAY,IAAI,KAAK;AACrB,YAAU;AACZ;AAEA,CAZC,OAYO,CAAC;AACP,SAAO;AACP,uBAAqB;AACvB;AAEA,CAAC;AACC,cAAY;AACZ,SAAO;AACP,iBAAe;AACf,WAAS,QAAQ;AACjB,aAAW;AACX,eAAa;AACf;AAEA,CAAC;AACC,iBAAe;AACjB;AAEA,CAJC,QAIQ;AACP,WAAS,OAAO;AAChB,UAAQ,IAAI,MAAM;AAClB,iBAAe;AACf,cAAY;AACd;AAEA,CAAC;AACC,cAAY;AACZ,WAAS;AACT,SAAO;AACT;AAEA,CAAC;AACC,WAAS;AACT,yBAAuB,OAAO,SAAS,EAAE,OAAO,KAAK,EAAE;AACvD,OAAK;AACP;AAEA,CAAC;AACC,cAAY;AACZ,iBAAe;AACf,WAAS;AACT,cAAY,EAAE,IAAI,IAAI,KAAK,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE;AACpC,UAAQ,IAAI,MAAM;AACpB;AAEA,CAAC;AACC,WAAS;AACT,mBAAiB;AACjB,eAAa;AACb,iBAAe;AACjB;AAEA,CAPC,eAOe;AACd,UAAQ;AACR,SAAO;AACT;AAEA,CAAC;AACC,WAAS,QAAQ;AACjB,iBAAe;AACf,SAAO;AACP,aAAW;AACX,eAAa;AACf;AAEA,CAAC,gBAAgB;AACf,UAAQ,OAAO;AACf,SAAO;AACT;AAEA,CAAC;AACC,UAAQ,KAAK;AACf;AAEA,CAJC,cAIc;AACb,aAAW;AACX,cAAY;AACZ,iBAAe;AACf,cAAY;AACd;AAEA,CAAC;AACC,WAAS;AACT,OAAK;AACL,cAAY;AACZ,eAAa;AACb,cAAY,IAAI,MAAM;AACxB;AAEA,CAAC;AACC,cAAY;AACZ,eAAa;AACb,cAAY,IAAI,MAAM;AACtB,cAAY;AACZ,WAAS;AACT,iBAAe;AACjB;AAEA,CAAC;AACC,WAAS,OAAO;AAChB,UAAQ;AACR,iBAAe;AACf,UAAQ;AACR,aAAW;AACX,cAAY,IAAI,KAAK;AACvB;AAEA,CAAC;AACC,cAAY;AACZ,SAAO;AACT;AAEA,CALC,WAKW;AACV,cAAY;AACd;AAEA,CAAC;AACC,cAAY;AACZ,SAAO;AACT;AAEA,CALC,UAKU;AACT,cAAY;AACd;AAEA,CAAC;AACC,cAAY;AACZ,SAAO;AACT;AAEA,CALC,aAKa;AACZ,cAAY;AACd;AAEA,CAAC;AACC,YAAU;AACV,OAAK;AACL,QAAM;AACN,SAAO;AACP,UAAQ;AACR,cAAY,KAAK,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE;AAC1B,WAAS;AACT,eAAa;AACb,mBAAiB;AACjB,WAAS;AACX;AAEA,CAAC;AACC,cAAY;AACZ,iBAAe;AACf,SAAO;AACP,aAAW;AACX,cAAY;AACZ,cAAY;AACd;AAEA,CAAC;AACC,WAAS;AACT,mBAAiB;AACjB,eAAa;AACb,WAAS;AACT,iBAAe,IAAI,MAAM;AAC3B;AAEA,CARC,aAQa;AACZ,UAAQ;AACV;AAEA,CAAC;AACC,cAAY;AACZ,UAAQ;AACR,aAAW;AACX,UAAQ;AACR,SAAO;AACT;AAEA,CAAC;AACC,WAAS;AACX;AAEA,CAJC,cAIc;AACb,SAAO;AACP,WAAS;AACT,UAAQ,IAAI,MAAM;AAClB,iBAAe;AACf,cAAY;AACZ,UAAQ;AACV;AAEA,CAAC;AACC,WAAS;AACT,mBAAiB;AACjB,OAAK;AACL,WAAS;AACT,cAAY,IAAI,MAAM;AACxB;", "names": []}
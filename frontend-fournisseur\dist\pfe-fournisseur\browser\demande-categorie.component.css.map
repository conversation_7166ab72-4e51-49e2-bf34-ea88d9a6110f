{"version": 3, "sources": ["angular:styles/component:css;5434fe34c30caa7f4de433b4370c725c63c41934f20e026d0e4f129fd459f42a;C:/Users/<USER>/Desktop/STE/PFE/PFE/frontend-fournisseur/src/app/components/fournisseur/demande-categorie/demande-categorie.component.ts"], "sourcesContent": ["\n    .demande-categorie {\n      padding: 2rem;\n      max-width: 1200px;\n      margin: 0 auto;\n    }\n\n    .page-header {\n      margin-bottom: 2rem;\n    }\n\n    .page-header h1 {\n      margin: 0 0 0.5rem 0;\n      color: #1e293b;\n    }\n\n    .page-header p {\n      margin: 0;\n      color: #64748b;\n    }\n\n    .tabs {\n      display: flex;\n      border-bottom: 2px solid #e2e8f0;\n      margin-bottom: 2rem;\n    }\n\n    .tab-btn {\n      padding: 1rem 2rem;\n      background: none;\n      border: none;\n      cursor: pointer;\n      font-size: 1rem;\n      color: #64748b;\n      border-bottom: 2px solid transparent;\n      transition: all 0.3s ease;\n      position: relative;\n    }\n\n    .tab-btn.active {\n      color: #3b82f6;\n      border-bottom-color: #3b82f6;\n    }\n\n    .badge {\n      background: #ef4444;\n      color: white;\n      border-radius: 50%;\n      padding: 0.25rem 0.5rem;\n      font-size: 0.75rem;\n      margin-left: 0.5rem;\n    }\n\n    .form-tabs {\n      display: flex;\n      gap: 1rem;\n      margin-bottom: 2rem;\n    }\n\n    .form-tab-btn {\n      padding: 0.75rem 1.5rem;\n      background: #f8fafc;\n      border: 1px solid #e2e8f0;\n      border-radius: 8px;\n      cursor: pointer;\n      transition: all 0.3s ease;\n    }\n\n    .form-tab-btn.active {\n      background: #3b82f6;\n      color: white;\n      border-color: #3b82f6;\n    }\n\n    .form-container {\n      background: white;\n      padding: 2rem;\n      border-radius: 12px;\n      box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);\n      border: 1px solid #e2e8f0;\n    }\n\n    .form-group {\n      margin-bottom: 1.5rem;\n    }\n\n    .form-group label {\n      display: block;\n      margin-bottom: 0.5rem;\n      font-weight: 500;\n      color: #374151;\n    }\n\n    .form-group input,\n    .form-group select,\n    .form-group textarea {\n      width: 100%;\n      padding: 0.75rem;\n      border: 1px solid #d1d5db;\n      border-radius: 8px;\n      font-size: 1rem;\n      transition: border-color 0.3s ease;\n    }\n\n    .form-group input:focus,\n    .form-group select:focus,\n    .form-group textarea:focus {\n      outline: none;\n      border-color: #3b82f6;\n      box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);\n    }\n\n    .char-count {\n      text-align: right;\n      font-size: 0.875rem;\n      color: #6b7280;\n      margin-top: 0.25rem;\n    }\n\n    .error {\n      color: #ef4444;\n      font-size: 0.875rem;\n      margin-top: 0.25rem;\n    }\n\n    .form-actions {\n      display: flex;\n      gap: 1rem;\n      justify-content: flex-end;\n      margin-top: 2rem;\n    }\n\n    .btn {\n      padding: 0.75rem 1.5rem;\n      border: none;\n      border-radius: 8px;\n      cursor: pointer;\n      font-size: 1rem;\n      transition: all 0.3s ease;\n    }\n\n    .btn-primary {\n      background: #3b82f6;\n      color: white;\n    }\n\n    .btn-primary:hover:not(:disabled) {\n      background: #2563eb;\n    }\n\n    .btn-primary:disabled {\n      background: #9ca3af;\n      cursor: not-allowed;\n    }\n\n    .btn-secondary {\n      background: #6b7280;\n      color: white;\n    }\n\n    .btn-secondary:hover {\n      background: #4b5563;\n    }\n\n    .demandes-section {\n      margin-bottom: 3rem;\n    }\n\n    .demandes-section h3 {\n      margin-bottom: 1rem;\n      color: #1e293b;\n    }\n\n    .empty-state {\n      text-align: center;\n      padding: 2rem;\n      color: #64748b;\n      background: #f8fafc;\n      border-radius: 8px;\n      margin-bottom: 2rem;\n    }\n\n    .demandes-grid {\n      display: grid;\n      grid-template-columns: repeat(auto-fill, minmax(400px, 1fr));\n      gap: 1.5rem;\n    }\n\n    .demande-card {\n      background: white;\n      border-radius: 12px;\n      padding: 1.5rem;\n      box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);\n      border: 1px solid #e2e8f0;\n    }\n\n    .demande-header {\n      display: flex;\n      justify-content: space-between;\n      align-items: center;\n      margin-bottom: 1rem;\n    }\n\n    .demande-header h4 {\n      margin: 0;\n      color: #1e293b;\n    }\n\n    .statut-badge {\n      padding: 0.25rem 0.75rem;\n      border-radius: 20px;\n      color: white;\n      font-size: 0.875rem;\n      font-weight: 500;\n    }\n\n    .demande-content p {\n      margin: 0.5rem 0;\n      color: #475569;\n    }\n\n    .traitement-info {\n      margin-top: 1rem;\n      padding-top: 1rem;\n      border-top: 1px solid #e2e8f0;\n      background: #f8fafc;\n      padding: 1rem;\n      border-radius: 8px;\n    }\n\n    .alert {\n      padding: 1rem;\n      border-radius: 8px;\n      margin-top: 1rem;\n    }\n\n    .alert-success {\n      background: #dcfce7;\n      color: #166534;\n      border: 1px solid #bbf7d0;\n    }\n\n    .alert-error {\n      background: #fef2f2;\n      color: #dc2626;\n      border: 1px solid #fecaca;\n    }\n  "], "mappings": ";AACI,CAAC;AACC,WAAS;AACT,aAAW;AACX,UAAQ,EAAE;AACZ;AAEA,CAAC;AACC,iBAAe;AACjB;AAEA,CAJC,YAIY;AACX,UAAQ,EAAE,EAAE,OAAO;AACnB,SAAO;AACT;AAEA,CATC,YASY;AACX,UAAQ;AACR,SAAO;AACT;AAEA,CAAC;AACC,WAAS;AACT,iBAAe,IAAI,MAAM;AACzB,iBAAe;AACjB;AAEA,CAAC;AACC,WAAS,KAAK;AACd,cAAY;AACZ,UAAQ;AACR,UAAQ;AACR,aAAW;AACX,SAAO;AACP,iBAAe,IAAI,MAAM;AACzB,cAAY,IAAI,KAAK;AACrB,YAAU;AACZ;AAEA,CAZC,OAYO,CAAC;AACP,SAAO;AACP,uBAAqB;AACvB;AAEA,CAAC;AACC,cAAY;AACZ,SAAO;AACP,iBAAe;AACf,WAAS,QAAQ;AACjB,aAAW;AACX,eAAa;AACf;AAEA,CAAC;AACC,WAAS;AACT,OAAK;AACL,iBAAe;AACjB;AAEA,CAAC;AACC,WAAS,QAAQ;AACjB,cAAY;AACZ,UAAQ,IAAI,MAAM;AAClB,iBAAe;AACf,UAAQ;AACR,cAAY,IAAI,KAAK;AACvB;AAEA,CATC,YASY,CA7BJ;AA8BP,cAAY;AACZ,SAAO;AACP,gBAAc;AAChB;AAEA,CAAC;AACC,cAAY;AACZ,WAAS;AACT,iBAAe;AACf,cAAY,EAAE,IAAI,IAAI,KAAK,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE;AACpC,UAAQ,IAAI,MAAM;AACpB;AAEA,CAAC;AACC,iBAAe;AACjB;AAEA,CAJC,WAIW;AACV,WAAS;AACT,iBAAe;AACf,eAAa;AACb,SAAO;AACT;AAEA,CAXC,WAWW;AACZ,CAZC,WAYW;AACZ,CAbC,WAaW;AACV,SAAO;AACP,WAAS;AACT,UAAQ,IAAI,MAAM;AAClB,iBAAe;AACf,aAAW;AACX,cAAY,aAAa,KAAK;AAChC;AAEA,CAtBC,WAsBW,KAAK;AACjB,CAvBC,WAuBW,MAAM;AAClB,CAxBC,WAwBW,QAAQ;AAClB,WAAS;AACT,gBAAc;AACd,cAAY,EAAE,EAAE,EAAE,IAAI,KAAK,EAAE,EAAE,GAAG,EAAE,GAAG,EAAE;AAC3C;AAEA,CAAC;AACC,cAAY;AACZ,aAAW;AACX,SAAO;AACP,cAAY;AACd;AAEA,CAAC;AACC,SAAO;AACP,aAAW;AACX,cAAY;AACd;AAEA,CAAC;AACC,WAAS;AACT,OAAK;AACL,mBAAiB;AACjB,cAAY;AACd;AAEA,CAAC;AACC,WAAS,QAAQ;AACjB,UAAQ;AACR,iBAAe;AACf,UAAQ;AACR,aAAW;AACX,cAAY,IAAI,KAAK;AACvB;AAEA,CAAC;AACC,cAAY;AACZ,SAAO;AACT;AAEA,CALC,WAKW,MAAM,KAAK;AACrB,cAAY;AACd;AAEA,CATC,WASW;AACV,cAAY;AACZ,UAAQ;AACV;AAEA,CAAC;AACC,cAAY;AACZ,SAAO;AACT;AAEA,CALC,aAKa;AACZ,cAAY;AACd;AAEA,CAAC;AACC,iBAAe;AACjB;AAEA,CAJC,iBAIiB;AAChB,iBAAe;AACf,SAAO;AACT;AAEA,CAAC;AACC,cAAY;AACZ,WAAS;AACT,SAAO;AACP,cAAY;AACZ,iBAAe;AACf,iBAAe;AACjB;AAEA,CAAC;AACC,WAAS;AACT,yBAAuB,OAAO,SAAS,EAAE,OAAO,KAAK,EAAE;AACvD,OAAK;AACP;AAEA,CAAC;AACC,cAAY;AACZ,iBAAe;AACf,WAAS;AACT,cAAY,EAAE,IAAI,IAAI,KAAK,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE;AACpC,UAAQ,IAAI,MAAM;AACpB;AAEA,CAAC;AACC,WAAS;AACT,mBAAiB;AACjB,eAAa;AACb,iBAAe;AACjB;AAEA,CAPC,eAOe;AACd,UAAQ;AACR,SAAO;AACT;AAEA,CAAC;AACC,WAAS,QAAQ;AACjB,iBAAe;AACf,SAAO;AACP,aAAW;AACX,eAAa;AACf;AAEA,CAAC,gBAAgB;AACf,UAAQ,OAAO;AACf,SAAO;AACT;AAEA,CAAC;AACC,cAAY;AACZ,eAAa;AACb,cAAY,IAAI,MAAM;AACtB,cAAY;AACZ,WAAS;AACT,iBAAe;AACjB;AAEA,CAAC;AACC,WAAS;AACT,iBAAe;AACf,cAAY;AACd;AAEA,CAAC;AACC,cAAY;AACZ,SAAO;AACP,UAAQ,IAAI,MAAM;AACpB;AAEA,CAAC;AACC,cAAY;AACZ,SAAO;AACP,UAAQ,IAAI,MAAM;AACpB;", "names": []}
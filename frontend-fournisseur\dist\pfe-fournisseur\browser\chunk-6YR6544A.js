import {
  environment
} from "./chunk-GFHHPDQ6.js";
import {
  HttpClient,
  HttpParams
} from "./chunk-7JDDWGD3.js";
import {
  Injectable,
  setClassMetadata,
  ɵɵdefineInjectable,
  ɵɵinject
} from "./chunk-UBZQS7JS.js";

// src/app/services/avis-moderation.service.ts
var StatutAvis;
(function(StatutAvis2) {
  StatutAvis2[StatutAvis2["Publie"] = 1] = "Publie";
  StatutAvis2[StatutAvis2["CommentaireSupprime"] = 2] = "CommentaireSupprime";
  StatutAvis2[StatutAvis2["Signale"] = 3] = "Signale";
})(StatutAvis || (StatutAvis = {}));
var AvisModerationService = class _AvisModerationService {
  http;
  apiUrl = `${environment.apiUrl}/AvisModeration`;
  constructor(http) {
    this.http = http;
  }
  // Méthodes pour l'admin
  getAvisForModeration(filter = {}) {
    let params = new HttpParams();
    if (filter.statut !== void 0)
      params = params.set("statut", filter.statut.toString());
    if (filter.fournisseurId)
      params = params.set("fournisseurId", filter.fournisseurId.toString());
    if (filter.produitId)
      params = params.set("produitId", filter.produitId.toString());
    if (filter.dateDebut)
      params = params.set("dateDebut", filter.dateDebut.toISOString());
    if (filter.dateFin)
      params = params.set("dateFin", filter.dateFin.toISOString());
    if (filter.recherche)
      params = params.set("recherche", filter.recherche);
    if (filter.page)
      params = params.set("page", filter.page.toString());
    if (filter.pageSize)
      params = params.set("pageSize", filter.pageSize.toString());
    if (filter.sortBy)
      params = params.set("sortBy", filter.sortBy);
    if (filter.sortDesc !== void 0)
      params = params.set("sortDesc", filter.sortDesc.toString());
    return this.http.get(this.apiUrl, { params });
  }
  getAvisModeration(id) {
    return this.http.get(`${this.apiUrl}/${id}`);
  }
  modererAvis(id, dto) {
    return this.http.put(`${this.apiUrl}/${id}/moderer`, dto);
  }
  getAvisStats() {
    return this.http.get(`${this.apiUrl}/statistiques`);
  }
  // Méthodes pour les fournisseurs
  getAvisFournisseur(filter = {}) {
    let params = new HttpParams();
    if (filter.statut !== void 0)
      params = params.set("statut", filter.statut.toString());
    if (filter.produitId)
      params = params.set("produitId", filter.produitId.toString());
    if (filter.dateDebut)
      params = params.set("dateDebut", filter.dateDebut.toISOString());
    if (filter.dateFin)
      params = params.set("dateFin", filter.dateFin.toISOString());
    if (filter.recherche)
      params = params.set("recherche", filter.recherche);
    if (filter.page)
      params = params.set("page", filter.page.toString());
    if (filter.pageSize)
      params = params.set("pageSize", filter.pageSize.toString());
    if (filter.sortBy)
      params = params.set("sortBy", filter.sortBy);
    if (filter.sortDesc !== void 0)
      params = params.set("sortDesc", filter.sortDesc.toString());
    return this.http.get(`${this.apiUrl}/fournisseur`, { params });
  }
  repondreAvis(id, reponse) {
    return this.http.put(`${this.apiUrl}/${id}/repondre`, JSON.stringify(reponse), {
      headers: { "Content-Type": "application/json" }
    });
  }
  getAvisStatsFournisseur() {
    return this.http.get(`${this.apiUrl}/statistiques/fournisseur`);
  }
  // Méthode pour signaler un avis (fournisseur)
  signalerAvis(id, raisonSignalement, detailsSignalement) {
    const signalementData = {
      raisonSignalement,
      detailsSignalement: detailsSignalement || ""
    };
    console.log("\u{1F6A8} Service - Envoi du signalement:", signalementData);
    return this.http.put(`${this.apiUrl}/${id}/signaler`, signalementData);
  }
  // Nouvelles méthodes pour la suppression et restauration de commentaires
  supprimerCommentaire(id, raisonSuppression) {
    return this.http.put(`${this.apiUrl}/${id}/supprimer-commentaire`, {
      raisonSuppression
    });
  }
  restaurerCommentaire(id) {
    return this.http.put(`${this.apiUrl}/${id}/restaurer-commentaire`, {});
  }
  // Méthode pour supprimer définitivement un avis (pour les données de test)
  supprimerAvis(id) {
    console.log(`\u{1F310} Appel DELETE vers: ${this.apiUrl}/${id}`);
    return this.http.delete(`${this.apiUrl}/${id}`);
  }
  // Méthode pour tester la connectivité avec le backend
  testerConnectivite() {
    return this.http.get(`${this.apiUrl}/test`);
  }
  // Méthodes utilitaires
  getStatutLibelle(statut) {
    switch (statut) {
      case StatutAvis.Publie:
        return "Publi\xE9";
      case StatutAvis.CommentaireSupprime:
        return "Commentaire supprim\xE9";
      case StatutAvis.Signale:
        return "Signal\xE9";
      default:
        return "Non d\xE9fini";
    }
  }
  getStatutColor(statut) {
    switch (statut) {
      case StatutAvis.Publie:
        return "success";
      case StatutAvis.CommentaireSupprime:
        return "warning";
      case StatutAvis.Signale:
        return "danger";
      default:
        return "secondary";
    }
  }
  getStatutIcon(statut) {
    switch (statut) {
      case StatutAvis.Publie:
        return "check-circle";
      case StatutAvis.CommentaireSupprime:
        return "exclamation-triangle";
      case StatutAvis.Signale:
        return "flag";
      default:
        return "question-circle";
    }
  }
  static \u0275fac = function AvisModerationService_Factory(__ngFactoryType__) {
    return new (__ngFactoryType__ || _AvisModerationService)(\u0275\u0275inject(HttpClient));
  };
  static \u0275prov = /* @__PURE__ */ \u0275\u0275defineInjectable({ token: _AvisModerationService, factory: _AvisModerationService.\u0275fac, providedIn: "root" });
};
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && setClassMetadata(AvisModerationService, [{
    type: Injectable,
    args: [{
      providedIn: "root"
    }]
  }], () => [{ type: HttpClient }], null);
})();

export {
  StatutAvis,
  AvisModerationService
};
//# sourceMappingURL=chunk-6YR6544A.js.map

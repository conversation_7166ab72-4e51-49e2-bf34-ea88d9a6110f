import { Component, OnInit, OnDestroy } from '@angular/core';
import { CommonModule } from '@angular/common';
import { Router } from '@angular/router';
import { Subscription, interval, forkJoin } from 'rxjs';
import { NotificationService, NotificationDto } from '../../services/notification.service';
import { AuthService } from '../../services/auth.service';
import { DemandeService, StatutDemande } from '../../services/demande.service';
import { AdminAuthService } from '../../services/admin-auth.service';
import { AdminNotificationService, AdminNotification } from '../../services/admin-notification.service';


@Component({
  selector: 'app-notification-icon',
  standalone: true,
  imports: [CommonModule],
  template: `
    <div class="notification-wrapper">
      <button
        type="button"
        class="notification-btn"
        [title]="'Notifications'"
        (click)="toggleMenu($event)"
        (mousedown)="onButtonMouseDown($event)">
        <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
          <path d="M18 8A6 6 0 0 0 6 8c0 7-3 9-3 9h18s-3-2-3-9"></path>
          <path d="M13.73 21a2 2 0 0 1-3.46 0"></path>
        </svg>
        <span *ngIf="totalUnreadCount > 0" class="notification-badge">{{ totalUnreadCount }}</span>
      </button>

      <div *ngIf="showMenu" class="notification-menu" (click)="$event.stopPropagation()">
        <!-- Flèche pointant vers le bouton -->
        <div class="notification-arrow"></div>
        <div class="notification-header">
          <h4>Notifications</h4>
          <button
            *ngIf="unreadCount > 0"
            (click)="markAllAsRead()"
            class="mark-all-read-btn">
            Tout marquer comme lu
          </button>
        </div>

        <div class="notification-divider"></div>

        <!-- Section notifications admin détaillées -->
        <div *ngIf="isAdmin() && adminNotifications.length > 0" class="admin-notifications-section">
          <div class="section-header">
            <span>🔔 Notifications Admin</span>
            <span class="admin-count">{{ adminUnreadCount }}</span>
          </div>

          <div
            *ngFor="let adminNotif of adminNotifications.slice(0, 3)"
            class="admin-notification-item"
            [class.unread]="!adminNotif.estLue"
            [class.high-priority]="adminNotif.priority === 'HIGH'"
            (click)="handleAdminNotificationClick(adminNotif)">

            <div class="admin-notification-icon">
              <span *ngIf="adminNotif.type === 'DEMANDE_CATEGORIE'">📁</span>
              <span *ngIf="adminNotif.type === 'DEMANDE_SOUS_CATEGORIE'">📂</span>
              <span *ngIf="adminNotif.type === 'NOUVEAU_PRODUIT'">📦</span>
              <span *ngIf="adminNotif.type === 'COMMANDE'">🛒</span>
              <span *ngIf="adminNotif.type === 'AUTRE'">🔔</span>
            </div>

            <div class="admin-notification-content">
              <div class="admin-notification-title">{{ adminNotif.titre }}</div>
              <div class="admin-notification-message">{{ adminNotif.message }}</div>
              <div class="admin-notification-meta">
                <span class="admin-notification-date">{{ formatDate(adminNotif.dateCreation) }}</span>
                <span *ngIf="adminNotif.priority === 'HIGH'" class="priority-badge high">Urgent</span>
                <span *ngIf="adminNotif.priority === 'MEDIUM'" class="priority-badge medium">Moyen</span>
              </div>
            </div>

            <button
              class="admin-delete-btn"
              (click)="deleteAdminNotification(adminNotif.id, $event)">
              <svg width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                <line x1="18" y1="6" x2="6" y2="18"></line>
                <line x1="6" y1="6" x2="18" y2="18"></line>
              </svg>
            </button>
          </div>

          <div *ngIf="adminNotifications.length > 3" class="view-all-admin">
            <button (click)="viewAllAdminNotifications()">
              Voir toutes les notifications admin ({{ adminNotifications.length }})
            </button>
          </div>
        </div>

        <!-- Section demandes pour les admins (version simplifiée) -->
        <div *ngIf="isAdmin() && demandesEnAttente > 0 && adminNotifications.length === 0" class="demandes-section">
          <div class="section-header">
            <span>🔔 Demandes en attente</span>
            <span class="demandes-count">{{ demandesEnAttente }}</span>
          </div>
          <div class="demande-item" (click)="viewDemandes()">
            <div class="demande-content">
              <div class="demande-title">Nouvelles demandes de catégories</div>
              <div class="demande-message">{{ demandesEnAttente }} demande(s) en attente de traitement</div>
            </div>
          </div>
        </div>

        <div *ngIf="isAdmin() && (adminNotifications.length > 0 || demandesEnAttente > 0)" class="notification-divider"></div>

        <div class="notification-list">
          <!-- Message quand aucune notification pour utilisateurs normaux -->
          <div *ngIf="!isAdmin() && notifications.length === 0" class="no-notifications">
            <svg class="empty-icon" width="48" height="48" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
              <path d="M20 21v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2"></path>
              <circle cx="12" cy="7" r="4"></circle>
            </svg>
            <p>Aucune notification</p>
            <span class="no-notifications-subtext">Vous êtes à jour !</span>
          </div>

          <!-- Message quand aucune notification pour admins -->
          <div *ngIf="isAdmin() && notifications.length === 0 && adminNotifications.length === 0 && demandesEnAttente === 0" class="no-notifications">
            <svg class="empty-icon" width="48" height="48" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
              <path d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
            </svg>
            <p>Aucune notification</p>
            <span class="no-notifications-subtext">Tout est sous contrôle !</span>
          </div>

          <div
            *ngFor="let notification of notifications.slice(0, 5)"
            class="notification-item"
            [class.unread]="!notification.estLue"
            (click)="markAsRead(notification)">

            <div class="notification-content">
              <p class="notification-text">{{ notification.contenu }}</p>
              <span class="notification-date">{{ formatDate(notification.dateEnvoi) }}</span>
            </div>

            <button
              class="delete-btn"
              (click)="deleteNotification(notification.id, $event)">
              <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                <line x1="18" y1="6" x2="6" y2="18"></line>
                <line x1="6" y1="6" x2="18" y2="18"></line>
              </svg>
            </button>
          </div>

          <div *ngIf="notifications.length > 5" class="view-all">
            <button (click)="viewAllNotifications()">
              Voir toutes les notifications
            </button>
          </div>
        </div>
      </div>
    </div>
  `,
  styles: [`
    .notification-wrapper {
      position: relative;
      display: inline-block;
      z-index: 999;
    }

    .notification-btn {
      position: relative;
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      border: none;
      cursor: pointer;
      padding: 12px;
      border-radius: 50%;
      transition: all 0.3s ease;
      color: white;
      z-index: 1001;
      outline: none;
      font-size: 18px;
      width: 48px;
      height: 48px;
      display: flex;
      align-items: center;
      justify-content: center;
      box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
    }

    .notification-btn:hover {
      background: linear-gradient(135deg, #5a67d8 0%, #6b46c1 100%);
      transform: translateY(-2px);
      box-shadow: 0 6px 20px rgba(102, 126, 234, 0.4);
    }

    .notification-btn:focus {
      outline: none;
      box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.3);
    }

    .notification-btn:active {
      transform: translateY(0) scale(0.95);
      box-shadow: 0 2px 8px rgba(102, 126, 234, 0.3);
    }

    .notification-badge {
      position: absolute;
      top: -2px;
      right: -2px;
      background: linear-gradient(135deg, #ff4757 0%, #ff3742 100%);
      color: white;
      border: 2px solid white;
      border-radius: 50%;
      min-width: 22px;
      height: 22px;
      font-size: 11px;
      display: flex;
      align-items: center;
      justify-content: center;
      font-weight: bold;
      box-shadow: 0 2px 8px rgba(255, 71, 87, 0.4);
      animation: pulse 2s infinite;
    }

    @keyframes pulse {
      0% {
        box-shadow: 0 2px 8px rgba(255, 71, 87, 0.4);
      }
      50% {
        box-shadow: 0 2px 8px rgba(255, 71, 87, 0.6), 0 0 0 4px rgba(255, 71, 87, 0.2);
      }
      100% {
        box-shadow: 0 2px 8px rgba(255, 71, 87, 0.4);
      }
    }

    .notification-menu {
      position: absolute;
      top: calc(100% + 8px);
      right: 0;
      width: 380px;
      max-width: 90vw;
      max-height: 500px;
      background: white;
      border: 1px solid #e5e7eb;
      border-radius: 12px;
      box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
      z-index: 1000;
      overflow: hidden;
      transform-origin: top right;
      animation: slideDown 0.2s ease-out;
    }

    @keyframes slideDown {
      from {
        opacity: 0;
        transform: translateY(-10px) scale(0.95);
      }
      to {
        opacity: 1;
        transform: translateY(0) scale(1);
      }
    }

    /* Responsive pour mobile */
    @media (max-width: 768px) {
      .notification-menu {
        position: fixed;
        top: 60px;
        left: 10px;
        right: 10px;
        width: auto;
        max-width: none;
        transform-origin: top center;
      }
    }

    /* Ajustement pour les petits écrans */
    @media (max-width: 480px) {
      .notification-menu {
        top: 50px;
        left: 5px;
        right: 5px;
        max-height: 70vh;
      }
    }

    /* Flèche pointant vers le bouton */
    .notification-arrow {
      position: absolute;
      top: -8px;
      right: 20px;
      width: 0;
      height: 0;
      border-left: 8px solid transparent;
      border-right: 8px solid transparent;
      border-bottom: 8px solid white;
      z-index: 1001;
    }

    .notification-arrow::before {
      content: '';
      position: absolute;
      top: 1px;
      left: -8px;
      width: 0;
      height: 0;
      border-left: 8px solid transparent;
      border-right: 8px solid transparent;
      border-bottom: 8px solid #e5e7eb;
    }

    /* Masquer la flèche sur mobile */
    @media (max-width: 768px) {
      .notification-arrow {
        display: none;
      }
    }

    .notification-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 16px;
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      color: white;
    }

    .notification-header h4 {
      margin: 0;
      font-size: 16px;
      font-weight: 500;
    }

    .mark-all-read-btn {
      background: none;
      border: none;
      color: white;
      font-size: 12px;
      cursor: pointer;
      padding: 4px 8px;
      border-radius: 4px;
      transition: background-color 0.2s;
    }

    .mark-all-read-btn:hover {
      background-color: rgba(255, 255, 255, 0.2);
    }

    .notification-divider {
      height: 1px;
      background-color: #e0e0e0;
    }

    .notification-list {
      max-height: 300px;
      overflow-y: auto;
    }

    .no-notifications {
      text-align: center;
      padding: 32px 16px;
      color: #666;
    }

    .empty-icon {
      margin-bottom: 16px;
      color: #ccc;
    }

    .no-notifications p {
      margin: 0;
      font-size: 16px;
      font-weight: 500;
    }

    .no-notifications-subtext {
      display: block;
      margin-top: 8px;
      font-size: 14px;
      color: #cbd5e1;
      font-style: italic;
    }

    .notification-item {
      display: flex;
      align-items: flex-start;
      padding: 12px 16px;
      border-bottom: 1px solid #e0e0e0;
      cursor: pointer;
      transition: background-color 0.2s;
    }

    .notification-item:hover {
      background-color: #f5f5f5;
    }

    .notification-item.unread {
      background-color: #e3f2fd;
      border-left: 3px solid #2196f3;
    }

    .notification-content {
      flex: 1;
      margin-right: 8px;
    }

    .notification-text {
      margin: 0 0 4px 0;
      font-size: 14px;
      line-height: 1.4;
    }

    .notification-date {
      font-size: 12px;
      color: #666;
    }

    .delete-btn {
      background: none;
      border: none;
      cursor: pointer;
      opacity: 0;
      transition: opacity 0.2s;
      width: 32px;
      height: 32px;
      border-radius: 4px;
      display: flex;
      align-items: center;
      justify-content: center;
      color: #666;
    }

    .delete-btn:hover {
      background-color: rgba(244, 67, 54, 0.1);
      color: #f44336;
    }

    .notification-item:hover .delete-btn {
      opacity: 1;
    }

    .view-all {
      text-align: center;
      padding: 16px;
      border-top: 1px solid #e0e0e0;
    }

    .view-all button {
      background: none;
      border: none;
      color: #2196f3;
      cursor: pointer;
      font-size: 14px;
      padding: 8px 16px;
      border-radius: 4px;
      transition: background-color 0.2s;
    }

    .view-all button:hover {
      background-color: rgba(33, 150, 243, 0.1);
    }

    .demandes-section {
      padding: 12px 16px;
      background: #f8f9ff;
      border-left: 4px solid #3b82f6;
    }

    .section-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 8px;
      font-weight: 500;
      color: #1e293b;
      font-size: 14px;
    }

    .demandes-count {
      background: #3b82f6;
      color: white;
      border-radius: 12px;
      padding: 2px 8px;
      font-size: 12px;
      font-weight: bold;
    }

    .demande-item {
      cursor: pointer;
      padding: 8px 0;
      transition: opacity 0.2s;
    }

    .demande-item:hover {
      opacity: 0.8;
    }

    .demande-content {
      display: flex;
      flex-direction: column;
      gap: 4px;
    }

    .demande-title {
      font-weight: 500;
      color: #1e293b;
      font-size: 14px;
    }

    .demande-message {
      color: #64748b;
      font-size: 12px;
    }

    /* Styles pour les notifications admin */
    .admin-notifications-section {
      padding: 12px 16px;
      background: #f0f9ff;
      border-left: 4px solid #3b82f6;
      margin-bottom: 8px;
    }

    .admin-count {
      background: #3b82f6;
      color: white;
      border-radius: 12px;
      padding: 2px 8px;
      font-size: 12px;
      font-weight: bold;
    }

    .admin-notification-item {
      display: flex;
      align-items: flex-start;
      gap: 12px;
      padding: 12px;
      margin: 8px 0;
      background: white;
      border-radius: 8px;
      cursor: pointer;
      transition: all 0.2s ease;
      border-left: 3px solid transparent;
    }

    .admin-notification-item:hover {
      background: #f8fafc;
      transform: translateX(2px);
    }

    .admin-notification-item.unread {
      border-left-color: #3b82f6;
      background: #f0f9ff;
    }

    .admin-notification-item.high-priority {
      border-left-color: #ef4444;
      background: #fef2f2;
    }

    .admin-notification-icon {
      font-size: 18px;
      min-width: 24px;
      text-align: center;
    }

    .admin-notification-content {
      flex: 1;
      min-width: 0;
    }

    .admin-notification-title {
      font-weight: 600;
      color: #1e293b;
      margin-bottom: 4px;
      font-size: 14px;
    }

    .admin-notification-message {
      color: #64748b;
      font-size: 12px;
      line-height: 1.4;
      margin-bottom: 8px;
    }

    .admin-notification-meta {
      display: flex;
      align-items: center;
      gap: 8px;
    }

    .admin-notification-date {
      color: #94a3b8;
      font-size: 11px;
    }

    .priority-badge {
      padding: 2px 6px;
      border-radius: 10px;
      font-size: 10px;
      font-weight: 600;
      text-transform: uppercase;
    }

    .priority-badge.high {
      background: #fee2e2;
      color: #dc2626;
    }

    .priority-badge.medium {
      background: #fef3c7;
      color: #d97706;
    }

    .admin-delete-btn {
      background: none;
      border: none;
      cursor: pointer;
      padding: 4px;
      border-radius: 4px;
      color: #94a3b8;
      transition: all 0.2s ease;
    }

    .admin-delete-btn:hover {
      background: #fee2e2;
      color: #dc2626;
    }

    .view-all-admin {
      text-align: center;
      margin-top: 12px;
      padding-top: 12px;
      border-top: 1px solid rgba(59, 130, 246, 0.2);
    }

    .view-all-admin button {
      background: none;
      border: 1px solid #3b82f6;
      color: #3b82f6;
      padding: 8px 16px;
      border-radius: 6px;
      cursor: pointer;
      font-size: 12px;
      transition: all 0.2s ease;
    }

    .view-all-admin button:hover {
      background: #3b82f6;
      color: white;
    }
  `]
})
export class NotificationIconComponent implements OnInit, OnDestroy {
  notifications: NotificationDto[] = [];
  adminNotifications: AdminNotification[] = [];
  unreadCount = 0;
  showMenu = false;
  demandesEnAttente = 0;
  private subscriptions = new Subscription();

  get adminUnreadCount(): number {
    const count = this.adminNotifications.filter(n => !n.estLue).length;
    console.log('📊 adminUnreadCount:', count, 'sur', this.adminNotifications.length, 'notifications admin');
    return count;
  }

  constructor(
    private notificationService: NotificationService,
    private authService: AuthService,
    private router: Router,
    private demandeService: DemandeService,
    private adminAuthService: AdminAuthService,
    private adminNotificationService: AdminNotificationService
  ) {}

  ngOnInit() {
    this.subscriptions.add(
      this.notificationService.notifications$.subscribe(notifications => {
        this.notifications = notifications;
      })
    );

    this.subscriptions.add(
      this.notificationService.unreadCount$.subscribe(count => {
        this.unreadCount = count;
      })
    );

    // Pour les admins, charger les notifications spécifiques
    if (this.isAdmin()) {
      this.subscriptions.add(
        this.adminNotificationService.notifications$.subscribe(adminNotifications => {
          this.adminNotifications = adminNotifications;
        })
      );

      // Ne pas s'abonner directement au unreadCount admin ici
      // car cela cause une duplication dans le calcul totalUnreadCount

      this.loadAdminNotifications();
    }

    this.loadNotifications();
    this.loadDemandesEnAttente();

    // Actualiser les demandes toutes les 30 secondes pour les admins
    if (this.isAdmin()) {
      this.subscriptions.add(
        interval(30000).subscribe(() => {
          this.loadDemandesEnAttente();
          this.loadAdminNotifications();
        })
      );
    }
  }

  ngOnDestroy() {
    this.subscriptions.unsubscribe();
  }

  toggleMenu(event?: Event) {
    console.log('🔄 toggleMenu appelé, showMenu avant:', this.showMenu);

    // Empêcher la propagation de l'événement
    if (event) {
      event.stopPropagation();
      event.preventDefault();
    }

    this.showMenu = !this.showMenu;
    console.log('🔄 showMenu après:', this.showMenu);

    if (this.showMenu) {
      console.log('📂 Menu ouvert, chargement des notifications...');
      this.loadNotifications();

      // Charger les notifications admin si c'est un admin
      if (this.isAdmin()) {
        console.log('👑 Utilisateur admin détecté, chargement des notifications admin...');
        this.loadAdminNotifications();
      }

      // Fermer le menu si on clique ailleurs
      setTimeout(() => {
        document.addEventListener('click', this.closeMenu.bind(this));
      }, 100);
    } else {
      console.log('📂 Menu fermé');
      document.removeEventListener('click', this.closeMenu.bind(this));
    }
  }

  closeMenu(event?: Event) {
    console.log('🔄 closeMenu appelé');
    this.showMenu = false;
    document.removeEventListener('click', this.closeMenu.bind(this));
  }

  onButtonMouseDown(event: Event) {
    console.log('🖱️ Bouton notification mousedown détecté !');
    event.stopPropagation();
  }

  convertDemandestoNotifications(demandesCategories: any[], demandesSousCategories: any[]): AdminNotification[] {
    const notifications: AdminNotification[] = [];
    console.log('🔄 Conversion des demandes en notifications...');

    // Convertir les demandes de catégories
    console.log('📁 Traitement des demandes de catégories:', demandesCategories.length);
    demandesCategories.forEach((demande, index) => {
      console.log(`🔍 Demande catégorie ${index + 1}:`, {
        id: demande.id,
        nom: demande.nom,
        statut: demande.statut,
        fournisseurId: demande.fournisseurId,
        fournisseurRaisonSociale: demande.fournisseurRaisonSociale,
        dateCreation: demande.dateCreation,
        dateDemande: demande.dateDemande
      });

      if (demande.statut === 'EN_ATTENTE' || demande.statut === 0) { // 0 = EN_ATTENTE
        const fournisseurNom = demande.fournisseurRaisonSociale ||
                              demande.fournisseur?.raisonSociale ||
                              demande.nomFournisseur ||
                              `Fournisseur #${demande.fournisseurId}`;

        notifications.push({
          id: parseInt(`1${demande.id}`), // Préfixe 1 pour catégories
          type: 'DEMANDE_CATEGORIE',
          titre: 'Nouvelle demande de catégorie',
          message: `Le fournisseur ${fournisseurNom} souhaite créer une nouvelle catégorie nommée "${demande.nom}"`,
          fournisseurNom: fournisseurNom,
          fournisseurId: demande.fournisseurId,
          referenceId: demande.id,
          dateCreation: new Date(demande.dateCreation || demande.dateDemande || Date.now()),
          estLue: false,
          priority: 'HIGH',
          actionUrl: '/admin/dashboard/demandes'
        });
        console.log(`✅ Notification créée pour catégorie: ${demande.nom}`);
      } else {
        console.log(`⏭️ Demande catégorie ignorée (statut: ${demande.statut})`);
      }
    });

    // Convertir les demandes de sous-catégories
    console.log('📂 Traitement des demandes de sous-catégories:', demandesSousCategories.length);
    demandesSousCategories.forEach((demande, index) => {
      console.log(`🔍 Demande sous-catégorie ${index + 1}:`, {
        id: demande.id,
        nom: demande.nom,
        statut: demande.statut,
        fournisseurId: demande.fournisseurId,
        fournisseurRaisonSociale: demande.fournisseurRaisonSociale
      });

      if (demande.statut === 'EN_ATTENTE' || demande.statut === 0) { // 0 = EN_ATTENTE
        const fournisseurNom = demande.fournisseurRaisonSociale ||
                              demande.fournisseur?.raisonSociale ||
                              demande.nomFournisseur ||
                              `Fournisseur #${demande.fournisseurId}`;

        notifications.push({
          id: parseInt(`2${demande.id}`), // Préfixe 2 pour sous-catégories
          type: 'DEMANDE_SOUS_CATEGORIE',
          titre: 'Nouvelle demande de sous-catégorie',
          message: `Le fournisseur ${fournisseurNom} souhaite créer une nouvelle sous-catégorie nommée "${demande.nom}"`,
          fournisseurNom: fournisseurNom,
          fournisseurId: demande.fournisseurId,
          referenceId: demande.id,
          dateCreation: new Date(demande.dateCreation || demande.dateDemande || Date.now()),
          estLue: false,
          priority: 'HIGH',
          actionUrl: '/admin/dashboard/demandes'
        });
        console.log(`✅ Notification créée pour sous-catégorie: ${demande.nom}`);
      } else {
        console.log(`⏭️ Demande sous-catégorie ignorée (statut: ${demande.statut})`);
      }
    });

    // Trier par date (plus récent en premier)
    notifications.sort((a, b) => new Date(b.dateCreation).getTime() - new Date(a.dateCreation).getTime());

    console.log(`📊 Total notifications créées: ${notifications.length}`);
    return notifications;
  }

  loadNotifications() {
    console.log('📥 loadNotifications appelé');
    const currentUser = this.authService.getCurrentUser();
    console.log('👤 Utilisateur actuel:', currentUser);

    if (currentUser?.id) {
      console.log('🔄 Chargement des notifications pour utilisateur ID:', currentUser.id);
      this.notificationService.getUserNotifications(currentUser.id).subscribe({
        next: (notifications) => {
          console.log('✅ Notifications reçues:', notifications);
        },
        error: (error) => {
          console.error('❌ Erreur lors du chargement des notifications:', error);
        }
      });
    } else {
      console.log('❌ Aucun utilisateur connecté');
    }
  }

  loadAdminNotifications() {
    console.log('📥 loadAdminNotifications appelé');
    if (this.isAdmin()) {
      console.log('👑 Chargement des notifications admin depuis la base de données...');

      // Charger les demandes de catégories et sous-catégories
      forkJoin({
        demandesCategories: this.demandeService.getMesDemandesCategories(),
        demandesSousCategories: this.demandeService.getMesDemandesSousCategories()
      }).subscribe({
        next: (result) => {
          console.log('✅ Demandes récupérées:', result);
          console.log('📊 Nombre de demandes catégories:', result.demandesCategories?.length || 0);
          console.log('📊 Nombre de demandes sous-catégories:', result.demandesSousCategories?.length || 0);

          // Afficher un échantillon des données pour debug
          if (result.demandesCategories?.length > 0) {
            console.log('🔍 Exemple demande catégorie:', result.demandesCategories[0]);
          }
          if (result.demandesSousCategories?.length > 0) {
            console.log('🔍 Exemple demande sous-catégorie:', result.demandesSousCategories[0]);
          }

          // Convertir les demandes en notifications admin
          this.adminNotifications = this.convertDemandestoNotifications(
            result.demandesCategories || [],
            result.demandesSousCategories || []
          );

          console.log('✅ Notifications admin créées:', this.adminNotifications);
          console.log('📊 Nombre total de notifications admin:', this.adminNotifications.length);
        },
        error: (error) => {
          console.error('❌ Erreur lors du chargement des demandes:', error);
          // En cas d'erreur, utiliser les notifications mock
          this.adminNotifications = this.adminNotificationService.getMockNotifications();
        }
      });
    }
  }

  markAsRead(notification: NotificationDto) {
    if (!notification.estLue) {
      this.notificationService.markAsRead(notification.id).subscribe();
    }
  }

  markAllAsRead() {
    const unreadNotifications = this.notifications.filter(n => !n.estLue);
    unreadNotifications.forEach(notification => {
      this.notificationService.markAsRead(notification.id).subscribe();
    });
  }

  deleteNotification(notificationId: number, event: Event) {
    event.stopPropagation();
    this.notificationService.deleteNotification(notificationId).subscribe();
  }

  viewAllNotifications() {
    this.router.navigate(['/notifications']);
  }

  viewDemandes() {
    this.router.navigate(['/admin/dashboard/demandes']);
    this.closeMenu();
  }

  isAdmin(): boolean {
    // Vérifier si l'utilisateur est admin via le service principal
    const isAdminViaAuth = this.authService.isAdmin();
    // Vérifier si l'utilisateur est admin via le service admin
    const isAdminViaAdminAuth = this.adminAuthService.getCurrentUser() !== null;

    console.log('🔍 Vérification admin - AuthService:', isAdminViaAuth);
    console.log('🔍 Vérification admin - AdminAuthService:', isAdminViaAdminAuth);

    const result = isAdminViaAuth || isAdminViaAdminAuth;
    console.log('🔍 Résultat final isAdmin:', result);

    return result;
  }

  handleAdminNotificationClick(notification: AdminNotification) {
    console.log('🔔 Clic sur notification admin:', notification);

    // Marquer comme lue localement
    notification.estLue = true;

    // Rediriger vers l'action appropriée
    if (notification.actionUrl) {
      this.router.navigate([notification.actionUrl]);
    }

    this.closeMenu();
  }

  deleteAdminNotification(notificationId: number, event: Event) {
    event.stopPropagation();
    console.log('🗑️ Suppression notification admin:', notificationId);

    // Supprimer de la liste locale immédiatement
    this.adminNotifications = this.adminNotifications.filter(n => n.id !== notificationId);
    console.log('✅ Notification supprimée localement');
  }

  viewAllAdminNotifications() {
    this.router.navigate(['/admin/notifications']);
    this.closeMenu();
  }

  loadDemandesEnAttente() {
    console.log('🔍 loadDemandesEnAttente appelé');
    console.log('🔍 isAdmin():', this.isAdmin());
    console.log('🔍 authService.isAuthenticated():', this.authService.isAuthenticated());
    console.log('🔍 authService.getToken():', !!this.authService.getToken());
    console.log('🔍 adminAuthService.getCurrentUser():', !!this.adminAuthService.getCurrentUser());

    if (this.isAdmin()) {
      console.log('✅ Utilisateur admin détecté, chargement des demandes...');
      forkJoin({
        categories: this.demandeService.getDemandesCategoriesByStatut(StatutDemande.EnAttente),
        sousCategories: this.demandeService.getDemandesSousCategoriesByStatut(StatutDemande.EnAttente)
      }).subscribe({
        next: (result) => {
          console.log('✅ Demandes chargées:', result);
          this.demandesEnAttente = result.categories.length + result.sousCategories.length;
        },
        error: (error) => {
          console.error('❌ Erreur lors du chargement des demandes en attente:', error);
        }
      });
    } else {
      console.log('❌ Utilisateur non admin, pas de chargement des demandes');
    }
  }

  get totalUnreadCount(): number {
    const adminUnreadCount = this.isAdmin() ? this.adminUnreadCount : 0;
    const normalUnreadCount = this.unreadCount || 0;

    // Pour les admins, utiliser seulement les notifications admin (pas de duplication)
    // Pour les fournisseurs, utiliser les notifications normales
    const total = this.isAdmin() ? adminUnreadCount : normalUnreadCount;

    console.log('🔢 Calcul badge:', {
      isAdmin: this.isAdmin(),
      normalUnreadCount,
      adminUnreadCount,
      total
    });

    return Math.max(0, total); // S'assurer que le total n'est jamais négatif
  }

  formatDate(date: Date): string {
    const now = new Date();
    const notifDate = new Date(date);
    const diffInMinutes = Math.floor((now.getTime() - notifDate.getTime()) / (1000 * 60));

    if (diffInMinutes < 1) return 'À l\'instant';
    if (diffInMinutes < 60) return `Il y a ${diffInMinutes} min`;

    const diffInHours = Math.floor(diffInMinutes / 60);
    if (diffInHours < 24) return `Il y a ${diffInHours}h`;

    const diffInDays = Math.floor(diffInHours / 24);
    if (diffInDays < 7) return `Il y a ${diffInDays}j`;

    return notifDate.toLocaleDateString('fr-FR');
  }
}

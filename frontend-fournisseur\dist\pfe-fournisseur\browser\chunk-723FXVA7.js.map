{"version": 3, "sources": ["src/app/guards/admin-auth.guard.ts", "src/app/guards/super-admin.guard.ts", "src/app/guards/role.guard.ts", "src/app/routes/admin.routes.ts"], "sourcesContent": ["import { Injectable } from '@angular/core';\nimport { CanActivate, CanActivateChild, Router, ActivatedRouteSnapshot, RouterStateSnapshot } from '@angular/router';\nimport { Observable, map, take } from 'rxjs';\nimport { AdminAuthService } from '../services/admin-auth.service';\n\n@Injectable({\n  providedIn: 'root'\n})\nexport class AdminAuthGuard implements CanActivate, CanActivateChild {\n\n  constructor(\n    private adminAuthService: AdminAuthService,\n    private router: Router\n  ) {}\n\n  canActivate(\n    route: ActivatedRouteSnapshot,\n    state: RouterStateSnapshot\n  ): Observable<boolean> {\n    return this.checkAuth(state.url);\n  }\n\n  canActivateChild(\n    childRoute: ActivatedRouteSnapshot,\n    state: RouterStateSnapshot\n  ): Observable<boolean> {\n    return this.checkAuth(state.url);\n  }\n\n  private checkAuth(url: string): Observable<boolean> {\n    return this.adminAuthService.isAuthenticated$.pipe(\n      take(1),\n      map(isAuthenticated => {\n        if (isAuthenticated) {\n          return true;\n        } else {\n          // Stocker l'URL demandée pour redirection après connexion\n          localStorage.setItem('admin_redirect_url', url);\n          this.router.navigate(['/adminOptiLet']);\n          return false;\n        }\n      })\n    );\n  }\n}\n", "import { Injectable } from '@angular/core';\nimport { CanActivate, Router, ActivatedRouteSnapshot, RouterStateSnapshot } from '@angular/router';\nimport { Observable, map, take } from 'rxjs';\nimport { AdminAuthService } from '../services/admin-auth.service';\nimport { AdminRole } from '../models/admin.model';\n\n@Injectable({\n  providedIn: 'root'\n})\nexport class SuperAdminGuard implements CanActivate {\n\n  constructor(\n    private adminAuthService: AdminAuthService,\n    private router: Router\n  ) {}\n\n  canActivate(\n    route: ActivatedRouteSnapshot,\n    state: RouterStateSnapshot\n  ): Observable<boolean> {\n    return this.adminAuthService.currentUser$.pipe(\n      take(1),\n      map(user => {\n        if (user && user.role === AdminRole.SUPER_ADMIN) {\n          return true;\n        } else {\n          // Rediriger vers une page d'accès refusé ou dashboard admin\n          this.router.navigate(['/admin/dashboard'], {\n            queryParams: { error: 'access_denied', message: 'Accès réservé aux Super Administrateurs' }\n          });\n          return false;\n        }\n      })\n    );\n  }\n}\n", "import { Injectable } from '@angular/core';\nimport { CanActivate, Router, ActivatedRouteSnapshot, RouterStateSnapshot } from '@angular/router';\nimport { Observable, map, take } from 'rxjs';\nimport { AdminAuthService } from '../services/admin-auth.service';\nimport { AdminRole, PermissionAction } from '../models/admin.model';\n\n@Injectable({\n  providedIn: 'root'\n})\nexport class RoleGuard implements CanActivate {\n\n  constructor(\n    private adminAuthService: AdminAuthService,\n    private router: Router\n  ) {}\n\n  canActivate(\n    route: ActivatedRouteSnapshot,\n    state: RouterStateSnapshot\n  ): Observable<boolean> {\n    return this.adminAuthService.currentUser$.pipe(\n      take(1),\n      map(user => {\n        if (!user) {\n          this.router.navigate(['/admin/login']);\n          return false;\n        }\n\n        // Vérifier les rôles requis\n        const requiredRoles = route.data['roles'] as AdminRole[];\n        if (requiredRoles && requiredRoles.length > 0) {\n          const hasRole = requiredRoles.includes(user.role);\n          if (!hasRole) {\n            this.handleAccessDenied('Rôle insuffisant pour accéder à cette page');\n            return false;\n          }\n        }\n\n        // Vérifier les permissions requises\n        const requiredPermissions = route.data['permissions'] as { resource: string, action: PermissionAction }[];\n        if (requiredPermissions && requiredPermissions.length > 0) {\n          const hasAllPermissions = requiredPermissions.every(perm =>\n            this.adminAuthService.hasPermission(perm.resource, perm.action)\n          );\n          if (!hasAllPermissions) {\n            this.handleAccessDenied('Permissions insuffisantes pour accéder à cette page');\n            return false;\n          }\n        }\n\n        return true;\n      })\n    );\n  }\n\n  private handleAccessDenied(message: string): void {\n    this.router.navigate(['/admin/dashboard'], {\n      queryParams: { \n        error: 'access_denied', \n        message: message \n      }\n    });\n  }\n}\n", "import { Routes } from '@angular/router';\nimport { AdminAuthGuard } from '../guards/admin-auth.guard';\nimport { SuperAdminGuard } from '../guards/super-admin.guard';\nimport { RoleGuard } from '../guards/role.guard';\nimport { AdminRole, PermissionAction } from '../models/admin.model';\n\nexport const adminRoutes: Routes = [\n  {\n    path: '',\n    redirectTo: 'dashboard',\n    pathMatch: 'full'\n  },\n  {\n    path: 'login',\n    loadComponent: () => import('../components/admin/auth/admin-login/admin-login.component').then(m => m.AdminLoginComponent),\n    title: 'Connexion Admin - Optique Vision'\n  },\n  {\n    path: 'dashboard',\n    canActivate: [AdminAuthGuard],\n    loadComponent: () => import('../components/admin/layout/admin-layout/admin-layout.component').then(m => m.AdminLayoutComponent),\n    title: 'Dashboard Admin - Optique Vision',\n    children: [\n      {\n        path: '',\n        loadComponent: () => import('../components/admin/admin-dashboard/admin-dashboard.component').then(m => m.AdminDashboardComponent),\n        title: 'Tableau de bord - Admin'\n      },\n      {\n        path: 'users',\n        canActivate: [RoleGuard],\n        data: {\n          roles: [AdminRole.ADMIN, AdminRole.SUPER_ADMIN],\n          permissions: [{ resource: 'users', action: PermissionAction.READ }]\n        },\n        loadComponent: () => import('../components/admin/user-management/user-management.component').then(m => m.UserManagementComponent),\n        title: 'Gestion des utilisateurs - Admin'\n      },\n      {\n        path: 'users/roles',\n        canActivate: [SuperAdminGuard],\n        loadComponent: () => import('../components/admin/users/role-assignment/role-assignment.component').then(m => m.RoleAssignmentComponent),\n        title: 'Attribution des rôles - Admin'\n      },\n      {\n        path: 'products',\n        canActivate: [RoleGuard],\n        data: {\n          permissions: [{ resource: 'products', action: PermissionAction.READ }]\n        },\n        loadComponent: () => import('../components/admin/product-management/product-management.component').then(m => m.ProductManagementComponent),\n        title: 'Gestion des produits - Admin'\n      },\n      {\n        path: 'products/validation',\n        canActivate: [RoleGuard],\n        data: { \n          permissions: [{ resource: 'products', action: PermissionAction.APPROVE }]\n        },\n        loadComponent: () => import('../components/admin/products/product-validation/product-validation.component').then(m => m.ProductValidationComponent),\n        title: 'Validation des produits - Admin'\n      },\n      {\n        path: 'categories',\n        canActivate: [AdminAuthGuard],\n        loadComponent: () => import('../components/admin/category-management/category-management.component').then(m => m.CategoryManagementComponent),\n        title: 'Gestion des catégories - Admin'\n      },\n      {\n        path: 'demandes',\n        canActivate: [RoleGuard],\n        data: {\n          roles: [AdminRole.ADMIN, AdminRole.SUPER_ADMIN],\n          permissions: [{ resource: 'categories', action: PermissionAction.APPROVE }]\n        },\n        loadComponent: () => import('../components/admin/demandes-management/demandes-management.component').then(m => m.DemandesManagementComponent),\n        title: 'Gestion des demandes - Admin'\n      },\n      {\n        path: 'commissions',\n        canActivate: [SuperAdminGuard],\n        loadComponent: () => import('../components/admin/business/commission-rules/commission-rules.component').then(m => m.CommissionRulesComponent),\n        title: 'Règles de commission - Admin'\n      },\n      {\n        path: 'promotions',\n        canActivate: [RoleGuard],\n        data: {\n          roles: [AdminRole.ADMIN, AdminRole.SUPER_ADMIN],\n          permissions: [{ resource: 'promotions', action: PermissionAction.CREATE }]\n        },\n        loadComponent: () => import('../components/admin/business/promotion-engine/promotion-engine.component').then(m => m.PromotionEngineComponent),\n        title: 'Moteur de promotions - Admin'\n      },\n      {\n        path: 'avis-moderation',\n        canActivate: [AdminAuthGuard],\n        loadComponent: () => import('../components/admin/avis-moderation/avis-moderation.component').then(m => m.AvisModerationComponent),\n        title: 'Modération des avis - Admin'\n      },\n\n      {\n        path: 'batch',\n        canActivate: [SuperAdminGuard],\n        loadComponent: () => import('../components/admin/tools/batch-operations/batch-operations.component').then(m => m.BatchOperationsComponent),\n        title: 'Opérations par lots - Admin'\n      },\n      {\n        path: 'workflow',\n        canActivate: [RoleGuard],\n        data: { \n          roles: [AdminRole.ADMIN, AdminRole.SUPER_ADMIN]\n        },\n        loadComponent: () => import('../components/admin/tools/workflow-engine/workflow-engine.component').then(m => m.WorkflowEngineComponent),\n        title: 'Moteur de workflow - Admin'\n      },\n\n      {\n        path: 'profile',\n        canActivate: [AdminAuthGuard],\n        loadComponent: () => import('../components/admin/profile/admin-profile/admin-profile.component').then(m => m.AdminProfileComponent),\n        title: 'Mon profil - Admin'\n      },\n      {\n        path: 'reports',\n        canActivate: [AdminAuthGuard],\n        loadComponent: () => import('../components/admin/reports/reports.component').then(m => m.ReportsComponent),\n        title: 'Rapports - Admin'\n      },\n      {\n        path: 'orders',\n        canActivate: [AdminAuthGuard],\n        loadComponent: () => import('../components/admin/order-management/order-management.component').then(m => m.OrderManagementComponent),\n        title: 'Gestion des commandes - Admin'\n      },\n\n    ]\n  },\n  {\n    path: 'unauthorized',\n    loadComponent: () => import('../components/admin/errors/unauthorized/unauthorized.component').then(m => m.UnauthorizedComponent),\n    title: 'Accès non autorisé - Admin'\n  },\n  {\n    path: '**',\n    redirectTo: 'login'\n  }\n];\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;AAQM,IAAO,iBAAP,MAAO,gBAAc;EAGf;EACA;EAFV,YACU,kBACA,QAAc;AADd,SAAA,mBAAA;AACA,SAAA,SAAA;EACP;EAEH,YACE,OACA,OAA0B;AAE1B,WAAO,KAAK,UAAU,MAAM,GAAG;EACjC;EAEA,iBACE,YACA,OAA0B;AAE1B,WAAO,KAAK,UAAU,MAAM,GAAG;EACjC;EAEQ,UAAU,KAAW;AAC3B,WAAO,KAAK,iBAAiB,iBAAiB,KAC5C,KAAK,CAAC,GACN,IAAI,qBAAkB;AACpB,UAAI,iBAAiB;AACnB,eAAO;MACT,OAAO;AAEL,qBAAa,QAAQ,sBAAsB,GAAG;AAC9C,aAAK,OAAO,SAAS,CAAC,eAAe,CAAC;AACtC,eAAO;MACT;IACF,CAAC,CAAC;EAEN;;qCAnCW,iBAAc,mBAAA,gBAAA,GAAA,mBAAA,MAAA,CAAA;EAAA;4EAAd,iBAAc,SAAd,gBAAc,WAAA,YAFb,OAAM,CAAA;;;sEAEP,gBAAc,CAAA;UAH1B;WAAW;MACV,YAAY;KACb;;;;;ACEK,IAAO,kBAAP,MAAO,iBAAe;EAGhB;EACA;EAFV,YACU,kBACA,QAAc;AADd,SAAA,mBAAA;AACA,SAAA,SAAA;EACP;EAEH,YACE,OACA,OAA0B;AAE1B,WAAO,KAAK,iBAAiB,aAAa,KACxC,KAAK,CAAC,GACN,IAAI,UAAO;AACT,UAAI,QAAQ,KAAK,SAAS,UAAU,aAAa;AAC/C,eAAO;MACT,OAAO;AAEL,aAAK,OAAO,SAAS,CAAC,kBAAkB,GAAG;UACzC,aAAa,EAAE,OAAO,iBAAiB,SAAS,mDAAyC;SAC1F;AACD,eAAO;MACT;IACF,CAAC,CAAC;EAEN;;qCAzBW,kBAAe,mBAAA,gBAAA,GAAA,mBAAA,MAAA,CAAA;EAAA;4EAAf,kBAAe,SAAf,iBAAe,WAAA,YAFd,OAAM,CAAA;;;sEAEP,iBAAe,CAAA;UAH3B;WAAW;MACV,YAAY;KACb;;;;;ACCK,IAAO,YAAP,MAAO,WAAS;EAGV;EACA;EAFV,YACU,kBACA,QAAc;AADd,SAAA,mBAAA;AACA,SAAA,SAAA;EACP;EAEH,YACE,OACA,OAA0B;AAE1B,WAAO,KAAK,iBAAiB,aAAa,KACxC,KAAK,CAAC,GACN,IAAI,UAAO;AACT,UAAI,CAAC,MAAM;AACT,aAAK,OAAO,SAAS,CAAC,cAAc,CAAC;AACrC,eAAO;MACT;AAGA,YAAM,gBAAgB,MAAM,KAAK,OAAO;AACxC,UAAI,iBAAiB,cAAc,SAAS,GAAG;AAC7C,cAAM,UAAU,cAAc,SAAS,KAAK,IAAI;AAChD,YAAI,CAAC,SAAS;AACZ,eAAK,mBAAmB,qDAA4C;AACpE,iBAAO;QACT;MACF;AAGA,YAAM,sBAAsB,MAAM,KAAK,aAAa;AACpD,UAAI,uBAAuB,oBAAoB,SAAS,GAAG;AACzD,cAAM,oBAAoB,oBAAoB,MAAM,UAClD,KAAK,iBAAiB,cAAc,KAAK,UAAU,KAAK,MAAM,CAAC;AAEjE,YAAI,CAAC,mBAAmB;AACtB,eAAK,mBAAmB,2DAAqD;AAC7E,iBAAO;QACT;MACF;AAEA,aAAO;IACT,CAAC,CAAC;EAEN;EAEQ,mBAAmB,SAAe;AACxC,SAAK,OAAO,SAAS,CAAC,kBAAkB,GAAG;MACzC,aAAa;QACX,OAAO;QACP;;KAEH;EACH;;qCArDW,YAAS,mBAAA,gBAAA,GAAA,mBAAA,MAAA,CAAA;EAAA;4EAAT,YAAS,SAAT,WAAS,WAAA,YAFR,OAAM,CAAA;;;sEAEP,WAAS,CAAA;UAHrB;WAAW;MACV,YAAY;KACb;;;;;ACFM,IAAM,cAAsB;EACjC;IACE,MAAM;IACN,YAAY;IACZ,WAAW;;EAEb;IACE,MAAM;IACN,eAAe,MAAM,OAAO,qBAA4D,EAAE,KAAK,OAAK,EAAE,mBAAmB;IACzH,OAAO;;EAET;IACE,MAAM;IACN,aAAa,CAAC,cAAc;IAC5B,eAAe,MAAM,OAAO,qBAAgE,EAAE,KAAK,OAAK,EAAE,oBAAoB;IAC9H,OAAO;IACP,UAAU;MACR;QACE,MAAM;QACN,eAAe,MAAM,OAAO,qBAA+D,EAAE,KAAK,OAAK,EAAE,uBAAuB;QAChI,OAAO;;MAET;QACE,MAAM;QACN,aAAa,CAAC,SAAS;QACvB,MAAM;UACJ,OAAO,CAAC,UAAU,OAAO,UAAU,WAAW;UAC9C,aAAa,CAAC,EAAE,UAAU,SAAS,QAAQ,iBAAiB,KAAI,CAAE;;QAEpE,eAAe,MAAM,OAAO,qBAA+D,EAAE,KAAK,OAAK,EAAE,uBAAuB;QAChI,OAAO;;MAET;QACE,MAAM;QACN,aAAa,CAAC,eAAe;QAC7B,eAAe,MAAM,OAAO,qBAAqE,EAAE,KAAK,OAAK,EAAE,uBAAuB;QACtI,OAAO;;MAET;QACE,MAAM;QACN,aAAa,CAAC,SAAS;QACvB,MAAM;UACJ,aAAa,CAAC,EAAE,UAAU,YAAY,QAAQ,iBAAiB,KAAI,CAAE;;QAEvE,eAAe,MAAM,OAAO,qBAAqE,EAAE,KAAK,OAAK,EAAE,0BAA0B;QACzI,OAAO;;MAET;QACE,MAAM;QACN,aAAa,CAAC,SAAS;QACvB,MAAM;UACJ,aAAa,CAAC,EAAE,UAAU,YAAY,QAAQ,iBAAiB,QAAO,CAAE;;QAE1E,eAAe,MAAM,OAAO,qBAA8E,EAAE,KAAK,OAAK,EAAE,0BAA0B;QAClJ,OAAO;;MAET;QACE,MAAM;QACN,aAAa,CAAC,cAAc;QAC5B,eAAe,MAAM,OAAO,qBAAuE,EAAE,KAAK,OAAK,EAAE,2BAA2B;QAC5I,OAAO;;MAET;QACE,MAAM;QACN,aAAa,CAAC,SAAS;QACvB,MAAM;UACJ,OAAO,CAAC,UAAU,OAAO,UAAU,WAAW;UAC9C,aAAa,CAAC,EAAE,UAAU,cAAc,QAAQ,iBAAiB,QAAO,CAAE;;QAE5E,eAAe,MAAM,OAAO,qBAAuE,EAAE,KAAK,OAAK,EAAE,2BAA2B;QAC5I,OAAO;;MAET;QACE,MAAM;QACN,aAAa,CAAC,eAAe;QAC7B,eAAe,MAAM,OAAO,qBAA0E,EAAE,KAAK,OAAK,EAAE,wBAAwB;QAC5I,OAAO;;MAET;QACE,MAAM;QACN,aAAa,CAAC,SAAS;QACvB,MAAM;UACJ,OAAO,CAAC,UAAU,OAAO,UAAU,WAAW;UAC9C,aAAa,CAAC,EAAE,UAAU,cAAc,QAAQ,iBAAiB,OAAM,CAAE;;QAE3E,eAAe,MAAM,OAAO,qBAA0E,EAAE,KAAK,OAAK,EAAE,wBAAwB;QAC5I,OAAO;;MAET;QACE,MAAM;QACN,aAAa,CAAC,cAAc;QAC5B,eAAe,MAAM,OAAO,qBAA+D,EAAE,KAAK,OAAK,EAAE,uBAAuB;QAChI,OAAO;;MAGT;QACE,MAAM;QACN,aAAa,CAAC,eAAe;QAC7B,eAAe,MAAM,OAAO,qBAAuE,EAAE,KAAK,OAAK,EAAE,wBAAwB;QACzI,OAAO;;MAET;QACE,MAAM;QACN,aAAa,CAAC,SAAS;QACvB,MAAM;UACJ,OAAO,CAAC,UAAU,OAAO,UAAU,WAAW;;QAEhD,eAAe,MAAM,OAAO,qBAAqE,EAAE,KAAK,OAAK,EAAE,uBAAuB;QACtI,OAAO;;MAGT;QACE,MAAM;QACN,aAAa,CAAC,cAAc;QAC5B,eAAe,MAAM,OAAO,qBAAmE,EAAE,KAAK,OAAK,EAAE,qBAAqB;QAClI,OAAO;;MAET;QACE,MAAM;QACN,aAAa,CAAC,cAAc;QAC5B,eAAe,MAAM,OAAO,qBAA+C,EAAE,KAAK,OAAK,EAAE,gBAAgB;QACzG,OAAO;;MAET;QACE,MAAM;QACN,aAAa,CAAC,cAAc;QAC5B,eAAe,MAAM,OAAO,qBAAiE,EAAE,KAAK,OAAK,EAAE,wBAAwB;QACnI,OAAO;;;;EAKb;IACE,MAAM;IACN,eAAe,MAAM,OAAO,qBAAgE,EAAE,KAAK,OAAK,EAAE,qBAAqB;IAC/H,OAAO;;EAET;IACE,MAAM;IACN,YAAY;;;", "names": []}
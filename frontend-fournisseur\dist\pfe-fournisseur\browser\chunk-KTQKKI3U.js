import {
  environment
} from "./chunk-GFHHPDQ6.js";
import {
  HttpClient
} from "./chunk-7JDDWGD3.js";
import {
  Injectable,
  setClassMetadata,
  tap,
  ɵɵdefineInjectable,
  ɵɵinject
} from "./chunk-UBZQS7JS.js";

// src/app/services/marque.service.ts
var MarqueService = class _MarqueService {
  http;
  API_URL = `${environment.apiUrl || "https://localhost:7264/api"}/Marques`;
  constructor(http) {
    this.http = http;
  }
  /**
   * GET /api/Marques - Obtenir toutes les marques
   */
  getAll() {
    console.log("\u{1F4E6} R\xE9cup\xE9ration des marques");
    return this.http.get(`${this.API_URL}/enriched`).pipe(tap((response) => console.log("\u2705 Marques r\xE9cup\xE9r\xE9es:", response)));
  }
  /**
   * GET /api/Marques/{id} - Obtenir une marque par ID
   */
  getById(id) {
    console.log("\u{1F50D} R\xE9cup\xE9ration de la marque ID:", id);
    return this.http.get(`${this.API_URL}/${id}`).pipe(tap((response) => console.log("\u2705 Marque r\xE9cup\xE9r\xE9e:", response)));
  }
  /**
   * POST /api/Marques - Créer une nouvelle marque
   */
  create(marque) {
    console.log("\u2795 Cr\xE9ation d'une nouvelle marque:", marque);
    return this.http.post(this.API_URL, marque).pipe(tap((response) => console.log("\u2705 Marque cr\xE9\xE9e:", response)));
  }
  /**
   * PUT /api/Marques/{id} - Mettre à jour une marque
   */
  update(id, marque) {
    console.log("\u270F\uFE0F Mise \xE0 jour de la marque ID:", id, marque);
    return this.http.put(`${this.API_URL}/${id}`, marque).pipe(tap((response) => console.log("\u2705 Marque mise \xE0 jour:", response)));
  }
  /**
   * DELETE /api/Marques/{id} - Supprimer une marque
   */
  delete(id) {
    console.log("\u{1F5D1}\uFE0F Suppression de la marque ID:", id);
    return this.http.delete(`${this.API_URL}/${id}`).pipe(tap(() => console.log("\u2705 Marque supprim\xE9e:", id)));
  }
  /**
   * GET /api/Marques/dropdown - Obtenir les marques pour dropdown
   */
  getDropdown() {
    console.log("\u{1F4CB} R\xE9cup\xE9ration des marques pour dropdown");
    return this.http.get(`${this.API_URL}/dropdown`).pipe(tap((response) => console.log("\u2705 Dropdown marques r\xE9cup\xE9r\xE9:", response)));
  }
  static \u0275fac = function MarqueService_Factory(__ngFactoryType__) {
    return new (__ngFactoryType__ || _MarqueService)(\u0275\u0275inject(HttpClient));
  };
  static \u0275prov = /* @__PURE__ */ \u0275\u0275defineInjectable({ token: _MarqueService, factory: _MarqueService.\u0275fac, providedIn: "root" });
};
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && setClassMetadata(MarqueService, [{
    type: Injectable,
    args: [{
      providedIn: "root"
    }]
  }], () => [{ type: HttpClient }], null);
})();

// src/app/services/forme.service.ts
var FormeService = class _FormeService {
  http;
  API_URL = `${environment.apiUrl || "https://localhost:7264/api"}/Formes`;
  constructor(http) {
    this.http = http;
  }
  /**
   * GET /api/Formes - Obtenir toutes les formes
   */
  getAll() {
    console.log("\u{1F4E6} R\xE9cup\xE9ration des formes");
    return this.http.get(`${this.API_URL}/enriched`).pipe(tap((response) => console.log("\u2705 Formes r\xE9cup\xE9r\xE9es:", response)));
  }
  /**
   * GET /api/Formes/{id} - Obtenir une forme par ID
   */
  getById(id) {
    console.log("\u{1F50D} R\xE9cup\xE9ration de la forme ID:", id);
    return this.http.get(`${this.API_URL}/${id}`).pipe(tap((response) => console.log("\u2705 Forme r\xE9cup\xE9r\xE9e:", response)));
  }
  /**
   * POST /api/Formes - Créer une nouvelle forme
   */
  create(forme) {
    console.log("\u2795 Cr\xE9ation d'une nouvelle forme:", forme);
    return this.http.post(this.API_URL, forme).pipe(tap((response) => console.log("\u2705 Forme cr\xE9\xE9e:", response)));
  }
  /**
   * PUT /api/Formes/{id} - Mettre à jour une forme
   */
  update(id, forme) {
    console.log("\u270F\uFE0F Mise \xE0 jour de la forme ID:", id, forme);
    return this.http.put(`${this.API_URL}/${id}`, forme).pipe(tap((response) => console.log("\u2705 Forme mise \xE0 jour:", response)));
  }
  /**
   * DELETE /api/Formes/{id} - Supprimer une forme
   */
  delete(id) {
    console.log("\u{1F5D1}\uFE0F Suppression de la forme ID:", id);
    return this.http.delete(`${this.API_URL}/${id}`).pipe(tap(() => console.log("\u2705 Forme supprim\xE9e:", id)));
  }
  /**
   * GET /api/Formes/by-categorie/{categorieId} - Obtenir les formes d'une catégorie
   */
  getByCategorie(categorieId) {
    console.log("\u{1F4C2} R\xE9cup\xE9ration des formes pour la cat\xE9gorie:", categorieId);
    return this.http.get(`${this.API_URL}/by-categorie/${categorieId}`).pipe(tap((response) => console.log("\u2705 Formes par cat\xE9gorie r\xE9cup\xE9r\xE9es:", response)));
  }
  /**
   * GET /api/Formes/dropdown - Obtenir les formes pour dropdown
   */
  getDropdown() {
    console.log("\u{1F4CB} R\xE9cup\xE9ration des formes pour dropdown");
    return this.http.get(`${this.API_URL}/dropdown`).pipe(tap((response) => console.log("\u2705 Dropdown formes r\xE9cup\xE9r\xE9:", response)));
  }
  static \u0275fac = function FormeService_Factory(__ngFactoryType__) {
    return new (__ngFactoryType__ || _FormeService)(\u0275\u0275inject(HttpClient));
  };
  static \u0275prov = /* @__PURE__ */ \u0275\u0275defineInjectable({ token: _FormeService, factory: _FormeService.\u0275fac, providedIn: "root" });
};
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && setClassMetadata(FormeService, [{
    type: Injectable,
    args: [{
      providedIn: "root"
    }]
  }], () => [{ type: HttpClient }], null);
})();

// src/app/services/taux-tva.service.ts
var TauxTVAService = class _TauxTVAService {
  http;
  API_URL = `${environment.apiUrl}/tva`;
  constructor(http) {
    this.http = http;
    console.log("\u{1F527} TauxTVAService initialis\xE9 avec URL:", this.API_URL);
  }
  /**
   * GET /api/tva - Obtenir tous les taux TVA
   */
  getAll() {
    console.log("\u{1F4E6} R\xE9cup\xE9ration des taux TVA");
    return this.http.get(this.API_URL).pipe(tap((response) => console.log("\u2705 Taux TVA r\xE9cup\xE9r\xE9s:", response)));
  }
  /**
   * GET /api/tva/{id} - Obtenir un taux TVA par ID
   */
  getById(id) {
    console.log("\u{1F50D} R\xE9cup\xE9ration du taux TVA ID:", id);
    return this.http.get(`${this.API_URL}/${id}`).pipe(tap((response) => console.log("\u2705 Taux TVA r\xE9cup\xE9r\xE9:", response)));
  }
  /**
   * POST /api/tva - Créer un nouveau taux TVA
   */
  create(tauxTVA) {
    console.log("\u2795 Cr\xE9ation d'un nouveau taux TVA:", tauxTVA);
    return this.http.post(this.API_URL, tauxTVA).pipe(tap((response) => console.log("\u2705 Taux TVA cr\xE9\xE9:", response)));
  }
  /**
   * PUT /api/tva/{id} - Mettre à jour un taux TVA
   */
  update(id, tauxTVA) {
    console.log("\u270F\uFE0F Mise \xE0 jour du taux TVA ID:", id, tauxTVA);
    return this.http.put(`${this.API_URL}/${id}`, tauxTVA).pipe(tap((response) => console.log("\u2705 Taux TVA mis \xE0 jour:", response)));
  }
  /**
   * DELETE /api/tva/{id} - Supprimer un taux TVA
   */
  delete(id) {
    console.log("\u{1F5D1}\uFE0F Suppression du taux TVA ID:", id);
    return this.http.delete(`${this.API_URL}/${id}`).pipe(tap(() => console.log("\u2705 Taux TVA supprim\xE9:", id)));
  }
  /**
   * GET /api/tva/actuel - Obtenir le taux TVA actuel
   */
  getActuel() {
    console.log("\u{1F4C5} R\xE9cup\xE9ration du taux TVA actuel");
    return this.http.get(`${this.API_URL}/actuel`).pipe(tap((response) => console.log("\u2705 Taux TVA actuel r\xE9cup\xE9r\xE9:", response)));
  }
  /**
   * GET /api/tva/dropdown - Obtenir les taux TVA pour dropdown
   */
  getDropdown() {
    console.log("\u{1F4CB} R\xE9cup\xE9ration des taux TVA pour dropdown");
    return this.http.get(`${this.API_URL}/dropdown`).pipe(tap((response) => console.log("\u2705 Dropdown taux TVA r\xE9cup\xE9r\xE9:", response)));
  }
  /**
   * GET /api/tva/by-categorie/{categorieId} - Obtenir les taux TVA d'une catégorie
   */
  getByCategorie(categorieId) {
    console.log("\u{1F4C2} R\xE9cup\xE9ration des taux TVA pour la cat\xE9gorie:", categorieId);
    return this.http.get(`${this.API_URL}/by-categorie/${categorieId}`).pipe(tap((response) => console.log("\u2705 Taux TVA par cat\xE9gorie r\xE9cup\xE9r\xE9s:", response)));
  }
  /**
   * GET /api/tva/dropdown/{categorieId} - Obtenir les taux TVA pour dropdown par catégorie
   */
  getDropdownByCategorie(categorieId) {
    console.log("\u{1F4CB} R\xE9cup\xE9ration des taux TVA dropdown pour la cat\xE9gorie:", categorieId);
    return this.http.get(`${this.API_URL}/dropdown/${categorieId}`).pipe(tap((response) => console.log("\u2705 Dropdown taux TVA par cat\xE9gorie r\xE9cup\xE9r\xE9:", response)));
  }
  /**
   * POST /api/tva/calculer-ttc - Calculer le montant TTC
   */
  calculerTTC(request) {
    console.log("\u{1F9EE} Calcul TTC:", request);
    return this.http.post(`${this.API_URL}/calculer-ttc`, request).pipe(tap((response) => console.log("\u2705 Calcul TTC effectu\xE9:", response)));
  }
  /**
   * POST /api/tva/calculer-ht - Calculer le montant HT
   */
  calculerHT(request) {
    console.log("\u{1F9EE} Calcul HT:", request);
    return this.http.post(`${this.API_URL}/calculer-ht`, request).pipe(tap((response) => console.log("\u2705 Calcul HT effectu\xE9:", response)));
  }
  static \u0275fac = function TauxTVAService_Factory(__ngFactoryType__) {
    return new (__ngFactoryType__ || _TauxTVAService)(\u0275\u0275inject(HttpClient));
  };
  static \u0275prov = /* @__PURE__ */ \u0275\u0275defineInjectable({ token: _TauxTVAService, factory: _TauxTVAService.\u0275fac, providedIn: "root" });
};
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && setClassMetadata(TauxTVAService, [{
    type: Injectable,
    args: [{
      providedIn: "root"
    }]
  }], () => [{ type: HttpClient }], null);
})();

export {
  MarqueService,
  FormeService,
  TauxTVAService
};
//# sourceMappingURL=chunk-KTQKKI3U.js.map

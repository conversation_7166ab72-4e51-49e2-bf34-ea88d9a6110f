import {
  DefaultV<PERSON>ueAccessor,
  FormsModule,
  NgControlStatus,
  NgModel,
  NgSelectOption,
  SelectControlValueAccessor,
  ɵNgSelectMultipleOption
} from "./chunk-HQBVYEOO.js";
import {
  AdminService
} from "./chunk-EFJVWLOV.js";
import "./chunk-GFHHPDQ6.js";
import "./chunk-7JDDWGD3.js";
import {
  CommonModule,
  Component,
  NgClass,
  NgForOf,
  NgIf,
  __spreadProps,
  __spreadValues,
  computed,
  setClassMetadata,
  signal,
  ɵsetClassDebugInfo,
  ɵɵadvance,
  ɵɵclassMap,
  ɵɵclassProp,
  ɵɵdefineComponent,
  ɵɵdirectiveInject,
  ɵɵelement,
  ɵɵelementEnd,
  ɵɵelementStart,
  ɵɵgetCurrentView,
  ɵɵlistener,
  ɵɵnextContext,
  ɵɵproperty,
  ɵɵreference,
  ɵɵresetView,
  ɵɵrestoreView,
  ɵɵtemplate,
  ɵɵtemplateRefExtractor,
  ɵɵtext,
  ɵɵtextInterpolate,
  ɵɵtextInterpolate1,
  ɵɵtextInterpolate2,
  ɵɵtextInterpolate3,
  ɵɵtwoWayBindingSet,
  ɵɵtwoWayListener,
  ɵɵtwoWayProperty
} from "./chunk-UBZQS7JS.js";

// src/app/components/admin/user-management/user-management.component.ts
function UserManagementComponent_option_21_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "option", 26);
    \u0275\u0275text(1);
    \u0275\u0275elementEnd();
  }
  if (rf & 2) {
    const role_r1 = ctx.$implicit;
    \u0275\u0275property("value", role_r1);
    \u0275\u0275advance();
    \u0275\u0275textInterpolate(role_r1);
  }
}
function UserManagementComponent_div_29_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "div", 27);
    \u0275\u0275element(1, "i", 28);
    \u0275\u0275text(2);
    \u0275\u0275elementEnd();
  }
  if (rf & 2) {
    const ctx_r1 = \u0275\u0275nextContext();
    \u0275\u0275advance(2);
    \u0275\u0275textInterpolate1(" ", ctx_r1.error(), " ");
  }
}
function UserManagementComponent_div_30_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "div", 29);
    \u0275\u0275element(1, "div", 30);
    \u0275\u0275elementStart(2, "p");
    \u0275\u0275text(3, "Chargement des utilisateurs...");
    \u0275\u0275elementEnd()();
  }
}
function UserManagementComponent_div_31_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "div", 31)(1, "h4", 32);
    \u0275\u0275text(2, "\u{1F50D}Info General");
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(3, "p")(4, "strong");
    \u0275\u0275text(5, "Nombre d'utilisateurs:");
    \u0275\u0275elementEnd();
    \u0275\u0275text(6);
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(7, "p")(8, "strong");
    \u0275\u0275text(9, "Total items:");
    \u0275\u0275elementEnd();
    \u0275\u0275text(10);
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(11, "p")(12, "strong");
    \u0275\u0275text(13, "Utilisateurs affich\xE9s:");
    \u0275\u0275elementEnd();
    \u0275\u0275text(14);
    \u0275\u0275elementEnd()();
  }
  if (rf & 2) {
    const ctx_r1 = \u0275\u0275nextContext();
    \u0275\u0275advance(6);
    \u0275\u0275textInterpolate1(" ", ctx_r1.utilisateurs().length, "");
    \u0275\u0275advance(4);
    \u0275\u0275textInterpolate1(" ", ctx_r1.totalItems(), "");
    \u0275\u0275advance(4);
    \u0275\u0275textInterpolate1(" ", ctx_r1.utilisateurs().length, "");
  }
}
function UserManagementComponent_div_32_tr_19_span_20_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "span");
    \u0275\u0275text(1);
    \u0275\u0275elementEnd();
  }
  if (rf & 2) {
    const user_r4 = \u0275\u0275nextContext().$implicit;
    const ctx_r1 = \u0275\u0275nextContext(2);
    \u0275\u0275advance();
    \u0275\u0275textInterpolate1(" ", ctx_r1.formatDate(user_r4.derniereConnexion), " ");
  }
}
function UserManagementComponent_div_32_tr_19_ng_template_21_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "span", 53);
    \u0275\u0275text(1, "Jamais connect\xE9");
    \u0275\u0275elementEnd();
  }
}
function UserManagementComponent_div_32_tr_19_Template(rf, ctx) {
  if (rf & 1) {
    const _r3 = \u0275\u0275getCurrentView();
    \u0275\u0275elementStart(0, "tr", 37)(1, "td", 38)(2, "div", 39);
    \u0275\u0275text(3);
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(4, "div", 40)(5, "div", 41);
    \u0275\u0275text(6);
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(7, "div", 42);
    \u0275\u0275text(8);
    \u0275\u0275elementEnd()()();
    \u0275\u0275elementStart(9, "td", 43);
    \u0275\u0275text(10);
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(11, "td")(12, "span", 44);
    \u0275\u0275text(13);
    \u0275\u0275elementEnd()();
    \u0275\u0275elementStart(14, "td")(15, "span", 45);
    \u0275\u0275text(16);
    \u0275\u0275elementEnd()();
    \u0275\u0275elementStart(17, "td", 46);
    \u0275\u0275text(18);
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(19, "td", 46);
    \u0275\u0275template(20, UserManagementComponent_div_32_tr_19_span_20_Template, 2, 1, "span", 47)(21, UserManagementComponent_div_32_tr_19_ng_template_21_Template, 2, 0, "ng-template", null, 0, \u0275\u0275templateRefExtractor);
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(23, "td", 48)(24, "div", 49)(25, "button", 50);
    \u0275\u0275listener("click", function UserManagementComponent_div_32_tr_19_Template_button_click_25_listener() {
      const user_r4 = \u0275\u0275restoreView(_r3).$implicit;
      const ctx_r1 = \u0275\u0275nextContext(2);
      return \u0275\u0275resetView(ctx_r1.toggleUtilisateur(user_r4));
    });
    \u0275\u0275element(26, "i");
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(27, "button", 51);
    \u0275\u0275listener("click", function UserManagementComponent_div_32_tr_19_Template_button_click_27_listener() {
      const user_r4 = \u0275\u0275restoreView(_r3).$implicit;
      const ctx_r1 = \u0275\u0275nextContext(2);
      return \u0275\u0275resetView(ctx_r1.deleteUtilisateur(user_r4));
    });
    \u0275\u0275element(28, "i", 52);
    \u0275\u0275elementEnd()()()();
  }
  if (rf & 2) {
    const user_r4 = ctx.$implicit;
    const noConnection_r5 = \u0275\u0275reference(22);
    const ctx_r1 = \u0275\u0275nextContext(2);
    \u0275\u0275advance(3);
    \u0275\u0275textInterpolate1(" ", ctx_r1.getInitials(user_r4.nomComplet), " ");
    \u0275\u0275advance(3);
    \u0275\u0275textInterpolate(user_r4.nomComplet);
    \u0275\u0275advance(2);
    \u0275\u0275textInterpolate1("ID: ", user_r4.id, "");
    \u0275\u0275advance(2);
    \u0275\u0275textInterpolate(user_r4.email);
    \u0275\u0275advance(2);
    \u0275\u0275property("ngClass", ctx_r1.getRoleClass(user_r4.role));
    \u0275\u0275advance();
    \u0275\u0275textInterpolate1(" ", user_r4.role, " ");
    \u0275\u0275advance(2);
    \u0275\u0275property("ngClass", ctx_r1.getStatusClass(user_r4));
    \u0275\u0275advance();
    \u0275\u0275textInterpolate1(" ", ctx_r1.getStatusText(user_r4), " ");
    \u0275\u0275advance(2);
    \u0275\u0275textInterpolate(ctx_r1.formatDate(user_r4.dateInscription));
    \u0275\u0275advance(2);
    \u0275\u0275property("ngIf", user_r4.derniereConnexion)("ngIfElse", noConnection_r5);
    \u0275\u0275advance(5);
    \u0275\u0275classProp("btn-activate", !user_r4.estActif)("btn-deactivate", user_r4.estActif);
    \u0275\u0275property("title", user_r4.estActif ? "D\xE9sactiver" : "Activer");
    \u0275\u0275advance();
    \u0275\u0275classMap(user_r4.estActif ? "icon-pause" : "icon-play");
  }
}
function UserManagementComponent_div_32_div_20_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "div", 54);
    \u0275\u0275element(1, "i", 5);
    \u0275\u0275elementStart(2, "h3");
    \u0275\u0275text(3, "Aucun utilisateur trouv\xE9");
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(4, "p");
    \u0275\u0275text(5, "Aucun utilisateur ne correspond aux crit\xE8res de recherche.");
    \u0275\u0275elementEnd()();
  }
}
function UserManagementComponent_div_32_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "div", 33)(1, "table", 34)(2, "thead")(3, "tr")(4, "th");
    \u0275\u0275text(5, "Utilisateur");
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(6, "th");
    \u0275\u0275text(7, "Email");
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(8, "th");
    \u0275\u0275text(9, "R\xF4le");
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(10, "th");
    \u0275\u0275text(11, "Statut");
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(12, "th");
    \u0275\u0275text(13, "Date cr\xE9ation");
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(14, "th");
    \u0275\u0275text(15, "Derni\xE8re connexion");
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(16, "th");
    \u0275\u0275text(17, "Actions");
    \u0275\u0275elementEnd()()();
    \u0275\u0275elementStart(18, "tbody");
    \u0275\u0275template(19, UserManagementComponent_div_32_tr_19_Template, 29, 18, "tr", 35);
    \u0275\u0275elementEnd()();
    \u0275\u0275template(20, UserManagementComponent_div_32_div_20_Template, 6, 0, "div", 36);
    \u0275\u0275elementEnd();
  }
  if (rf & 2) {
    const ctx_r1 = \u0275\u0275nextContext();
    \u0275\u0275advance(19);
    \u0275\u0275property("ngForOf", ctx_r1.utilisateurs());
    \u0275\u0275advance();
    \u0275\u0275property("ngIf", ctx_r1.utilisateurs().length === 0);
  }
}
function UserManagementComponent_div_33_Template(rf, ctx) {
  if (rf & 1) {
    const _r6 = \u0275\u0275getCurrentView();
    \u0275\u0275elementStart(0, "div", 55)(1, "div", 56);
    \u0275\u0275text(2);
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(3, "div", 57)(4, "button", 58);
    \u0275\u0275listener("click", function UserManagementComponent_div_33_Template_button_click_4_listener() {
      \u0275\u0275restoreView(_r6);
      const ctx_r1 = \u0275\u0275nextContext();
      return \u0275\u0275resetView(ctx_r1.onPageChange(ctx_r1.currentPage() - 1));
    });
    \u0275\u0275element(5, "i", 59);
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(6, "span", 60);
    \u0275\u0275text(7);
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(8, "button", 58);
    \u0275\u0275listener("click", function UserManagementComponent_div_33_Template_button_click_8_listener() {
      \u0275\u0275restoreView(_r6);
      const ctx_r1 = \u0275\u0275nextContext();
      return \u0275\u0275resetView(ctx_r1.onPageChange(ctx_r1.currentPage() + 1));
    });
    \u0275\u0275element(9, "i", 61);
    \u0275\u0275elementEnd()()();
  }
  if (rf & 2) {
    const ctx_r1 = \u0275\u0275nextContext();
    \u0275\u0275advance(2);
    \u0275\u0275textInterpolate3(" Affichage de ", (ctx_r1.currentPage() - 1) * ctx_r1.pageSize() + 1, " \xE0 ", ctx_r1.Math.min(ctx_r1.currentPage() * ctx_r1.pageSize(), ctx_r1.totalItems()), " sur ", ctx_r1.totalItems(), " utilisateurs ");
    \u0275\u0275advance(2);
    \u0275\u0275property("disabled", ctx_r1.currentPage() === 1);
    \u0275\u0275advance(3);
    \u0275\u0275textInterpolate2(" Page ", ctx_r1.currentPage(), " sur ", ctx_r1.totalPages(), " ");
    \u0275\u0275advance();
    \u0275\u0275property("disabled", ctx_r1.currentPage() === ctx_r1.totalPages());
  }
}
var UserManagementComponent = class _UserManagementComponent {
  adminService;
  // Angular 19: Signals
  utilisateurs = signal([]);
  isLoading = signal(false);
  error = signal("");
  searchTerm = signal("");
  selectedRole = signal("");
  selectedStatus = signal("");
  currentPage = signal(1);
  pageSize = signal(10);
  totalItems = signal(0);
  // Computed signals - Plus besoin de filtrage côté client car fait côté serveur
  totalPages = computed(() => Math.ceil(this.totalItems() / this.pageSize()));
  roles = ["Admin", "Fournisseur", "Client"];
  // Expose Math to template
  Math = Math;
  constructor(adminService) {
    this.adminService = adminService;
  }
  ngOnInit() {
    this.loadUtilisateurs();
  }
  loadUtilisateurs() {
    this.isLoading.set(true);
    this.error.set("");
    const params = {
      page: this.currentPage(),
      pageSize: this.pageSize(),
      search: this.searchTerm() || void 0,
      role: this.selectedRole() || void 0,
      estActif: this.selectedStatus() === "actif" ? true : this.selectedStatus() === "inactif" ? false : void 0
    };
    this.adminService.getUtilisateurs(params).subscribe({
      next: (response) => {
        console.log("\u2705 R\xE9ponse pagin\xE9e re\xE7ue:", response);
        this.utilisateurs.set(response.utilisateurs || []);
        this.totalItems.set(response.totalCount || 0);
        this.isLoading.set(false);
      },
      error: (error) => {
        console.error("\u274C Erreur lors du chargement des utilisateurs:", error);
        this.error.set(`Erreur lors du chargement des utilisateurs: ${error.status || "Erreur r\xE9seau"}`);
        this.isLoading.set(false);
      }
    });
  }
  toggleUtilisateur(user) {
    const action = user.estActif ? "d\xE9sactiver" : "activer";
    if (confirm(`\xCAtes-vous s\xFBr de vouloir ${action} l'utilisateur ${user.nomComplet} ?`)) {
      this.adminService.toggleUtilisateur(user.id).subscribe({
        next: () => {
          const users = this.utilisateurs();
          const index = users.findIndex((u) => u.id === user.id);
          if (index !== -1) {
            users[index] = __spreadProps(__spreadValues({}, users[index]), { estActif: !users[index].estActif });
            this.utilisateurs.set([...users]);
          }
          alert(`Utilisateur ${action} avec succ\xE8s`);
        },
        error: (error) => {
          console.error("Erreur lors du toggle:", error);
          alert(`Erreur lors de l'${action}ation de l'utilisateur`);
        }
      });
    }
  }
  deleteUtilisateur(user) {
    if (confirm(`\xCAtes-vous s\xFBr de vouloir supprimer d\xE9finitivement l'utilisateur ${user.nomComplet} ?

Cette action est irr\xE9versible.`)) {
      this.adminService.deleteUtilisateur(user.id).subscribe({
        next: () => {
          const users = this.utilisateurs().filter((u) => u.id !== user.id);
          this.utilisateurs.set(users);
          this.totalItems.set(this.totalItems() - 1);
          alert("Utilisateur supprim\xE9 avec succ\xE8s");
        },
        error: (error) => {
          console.error("Erreur lors de la suppression:", error);
          alert("Erreur lors de la suppression de l'utilisateur");
        }
      });
    }
  }
  onSearch() {
    this.currentPage.set(1);
    this.loadUtilisateurs();
  }
  onFilterChange() {
    this.currentPage.set(1);
    this.loadUtilisateurs();
  }
  onPageChange(page) {
    this.currentPage.set(page);
    this.loadUtilisateurs();
  }
  getStatusClass(user) {
    return user.estActif ? "status-active" : "status-inactive";
  }
  getStatusText(user) {
    return user.estActif ? "Actif" : "Inactif";
  }
  getRoleClass(role) {
    switch (role) {
      case "Admin":
        return "role-admin";
      case "Fournisseur":
        return "role-fournisseur";
      case "Client":
        return "role-client";
      default:
        return "role-default";
    }
  }
  getInitials(nomComplet) {
    if (!nomComplet)
      return "??";
    const parts = nomComplet.trim().split(" ");
    if (parts.length >= 2) {
      return (parts[0].charAt(0) + parts[parts.length - 1].charAt(0)).toUpperCase();
    }
    return nomComplet.charAt(0).toUpperCase() + "?";
  }
  formatDate(date) {
    if (!date)
      return "Non d\xE9fini";
    const dateObj = typeof date === "string" ? new Date(date) : date;
    if (isNaN(dateObj.getTime())) {
      return "Date invalide";
    }
    return dateObj.toLocaleDateString("fr-FR", {
      year: "numeric",
      month: "short",
      day: "numeric",
      hour: "2-digit",
      minute: "2-digit"
    });
  }
  refresh() {
    this.loadUtilisateurs();
  }
  exportUsers() {
    alert("Fonctionnalit\xE9 d'export en cours de d\xE9veloppement");
  }
  static \u0275fac = function UserManagementComponent_Factory(__ngFactoryType__) {
    return new (__ngFactoryType__ || _UserManagementComponent)(\u0275\u0275directiveInject(AdminService));
  };
  static \u0275cmp = /* @__PURE__ */ \u0275\u0275defineComponent({ type: _UserManagementComponent, selectors: [["app-user-management"]], decls: 34, vars: 10, consts: [["noConnection", ""], [1, "user-management-container"], [1, "page-header"], [1, "header-content"], [1, "page-title"], [1, "icon-users"], [1, "header-actions"], [1, "btn", "btn-secondary", 3, "click", "disabled"], [1, "icon-refresh"], [1, "btn", "btn-primary", 3, "click"], [1, "icon-download"], [1, "filters-section"], [1, "search-box"], [1, "icon-search"], ["type", "text", "placeholder", "Rechercher par nom, pr\xE9nom ou email...", 1, "search-input", 3, "ngModelChange", "input", "ngModel"], [1, "filters"], [1, "filter-select", 3, "ngModelChange", "change", "ngModel"], ["value", ""], [3, "value", 4, "ngFor", "ngForOf"], ["value", "actif"], ["value", "inactif"], ["class", "error-message", 4, "ngIf"], ["class", "loading-container", 4, "ngIf"], ["class", "debug-info", "style", "background: #f0f9ff; padding: 1rem; margin: 1rem 0; border-radius: 8px; border: 1px solid #0ea5e9;", 4, "ngIf"], ["class", "table-container", 4, "ngIf"], ["class", "pagination-container", 4, "ngIf"], [3, "value"], [1, "error-message"], [1, "icon-alert"], [1, "loading-container"], [1, "loading-spinner"], [1, "debug-info", 2, "background", "#f0f9ff", "padding", "1rem", "margin", "1rem 0", "border-radius", "8px", "border", "1px solid #0ea5e9"], [2, "margin", "0 0 0.5rem 0", "color", "#0369a1"], [1, "table-container"], [1, "users-table"], ["class", "user-row", 4, "ngFor", "ngForOf"], ["class", "no-data", 4, "ngIf"], [1, "user-row"], [1, "user-info"], [1, "user-avatar"], [1, "user-details"], [1, "user-name"], [1, "user-id"], [1, "user-email"], [1, "role-badge", 3, "ngClass"], [1, "status-badge", 3, "ngClass"], [1, "date-cell"], [4, "ngIf", "ngIfElse"], [1, "actions-cell"], [1, "action-buttons"], [1, "btn-action", "btn-toggle", 3, "click", "title"], ["title", "Supprimer", 1, "btn-action", "btn-delete", 3, "click"], [1, "icon-trash"], [1, "no-connection"], [1, "no-data"], [1, "pagination-container"], [1, "pagination-info"], [1, "pagination"], [1, "btn-page", 3, "click", "disabled"], [1, "icon-chevron-left"], [1, "page-info"], [1, "icon-chevron-right"]], template: function UserManagementComponent_Template(rf, ctx) {
    if (rf & 1) {
      \u0275\u0275elementStart(0, "div", 1)(1, "div", 2)(2, "div", 3)(3, "h1", 4);
      \u0275\u0275element(4, "i", 5);
      \u0275\u0275text(5, " Gestion des Utilisateurs ");
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(6, "div", 6)(7, "button", 7);
      \u0275\u0275listener("click", function UserManagementComponent_Template_button_click_7_listener() {
        return ctx.refresh();
      });
      \u0275\u0275element(8, "i", 8);
      \u0275\u0275text(9, " Actualiser ");
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(10, "button", 9);
      \u0275\u0275listener("click", function UserManagementComponent_Template_button_click_10_listener() {
        return ctx.exportUsers();
      });
      \u0275\u0275element(11, "i", 10);
      \u0275\u0275text(12, " Exporter ");
      \u0275\u0275elementEnd()()()();
      \u0275\u0275elementStart(13, "div", 11)(14, "div", 12);
      \u0275\u0275element(15, "i", 13);
      \u0275\u0275elementStart(16, "input", 14);
      \u0275\u0275twoWayListener("ngModelChange", function UserManagementComponent_Template_input_ngModelChange_16_listener($event) {
        \u0275\u0275twoWayBindingSet(ctx.searchTerm, $event) || (ctx.searchTerm = $event);
        return $event;
      });
      \u0275\u0275listener("input", function UserManagementComponent_Template_input_input_16_listener() {
        return ctx.onSearch();
      });
      \u0275\u0275elementEnd()();
      \u0275\u0275elementStart(17, "div", 15)(18, "select", 16);
      \u0275\u0275twoWayListener("ngModelChange", function UserManagementComponent_Template_select_ngModelChange_18_listener($event) {
        \u0275\u0275twoWayBindingSet(ctx.selectedRole, $event) || (ctx.selectedRole = $event);
        return $event;
      });
      \u0275\u0275listener("change", function UserManagementComponent_Template_select_change_18_listener() {
        return ctx.onFilterChange();
      });
      \u0275\u0275elementStart(19, "option", 17);
      \u0275\u0275text(20, "Tous les r\xF4les");
      \u0275\u0275elementEnd();
      \u0275\u0275template(21, UserManagementComponent_option_21_Template, 2, 2, "option", 18);
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(22, "select", 16);
      \u0275\u0275twoWayListener("ngModelChange", function UserManagementComponent_Template_select_ngModelChange_22_listener($event) {
        \u0275\u0275twoWayBindingSet(ctx.selectedStatus, $event) || (ctx.selectedStatus = $event);
        return $event;
      });
      \u0275\u0275listener("change", function UserManagementComponent_Template_select_change_22_listener() {
        return ctx.onFilterChange();
      });
      \u0275\u0275elementStart(23, "option", 17);
      \u0275\u0275text(24, "Tous les statuts");
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(25, "option", 19);
      \u0275\u0275text(26, "Actifs");
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(27, "option", 20);
      \u0275\u0275text(28, "Inactifs");
      \u0275\u0275elementEnd()()()();
      \u0275\u0275template(29, UserManagementComponent_div_29_Template, 3, 1, "div", 21)(30, UserManagementComponent_div_30_Template, 4, 0, "div", 22)(31, UserManagementComponent_div_31_Template, 15, 3, "div", 23)(32, UserManagementComponent_div_32_Template, 21, 2, "div", 24)(33, UserManagementComponent_div_33_Template, 10, 7, "div", 25);
      \u0275\u0275elementEnd();
    }
    if (rf & 2) {
      \u0275\u0275advance(7);
      \u0275\u0275property("disabled", ctx.isLoading());
      \u0275\u0275advance(9);
      \u0275\u0275twoWayProperty("ngModel", ctx.searchTerm);
      \u0275\u0275advance(2);
      \u0275\u0275twoWayProperty("ngModel", ctx.selectedRole);
      \u0275\u0275advance(3);
      \u0275\u0275property("ngForOf", ctx.roles);
      \u0275\u0275advance();
      \u0275\u0275twoWayProperty("ngModel", ctx.selectedStatus);
      \u0275\u0275advance(7);
      \u0275\u0275property("ngIf", ctx.error());
      \u0275\u0275advance();
      \u0275\u0275property("ngIf", ctx.isLoading());
      \u0275\u0275advance();
      \u0275\u0275property("ngIf", !ctx.isLoading() && !ctx.error());
      \u0275\u0275advance();
      \u0275\u0275property("ngIf", !ctx.isLoading() && !ctx.error());
      \u0275\u0275advance();
      \u0275\u0275property("ngIf", ctx.totalPages() > 1);
    }
  }, dependencies: [CommonModule, NgClass, NgForOf, NgIf, FormsModule, NgSelectOption, \u0275NgSelectMultipleOption, DefaultValueAccessor, SelectControlValueAccessor, NgControlStatus, NgModel], styles: ['\n\n.user-management-container[_ngcontent-%COMP%] {\n  padding: 2rem;\n  max-width: 1400px;\n  margin: 0 auto;\n}\n.page-header[_ngcontent-%COMP%] {\n  margin-bottom: 2rem;\n}\n.header-content[_ngcontent-%COMP%] {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  flex-wrap: wrap;\n  gap: 1rem;\n}\n.page-title[_ngcontent-%COMP%] {\n  display: flex;\n  align-items: center;\n  gap: 0.75rem;\n  font-size: 2rem;\n  font-weight: 600;\n  color: #1a202c;\n  margin: 0;\n}\n.header-actions[_ngcontent-%COMP%] {\n  display: flex;\n  gap: 1rem;\n}\n.filters-section[_ngcontent-%COMP%] {\n  background: white;\n  border-radius: 12px;\n  padding: 1.5rem;\n  margin-bottom: 2rem;\n  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);\n  display: flex;\n  gap: 1rem;\n  flex-wrap: wrap;\n  align-items: center;\n}\n.search-box[_ngcontent-%COMP%] {\n  position: relative;\n  flex: 1;\n  min-width: 300px;\n}\n.search-box[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\n  position: absolute;\n  left: 1rem;\n  top: 50%;\n  transform: translateY(-50%);\n  color: #64748b;\n}\n.search-input[_ngcontent-%COMP%] {\n  width: 100%;\n  padding: 0.75rem 1rem 0.75rem 2.5rem;\n  border: 2px solid #e2e8f0;\n  border-radius: 8px;\n  font-size: 1rem;\n  transition: border-color 0.2s;\n}\n.search-input[_ngcontent-%COMP%]:focus {\n  outline: none;\n  border-color: #3b82f6;\n}\n.filters[_ngcontent-%COMP%] {\n  display: flex;\n  gap: 1rem;\n}\n.filter-select[_ngcontent-%COMP%] {\n  padding: 0.75rem 1rem;\n  border: 2px solid #e2e8f0;\n  border-radius: 8px;\n  font-size: 1rem;\n  background: white;\n  min-width: 150px;\n}\n.error-message[_ngcontent-%COMP%] {\n  background: #fef2f2;\n  border: 1px solid #fecaca;\n  color: #dc2626;\n  padding: 1rem;\n  border-radius: 8px;\n  margin-bottom: 2rem;\n  display: flex;\n  align-items: center;\n  gap: 0.5rem;\n}\n.loading-container[_ngcontent-%COMP%] {\n  text-align: center;\n  padding: 3rem;\n}\n.loading-spinner[_ngcontent-%COMP%] {\n  width: 40px;\n  height: 40px;\n  border: 4px solid #e2e8f0;\n  border-top: 4px solid #3b82f6;\n  border-radius: 50%;\n  animation: _ngcontent-%COMP%_spin 1s linear infinite;\n  margin: 0 auto 1rem;\n}\n@keyframes _ngcontent-%COMP%_spin {\n  0% {\n    transform: rotate(0deg);\n  }\n  100% {\n    transform: rotate(360deg);\n  }\n}\n.table-container[_ngcontent-%COMP%] {\n  background: white;\n  border-radius: 12px;\n  overflow: hidden;\n  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);\n  margin-bottom: 2rem;\n}\n.users-table[_ngcontent-%COMP%] {\n  width: 100%;\n  border-collapse: collapse;\n}\n.users-table[_ngcontent-%COMP%]   th[_ngcontent-%COMP%] {\n  background: #f8fafc;\n  padding: 1rem;\n  text-align: left;\n  font-weight: 600;\n  color: #374151;\n  border-bottom: 1px solid #e5e7eb;\n}\n.user-row[_ngcontent-%COMP%] {\n  border-bottom: 1px solid #f3f4f6;\n  transition: background-color 0.2s;\n}\n.user-row[_ngcontent-%COMP%]:hover {\n  background: #f9fafb;\n}\n.users-table[_ngcontent-%COMP%]   td[_ngcontent-%COMP%] {\n  padding: 1rem;\n  vertical-align: middle;\n}\n.user-info[_ngcontent-%COMP%] {\n  display: flex;\n  align-items: center;\n  gap: 1rem;\n}\n.user-avatar[_ngcontent-%COMP%] {\n  width: 40px;\n  height: 40px;\n  border-radius: 50%;\n  background:\n    linear-gradient(\n      135deg,\n      #667eea 0%,\n      #764ba2 100%);\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  color: white;\n  font-weight: 600;\n  font-size: 0.875rem;\n}\n.user-details[_ngcontent-%COMP%] {\n  display: flex;\n  flex-direction: column;\n}\n.user-name[_ngcontent-%COMP%] {\n  font-weight: 600;\n  color: #1a202c;\n}\n.user-id[_ngcontent-%COMP%] {\n  font-size: 0.875rem;\n  color: #64748b;\n}\n.user-email[_ngcontent-%COMP%] {\n  color: #4f46e5;\n  font-family: monospace;\n}\n.role-badge[_ngcontent-%COMP%], \n.status-badge[_ngcontent-%COMP%] {\n  padding: 0.25rem 0.75rem;\n  border-radius: 9999px;\n  font-size: 0.875rem;\n  font-weight: 500;\n}\n.role-admin[_ngcontent-%COMP%] {\n  background: #fef3c7;\n  color: #d97706;\n}\n.role-fournisseur[_ngcontent-%COMP%] {\n  background: #dbeafe;\n  color: #2563eb;\n}\n.role-client[_ngcontent-%COMP%] {\n  background: #d1fae5;\n  color: #059669;\n}\n.role-default[_ngcontent-%COMP%] {\n  background: #f3f4f6;\n  color: #6b7280;\n}\n.status-active[_ngcontent-%COMP%] {\n  background: #d1fae5;\n  color: #059669;\n}\n.status-inactive[_ngcontent-%COMP%] {\n  background: #fee2e2;\n  color: #dc2626;\n}\n.date-cell[_ngcontent-%COMP%] {\n  color: #64748b;\n  font-size: 0.875rem;\n}\n.no-connection[_ngcontent-%COMP%] {\n  color: #9ca3af;\n  font-style: italic;\n}\n.actions-cell[_ngcontent-%COMP%] {\n  width: 120px;\n}\n.action-buttons[_ngcontent-%COMP%] {\n  display: flex;\n  gap: 0.5rem;\n}\n.btn-action[_ngcontent-%COMP%] {\n  width: 36px;\n  height: 36px;\n  border: none;\n  border-radius: 6px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  cursor: pointer;\n  transition: all 0.2s;\n}\n.btn-activate[_ngcontent-%COMP%] {\n  background: #d1fae5;\n  color: #059669;\n}\n.btn-activate[_ngcontent-%COMP%]:hover {\n  background: #a7f3d0;\n}\n.btn-deactivate[_ngcontent-%COMP%] {\n  background: #fef3c7;\n  color: #d97706;\n}\n.btn-deactivate[_ngcontent-%COMP%]:hover {\n  background: #fde68a;\n}\n.btn-delete[_ngcontent-%COMP%] {\n  background: #fee2e2;\n  color: #dc2626;\n}\n.btn-delete[_ngcontent-%COMP%]:hover {\n  background: #fecaca;\n}\n.btn[_ngcontent-%COMP%] {\n  padding: 0.75rem 1.5rem;\n  border: none;\n  border-radius: 8px;\n  font-weight: 500;\n  cursor: pointer;\n  transition: all 0.2s;\n  display: flex;\n  align-items: center;\n  gap: 0.5rem;\n}\n.btn-primary[_ngcontent-%COMP%] {\n  background: #3b82f6;\n  color: white;\n}\n.btn-primary[_ngcontent-%COMP%]:hover {\n  background: #2563eb;\n}\n.btn-secondary[_ngcontent-%COMP%] {\n  background: #f3f4f6;\n  color: #374151;\n}\n.btn-secondary[_ngcontent-%COMP%]:hover {\n  background: #e5e7eb;\n}\n.btn[_ngcontent-%COMP%]:disabled {\n  opacity: 0.5;\n  cursor: not-allowed;\n}\n.no-data[_ngcontent-%COMP%] {\n  text-align: center;\n  padding: 3rem;\n  color: #64748b;\n}\n.no-data[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\n  font-size: 3rem;\n  margin-bottom: 1rem;\n  opacity: 0.5;\n}\n.no-data[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%] {\n  margin: 0 0 0.5rem 0;\n  color: #374151;\n}\n.pagination-container[_ngcontent-%COMP%] {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  padding: 1rem;\n  background: white;\n  border-radius: 8px;\n  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);\n}\n.pagination-info[_ngcontent-%COMP%] {\n  color: #64748b;\n  font-size: 0.875rem;\n}\n.pagination[_ngcontent-%COMP%] {\n  display: flex;\n  align-items: center;\n  gap: 1rem;\n}\n.btn-page[_ngcontent-%COMP%] {\n  width: 36px;\n  height: 36px;\n  border: 1px solid #e5e7eb;\n  background: white;\n  border-radius: 6px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  cursor: pointer;\n  transition: all 0.2s;\n}\n.btn-page[_ngcontent-%COMP%]:hover:not(:disabled) {\n  background: #f3f4f6;\n}\n.btn-page[_ngcontent-%COMP%]:disabled {\n  opacity: 0.5;\n  cursor: not-allowed;\n}\n.page-info[_ngcontent-%COMP%] {\n  font-weight: 500;\n  color: #374151;\n}\n.icon-users[_ngcontent-%COMP%]::before {\n  content: "\\1f465";\n}\n.icon-refresh[_ngcontent-%COMP%]::before {\n  content: "\\1f504";\n}\n.icon-download[_ngcontent-%COMP%]::before {\n  content: "\\1f4e5";\n}\n.icon-search[_ngcontent-%COMP%]::before {\n  content: "\\1f50d";\n}\n.icon-alert[_ngcontent-%COMP%]::before {\n  content: "\\26a0\\fe0f";\n}\n.icon-play[_ngcontent-%COMP%]::before {\n  content: "\\25b6\\fe0f";\n}\n.icon-pause[_ngcontent-%COMP%]::before {\n  content: "\\23f8\\fe0f";\n}\n.icon-trash[_ngcontent-%COMP%]::before {\n  content: "\\1f5d1\\fe0f";\n}\n.icon-chevron-left[_ngcontent-%COMP%]::before {\n  content: "\\25c0\\fe0f";\n}\n.icon-chevron-right[_ngcontent-%COMP%]::before {\n  content: "\\25b6\\fe0f";\n}\n.icon-filter-x[_ngcontent-%COMP%]::before {\n  content: "\\1f6ab";\n}\n.icon-test[_ngcontent-%COMP%]::before {\n  content: "\\1f9ea";\n}\n@media (max-width: 768px) {\n  .user-management-container[_ngcontent-%COMP%] {\n    padding: 1rem;\n  }\n  .header-content[_ngcontent-%COMP%] {\n    flex-direction: column;\n    align-items: stretch;\n  }\n  .filters-section[_ngcontent-%COMP%] {\n    flex-direction: column;\n  }\n  .search-box[_ngcontent-%COMP%] {\n    min-width: auto;\n  }\n  .users-table[_ngcontent-%COMP%] {\n    font-size: 0.875rem;\n  }\n  .users-table[_ngcontent-%COMP%]   th[_ngcontent-%COMP%], \n   .users-table[_ngcontent-%COMP%]   td[_ngcontent-%COMP%] {\n    padding: 0.75rem 0.5rem;\n  }\n  .pagination-container[_ngcontent-%COMP%] {\n    flex-direction: column;\n    gap: 1rem;\n  }\n}\n/*# sourceMappingURL=user-management.component.css.map */'] });
};
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && setClassMetadata(UserManagementComponent, [{
    type: Component,
    args: [{ selector: "app-user-management", standalone: true, imports: [CommonModule, FormsModule], template: `<div class="user-management-container">
  <!-- Header -->
  <div class="page-header">
    <div class="header-content">
      <h1 class="page-title">
        <i class="icon-users"></i>
        Gestion des Utilisateurs
      </h1>
      <div class="header-actions">
        <button class="btn btn-secondary" (click)="refresh()" [disabled]="isLoading()">
          <i class="icon-refresh"></i>
          Actualiser
        </button>
        <button class="btn btn-primary" (click)="exportUsers()">
          <i class="icon-download"></i>
          Exporter
        </button>

      </div>
    </div>
  </div>

  <!-- Filtres et Recherche -->
  <div class="filters-section">
    <div class="search-box">
      <i class="icon-search"></i>
      <input 
        type="text" 
        placeholder="Rechercher par nom, pr\xE9nom ou email..."
        [(ngModel)]="searchTerm"
        (input)="onSearch()"
        class="search-input">
    </div>

    <div class="filters">
      <select [(ngModel)]="selectedRole" (change)="onFilterChange()" class="filter-select">
        <option value="">Tous les r\xF4les</option>
        <option *ngFor="let role of roles" [value]="role">{{ role }}</option>
      </select>

      <select [(ngModel)]="selectedStatus" (change)="onFilterChange()" class="filter-select">
        <option value="">Tous les statuts</option>
        <option value="actif">Actifs</option>
        <option value="inactif">Inactifs</option>
      </select>
    </div>
  </div>

  <!-- Message d'erreur -->
  <div *ngIf="error()" class="error-message">
    <i class="icon-alert"></i>
    {{ error() }}
  </div>

  <!-- Loading -->
   
  <div *ngIf="isLoading()" class="loading-container">
    <div class="loading-spinner"></div>
    <p>Chargement des utilisateurs...</p>
  </div>

  <!-- Debug Info -->
  <div *ngIf="!isLoading() && !error()" class="debug-info" style="background: #f0f9ff; padding: 1rem; margin: 1rem 0; border-radius: 8px; border: 1px solid #0ea5e9;">
    <h4 style="margin: 0 0 0.5rem 0; color: #0369a1;">\u{1F50D}Info General</h4>
    <p><strong>Nombre d'utilisateurs:</strong> {{ utilisateurs().length }}</p>
    <p><strong>Total items:</strong> {{ totalItems() }}</p>
    <p><strong>Utilisateurs affich\xE9s:</strong> {{ utilisateurs().length }}</p>

  </div>

  <!-- Tableau des utilisateurs -->
  <div *ngIf="!isLoading() && !error()" class="table-container">
    <table class="users-table">
      <thead>
        <tr>
          <th>Utilisateur</th>
          <th>Email</th>
          <th>R\xF4le</th>
          <th>Statut</th>
          <th>Date cr\xE9ation</th>
          <th>Derni\xE8re connexion</th>
          <th>Actions</th>
        </tr>
      </thead>
      <tbody>
        <tr *ngFor="let user of utilisateurs()" class="user-row">
          <td class="user-info">
            <div class="user-avatar">
              {{ getInitials(user.nomComplet) }}
            </div>
            <div class="user-details">
              <div class="user-name">{{ user.nomComplet }}</div>
              <div class="user-id">ID: {{ user.id }}</div>
            </div>
          </td>
          
          <td class="user-email">{{ user.email }}</td>
          
          <td>
            <span class="role-badge" [ngClass]="getRoleClass(user.role)">
              {{ user.role }}
            </span>
          </td>
          
          <td>
            <span class="status-badge" [ngClass]="getStatusClass(user)">
              {{ getStatusText(user) }}
            </span>
          </td>
          
          <td class="date-cell">{{ formatDate(user.dateInscription) }}</td>
          
          <td class="date-cell">
            <span *ngIf="user.derniereConnexion; else noConnection">
              {{ formatDate(user.derniereConnexion) }}
            </span>
            <ng-template #noConnection>
              <span class="no-connection">Jamais connect\xE9</span>
            </ng-template>
          </td>
          
          <td class="actions-cell">
            <div class="action-buttons">
              <button 
                class="btn-action btn-toggle"
                [class.btn-activate]="!user.estActif"
                [class.btn-deactivate]="user.estActif"
                (click)="toggleUtilisateur(user)"
                [title]="user.estActif ? 'D\xE9sactiver' : 'Activer'">
                <i [class]="user.estActif ? 'icon-pause' : 'icon-play'"></i>
              </button>
              
              <button 
                class="btn-action btn-delete"
                (click)="deleteUtilisateur(user)"
                title="Supprimer">
                <i class="icon-trash"></i>
              </button>
            </div>
          </td>
        </tr>
      </tbody>
    </table>

    <!-- Message si aucun utilisateur -->
    <div *ngIf="utilisateurs().length === 0" class="no-data">
      <i class="icon-users"></i>
      <h3>Aucun utilisateur trouv\xE9</h3>
      <p>Aucun utilisateur ne correspond aux crit\xE8res de recherche.</p>
    </div>
  </div>

  <!-- Pagination -->
  <div *ngIf="totalPages() > 1" class="pagination-container">
    <div class="pagination-info">
      Affichage de {{ (currentPage() - 1) * pageSize() + 1 }} \xE0 
      {{ Math.min(currentPage() * pageSize(), totalItems()) }} sur {{ totalItems() }} utilisateurs
    </div>
    
    <div class="pagination">
      <button 
        class="btn-page"
        [disabled]="currentPage() === 1"
        (click)="onPageChange(currentPage() - 1)">
        <i class="icon-chevron-left"></i>
      </button>
      
      <span class="page-info">
        Page {{ currentPage() }} sur {{ totalPages() }}
      </span>
      
      <button 
        class="btn-page"
        [disabled]="currentPage() === totalPages()"
        (click)="onPageChange(currentPage() + 1)">
        <i class="icon-chevron-right"></i>
      </button>
    </div>
  </div>
</div>
`, styles: ['/* src/app/components/admin/user-management/user-management.component.css */\n.user-management-container {\n  padding: 2rem;\n  max-width: 1400px;\n  margin: 0 auto;\n}\n.page-header {\n  margin-bottom: 2rem;\n}\n.header-content {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  flex-wrap: wrap;\n  gap: 1rem;\n}\n.page-title {\n  display: flex;\n  align-items: center;\n  gap: 0.75rem;\n  font-size: 2rem;\n  font-weight: 600;\n  color: #1a202c;\n  margin: 0;\n}\n.header-actions {\n  display: flex;\n  gap: 1rem;\n}\n.filters-section {\n  background: white;\n  border-radius: 12px;\n  padding: 1.5rem;\n  margin-bottom: 2rem;\n  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);\n  display: flex;\n  gap: 1rem;\n  flex-wrap: wrap;\n  align-items: center;\n}\n.search-box {\n  position: relative;\n  flex: 1;\n  min-width: 300px;\n}\n.search-box i {\n  position: absolute;\n  left: 1rem;\n  top: 50%;\n  transform: translateY(-50%);\n  color: #64748b;\n}\n.search-input {\n  width: 100%;\n  padding: 0.75rem 1rem 0.75rem 2.5rem;\n  border: 2px solid #e2e8f0;\n  border-radius: 8px;\n  font-size: 1rem;\n  transition: border-color 0.2s;\n}\n.search-input:focus {\n  outline: none;\n  border-color: #3b82f6;\n}\n.filters {\n  display: flex;\n  gap: 1rem;\n}\n.filter-select {\n  padding: 0.75rem 1rem;\n  border: 2px solid #e2e8f0;\n  border-radius: 8px;\n  font-size: 1rem;\n  background: white;\n  min-width: 150px;\n}\n.error-message {\n  background: #fef2f2;\n  border: 1px solid #fecaca;\n  color: #dc2626;\n  padding: 1rem;\n  border-radius: 8px;\n  margin-bottom: 2rem;\n  display: flex;\n  align-items: center;\n  gap: 0.5rem;\n}\n.loading-container {\n  text-align: center;\n  padding: 3rem;\n}\n.loading-spinner {\n  width: 40px;\n  height: 40px;\n  border: 4px solid #e2e8f0;\n  border-top: 4px solid #3b82f6;\n  border-radius: 50%;\n  animation: spin 1s linear infinite;\n  margin: 0 auto 1rem;\n}\n@keyframes spin {\n  0% {\n    transform: rotate(0deg);\n  }\n  100% {\n    transform: rotate(360deg);\n  }\n}\n.table-container {\n  background: white;\n  border-radius: 12px;\n  overflow: hidden;\n  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);\n  margin-bottom: 2rem;\n}\n.users-table {\n  width: 100%;\n  border-collapse: collapse;\n}\n.users-table th {\n  background: #f8fafc;\n  padding: 1rem;\n  text-align: left;\n  font-weight: 600;\n  color: #374151;\n  border-bottom: 1px solid #e5e7eb;\n}\n.user-row {\n  border-bottom: 1px solid #f3f4f6;\n  transition: background-color 0.2s;\n}\n.user-row:hover {\n  background: #f9fafb;\n}\n.users-table td {\n  padding: 1rem;\n  vertical-align: middle;\n}\n.user-info {\n  display: flex;\n  align-items: center;\n  gap: 1rem;\n}\n.user-avatar {\n  width: 40px;\n  height: 40px;\n  border-radius: 50%;\n  background:\n    linear-gradient(\n      135deg,\n      #667eea 0%,\n      #764ba2 100%);\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  color: white;\n  font-weight: 600;\n  font-size: 0.875rem;\n}\n.user-details {\n  display: flex;\n  flex-direction: column;\n}\n.user-name {\n  font-weight: 600;\n  color: #1a202c;\n}\n.user-id {\n  font-size: 0.875rem;\n  color: #64748b;\n}\n.user-email {\n  color: #4f46e5;\n  font-family: monospace;\n}\n.role-badge,\n.status-badge {\n  padding: 0.25rem 0.75rem;\n  border-radius: 9999px;\n  font-size: 0.875rem;\n  font-weight: 500;\n}\n.role-admin {\n  background: #fef3c7;\n  color: #d97706;\n}\n.role-fournisseur {\n  background: #dbeafe;\n  color: #2563eb;\n}\n.role-client {\n  background: #d1fae5;\n  color: #059669;\n}\n.role-default {\n  background: #f3f4f6;\n  color: #6b7280;\n}\n.status-active {\n  background: #d1fae5;\n  color: #059669;\n}\n.status-inactive {\n  background: #fee2e2;\n  color: #dc2626;\n}\n.date-cell {\n  color: #64748b;\n  font-size: 0.875rem;\n}\n.no-connection {\n  color: #9ca3af;\n  font-style: italic;\n}\n.actions-cell {\n  width: 120px;\n}\n.action-buttons {\n  display: flex;\n  gap: 0.5rem;\n}\n.btn-action {\n  width: 36px;\n  height: 36px;\n  border: none;\n  border-radius: 6px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  cursor: pointer;\n  transition: all 0.2s;\n}\n.btn-activate {\n  background: #d1fae5;\n  color: #059669;\n}\n.btn-activate:hover {\n  background: #a7f3d0;\n}\n.btn-deactivate {\n  background: #fef3c7;\n  color: #d97706;\n}\n.btn-deactivate:hover {\n  background: #fde68a;\n}\n.btn-delete {\n  background: #fee2e2;\n  color: #dc2626;\n}\n.btn-delete:hover {\n  background: #fecaca;\n}\n.btn {\n  padding: 0.75rem 1.5rem;\n  border: none;\n  border-radius: 8px;\n  font-weight: 500;\n  cursor: pointer;\n  transition: all 0.2s;\n  display: flex;\n  align-items: center;\n  gap: 0.5rem;\n}\n.btn-primary {\n  background: #3b82f6;\n  color: white;\n}\n.btn-primary:hover {\n  background: #2563eb;\n}\n.btn-secondary {\n  background: #f3f4f6;\n  color: #374151;\n}\n.btn-secondary:hover {\n  background: #e5e7eb;\n}\n.btn:disabled {\n  opacity: 0.5;\n  cursor: not-allowed;\n}\n.no-data {\n  text-align: center;\n  padding: 3rem;\n  color: #64748b;\n}\n.no-data i {\n  font-size: 3rem;\n  margin-bottom: 1rem;\n  opacity: 0.5;\n}\n.no-data h3 {\n  margin: 0 0 0.5rem 0;\n  color: #374151;\n}\n.pagination-container {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  padding: 1rem;\n  background: white;\n  border-radius: 8px;\n  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);\n}\n.pagination-info {\n  color: #64748b;\n  font-size: 0.875rem;\n}\n.pagination {\n  display: flex;\n  align-items: center;\n  gap: 1rem;\n}\n.btn-page {\n  width: 36px;\n  height: 36px;\n  border: 1px solid #e5e7eb;\n  background: white;\n  border-radius: 6px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  cursor: pointer;\n  transition: all 0.2s;\n}\n.btn-page:hover:not(:disabled) {\n  background: #f3f4f6;\n}\n.btn-page:disabled {\n  opacity: 0.5;\n  cursor: not-allowed;\n}\n.page-info {\n  font-weight: 500;\n  color: #374151;\n}\n.icon-users::before {\n  content: "\\1f465";\n}\n.icon-refresh::before {\n  content: "\\1f504";\n}\n.icon-download::before {\n  content: "\\1f4e5";\n}\n.icon-search::before {\n  content: "\\1f50d";\n}\n.icon-alert::before {\n  content: "\\26a0\\fe0f";\n}\n.icon-play::before {\n  content: "\\25b6\\fe0f";\n}\n.icon-pause::before {\n  content: "\\23f8\\fe0f";\n}\n.icon-trash::before {\n  content: "\\1f5d1\\fe0f";\n}\n.icon-chevron-left::before {\n  content: "\\25c0\\fe0f";\n}\n.icon-chevron-right::before {\n  content: "\\25b6\\fe0f";\n}\n.icon-filter-x::before {\n  content: "\\1f6ab";\n}\n.icon-test::before {\n  content: "\\1f9ea";\n}\n@media (max-width: 768px) {\n  .user-management-container {\n    padding: 1rem;\n  }\n  .header-content {\n    flex-direction: column;\n    align-items: stretch;\n  }\n  .filters-section {\n    flex-direction: column;\n  }\n  .search-box {\n    min-width: auto;\n  }\n  .users-table {\n    font-size: 0.875rem;\n  }\n  .users-table th,\n  .users-table td {\n    padding: 0.75rem 0.5rem;\n  }\n  .pagination-container {\n    flex-direction: column;\n    gap: 1rem;\n  }\n}\n/*# sourceMappingURL=user-management.component.css.map */\n'] }]
  }], () => [{ type: AdminService }], null);
})();
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && \u0275setClassDebugInfo(UserManagementComponent, { className: "UserManagementComponent", filePath: "src/app/components/admin/user-management/user-management.component.ts", lineNumber: 13 });
})();
export {
  UserManagementComponent
};
//# sourceMappingURL=chunk-VJGGIZOP.js.map

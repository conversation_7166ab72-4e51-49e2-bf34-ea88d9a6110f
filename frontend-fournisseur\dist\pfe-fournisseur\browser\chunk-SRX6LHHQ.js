import {
  SousCategorieService
} from "./chunk-VGHANLZK.js";
import {
  CategorieService
} from "./chunk-LBLEENAN.js";
import {
  DefaultValueAccessor,
  FormBuilder,
  FormControlName,
  FormGroupDirective,
  FormsModule,
  NgControlStatus,
  NgControlStatusGroup,
  NgSelectOption,
  ReactiveFormsModule,
  SelectControlValueAccessor,
  Validators,
  ɵNgNoValidate,
  ɵNgSelectMultipleOption
} from "./chunk-HQBVYEOO.js";
import {
  AdminService
} from "./chunk-EFJVWLOV.js";
import "./chunk-GFHHPDQ6.js";
import "./chunk-7JDDWGD3.js";
import {
  CommonModule,
  Component,
  NgClass,
  NgForOf,
  NgIf,
  __spreadProps,
  __spreadValues,
  setClassMetadata,
  signal,
  ɵsetClassDebugInfo,
  ɵɵadvance,
  ɵɵclassProp,
  ɵɵdefineComponent,
  ɵɵdirectiveInject,
  ɵɵelement,
  ɵɵelementEnd,
  ɵɵelementStart,
  ɵɵgetCurrentView,
  ɵɵlistener,
  ɵɵnextContext,
  ɵɵproperty,
  ɵɵreference,
  ɵɵresetView,
  ɵɵrestoreView,
  ɵɵtemplate,
  ɵɵtemplateRefExtractor,
  ɵɵtext,
  ɵɵtextInterpolate,
  ɵɵtextInterpolate1
} from "./chunk-UBZQS7JS.js";

// src/app/components/admin/category-management/category-management.component.ts
function CategoryManagementComponent_div_18_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "div", 17);
    \u0275\u0275element(1, "i", 18);
    \u0275\u0275text(2);
    \u0275\u0275elementEnd();
  }
  if (rf & 2) {
    const ctx_r0 = \u0275\u0275nextContext();
    \u0275\u0275advance(2);
    \u0275\u0275textInterpolate1(" ", ctx_r0.error(), " ");
  }
}
function CategoryManagementComponent_div_19_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "div", 19);
    \u0275\u0275element(1, "div", 20);
    \u0275\u0275elementStart(2, "p");
    \u0275\u0275text(3, "Chargement...");
    \u0275\u0275elementEnd()();
  }
}
function CategoryManagementComponent_div_20_tr_24_span_7_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "span");
    \u0275\u0275text(1);
    \u0275\u0275elementEnd();
  }
  if (rf & 2) {
    const category_r4 = \u0275\u0275nextContext().$implicit;
    \u0275\u0275advance();
    \u0275\u0275textInterpolate1(" ", category_r4.description, " ");
  }
}
function CategoryManagementComponent_div_20_tr_24_ng_template_8_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "span", 46);
    \u0275\u0275text(1, "Aucune description");
    \u0275\u0275elementEnd();
  }
}
function CategoryManagementComponent_div_20_tr_24_button_21_Template(rf, ctx) {
  if (rf & 1) {
    const _r5 = \u0275\u0275getCurrentView();
    \u0275\u0275elementStart(0, "button", 47);
    \u0275\u0275listener("click", function CategoryManagementComponent_div_20_tr_24_button_21_Template_button_click_0_listener() {
      \u0275\u0275restoreView(_r5);
      const category_r4 = \u0275\u0275nextContext().$implicit;
      const ctx_r0 = \u0275\u0275nextContext(2);
      return \u0275\u0275resetView(ctx_r0.validateCategory(category_r4));
    });
    \u0275\u0275element(1, "i", 48);
    \u0275\u0275elementEnd();
  }
}
function CategoryManagementComponent_div_20_tr_24_button_22_Template(rf, ctx) {
  if (rf & 1) {
    const _r6 = \u0275\u0275getCurrentView();
    \u0275\u0275elementStart(0, "button", 49);
    \u0275\u0275listener("click", function CategoryManagementComponent_div_20_tr_24_button_22_Template_button_click_0_listener() {
      \u0275\u0275restoreView(_r6);
      const category_r4 = \u0275\u0275nextContext().$implicit;
      const ctx_r0 = \u0275\u0275nextContext(2);
      return \u0275\u0275resetView(ctx_r0.rejectCategory(category_r4));
    });
    \u0275\u0275element(1, "i", 50);
    \u0275\u0275elementEnd();
  }
}
function CategoryManagementComponent_div_20_tr_24_Template(rf, ctx) {
  if (rf & 1) {
    const _r3 = \u0275\u0275getCurrentView();
    \u0275\u0275elementStart(0, "tr", 29)(1, "td", 30)(2, "div", 31);
    \u0275\u0275text(3);
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(4, "div", 32);
    \u0275\u0275text(5);
    \u0275\u0275elementEnd()();
    \u0275\u0275elementStart(6, "td", 33);
    \u0275\u0275template(7, CategoryManagementComponent_div_20_tr_24_span_7_Template, 2, 1, "span", 34)(8, CategoryManagementComponent_div_20_tr_24_ng_template_8_Template, 2, 0, "ng-template", null, 0, \u0275\u0275templateRefExtractor);
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(10, "td")(11, "span", 35);
    \u0275\u0275text(12);
    \u0275\u0275elementEnd()();
    \u0275\u0275elementStart(13, "td", 36);
    \u0275\u0275text(14);
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(15, "td", 37);
    \u0275\u0275text(16);
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(17, "td", 38)(18, "div", 39)(19, "button", 40);
    \u0275\u0275listener("click", function CategoryManagementComponent_div_20_tr_24_Template_button_click_19_listener() {
      const category_r4 = \u0275\u0275restoreView(_r3).$implicit;
      const ctx_r0 = \u0275\u0275nextContext(2);
      return \u0275\u0275resetView(ctx_r0.openCategoryModal(category_r4));
    });
    \u0275\u0275element(20, "i", 41);
    \u0275\u0275elementEnd();
    \u0275\u0275template(21, CategoryManagementComponent_div_20_tr_24_button_21_Template, 2, 0, "button", 42)(22, CategoryManagementComponent_div_20_tr_24_button_22_Template, 2, 0, "button", 43);
    \u0275\u0275elementStart(23, "button", 44);
    \u0275\u0275listener("click", function CategoryManagementComponent_div_20_tr_24_Template_button_click_23_listener() {
      const category_r4 = \u0275\u0275restoreView(_r3).$implicit;
      const ctx_r0 = \u0275\u0275nextContext(2);
      return \u0275\u0275resetView(ctx_r0.deleteCategory(category_r4));
    });
    \u0275\u0275element(24, "i", 45);
    \u0275\u0275elementEnd()()()();
  }
  if (rf & 2) {
    const category_r4 = ctx.$implicit;
    const noDescription_r7 = \u0275\u0275reference(9);
    const ctx_r0 = \u0275\u0275nextContext(2);
    \u0275\u0275advance(3);
    \u0275\u0275textInterpolate(category_r4.nom);
    \u0275\u0275advance(2);
    \u0275\u0275textInterpolate1("ID: ", category_r4.id, "");
    \u0275\u0275advance(2);
    \u0275\u0275property("ngIf", category_r4.description)("ngIfElse", noDescription_r7);
    \u0275\u0275advance(4);
    \u0275\u0275property("ngClass", ctx_r0.getStatusClass(category_r4.estValide));
    \u0275\u0275advance();
    \u0275\u0275textInterpolate1(" ", ctx_r0.getStatusText(category_r4.estValide), " ");
    \u0275\u0275advance(2);
    \u0275\u0275textInterpolate(category_r4.nombreProduits);
    \u0275\u0275advance(2);
    \u0275\u0275textInterpolate(ctx_r0.formatDate(category_r4.dateCreation));
    \u0275\u0275advance(5);
    \u0275\u0275property("ngIf", !category_r4.estValide);
    \u0275\u0275advance();
    \u0275\u0275property("ngIf", category_r4.estValide);
  }
}
function CategoryManagementComponent_div_20_div_25_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "div", 51);
    \u0275\u0275element(1, "i", 5);
    \u0275\u0275elementStart(2, "h3");
    \u0275\u0275text(3, "Aucune cat\xE9gorie");
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(4, "p");
    \u0275\u0275text(5, "Aucune cat\xE9gorie n'a \xE9t\xE9 cr\xE9\xE9e pour le moment.");
    \u0275\u0275elementEnd()();
  }
}
function CategoryManagementComponent_div_20_Template(rf, ctx) {
  if (rf & 1) {
    const _r2 = \u0275\u0275getCurrentView();
    \u0275\u0275elementStart(0, "div", 21)(1, "div", 22)(2, "h2");
    \u0275\u0275text(3, "Cat\xE9gories");
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(4, "button", 23);
    \u0275\u0275listener("click", function CategoryManagementComponent_div_20_Template_button_click_4_listener() {
      \u0275\u0275restoreView(_r2);
      const ctx_r0 = \u0275\u0275nextContext();
      return \u0275\u0275resetView(ctx_r0.openCategoryModal());
    });
    \u0275\u0275element(5, "i", 24);
    \u0275\u0275text(6, " Nouvelle cat\xE9gorie ");
    \u0275\u0275elementEnd()();
    \u0275\u0275elementStart(7, "div", 25)(8, "table", 26)(9, "thead")(10, "tr")(11, "th");
    \u0275\u0275text(12, "Nom");
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(13, "th");
    \u0275\u0275text(14, "Description");
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(15, "th");
    \u0275\u0275text(16, "Statut");
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(17, "th");
    \u0275\u0275text(18, "Produits");
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(19, "th");
    \u0275\u0275text(20, "Date cr\xE9ation");
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(21, "th");
    \u0275\u0275text(22, "Actions");
    \u0275\u0275elementEnd()()();
    \u0275\u0275elementStart(23, "tbody");
    \u0275\u0275template(24, CategoryManagementComponent_div_20_tr_24_Template, 25, 10, "tr", 27);
    \u0275\u0275elementEnd()();
    \u0275\u0275template(25, CategoryManagementComponent_div_20_div_25_Template, 6, 0, "div", 28);
    \u0275\u0275elementEnd()();
  }
  if (rf & 2) {
    const ctx_r0 = \u0275\u0275nextContext();
    \u0275\u0275advance(24);
    \u0275\u0275property("ngForOf", ctx_r0.categories());
    \u0275\u0275advance();
    \u0275\u0275property("ngIf", ctx_r0.categories().length === 0);
  }
}
function CategoryManagementComponent_div_21_tr_26_span_9_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "span");
    \u0275\u0275text(1);
    \u0275\u0275elementEnd();
  }
  if (rf & 2) {
    const sousCategory_r10 = \u0275\u0275nextContext().$implicit;
    \u0275\u0275advance();
    \u0275\u0275textInterpolate1(" ", sousCategory_r10.description, " ");
  }
}
function CategoryManagementComponent_div_21_tr_26_ng_template_10_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "span", 46);
    \u0275\u0275text(1, "Aucune description");
    \u0275\u0275elementEnd();
  }
}
function CategoryManagementComponent_div_21_tr_26_button_23_Template(rf, ctx) {
  if (rf & 1) {
    const _r11 = \u0275\u0275getCurrentView();
    \u0275\u0275elementStart(0, "button", 47);
    \u0275\u0275listener("click", function CategoryManagementComponent_div_21_tr_26_button_23_Template_button_click_0_listener() {
      \u0275\u0275restoreView(_r11);
      const sousCategory_r10 = \u0275\u0275nextContext().$implicit;
      const ctx_r0 = \u0275\u0275nextContext(2);
      return \u0275\u0275resetView(ctx_r0.validateSubCategory(sousCategory_r10));
    });
    \u0275\u0275element(1, "i", 48);
    \u0275\u0275elementEnd();
  }
}
function CategoryManagementComponent_div_21_tr_26_button_24_Template(rf, ctx) {
  if (rf & 1) {
    const _r12 = \u0275\u0275getCurrentView();
    \u0275\u0275elementStart(0, "button", 49);
    \u0275\u0275listener("click", function CategoryManagementComponent_div_21_tr_26_button_24_Template_button_click_0_listener() {
      \u0275\u0275restoreView(_r12);
      const sousCategory_r10 = \u0275\u0275nextContext().$implicit;
      const ctx_r0 = \u0275\u0275nextContext(2);
      return \u0275\u0275resetView(ctx_r0.rejectSubCategory(sousCategory_r10));
    });
    \u0275\u0275element(1, "i", 50);
    \u0275\u0275elementEnd();
  }
}
function CategoryManagementComponent_div_21_tr_26_Template(rf, ctx) {
  if (rf & 1) {
    const _r9 = \u0275\u0275getCurrentView();
    \u0275\u0275elementStart(0, "tr", 29)(1, "td", 30)(2, "div", 31);
    \u0275\u0275text(3);
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(4, "div", 32);
    \u0275\u0275text(5);
    \u0275\u0275elementEnd()();
    \u0275\u0275elementStart(6, "td", 52);
    \u0275\u0275text(7);
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(8, "td", 33);
    \u0275\u0275template(9, CategoryManagementComponent_div_21_tr_26_span_9_Template, 2, 1, "span", 34)(10, CategoryManagementComponent_div_21_tr_26_ng_template_10_Template, 2, 0, "ng-template", null, 0, \u0275\u0275templateRefExtractor);
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(12, "td")(13, "span", 35);
    \u0275\u0275text(14);
    \u0275\u0275elementEnd()();
    \u0275\u0275elementStart(15, "td", 36);
    \u0275\u0275text(16);
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(17, "td", 37);
    \u0275\u0275text(18);
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(19, "td", 38)(20, "div", 39)(21, "button", 40);
    \u0275\u0275listener("click", function CategoryManagementComponent_div_21_tr_26_Template_button_click_21_listener() {
      const sousCategory_r10 = \u0275\u0275restoreView(_r9).$implicit;
      const ctx_r0 = \u0275\u0275nextContext(2);
      return \u0275\u0275resetView(ctx_r0.openSubCategoryModal(sousCategory_r10));
    });
    \u0275\u0275element(22, "i", 41);
    \u0275\u0275elementEnd();
    \u0275\u0275template(23, CategoryManagementComponent_div_21_tr_26_button_23_Template, 2, 0, "button", 42)(24, CategoryManagementComponent_div_21_tr_26_button_24_Template, 2, 0, "button", 43);
    \u0275\u0275elementStart(25, "button", 44);
    \u0275\u0275listener("click", function CategoryManagementComponent_div_21_tr_26_Template_button_click_25_listener() {
      const sousCategory_r10 = \u0275\u0275restoreView(_r9).$implicit;
      const ctx_r0 = \u0275\u0275nextContext(2);
      return \u0275\u0275resetView(ctx_r0.deleteSubCategory(sousCategory_r10));
    });
    \u0275\u0275element(26, "i", 45);
    \u0275\u0275elementEnd()()()();
  }
  if (rf & 2) {
    const sousCategory_r10 = ctx.$implicit;
    const noDescription_r13 = \u0275\u0275reference(11);
    const ctx_r0 = \u0275\u0275nextContext(2);
    \u0275\u0275advance(3);
    \u0275\u0275textInterpolate(sousCategory_r10.nom);
    \u0275\u0275advance(2);
    \u0275\u0275textInterpolate1("ID: ", sousCategory_r10.id, "");
    \u0275\u0275advance(2);
    \u0275\u0275textInterpolate(ctx_r0.getCategoryName(sousCategory_r10.categorieId));
    \u0275\u0275advance(2);
    \u0275\u0275property("ngIf", sousCategory_r10.description)("ngIfElse", noDescription_r13);
    \u0275\u0275advance(4);
    \u0275\u0275property("ngClass", ctx_r0.getStatusClass(sousCategory_r10.estValide));
    \u0275\u0275advance();
    \u0275\u0275textInterpolate1(" ", ctx_r0.getStatusText(sousCategory_r10.estValide), " ");
    \u0275\u0275advance(2);
    \u0275\u0275textInterpolate(sousCategory_r10.nombreProduits);
    \u0275\u0275advance(2);
    \u0275\u0275textInterpolate(ctx_r0.formatDate(sousCategory_r10.dateCreation));
    \u0275\u0275advance(5);
    \u0275\u0275property("ngIf", !sousCategory_r10.estValide);
    \u0275\u0275advance();
    \u0275\u0275property("ngIf", sousCategory_r10.estValide);
  }
}
function CategoryManagementComponent_div_21_div_27_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "div", 51);
    \u0275\u0275element(1, "i", 12);
    \u0275\u0275elementStart(2, "h3");
    \u0275\u0275text(3, "Aucune sous-cat\xE9gorie");
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(4, "p");
    \u0275\u0275text(5, "Aucune sous-cat\xE9gorie n'a \xE9t\xE9 cr\xE9\xE9e pour le moment.");
    \u0275\u0275elementEnd()();
  }
}
function CategoryManagementComponent_div_21_Template(rf, ctx) {
  if (rf & 1) {
    const _r8 = \u0275\u0275getCurrentView();
    \u0275\u0275elementStart(0, "div", 21)(1, "div", 22)(2, "h2");
    \u0275\u0275text(3, "Sous-cat\xE9gories");
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(4, "button", 23);
    \u0275\u0275listener("click", function CategoryManagementComponent_div_21_Template_button_click_4_listener() {
      \u0275\u0275restoreView(_r8);
      const ctx_r0 = \u0275\u0275nextContext();
      return \u0275\u0275resetView(ctx_r0.openSubCategoryModal());
    });
    \u0275\u0275element(5, "i", 24);
    \u0275\u0275text(6, " Nouvelle sous-cat\xE9gorie ");
    \u0275\u0275elementEnd()();
    \u0275\u0275elementStart(7, "div", 25)(8, "table", 26)(9, "thead")(10, "tr")(11, "th");
    \u0275\u0275text(12, "Nom");
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(13, "th");
    \u0275\u0275text(14, "Cat\xE9gorie parent");
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(15, "th");
    \u0275\u0275text(16, "Description");
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(17, "th");
    \u0275\u0275text(18, "Statut");
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(19, "th");
    \u0275\u0275text(20, "Produits");
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(21, "th");
    \u0275\u0275text(22, "Date cr\xE9ation");
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(23, "th");
    \u0275\u0275text(24, "Actions");
    \u0275\u0275elementEnd()()();
    \u0275\u0275elementStart(25, "tbody");
    \u0275\u0275template(26, CategoryManagementComponent_div_21_tr_26_Template, 27, 11, "tr", 27);
    \u0275\u0275elementEnd()();
    \u0275\u0275template(27, CategoryManagementComponent_div_21_div_27_Template, 6, 0, "div", 28);
    \u0275\u0275elementEnd()();
  }
  if (rf & 2) {
    const ctx_r0 = \u0275\u0275nextContext();
    \u0275\u0275advance(26);
    \u0275\u0275property("ngForOf", ctx_r0.sousCategories());
    \u0275\u0275advance();
    \u0275\u0275property("ngIf", ctx_r0.sousCategories().length === 0);
  }
}
function CategoryManagementComponent_div_22_div_12_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "div", 67);
    \u0275\u0275text(1, " Le nom est requis (minimum 2 caract\xE8res) ");
    \u0275\u0275elementEnd();
  }
}
function CategoryManagementComponent_div_22_Template(rf, ctx) {
  if (rf & 1) {
    const _r14 = \u0275\u0275getCurrentView();
    \u0275\u0275elementStart(0, "div", 53);
    \u0275\u0275listener("click", function CategoryManagementComponent_div_22_Template_div_click_0_listener() {
      \u0275\u0275restoreView(_r14);
      const ctx_r0 = \u0275\u0275nextContext();
      return \u0275\u0275resetView(ctx_r0.closeCategoryModal());
    });
    \u0275\u0275elementStart(1, "div", 54);
    \u0275\u0275listener("click", function CategoryManagementComponent_div_22_Template_div_click_1_listener($event) {
      \u0275\u0275restoreView(_r14);
      return \u0275\u0275resetView($event.stopPropagation());
    });
    \u0275\u0275elementStart(2, "div", 55)(3, "h3");
    \u0275\u0275text(4);
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(5, "button", 56);
    \u0275\u0275listener("click", function CategoryManagementComponent_div_22_Template_button_click_5_listener() {
      \u0275\u0275restoreView(_r14);
      const ctx_r0 = \u0275\u0275nextContext();
      return \u0275\u0275resetView(ctx_r0.closeCategoryModal());
    });
    \u0275\u0275element(6, "i", 50);
    \u0275\u0275elementEnd()();
    \u0275\u0275elementStart(7, "form", 57);
    \u0275\u0275listener("ngSubmit", function CategoryManagementComponent_div_22_Template_form_ngSubmit_7_listener() {
      \u0275\u0275restoreView(_r14);
      const ctx_r0 = \u0275\u0275nextContext();
      return \u0275\u0275resetView(ctx_r0.saveCategoryForm());
    });
    \u0275\u0275elementStart(8, "div", 58)(9, "label", 59);
    \u0275\u0275text(10, "Nom *");
    \u0275\u0275elementEnd();
    \u0275\u0275element(11, "input", 60);
    \u0275\u0275template(12, CategoryManagementComponent_div_22_div_12_Template, 2, 0, "div", 61);
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(13, "div", 58)(14, "label", 62);
    \u0275\u0275text(15, "Description");
    \u0275\u0275elementEnd();
    \u0275\u0275element(16, "textarea", 63);
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(17, "div", 64)(18, "button", 65);
    \u0275\u0275listener("click", function CategoryManagementComponent_div_22_Template_button_click_18_listener() {
      \u0275\u0275restoreView(_r14);
      const ctx_r0 = \u0275\u0275nextContext();
      return \u0275\u0275resetView(ctx_r0.closeCategoryModal());
    });
    \u0275\u0275text(19, " Annuler ");
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(20, "button", 66);
    \u0275\u0275text(21);
    \u0275\u0275elementEnd()()()()();
  }
  if (rf & 2) {
    let tmp_3_0;
    const ctx_r0 = \u0275\u0275nextContext();
    \u0275\u0275advance(4);
    \u0275\u0275textInterpolate(ctx_r0.editingCategory() ? "Modifier la cat\xE9gorie" : "Nouvelle cat\xE9gorie");
    \u0275\u0275advance(3);
    \u0275\u0275property("formGroup", ctx_r0.categoryForm);
    \u0275\u0275advance(5);
    \u0275\u0275property("ngIf", ((tmp_3_0 = ctx_r0.categoryForm.get("nom")) == null ? null : tmp_3_0.invalid) && ((tmp_3_0 = ctx_r0.categoryForm.get("nom")) == null ? null : tmp_3_0.touched));
    \u0275\u0275advance(8);
    \u0275\u0275property("disabled", ctx_r0.categoryForm.invalid);
    \u0275\u0275advance();
    \u0275\u0275textInterpolate1(" ", ctx_r0.editingCategory() ? "Modifier" : "Cr\xE9er", " ");
  }
}
function CategoryManagementComponent_div_23_div_12_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "div", 67);
    \u0275\u0275text(1, " Le nom est requis (minimum 2 caract\xE8res) ");
    \u0275\u0275elementEnd();
  }
}
function CategoryManagementComponent_div_23_option_19_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "option", 76);
    \u0275\u0275text(1);
    \u0275\u0275elementEnd();
  }
  if (rf & 2) {
    const category_r16 = ctx.$implicit;
    \u0275\u0275property("value", category_r16.id);
    \u0275\u0275advance();
    \u0275\u0275textInterpolate1(" ", category_r16.nom, " ");
  }
}
function CategoryManagementComponent_div_23_div_20_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "div", 67);
    \u0275\u0275text(1, " La cat\xE9gorie parent est requise ");
    \u0275\u0275elementEnd();
  }
}
function CategoryManagementComponent_div_23_Template(rf, ctx) {
  if (rf & 1) {
    const _r15 = \u0275\u0275getCurrentView();
    \u0275\u0275elementStart(0, "div", 53);
    \u0275\u0275listener("click", function CategoryManagementComponent_div_23_Template_div_click_0_listener() {
      \u0275\u0275restoreView(_r15);
      const ctx_r0 = \u0275\u0275nextContext();
      return \u0275\u0275resetView(ctx_r0.closeSubCategoryModal());
    });
    \u0275\u0275elementStart(1, "div", 54);
    \u0275\u0275listener("click", function CategoryManagementComponent_div_23_Template_div_click_1_listener($event) {
      \u0275\u0275restoreView(_r15);
      return \u0275\u0275resetView($event.stopPropagation());
    });
    \u0275\u0275elementStart(2, "div", 55)(3, "h3");
    \u0275\u0275text(4);
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(5, "button", 56);
    \u0275\u0275listener("click", function CategoryManagementComponent_div_23_Template_button_click_5_listener() {
      \u0275\u0275restoreView(_r15);
      const ctx_r0 = \u0275\u0275nextContext();
      return \u0275\u0275resetView(ctx_r0.closeSubCategoryModal());
    });
    \u0275\u0275element(6, "i", 50);
    \u0275\u0275elementEnd()();
    \u0275\u0275elementStart(7, "form", 57);
    \u0275\u0275listener("ngSubmit", function CategoryManagementComponent_div_23_Template_form_ngSubmit_7_listener() {
      \u0275\u0275restoreView(_r15);
      const ctx_r0 = \u0275\u0275nextContext();
      return \u0275\u0275resetView(ctx_r0.saveSubCategoryForm());
    });
    \u0275\u0275elementStart(8, "div", 58)(9, "label", 68);
    \u0275\u0275text(10, "Nom *");
    \u0275\u0275elementEnd();
    \u0275\u0275element(11, "input", 69);
    \u0275\u0275template(12, CategoryManagementComponent_div_23_div_12_Template, 2, 0, "div", 61);
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(13, "div", 58)(14, "label", 70);
    \u0275\u0275text(15, "Cat\xE9gorie parent *");
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(16, "select", 71)(17, "option", 72);
    \u0275\u0275text(18, "S\xE9lectionner une cat\xE9gorie");
    \u0275\u0275elementEnd();
    \u0275\u0275template(19, CategoryManagementComponent_div_23_option_19_Template, 2, 2, "option", 73);
    \u0275\u0275elementEnd();
    \u0275\u0275template(20, CategoryManagementComponent_div_23_div_20_Template, 2, 0, "div", 61);
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(21, "div", 58)(22, "label", 74);
    \u0275\u0275text(23, "Description");
    \u0275\u0275elementEnd();
    \u0275\u0275element(24, "textarea", 75);
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(25, "div", 64)(26, "button", 65);
    \u0275\u0275listener("click", function CategoryManagementComponent_div_23_Template_button_click_26_listener() {
      \u0275\u0275restoreView(_r15);
      const ctx_r0 = \u0275\u0275nextContext();
      return \u0275\u0275resetView(ctx_r0.closeSubCategoryModal());
    });
    \u0275\u0275text(27, " Annuler ");
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(28, "button", 66);
    \u0275\u0275text(29);
    \u0275\u0275elementEnd()()()()();
  }
  if (rf & 2) {
    let tmp_3_0;
    let tmp_5_0;
    const ctx_r0 = \u0275\u0275nextContext();
    \u0275\u0275advance(4);
    \u0275\u0275textInterpolate(ctx_r0.editingSousCategory() ? "Modifier la sous-cat\xE9gorie" : "Nouvelle sous-cat\xE9gorie");
    \u0275\u0275advance(3);
    \u0275\u0275property("formGroup", ctx_r0.subCategoryForm);
    \u0275\u0275advance(5);
    \u0275\u0275property("ngIf", ((tmp_3_0 = ctx_r0.subCategoryForm.get("nom")) == null ? null : tmp_3_0.invalid) && ((tmp_3_0 = ctx_r0.subCategoryForm.get("nom")) == null ? null : tmp_3_0.touched));
    \u0275\u0275advance(7);
    \u0275\u0275property("ngForOf", ctx_r0.categories());
    \u0275\u0275advance();
    \u0275\u0275property("ngIf", ((tmp_5_0 = ctx_r0.subCategoryForm.get("categorieId")) == null ? null : tmp_5_0.invalid) && ((tmp_5_0 = ctx_r0.subCategoryForm.get("categorieId")) == null ? null : tmp_5_0.touched));
    \u0275\u0275advance(8);
    \u0275\u0275property("disabled", ctx_r0.subCategoryForm.invalid);
    \u0275\u0275advance();
    \u0275\u0275textInterpolate1(" ", ctx_r0.editingSousCategory() ? "Modifier" : "Cr\xE9er", " ");
  }
}
var CategoryManagementComponent = class _CategoryManagementComponent {
  adminService;
  categorieService;
  sousCategorieService;
  fb;
  // Angular 19: Signals
  categories = signal([]);
  sousCategories = signal([]);
  isLoading = signal(false);
  error = signal("");
  activeTab = signal("categories");
  // Modals
  showCategoryModal = signal(false);
  showSubCategoryModal = signal(false);
  editingCategory = signal(null);
  editingSousCategory = signal(null);
  // Forms
  categoryForm;
  subCategoryForm;
  constructor(adminService, categorieService, sousCategorieService, fb) {
    this.adminService = adminService;
    this.categorieService = categorieService;
    this.sousCategorieService = sousCategorieService;
    this.fb = fb;
    this.categoryForm = this.fb.group({
      nom: ["", [Validators.required, Validators.minLength(2)]],
      description: [""]
    });
    this.subCategoryForm = this.fb.group({
      nom: ["", [Validators.required, Validators.minLength(2)]],
      description: [""],
      categorieId: ["", [Validators.required]]
    });
  }
  ngOnInit() {
    this.loadCategories();
    this.loadSousCategories();
  }
  loadCategories() {
    this.isLoading.set(true);
    this.error.set("");
    this.categorieService.getAll().subscribe({
      next: (categories) => {
        console.log("\u2705 Cat\xE9gories re\xE7ues:", categories);
        const categoriesAdmin = [];
        let processedCount = 0;
        if (categories.length === 0) {
          this.categories.set([]);
          this.isLoading.set(false);
          return;
        }
        categories.forEach((cat) => {
          this.categorieService.getProduitsCount(cat.id).subscribe({
            next: (nombreProduits) => {
              categoriesAdmin.push({
                id: cat.id,
                nom: cat.nom,
                description: cat.description,
                estValide: cat.estValidee,
                dateCreation: (/* @__PURE__ */ new Date()).toISOString(),
                nombreProduits,
                sousCategories: []
              });
              processedCount++;
              if (processedCount === categories.length) {
                categoriesAdmin.sort((a, b) => a.nom.localeCompare(b.nom));
                this.categories.set(categoriesAdmin);
                this.isLoading.set(false);
              }
            },
            error: (error) => {
              console.warn(`Erreur comptage produits pour cat\xE9gorie ${cat.id}:`, error);
              categoriesAdmin.push({
                id: cat.id,
                nom: cat.nom,
                description: cat.description,
                estValide: cat.estValidee,
                dateCreation: (/* @__PURE__ */ new Date()).toISOString(),
                nombreProduits: 0,
                sousCategories: []
              });
              processedCount++;
              if (processedCount === categories.length) {
                categoriesAdmin.sort((a, b) => a.nom.localeCompare(b.nom));
                this.categories.set(categoriesAdmin);
                this.isLoading.set(false);
              }
            }
          });
        });
      },
      error: (error) => {
        console.error("\u274C Erreur lors du chargement des cat\xE9gories:", error);
        this.error.set("Erreur lors du chargement des cat\xE9gories");
        this.isLoading.set(false);
      }
    });
  }
  loadSousCategories() {
    this.sousCategorieService.getAll().subscribe({
      next: (sousCategories) => {
        console.log("\u2705 Sous-cat\xE9gories re\xE7ues:", sousCategories);
        const sousCategoriesAdmin = [];
        let processedCount = 0;
        if (sousCategories.length === 0) {
          this.sousCategories.set([]);
          return;
        }
        sousCategories.forEach((sousCat) => {
          this.sousCategorieService.getProduitsCount(sousCat.id).subscribe({
            next: (nombreProduits) => {
              sousCategoriesAdmin.push({
                id: sousCat.id,
                nom: sousCat.nom,
                description: sousCat.description,
                categorieId: sousCat.categorieId,
                categorieNom: this.getCategoryName(sousCat.categorieId),
                estValide: sousCat.estValidee,
                dateCreation: (/* @__PURE__ */ new Date()).toISOString(),
                nombreProduits
              });
              processedCount++;
              if (processedCount === sousCategories.length) {
                sousCategoriesAdmin.sort((a, b) => a.nom.localeCompare(b.nom));
                this.sousCategories.set(sousCategoriesAdmin);
              }
            },
            error: (error) => {
              console.warn(`Erreur comptage produits pour sous-cat\xE9gorie ${sousCat.id}:`, error);
              sousCategoriesAdmin.push({
                id: sousCat.id,
                nom: sousCat.nom,
                description: sousCat.description,
                categorieId: sousCat.categorieId,
                categorieNom: this.getCategoryName(sousCat.categorieId),
                estValide: sousCat.estValidee,
                dateCreation: (/* @__PURE__ */ new Date()).toISOString(),
                nombreProduits: 0
              });
              processedCount++;
              if (processedCount === sousCategories.length) {
                sousCategoriesAdmin.sort((a, b) => a.nom.localeCompare(b.nom));
                this.sousCategories.set(sousCategoriesAdmin);
              }
            }
          });
        });
      },
      error: (error) => {
        console.error("\u274C Erreur lors du chargement des sous-cat\xE9gories:", error);
        this.error.set("Erreur lors du chargement des sous-cat\xE9gories");
      }
    });
  }
  // ==================== CATÉGORIES ====================
  openCategoryModal(category) {
    this.editingCategory.set(category || null);
    if (category) {
      this.categoryForm.patchValue({
        nom: category.nom,
        description: category.description || ""
      });
    } else {
      this.categoryForm.reset();
    }
    this.showCategoryModal.set(true);
  }
  closeCategoryModal() {
    this.showCategoryModal.set(false);
    this.editingCategory.set(null);
    this.categoryForm.reset();
  }
  saveCategoryForm() {
    if (this.categoryForm.valid) {
      const formData = this.categoryForm.value;
      const editingCat = this.editingCategory();
      if (editingCat) {
        this.adminService.updateCategorie(editingCat.id, formData).subscribe({
          next: () => {
            this.loadCategories();
            this.closeCategoryModal();
            alert("Cat\xE9gorie modifi\xE9e avec succ\xE8s");
          },
          error: (error) => {
            console.error("Erreur lors de la modification:", error);
            alert("Erreur lors de la modification de la cat\xE9gorie");
          }
        });
      } else {
        this.adminService.createCategorie(formData).subscribe({
          next: () => {
            this.loadCategories();
            this.closeCategoryModal();
            alert("Cat\xE9gorie cr\xE9\xE9e avec succ\xE8s");
          },
          error: (error) => {
            console.error("Erreur lors de la cr\xE9ation:", error);
            alert("Erreur lors de la cr\xE9ation de la cat\xE9gorie");
          }
        });
      }
    }
  }
  deleteCategory(category) {
    if (confirm(`\xCAtes-vous s\xFBr de vouloir supprimer la cat\xE9gorie "${category.nom}" ?

Cette action supprimera aussi toutes les sous-cat\xE9gories associ\xE9es.`)) {
      this.adminService.deleteCategorie(category.id).subscribe({
        next: () => {
          this.loadCategories();
          this.loadSousCategories();
          alert("Cat\xE9gorie supprim\xE9e avec succ\xE8s");
        },
        error: (error) => {
          console.error("Erreur lors de la suppression:", error);
          alert("Erreur lors de la suppression de la cat\xE9gorie");
        }
      });
    }
  }
  validateCategory(category) {
    this.adminService.validerCategorie(category.id).subscribe({
      next: () => {
        const cats = this.categories();
        const index = cats.findIndex((c) => c.id === category.id);
        if (index !== -1) {
          cats[index] = __spreadProps(__spreadValues({}, cats[index]), { estValide: true });
          this.categories.set([...cats]);
        }
        alert("Cat\xE9gorie valid\xE9e avec succ\xE8s");
      },
      error: (error) => {
        console.error("Erreur lors de la validation:", error);
        let errorMessage = "Erreur lors de la validation de la cat\xE9gorie";
        if (error.status === 404) {
          errorMessage = "Endpoint de validation non trouv\xE9. V\xE9rifiez que le backend est d\xE9marr\xE9.";
        } else if (error.status === 0) {
          errorMessage = "Impossible de contacter le serveur. V\xE9rifiez que le backend est d\xE9marr\xE9 sur https://localhost:7264";
        } else if (error.error?.message) {
          errorMessage = `Erreur: ${error.error.message}`;
        }
        alert(errorMessage);
      }
    });
  }
  rejectCategory(category) {
    const motif = prompt("Motif du refus:");
    if (motif) {
      this.adminService.refuserCategorie(category.id, motif).subscribe({
        next: () => {
          const cats = this.categories();
          const index = cats.findIndex((c) => c.id === category.id);
          if (index !== -1) {
            cats[index] = __spreadProps(__spreadValues({}, cats[index]), { estValide: false });
            this.categories.set([...cats]);
          }
          alert("Cat\xE9gorie refus\xE9e avec succ\xE8s");
        },
        error: (error) => {
          console.error("Erreur lors du refus:", error);
          alert("Erreur lors du refus de la cat\xE9gorie");
        }
      });
    }
  }
  // ==================== SOUS-CATÉGORIES ====================
  openSubCategoryModal(sousCategory) {
    this.editingSousCategory.set(sousCategory || null);
    if (sousCategory) {
      this.subCategoryForm.patchValue({
        nom: sousCategory.nom,
        description: sousCategory.description || "",
        categorieId: sousCategory.categorieId
      });
    } else {
      this.subCategoryForm.reset();
    }
    this.showSubCategoryModal.set(true);
  }
  closeSubCategoryModal() {
    this.showSubCategoryModal.set(false);
    this.editingSousCategory.set(null);
    this.subCategoryForm.reset();
  }
  saveSubCategoryForm() {
    if (this.subCategoryForm.valid) {
      const formData = this.subCategoryForm.value;
      const editingSousCat = this.editingSousCategory();
      if (editingSousCat) {
        this.adminService.updateSousCategorie(editingSousCat.id, formData).subscribe({
          next: () => {
            this.loadSousCategories();
            this.closeSubCategoryModal();
            alert("Sous-cat\xE9gorie modifi\xE9e avec succ\xE8s");
          },
          error: (error) => {
            console.error("Erreur lors de la modification:", error);
            alert("Erreur lors de la modification de la sous-cat\xE9gorie");
          }
        });
      } else {
        this.adminService.createSousCategorie(formData).subscribe({
          next: () => {
            this.loadSousCategories();
            this.closeSubCategoryModal();
            alert("Sous-cat\xE9gorie cr\xE9\xE9e avec succ\xE8s");
          },
          error: (error) => {
            console.error("Erreur lors de la cr\xE9ation:", error);
            alert("Erreur lors de la cr\xE9ation de la sous-cat\xE9gorie");
          }
        });
      }
    }
  }
  deleteSubCategory(sousCategory) {
    if (confirm(`\xCAtes-vous s\xFBr de vouloir supprimer la sous-cat\xE9gorie "${sousCategory.nom}" ?`)) {
      this.adminService.deleteSousCategorie(sousCategory.id).subscribe({
        next: () => {
          this.loadSousCategories();
          alert("Sous-cat\xE9gorie supprim\xE9e avec succ\xE8s");
        },
        error: (error) => {
          console.error("Erreur lors de la suppression:", error);
          alert("Erreur lors de la suppression de la sous-cat\xE9gorie");
        }
      });
    }
  }
  validateSubCategory(sousCategory) {
    this.adminService.validerSousCategorie(sousCategory.id).subscribe({
      next: () => {
        const sousCats = this.sousCategories();
        const index = sousCats.findIndex((sc) => sc.id === sousCategory.id);
        if (index !== -1) {
          sousCats[index] = __spreadProps(__spreadValues({}, sousCats[index]), { estValide: true });
          this.sousCategories.set([...sousCats]);
        }
        alert("Sous-cat\xE9gorie valid\xE9e avec succ\xE8s");
      },
      error: (error) => {
        console.error("Erreur lors de la validation:", error);
        alert("Erreur lors de la validation de la sous-cat\xE9gorie");
      }
    });
  }
  rejectSubCategory(sousCategory) {
    const motif = prompt("Motif du refus:");
    if (motif) {
      this.adminService.refuserSousCategorie(sousCategory.id, motif).subscribe({
        next: () => {
          const sousCats = this.sousCategories();
          const index = sousCats.findIndex((sc) => sc.id === sousCategory.id);
          if (index !== -1) {
            sousCats[index] = __spreadProps(__spreadValues({}, sousCats[index]), { estValide: false });
            this.sousCategories.set([...sousCats]);
          }
          alert("Sous-cat\xE9gorie refus\xE9e avec succ\xE8s");
        },
        error: (error) => {
          console.error("Erreur lors du refus:", error);
          alert("Erreur lors du refus de la sous-cat\xE9gorie");
        }
      });
    }
  }
  // ==================== UTILITAIRES ====================
  setActiveTab(tab) {
    this.activeTab.set(tab);
  }
  getStatusClass(estValide) {
    return estValide ? "status-validated" : "status-pending";
  }
  getStatusText(estValide) {
    return estValide ? "Valid\xE9e" : "En attente";
  }
  formatDate(date) {
    return new Date(date).toLocaleDateString("fr-FR", {
      year: "numeric",
      month: "short",
      day: "numeric"
    });
  }
  getCategoryName(categorieId) {
    const category = this.categories().find((c) => c.id === categorieId);
    return category ? category.nom : "Cat\xE9gorie inconnue";
  }
  refresh() {
    this.loadCategories();
    this.loadSousCategories();
  }
  static \u0275fac = function CategoryManagementComponent_Factory(__ngFactoryType__) {
    return new (__ngFactoryType__ || _CategoryManagementComponent)(\u0275\u0275directiveInject(AdminService), \u0275\u0275directiveInject(CategorieService), \u0275\u0275directiveInject(SousCategorieService), \u0275\u0275directiveInject(FormBuilder));
  };
  static \u0275cmp = /* @__PURE__ */ \u0275\u0275defineComponent({ type: _CategoryManagementComponent, selectors: [["app-category-management"]], decls: 24, vars: 13, consts: [["noDescription", ""], [1, "category-management-container"], [1, "page-header"], [1, "header-content"], [1, "page-title"], [1, "icon-folder"], [1, "header-actions"], [1, "btn", "btn-secondary", 3, "click", "disabled"], [1, "icon-refresh"], [1, "tabs-container"], [1, "tabs"], [1, "tab-button", 3, "click"], [1, "icon-folder-open"], ["class", "error-message", 4, "ngIf"], ["class", "loading-container", 4, "ngIf"], ["class", "tab-content", 4, "ngIf"], ["class", "modal-overlay", 3, "click", 4, "ngIf"], [1, "error-message"], [1, "icon-alert"], [1, "loading-container"], [1, "loading-spinner"], [1, "tab-content"], [1, "section-header"], [1, "btn", "btn-primary", 3, "click"], [1, "icon-plus"], [1, "table-container"], [1, "data-table"], ["class", "data-row", 4, "ngFor", "ngForOf"], ["class", "no-data", 4, "ngIf"], [1, "data-row"], [1, "name-cell"], [1, "item-name"], [1, "item-id"], [1, "description-cell"], [4, "ngIf", "ngIfElse"], [1, "status-badge", 3, "ngClass"], [1, "count-cell"], [1, "date-cell"], [1, "actions-cell"], [1, "action-buttons"], ["title", "Modifier", 1, "btn-action", "btn-edit", 3, "click"], [1, "icon-edit"], ["class", "btn-action btn-validate", "title", "Valider", 3, "click", 4, "ngIf"], ["class", "btn-action btn-reject", "title", "Refuser", 3, "click", 4, "ngIf"], ["title", "Supprimer", 1, "btn-action", "btn-delete", 3, "click"], [1, "icon-trash"], [1, "no-description"], ["title", "Valider", 1, "btn-action", "btn-validate", 3, "click"], [1, "icon-check"], ["title", "Refuser", 1, "btn-action", "btn-reject", 3, "click"], [1, "icon-x"], [1, "no-data"], [1, "parent-cell"], [1, "modal-overlay", 3, "click"], [1, "modal-content", 3, "click"], [1, "modal-header"], [1, "btn-close", 3, "click"], [1, "modal-form", 3, "ngSubmit", "formGroup"], [1, "form-group"], ["for", "categoryName"], ["id", "categoryName", "type", "text", "formControlName", "nom", "placeholder", "Nom de la cat\xE9gorie", 1, "form-input"], ["class", "error-text", 4, "ngIf"], ["for", "categoryDescription"], ["id", "categoryDescription", "formControlName", "description", "placeholder", "Description de la cat\xE9gorie", "rows", "3", 1, "form-textarea"], [1, "modal-actions"], ["type", "button", 1, "btn", "btn-secondary", 3, "click"], ["type", "submit", 1, "btn", "btn-primary", 3, "disabled"], [1, "error-text"], ["for", "subCategoryName"], ["id", "subCategoryName", "type", "text", "formControlName", "nom", "placeholder", "Nom de la sous-cat\xE9gorie", 1, "form-input"], ["for", "parentCategory"], ["id", "parentCategory", "formControlName", "categorieId", 1, "form-select"], ["value", ""], [3, "value", 4, "ngFor", "ngForOf"], ["for", "subCategoryDescription"], ["id", "subCategoryDescription", "formControlName", "description", "placeholder", "Description de la sous-cat\xE9gorie", "rows", "3", 1, "form-textarea"], [3, "value"]], template: function CategoryManagementComponent_Template(rf, ctx) {
    if (rf & 1) {
      \u0275\u0275elementStart(0, "div", 1)(1, "div", 2)(2, "div", 3)(3, "h1", 4);
      \u0275\u0275element(4, "i", 5);
      \u0275\u0275text(5, " Gestion des Cat\xE9gories ");
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(6, "div", 6)(7, "button", 7);
      \u0275\u0275listener("click", function CategoryManagementComponent_Template_button_click_7_listener() {
        return ctx.refresh();
      });
      \u0275\u0275element(8, "i", 8);
      \u0275\u0275text(9, " Actualiser ");
      \u0275\u0275elementEnd()()()();
      \u0275\u0275elementStart(10, "div", 9)(11, "div", 10)(12, "button", 11);
      \u0275\u0275listener("click", function CategoryManagementComponent_Template_button_click_12_listener() {
        return ctx.setActiveTab("categories");
      });
      \u0275\u0275element(13, "i", 5);
      \u0275\u0275text(14);
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(15, "button", 11);
      \u0275\u0275listener("click", function CategoryManagementComponent_Template_button_click_15_listener() {
        return ctx.setActiveTab("souscategories");
      });
      \u0275\u0275element(16, "i", 12);
      \u0275\u0275text(17);
      \u0275\u0275elementEnd()()();
      \u0275\u0275template(18, CategoryManagementComponent_div_18_Template, 3, 1, "div", 13)(19, CategoryManagementComponent_div_19_Template, 4, 0, "div", 14)(20, CategoryManagementComponent_div_20_Template, 26, 2, "div", 15)(21, CategoryManagementComponent_div_21_Template, 28, 2, "div", 15);
      \u0275\u0275elementEnd();
      \u0275\u0275template(22, CategoryManagementComponent_div_22_Template, 22, 5, "div", 16)(23, CategoryManagementComponent_div_23_Template, 30, 7, "div", 16);
    }
    if (rf & 2) {
      \u0275\u0275advance(7);
      \u0275\u0275property("disabled", ctx.isLoading());
      \u0275\u0275advance(5);
      \u0275\u0275classProp("active", ctx.activeTab() === "categories");
      \u0275\u0275advance(2);
      \u0275\u0275textInterpolate1(" Cat\xE9gories (", ctx.categories().length, ") ");
      \u0275\u0275advance();
      \u0275\u0275classProp("active", ctx.activeTab() === "souscategories");
      \u0275\u0275advance(2);
      \u0275\u0275textInterpolate1(" Sous-cat\xE9gories (", ctx.sousCategories().length, ") ");
      \u0275\u0275advance();
      \u0275\u0275property("ngIf", ctx.error());
      \u0275\u0275advance();
      \u0275\u0275property("ngIf", ctx.isLoading());
      \u0275\u0275advance();
      \u0275\u0275property("ngIf", ctx.activeTab() === "categories" && !ctx.isLoading());
      \u0275\u0275advance();
      \u0275\u0275property("ngIf", ctx.activeTab() === "souscategories" && !ctx.isLoading());
      \u0275\u0275advance();
      \u0275\u0275property("ngIf", ctx.showCategoryModal());
      \u0275\u0275advance();
      \u0275\u0275property("ngIf", ctx.showSubCategoryModal());
    }
  }, dependencies: [CommonModule, NgClass, NgForOf, NgIf, FormsModule, \u0275NgNoValidate, NgSelectOption, \u0275NgSelectMultipleOption, DefaultValueAccessor, SelectControlValueAccessor, NgControlStatus, NgControlStatusGroup, ReactiveFormsModule, FormGroupDirective, FormControlName], styles: ['\n\n.category-management-container[_ngcontent-%COMP%] {\n  padding: 2rem;\n  max-width: 1400px;\n  margin: 0 auto;\n}\n.page-header[_ngcontent-%COMP%] {\n  margin-bottom: 2rem;\n}\n.header-content[_ngcontent-%COMP%] {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  flex-wrap: wrap;\n  gap: 1rem;\n}\n.page-title[_ngcontent-%COMP%] {\n  display: flex;\n  align-items: center;\n  gap: 0.75rem;\n  font-size: 2rem;\n  font-weight: 600;\n  color: #1a202c;\n  margin: 0;\n}\n.header-actions[_ngcontent-%COMP%] {\n  display: flex;\n  gap: 1rem;\n}\n.tabs-container[_ngcontent-%COMP%] {\n  margin-bottom: 2rem;\n}\n.tabs[_ngcontent-%COMP%] {\n  display: flex;\n  border-bottom: 2px solid #e5e7eb;\n}\n.tab-button[_ngcontent-%COMP%] {\n  background: none;\n  border: none;\n  padding: 1rem 2rem;\n  cursor: pointer;\n  font-weight: 500;\n  color: #64748b;\n  border-bottom: 2px solid transparent;\n  transition: all 0.2s;\n  display: flex;\n  align-items: center;\n  gap: 0.5rem;\n}\n.tab-button[_ngcontent-%COMP%]:hover {\n  color: #3b82f6;\n}\n.tab-button.active[_ngcontent-%COMP%] {\n  color: #3b82f6;\n  border-bottom-color: #3b82f6;\n}\n.section-header[_ngcontent-%COMP%] {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 1.5rem;\n  padding: 0 1rem;\n}\n.section-header[_ngcontent-%COMP%]   h2[_ngcontent-%COMP%] {\n  font-size: 1.5rem;\n  font-weight: 600;\n  color: #1a202c;\n  margin: 0;\n}\n.table-container[_ngcontent-%COMP%] {\n  background: white;\n  border-radius: 12px;\n  overflow: hidden;\n  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);\n}\n.data-table[_ngcontent-%COMP%] {\n  width: 100%;\n  border-collapse: collapse;\n}\n.data-table[_ngcontent-%COMP%]   th[_ngcontent-%COMP%] {\n  background: #f8fafc;\n  padding: 1rem;\n  text-align: left;\n  font-weight: 600;\n  color: #374151;\n  border-bottom: 1px solid #e5e7eb;\n}\n.data-row[_ngcontent-%COMP%] {\n  border-bottom: 1px solid #f3f4f6;\n  transition: background-color 0.2s;\n}\n.data-row[_ngcontent-%COMP%]:hover {\n  background: #f9fafb;\n}\n.data-table[_ngcontent-%COMP%]   td[_ngcontent-%COMP%] {\n  padding: 1rem;\n  vertical-align: middle;\n}\n.name-cell[_ngcontent-%COMP%] {\n  display: flex;\n  flex-direction: column;\n}\n.item-name[_ngcontent-%COMP%] {\n  font-weight: 600;\n  color: #1a202c;\n}\n.item-id[_ngcontent-%COMP%] {\n  font-size: 0.875rem;\n  color: #64748b;\n}\n.description-cell[_ngcontent-%COMP%] {\n  max-width: 300px;\n}\n.no-description[_ngcontent-%COMP%] {\n  color: #9ca3af;\n  font-style: italic;\n}\n.parent-cell[_ngcontent-%COMP%] {\n  color: #4f46e5;\n  font-weight: 500;\n}\n.count-cell[_ngcontent-%COMP%] {\n  text-align: center;\n  font-weight: 600;\n  color: #059669;\n}\n.date-cell[_ngcontent-%COMP%] {\n  color: #64748b;\n  font-size: 0.875rem;\n}\n.status-badge[_ngcontent-%COMP%] {\n  padding: 0.25rem 0.75rem;\n  border-radius: 9999px;\n  font-size: 0.875rem;\n  font-weight: 500;\n}\n.status-validated[_ngcontent-%COMP%] {\n  background: #d1fae5;\n  color: #059669;\n}\n.status-pending[_ngcontent-%COMP%] {\n  background: #fef3c7;\n  color: #d97706;\n}\n.actions-cell[_ngcontent-%COMP%] {\n  width: 160px;\n}\n.action-buttons[_ngcontent-%COMP%] {\n  display: flex;\n  gap: 0.5rem;\n}\n.btn-action[_ngcontent-%COMP%] {\n  width: 32px;\n  height: 32px;\n  border: none;\n  border-radius: 6px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  cursor: pointer;\n  transition: all 0.2s;\n}\n.btn-edit[_ngcontent-%COMP%] {\n  background: #dbeafe;\n  color: #2563eb;\n}\n.btn-edit[_ngcontent-%COMP%]:hover {\n  background: #bfdbfe;\n}\n.btn-validate[_ngcontent-%COMP%] {\n  background: #d1fae5;\n  color: #059669;\n}\n.btn-validate[_ngcontent-%COMP%]:hover {\n  background: #a7f3d0;\n}\n.btn-reject[_ngcontent-%COMP%] {\n  background: #fef3c7;\n  color: #d97706;\n}\n.btn-reject[_ngcontent-%COMP%]:hover {\n  background: #fde68a;\n}\n.btn-delete[_ngcontent-%COMP%] {\n  background: #fee2e2;\n  color: #dc2626;\n}\n.btn-delete[_ngcontent-%COMP%]:hover {\n  background: #fecaca;\n}\n.btn[_ngcontent-%COMP%] {\n  padding: 0.75rem 1.5rem;\n  border: none;\n  border-radius: 8px;\n  font-weight: 500;\n  cursor: pointer;\n  transition: all 0.2s;\n  display: flex;\n  align-items: center;\n  gap: 0.5rem;\n}\n.btn-primary[_ngcontent-%COMP%] {\n  background: #3b82f6;\n  color: white;\n}\n.btn-primary[_ngcontent-%COMP%]:hover {\n  background: #2563eb;\n}\n.btn-secondary[_ngcontent-%COMP%] {\n  background: #f3f4f6;\n  color: #374151;\n}\n.btn-secondary[_ngcontent-%COMP%]:hover {\n  background: #e5e7eb;\n}\n.btn[_ngcontent-%COMP%]:disabled {\n  opacity: 0.5;\n  cursor: not-allowed;\n}\n.modal-overlay[_ngcontent-%COMP%] {\n  position: fixed;\n  top: 0;\n  left: 0;\n  right: 0;\n  bottom: 0;\n  background: rgba(0, 0, 0, 0.5);\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  z-index: 1000;\n}\n.modal-content[_ngcontent-%COMP%] {\n  background: white;\n  border-radius: 12px;\n  width: 90%;\n  max-width: 500px;\n  max-height: 90vh;\n  overflow-y: auto;\n}\n.modal-header[_ngcontent-%COMP%] {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  padding: 1.5rem;\n  border-bottom: 1px solid #e5e7eb;\n}\n.modal-header[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%] {\n  margin: 0;\n  font-size: 1.25rem;\n  font-weight: 600;\n  color: #1a202c;\n}\n.btn-close[_ngcontent-%COMP%] {\n  background: none;\n  border: none;\n  font-size: 1.5rem;\n  cursor: pointer;\n  color: #64748b;\n  padding: 0.25rem;\n}\n.btn-close[_ngcontent-%COMP%]:hover {\n  color: #374151;\n}\n.modal-form[_ngcontent-%COMP%] {\n  padding: 1.5rem;\n}\n.form-group[_ngcontent-%COMP%] {\n  margin-bottom: 1.5rem;\n}\n.form-group[_ngcontent-%COMP%]   label[_ngcontent-%COMP%] {\n  display: block;\n  margin-bottom: 0.5rem;\n  font-weight: 500;\n  color: #374151;\n}\n.form-input[_ngcontent-%COMP%], \n.form-textarea[_ngcontent-%COMP%], \n.form-select[_ngcontent-%COMP%] {\n  width: 100%;\n  padding: 0.75rem;\n  border: 2px solid #e5e7eb;\n  border-radius: 8px;\n  font-size: 1rem;\n  transition: border-color 0.2s;\n}\n.form-input[_ngcontent-%COMP%]:focus, \n.form-textarea[_ngcontent-%COMP%]:focus, \n.form-select[_ngcontent-%COMP%]:focus {\n  outline: none;\n  border-color: #3b82f6;\n}\n.form-textarea[_ngcontent-%COMP%] {\n  resize: vertical;\n  min-height: 80px;\n}\n.error-text[_ngcontent-%COMP%] {\n  color: #dc2626;\n  font-size: 0.875rem;\n  margin-top: 0.25rem;\n}\n.modal-actions[_ngcontent-%COMP%] {\n  display: flex;\n  gap: 1rem;\n  justify-content: flex-end;\n  margin-top: 2rem;\n}\n.error-message[_ngcontent-%COMP%] {\n  background: #fef2f2;\n  border: 1px solid #fecaca;\n  color: #dc2626;\n  padding: 1rem;\n  border-radius: 8px;\n  margin-bottom: 2rem;\n  display: flex;\n  align-items: center;\n  gap: 0.5rem;\n}\n.loading-container[_ngcontent-%COMP%] {\n  text-align: center;\n  padding: 3rem;\n}\n.loading-spinner[_ngcontent-%COMP%] {\n  width: 40px;\n  height: 40px;\n  border: 4px solid #e2e8f0;\n  border-top: 4px solid #3b82f6;\n  border-radius: 50%;\n  animation: _ngcontent-%COMP%_spin 1s linear infinite;\n  margin: 0 auto 1rem;\n}\n@keyframes _ngcontent-%COMP%_spin {\n  0% {\n    transform: rotate(0deg);\n  }\n  100% {\n    transform: rotate(360deg);\n  }\n}\n.no-data[_ngcontent-%COMP%] {\n  text-align: center;\n  padding: 3rem;\n  color: #64748b;\n}\n.no-data[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\n  font-size: 3rem;\n  margin-bottom: 1rem;\n  opacity: 0.5;\n}\n.no-data[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%] {\n  margin: 0 0 0.5rem 0;\n  color: #374151;\n}\n.icon-folder[_ngcontent-%COMP%]::before {\n  content: "\\1f4c1";\n}\n.icon-folder-open[_ngcontent-%COMP%]::before {\n  content: "\\1f4c2";\n}\n.icon-refresh[_ngcontent-%COMP%]::before {\n  content: "\\1f504";\n}\n.icon-plus[_ngcontent-%COMP%]::before {\n  content: "\\2795";\n}\n.icon-edit[_ngcontent-%COMP%]::before {\n  content: "\\270f\\fe0f";\n}\n.icon-check[_ngcontent-%COMP%]::before {\n  content: "\\2705";\n}\n.icon-x[_ngcontent-%COMP%]::before {\n  content: "\\274c";\n}\n.icon-trash[_ngcontent-%COMP%]::before {\n  content: "\\1f5d1\\fe0f";\n}\n.icon-alert[_ngcontent-%COMP%]::before {\n  content: "\\26a0\\fe0f";\n}\n@media (max-width: 768px) {\n  .category-management-container[_ngcontent-%COMP%] {\n    padding: 1rem;\n  }\n  .header-content[_ngcontent-%COMP%] {\n    flex-direction: column;\n    align-items: stretch;\n  }\n  .tabs[_ngcontent-%COMP%] {\n    flex-direction: column;\n  }\n  .tab-button[_ngcontent-%COMP%] {\n    padding: 0.75rem 1rem;\n  }\n  .section-header[_ngcontent-%COMP%] {\n    flex-direction: column;\n    gap: 1rem;\n    align-items: stretch;\n  }\n  .data-table[_ngcontent-%COMP%] {\n    font-size: 0.875rem;\n  }\n  .data-table[_ngcontent-%COMP%]   th[_ngcontent-%COMP%], \n   .data-table[_ngcontent-%COMP%]   td[_ngcontent-%COMP%] {\n    padding: 0.75rem 0.5rem;\n  }\n  .modal-content[_ngcontent-%COMP%] {\n    width: 95%;\n    margin: 1rem;\n  }\n}\n/*# sourceMappingURL=category-management.component.css.map */'] });
};
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && setClassMetadata(CategoryManagementComponent, [{
    type: Component,
    args: [{ selector: "app-category-management", standalone: true, imports: [CommonModule, FormsModule, ReactiveFormsModule], template: `<div class="category-management-container">
  <!-- Header -->
  <div class="page-header">
    <div class="header-content">
      <h1 class="page-title">
        <i class="icon-folder"></i>
        Gestion des Cat\xE9gories
      </h1>
      <div class="header-actions">
        <button class="btn btn-secondary" (click)="refresh()" [disabled]="isLoading()">
          <i class="icon-refresh"></i>
          Actualiser
        </button>
      </div>
    </div>
  </div>

  <!-- Tabs -->
  <div class="tabs-container">
    <div class="tabs">
      <button 
        class="tab-button"
        [class.active]="activeTab() === 'categories'"
        (click)="setActiveTab('categories')">
        <i class="icon-folder"></i>
        Cat\xE9gories ({{ categories().length }})
      </button>
      <button 
        class="tab-button"
        [class.active]="activeTab() === 'souscategories'"
        (click)="setActiveTab('souscategories')">
        <i class="icon-folder-open"></i>
        Sous-cat\xE9gories ({{ sousCategories().length }})
      </button>
    </div>
  </div>

  <!-- Message d'erreur -->
  <div *ngIf="error()" class="error-message">
    <i class="icon-alert"></i>
    {{ error() }}
  </div>

  <!-- Loading -->
  <div *ngIf="isLoading()" class="loading-container">
    <div class="loading-spinner"></div>
    <p>Chargement...</p>
  </div>

  <!-- Onglet Cat\xE9gories -->
  <div *ngIf="activeTab() === 'categories' && !isLoading()" class="tab-content">
    <div class="section-header">
      <h2>Cat\xE9gories</h2>
      <button class="btn btn-primary" (click)="openCategoryModal()">
        <i class="icon-plus"></i>
        Nouvelle cat\xE9gorie
      </button>
    </div>

    <div class="table-container">
      <table class="data-table">
        <thead>
          <tr>
            <th>Nom</th>
            <th>Description</th>
            <th>Statut</th>
            <th>Produits</th>
            <th>Date cr\xE9ation</th>
            <th>Actions</th>
          </tr>
        </thead>
        <tbody>
          <tr *ngFor="let category of categories()" class="data-row">
            <td class="name-cell">
              <div class="item-name">{{ category.nom }}</div>
              <div class="item-id">ID: {{ category.id }}</div>
            </td>
            
            <td class="description-cell">
              <span *ngIf="category.description; else noDescription">
                {{ category.description }}
              </span>
              <ng-template #noDescription>
                <span class="no-description">Aucune description</span>
              </ng-template>
            </td>
            
            <td>
              <span class="status-badge" [ngClass]="getStatusClass(category.estValide)">
                {{ getStatusText(category.estValide) }}
              </span>
            </td>
            
            <td class="count-cell">{{ category.nombreProduits }}</td>
            
            <td class="date-cell">{{ formatDate(category.dateCreation) }}</td>
            
            <td class="actions-cell">
              <div class="action-buttons">
                <button 
                  class="btn-action btn-edit"
                  (click)="openCategoryModal(category)"
                  title="Modifier">
                  <i class="icon-edit"></i>
                </button>
                
                <button 
                  *ngIf="!category.estValide"
                  class="btn-action btn-validate"
                  (click)="validateCategory(category)"
                  title="Valider">
                  <i class="icon-check"></i>
                </button>
                
                <button 
                  *ngIf="category.estValide"
                  class="btn-action btn-reject"
                  (click)="rejectCategory(category)"
                  title="Refuser">
                  <i class="icon-x"></i>
                </button>
                
                <button 
                  class="btn-action btn-delete"
                  (click)="deleteCategory(category)"
                  title="Supprimer">
                  <i class="icon-trash"></i>
                </button>
              </div>
            </td>
          </tr>
        </tbody>
      </table>

      <div *ngIf="categories().length === 0" class="no-data">
        <i class="icon-folder"></i>
        <h3>Aucune cat\xE9gorie</h3>
        <p>Aucune cat\xE9gorie n'a \xE9t\xE9 cr\xE9\xE9e pour le moment.</p>
      </div>
    </div>
  </div>

  <!-- Onglet Sous-cat\xE9gories -->
  <div *ngIf="activeTab() === 'souscategories' && !isLoading()" class="tab-content">
    <div class="section-header">
      <h2>Sous-cat\xE9gories</h2>
      <button class="btn btn-primary" (click)="openSubCategoryModal()">
        <i class="icon-plus"></i>
        Nouvelle sous-cat\xE9gorie
      </button>
    </div>

    <div class="table-container">
      <table class="data-table">
        <thead>
          <tr>
            <th>Nom</th>
            <th>Cat\xE9gorie parent</th>
            <th>Description</th>
            <th>Statut</th>
            <th>Produits</th>
            <th>Date cr\xE9ation</th>
            <th>Actions</th>
          </tr>
        </thead>
        <tbody>
          <tr *ngFor="let sousCategory of sousCategories()" class="data-row">
            <td class="name-cell">
              <div class="item-name">{{ sousCategory.nom }}</div>
              <div class="item-id">ID: {{ sousCategory.id }}</div>
            </td>
            
            <td class="parent-cell">{{ getCategoryName(sousCategory.categorieId) }}</td>
            
            <td class="description-cell">
              <span *ngIf="sousCategory.description; else noDescription">
                {{ sousCategory.description }}
              </span>
              <ng-template #noDescription>
                <span class="no-description">Aucune description</span>
              </ng-template>
            </td>
            
            <td>
              <span class="status-badge" [ngClass]="getStatusClass(sousCategory.estValide)">
                {{ getStatusText(sousCategory.estValide) }}
              </span>
            </td>
            
            <td class="count-cell">{{ sousCategory.nombreProduits }}</td>
            
            <td class="date-cell">{{ formatDate(sousCategory.dateCreation) }}</td>
            
            <td class="actions-cell">
              <div class="action-buttons">
                <button 
                  class="btn-action btn-edit"
                  (click)="openSubCategoryModal(sousCategory)"
                  title="Modifier">
                  <i class="icon-edit"></i>
                </button>
                
                <button 
                  *ngIf="!sousCategory.estValide"
                  class="btn-action btn-validate"
                  (click)="validateSubCategory(sousCategory)"
                  title="Valider">
                  <i class="icon-check"></i>
                </button>
                
                <button 
                  *ngIf="sousCategory.estValide"
                  class="btn-action btn-reject"
                  (click)="rejectSubCategory(sousCategory)"
                  title="Refuser">
                  <i class="icon-x"></i>
                </button>
                
                <button 
                  class="btn-action btn-delete"
                  (click)="deleteSubCategory(sousCategory)"
                  title="Supprimer">
                  <i class="icon-trash"></i>
                </button>
              </div>
            </td>
          </tr>
        </tbody>
      </table>

      <div *ngIf="sousCategories().length === 0" class="no-data">
        <i class="icon-folder-open"></i>
        <h3>Aucune sous-cat\xE9gorie</h3>
        <p>Aucune sous-cat\xE9gorie n'a \xE9t\xE9 cr\xE9\xE9e pour le moment.</p>
      </div>
    </div>
  </div>
</div>

<!-- Modal Cat\xE9gorie -->
<div *ngIf="showCategoryModal()" class="modal-overlay" (click)="closeCategoryModal()">
  <div class="modal-content" (click)="$event.stopPropagation()">
    <div class="modal-header">
      <h3>{{ editingCategory() ? 'Modifier la cat\xE9gorie' : 'Nouvelle cat\xE9gorie' }}</h3>
      <button class="btn-close" (click)="closeCategoryModal()">
        <i class="icon-x"></i>
      </button>
    </div>
    
    <form [formGroup]="categoryForm" (ngSubmit)="saveCategoryForm()" class="modal-form">
      <div class="form-group">
        <label for="categoryName">Nom *</label>
        <input 
          id="categoryName"
          type="text" 
          formControlName="nom"
          class="form-input"
          placeholder="Nom de la cat\xE9gorie">
        <div *ngIf="categoryForm.get('nom')?.invalid && categoryForm.get('nom')?.touched" class="error-text">
          Le nom est requis (minimum 2 caract\xE8res)
        </div>
      </div>
      
      <div class="form-group">
        <label for="categoryDescription">Description</label>
        <textarea 
          id="categoryDescription"
          formControlName="description"
          class="form-textarea"
          placeholder="Description de la cat\xE9gorie"
          rows="3"></textarea>
      </div>
      
      <div class="modal-actions">
        <button type="button" class="btn btn-secondary" (click)="closeCategoryModal()">
          Annuler
        </button>
        <button type="submit" class="btn btn-primary" [disabled]="categoryForm.invalid">
          {{ editingCategory() ? 'Modifier' : 'Cr\xE9er' }}
        </button>
      </div>
    </form>
  </div>
</div>

<!-- Modal Sous-cat\xE9gorie -->
<div *ngIf="showSubCategoryModal()" class="modal-overlay" (click)="closeSubCategoryModal()">
  <div class="modal-content" (click)="$event.stopPropagation()">
    <div class="modal-header">
      <h3>{{ editingSousCategory() ? 'Modifier la sous-cat\xE9gorie' : 'Nouvelle sous-cat\xE9gorie' }}</h3>
      <button class="btn-close" (click)="closeSubCategoryModal()">
        <i class="icon-x"></i>
      </button>
    </div>
    
    <form [formGroup]="subCategoryForm" (ngSubmit)="saveSubCategoryForm()" class="modal-form">
      <div class="form-group">
        <label for="subCategoryName">Nom *</label>
        <input 
          id="subCategoryName"
          type="text" 
          formControlName="nom"
          class="form-input"
          placeholder="Nom de la sous-cat\xE9gorie">
        <div *ngIf="subCategoryForm.get('nom')?.invalid && subCategoryForm.get('nom')?.touched" class="error-text">
          Le nom est requis (minimum 2 caract\xE8res)
        </div>
      </div>
      
      <div class="form-group">
        <label for="parentCategory">Cat\xE9gorie parent *</label>
        <select 
          id="parentCategory"
          formControlName="categorieId"
          class="form-select">
          <option value="">S\xE9lectionner une cat\xE9gorie</option>
          <option *ngFor="let category of categories()" [value]="category.id">
            {{ category.nom }}
          </option>
        </select>
        <div *ngIf="subCategoryForm.get('categorieId')?.invalid && subCategoryForm.get('categorieId')?.touched" class="error-text">
          La cat\xE9gorie parent est requise
        </div>
      </div>
      
      <div class="form-group">
        <label for="subCategoryDescription">Description</label>
        <textarea 
          id="subCategoryDescription"
          formControlName="description"
          class="form-textarea"
          placeholder="Description de la sous-cat\xE9gorie"
          rows="3"></textarea>
      </div>
      
      <div class="modal-actions">
        <button type="button" class="btn btn-secondary" (click)="closeSubCategoryModal()">
          Annuler
        </button>
        <button type="submit" class="btn btn-primary" [disabled]="subCategoryForm.invalid">
          {{ editingSousCategory() ? 'Modifier' : 'Cr\xE9er' }}
        </button>
      </div>
    </form>
  </div>
</div>
`, styles: ['/* src/app/components/admin/category-management/category-management.component.css */\n.category-management-container {\n  padding: 2rem;\n  max-width: 1400px;\n  margin: 0 auto;\n}\n.page-header {\n  margin-bottom: 2rem;\n}\n.header-content {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  flex-wrap: wrap;\n  gap: 1rem;\n}\n.page-title {\n  display: flex;\n  align-items: center;\n  gap: 0.75rem;\n  font-size: 2rem;\n  font-weight: 600;\n  color: #1a202c;\n  margin: 0;\n}\n.header-actions {\n  display: flex;\n  gap: 1rem;\n}\n.tabs-container {\n  margin-bottom: 2rem;\n}\n.tabs {\n  display: flex;\n  border-bottom: 2px solid #e5e7eb;\n}\n.tab-button {\n  background: none;\n  border: none;\n  padding: 1rem 2rem;\n  cursor: pointer;\n  font-weight: 500;\n  color: #64748b;\n  border-bottom: 2px solid transparent;\n  transition: all 0.2s;\n  display: flex;\n  align-items: center;\n  gap: 0.5rem;\n}\n.tab-button:hover {\n  color: #3b82f6;\n}\n.tab-button.active {\n  color: #3b82f6;\n  border-bottom-color: #3b82f6;\n}\n.section-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 1.5rem;\n  padding: 0 1rem;\n}\n.section-header h2 {\n  font-size: 1.5rem;\n  font-weight: 600;\n  color: #1a202c;\n  margin: 0;\n}\n.table-container {\n  background: white;\n  border-radius: 12px;\n  overflow: hidden;\n  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);\n}\n.data-table {\n  width: 100%;\n  border-collapse: collapse;\n}\n.data-table th {\n  background: #f8fafc;\n  padding: 1rem;\n  text-align: left;\n  font-weight: 600;\n  color: #374151;\n  border-bottom: 1px solid #e5e7eb;\n}\n.data-row {\n  border-bottom: 1px solid #f3f4f6;\n  transition: background-color 0.2s;\n}\n.data-row:hover {\n  background: #f9fafb;\n}\n.data-table td {\n  padding: 1rem;\n  vertical-align: middle;\n}\n.name-cell {\n  display: flex;\n  flex-direction: column;\n}\n.item-name {\n  font-weight: 600;\n  color: #1a202c;\n}\n.item-id {\n  font-size: 0.875rem;\n  color: #64748b;\n}\n.description-cell {\n  max-width: 300px;\n}\n.no-description {\n  color: #9ca3af;\n  font-style: italic;\n}\n.parent-cell {\n  color: #4f46e5;\n  font-weight: 500;\n}\n.count-cell {\n  text-align: center;\n  font-weight: 600;\n  color: #059669;\n}\n.date-cell {\n  color: #64748b;\n  font-size: 0.875rem;\n}\n.status-badge {\n  padding: 0.25rem 0.75rem;\n  border-radius: 9999px;\n  font-size: 0.875rem;\n  font-weight: 500;\n}\n.status-validated {\n  background: #d1fae5;\n  color: #059669;\n}\n.status-pending {\n  background: #fef3c7;\n  color: #d97706;\n}\n.actions-cell {\n  width: 160px;\n}\n.action-buttons {\n  display: flex;\n  gap: 0.5rem;\n}\n.btn-action {\n  width: 32px;\n  height: 32px;\n  border: none;\n  border-radius: 6px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  cursor: pointer;\n  transition: all 0.2s;\n}\n.btn-edit {\n  background: #dbeafe;\n  color: #2563eb;\n}\n.btn-edit:hover {\n  background: #bfdbfe;\n}\n.btn-validate {\n  background: #d1fae5;\n  color: #059669;\n}\n.btn-validate:hover {\n  background: #a7f3d0;\n}\n.btn-reject {\n  background: #fef3c7;\n  color: #d97706;\n}\n.btn-reject:hover {\n  background: #fde68a;\n}\n.btn-delete {\n  background: #fee2e2;\n  color: #dc2626;\n}\n.btn-delete:hover {\n  background: #fecaca;\n}\n.btn {\n  padding: 0.75rem 1.5rem;\n  border: none;\n  border-radius: 8px;\n  font-weight: 500;\n  cursor: pointer;\n  transition: all 0.2s;\n  display: flex;\n  align-items: center;\n  gap: 0.5rem;\n}\n.btn-primary {\n  background: #3b82f6;\n  color: white;\n}\n.btn-primary:hover {\n  background: #2563eb;\n}\n.btn-secondary {\n  background: #f3f4f6;\n  color: #374151;\n}\n.btn-secondary:hover {\n  background: #e5e7eb;\n}\n.btn:disabled {\n  opacity: 0.5;\n  cursor: not-allowed;\n}\n.modal-overlay {\n  position: fixed;\n  top: 0;\n  left: 0;\n  right: 0;\n  bottom: 0;\n  background: rgba(0, 0, 0, 0.5);\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  z-index: 1000;\n}\n.modal-content {\n  background: white;\n  border-radius: 12px;\n  width: 90%;\n  max-width: 500px;\n  max-height: 90vh;\n  overflow-y: auto;\n}\n.modal-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  padding: 1.5rem;\n  border-bottom: 1px solid #e5e7eb;\n}\n.modal-header h3 {\n  margin: 0;\n  font-size: 1.25rem;\n  font-weight: 600;\n  color: #1a202c;\n}\n.btn-close {\n  background: none;\n  border: none;\n  font-size: 1.5rem;\n  cursor: pointer;\n  color: #64748b;\n  padding: 0.25rem;\n}\n.btn-close:hover {\n  color: #374151;\n}\n.modal-form {\n  padding: 1.5rem;\n}\n.form-group {\n  margin-bottom: 1.5rem;\n}\n.form-group label {\n  display: block;\n  margin-bottom: 0.5rem;\n  font-weight: 500;\n  color: #374151;\n}\n.form-input,\n.form-textarea,\n.form-select {\n  width: 100%;\n  padding: 0.75rem;\n  border: 2px solid #e5e7eb;\n  border-radius: 8px;\n  font-size: 1rem;\n  transition: border-color 0.2s;\n}\n.form-input:focus,\n.form-textarea:focus,\n.form-select:focus {\n  outline: none;\n  border-color: #3b82f6;\n}\n.form-textarea {\n  resize: vertical;\n  min-height: 80px;\n}\n.error-text {\n  color: #dc2626;\n  font-size: 0.875rem;\n  margin-top: 0.25rem;\n}\n.modal-actions {\n  display: flex;\n  gap: 1rem;\n  justify-content: flex-end;\n  margin-top: 2rem;\n}\n.error-message {\n  background: #fef2f2;\n  border: 1px solid #fecaca;\n  color: #dc2626;\n  padding: 1rem;\n  border-radius: 8px;\n  margin-bottom: 2rem;\n  display: flex;\n  align-items: center;\n  gap: 0.5rem;\n}\n.loading-container {\n  text-align: center;\n  padding: 3rem;\n}\n.loading-spinner {\n  width: 40px;\n  height: 40px;\n  border: 4px solid #e2e8f0;\n  border-top: 4px solid #3b82f6;\n  border-radius: 50%;\n  animation: spin 1s linear infinite;\n  margin: 0 auto 1rem;\n}\n@keyframes spin {\n  0% {\n    transform: rotate(0deg);\n  }\n  100% {\n    transform: rotate(360deg);\n  }\n}\n.no-data {\n  text-align: center;\n  padding: 3rem;\n  color: #64748b;\n}\n.no-data i {\n  font-size: 3rem;\n  margin-bottom: 1rem;\n  opacity: 0.5;\n}\n.no-data h3 {\n  margin: 0 0 0.5rem 0;\n  color: #374151;\n}\n.icon-folder::before {\n  content: "\\1f4c1";\n}\n.icon-folder-open::before {\n  content: "\\1f4c2";\n}\n.icon-refresh::before {\n  content: "\\1f504";\n}\n.icon-plus::before {\n  content: "\\2795";\n}\n.icon-edit::before {\n  content: "\\270f\\fe0f";\n}\n.icon-check::before {\n  content: "\\2705";\n}\n.icon-x::before {\n  content: "\\274c";\n}\n.icon-trash::before {\n  content: "\\1f5d1\\fe0f";\n}\n.icon-alert::before {\n  content: "\\26a0\\fe0f";\n}\n@media (max-width: 768px) {\n  .category-management-container {\n    padding: 1rem;\n  }\n  .header-content {\n    flex-direction: column;\n    align-items: stretch;\n  }\n  .tabs {\n    flex-direction: column;\n  }\n  .tab-button {\n    padding: 0.75rem 1rem;\n  }\n  .section-header {\n    flex-direction: column;\n    gap: 1rem;\n    align-items: stretch;\n  }\n  .data-table {\n    font-size: 0.875rem;\n  }\n  .data-table th,\n  .data-table td {\n    padding: 0.75rem 0.5rem;\n  }\n  .modal-content {\n    width: 95%;\n    margin: 1rem;\n  }\n}\n/*# sourceMappingURL=category-management.component.css.map */\n'] }]
  }], () => [{ type: AdminService }, { type: CategorieService }, { type: SousCategorieService }, { type: FormBuilder }], null);
})();
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && \u0275setClassDebugInfo(CategoryManagementComponent, { className: "CategoryManagementComponent", filePath: "src/app/components/admin/category-management/category-management.component.ts", lineNumber: 36 });
})();
export {
  CategoryManagementComponent
};
//# sourceMappingURL=chunk-SRX6LHHQ.js.map

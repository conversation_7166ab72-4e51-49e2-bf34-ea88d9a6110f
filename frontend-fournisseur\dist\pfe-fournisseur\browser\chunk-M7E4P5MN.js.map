{"version": 3, "sources": ["src/app/components/admin/demandes-management/demandes-management.component.ts"], "sourcesContent": ["import { Component, OnInit } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { FormsModule } from '@angular/forms';\nimport { DemandeService, DemandeCategorieDto, DemandeSousCategorieDto, StatutDemande, TraiterDemandeCategorieDto, TraiterDemandeSousCategorieDto } from '../../../services/demande.service';\n\n@Component({\n  selector: 'app-demandes-management',\n  standalone: true,\n  imports: [CommonModule, FormsModule],\n  template: `\n    <div class=\"demandes-management\">\n      <div class=\"page-header\">\n        <h1>🔔 Gestion des Demandes</h1>\n        <p>Gérez les demandes de création de catégories et sous-catégories</p>\n      </div>\n\n      <div class=\"tabs\">\n        <button \n          class=\"tab-btn\" \n          [class.active]=\"activeTab === 'categories'\"\n          (click)=\"activeTab = 'categories'; loadDemandesCategories()\">\n          Demandes de Catégories\n          <span *ngIf=\"demandesCategories.length > 0\" class=\"badge\">{{ demandesCategories.length }}</span>\n        </button>\n        <button \n          class=\"tab-btn\" \n          [class.active]=\"activeTab === 'sous-categories'\"\n          (click)=\"activeTab = 'sous-categories'; loadDemandesSousCategories()\">\n          Demandes de Sous-catégories\n          <span *ngIf=\"demandesSousCategories.length > 0\" class=\"badge\">{{ demandesSousCategories.length }}</span>\n        </button>\n      </div>\n\n      <!-- Demandes de catégories -->\n      <div *ngIf=\"activeTab === 'categories'\" class=\"tab-content\">\n        <div class=\"filters\">\n          <select [(ngModel)]=\"filtreStatutCategories\" (change)=\"loadDemandesCategories()\">\n            <option value=\"\">Tous les statuts</option>\n            <option value=\"0\">En attente</option>\n            <option value=\"1\">Approuvées</option>\n            <option value=\"2\">Rejetées</option>\n          </select>\n        </div>\n\n        <div *ngIf=\"demandesCategories.length === 0\" class=\"empty-state\">\n          <p>Aucune demande de catégorie trouvée</p>\n        </div>\n\n        <div class=\"demandes-grid\">\n          <div *ngFor=\"let demande of demandesCategories\" class=\"demande-card\">\n            <div class=\"demande-header\">\n              <h3>{{ demande.nom }}</h3>\n              <span class=\"statut-badge\" [style.background-color]=\"getStatutColor(demande.statut)\">\n                {{ getStatutLabel(demande.statut) }}\n              </span>\n            </div>\n            \n            <div class=\"demande-content\">\n              <p><strong>Description:</strong> {{ demande.description }}</p>\n              <p><strong>Demandé par:</strong> {{ demande.fournisseurNom }}</p>\n              <p><strong>Date de demande:</strong> {{ formatDate(demande.dateDemande) }}</p>\n              \n              <div *ngIf=\"demande.imageUrl\" class=\"image-preview\">\n                <img [src]=\"demande.imageUrl\" alt=\"Image de la catégorie\" />\n              </div>\n            </div>\n\n            <div *ngIf=\"demande.statut === 0\" class=\"demande-actions\">\n              <button class=\"btn btn-success\" (click)=\"traiterDemandeCategorie(demande.id, 1)\">\n                ✅ Approuver\n              </button>\n              <button class=\"btn btn-danger\" (click)=\"openRejectModal(demande, 'categorie')\">\n                ❌ Rejeter\n              </button>\n            </div>\n\n            <div *ngIf=\"demande.statut !== 0 && demande.commentaireAdmin\" class=\"admin-comment\">\n              <p><strong>Commentaire admin:</strong> {{ demande.commentaireAdmin }}</p>\n              <p><strong>Traité le:</strong> {{ formatDate(demande.dateTraitement!) }}</p>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      <!-- Demandes de sous-catégories -->\n      <div *ngIf=\"activeTab === 'sous-categories'\" class=\"tab-content\">\n        <div class=\"filters\">\n          <select [(ngModel)]=\"filtreStatutSousCategories\" (change)=\"loadDemandesSousCategories()\">\n            <option value=\"\">Tous les statuts</option>\n            <option value=\"0\">En attente</option>\n            <option value=\"1\">Approuvées</option>\n            <option value=\"2\">Rejetées</option>\n          </select>\n        </div>\n\n        <div *ngIf=\"demandesSousCategories.length === 0\" class=\"empty-state\">\n          <p>Aucune demande de sous-catégorie trouvée</p>\n        </div>\n\n        <div class=\"demandes-grid\">\n          <div *ngFor=\"let demande of demandesSousCategories\" class=\"demande-card\">\n            <div class=\"demande-header\">\n              <h3>{{ demande.nom }}</h3>\n              <span class=\"statut-badge\" [style.background-color]=\"getStatutColor(demande.statut)\">\n                {{ getStatutLabel(demande.statut) }}\n              </span>\n            </div>\n            \n            <div class=\"demande-content\">\n              <p><strong>Description:</strong> {{ demande.description }}</p>\n              <p><strong>Catégorie parent:</strong> {{ demande.categorieNom }}</p>\n              <p><strong>Demandé par:</strong> {{ demande.fournisseurNom }}</p>\n              <p><strong>Date de demande:</strong> {{ formatDate(demande.dateDemande) }}</p>\n              \n              <div *ngIf=\"demande.imageUrl\" class=\"image-preview\">\n                <img [src]=\"demande.imageUrl\" alt=\"Image de la sous-catégorie\" />\n              </div>\n            </div>\n\n            <div *ngIf=\"demande.statut === 0\" class=\"demande-actions\">\n              <button class=\"btn btn-success\" (click)=\"traiterDemandeSousCategorie(demande.id, 1)\">\n                ✅ Approuver\n              </button>\n              <button class=\"btn btn-danger\" (click)=\"openRejectModal(demande, 'sous-categorie')\">\n                ❌ Rejeter\n              </button>\n            </div>\n\n            <div *ngIf=\"demande.statut !== 0 && demande.commentaireAdmin\" class=\"admin-comment\">\n              <p><strong>Commentaire admin:</strong> {{ demande.commentaireAdmin }}</p>\n              <p><strong>Traité le:</strong> {{ formatDate(demande.dateTraitement!) }}</p>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      <!-- Modal de rejet -->\n      <div *ngIf=\"showRejectModal\" class=\"modal-overlay\" (click)=\"closeRejectModal()\">\n        <div class=\"modal\" (click)=\"$event.stopPropagation()\">\n          <div class=\"modal-header\">\n            <h3>Rejeter la demande</h3>\n            <button class=\"close-btn\" (click)=\"closeRejectModal()\">×</button>\n          </div>\n          <div class=\"modal-content\">\n            <p>Êtes-vous sûr de vouloir rejeter cette demande ?</p>\n            <textarea \n              [(ngModel)]=\"commentaireRejet\" \n              placeholder=\"Commentaire (optionnel)\"\n              rows=\"3\">\n            </textarea>\n          </div>\n          <div class=\"modal-actions\">\n            <button class=\"btn btn-secondary\" (click)=\"closeRejectModal()\">Annuler</button>\n            <button class=\"btn btn-danger\" (click)=\"confirmReject()\">Rejeter</button>\n          </div>\n        </div>\n      </div>\n    </div>\n  `,\n  styles: [`\n    .demandes-management {\n      padding: 2rem;\n    }\n\n    .page-header {\n      margin-bottom: 2rem;\n    }\n\n    .page-header h1 {\n      margin: 0 0 0.5rem 0;\n      color: #1e293b;\n    }\n\n    .page-header p {\n      margin: 0;\n      color: #64748b;\n    }\n\n    .tabs {\n      display: flex;\n      border-bottom: 2px solid #e2e8f0;\n      margin-bottom: 2rem;\n    }\n\n    .tab-btn {\n      padding: 1rem 2rem;\n      background: none;\n      border: none;\n      cursor: pointer;\n      font-size: 1rem;\n      color: #64748b;\n      border-bottom: 2px solid transparent;\n      transition: all 0.3s ease;\n      position: relative;\n    }\n\n    .tab-btn.active {\n      color: #3b82f6;\n      border-bottom-color: #3b82f6;\n    }\n\n    .badge {\n      background: #ef4444;\n      color: white;\n      border-radius: 50%;\n      padding: 0.25rem 0.5rem;\n      font-size: 0.75rem;\n      margin-left: 0.5rem;\n    }\n\n    .filters {\n      margin-bottom: 1.5rem;\n    }\n\n    .filters select {\n      padding: 0.5rem 1rem;\n      border: 1px solid #d1d5db;\n      border-radius: 8px;\n      background: white;\n    }\n\n    .empty-state {\n      text-align: center;\n      padding: 3rem;\n      color: #64748b;\n    }\n\n    .demandes-grid {\n      display: grid;\n      grid-template-columns: repeat(auto-fill, minmax(400px, 1fr));\n      gap: 1.5rem;\n    }\n\n    .demande-card {\n      background: white;\n      border-radius: 12px;\n      padding: 1.5rem;\n      box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);\n      border: 1px solid #e2e8f0;\n    }\n\n    .demande-header {\n      display: flex;\n      justify-content: space-between;\n      align-items: center;\n      margin-bottom: 1rem;\n    }\n\n    .demande-header h3 {\n      margin: 0;\n      color: #1e293b;\n    }\n\n    .statut-badge {\n      padding: 0.25rem 0.75rem;\n      border-radius: 20px;\n      color: white;\n      font-size: 0.875rem;\n      font-weight: 500;\n    }\n\n    .demande-content p {\n      margin: 0.5rem 0;\n      color: #475569;\n    }\n\n    .image-preview {\n      margin: 1rem 0;\n    }\n\n    .image-preview img {\n      max-width: 100px;\n      max-height: 100px;\n      border-radius: 8px;\n      object-fit: cover;\n    }\n\n    .demande-actions {\n      display: flex;\n      gap: 0.5rem;\n      margin-top: 1rem;\n      padding-top: 1rem;\n      border-top: 1px solid #e2e8f0;\n    }\n\n    .admin-comment {\n      margin-top: 1rem;\n      padding-top: 1rem;\n      border-top: 1px solid #e2e8f0;\n      background: #f8fafc;\n      padding: 1rem;\n      border-radius: 8px;\n    }\n\n    .btn {\n      padding: 0.5rem 1rem;\n      border: none;\n      border-radius: 6px;\n      cursor: pointer;\n      font-size: 0.875rem;\n      transition: all 0.3s ease;\n    }\n\n    .btn-success {\n      background: #10b981;\n      color: white;\n    }\n\n    .btn-success:hover {\n      background: #059669;\n    }\n\n    .btn-danger {\n      background: #ef4444;\n      color: white;\n    }\n\n    .btn-danger:hover {\n      background: #dc2626;\n    }\n\n    .btn-secondary {\n      background: #6b7280;\n      color: white;\n    }\n\n    .btn-secondary:hover {\n      background: #4b5563;\n    }\n\n    .modal-overlay {\n      position: fixed;\n      top: 0;\n      left: 0;\n      right: 0;\n      bottom: 0;\n      background: rgba(0, 0, 0, 0.5);\n      display: flex;\n      align-items: center;\n      justify-content: center;\n      z-index: 1000;\n    }\n\n    .modal {\n      background: white;\n      border-radius: 12px;\n      width: 90%;\n      max-width: 500px;\n      max-height: 90vh;\n      overflow-y: auto;\n    }\n\n    .modal-header {\n      display: flex;\n      justify-content: space-between;\n      align-items: center;\n      padding: 1.5rem;\n      border-bottom: 1px solid #e2e8f0;\n    }\n\n    .modal-header h3 {\n      margin: 0;\n    }\n\n    .close-btn {\n      background: none;\n      border: none;\n      font-size: 1.5rem;\n      cursor: pointer;\n      color: #6b7280;\n    }\n\n    .modal-content {\n      padding: 1.5rem;\n    }\n\n    .modal-content textarea {\n      width: 100%;\n      padding: 0.75rem;\n      border: 1px solid #d1d5db;\n      border-radius: 8px;\n      margin-top: 1rem;\n      resize: vertical;\n    }\n\n    .modal-actions {\n      display: flex;\n      justify-content: flex-end;\n      gap: 0.5rem;\n      padding: 1.5rem;\n      border-top: 1px solid #e2e8f0;\n    }\n  `]\n})\nexport class DemandesManagementComponent implements OnInit {\n  activeTab: 'categories' | 'sous-categories' = 'categories';\n  demandesCategories: DemandeCategorieDto[] = [];\n  demandesSousCategories: DemandeSousCategorieDto[] = [];\n  filtreStatutCategories = '';\n  filtreStatutSousCategories = '';\n  \n  showRejectModal = false;\n  currentDemande: any = null;\n  currentType: 'categorie' | 'sous-categorie' = 'categorie';\n  commentaireRejet = '';\n\n  constructor(private demandeService: DemandeService) {}\n\n  ngOnInit() {\n    this.loadDemandesCategories();\n  }\n\n  loadDemandesCategories() {\n    console.log('🔄 Chargement des demandes de catégories (Admin)...');\n    if (this.filtreStatutCategories) {\n      const statut = parseInt(this.filtreStatutCategories) as StatutDemande;\n      console.log('📊 Filtrage par statut:', statut);\n      this.demandeService.getDemandesCategoriesByStatut(statut).subscribe({\n        next: (demandes) => {\n          console.log('✅ Demandes reçues (par statut):', demandes);\n          this.demandesCategories = demandes;\n        },\n        error: (error) => {\n          console.error('❌ Erreur lors du chargement des demandes par statut:', error);\n        }\n      });\n    } else {\n      console.log('📋 Chargement de toutes les demandes...');\n      this.demandeService.getAllDemandesCategories().subscribe({\n        next: (demandes) => {\n          console.log('✅ Toutes les demandes reçues:', demandes);\n          this.demandesCategories = demandes;\n        },\n        error: (error) => {\n          console.error('❌ Erreur lors du chargement de toutes les demandes:', error);\n        }\n      });\n    }\n  }\n\n  loadDemandesSousCategories() {\n    if (this.filtreStatutSousCategories) {\n      const statut = parseInt(this.filtreStatutSousCategories) as StatutDemande;\n      this.demandeService.getDemandesSousCategoriesByStatut(statut).subscribe(\n        demandes => this.demandesSousCategories = demandes\n      );\n    } else {\n      this.demandeService.getAllDemandesSousCategories().subscribe(\n        demandes => this.demandesSousCategories = demandes\n      );\n    }\n  }\n\n  traiterDemandeCategorie(id: number, statut: StatutDemande) {\n    const traitement: TraiterDemandeCategorieDto = { statut };\n    this.demandeService.traiterDemandeCategorie(id, traitement).subscribe(() => {\n      this.loadDemandesCategories();\n    });\n  }\n\n  traiterDemandeSousCategorie(id: number, statut: StatutDemande) {\n    const traitement: TraiterDemandeSousCategorieDto = { statut };\n    this.demandeService.traiterDemandeSousCategorie(id, traitement).subscribe(() => {\n      this.loadDemandesSousCategories();\n    });\n  }\n\n  openRejectModal(demande: any, type: 'categorie' | 'sous-categorie') {\n    this.currentDemande = demande;\n    this.currentType = type;\n    this.commentaireRejet = '';\n    this.showRejectModal = true;\n  }\n\n  closeRejectModal() {\n    this.showRejectModal = false;\n    this.currentDemande = null;\n    this.commentaireRejet = '';\n  }\n\n  confirmReject() {\n    if (this.currentDemande) {\n      const traitement = {\n        statut: StatutDemande.Rejetee,\n        commentaireAdmin: this.commentaireRejet || undefined\n      };\n\n      if (this.currentType === 'categorie') {\n        this.demandeService.traiterDemandeCategorie(this.currentDemande.id, traitement).subscribe(() => {\n          this.loadDemandesCategories();\n          this.closeRejectModal();\n        });\n      } else {\n        this.demandeService.traiterDemandeSousCategorie(this.currentDemande.id, traitement).subscribe(() => {\n          this.loadDemandesSousCategories();\n          this.closeRejectModal();\n        });\n      }\n    }\n  }\n\n  getStatutLabel(statut: StatutDemande): string {\n    return this.demandeService.getStatutLabel(statut);\n  }\n\n  getStatutColor(statut: StatutDemande): string {\n    return this.demandeService.getStatutColor(statut);\n  }\n\n  formatDate(date: Date): string {\n    return new Date(date).toLocaleDateString('fr-FR', {\n      year: 'numeric',\n      month: 'long',\n      day: 'numeric',\n      hour: '2-digit',\n      minute: '2-digit'\n    });\n  }\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAsBU,IAAA,yBAAA,GAAA,QAAA,CAAA;AAA0D,IAAA,iBAAA,CAAA;AAA+B,IAAA,uBAAA;;;;AAA/B,IAAA,oBAAA;AAAA,IAAA,4BAAA,OAAA,mBAAA,MAAA;;;;;AAO1D,IAAA,yBAAA,GAAA,QAAA,CAAA;AAA8D,IAAA,iBAAA,CAAA;AAAmC,IAAA,uBAAA;;;;AAAnC,IAAA,oBAAA;AAAA,IAAA,4BAAA,OAAA,uBAAA,MAAA;;;;;AAehE,IAAA,yBAAA,GAAA,OAAA,EAAA,EAAiE,GAAA,GAAA;AAC5D,IAAA,iBAAA,GAAA,2CAAA;AAAmC,IAAA,uBAAA,EAAI;;;;;AAiBtC,IAAA,yBAAA,GAAA,OAAA,EAAA;AACE,IAAA,oBAAA,GAAA,OAAA,EAAA;AACF,IAAA,uBAAA;;;;AADO,IAAA,oBAAA;AAAA,IAAA,qBAAA,OAAA,WAAA,UAAA,uBAAA;;;;;;AAIT,IAAA,yBAAA,GAAA,OAAA,EAAA,EAA0D,GAAA,UAAA,EAAA;AACxB,IAAA,qBAAA,SAAA,SAAA,oFAAA;AAAA,MAAA,wBAAA,GAAA;AAAA,YAAA,aAAA,wBAAA,EAAA;AAAA,YAAA,SAAA,wBAAA,CAAA;AAAA,aAAA,sBAAS,OAAA,wBAAA,WAAA,IAAoC,CAAC,CAAC;IAAA,CAAA;AAC7E,IAAA,iBAAA,GAAA,oBAAA;AACF,IAAA,uBAAA;AACA,IAAA,yBAAA,GAAA,UAAA,EAAA;AAA+B,IAAA,qBAAA,SAAA,SAAA,oFAAA;AAAA,MAAA,wBAAA,GAAA;AAAA,YAAA,aAAA,wBAAA,EAAA;AAAA,YAAA,SAAA,wBAAA,CAAA;AAAA,aAAA,sBAAS,OAAA,gBAAA,YAAyB,WAAW,CAAC;IAAA,CAAA;AAC3E,IAAA,iBAAA,GAAA,kBAAA;AACF,IAAA,uBAAA,EAAS;;;;;AAGX,IAAA,yBAAA,GAAA,OAAA,EAAA,EAAoF,GAAA,GAAA,EAC/E,GAAA,QAAA;AAAQ,IAAA,iBAAA,GAAA,oBAAA;AAAkB,IAAA,uBAAA;AAAU,IAAA,iBAAA,CAAA;AAA8B,IAAA,uBAAA;AACrE,IAAA,yBAAA,GAAA,GAAA,EAAG,GAAA,QAAA;AAAQ,IAAA,iBAAA,GAAA,eAAA;AAAU,IAAA,uBAAA;AAAU,IAAA,iBAAA,CAAA;AAAyC,IAAA,uBAAA,EAAI;;;;;AADrC,IAAA,oBAAA,CAAA;AAAA,IAAA,6BAAA,KAAA,WAAA,kBAAA,EAAA;AACR,IAAA,oBAAA,CAAA;AAAA,IAAA,6BAAA,KAAA,OAAA,WAAA,WAAA,cAAA,GAAA,EAAA;;;;;AA7BnC,IAAA,yBAAA,GAAA,OAAA,EAAA,EAAqE,GAAA,OAAA,EAAA,EACvC,GAAA,IAAA;AACtB,IAAA,iBAAA,CAAA;AAAiB,IAAA,uBAAA;AACrB,IAAA,yBAAA,GAAA,QAAA,EAAA;AACE,IAAA,iBAAA,CAAA;AACF,IAAA,uBAAA,EAAO;AAGT,IAAA,yBAAA,GAAA,OAAA,EAAA,EAA6B,GAAA,GAAA,EACxB,GAAA,QAAA;AAAQ,IAAA,iBAAA,GAAA,cAAA;AAAY,IAAA,uBAAA;AAAU,IAAA,iBAAA,EAAA;AAAyB,IAAA,uBAAA;AAC1D,IAAA,yBAAA,IAAA,GAAA,EAAG,IAAA,QAAA;AAAQ,IAAA,iBAAA,IAAA,iBAAA;AAAY,IAAA,uBAAA;AAAU,IAAA,iBAAA,EAAA;AAA4B,IAAA,uBAAA;AAC7D,IAAA,yBAAA,IAAA,GAAA,EAAG,IAAA,QAAA;AAAQ,IAAA,iBAAA,IAAA,kBAAA;AAAgB,IAAA,uBAAA;AAAU,IAAA,iBAAA,EAAA;AAAqC,IAAA,uBAAA;AAE1E,IAAA,qBAAA,IAAA,2DAAA,GAAA,GAAA,OAAA,EAAA;AAGF,IAAA,uBAAA;AAEA,IAAA,qBAAA,IAAA,2DAAA,GAAA,GAAA,OAAA,EAAA,EAA0D,IAAA,2DAAA,GAAA,GAAA,OAAA,EAAA;AAa5D,IAAA,uBAAA;;;;;AA7BQ,IAAA,oBAAA,CAAA;AAAA,IAAA,4BAAA,WAAA,GAAA;AACuB,IAAA,oBAAA;AAAA,IAAA,sBAAA,oBAAA,OAAA,eAAA,WAAA,MAAA,CAAA;AACzB,IAAA,oBAAA;AAAA,IAAA,6BAAA,KAAA,OAAA,eAAA,WAAA,MAAA,GAAA,GAAA;AAK+B,IAAA,oBAAA,CAAA;AAAA,IAAA,6BAAA,KAAA,WAAA,aAAA,EAAA;AACA,IAAA,oBAAA,CAAA;AAAA,IAAA,6BAAA,KAAA,WAAA,gBAAA,EAAA;AACI,IAAA,oBAAA,CAAA;AAAA,IAAA,6BAAA,KAAA,OAAA,WAAA,WAAA,WAAA,GAAA,EAAA;AAE/B,IAAA,oBAAA;AAAA,IAAA,qBAAA,QAAA,WAAA,QAAA;AAKF,IAAA,oBAAA;AAAA,IAAA,qBAAA,QAAA,WAAA,WAAA,CAAA;AASA,IAAA,oBAAA;AAAA,IAAA,qBAAA,QAAA,WAAA,WAAA,KAAA,WAAA,gBAAA;;;;;;AA1CZ,IAAA,yBAAA,GAAA,OAAA,CAAA,EAA4D,GAAA,OAAA,CAAA,EACrC,GAAA,UAAA,EAAA;AACX,IAAA,2BAAA,iBAAA,SAAA,4EAAA,QAAA;AAAA,MAAA,wBAAA,GAAA;AAAA,YAAA,SAAA,wBAAA;AAAA,MAAA,6BAAA,OAAA,wBAAA,MAAA,MAAA,OAAA,yBAAA;AAAA,aAAA,sBAAA,MAAA;IAAA,CAAA;AAAqC,IAAA,qBAAA,UAAA,SAAA,uEAAA;AAAA,MAAA,wBAAA,GAAA;AAAA,YAAA,SAAA,wBAAA;AAAA,aAAA,sBAAU,OAAA,uBAAA,CAAwB;IAAA,CAAA;AAC7E,IAAA,yBAAA,GAAA,UAAA,EAAA;AAAiB,IAAA,iBAAA,GAAA,kBAAA;AAAgB,IAAA,uBAAA;AACjC,IAAA,yBAAA,GAAA,UAAA,EAAA;AAAkB,IAAA,iBAAA,GAAA,YAAA;AAAU,IAAA,uBAAA;AAC5B,IAAA,yBAAA,GAAA,UAAA,EAAA;AAAkB,IAAA,iBAAA,GAAA,eAAA;AAAU,IAAA,uBAAA;AAC5B,IAAA,yBAAA,GAAA,UAAA,EAAA;AAAkB,IAAA,iBAAA,IAAA,aAAA;AAAQ,IAAA,uBAAA,EAAS,EAC5B;AAGX,IAAA,qBAAA,IAAA,oDAAA,GAAA,GAAA,OAAA,EAAA;AAIA,IAAA,yBAAA,IAAA,OAAA,EAAA;AACE,IAAA,qBAAA,IAAA,oDAAA,IAAA,IAAA,OAAA,EAAA;AAgCF,IAAA,uBAAA,EAAM;;;;AA7CI,IAAA,oBAAA,CAAA;AAAA,IAAA,2BAAA,WAAA,OAAA,sBAAA;AAQJ,IAAA,oBAAA,CAAA;AAAA,IAAA,qBAAA,QAAA,OAAA,mBAAA,WAAA,CAAA;AAKqB,IAAA,oBAAA,CAAA;AAAA,IAAA,qBAAA,WAAA,OAAA,kBAAA;;;;;AA8C3B,IAAA,yBAAA,GAAA,OAAA,EAAA,EAAqE,GAAA,GAAA;AAChE,IAAA,iBAAA,GAAA,gDAAA;AAAwC,IAAA,uBAAA,EAAI;;;;;AAkB3C,IAAA,yBAAA,GAAA,OAAA,EAAA;AACE,IAAA,oBAAA,GAAA,OAAA,EAAA;AACF,IAAA,uBAAA;;;;AADO,IAAA,oBAAA;AAAA,IAAA,qBAAA,OAAA,WAAA,UAAA,uBAAA;;;;;;AAIT,IAAA,yBAAA,GAAA,OAAA,EAAA,EAA0D,GAAA,UAAA,EAAA;AACxB,IAAA,qBAAA,SAAA,SAAA,oFAAA;AAAA,MAAA,wBAAA,GAAA;AAAA,YAAA,aAAA,wBAAA,EAAA;AAAA,YAAA,SAAA,wBAAA,CAAA;AAAA,aAAA,sBAAS,OAAA,4BAAA,WAAA,IAAwC,CAAC,CAAC;IAAA,CAAA;AACjF,IAAA,iBAAA,GAAA,oBAAA;AACF,IAAA,uBAAA;AACA,IAAA,yBAAA,GAAA,UAAA,EAAA;AAA+B,IAAA,qBAAA,SAAA,SAAA,oFAAA;AAAA,MAAA,wBAAA,GAAA;AAAA,YAAA,aAAA,wBAAA,EAAA;AAAA,YAAA,SAAA,wBAAA,CAAA;AAAA,aAAA,sBAAS,OAAA,gBAAA,YAAyB,gBAAgB,CAAC;IAAA,CAAA;AAChF,IAAA,iBAAA,GAAA,kBAAA;AACF,IAAA,uBAAA,EAAS;;;;;AAGX,IAAA,yBAAA,GAAA,OAAA,EAAA,EAAoF,GAAA,GAAA,EAC/E,GAAA,QAAA;AAAQ,IAAA,iBAAA,GAAA,oBAAA;AAAkB,IAAA,uBAAA;AAAU,IAAA,iBAAA,CAAA;AAA8B,IAAA,uBAAA;AACrE,IAAA,yBAAA,GAAA,GAAA,EAAG,GAAA,QAAA;AAAQ,IAAA,iBAAA,GAAA,eAAA;AAAU,IAAA,uBAAA;AAAU,IAAA,iBAAA,CAAA;AAAyC,IAAA,uBAAA,EAAI;;;;;AADrC,IAAA,oBAAA,CAAA;AAAA,IAAA,6BAAA,KAAA,WAAA,kBAAA,EAAA;AACR,IAAA,oBAAA,CAAA;AAAA,IAAA,6BAAA,KAAA,OAAA,WAAA,WAAA,cAAA,GAAA,EAAA;;;;;AA9BnC,IAAA,yBAAA,GAAA,OAAA,EAAA,EAAyE,GAAA,OAAA,EAAA,EAC3C,GAAA,IAAA;AACtB,IAAA,iBAAA,CAAA;AAAiB,IAAA,uBAAA;AACrB,IAAA,yBAAA,GAAA,QAAA,EAAA;AACE,IAAA,iBAAA,CAAA;AACF,IAAA,uBAAA,EAAO;AAGT,IAAA,yBAAA,GAAA,OAAA,EAAA,EAA6B,GAAA,GAAA,EACxB,GAAA,QAAA;AAAQ,IAAA,iBAAA,GAAA,cAAA;AAAY,IAAA,uBAAA;AAAU,IAAA,iBAAA,EAAA;AAAyB,IAAA,uBAAA;AAC1D,IAAA,yBAAA,IAAA,GAAA,EAAG,IAAA,QAAA;AAAQ,IAAA,iBAAA,IAAA,sBAAA;AAAiB,IAAA,uBAAA;AAAU,IAAA,iBAAA,EAAA;AAA0B,IAAA,uBAAA;AAChE,IAAA,yBAAA,IAAA,GAAA,EAAG,IAAA,QAAA;AAAQ,IAAA,iBAAA,IAAA,iBAAA;AAAY,IAAA,uBAAA;AAAU,IAAA,iBAAA,EAAA;AAA4B,IAAA,uBAAA;AAC7D,IAAA,yBAAA,IAAA,GAAA,EAAG,IAAA,QAAA;AAAQ,IAAA,iBAAA,IAAA,kBAAA;AAAgB,IAAA,uBAAA;AAAU,IAAA,iBAAA,EAAA;AAAqC,IAAA,uBAAA;AAE1E,IAAA,qBAAA,IAAA,2DAAA,GAAA,GAAA,OAAA,EAAA;AAGF,IAAA,uBAAA;AAEA,IAAA,qBAAA,IAAA,2DAAA,GAAA,GAAA,OAAA,EAAA,EAA0D,IAAA,2DAAA,GAAA,GAAA,OAAA,EAAA;AAa5D,IAAA,uBAAA;;;;;AA9BQ,IAAA,oBAAA,CAAA;AAAA,IAAA,4BAAA,WAAA,GAAA;AACuB,IAAA,oBAAA;AAAA,IAAA,sBAAA,oBAAA,OAAA,eAAA,WAAA,MAAA,CAAA;AACzB,IAAA,oBAAA;AAAA,IAAA,6BAAA,KAAA,OAAA,eAAA,WAAA,MAAA,GAAA,GAAA;AAK+B,IAAA,oBAAA,CAAA;AAAA,IAAA,6BAAA,KAAA,WAAA,aAAA,EAAA;AACK,IAAA,oBAAA,CAAA;AAAA,IAAA,6BAAA,KAAA,WAAA,cAAA,EAAA;AACL,IAAA,oBAAA,CAAA;AAAA,IAAA,6BAAA,KAAA,WAAA,gBAAA,EAAA;AACI,IAAA,oBAAA,CAAA;AAAA,IAAA,6BAAA,KAAA,OAAA,WAAA,WAAA,WAAA,GAAA,EAAA;AAE/B,IAAA,oBAAA;AAAA,IAAA,qBAAA,QAAA,WAAA,QAAA;AAKF,IAAA,oBAAA;AAAA,IAAA,qBAAA,QAAA,WAAA,WAAA,CAAA;AASA,IAAA,oBAAA;AAAA,IAAA,qBAAA,QAAA,WAAA,WAAA,KAAA,WAAA,gBAAA;;;;;;AA3CZ,IAAA,yBAAA,GAAA,OAAA,CAAA,EAAiE,GAAA,OAAA,CAAA,EAC1C,GAAA,UAAA,EAAA;AACX,IAAA,2BAAA,iBAAA,SAAA,4EAAA,QAAA;AAAA,MAAA,wBAAA,GAAA;AAAA,YAAA,SAAA,wBAAA;AAAA,MAAA,6BAAA,OAAA,4BAAA,MAAA,MAAA,OAAA,6BAAA;AAAA,aAAA,sBAAA,MAAA;IAAA,CAAA;AAAyC,IAAA,qBAAA,UAAA,SAAA,uEAAA;AAAA,MAAA,wBAAA,GAAA;AAAA,YAAA,SAAA,wBAAA;AAAA,aAAA,sBAAU,OAAA,2BAAA,CAA4B;IAAA,CAAA;AACrF,IAAA,yBAAA,GAAA,UAAA,EAAA;AAAiB,IAAA,iBAAA,GAAA,kBAAA;AAAgB,IAAA,uBAAA;AACjC,IAAA,yBAAA,GAAA,UAAA,EAAA;AAAkB,IAAA,iBAAA,GAAA,YAAA;AAAU,IAAA,uBAAA;AAC5B,IAAA,yBAAA,GAAA,UAAA,EAAA;AAAkB,IAAA,iBAAA,GAAA,eAAA;AAAU,IAAA,uBAAA;AAC5B,IAAA,yBAAA,GAAA,UAAA,EAAA;AAAkB,IAAA,iBAAA,IAAA,aAAA;AAAQ,IAAA,uBAAA,EAAS,EAC5B;AAGX,IAAA,qBAAA,IAAA,oDAAA,GAAA,GAAA,OAAA,EAAA;AAIA,IAAA,yBAAA,IAAA,OAAA,EAAA;AACE,IAAA,qBAAA,IAAA,oDAAA,IAAA,IAAA,OAAA,EAAA;AAiCF,IAAA,uBAAA,EAAM;;;;AA9CI,IAAA,oBAAA,CAAA;AAAA,IAAA,2BAAA,WAAA,OAAA,0BAAA;AAQJ,IAAA,oBAAA,CAAA;AAAA,IAAA,qBAAA,QAAA,OAAA,uBAAA,WAAA,CAAA;AAKqB,IAAA,oBAAA,CAAA;AAAA,IAAA,qBAAA,WAAA,OAAA,sBAAA;;;;;;AAqC7B,IAAA,yBAAA,GAAA,OAAA,EAAA;AAAmD,IAAA,qBAAA,SAAA,SAAA,mEAAA;AAAA,MAAA,wBAAA,GAAA;AAAA,YAAA,SAAA,wBAAA;AAAA,aAAA,sBAAS,OAAA,iBAAA,CAAkB;IAAA,CAAA;AAC5E,IAAA,yBAAA,GAAA,OAAA,EAAA;AAAmB,IAAA,qBAAA,SAAA,SAAA,iEAAA,QAAA;AAAA,MAAA,wBAAA,GAAA;AAAA,aAAA,sBAAS,OAAA,gBAAA,CAAwB;IAAA,CAAA;AAClD,IAAA,yBAAA,GAAA,OAAA,EAAA,EAA0B,GAAA,IAAA;AACpB,IAAA,iBAAA,GAAA,oBAAA;AAAkB,IAAA,uBAAA;AACtB,IAAA,yBAAA,GAAA,UAAA,EAAA;AAA0B,IAAA,qBAAA,SAAA,SAAA,sEAAA;AAAA,MAAA,wBAAA,GAAA;AAAA,YAAA,SAAA,wBAAA;AAAA,aAAA,sBAAS,OAAA,iBAAA,CAAkB;IAAA,CAAA;AAAE,IAAA,iBAAA,GAAA,MAAA;AAAC,IAAA,uBAAA,EAAS;AAEnE,IAAA,yBAAA,GAAA,OAAA,EAAA,EAA2B,GAAA,GAAA;AACtB,IAAA,iBAAA,GAAA,wDAAA;AAAgD,IAAA,uBAAA;AACnD,IAAA,yBAAA,IAAA,YAAA,EAAA;AACE,IAAA,2BAAA,iBAAA,SAAA,+EAAA,QAAA;AAAA,MAAA,wBAAA,GAAA;AAAA,YAAA,SAAA,wBAAA;AAAA,MAAA,6BAAA,OAAA,kBAAA,MAAA,MAAA,OAAA,mBAAA;AAAA,aAAA,sBAAA,MAAA;IAAA,CAAA;AAGF,IAAA,iBAAA,IAAA,cAAA;AAAA,IAAA,uBAAA,EAAW;AAEb,IAAA,yBAAA,IAAA,OAAA,EAAA,EAA2B,IAAA,UAAA,EAAA;AACS,IAAA,qBAAA,SAAA,SAAA,uEAAA;AAAA,MAAA,wBAAA,GAAA;AAAA,YAAA,SAAA,wBAAA;AAAA,aAAA,sBAAS,OAAA,iBAAA,CAAkB;IAAA,CAAA;AAAE,IAAA,iBAAA,IAAA,SAAA;AAAO,IAAA,uBAAA;AACtE,IAAA,yBAAA,IAAA,UAAA,EAAA;AAA+B,IAAA,qBAAA,SAAA,SAAA,uEAAA;AAAA,MAAA,wBAAA,GAAA;AAAA,YAAA,SAAA,wBAAA;AAAA,aAAA,sBAAS,OAAA,cAAA,CAAe;IAAA,CAAA;AAAE,IAAA,iBAAA,IAAA,SAAA;AAAO,IAAA,uBAAA,EAAS,EACrE,EACF;;;;AATA,IAAA,oBAAA,EAAA;AAAA,IAAA,2BAAA,WAAA,OAAA,gBAAA;;;AAwPR,IAAO,8BAAP,MAAO,6BAA2B;EAYlB;EAXpB,YAA8C;EAC9C,qBAA4C,CAAA;EAC5C,yBAAoD,CAAA;EACpD,yBAAyB;EACzB,6BAA6B;EAE7B,kBAAkB;EAClB,iBAAsB;EACtB,cAA8C;EAC9C,mBAAmB;EAEnB,YAAoB,gBAA8B;AAA9B,SAAA,iBAAA;EAAiC;EAErD,WAAQ;AACN,SAAK,uBAAsB;EAC7B;EAEA,yBAAsB;AACpB,YAAQ,IAAI,+DAAqD;AACjE,QAAI,KAAK,wBAAwB;AAC/B,YAAM,SAAS,SAAS,KAAK,sBAAsB;AACnD,cAAQ,IAAI,kCAA2B,MAAM;AAC7C,WAAK,eAAe,8BAA8B,MAAM,EAAE,UAAU;QAClE,MAAM,CAAC,aAAY;AACjB,kBAAQ,IAAI,2CAAmC,QAAQ;AACvD,eAAK,qBAAqB;QAC5B;QACA,OAAO,CAAC,UAAS;AACf,kBAAQ,MAAM,6DAAwD,KAAK;QAC7E;OACD;IACH,OAAO;AACL,cAAQ,IAAI,gDAAyC;AACrD,WAAK,eAAe,yBAAwB,EAAG,UAAU;QACvD,MAAM,CAAC,aAAY;AACjB,kBAAQ,IAAI,yCAAiC,QAAQ;AACrD,eAAK,qBAAqB;QAC5B;QACA,OAAO,CAAC,UAAS;AACf,kBAAQ,MAAM,4DAAuD,KAAK;QAC5E;OACD;IACH;EACF;EAEA,6BAA0B;AACxB,QAAI,KAAK,4BAA4B;AACnC,YAAM,SAAS,SAAS,KAAK,0BAA0B;AACvD,WAAK,eAAe,kCAAkC,MAAM,EAAE,UAC5D,cAAY,KAAK,yBAAyB,QAAQ;IAEtD,OAAO;AACL,WAAK,eAAe,6BAA4B,EAAG,UACjD,cAAY,KAAK,yBAAyB,QAAQ;IAEtD;EACF;EAEA,wBAAwB,IAAY,QAAqB;AACvD,UAAM,aAAyC,EAAE,OAAM;AACvD,SAAK,eAAe,wBAAwB,IAAI,UAAU,EAAE,UAAU,MAAK;AACzE,WAAK,uBAAsB;IAC7B,CAAC;EACH;EAEA,4BAA4B,IAAY,QAAqB;AAC3D,UAAM,aAA6C,EAAE,OAAM;AAC3D,SAAK,eAAe,4BAA4B,IAAI,UAAU,EAAE,UAAU,MAAK;AAC7E,WAAK,2BAA0B;IACjC,CAAC;EACH;EAEA,gBAAgB,SAAc,MAAoC;AAChE,SAAK,iBAAiB;AACtB,SAAK,cAAc;AACnB,SAAK,mBAAmB;AACxB,SAAK,kBAAkB;EACzB;EAEA,mBAAgB;AACd,SAAK,kBAAkB;AACvB,SAAK,iBAAiB;AACtB,SAAK,mBAAmB;EAC1B;EAEA,gBAAa;AACX,QAAI,KAAK,gBAAgB;AACvB,YAAM,aAAa;QACjB,QAAQ,cAAc;QACtB,kBAAkB,KAAK,oBAAoB;;AAG7C,UAAI,KAAK,gBAAgB,aAAa;AACpC,aAAK,eAAe,wBAAwB,KAAK,eAAe,IAAI,UAAU,EAAE,UAAU,MAAK;AAC7F,eAAK,uBAAsB;AAC3B,eAAK,iBAAgB;QACvB,CAAC;MACH,OAAO;AACL,aAAK,eAAe,4BAA4B,KAAK,eAAe,IAAI,UAAU,EAAE,UAAU,MAAK;AACjG,eAAK,2BAA0B;AAC/B,eAAK,iBAAgB;QACvB,CAAC;MACH;IACF;EACF;EAEA,eAAe,QAAqB;AAClC,WAAO,KAAK,eAAe,eAAe,MAAM;EAClD;EAEA,eAAe,QAAqB;AAClC,WAAO,KAAK,eAAe,eAAe,MAAM;EAClD;EAEA,WAAW,MAAU;AACnB,WAAO,IAAI,KAAK,IAAI,EAAE,mBAAmB,SAAS;MAChD,MAAM;MACN,OAAO;MACP,KAAK;MACL,MAAM;MACN,QAAQ;KACT;EACH;;qCA3HW,8BAA2B,4BAAA,cAAA,CAAA;EAAA;yEAA3B,8BAA2B,WAAA,CAAA,CAAA,yBAAA,CAAA,GAAA,OAAA,IAAA,MAAA,GAAA,QAAA,CAAA,CAAA,GAAA,qBAAA,GAAA,CAAA,GAAA,aAAA,GAAA,CAAA,GAAA,MAAA,GAAA,CAAA,GAAA,WAAA,GAAA,OAAA,GAAA,CAAA,SAAA,SAAA,GAAA,MAAA,GAAA,CAAA,SAAA,eAAA,GAAA,MAAA,GAAA,CAAA,SAAA,iBAAA,GAAA,SAAA,GAAA,MAAA,GAAA,CAAA,GAAA,OAAA,GAAA,CAAA,GAAA,aAAA,GAAA,CAAA,GAAA,SAAA,GAAA,CAAA,GAAA,iBAAA,UAAA,SAAA,GAAA,CAAA,SAAA,EAAA,GAAA,CAAA,SAAA,GAAA,GAAA,CAAA,SAAA,GAAA,GAAA,CAAA,SAAA,GAAA,GAAA,CAAA,SAAA,eAAA,GAAA,MAAA,GAAA,CAAA,GAAA,eAAA,GAAA,CAAA,SAAA,gBAAA,GAAA,SAAA,SAAA,GAAA,CAAA,GAAA,aAAA,GAAA,CAAA,GAAA,cAAA,GAAA,CAAA,GAAA,gBAAA,GAAA,CAAA,GAAA,cAAA,GAAA,CAAA,GAAA,iBAAA,GAAA,CAAA,SAAA,iBAAA,GAAA,MAAA,GAAA,CAAA,SAAA,mBAAA,GAAA,MAAA,GAAA,CAAA,SAAA,iBAAA,GAAA,MAAA,GAAA,CAAA,GAAA,eAAA,GAAA,CAAA,OAAA,4BAAA,GAAA,KAAA,GAAA,CAAA,GAAA,iBAAA,GAAA,CAAA,GAAA,OAAA,eAAA,GAAA,OAAA,GAAA,CAAA,GAAA,OAAA,cAAA,GAAA,OAAA,GAAA,CAAA,GAAA,eAAA,GAAA,CAAA,OAAA,iCAAA,GAAA,KAAA,GAAA,CAAA,GAAA,iBAAA,GAAA,OAAA,GAAA,CAAA,GAAA,SAAA,GAAA,OAAA,GAAA,CAAA,GAAA,cAAA,GAAA,CAAA,GAAA,aAAA,GAAA,OAAA,GAAA,CAAA,GAAA,eAAA,GAAA,CAAA,eAAA,2BAAA,QAAA,KAAA,GAAA,iBAAA,SAAA,GAAA,CAAA,GAAA,eAAA,GAAA,CAAA,GAAA,OAAA,iBAAA,GAAA,OAAA,CAAA,GAAA,UAAA,SAAA,qCAAA,IAAA,KAAA;AAAA,QAAA,KAAA,GAAA;AAhYpC,MAAA,yBAAA,GAAA,OAAA,CAAA,EAAiC,GAAA,OAAA,CAAA,EACN,GAAA,IAAA;AACnB,MAAA,iBAAA,GAAA,gCAAA;AAAuB,MAAA,uBAAA;AAC3B,MAAA,yBAAA,GAAA,GAAA;AAAG,MAAA,iBAAA,GAAA,6EAAA;AAA+D,MAAA,uBAAA,EAAI;AAGxE,MAAA,yBAAA,GAAA,OAAA,CAAA,EAAkB,GAAA,UAAA,CAAA;AAId,MAAA,qBAAA,SAAA,SAAA,+DAAA;AAAA,YAAA,YAAqB;AAAY,eAAE,IAAA,uBAAA;MAAwB,CAAA;AAC3D,MAAA,iBAAA,GAAA,6BAAA;AACA,MAAA,qBAAA,GAAA,6CAAA,GAAA,GAAA,QAAA,CAAA;AACF,MAAA,uBAAA;AACA,MAAA,yBAAA,IAAA,UAAA,CAAA;AAGE,MAAA,qBAAA,SAAA,SAAA,gEAAA;AAAA,YAAA,YAAqB;AAAiB,eAAE,IAAA,2BAAA;MAA4B,CAAA;AACpE,MAAA,iBAAA,IAAA,kCAAA;AACA,MAAA,qBAAA,IAAA,8CAAA,GAAA,GAAA,QAAA,CAAA;AACF,MAAA,uBAAA,EAAS;AAIX,MAAA,qBAAA,IAAA,6CAAA,IAAA,GAAA,OAAA,CAAA,EAA4D,IAAA,6CAAA,IAAA,GAAA,OAAA,CAAA,EAmDK,IAAA,6CAAA,IAAA,GAAA,OAAA,CAAA;AAwEnE,MAAA,uBAAA;;;AA1IM,MAAA,oBAAA,CAAA;AAAA,MAAA,sBAAA,UAAA,IAAA,cAAA,YAAA;AAGO,MAAA,oBAAA,CAAA;AAAA,MAAA,qBAAA,QAAA,IAAA,mBAAA,SAAA,CAAA;AAIP,MAAA,oBAAA;AAAA,MAAA,sBAAA,UAAA,IAAA,cAAA,iBAAA;AAGO,MAAA,oBAAA,CAAA;AAAA,MAAA,qBAAA,QAAA,IAAA,uBAAA,SAAA,CAAA;AAKL,MAAA,oBAAA;AAAA,MAAA,qBAAA,QAAA,IAAA,cAAA,YAAA;AAmDA,MAAA,oBAAA;AAAA,MAAA,qBAAA,QAAA,IAAA,cAAA,iBAAA;AAoDA,MAAA,oBAAA;AAAA,MAAA,qBAAA,QAAA,IAAA,eAAA;;oBAjIA,cAAY,SAAA,MAAE,aAAW,gBAAA,8BAAA,sBAAA,4BAAA,iBAAA,OAAA,GAAA,QAAA,CAAA,05IAAA,EAAA,CAAA;;;sEAkYxB,6BAA2B,CAAA;UArYvC;uBACW,2BAAyB,YACvB,MAAI,SACP,CAAC,cAAc,WAAW,GAAC,UAC1B;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;KAqJT,QAAA,CAAA,8yHAAA,EAAA,CAAA;;;;6EA4OU,6BAA2B,EAAA,WAAA,+BAAA,UAAA,iFAAA,YAAA,IAAA,CAAA;AAAA,GAAA;", "names": []}
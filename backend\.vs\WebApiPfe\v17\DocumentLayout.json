{"Version": 1, "WorkspaceRootPath": "C:\\Users\\<USER>\\Desktop\\STE\\PFE\\PFE\\backend\\", "Documents": [{"AbsoluteMoniker": "D:0:0:{7C761E66-B690-4E5F-9F91-A1DA9AB1218C}|WebApiPfe\\WebApiPfe.csproj|c:\\users\\<USER>\\desktop\\ste\\pfe\\pfe\\backend\\webapipfe\\appsettings.json||{90A6B3A7-C1A3-4009-A288-E2FF89E96FA0}", "RelativeMoniker": "D:0:0:{7C761E66-B690-4E5F-9F91-A1DA9AB1218C}|WebApiPfe\\WebApiPfe.csproj|solutionrelative:webapipfe\\appsettings.json||{90A6B3A7-C1A3-4009-A288-E2FF89E96FA0}"}, {"AbsoluteMoniker": "D:0:0:{7C761E66-B690-4E5F-9F91-A1DA9AB1218C}|WebApiPfe\\WebApiPfe.csproj|c:\\users\\<USER>\\desktop\\ste\\pfe\\pfe\\backend\\webapipfe\\program.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{7C761E66-B690-4E5F-9F91-A1DA9AB1218C}|WebApiPfe\\WebApiPfe.csproj|solutionrelative:webapipfe\\program.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{7C761E66-B690-4E5F-9F91-A1DA9AB1218C}|WebApiPfe\\WebApiPfe.csproj|c:\\users\\<USER>\\desktop\\ste\\pfe\\pfe\\backend\\webapipfe\\models\\reclamation.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{7C761E66-B690-4E5F-9F91-A1DA9AB1218C}|WebApiPfe\\WebApiPfe.csproj|solutionrelative:webapipfe\\models\\reclamation.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{7C761E66-B690-4E5F-9F91-A1DA9AB1218C}|WebApiPfe\\WebApiPfe.csproj|c:\\users\\<USER>\\desktop\\ste\\pfe\\pfe\\backend\\webapipfe\\models\\enum\\typenotification.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{7C761E66-B690-4E5F-9F91-A1DA9AB1218C}|WebApiPfe\\WebApiPfe.csproj|solutionrelative:webapipfe\\models\\enum\\typenotification.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{7C761E66-B690-4E5F-9F91-A1DA9AB1218C}|WebApiPfe\\WebApiPfe.csproj|c:\\users\\<USER>\\desktop\\ste\\pfe\\pfe\\backend\\webapipfe\\models\\enum\\statutdemande.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{7C761E66-B690-4E5F-9F91-A1DA9AB1218C}|WebApiPfe\\WebApiPfe.csproj|solutionrelative:webapipfe\\models\\enum\\statutdemande.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{7C761E66-B690-4E5F-9F91-A1DA9AB1218C}|WebApiPfe\\WebApiPfe.csproj|c:\\users\\<USER>\\desktop\\ste\\pfe\\pfe\\backend\\webapipfe\\models\\enum\\typepromotion.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{7C761E66-B690-4E5F-9F91-A1DA9AB1218C}|WebApiPfe\\WebApiPfe.csproj|solutionrelative:webapipfe\\models\\enum\\typepromotion.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{7C761E66-B690-4E5F-9F91-A1DA9AB1218C}|WebApiPfe\\WebApiPfe.csproj|c:\\users\\<USER>\\desktop\\ste\\pfe\\pfe\\backend\\webapipfe\\models\\entity\\produit.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{7C761E66-B690-4E5F-9F91-A1DA9AB1218C}|WebApiPfe\\WebApiPfe.csproj|solutionrelative:webapipfe\\models\\entity\\produit.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{7C761E66-B690-4E5F-9F91-A1DA9AB1218C}|WebApiPfe\\WebApiPfe.csproj|c:\\users\\<USER>\\desktop\\ste\\pfe\\pfe\\backend\\webapipfe\\models\\entity\\adresse.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{7C761E66-B690-4E5F-9F91-A1DA9AB1218C}|WebApiPfe\\WebApiPfe.csproj|solutionrelative:webapipfe\\models\\entity\\adresse.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{7C761E66-B690-4E5F-9F91-A1DA9AB1218C}|WebApiPfe\\WebApiPfe.csproj|c:\\users\\<USER>\\desktop\\ste\\pfe\\pfe\\backend\\webapipfe\\services\\implementations\\lignecommandefournisseurservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{7C761E66-B690-4E5F-9F91-A1DA9AB1218C}|WebApiPfe\\WebApiPfe.csproj|solutionrelative:webapipfe\\services\\implementations\\lignecommandefournisseurservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{7C761E66-B690-4E5F-9F91-A1DA9AB1218C}|WebApiPfe\\WebApiPfe.csproj|c:\\users\\<USER>\\desktop\\ste\\pfe\\pfe\\backend\\webapipfe\\services\\implementations\\commandefournisseurservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{7C761E66-B690-4E5F-9F91-A1DA9AB1218C}|WebApiPfe\\WebApiPfe.csproj|solutionrelative:webapipfe\\services\\implementations\\commandefournisseurservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{7C761E66-B690-4E5F-9F91-A1DA9AB1218C}|WebApiPfe\\WebApiPfe.csproj|c:\\users\\<USER>\\desktop\\ste\\pfe\\pfe\\backend\\webapipfe\\services\\implementations\\detailscommandeservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{7C761E66-B690-4E5F-9F91-A1DA9AB1218C}|WebApiPfe\\WebApiPfe.csproj|solutionrelative:webapipfe\\services\\implementations\\detailscommandeservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{7C761E66-B690-4E5F-9F91-A1DA9AB1218C}|WebApiPfe\\WebApiPfe.csproj|c:\\users\\<USER>\\desktop\\ste\\pfe\\pfe\\backend\\webapipfe\\services\\implementations\\commandeservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{7C761E66-B690-4E5F-9F91-A1DA9AB1218C}|WebApiPfe\\WebApiPfe.csproj|solutionrelative:webapipfe\\services\\implementations\\commandeservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{7C761E66-B690-4E5F-9F91-A1DA9AB1218C}|WebApiPfe\\WebApiPfe.csproj|c:\\users\\<USER>\\desktop\\ste\\pfe\\pfe\\backend\\webapipfe\\services\\implementations\\favoriservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{7C761E66-B690-4E5F-9F91-A1DA9AB1218C}|WebApiPfe\\WebApiPfe.csproj|solutionrelative:webapipfe\\services\\implementations\\favoriservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{7C761E66-B690-4E5F-9F91-A1DA9AB1218C}|WebApiPfe\\WebApiPfe.csproj|c:\\users\\<USER>\\desktop\\ste\\pfe\\pfe\\backend\\webapipfe\\services\\implementations\\promotionutiliseeservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{7C761E66-B690-4E5F-9F91-A1DA9AB1218C}|WebApiPfe\\WebApiPfe.csproj|solutionrelative:webapipfe\\services\\implementations\\promotionutiliseeservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{7C761E66-B690-4E5F-9F91-A1DA9AB1218C}|WebApiPfe\\WebApiPfe.csproj|c:\\users\\<USER>\\desktop\\ste\\pfe\\pfe\\backend\\webapipfe\\services\\implementations\\produitservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{7C761E66-B690-4E5F-9F91-A1DA9AB1218C}|WebApiPfe\\WebApiPfe.csproj|solutionrelative:webapipfe\\services\\implementations\\produitservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{7C761E66-B690-4E5F-9F91-A1DA9AB1218C}|WebApiPfe\\WebApiPfe.csproj|c:\\users\\<USER>\\desktop\\ste\\pfe\\pfe\\backend\\webapipfe\\services\\implementations\\souscategorieservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{7C761E66-B690-4E5F-9F91-A1DA9AB1218C}|WebApiPfe\\WebApiPfe.csproj|solutionrelative:webapipfe\\services\\implementations\\souscategorieservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{7C761E66-B690-4E5F-9F91-A1DA9AB1218C}|WebApiPfe\\WebApiPfe.csproj|c:\\users\\<USER>\\desktop\\ste\\pfe\\pfe\\backend\\webapipfe\\services\\implementations\\marqueservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{7C761E66-B690-4E5F-9F91-A1DA9AB1218C}|WebApiPfe\\WebApiPfe.csproj|solutionrelative:webapipfe\\services\\implementations\\marqueservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{7C761E66-B690-4E5F-9F91-A1DA9AB1218C}|WebApiPfe\\WebApiPfe.csproj|c:\\users\\<USER>\\desktop\\ste\\pfe\\pfe\\backend\\webapipfe\\services\\implementations\\formeservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{7C761E66-B690-4E5F-9F91-A1DA9AB1218C}|WebApiPfe\\WebApiPfe.csproj|solutionrelative:webapipfe\\services\\implementations\\formeservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{7C761E66-B690-4E5F-9F91-A1DA9AB1218C}|WebApiPfe\\WebApiPfe.csproj|c:\\users\\<USER>\\desktop\\ste\\pfe\\pfe\\backend\\webapipfe\\services\\implementations\\clientservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{7C761E66-B690-4E5F-9F91-A1DA9AB1218C}|WebApiPfe\\WebApiPfe.csproj|solutionrelative:webapipfe\\services\\implementations\\clientservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{7C761E66-B690-4E5F-9F91-A1DA9AB1218C}|WebApiPfe\\WebApiPfe.csproj|c:\\users\\<USER>\\desktop\\ste\\pfe\\pfe\\backend\\webapipfe\\services\\implementations\\categorieservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{7C761E66-B690-4E5F-9F91-A1DA9AB1218C}|WebApiPfe\\WebApiPfe.csproj|solutionrelative:webapipfe\\services\\implementations\\categorieservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{7C761E66-B690-4E5F-9F91-A1DA9AB1218C}|WebApiPfe\\WebApiPfe.csproj|c:\\users\\<USER>\\desktop\\ste\\pfe\\pfe\\backend\\webapipfe\\services\\implementations\\adresseservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{7C761E66-B690-4E5F-9F91-A1DA9AB1218C}|WebApiPfe\\WebApiPfe.csproj|solutionrelative:webapipfe\\services\\implementations\\adresseservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{7C761E66-B690-4E5F-9F91-A1DA9AB1218C}|WebApiPfe\\WebApiPfe.csproj|c:\\users\\<USER>\\desktop\\ste\\pfe\\pfe\\backend\\webapipfe\\services\\implementations\\adminservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{7C761E66-B690-4E5F-9F91-A1DA9AB1218C}|WebApiPfe\\WebApiPfe.csproj|solutionrelative:webapipfe\\services\\implementations\\adminservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{7C761E66-B690-4E5F-9F91-A1DA9AB1218C}|WebApiPfe\\WebApiPfe.csproj|c:\\users\\<USER>\\desktop\\ste\\pfe\\pfe\\backend\\webapipfe\\services\\implementations\\fournisseurservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{7C761E66-B690-4E5F-9F91-A1DA9AB1218C}|WebApiPfe\\WebApiPfe.csproj|solutionrelative:webapipfe\\services\\implementations\\fournisseurservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{7C761E66-B690-4E5F-9F91-A1DA9AB1218C}|WebApiPfe\\WebApiPfe.csproj|c:\\users\\<USER>\\desktop\\ste\\pfe\\pfe\\backend\\webapipfe\\services\\implementations\\authservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{7C761E66-B690-4E5F-9F91-A1DA9AB1218C}|WebApiPfe\\WebApiPfe.csproj|solutionrelative:webapipfe\\services\\implementations\\authservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{7C761E66-B690-4E5F-9F91-A1DA9AB1218C}|WebApiPfe\\WebApiPfe.csproj|c:\\users\\<USER>\\desktop\\ste\\pfe\\pfe\\backend\\webapipfe\\services\\token\\tokenservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{7C761E66-B690-4E5F-9F91-A1DA9AB1218C}|WebApiPfe\\WebApiPfe.csproj|solutionrelative:webapipfe\\services\\token\\tokenservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{7C761E66-B690-4E5F-9F91-A1DA9AB1218C}|WebApiPfe\\WebApiPfe.csproj|c:\\users\\<USER>\\desktop\\ste\\pfe\\pfe\\backend\\webapipfe\\models\\entity\\fournisseur.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{7C761E66-B690-4E5F-9F91-A1DA9AB1218C}|WebApiPfe\\WebApiPfe.csproj|solutionrelative:webapipfe\\models\\entity\\fournisseur.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{7C761E66-B690-4E5F-9F91-A1DA9AB1218C}|WebApiPfe\\WebApiPfe.csproj|c:\\users\\<USER>\\desktop\\ste\\pfe\\pfe\\backend\\webapipfe\\models\\dtos\\calculerfraislivraisonrequest.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{7C761E66-B690-4E5F-9F91-A1DA9AB1218C}|WebApiPfe\\WebApiPfe.csproj|solutionrelative:webapipfe\\models\\dtos\\calculerfraislivraisonrequest.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{7C761E66-B690-4E5F-9F91-A1DA9AB1218C}|WebApiPfe\\WebApiPfe.csproj|c:\\users\\<USER>\\desktop\\ste\\pfe\\pfe\\backend\\webapipfe\\models\\enum\\statutremboursement.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{7C761E66-B690-4E5F-9F91-A1DA9AB1218C}|WebApiPfe\\WebApiPfe.csproj|solutionrelative:webapipfe\\models\\enum\\statutremboursement.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}], "DocumentGroupContainers": [{"Orientation": 0, "VerticalTabListWidth": 256, "DocumentGroups": [{"DockedWidth": 200, "SelectedChildIndex": 32, "Children": [{"$type": "Bookmark", "Name": "ST:0:0:{3ae79031-e1bc-11d0-8f78-00a0c9110057}"}, {"$type": "Bookmark", "Name": "ST:0:0:{a83394e3-39c2-4678-a3c8-364b69d78fd3}"}, {"$type": "Bookmark", "Name": "ST:128:0:{75188d03-9892-4ae2-abf1-207126247ce5}"}, {"$type": "Bookmark", "Name": "ST:0:0:{1c4feeaa-4718-4aa9-859d-94ce25d182ba}"}, {"$type": "Bookmark", "Name": "ST:0:0:{aa2115a1-9712-457b-9047-dbb71ca2cdd2}"}, {"$type": "Bookmark", "Name": "ST:793004293:0:{83107a3e-496a-485e-b455-16ddb978e55e}"}, {"$type": "Bookmark", "Name": "ST:0:0:{0ad07096-bba9-4900-a651-0598d26f6d24}"}, {"$type": "Document", "DocumentIndex": 2, "Title": "Reclamation.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\STE\\PFE\\PFE\\backend\\WebApiPfe\\Models\\Reclamation.cs", "RelativeDocumentMoniker": "WebApiPfe\\Models\\Reclamation.cs", "ToolTip": "C:\\Users\\<USER>\\Desktop\\STE\\PFE\\PFE\\backend\\WebApiPfe\\Models\\Reclamation.cs", "RelativeToolTip": "WebApiPfe\\Models\\Reclamation.cs", "ViewState": "AgIAAB0AAAAAAAAAAAA8wAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-17T09:31:57.275Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 3, "Title": "TypeNotification.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\STE\\PFE\\PFE\\backend\\WebApiPfe\\Models\\Enum\\TypeNotification.cs", "RelativeDocumentMoniker": "WebApiPfe\\Models\\Enum\\TypeNotification.cs", "ToolTip": "C:\\Users\\<USER>\\Desktop\\STE\\PFE\\PFE\\backend\\WebApiPfe\\Models\\Enum\\TypeNotification.cs", "RelativeToolTip": "WebApiPfe\\Models\\Enum\\TypeNotification.cs", "ViewState": "AgIAAAUAAAAAAAAAAAAAwAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-17T09:31:49.016Z"}, {"$type": "Document", "DocumentIndex": 4, "Title": "StatutDemande.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\STE\\PFE\\PFE\\backend\\WebApiPfe\\Models\\Enum\\StatutDemande.cs", "RelativeDocumentMoniker": "WebApiPfe\\Models\\Enum\\StatutDemande.cs", "ToolTip": "C:\\Users\\<USER>\\Desktop\\STE\\PFE\\PFE\\backend\\WebApiPfe\\Models\\Enum\\StatutDemande.cs", "RelativeToolTip": "WebApiPfe\\Models\\Enum\\StatutDemande.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-17T09:31:44.657Z"}, {"$type": "Document", "DocumentIndex": 8, "Title": "LigneCommandeFournisseurService.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\STE\\PFE\\PFE\\backend\\WebApiPfe\\Services\\Implementations\\LigneCommandeFournisseurService.cs", "RelativeDocumentMoniker": "WebApiPfe\\Services\\Implementations\\LigneCommandeFournisseurService.cs", "ToolTip": "C:\\Users\\<USER>\\Desktop\\STE\\PFE\\PFE\\backend\\WebApiPfe\\Services\\Implementations\\LigneCommandeFournisseurService.cs", "RelativeToolTip": "WebApiPfe\\Services\\Implementations\\LigneCommandeFournisseurService.cs", "ViewState": "AgIAAJkAAAAAAAAAAAAAAJkAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-07T21:12:05.524Z"}, {"$type": "Document", "DocumentIndex": 10, "Title": "DetailsCommandeService.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\STE\\PFE\\PFE\\backend\\WebApiPfe\\Services\\Implementations\\DetailsCommandeService.cs", "RelativeDocumentMoniker": "WebApiPfe\\Services\\Implementations\\DetailsCommandeService.cs", "ToolTip": "C:\\Users\\<USER>\\Desktop\\STE\\PFE\\PFE\\backend\\WebApiPfe\\Services\\Implementations\\DetailsCommandeService.cs", "RelativeToolTip": "WebApiPfe\\Services\\Implementations\\DetailsCommandeService.cs", "ViewState": "AgIAAGkAAAAAAAAAAAAAAHcAAAABAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-07T21:11:34.078Z"}, {"$type": "Document", "DocumentIndex": 11, "Title": "CommandeService.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\STE\\PFE\\PFE\\backend\\WebApiPfe\\Services\\Implementations\\CommandeService.cs", "RelativeDocumentMoniker": "WebApiPfe\\Services\\Implementations\\CommandeService.cs", "ToolTip": "C:\\Users\\<USER>\\Desktop\\STE\\PFE\\PFE\\backend\\WebApiPfe\\Services\\Implementations\\CommandeService.cs", "RelativeToolTip": "WebApiPfe\\Services\\Implementations\\CommandeService.cs", "ViewState": "AgIAACwCAAAAAAAAAAAAADsCAAABAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-07T21:10:50.361Z"}, {"$type": "Document", "DocumentIndex": 9, "Title": "CommandeFournisseurService.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\STE\\PFE\\PFE\\backend\\WebApiPfe\\Services\\Implementations\\CommandeFournisseurService.cs", "RelativeDocumentMoniker": "WebApiPfe\\Services\\Implementations\\CommandeFournisseurService.cs", "ToolTip": "C:\\Users\\<USER>\\Desktop\\STE\\PFE\\PFE\\backend\\WebApiPfe\\Services\\Implementations\\CommandeFournisseurService.cs", "RelativeToolTip": "WebApiPfe\\Services\\Implementations\\CommandeFournisseurService.cs", "ViewState": "AgIAALYAAAAAAAAAAAAAALUAAAABAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-07T21:10:49.065Z"}, {"$type": "Document", "DocumentIndex": 12, "Title": "FavoriService.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\STE\\PFE\\PFE\\backend\\WebApiPfe\\Services\\Implementations\\FavoriService.cs", "RelativeDocumentMoniker": "WebApiPfe\\Services\\Implementations\\FavoriService.cs", "ToolTip": "C:\\Users\\<USER>\\Desktop\\STE\\PFE\\PFE\\backend\\WebApiPfe\\Services\\Implementations\\FavoriService.cs", "RelativeToolTip": "WebApiPfe\\Services\\Implementations\\FavoriService.cs", "ViewState": "AgIAAFYAAAAAAAAAAAAAAFYAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-07T21:10:06.189Z"}, {"$type": "Document", "DocumentIndex": 5, "Title": "TypePromotion.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\STE\\PFE\\PFE\\backend\\WebApiPfe\\Models\\Enum\\TypePromotion.cs", "RelativeDocumentMoniker": "WebApiPfe\\Models\\Enum\\TypePromotion.cs", "ToolTip": "C:\\Users\\<USER>\\Desktop\\STE\\PFE\\PFE\\backend\\WebApiPfe\\Models\\Enum\\TypePromotion.cs", "RelativeToolTip": "WebApiPfe\\Models\\Enum\\TypePromotion.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAUAAAAdAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-05T13:43:58.484Z"}, {"$type": "Document", "DocumentIndex": 6, "Title": "Produit.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\STE\\PFE\\PFE\\backend\\WebApiPfe\\Models\\Entity\\Produit.cs", "RelativeDocumentMoniker": "WebApiPfe\\Models\\Entity\\Produit.cs", "ToolTip": "C:\\Users\\<USER>\\Desktop\\STE\\PFE\\PFE\\backend\\WebApiPfe\\Models\\Entity\\Produit.cs", "RelativeToolTip": "WebApiPfe\\Models\\Entity\\Produit.cs", "ViewState": "AgIAAIMAAAAAAAAAAAAiwI4AAAABAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-09T01:32:58.6Z"}, {"$type": "Document", "DocumentIndex": 7, "Title": "Adresse.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\STE\\PFE\\PFE\\backend\\WebApiPfe\\Models\\Entity\\Adresse.cs", "RelativeDocumentMoniker": "WebApiPfe\\Models\\Entity\\Adresse.cs", "ToolTip": "C:\\Users\\<USER>\\Desktop\\STE\\PFE\\PFE\\backend\\WebApiPfe\\Models\\Entity\\Adresse.cs", "RelativeToolTip": "WebApiPfe\\Models\\Entity\\Adresse.cs", "ViewState": "AgIAAAQAAAAAAAAAAAAowBQAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-09T01:08:42.457Z"}, {"$type": "Document", "DocumentIndex": 13, "Title": "PromotionUtiliseeService.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\STE\\PFE\\PFE\\backend\\WebApiPfe\\Services\\Implementations\\PromotionUtiliseeService.cs", "RelativeDocumentMoniker": "WebApiPfe\\Services\\Implementations\\PromotionUtiliseeService.cs", "ToolTip": "C:\\Users\\<USER>\\Desktop\\STE\\PFE\\PFE\\backend\\WebApiPfe\\Services\\Implementations\\PromotionUtiliseeService.cs", "RelativeToolTip": "WebApiPfe\\Services\\Implementations\\PromotionUtiliseeService.cs", "ViewState": "AgIAAH0AAAAAAAAAAAAAAH0AAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-07T21:09:44.984Z"}, {"$type": "Document", "DocumentIndex": 14, "Title": "ProduitService.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\STE\\PFE\\PFE\\backend\\WebApiPfe\\Services\\Implementations\\ProduitService.cs", "RelativeDocumentMoniker": "WebApiPfe\\Services\\Implementations\\ProduitService.cs", "ToolTip": "C:\\Users\\<USER>\\Desktop\\STE\\PFE\\PFE\\backend\\WebApiPfe\\Services\\Implementations\\ProduitService.cs", "RelativeToolTip": "WebApiPfe\\Services\\Implementations\\ProduitService.cs", "ViewState": "AgIAAJgCAAAAAAAAAAAAAKYCAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-07T18:39:06.907Z"}, {"$type": "Document", "DocumentIndex": 15, "Title": "SousCategorieService.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\STE\\PFE\\PFE\\backend\\WebApiPfe\\Services\\Implementations\\SousCategorieService.cs", "RelativeDocumentMoniker": "WebApiPfe\\Services\\Implementations\\SousCategorieService.cs", "ToolTip": "C:\\Users\\<USER>\\Desktop\\STE\\PFE\\PFE\\backend\\WebApiPfe\\Services\\Implementations\\SousCategorieService.cs", "RelativeToolTip": "WebApiPfe\\Services\\Implementations\\SousCategorieService.cs", "ViewState": "AgIAAGkAAAAAAAAAAAAAAGkAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-06T23:10:39.534Z"}, {"$type": "Document", "DocumentIndex": 16, "Title": "MarqueService.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\STE\\PFE\\PFE\\backend\\WebApiPfe\\Services\\Implementations\\MarqueService.cs", "RelativeDocumentMoniker": "WebApiPfe\\Services\\Implementations\\MarqueService.cs", "ToolTip": "C:\\Users\\<USER>\\Desktop\\STE\\PFE\\PFE\\backend\\WebApiPfe\\Services\\Implementations\\MarqueService.cs", "RelativeToolTip": "WebApiPfe\\Services\\Implementations\\MarqueService.cs", "ViewState": "AgIAAEkAAAAAAAAAAAAAAFsAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-06T23:10:19.162Z"}, {"$type": "Document", "DocumentIndex": 17, "Title": "FormeService.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\STE\\PFE\\PFE\\backend\\WebApiPfe\\Services\\Implementations\\FormeService.cs", "RelativeDocumentMoniker": "WebApiPfe\\Services\\Implementations\\FormeService.cs", "ToolTip": "C:\\Users\\<USER>\\Desktop\\STE\\PFE\\PFE\\backend\\WebApiPfe\\Services\\Implementations\\FormeService.cs", "RelativeToolTip": "WebApiPfe\\Services\\Implementations\\FormeService.cs", "ViewState": "AgIAAFYAAAAAAAAAAAAAAG0AAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-06T23:10:00.155Z"}, {"$type": "Document", "DocumentIndex": 18, "Title": "ClientService.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\STE\\PFE\\PFE\\backend\\WebApiPfe\\Services\\Implementations\\ClientService.cs", "RelativeDocumentMoniker": "WebApiPfe\\Services\\Implementations\\ClientService.cs", "ToolTip": "C:\\Users\\<USER>\\Desktop\\STE\\PFE\\PFE\\backend\\WebApiPfe\\Services\\Implementations\\ClientService.cs", "RelativeToolTip": "WebApiPfe\\Services\\Implementations\\ClientService.cs", "ViewState": "AgIAAJAAAAAAAAAAAAAAwKMAAAABAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-06T23:09:40.866Z"}, {"$type": "Document", "DocumentIndex": 19, "Title": "CategorieService.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\STE\\PFE\\PFE\\backend\\WebApiPfe\\Services\\Implementations\\CategorieService.cs", "RelativeDocumentMoniker": "WebApiPfe\\Services\\Implementations\\CategorieService.cs", "ToolTip": "C:\\Users\\<USER>\\Desktop\\STE\\PFE\\PFE\\backend\\WebApiPfe\\Services\\Implementations\\CategorieService.cs", "RelativeToolTip": "WebApiPfe\\Services\\Implementations\\CategorieService.cs", "ViewState": "AgIAAD0AAAAAAAAAAAAAAFoAAAABAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-06T23:09:21.575Z"}, {"$type": "Document", "DocumentIndex": 20, "Title": "AdresseService.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\STE\\PFE\\PFE\\backend\\WebApiPfe\\Services\\Implementations\\AdresseService.cs", "RelativeDocumentMoniker": "WebApiPfe\\Services\\Implementations\\AdresseService.cs", "ToolTip": "C:\\Users\\<USER>\\Desktop\\STE\\PFE\\PFE\\backend\\WebApiPfe\\Services\\Implementations\\AdresseService.cs", "RelativeToolTip": "WebApiPfe\\Services\\Implementations\\AdresseService.cs", "ViewState": "AgIAAEgAAAAAAAAAAAAAAGcAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-06T23:08:54.412Z"}, {"$type": "Document", "DocumentIndex": 21, "Title": "AdminService.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\STE\\PFE\\PFE\\backend\\WebApiPfe\\Services\\Implementations\\AdminService.cs", "RelativeDocumentMoniker": "WebApiPfe\\Services\\Implementations\\AdminService.cs", "ToolTip": "C:\\Users\\<USER>\\Desktop\\STE\\PFE\\PFE\\backend\\WebApiPfe\\Services\\Implementations\\AdminService.cs", "RelativeToolTip": "WebApiPfe\\Services\\Implementations\\AdminService.cs", "ViewState": "AgIAAKYAAAAAAAAAAAAAAMMAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-06T23:07:28.715Z"}, {"$type": "Document", "DocumentIndex": 22, "Title": "FournisseurService.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\STE\\PFE\\PFE\\backend\\WebApiPfe\\Services\\Implementations\\FournisseurService.cs", "RelativeDocumentMoniker": "WebApiPfe\\Services\\Implementations\\FournisseurService.cs", "ToolTip": "C:\\Users\\<USER>\\Desktop\\STE\\PFE\\PFE\\backend\\WebApiPfe\\Services\\Implementations\\FournisseurService.cs", "RelativeToolTip": "WebApiPfe\\Services\\Implementations\\FournisseurService.cs", "ViewState": "AgIAANUAAAAAAAAAAAAAAPUAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-06T23:06:51.179Z"}, {"$type": "Document", "DocumentIndex": 23, "Title": "AuthService.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\STE\\PFE\\PFE\\backend\\WebApiPfe\\Services\\Implementations\\AuthService.cs", "RelativeDocumentMoniker": "WebApiPfe\\Services\\Implementations\\AuthService.cs", "ToolTip": "C:\\Users\\<USER>\\Desktop\\STE\\PFE\\PFE\\backend\\WebApiPfe\\Services\\Implementations\\AuthService.cs", "RelativeToolTip": "WebApiPfe\\Services\\Implementations\\AuthService.cs", "ViewState": "AgIAAEMAAAAAAAAAAAAAAGIAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-06T23:05:59.051Z"}, {"$type": "Document", "DocumentIndex": 24, "Title": "TokenService.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\STE\\PFE\\PFE\\backend\\WebApiPfe\\Services\\Token\\TokenService.cs", "RelativeDocumentMoniker": "WebApiPfe\\Services\\Token\\TokenService.cs", "ToolTip": "C:\\Users\\<USER>\\Desktop\\STE\\PFE\\PFE\\backend\\WebApiPfe\\Services\\Token\\TokenService.cs", "RelativeToolTip": "WebApiPfe\\Services\\Token\\TokenService.cs", "ViewState": "AgIAABYAAAAAAAAAAAAQwIUAAAANAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-06T21:44:22.063Z"}, {"$type": "Document", "DocumentIndex": 25, "Title": "Fournisseur.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\STE\\PFE\\PFE\\backend\\WebApiPfe\\Models\\Entity\\Fournisseur.cs", "RelativeDocumentMoniker": "WebApiPfe\\Models\\Entity\\Fournisseur.cs", "ToolTip": "C:\\Users\\<USER>\\Desktop\\STE\\PFE\\PFE\\backend\\WebApiPfe\\Models\\Entity\\Fournisseur.cs", "RelativeToolTip": "WebApiPfe\\Models\\Entity\\Fournisseur.cs", "ViewState": "AgIAAAYAAAAAAAAAAAAAAA4AAAAvAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-06T15:59:35.784Z"}, {"$type": "Document", "DocumentIndex": 26, "Title": "CalculerFraisLivraisonRequest.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\STE\\PFE\\PFE\\backend\\WebApiPfe\\Models\\DTOs\\CalculerFraisLivraisonRequest.cs", "RelativeDocumentMoniker": "WebApiPfe\\Models\\DTOs\\CalculerFraisLivraisonRequest.cs", "ToolTip": "C:\\Users\\<USER>\\Desktop\\STE\\PFE\\PFE\\backend\\WebApiPfe\\Models\\DTOs\\CalculerFraisLivraisonRequest.cs", "RelativeToolTip": "WebApiPfe\\Models\\DTOs\\CalculerFraisLivraisonRequest.cs", "ViewState": "AgIAAAgAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-05T22:13:31.504Z"}, {"$type": "Document", "DocumentIndex": 0, "Title": "appsettings.json", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\STE\\PFE\\PFE\\backend\\WebApiPfe\\appsettings.json", "RelativeDocumentMoniker": "WebApiPfe\\appsettings.json", "ToolTip": "C:\\Users\\<USER>\\Desktop\\STE\\PFE\\PFE\\backend\\WebApiPfe\\appsettings.json", "RelativeToolTip": "WebApiPfe\\appsettings.json", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAgAAAAFAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.001642|", "WhenOpened": "2025-07-05T16:33:41.374Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 1, "Title": "Program.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\STE\\PFE\\PFE\\backend\\WebApiPfe\\Program.cs", "RelativeDocumentMoniker": "WebApiPfe\\Program.cs", "ToolTip": "C:\\Users\\<USER>\\Desktop\\STE\\PFE\\PFE\\backend\\WebApiPfe\\Program.cs", "RelativeToolTip": "WebApiPfe\\Program.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAALIBAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-05T16:33:35.418Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 27, "Title": "StatutRemboursement.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\STE\\PFE\\PFE\\backend\\WebApiPfe\\Models\\Enum\\StatutRemboursement.cs", "RelativeDocumentMoniker": "WebApiPfe\\Models\\Enum\\StatutRemboursement.cs", "ToolTip": "C:\\Users\\<USER>\\Desktop\\STE\\PFE\\PFE\\backend\\WebApiPfe\\Models\\Enum\\StatutRemboursement.cs", "RelativeToolTip": "WebApiPfe\\Models\\Enum\\StatutRemboursement.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAQAAAASAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-05T13:43:53.915Z"}, {"$type": "Bookmark", "Name": "ST:0:0:{fbcae063-e2c0-4ab1-a516-996ea3dafb72}"}, {"$type": "Bookmark", "Name": "ST:0:0:{cce594b6-0c39-4442-ba28-10c64ac7e89f}"}]}]}]}
import {
  environment
} from "./chunk-GFHHPDQ6.js";
import {
  Injectable,
  __async,
  setClassMetadata,
  ɵɵdefineInjectable
} from "./chunk-UBZQS7JS.js";

// src/app/services/image-url.service.ts
var ImageUrlService = class _ImageUrlService {
  // Pour les images, on utilise le serveur backend sans /api
  baseUrl = environment.apiUrl?.replace("/api", "") || "http://localhost:5014";
  placeholderImage = "data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMzAwIiBoZWlnaHQ9IjMwMCIgdmlld0JveD0iMCAwIDMwMCAzMDAiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxyZWN0IHdpZHRoPSIzMDAiIGhlaWdodD0iMzAwIiBmaWxsPSIjRjNGNEY2Ii8+CjxwYXRoIGQ9Ik0xNTAgMTAwQzE2MS4wNDYgMTAwIDE3MCA5MC45NTQzIDE3MCA4MEM1NyA2OS4wNDU3IDE0OC45NTQgNjAgMTQwIDYwQzEyOS4wNDYgNjAgMTIwIDY5LjA0NTcgMTIwIDgwQzEyMCA5MC45NTQzIDEyOS4wNDYgMTAwIDE0MCAxMDBIMTUwWiIgZmlsbD0iIzlDQTNBRiIvPgo8cGF0aCBkPSJNMjQwIDIyMEgyNDBWMjQwSDI0MEgyNDBWMjIwWk0yNDAgMjIwSDYwVjI0MEgyNDBWMjIwWiIgZmlsbD0iIzlDQTNBRiIvPgo8cGF0aCBkPSJNMjEwIDEzMEMxOTguOTU0IDEzMCAxOTAgMTM5LjA0NiAxOTAgMTUwQzE5MCAyMDkuMDU0IDE5OC45NTQgMjIwIDIxMCAyMjBIMjQwVjE1MEMyNDAgMTM5LjA0NiAyMzEuMDQ2IDEzMCAyMjAgMTMwSDIxMFoiIGZpbGw9IiM5Q0EzQUYiLz4KPHR4dCB4PSIxNTAiIHk9IjI3MCIgdGV4dC1hbmNob3I9Im1pZGRsZSIgZmlsbD0iIzlDQTNBRiIgZm9udC1mYW1pbHk9IkFyaWFsLCBzYW5zLXNlcmlmIiBmb250LXNpemU9IjE0Ij5JbWFnZSBub24gZGlzcG9uaWJsZTwvdGV4dD4KPHN2Zz4=";
  constructor() {
    console.log("\u{1F5BC}\uFE0F ImageUrlService initialis\xE9 avec baseUrl:", this.baseUrl);
    console.log("\u{1F5BC}\uFE0F Environment apiUrl:", environment.apiUrl);
  }
  /**
   * Obtient l'URL complète pour n'importe quelle image
   * @param imagePath - Chemin de l'image (peut être null/undefined)
   * @returns URL complète de l'image ou placeholder
   */
  getFullImageUrl(imagePath) {
    console.log("\u{1F5BC}\uFE0F getFullImageUrl appel\xE9 avec:", imagePath);
    if (!imagePath || imagePath.trim() === "") {
      console.log("\u{1F5BC}\uFE0F Chemin vide, retour du placeholder");
      return this.getPlaceholderUrl();
    }
    if (imagePath.startsWith("http://") || imagePath.startsWith("https://")) {
      console.log("\u{1F5BC}\uFE0F URL d\xE9j\xE0 compl\xE8te:", imagePath);
      return imagePath;
    }
    if (imagePath.startsWith("assets/")) {
      console.log("\u{1F5BC}\uFE0F Chemin assets local:", imagePath);
      let correctedPath = imagePath;
      if (imagePath.includes("assets/images/formes/")) {
        correctedPath = imagePath.replace("assets/images/formes/", "assets/formes/");
        console.log("\u{1F527} Correction chemin formes:", imagePath, "\u2192", correctedPath);
      }
      if (imagePath.includes("assets/images/logos/")) {
        correctedPath = imagePath.replace("assets/images/logos/", "assets/logos/");
        console.log("\u{1F527} Correction chemin logos:", imagePath, "\u2192", correctedPath);
      }
      return correctedPath;
    }
    const cleanPath = imagePath.startsWith("/") ? imagePath.substring(1) : imagePath;
    const fullUrl = `${this.baseUrl}/${cleanPath}`;
    console.log("\u{1F5BC}\uFE0F URL construite:", {
      baseUrl: this.baseUrl,
      imagePath,
      cleanPath,
      fullUrl
    });
    return fullUrl;
  }
  /**
   * Obtient l'URL complète pour une image de produit
   * @param imagePath - Chemin de l'image du produit
   * @returns URL complète de l'image du produit
   */
  getProduitImageUrl(imagePath) {
    return this.getFullImageUrl(imagePath);
  }
  /**
   * Obtient l'URL complète pour un logo de fournisseur
   * @param logoPath - Chemin du logo du fournisseur
   * @returns URL complète du logo du fournisseur
   */
  getFournisseurLogoUrl(logoPath) {
    return this.getFullImageUrl(logoPath);
  }
  /**
   * Obtient l'URL complète pour un logo de marque
   * @param logoPath - Chemin du logo de la marque
   * @returns URL complète du logo de la marque
   */
  getMarqueLogoUrl(logoPath) {
    console.log("\u{1F3F7}\uFE0F getMarqueLogoUrl appel\xE9 avec:", logoPath);
    const url = this.getFullImageUrl(logoPath);
    console.log("\u{1F3F7}\uFE0F URL logo marque finale:", url);
    return url;
  }
  /**
   * Obtient l'URL du placeholder par défaut
   * @returns URL du placeholder
   */
  getPlaceholderUrl() {
    return this.placeholderImage;
  }
  /**
   * Vérifie si une URL d'image est valide
   * @param imageUrl - URL à vérifier
   * @returns Promise<boolean> - true si l'image est accessible
   */
  isImageValid(imageUrl) {
    return __async(this, null, function* () {
      try {
        const response = yield fetch(imageUrl, { method: "HEAD" });
        return response.ok;
      } catch {
        return false;
      }
    });
  }
  /**
   * Obtient l'URL d'image avec fallback vers placeholder
   * @param imagePath - Chemin de l'image
   * @returns URL de l'image ou placeholder si invalide
   */
  getImageUrlWithFallback(imagePath) {
    return __async(this, null, function* () {
      const imageUrl = this.getFullImageUrl(imagePath);
      if (imageUrl === this.getPlaceholderUrl()) {
        return imageUrl;
      }
      const isValid = yield this.isImageValid(imageUrl);
      return isValid ? imageUrl : this.getPlaceholderUrl();
    });
  }
  static \u0275fac = function ImageUrlService_Factory(__ngFactoryType__) {
    return new (__ngFactoryType__ || _ImageUrlService)();
  };
  static \u0275prov = /* @__PURE__ */ \u0275\u0275defineInjectable({ token: _ImageUrlService, factory: _ImageUrlService.\u0275fac, providedIn: "root" });
};
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && setClassMetadata(ImageUrlService, [{
    type: Injectable,
    args: [{
      providedIn: "root"
    }]
  }], () => [], null);
})();

export {
  ImageUrlService
};
//# sourceMappingURL=chunk-CZWG52C5.js.map

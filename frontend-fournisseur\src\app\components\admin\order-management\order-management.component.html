<div class="order-management-container">
  <!-- Header -->
  <div class="page-header">
    <div class="header-content">
      <h1 class="page-title">
        <i class="icon-shopping-cart"></i>
        Gestion des Commandes
      </h1>
      <div class="header-actions">
        <button class="btn btn-secondary" (click)="clearFilters()">
          <i class="icon-filter-x"></i>
          Effacer filtres
        </button>
        <button class="btn btn-secondary" (click)="refresh()" [disabled]="isLoading()">
          <i class="icon-refresh"></i>
          Actualiser
        </button>
      </div>
    </div>
  </div>

  <!-- Filtres et Recherche -->
  <div class="filters-section">
    <div class="search-box">
      <i class="icon-search"></i>
      <input
        type="text"
        placeholder="Rechercher par numéro, client..."
        [value]="searchTerm()"
        (input)="searchTerm.set($any($event.target).value)"
        class="search-input">
    </div>

    <div class="filters">
      <select [value]="selectedStatus()" (change)="selectedStatus.set($any($event.target).value)" class="filter-select">
        <option value="">Tous les statuts</option>
        <option *ngFor="let statut of statuts" [value]="statut">{{ getStatusText(statut) }}</option>
      </select>

      <input
        type="date"
        [value]="dateDebut()"
        (change)="dateDebut.set($any($event.target).value)"
        class="filter-date"
        placeholder="Date début">

      <input
        type="date"
        [value]="dateFin()"
        (change)="dateFin.set($any($event.target).value)"
        class="filter-date"
        placeholder="Date fin">
    </div>
  </div>

  <!-- Message d'erreur -->
  <div *ngIf="error()" class="error-message">
    <i class="icon-alert"></i>
    {{ error() }}
  </div>

  <!-- Loading -->
  <div *ngIf="isLoading()" class="loading-container">
    <div class="loading-spinner"></div>
    <p>Chargement des commandes...</p>
  </div>



  <!-- Vue détaillée des commandes avec fournisseurs -->
  <div *ngIf="!isLoading() && !error()" class="detailed-orders-container">
    <div *ngFor="let commande of filteredCommandesAvecFournisseur()" class="order-card">
      <!-- En-tête de la commande client -->
      <div class="order-header">
        <div class="order-info">
          <h3 class="order-title">{{ commande.numeroCommande }}</h3>
          <div class="client-info">
            <strong>{{ commande.clientNom }}</strong> - {{ commande.clientEmail }}
          </div>
          <div class="order-meta">
            <span class="order-date">{{ formatDate(commande.dateCommande) }}</span>
            <span class="status-badge" [ngClass]="getStatusClass(commande.statut)">
              {{ getStatusText(commande.statut) }}
            </span>
            <span class="order-amount">{{ formatPrice(commande.montantTotal) }}</span>
          </div>
        </div>
        <div class="order-actions">
          <button
            class="btn btn-sm btn-outline"
            (click)="viewOrderDetails(commande)"
            [class.active]="isOrderExpanded(commande.id)">
            <i class="icon-chevron-down" [class.rotated]="isOrderExpanded(commande.id)"></i>
            {{ isOrderExpanded(commande.id) ? 'Masquer' : 'Voir' }} détails
          </button>
        </div>
      </div>

      <!-- Commandes fournisseur associées (affichées seulement si expandée) -->
      <div *ngIf="isOrderExpanded(commande.id)" class="supplier-orders">
        <h4 class="supplier-orders-title">
          <i class="icon-truck"></i>
          Commandes Fournisseur ({{ commande.commandesFournisseur.length }})
        </h4>

        <div *ngIf="commande.commandesFournisseur.length === 0" class="no-supplier-orders">
          <p>Aucune commande fournisseur associée</p>
        </div>

        <div *ngFor="let cmdFournisseur of commande.commandesFournisseur" class="supplier-order-card">
          <div class="supplier-order-header">
            <div class="supplier-info">
              <strong>{{ cmdFournisseur.fournisseurNom }}</strong>
              <span class="supplier-email">{{ cmdFournisseur.fournisseurEmail }}</span>
            </div>
            <div class="supplier-order-meta">
              <span class="supplier-reference">{{ cmdFournisseur.reference }}</span>
              <span class="status-badge" [ngClass]="getCommandeFournisseurStatusClass(cmdFournisseur.statut)">
                {{ getCommandeFournisseurStatusText(cmdFournisseur.statut) }}
              </span>
              <span class="supplier-amount">{{ formatPrice(cmdFournisseur.montantTotal) }}</span>
            </div>
          </div>

          <!-- Détails de livraison -->
          <div *ngIf="cmdFournisseur.dateLivraison || cmdFournisseur.numeroBonLivraison" class="delivery-info">
            <div *ngIf="cmdFournisseur.dateLivraison" class="delivery-date">
              <i class="icon-calendar"></i>
              Livraison prévue: {{ formatDate(cmdFournisseur.dateLivraison) }}
            </div>
            <div *ngIf="cmdFournisseur.numeroBonLivraison" class="delivery-number">
              <i class="icon-package"></i>
              Bon de livraison: {{ cmdFournisseur.numeroBonLivraison }}
            </div>
          </div>

          <!-- Lignes de commande -->
          <div class="order-lines">
            <div *ngFor="let ligne of cmdFournisseur.lignes" class="order-line">
              <div class="product-info">
                <span class="product-name">{{ ligne.produitNom }}</span>
                <span class="product-ref">{{ ligne.referenceProduit }}</span>
              </div>
              <div class="quantity-price">
                <span class="quantity">{{ ligne.quantite }}x</span>
                <span class="unit-price">{{ formatPrice(ligne.prixUnitaire) }}</span>
                <span class="line-total">{{ formatPrice(ligne.montantLigne) }}</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Message si aucune commande -->
    <div *ngIf="commandesAvecFournisseur().length === 0" class="no-data">
      <i class="icon-shopping-cart"></i>
      <h3>Aucune commande trouvée</h3>
      <p>Aucune commande ne correspond aux critères de recherche.</p>
    </div>
  </div>

  <!-- Pagination -->
  <div *ngIf="totalPages() > 1" class="pagination-container">
    <div class="pagination-info">
      Affichage de {{ (currentPage() - 1) * pageSize() + 1 }} à 
      {{ Math.min(currentPage() * pageSize(), totalItems()) }} sur {{ totalItems() }} commandes
    </div>
    
    <div class="pagination">
      <button 
        class="btn-page"
        [disabled]="currentPage() === 1"
        (click)="onPageChange(currentPage() - 1)">
        <i class="icon-chevron-left"></i>
      </button>
      
      <span class="page-info">
        Page {{ currentPage() }} sur {{ totalPages() }}
      </span>
      
      <button 
        class="btn-page"
        [disabled]="currentPage() === totalPages()"
        (click)="onPageChange(currentPage() + 1)">
        <i class="icon-chevron-right"></i>
      </button>
    </div>
  </div>

  <!-- Statistiques rapides -->
  <div class="stats-summary">
    <div class="stat-card">
      <div class="stat-value">{{ getTotalCommandes() }}</div>
      <div class="stat-label">Commandes</div>
    </div>
</div>

import {
  environment
} from "./chunk-GFHHPDQ6.js";
import {
  HttpClient
} from "./chunk-7JDDWGD3.js";
import {
  Injectable,
  catchError,
  setClassMetadata,
  tap,
  ɵɵdefineInjectable,
  ɵɵinject
} from "./chunk-UBZQS7JS.js";

// src/app/services/demande.service.ts
var StatutDemande;
(function(StatutDemande2) {
  StatutDemande2[StatutDemande2["EnAttente"] = 0] = "EnAttente";
  StatutDemande2[StatutDemande2["Approuvee"] = 1] = "Approuvee";
  StatutDemande2[StatutDemande2["Rejetee"] = 2] = "Rejetee";
})(StatutDemande || (StatutDemande = {}));
var DemandeService = class _DemandeService {
  http;
  apiUrl = environment.apiUrl;
  constructor(http) {
    this.http = http;
  }
  // Demandes de catégories
  getAllDemandesCategories() {
    return this.http.get(`${this.apiUrl}/DemandesCategories`);
  }
  getDemandesCategoriesByStatut(statut) {
    return this.http.get(`${this.apiUrl}/DemandesCategories/statut/${statut}`);
  }
  getMesDemandesCategories() {
    return this.http.get(`${this.apiUrl}/DemandesCategories/mes-demandes`);
  }
  getDemandeCategorie(id) {
    return this.http.get(`${this.apiUrl}/DemandesCategories/${id}`);
  }
  createDemandeCategorie(demande) {
    console.log("\u{1F680} Service createDemandeCategorie appel\xE9");
    console.log("\u{1F4DD} URL:", `${this.apiUrl}/DemandesCategories`);
    console.log("\u{1F4E4} Donn\xE9es \xE0 envoyer:", demande);
    return this.http.post(`${this.apiUrl}/DemandesCategories`, demande).pipe(tap((response) => {
      console.log("\u2705 R\xE9ponse du service re\xE7ue:", response);
    }), catchError((error) => {
      console.error("\u274C Erreur dans le service:", error);
      console.error("\u{1F4CA} Status:", error.status);
      console.error("\u{1F4CB} Message:", error.message);
      console.error("\u{1F50D} Error complet:", error);
      throw error;
    }));
  }
  traiterDemandeCategorie(id, traitement) {
    return this.http.put(`${this.apiUrl}/DemandesCategories/${id}/traiter`, traitement);
  }
  deleteDemandeCategorie(id) {
    return this.http.delete(`${this.apiUrl}/DemandesCategories/${id}`);
  }
  // Demandes de sous-catégories
  getAllDemandesSousCategories() {
    return this.http.get(`${this.apiUrl}/DemandesSousCategories`);
  }
  getDemandesSousCategoriesByStatut(statut) {
    return this.http.get(`${this.apiUrl}/DemandesSousCategories/statut/${statut}`);
  }
  getMesDemandesSousCategories() {
    return this.http.get(`${this.apiUrl}/DemandesSousCategories/mes-demandes`);
  }
  getDemandeSousCategorie(id) {
    return this.http.get(`${this.apiUrl}/DemandesSousCategories/${id}`);
  }
  createDemandeSousCategorie(demande) {
    return this.http.post(`${this.apiUrl}/DemandesSousCategories`, demande);
  }
  traiterDemandeSousCategorie(id, traitement) {
    return this.http.put(`${this.apiUrl}/DemandesSousCategories/${id}/traiter`, traitement);
  }
  deleteDemandeSousCategorie(id) {
    return this.http.delete(`${this.apiUrl}/DemandesSousCategories/${id}`);
  }
  // Méthodes utilitaires
  getStatutLabel(statut) {
    switch (statut) {
      case StatutDemande.EnAttente:
        return "En attente";
      case StatutDemande.Approuvee:
        return "Approuv\xE9e";
      case StatutDemande.Rejetee:
        return "Rejet\xE9e";
      default:
        return "Inconnu";
    }
  }
  getStatutColor(statut) {
    switch (statut) {
      case StatutDemande.EnAttente:
        return "#ff9800";
      case StatutDemande.Approuvee:
        return "#4caf50";
      case StatutDemande.Rejetee:
        return "#f44336";
      default:
        return "#666";
    }
  }
  static \u0275fac = function DemandeService_Factory(__ngFactoryType__) {
    return new (__ngFactoryType__ || _DemandeService)(\u0275\u0275inject(HttpClient));
  };
  static \u0275prov = /* @__PURE__ */ \u0275\u0275defineInjectable({ token: _DemandeService, factory: _DemandeService.\u0275fac, providedIn: "root" });
};
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && setClassMetadata(DemandeService, [{
    type: Injectable,
    args: [{
      providedIn: "root"
    }]
  }], () => [{ type: HttpClient }], null);
})();

export {
  StatutDemande,
  DemandeService
};
//# sourceMappingURL=chunk-WQ24PYVH.js.map

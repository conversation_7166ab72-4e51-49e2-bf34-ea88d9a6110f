{"version": 3, "sources": ["src/app/components/admin/profile/admin-profile/admin-profile.component.ts"], "sourcesContent": ["import { Component } from '@angular/core';\nimport { CommonModule } from '@angular/common';\n\n@Component({\n  selector: 'app-admin-profile',\n  standalone: true,\n  imports: [CommonModule],\n  template: `<div><h1>👤 Mon profil admin</h1><p>En développement...</p></div>`\n})\nexport class AdminProfileComponent {}\n"], "mappings": ";;;;;;;;;;;;AASM,IAAO,wBAAP,MAAO,uBAAqB;;qCAArB,wBAAqB;EAAA;yEAArB,wBAAqB,WAAA,CAAA,CAAA,mBAAA,CAAA,GAAA,OAAA,GAAA,MAAA,GAAA,UAAA,SAAA,+BAAA,IAAA,KAAA;AAAA,QAAA,KAAA,GAAA;AAFrB,MAAA,yBAAA,GAAA,KAAA,EAAK,GAAA,IAAA;AAAI,MAAA,iBAAA,GAAA,4BAAA;AAAmB,MAAA,uBAAA;AAAK,MAAA,yBAAA,GAAA,GAAA;AAAG,MAAA,iBAAA,GAAA,wBAAA;AAAmB,MAAA,uBAAA,EAAI;;oBAD5D,YAAY,GAAA,eAAA,EAAA,CAAA;;;sEAGX,uBAAqB,CAAA;UANjC;WAAU;MACT,UAAU;MACV,YAAY;MACZ,SAAS,CAAC,YAAY;MACtB,UAAU;KACX;;;;6EACY,uBAAqB,EAAA,WAAA,yBAAA,UAAA,6EAAA,YAAA,GAAA,CAAA;AAAA,GAAA;", "names": []}
import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterOutlet, Router, RouterLink, RouterLinkActive } from '@angular/router';
import { AdminAuthService } from '../../../../services/admin-auth.service';
import { AdminUser } from '../../../../models/admin.model';
import { NotificationIconComponent } from '../../../notification-icon/notification-icon.component';

@Component({
  selector: 'app-admin-layout',
  standalone: true,
  imports: [CommonModule, RouterOutlet, RouterLink, RouterLinkActive, NotificationIconComponent],
  template: `
    <div class="admin-layout">
      <!-- Header sur toute la largeur -->
      <header class="admin-header">
        <h1>Administration - OptiLet</h1>
        <div class="header-actions">
          <app-notification-icon></app-notification-icon>
          <span class="user-role">{{ currentUser?.role }}</span>
        </div>
      </header>

      <!-- Container pour sidebar et contenu -->
      <div class="admin-body">
        <div class="admin-sidebar">
          <nav class="sidebar-nav">
            <a routerLink="/admin/dashboard" routerLinkActive="active" class="nav-item">🏠 Dashboard</a>
            <a routerLink="/admin/dashboard/users" routerLinkActive="active" class="nav-item">👥 Utilisateurs</a>
            <a routerLink="/admin/dashboard/products" routerLinkActive="active" class="nav-item">📦 Produits</a>
            <a routerLink="/admin/dashboard/demandes" routerLinkActive="active" class="nav-item">📋 Demandes</a>
            <a routerLink="/admin/dashboard/categories" routerLinkActive="active" class="nav-item">📁 Catégories</a>
            <a routerLink="/admin/dashboard/orders" routerLinkActive="active" class="nav-item">🛒 Commandes</a>
            <a routerLink="/admin/dashboard/avis-moderation" routerLinkActive="active" class="nav-item">⭐ Modération Avis</a>
            <a routerLink="/admin/dashboard/reports" routerLinkActive="active" class="nav-item">📈 Rapports</a>
          </nav>
        </div>

        <main class="admin-content">
          <router-outlet></router-outlet>
        </main>
      </div>
    </div>
  `,
  styles: [`
    .admin-layout {
      display: flex;
      flex-direction: column;
      min-height: 100vh;
      background: #f8fafc;
    }
    
    .admin-body {
      display: flex;
      flex: 1;
    }

    .admin-sidebar {
      width: 280px;
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      color: white;
      display: flex;
      flex-direction: column;
      box-shadow: 4px 0 20px rgba(102, 126, 234, 0.2);
    }
    

    
    .sidebar-nav {
      flex: 1;
      padding: 1rem 0;
      display: flex;
      flex-direction: column;
    }
    
    .nav-item {
      display: flex;
      align-items: center;
      padding: 1rem 2rem;
      color: rgba(255, 255, 255, 0.9);
      text-decoration: none;
      transition: all 0.3s ease;
      border-left: 3px solid transparent;
      font-weight: 500;
      font-size: 0.95rem;
      margin: 0;
      border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    }

    .nav-item:hover {
      background: rgba(255, 255, 255, 0.15);
      color: white;
      border-left-color: #667eea;
      transform: translateX(4px);
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    }

    .nav-item.active {
      background: rgba(102, 126, 234, 0.2);
      color: white;
      border-left-color: #667eea;
      font-weight: 600;
    }
    

    
    .admin-main {
      flex: 1;
      display: flex;
      flex-direction: column;
    }
    
    .admin-header {
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      padding: 1.5rem 2rem;
      border-bottom: none;
      display: flex;
      justify-content: space-between;
      align-items: center;
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
      width: 100%;
      position: sticky;
      top: 0;
      z-index: 1000;
    }

    .header-actions {
      display: flex;
      align-items: center;
      gap: 1.5rem;
    }

    .admin-header h1 {
      margin: 0;
      font-size: 1.75rem;
      color: white;
      font-weight: 600;
      text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    }

    .user-role {
      padding: 0.75rem 1.25rem;
      background: rgba(255, 255, 255, 0.2);
      color: white;
      border-radius: 25px;
      font-size: 0.875rem;
      font-weight: 500;
      border: 1px solid rgba(255, 255, 255, 0.3);
      backdrop-filter: blur(10px);
      transition: all 0.3s ease;
    }

    .user-role:hover {
      background: rgba(255, 255, 255, 0.3);
      transform: translateY(-1px);
    }
    
    .admin-content {
      flex: 1;
      padding: 0;
      overflow-y: auto;
      background: #f8fafc;
    }
    
    @media (max-width: 768px) {
      .admin-header {
        padding: 1rem;
      }

      .admin-header h1 {
        font-size: 1.25rem;
      }

      .admin-body {
        flex-direction: column;
      }

      .admin-sidebar {
        width: 100%;
        height: auto;
        order: 2;
      }

      .admin-content {
        order: 1;
      }

      .sidebar-nav {
        display: flex;
        overflow-x: auto;
        padding: 1rem;
        gap: 0.5rem;
      }

      .nav-item {
        white-space: nowrap;
        min-width: 120px;
        text-align: center;
        padding: 0.75rem 1rem;
        border-radius: 12px;
        border-left: none;
        border-bottom: 3px solid transparent;
      }

      .nav-item:hover {
        border-left: none;
        border-bottom-color: #667eea;
        transform: translateX(0);
        transform: translateY(-2px);
      }
    }
  `]
})
export class AdminLayoutComponent implements OnInit {
  currentUser: AdminUser | null = null;

  constructor(
    private adminAuthService: AdminAuthService,
    private router: Router
  ) {}

  ngOnInit(): void {
    this.adminAuthService.currentUser$.subscribe(user => {
      this.currentUser = user;
    });
  }

  logout(): void {
    this.adminAuthService.logout().subscribe(() => {
      this.router.navigate(['/admin/login']);
    });
  }
}

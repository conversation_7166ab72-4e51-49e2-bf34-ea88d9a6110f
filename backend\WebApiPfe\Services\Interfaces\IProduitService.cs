using System.Collections.Generic;
using System.Threading.Tasks;
using WebApiPfe.DTOs.CreateDTOs;
using WebApiPfe.DTOs.ReadDTOs;
using WebApiPfe.DTOs.UpdateDTOs;
using WebApiPfe.Models.Entity;


namespace WebApiPfe.Services.Interfaces
{
    public interface IProduitService
    {
        //CRUD
        Task<ProduitDto> CreateAsync(ProduitCreateDto produitDto);
        Task UpdateAsync(ProduitUpdateDto produitDto);
        Task DeleteAsync(int id);
        Task<bool> ExistsAsync(int id);

        //Recuperation
        Task<IEnumerable<ProduitDto>> GetAllAsync();
        Task<ProduitDto> GetByIdAsync(int id);
        Task<ProduitDto> GetByReferenceOriginalAsync(string reference);
        Task<ProduitDto> GetByReferenceFournisseurAsync(string reference);
        Task<ProduitDto> GetByCodeABarreAsync(string codeABarre);

        //Recherche/Filtrage
        Task<IEnumerable<Produit>> GetBySousCategorieAsync(int sousCategorieId);
        Task<IEnumerable<Produit>> GetByCategorieAsync(int categorieId);
        Task<IEnumerable<ProduitDto>> GetByFournisseurAsync(int fournisseurId);
        Task<IEnumerable<Produit>> GetByMarqueAsync(int marqueId);
        Task<IEnumerable<Produit>> GetByFormeAsync(int formeId);
        Task<IEnumerable<Produit>> SearchAsync(string searchTerm);
        Task<IEnumerable<ProduitDto>> GetProduitsEnPromotionAsync();
        Task<IEnumerable<ProduitDto>> GetProduitsEnStockAsync();
        Task<IEnumerable<ImageProduitDto>> GetImagesForProduitAsync(int produitId);

        //Autre
        Task<Dictionary<int, string>> GetProduitsForDropdownAsync();
        Task<IEnumerable<Produit>> GetMeilleuresVentesAsync(int limit = 20);
        Task<IEnumerable<Produit>> GetNouveauxArrivagesAsync(int limit = 20);

        //Images
        Task AddImageToProduitAsync(int produitId, string imagePath, bool? isMain = null);
        Task<string> RemoveImageFromProduitAsync(int produitId, int imageId);
        Task UpdateImageAsync(int produitId, ImageProduitUpdateDto imageDto);
        Task SetMainImageAsync(int produitId, int imageId);

        //Validation
        Task<bool> IsReferenceUniqueAsync(string reference, int? ignoreId = null);
        Task<bool> IsCodeABarreUniqueAsync(string codeABarre, int? ignoreId = null);

        //Mise à jour specifiques
        Task UpdateStockAsync(ProduitStockUpdateDto stockDto);        
        Task UpdatePrixAsync(int produitId, decimal nouveauPrixHT);       
    }
}

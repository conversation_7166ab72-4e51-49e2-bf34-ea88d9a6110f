{"version": 3, "sources": ["src/app/services/produit.service.ts"], "sourcesContent": ["import { Injectable } from '@angular/core';\nimport { HttpClient, HttpParams } from '@angular/common/http';\nimport { Observable } from 'rxjs';\nimport { tap } from 'rxjs/operators';\nimport {\n  Produit,\n  ProduitCreate,\n  ProduitUpdate,\n  StockUpdate,\n  PrixUpdate,\n  ImageProduit\n} from '../models';\nimport { environment } from '../../environments/environment';\n\n// Interfaces pour les réponses API selon le backend\nexport interface ProduitResponse {\n  // Le backend retourne directement le ProduitDto, pas un wrapper\n}\n\n@Injectable({\n  providedIn: 'root'\n})\nexport class ProduitService {\n  private readonly API_URL = `${environment.apiUrl || 'https://localhost:7264/api'}/Produits`;\n\n  constructor(private http: HttpClient) {}\n\n  /**\n   * GET /api/Produits - Obtenir tous les produits\n   */\n  getAll(filters?: any): Observable<Produit[]> {\n    let params = new HttpParams();\n\n    if (filters) {\n      Object.keys(filters).forEach(key => {\n        if (filters[key] !== null && filters[key] !== undefined) {\n          params = params.set(key, filters[key].toString());\n        }\n      });\n    }\n\n    console.log('📦 Récupération des produits avec filtres:', filters);\n\n    return this.http.get<Produit[]>(this.API_URL, { params })\n      .pipe(\n        tap(response => console.log('✅ Produits récupérés:', response))\n      );\n  }\n\n  /**\n   * GET /api/Produits/{id} - Obtenir un produit par ID\n   */\n  getById(id: number): Observable<Produit> {\n    console.log('🔍 Récupération du produit ID:', id);\n\n    return this.http.get<Produit>(`${this.API_URL}/${id}`)\n      .pipe(\n        tap(response => console.log('✅ Produit récupéré:', response))\n      );\n  }\n\n  /**\n   * POST /api/Produits - Créer un nouveau produit (multipart/form-data)\n   */\n  create(produit: ProduitCreate): Observable<Produit> {\n    console.log('➕ Création d\\'un nouveau produit:', produit);\n\n    // Créer FormData pour multipart/form-data\n    const formData = new FormData();\n\n    // Ajouter les champs du produit\n    formData.append('referenceOriginal', produit.referenceOriginal);\n    if (produit.referenceFournisseur) {\n      formData.append('referenceFournisseur', produit.referenceFournisseur);\n    }\n    formData.append('codeABarre', produit.codeABarre);\n    formData.append('nom', produit.nom);\n    formData.append('description', produit.description);\n    formData.append('prixAchatHT', produit.prixAchatHT.toString());\n    formData.append('prixVenteHT', produit.prixVenteHT.toString());\n    formData.append('tauxTVAId', produit.tauxTVAId.toString());\n    formData.append('stock', produit.stock.toString());\n    formData.append('sousCategorieId', produit.sousCategorieId.toString());\n    formData.append('marqueId', produit.marqueId.toString());\n    formData.append('formeId', produit.formeId.toString());\n    formData.append('fournisseurId', produit.fournisseurId.toString());\n\n    if (produit.pourcentageRemise !== undefined) {\n      formData.append('pourcentageRemise', produit.pourcentageRemise.toString());\n    }\n\n    // Ajouter les fichiers images\n    if (produit.imageFiles && produit.imageFiles.length > 0) {\n      produit.imageFiles.forEach((file, index) => {\n        formData.append('imageFiles', file, file.name);\n      });\n    }\n\n    return this.http.post<Produit>(this.API_URL, formData)\n      .pipe(\n        tap(response => console.log('✅ Produit créé:', response))\n      );\n  }\n\n  /**\n   * PUT /api/Produits/{id} - Mettre à jour un produit (multipart/form-data)\n   */\n  update(id: number, produit: ProduitUpdate): Observable<void> {\n    console.log('✏️ Mise à jour du produit ID:', id, produit);\n\n    // Créer FormData pour multipart/form-data\n    const formData = new FormData();\n\n    formData.append('id', produit.id.toString());\n    formData.append('nom', produit.nom);\n    formData.append('description', produit.description);\n\n    if (produit.prixVenteHT !== undefined) {\n      formData.append('prixVenteHT', produit.prixVenteHT.toString());\n    }\n    if (produit.stock !== undefined) {\n      formData.append('stock', produit.stock.toString());\n    }\n    if (produit.pourcentageRemise !== undefined) {\n      formData.append('pourcentageRemise', produit.pourcentageRemise.toString());\n    }\n\n    // Ajouter les fichiers images\n    if (produit.imageFiles && produit.imageFiles.length > 0) {\n      produit.imageFiles.forEach((file, index) => {\n        formData.append('imageFiles', file, file.name);\n      });\n    }\n\n    return this.http.put<void>(`${this.API_URL}/${id}`, formData)\n      .pipe(\n        tap(() => console.log('✅ Produit mis à jour'))\n      );\n  }\n\n  /**\n   * Supprimer un produit\n   */\n  delete(id: number): Observable<void> {\n    return this.http.delete<void>(`${this.API_URL}/${id}`);\n  }\n\n  /**\n   * Obtenir les produits par sous-catégorie\n   */\n  getBySousCategorie(sousCategorieId: number): Observable<Produit[]> {\n    return this.http.get<Produit[]>(`${this.API_URL}/by-sous-categorie/${sousCategorieId}`);\n  }\n\n  /**\n   * Obtenir les produits par catégorie\n   */\n  getByCategorie(categorieId: number): Observable<Produit[]> {\n    return this.http.get<Produit[]>(`${this.API_URL}/by-categorie/${categorieId}`);\n  }\n\n  /**\n   * Obtenir les produits par fournisseur\n   */\n  getByFournisseur(fournisseurId: number): Observable<Produit[]> {\n    return this.http.get<Produit[]>(`${this.API_URL}/by-fournisseur/${fournisseurId}`);\n  }\n\n  /**\n   * Obtenir les produits par marque\n   */\n  getByMarque(marqueId: number): Observable<Produit[]> {\n    return this.http.get<Produit[]>(`${this.API_URL}/by-marque/${marqueId}`);\n  }\n\n  /**\n   * Rechercher des produits\n   */\n  search(query: string, filters?: any): Observable<Produit[]> {\n    let params = new HttpParams().set('q', query);\n    \n    if (filters) {\n      Object.keys(filters).forEach(key => {\n        if (filters[key] !== null && filters[key] !== undefined) {\n          params = params.set(key, filters[key].toString());\n        }\n      });\n    }\n    \n    return this.http.get<Produit[]>(`${this.API_URL}/search`, { params });\n  }\n\n  /**\n   * GET /api/Produits/dropdown - Obtenir les produits pour dropdown\n   */\n  getDropdown(): Observable<{[key: number]: string}> {\n    return this.http.get<{[key: number]: string}>(`${this.API_URL}/dropdown`);\n  }\n\n  /**\n   * PATCH /api/Produits/{id}/stock - Mettre à jour le stock d'un produit\n   */\n  updateStock(id: number, stockData: StockUpdate): Observable<void> {\n    console.log('📊 Mise à jour du stock du produit ID:', id, stockData);\n\n    return this.http.patch<void>(`${this.API_URL}/${id}/stock`, stockData)\n      .pipe(\n        tap(() => console.log('✅ Stock mis à jour'))\n      );\n  }\n\n  /**\n   * PATCH /api/Produits/{id}/prix - Mettre à jour le prix d'un produit\n   */\n  updatePrix(id: number, prixData: PrixUpdate): Observable<void> {\n    console.log('💰 Mise à jour du prix du produit ID:', id, prixData);\n\n    return this.http.patch<void>(`${this.API_URL}/${id}/prix`, prixData)\n      .pipe(\n        tap(() => console.log('✅ Prix mis à jour'))\n      );\n  }\n\n  /**\n   * Obtenir un produit par référence originale\n   */\n  getByReferenceOriginal(reference: string): Observable<Produit> {\n    return this.http.get<Produit>(`${this.API_URL}/by-reference-original/${reference}`);\n  }\n\n  /**\n   * Obtenir un produit par référence fournisseur\n   */\n  getByReferenceFournisseur(reference: string): Observable<Produit> {\n    return this.http.get<Produit>(`${this.API_URL}/by-reference-fournisseur/${reference}`);\n  }\n\n  /**\n   * Obtenir les produits par forme\n   */\n  getByForme(formeId: number): Observable<Produit[]> {\n    return this.http.get<Produit[]>(`${this.API_URL}/by-forme/${formeId}`);\n  }\n\n  // Gestion des images selon les endpoints backend\n  /**\n   * GET /api/Produits/{id}/images - Obtenir les images d'un produit\n   */\n  getImages(produitId: number): Observable<ImageProduit[]> {\n    return this.http.get<ImageProduit[]>(`${this.API_URL}/${produitId}/images`);\n  }\n\n  /**\n   * POST /api/Produits/{id}/images - Ajouter une image à un produit\n   */\n  addImage(produitId: number, imageData: FormData): Observable<ImageProduit> {\n    return this.http.post<ImageProduit>(`${this.API_URL}/${produitId}/images`, imageData);\n  }\n\n  /**\n   * DELETE /api/Produits/{produitId}/images/{imageId} - Supprimer une image\n   */\n  deleteImage(produitId: number, imageId: number): Observable<string> {\n    return this.http.delete<string>(`${this.API_URL}/${produitId}/images/${imageId}`);\n  }\n\n  /**\n   * PUT /api/Produits/{produitId}/images/{imageId} - Mettre à jour une image\n   */\n  updateImage(produitId: number, imageId: number, imageData: any): Observable<void> {\n    return this.http.put<void>(`${this.API_URL}/${produitId}/images/${imageId}`, imageData);\n  }\n\n  /**\n   * PATCH /api/Produits/{produitId}/images/{imageId}/main - Définir comme image principale\n   */\n  setMainImage(produitId: number, imageId: number): Observable<void> {\n    return this.http.patch<void>(`${this.API_URL}/${produitId}/images/${imageId}/main`, {});\n  }\n\n}\n"], "mappings": ";;;;;;;;;;;;;;;;AAsBM,IAAO,iBAAP,MAAO,gBAAc;EAGL;EAFH,UAAU,GAAG,YAAY,UAAU,4BAA4B;EAEhF,YAAoB,MAAgB;AAAhB,SAAA,OAAA;EAAmB;;;;EAKvC,OAAO,SAAa;AAClB,QAAI,SAAS,IAAI,WAAU;AAE3B,QAAI,SAAS;AACX,aAAO,KAAK,OAAO,EAAE,QAAQ,SAAM;AACjC,YAAI,QAAQ,GAAG,MAAM,QAAQ,QAAQ,GAAG,MAAM,QAAW;AACvD,mBAAS,OAAO,IAAI,KAAK,QAAQ,GAAG,EAAE,SAAQ,CAAE;QAClD;MACF,CAAC;IACH;AAEA,YAAQ,IAAI,2DAA8C,OAAO;AAEjE,WAAO,KAAK,KAAK,IAAe,KAAK,SAAS,EAAE,OAAM,CAAE,EACrD,KACC,IAAI,cAAY,QAAQ,IAAI,uCAAyB,QAAQ,CAAC,CAAC;EAErE;;;;EAKA,QAAQ,IAAU;AAChB,YAAQ,IAAI,+CAAkC,EAAE;AAEhD,WAAO,KAAK,KAAK,IAAa,GAAG,KAAK,OAAO,IAAI,EAAE,EAAE,EAClD,KACC,IAAI,cAAY,QAAQ,IAAI,qCAAuB,QAAQ,CAAC,CAAC;EAEnE;;;;EAKA,OAAO,SAAsB;AAC3B,YAAQ,IAAI,4CAAqC,OAAO;AAGxD,UAAM,WAAW,IAAI,SAAQ;AAG7B,aAAS,OAAO,qBAAqB,QAAQ,iBAAiB;AAC9D,QAAI,QAAQ,sBAAsB;AAChC,eAAS,OAAO,wBAAwB,QAAQ,oBAAoB;IACtE;AACA,aAAS,OAAO,cAAc,QAAQ,UAAU;AAChD,aAAS,OAAO,OAAO,QAAQ,GAAG;AAClC,aAAS,OAAO,eAAe,QAAQ,WAAW;AAClD,aAAS,OAAO,eAAe,QAAQ,YAAY,SAAQ,CAAE;AAC7D,aAAS,OAAO,eAAe,QAAQ,YAAY,SAAQ,CAAE;AAC7D,aAAS,OAAO,aAAa,QAAQ,UAAU,SAAQ,CAAE;AACzD,aAAS,OAAO,SAAS,QAAQ,MAAM,SAAQ,CAAE;AACjD,aAAS,OAAO,mBAAmB,QAAQ,gBAAgB,SAAQ,CAAE;AACrE,aAAS,OAAO,YAAY,QAAQ,SAAS,SAAQ,CAAE;AACvD,aAAS,OAAO,WAAW,QAAQ,QAAQ,SAAQ,CAAE;AACrD,aAAS,OAAO,iBAAiB,QAAQ,cAAc,SAAQ,CAAE;AAEjE,QAAI,QAAQ,sBAAsB,QAAW;AAC3C,eAAS,OAAO,qBAAqB,QAAQ,kBAAkB,SAAQ,CAAE;IAC3E;AAGA,QAAI,QAAQ,cAAc,QAAQ,WAAW,SAAS,GAAG;AACvD,cAAQ,WAAW,QAAQ,CAAC,MAAM,UAAS;AACzC,iBAAS,OAAO,cAAc,MAAM,KAAK,IAAI;MAC/C,CAAC;IACH;AAEA,WAAO,KAAK,KAAK,KAAc,KAAK,SAAS,QAAQ,EAClD,KACC,IAAI,cAAY,QAAQ,IAAI,8BAAmB,QAAQ,CAAC,CAAC;EAE/D;;;;EAKA,OAAO,IAAY,SAAsB;AACvC,YAAQ,IAAI,8CAAiC,IAAI,OAAO;AAGxD,UAAM,WAAW,IAAI,SAAQ;AAE7B,aAAS,OAAO,MAAM,QAAQ,GAAG,SAAQ,CAAE;AAC3C,aAAS,OAAO,OAAO,QAAQ,GAAG;AAClC,aAAS,OAAO,eAAe,QAAQ,WAAW;AAElD,QAAI,QAAQ,gBAAgB,QAAW;AACrC,eAAS,OAAO,eAAe,QAAQ,YAAY,SAAQ,CAAE;IAC/D;AACA,QAAI,QAAQ,UAAU,QAAW;AAC/B,eAAS,OAAO,SAAS,QAAQ,MAAM,SAAQ,CAAE;IACnD;AACA,QAAI,QAAQ,sBAAsB,QAAW;AAC3C,eAAS,OAAO,qBAAqB,QAAQ,kBAAkB,SAAQ,CAAE;IAC3E;AAGA,QAAI,QAAQ,cAAc,QAAQ,WAAW,SAAS,GAAG;AACvD,cAAQ,WAAW,QAAQ,CAAC,MAAM,UAAS;AACzC,iBAAS,OAAO,cAAc,MAAM,KAAK,IAAI;MAC/C,CAAC;IACH;AAEA,WAAO,KAAK,KAAK,IAAU,GAAG,KAAK,OAAO,IAAI,EAAE,IAAI,QAAQ,EACzD,KACC,IAAI,MAAM,QAAQ,IAAI,8BAAsB,CAAC,CAAC;EAEpD;;;;EAKA,OAAO,IAAU;AACf,WAAO,KAAK,KAAK,OAAa,GAAG,KAAK,OAAO,IAAI,EAAE,EAAE;EACvD;;;;EAKA,mBAAmB,iBAAuB;AACxC,WAAO,KAAK,KAAK,IAAe,GAAG,KAAK,OAAO,sBAAsB,eAAe,EAAE;EACxF;;;;EAKA,eAAe,aAAmB;AAChC,WAAO,KAAK,KAAK,IAAe,GAAG,KAAK,OAAO,iBAAiB,WAAW,EAAE;EAC/E;;;;EAKA,iBAAiB,eAAqB;AACpC,WAAO,KAAK,KAAK,IAAe,GAAG,KAAK,OAAO,mBAAmB,aAAa,EAAE;EACnF;;;;EAKA,YAAY,UAAgB;AAC1B,WAAO,KAAK,KAAK,IAAe,GAAG,KAAK,OAAO,cAAc,QAAQ,EAAE;EACzE;;;;EAKA,OAAO,OAAe,SAAa;AACjC,QAAI,SAAS,IAAI,WAAU,EAAG,IAAI,KAAK,KAAK;AAE5C,QAAI,SAAS;AACX,aAAO,KAAK,OAAO,EAAE,QAAQ,SAAM;AACjC,YAAI,QAAQ,GAAG,MAAM,QAAQ,QAAQ,GAAG,MAAM,QAAW;AACvD,mBAAS,OAAO,IAAI,KAAK,QAAQ,GAAG,EAAE,SAAQ,CAAE;QAClD;MACF,CAAC;IACH;AAEA,WAAO,KAAK,KAAK,IAAe,GAAG,KAAK,OAAO,WAAW,EAAE,OAAM,CAAE;EACtE;;;;EAKA,cAAW;AACT,WAAO,KAAK,KAAK,IAA6B,GAAG,KAAK,OAAO,WAAW;EAC1E;;;;EAKA,YAAY,IAAY,WAAsB;AAC5C,YAAQ,IAAI,oDAA0C,IAAI,SAAS;AAEnE,WAAO,KAAK,KAAK,MAAY,GAAG,KAAK,OAAO,IAAI,EAAE,UAAU,SAAS,EAClE,KACC,IAAI,MAAM,QAAQ,IAAI,4BAAoB,CAAC,CAAC;EAElD;;;;EAKA,WAAW,IAAY,UAAoB;AACzC,YAAQ,IAAI,mDAAyC,IAAI,QAAQ;AAEjE,WAAO,KAAK,KAAK,MAAY,GAAG,KAAK,OAAO,IAAI,EAAE,SAAS,QAAQ,EAChE,KACC,IAAI,MAAM,QAAQ,IAAI,2BAAmB,CAAC,CAAC;EAEjD;;;;EAKA,uBAAuB,WAAiB;AACtC,WAAO,KAAK,KAAK,IAAa,GAAG,KAAK,OAAO,0BAA0B,SAAS,EAAE;EACpF;;;;EAKA,0BAA0B,WAAiB;AACzC,WAAO,KAAK,KAAK,IAAa,GAAG,KAAK,OAAO,6BAA6B,SAAS,EAAE;EACvF;;;;EAKA,WAAW,SAAe;AACxB,WAAO,KAAK,KAAK,IAAe,GAAG,KAAK,OAAO,aAAa,OAAO,EAAE;EACvE;;;;;EAMA,UAAU,WAAiB;AACzB,WAAO,KAAK,KAAK,IAAoB,GAAG,KAAK,OAAO,IAAI,SAAS,SAAS;EAC5E;;;;EAKA,SAAS,WAAmB,WAAmB;AAC7C,WAAO,KAAK,KAAK,KAAmB,GAAG,KAAK,OAAO,IAAI,SAAS,WAAW,SAAS;EACtF;;;;EAKA,YAAY,WAAmB,SAAe;AAC5C,WAAO,KAAK,KAAK,OAAe,GAAG,KAAK,OAAO,IAAI,SAAS,WAAW,OAAO,EAAE;EAClF;;;;EAKA,YAAY,WAAmB,SAAiB,WAAc;AAC5D,WAAO,KAAK,KAAK,IAAU,GAAG,KAAK,OAAO,IAAI,SAAS,WAAW,OAAO,IAAI,SAAS;EACxF;;;;EAKA,aAAa,WAAmB,SAAe;AAC7C,WAAO,KAAK,KAAK,MAAY,GAAG,KAAK,OAAO,IAAI,SAAS,WAAW,OAAO,SAAS,CAAA,CAAE;EACxF;;qCAhQW,iBAAc,mBAAA,UAAA,CAAA;EAAA;4EAAd,iBAAc,SAAd,gBAAc,WAAA,YAFb,OAAM,CAAA;;;sEAEP,gBAAc,CAAA;UAH1B;WAAW;MACV,YAAY;KACb;;;", "names": []}
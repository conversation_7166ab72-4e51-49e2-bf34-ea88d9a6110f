import {
  CommonModule,
  Component,
  setClassMetadata,
  ɵsetClassDebugInfo,
  ɵɵdefineComponent,
  ɵɵelementEnd,
  ɵɵelementStart,
  ɵɵtext
} from "./chunk-UBZQS7JS.js";

// src/app/components/admin/tools/workflow-engine/workflow-engine.component.ts
var WorkflowEngineComponent = class _WorkflowEngineComponent {
  static \u0275fac = function WorkflowEngineComponent_Factory(__ngFactoryType__) {
    return new (__ngFactoryType__ || _WorkflowEngineComponent)();
  };
  static \u0275cmp = /* @__PURE__ */ \u0275\u0275defineComponent({ type: _WorkflowEngineComponent, selectors: [["app-workflow-engine"]], decls: 5, vars: 0, template: function WorkflowEngineComponent_Template(rf, ctx) {
    if (rf & 1) {
      \u0275\u0275elementStart(0, "div")(1, "h1");
      \u0275\u0275text(2, "\u{1F504} Moteur de workflow");
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(3, "p");
      \u0275\u0275text(4, "En d\xE9veloppement...");
      \u0275\u0275elementEnd()();
    }
  }, dependencies: [CommonModule], encapsulation: 2 });
};
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && setClassMetadata(WorkflowEngineComponent, [{
    type: Component,
    args: [{
      selector: "app-workflow-engine",
      standalone: true,
      imports: [CommonModule],
      template: `<div><h1>\u{1F504} Moteur de workflow</h1><p>En d\xE9veloppement...</p></div>`
    }]
  }], null, null);
})();
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && \u0275setClassDebugInfo(WorkflowEngineComponent, { className: "WorkflowEngineComponent", filePath: "src/app/components/admin/tools/workflow-engine/workflow-engine.component.ts", lineNumber: 10 });
})();
export {
  WorkflowEngineComponent
};
//# sourceMappingURL=chunk-NBJWD4PR.js.map

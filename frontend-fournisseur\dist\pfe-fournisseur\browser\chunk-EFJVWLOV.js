import {
  environment
} from "./chunk-GFHHPDQ6.js";
import {
  HttpClient,
  HttpParams
} from "./chunk-7JDDWGD3.js";
import {
  Injectable,
  catchError,
  map,
  of,
  setClassMetadata,
  tap,
  ɵɵdefineInjectable,
  ɵɵinject
} from "./chunk-UBZQS7JS.js";

// src/app/services/admin.service.ts
var AdminService = class _AdminService {
  http;
  API_URL = `${environment.apiUrl || "http://localhost:5014/api"}/Admin`;
  constructor(http) {
    this.http = http;
  }
  // ========================================
  // GESTION DES UTILISATEURS
  // ========================================
  /**
   * GET /api/Admin/utilisateurs - Obtenir tous les utilisateurs avec pagination
   */
  getUtilisateurs(params) {
    let httpParams = new HttpParams();
    if (params) {
      if (params.page)
        httpParams = httpParams.set("page", params.page.toString());
      if (params.pageSize)
        httpParams = httpParams.set("pageSize", params.pageSize.toString());
      if (params.search)
        httpParams = httpParams.set("search", params.search);
      if (params.role)
        httpParams = httpParams.set("role", params.role);
      if (params.estActif !== void 0)
        httpParams = httpParams.set("estActif", params.estActif.toString());
    }
    console.log("\u{1F465} R\xE9cup\xE9ration des utilisateurs admin avec pagination:", params);
    return this.http.get(`${this.API_URL}/utilisateurs`, { params: httpParams }).pipe(tap((response) => console.log("\u2705 Utilisateurs admin r\xE9cup\xE9r\xE9s avec pagination:", response)), catchError((error) => {
      console.error("\u274C Erreur r\xE9cup\xE9ration utilisateurs:", error);
      return of({ utilisateurs: [], totalCount: 0, page: 1, pageSize: 10, totalPages: 0 });
    }));
  }
  /**
   * PATCH /api/Admin/utilisateurs/{id}/toggle - Activer/Désactiver un utilisateur
   */
  toggleUtilisateur(id, motif) {
    console.log("\u{1F504} Toggle utilisateur ID:", id);
    const body = motif ? { motif } : {};
    return this.http.patch(`${this.API_URL}/utilisateurs/${id}/toggle`, body).pipe(tap((response) => console.log("\u2705 Utilisateur toggl\xE9:", response)), catchError((error) => {
      console.error("\u274C Erreur toggle utilisateur:", error);
      throw error;
    }));
  }
  /**
   * DELETE /api/Admin/utilisateurs/{id} - Supprimer un utilisateur
   */
  deleteUtilisateur(id) {
    console.log("\u{1F5D1}\uFE0F Suppression utilisateur ID:", id);
    return this.http.delete(`${this.API_URL}/utilisateurs/${id}`).pipe(tap((response) => console.log("\u2705 Utilisateur supprim\xE9:", response)), catchError((error) => {
      console.error("\u274C Erreur suppression utilisateur:", error);
      if (error.status === 404) {
        console.warn("\u26A0\uFE0F Utilisateur non trouv\xE9 (404)");
      }
      throw error;
    }));
  }
  // ========================================
  // GESTION DES CATÉGORIES
  // ========================================
  /**
   * POST /api/Admin/categories - Créer une catégorie
   */
  createCategorie(categorie) {
    console.log("\u2795 Cr\xE9ation cat\xE9gorie admin:", categorie);
    return this.http.post(`${this.API_URL}/categories`, categorie).pipe(tap((response) => console.log("\u2705 Cat\xE9gorie cr\xE9\xE9e:", response)));
  }
  /**
   * PUT /api/Admin/categories/{id} - Mettre à jour une catégorie
   */
  updateCategorie(id, categorie) {
    console.log("\u270F\uFE0F Mise \xE0 jour cat\xE9gorie ID:", id);
    return this.http.put(`${this.API_URL}/categories/${id}`, categorie).pipe(tap((response) => console.log("\u2705 Cat\xE9gorie mise \xE0 jour:", response)));
  }
  /**
   * DELETE /api/Admin/categories/{id} - Supprimer une catégorie
   */
  deleteCategorie(id) {
    console.log("\u{1F5D1}\uFE0F Suppression cat\xE9gorie ID:", id);
    return this.http.delete(`${this.API_URL}/categories/${id}`).pipe(tap((response) => console.log("\u2705 Cat\xE9gorie supprim\xE9e:", response)));
  }
  /**
   * PATCH /api/Admin/categories/{id}/valider - Valider une catégorie
   */
  validerCategorie(id, commentaire) {
    console.log("\u2705 Validation cat\xE9gorie ID:", id);
    const body = commentaire ? { commentaire } : {};
    return this.http.patch(`${this.API_URL}/categories/${id}/valider`, body).pipe(tap((response) => console.log("\u2705 Cat\xE9gorie valid\xE9e:", response)));
  }
  /**
   * PATCH /api/Admin/categories/{id}/refuser - Refuser une catégorie
   */
  refuserCategorie(id, motif) {
    console.log("\u274C Refus cat\xE9gorie ID:", id);
    return this.http.patch(`${this.API_URL}/categories/${id}/refuser`, { motif }).pipe(tap((response) => console.log("\u2705 Cat\xE9gorie refus\xE9e:", response)));
  }
  // ========================================
  // GESTION DES SOUS-CATÉGORIES
  // ========================================
  /**
   * POST /api/Admin/souscategories - Créer une sous-catégorie
   */
  createSousCategorie(sousCategorie) {
    console.log("\u2795 Cr\xE9ation sous-cat\xE9gorie admin:", sousCategorie);
    return this.http.post(`${this.API_URL}/souscategories`, sousCategorie).pipe(tap((response) => console.log("\u2705 Sous-cat\xE9gorie cr\xE9\xE9e:", response)));
  }
  /**
   * PUT /api/Admin/souscategories/{id} - Mettre à jour une sous-catégorie
   */
  updateSousCategorie(id, sousCategorie) {
    console.log("\u270F\uFE0F Mise \xE0 jour sous-cat\xE9gorie ID:", id);
    return this.http.put(`${this.API_URL}/souscategories/${id}`, sousCategorie).pipe(tap((response) => console.log("\u2705 Sous-cat\xE9gorie mise \xE0 jour:", response)));
  }
  /**
   * DELETE /api/Admin/souscategories/{id} - Supprimer une sous-catégorie
   */
  deleteSousCategorie(id) {
    console.log("\u{1F5D1}\uFE0F Suppression sous-cat\xE9gorie ID:", id);
    return this.http.delete(`${this.API_URL}/souscategories/${id}`).pipe(tap((response) => console.log("\u2705 Sous-cat\xE9gorie supprim\xE9e:", response)));
  }
  /**
   * PATCH /api/Admin/souscategories/{id}/valider - Valider une sous-catégorie
   */
  validerSousCategorie(id, commentaire) {
    console.log("\u2705 Validation sous-cat\xE9gorie ID:", id);
    const body = commentaire ? { commentaire } : {};
    return this.http.patch(`${this.API_URL}/souscategories/${id}/valider`, body).pipe(tap((response) => console.log("\u2705 Sous-cat\xE9gorie valid\xE9e:", response)));
  }
  /**
   * PATCH /api/Admin/souscategories/{id}/refuser - Refuser une sous-catégorie
   */
  refuserSousCategorie(id, motif) {
    console.log("\u274C Refus sous-cat\xE9gorie ID:", id);
    return this.http.patch(`${this.API_URL}/souscategories/${id}/refuser`, { motif }).pipe(tap((response) => console.log("\u2705 Sous-cat\xE9gorie refus\xE9e:", response)));
  }
  // ========================================
  // GESTION DES COMMANDES
  // ========================================
  /**
   * GET /api/Admin/commandes - Obtenir toutes les commandes
   */
  getCommandes(params) {
    let httpParams = new HttpParams();
    if (params) {
      if (params.page)
        httpParams = httpParams.set("page", params.page.toString());
      if (params.pageSize)
        httpParams = httpParams.set("pageSize", params.pageSize.toString());
      if (params.statut)
        httpParams = httpParams.set("statut", params.statut);
      if (params.dateDebut)
        httpParams = httpParams.set("dateDebut", params.dateDebut.toISOString());
      if (params.dateFin)
        httpParams = httpParams.set("dateFin", params.dateFin.toISOString());
    }
    console.log("\u{1F4CB} R\xE9cup\xE9ration des commandes admin:", params);
    return this.http.get(`${this.API_URL}/commandes`, { params: httpParams }).pipe(tap((response) => console.log("\u2705 Commandes admin r\xE9cup\xE9r\xE9es:", response)));
  }
  /**
   * PATCH /api/Admin/commandes/{id}/annuler - Annuler une commande
   */
  annulerCommande(id, motif) {
    console.log("\u274C Annulation commande ID:", id);
    return this.http.patch(`${this.API_URL}/commandes/${id}/annuler`, { motif }).pipe(tap((response) => console.log("\u2705 Commande annul\xE9e:", response)));
  }
  // ========================================
  // GESTION DES PRODUITS
  // ========================================
  /**
   * GET /api/Admin/produits - Obtenir tous les produits avec pagination
   */
  getProduits(params) {
    let httpParams = new HttpParams();
    if (params) {
      if (params.page)
        httpParams = httpParams.set("page", params.page.toString());
      if (params.pageSize)
        httpParams = httpParams.set("pageSize", params.pageSize.toString());
      if (params.search)
        httpParams = httpParams.set("search", params.search);
      if (params.statut)
        httpParams = httpParams.set("statut", params.statut);
      if (params.stockCritique !== void 0)
        httpParams = httpParams.set("stockCritique", params.stockCritique.toString());
      if (params.fournisseur)
        httpParams = httpParams.set("fournisseur", params.fournisseur);
    }
    console.log("\u{1F4E6} R\xE9cup\xE9ration des produits admin avec pagination:", params);
    return this.http.get(`${this.API_URL}/produits`, { params: httpParams }).pipe(tap((response) => console.log("\u2705 Produits admin r\xE9cup\xE9r\xE9s avec pagination:", response)), catchError((error) => {
      console.error("\u274C Erreur r\xE9cup\xE9ration produits:", error);
      return of({ produits: [], totalCount: 0, page: 1, pageSize: 10, totalPages: 0 });
    }));
  }
  /**
   * PATCH /api/Admin/produits/{id}/valider - Valider un produit
   */
  validerProduit(id) {
    console.log("\u2705 Validation produit admin:", id);
    return this.http.patch(`${this.API_URL}/produits/${id}/valider`, {}).pipe(
      map((response) => true),
      // Le backend retourne 200 OK en cas de succès
      tap((success) => console.log("\u2705 Produit valid\xE9:", success)),
      catchError((error) => {
        console.error("\u274C Erreur validation produit:", error);
        return of(false);
      })
    );
  }
  /**
   * PATCH /api/Admin/produits/{id}/refuser - Refuser un produit
   */
  refuserProduit(id, raison) {
    console.log("\u274C Refus produit admin:", id, raison);
    return this.http.patch(`${this.API_URL}/produits/${id}/refuser`, raison || "").pipe(map((response) => true), tap((success) => console.log("\u2705 Produit refus\xE9:", success)), catchError((error) => {
      console.error("\u274C Erreur refus produit:", error);
      return of(false);
    }));
  }
  /**
   * PATCH /api/Admin/produits/{produitId}/enavant - Mettre un produit en avant
   */
  mettreEnAvantProduit(produitId, enAvant = true) {
    console.log("\u2B50 Mise en avant produit ID:", produitId);
    return this.http.patch(`${this.API_URL}/produits/${produitId}/enavant`, { enAvant }).pipe(tap((response) => console.log("\u2705 Produit mis en avant:", response)));
  }
  /**
   * PATCH /api/Admin/produits/{id}/retirer-avant - Retirer la mise en avant d'un produit
   */
  retirerMiseEnAvantProduit(id) {
    console.log("\u2B50 Retrait mise en avant produit admin:", id);
    return this.http.patch(`${this.API_URL}/produits/${id}/retirer-avant`, {}).pipe(map((response) => true), tap((success) => console.log("\u2705 Mise en avant retir\xE9e:", success)), catchError((error) => {
      console.error("\u274C Erreur retrait mise en avant:", error);
      return of(false);
    }));
  }
  /**
   * PUT /api/Admin/produits/{id}/moderer - Modérer le contenu d'un produit
   */
  modererContenuProduit(id, moderation) {
    console.log("\u{1F50D} Mod\xE9ration contenu produit admin:", id, moderation);
    return this.http.put(`${this.API_URL}/produits/${id}/moderer`, moderation).pipe(map((response) => true), tap((success) => console.log("\u2705 Contenu produit mod\xE9r\xE9:", success)), catchError((error) => {
      console.error("\u274C Erreur mod\xE9ration contenu:", error);
      return of(false);
    }));
  }
  /**
   * DELETE /api/Admin/produits/{id} - Supprimer un produit
   */
  supprimerProduit(id) {
    console.log("\u{1F5D1}\uFE0F Suppression produit admin:", id);
    return this.http.delete(`${this.API_URL}/produits/${id}`).pipe(map((response) => true), tap((success) => console.log("\u2705 Produit supprim\xE9:", success)), catchError((error) => {
      console.error("\u274C Erreur suppression produit:", error);
      return of(false);
    }));
  }
  /**
   * GET /api/Admin/produits/stock-critique - Obtenir les produits en stock critique
   */
  getProduitsStockCritique(seuil = 10) {
    console.log("\u26A0\uFE0F R\xE9cup\xE9ration produits stock critique, seuil:", seuil);
    return this.http.get(`${this.API_URL}/produits/stock-critique?seuil=${seuil}`).pipe(tap((response) => console.log("\u2705 Produits stock critique r\xE9cup\xE9r\xE9s:", response)), catchError((error) => {
      console.error("\u274C Erreur r\xE9cup\xE9ration stock critique:", error);
      return of([]);
    }));
  }
  /**
   * PATCH /api/Admin/produits/{id}/stock - Mettre à jour le stock d'un produit
   */
  updateStockProduit(id, nouveauStock) {
    console.log("\u{1F4E6} Mise \xE0 jour stock produit admin:", id, nouveauStock);
    return this.http.patch(`${this.API_URL}/produits/${id}/stock`, nouveauStock).pipe(map((response) => true), tap((success) => console.log("\u2705 Stock produit mis \xE0 jour:", success)), catchError((error) => {
      console.error("\u274C Erreur mise \xE0 jour stock:", error);
      return of(false);
    }));
  }
  /**
   * GET /api/Admin/produits/en-avant - Obtenir les produits mis en avant
   */
  getProduitsEnAvant() {
    console.log("\u2B50 R\xE9cup\xE9ration produits en avant");
    return this.http.get(`${this.API_URL}/produits/en-avant`).pipe(tap((response) => console.log("\u2705 Produits en avant r\xE9cup\xE9r\xE9s:", response)), catchError((error) => {
      console.error("\u274C Erreur r\xE9cup\xE9ration produits en avant:", error);
      return of([]);
    }));
  }
  /**
   * GET /api/Admin/statistiques - Obtenir les statistiques admin
   */
  getStatistiques() {
    console.log("\u{1F4CA} R\xE9cup\xE9ration des statistiques admin");
    return this.http.get(`${this.API_URL}/statistiques`).pipe(tap((response) => console.log("\u2705 Statistiques admin r\xE9cup\xE9r\xE9es:", response)), catchError((error) => {
      console.error("\u274C Erreur r\xE9cup\xE9ration statistiques:", error);
      return of({
        nombreUtilisateurs: 0,
        nombreVentes: 0,
        nombreProduits: 0,
        nombreCommandesAnnulees: 0,
        nombreCommandes: 0,
        nombreFournisseurs: 0,
        nombreClients: 0
      });
    }));
  }
  static \u0275fac = function AdminService_Factory(__ngFactoryType__) {
    return new (__ngFactoryType__ || _AdminService)(\u0275\u0275inject(HttpClient));
  };
  static \u0275prov = /* @__PURE__ */ \u0275\u0275defineInjectable({ token: _AdminService, factory: _AdminService.\u0275fac, providedIn: "root" });
};
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && setClassMetadata(AdminService, [{
    type: Injectable,
    args: [{
      providedIn: "root"
    }]
  }], () => [{ type: HttpClient }], null);
})();

export {
  AdminService
};
//# sourceMappingURL=chunk-EFJVWLOV.js.map

import {
  CommonModule,
  Component,
  setClassMetadata,
  ɵsetClassDebugInfo,
  ɵɵdefineComponent,
  ɵɵelementEnd,
  ɵɵelementStart,
  ɵɵtext
} from "./chunk-UBZQS7JS.js";

// src/app/components/admin/tools/batch-operations/batch-operations.component.ts
var BatchOperationsComponent = class _BatchOperationsComponent {
  static \u0275fac = function BatchOperationsComponent_Factory(__ngFactoryType__) {
    return new (__ngFactoryType__ || _BatchOperationsComponent)();
  };
  static \u0275cmp = /* @__PURE__ */ \u0275\u0275defineComponent({ type: _BatchOperationsComponent, selectors: [["app-batch-operations"]], decls: 5, vars: 0, template: function BatchOperationsComponent_Template(rf, ctx) {
    if (rf & 1) {
      \u0275\u0275elementStart(0, "div")(1, "h1");
      \u0275\u0275text(2, "\u2699\uFE0F Op\xE9rations par lots");
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(3, "p");
      \u0275\u0275text(4, "En d\xE9veloppement...");
      \u0275\u0275elementEnd()();
    }
  }, dependencies: [CommonModule], encapsulation: 2 });
};
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && setClassMetadata(BatchOperationsComponent, [{
    type: Component,
    args: [{
      selector: "app-batch-operations",
      standalone: true,
      imports: [CommonModule],
      template: `<div><h1>\u2699\uFE0F Op\xE9rations par lots</h1><p>En d\xE9veloppement...</p></div>`
    }]
  }], null, null);
})();
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && \u0275setClassDebugInfo(BatchOperationsComponent, { className: "BatchOperationsComponent", filePath: "src/app/components/admin/tools/batch-operations/batch-operations.component.ts", lineNumber: 10 });
})();
export {
  BatchOperationsComponent
};
//# sourceMappingURL=chunk-U3TE3X4Q.js.map

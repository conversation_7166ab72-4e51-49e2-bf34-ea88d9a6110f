import {
  environment
} from "./chunk-GFHHPDQ6.js";
import {
  HttpClient
} from "./chunk-7JDDWGD3.js";
import {
  BehaviorSubject,
  Injectable,
  __spreadProps,
  __spreadValues,
  map,
  setClassMetadata,
  tap,
  ɵɵdefineInjectable,
  ɵɵinject
} from "./chunk-UBZQS7JS.js";

// src/app/services/notification.service.ts
var NotificationService = class _NotificationService {
  http;
  apiUrl = `${environment.apiUrl}/notification`;
  notificationsSubject = new BehaviorSubject([]);
  unreadCountSubject = new BehaviorSubject(0);
  notifications$ = this.notificationsSubject.asObservable();
  unreadCount$ = this.unreadCountSubject.asObservable();
  constructor(http) {
    this.http = http;
  }
  getUserNotifications(userId) {
    return this.http.get(`${this.apiUrl}/user/${userId}`).pipe(map((notifs) => this.sortNotifications(notifs)), tap((notifs) => {
      this.notificationsSubject.next(notifs);
      this.updateUnreadCount(notifs);
    }));
  }
  getUnreadNotifications(userId) {
    return this.http.get(`${this.apiUrl}/user/${userId}/unread`).pipe(tap((notifs) => this.updateUnreadCount(notifs)));
  }
  markAsRead(notificationId) {
    return this.http.patch(`${this.apiUrl}/${notificationId}/read`, {}).pipe(tap(() => {
      const updated = this.notificationsSubject.value.map((n) => n.id === notificationId ? __spreadProps(__spreadValues({}, n), { estLue: true }) : n);
      this.notificationsSubject.next(updated);
      this.updateUnreadCount(updated);
    }));
  }
  createNotification(notification) {
    return this.http.post(this.apiUrl, notification);
  }
  deleteNotification(notificationId) {
    return this.http.delete(`${this.apiUrl}/${notificationId}`).pipe(tap(() => {
      const updated = this.notificationsSubject.value.filter((n) => n.id !== notificationId);
      this.notificationsSubject.next(updated);
      this.updateUnreadCount(updated);
    }));
  }
  sortNotifications(notifs) {
    return [...notifs].sort((a, b) => new Date(b.dateEnvoi).getTime() - new Date(a.dateEnvoi).getTime());
  }
  updateUnreadCount(notifs) {
    const count = notifs.filter((n) => !n.estLue).length;
    this.unreadCountSubject.next(count);
  }
  static \u0275fac = function NotificationService_Factory(__ngFactoryType__) {
    return new (__ngFactoryType__ || _NotificationService)(\u0275\u0275inject(HttpClient));
  };
  static \u0275prov = /* @__PURE__ */ \u0275\u0275defineInjectable({ token: _NotificationService, factory: _NotificationService.\u0275fac, providedIn: "root" });
};
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && setClassMetadata(NotificationService, [{
    type: Injectable,
    args: [{
      providedIn: "root"
    }]
  }], () => [{ type: HttpClient }], null);
})();

export {
  NotificationService
};
//# sourceMappingURL=chunk-2WHFEWR5.js.map

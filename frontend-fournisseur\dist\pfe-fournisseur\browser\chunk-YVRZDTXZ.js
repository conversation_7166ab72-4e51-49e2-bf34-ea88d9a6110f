import {
  Router
} from "./chunk-6BVUYNW4.js";
import "./chunk-7JDDWGD3.js";
import {
  CommonModule,
  Component,
  setClassMetadata,
  ɵsetClassDebugInfo,
  ɵɵdefineComponent,
  ɵɵdirectiveInject,
  ɵɵelementEnd,
  ɵɵelementStart,
  ɵɵlistener,
  ɵɵtext
} from "./chunk-UBZQS7JS.js";

// src/app/components/admin/errors/unauthorized/unauthorized.component.ts
var UnauthorizedComponent = class _UnauthorizedComponent {
  router;
  constructor(router) {
    this.router = router;
  }
  goBack() {
    window.history.back();
  }
  goToDashboard() {
    this.router.navigate(["/admin/dashboard"]);
  }
  static \u0275fac = function UnauthorizedComponent_Factory(__ngFactoryType__) {
    return new (__ngFactoryType__ || _UnauthorizedComponent)(\u0275\u0275directiveInject(Router));
  };
  static \u0275cmp = /* @__PURE__ */ \u0275\u0275defineComponent({ type: _UnauthorizedComponent, selectors: [["app-unauthorized"]], decls: 13, vars: 0, consts: [[1, "unauthorized-page"], [1, "error-container"], [1, "error-icon"], [1, "error-actions"], [1, "btn-secondary", 3, "click"], [1, "btn-primary", 3, "click"]], template: function UnauthorizedComponent_Template(rf, ctx) {
    if (rf & 1) {
      \u0275\u0275elementStart(0, "div", 0)(1, "div", 1)(2, "div", 2);
      \u0275\u0275text(3, "\u{1F6AB}");
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(4, "h1");
      \u0275\u0275text(5, "Acc\xE8s non autoris\xE9");
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(6, "p");
      \u0275\u0275text(7, "Vous n'avez pas les permissions n\xE9cessaires pour acc\xE9der \xE0 cette page.");
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(8, "div", 3)(9, "button", 4);
      \u0275\u0275listener("click", function UnauthorizedComponent_Template_button_click_9_listener() {
        return ctx.goBack();
      });
      \u0275\u0275text(10, "\u2190 Retour");
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(11, "button", 5);
      \u0275\u0275listener("click", function UnauthorizedComponent_Template_button_click_11_listener() {
        return ctx.goToDashboard();
      });
      \u0275\u0275text(12, "\u{1F3E0} Dashboard");
      \u0275\u0275elementEnd()()()();
    }
  }, dependencies: [CommonModule], styles: ["\n\n.unauthorized-page[_ngcontent-%COMP%] {\n  min-height: 100vh;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  background: #f8fafc;\n}\n.error-container[_ngcontent-%COMP%] {\n  text-align: center;\n  max-width: 400px;\n  padding: 2rem;\n}\n.error-icon[_ngcontent-%COMP%] {\n  font-size: 4rem;\n  margin-bottom: 1rem;\n}\nh1[_ngcontent-%COMP%] {\n  font-size: 2rem;\n  color: #1e293b;\n  margin: 0 0 1rem 0;\n}\np[_ngcontent-%COMP%] {\n  color: #64748b;\n  margin: 0 0 2rem 0;\n  line-height: 1.6;\n}\n.error-actions[_ngcontent-%COMP%] {\n  display: flex;\n  gap: 1rem;\n  justify-content: center;\n}\n.btn-primary[_ngcontent-%COMP%], \n.btn-secondary[_ngcontent-%COMP%] {\n  padding: 0.75rem 1.5rem;\n  border-radius: 8px;\n  font-weight: 500;\n  cursor: pointer;\n  transition: all 0.3s ease;\n  border: none;\n}\n.btn-primary[_ngcontent-%COMP%] {\n  background: #3b82f6;\n  color: white;\n}\n.btn-primary[_ngcontent-%COMP%]:hover {\n  background: #2563eb;\n}\n.btn-secondary[_ngcontent-%COMP%] {\n  background: #f1f5f9;\n  color: #64748b;\n  border: 1px solid #e2e8f0;\n}\n.btn-secondary[_ngcontent-%COMP%]:hover {\n  background: #e2e8f0;\n}\n/*# sourceMappingURL=unauthorized.component.css.map */"] });
};
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && setClassMetadata(UnauthorizedComponent, [{
    type: Component,
    args: [{ selector: "app-unauthorized", standalone: true, imports: [CommonModule], template: `
    <div class="unauthorized-page">
      <div class="error-container">
        <div class="error-icon">\u{1F6AB}</div>
        <h1>Acc\xE8s non autoris\xE9</h1>
        <p>Vous n'avez pas les permissions n\xE9cessaires pour acc\xE9der \xE0 cette page.</p>
        <div class="error-actions">
          <button (click)="goBack()" class="btn-secondary">\u2190 Retour</button>
          <button (click)="goToDashboard()" class="btn-primary">\u{1F3E0} Dashboard</button>
        </div>
      </div>
    </div>
  `, styles: ["/* angular:styles/component:css;2efef486554b1763b7884e96fba1a51e27371ae58aca6e87a82d25c41204d4bf;C:/Users/<USER>/Desktop/STE/PFE/PFE/frontend-fournisseur/src/app/components/admin/errors/unauthorized/unauthorized.component.ts */\n.unauthorized-page {\n  min-height: 100vh;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  background: #f8fafc;\n}\n.error-container {\n  text-align: center;\n  max-width: 400px;\n  padding: 2rem;\n}\n.error-icon {\n  font-size: 4rem;\n  margin-bottom: 1rem;\n}\nh1 {\n  font-size: 2rem;\n  color: #1e293b;\n  margin: 0 0 1rem 0;\n}\np {\n  color: #64748b;\n  margin: 0 0 2rem 0;\n  line-height: 1.6;\n}\n.error-actions {\n  display: flex;\n  gap: 1rem;\n  justify-content: center;\n}\n.btn-primary,\n.btn-secondary {\n  padding: 0.75rem 1.5rem;\n  border-radius: 8px;\n  font-weight: 500;\n  cursor: pointer;\n  transition: all 0.3s ease;\n  border: none;\n}\n.btn-primary {\n  background: #3b82f6;\n  color: white;\n}\n.btn-primary:hover {\n  background: #2563eb;\n}\n.btn-secondary {\n  background: #f1f5f9;\n  color: #64748b;\n  border: 1px solid #e2e8f0;\n}\n.btn-secondary:hover {\n  background: #e2e8f0;\n}\n/*# sourceMappingURL=unauthorized.component.css.map */\n"] }]
  }], () => [{ type: Router }], null);
})();
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && \u0275setClassDebugInfo(UnauthorizedComponent, { className: "UnauthorizedComponent", filePath: "src/app/components/admin/errors/unauthorized/unauthorized.component.ts", lineNumber: 90 });
})();
export {
  UnauthorizedComponent
};
//# sourceMappingURL=chunk-YVRZDTXZ.js.map

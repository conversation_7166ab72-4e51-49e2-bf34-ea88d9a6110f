{"version": 3, "sources": ["src/app/components/admin/errors/unauthorized/unauthorized.component.ts"], "sourcesContent": ["import { Component } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { Router } from '@angular/router';\n\n@Component({\n  selector: 'app-unauthorized',\n  standalone: true,\n  imports: [CommonModule],\n  template: `\n    <div class=\"unauthorized-page\">\n      <div class=\"error-container\">\n        <div class=\"error-icon\">🚫</div>\n        <h1>Accès non autorisé</h1>\n        <p>Vous n'avez pas les permissions nécessaires pour accéder à cette page.</p>\n        <div class=\"error-actions\">\n          <button (click)=\"goBack()\" class=\"btn-secondary\">← Retour</button>\n          <button (click)=\"goToDashboard()\" class=\"btn-primary\">🏠 Dashboard</button>\n        </div>\n      </div>\n    </div>\n  `,\n  styles: [`\n    .unauthorized-page {\n      min-height: 100vh;\n      display: flex;\n      align-items: center;\n      justify-content: center;\n      background: #f8fafc;\n    }\n\n    .error-container {\n      text-align: center;\n      max-width: 400px;\n      padding: 2rem;\n    }\n\n    .error-icon {\n      font-size: 4rem;\n      margin-bottom: 1rem;\n    }\n\n    h1 {\n      font-size: 2rem;\n      color: #1e293b;\n      margin: 0 0 1rem 0;\n    }\n\n    p {\n      color: #64748b;\n      margin: 0 0 2rem 0;\n      line-height: 1.6;\n    }\n\n    .error-actions {\n      display: flex;\n      gap: 1rem;\n      justify-content: center;\n    }\n\n    .btn-primary,\n    .btn-secondary {\n      padding: 0.75rem 1.5rem;\n      border-radius: 8px;\n      font-weight: 500;\n      cursor: pointer;\n      transition: all 0.3s ease;\n      border: none;\n    }\n\n    .btn-primary {\n      background: #3b82f6;\n      color: white;\n    }\n\n    .btn-primary:hover {\n      background: #2563eb;\n    }\n\n    .btn-secondary {\n      background: #f1f5f9;\n      color: #64748b;\n      border: 1px solid #e2e8f0;\n    }\n\n    .btn-secondary:hover {\n      background: #e2e8f0;\n    }\n  `]\n})\nexport class UnauthorizedComponent {\n  constructor(private router: Router) {}\n\n  goBack(): void {\n    window.history.back();\n  }\n\n  goToDashboard(): void {\n    this.router.navigate(['/admin/dashboard']);\n  }\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;AAyFM,IAAO,wBAAP,MAAO,uBAAqB;EACZ;EAApB,YAAoB,QAAc;AAAd,SAAA,SAAA;EAAiB;EAErC,SAAM;AACJ,WAAO,QAAQ,KAAI;EACrB;EAEA,gBAAa;AACX,SAAK,OAAO,SAAS,CAAC,kBAAkB,CAAC;EAC3C;;qCATW,wBAAqB,4BAAA,MAAA,CAAA;EAAA;yEAArB,wBAAqB,WAAA,CAAA,CAAA,kBAAA,CAAA,GAAA,OAAA,IAAA,MAAA,GAAA,QAAA,CAAA,CAAA,GAAA,mBAAA,GAAA,CAAA,GAAA,iBAAA,GAAA,CAAA,GAAA,YAAA,GAAA,CAAA,GAAA,eAAA,GAAA,CAAA,GAAA,iBAAA,GAAA,OAAA,GAAA,CAAA,GAAA,eAAA,GAAA,OAAA,CAAA,GAAA,UAAA,SAAA,+BAAA,IAAA,KAAA;AAAA,QAAA,KAAA,GAAA;AAhF9B,MAAA,yBAAA,GAAA,OAAA,CAAA,EAA+B,GAAA,OAAA,CAAA,EACA,GAAA,OAAA,CAAA;AACH,MAAA,iBAAA,GAAA,WAAA;AAAE,MAAA,uBAAA;AAC1B,MAAA,yBAAA,GAAA,IAAA;AAAI,MAAA,iBAAA,GAAA,0BAAA;AAAkB,MAAA,uBAAA;AACtB,MAAA,yBAAA,GAAA,GAAA;AAAG,MAAA,iBAAA,GAAA,iFAAA;AAAsE,MAAA,uBAAA;AACzE,MAAA,yBAAA,GAAA,OAAA,CAAA,EAA2B,GAAA,UAAA,CAAA;AACjB,MAAA,qBAAA,SAAA,SAAA,yDAAA;AAAA,eAAS,IAAA,OAAA;MAAQ,CAAA;AAAwB,MAAA,iBAAA,IAAA,eAAA;AAAQ,MAAA,uBAAA;AACzD,MAAA,yBAAA,IAAA,UAAA,CAAA;AAAQ,MAAA,qBAAA,SAAA,SAAA,0DAAA;AAAA,eAAS,IAAA,cAAA;MAAe,CAAA;AAAsB,MAAA,iBAAA,IAAA,qBAAA;AAAY,MAAA,uBAAA,EAAS,EACvE,EACF;;oBAXA,YAAY,GAAA,QAAA,CAAA,6sCAAA,EAAA,CAAA;;;sEAkFX,uBAAqB,CAAA;UArFjC;uBACW,oBAAkB,YAChB,MAAI,SACP,CAAC,YAAY,GAAC,UACb;;;;;;;;;;;;KAYT,QAAA,CAAA,ysCAAA,EAAA,CAAA;;;;6EAqEU,uBAAqB,EAAA,WAAA,yBAAA,UAAA,0EAAA,YAAA,GAAA,CAAA;AAAA,GAAA;", "names": []}
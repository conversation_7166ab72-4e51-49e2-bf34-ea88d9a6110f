import {
  CommonModule,
  Component,
  setClassMetadata,
  ɵsetClassDebugInfo,
  ɵɵdefineComponent,
  ɵɵelementEnd,
  ɵɵelementStart,
  ɵɵtext
} from "./chunk-UBZQS7JS.js";

// src/app/components/admin/products/product-validation/product-validation.component.ts
var ProductValidationComponent = class _ProductValidationComponent {
  static \u0275fac = function ProductValidationComponent_Factory(__ngFactoryType__) {
    return new (__ngFactoryType__ || _ProductValidationComponent)();
  };
  static \u0275cmp = /* @__PURE__ */ \u0275\u0275defineComponent({ type: _ProductValidationComponent, selectors: [["app-product-validation"]], decls: 5, vars: 0, template: function ProductValidationComponent_Template(rf, ctx) {
    if (rf & 1) {
      \u0275\u0275elementStart(0, "div")(1, "h1");
      \u0275\u0275text(2, "\u{1F50D} Validation des produits");
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(3, "p");
      \u0275\u0275text(4, "En d\xE9veloppement...");
      \u0275\u0275elementEnd()();
    }
  }, dependencies: [CommonModule], encapsulation: 2 });
};
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && setClassMetadata(ProductValidationComponent, [{
    type: Component,
    args: [{
      selector: "app-product-validation",
      standalone: true,
      imports: [CommonModule],
      template: `<div><h1>\u{1F50D} Validation des produits</h1><p>En d\xE9veloppement...</p></div>`
    }]
  }], null, null);
})();
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && \u0275setClassDebugInfo(ProductValidationComponent, { className: "ProductValidationComponent", filePath: "src/app/components/admin/products/product-validation/product-validation.component.ts", lineNumber: 10 });
})();
export {
  ProductValidationComponent
};
//# sourceMappingURL=chunk-5ZQT6ZXA.js.map

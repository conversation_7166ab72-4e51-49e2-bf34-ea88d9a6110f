import { Injectable } from '@angular/core';
import { environment } from '../../environments/environment';

@Injectable({
  providedIn: 'root'
})
export class ImageUrlService {
  // Pour les images, on utilise le serveur backend sans /api
  private readonly baseUrl = environment.apiUrl?.replace('/api', '') || 'http://localhost:5014';
  private readonly placeholderImage = 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMzAwIiBoZWlnaHQ9IjMwMCIgdmlld0JveD0iMCAwIDMwMCAzMDAiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxyZWN0IHdpZHRoPSIzMDAiIGhlaWdodD0iMzAwIiBmaWxsPSIjRjNGNEY2Ii8+CjxwYXRoIGQ9Ik0xNTAgMTAwQzE2MS4wNDYgMTAwIDE3MCA5MC45NTQzIDE3MCA4MEM1NyA2OS4wNDU3IDE0OC45NTQgNjAgMTQwIDYwQzEyOS4wNDYgNjAgMTIwIDY5LjA0NTcgMTIwIDgwQzEyMCA5MC45NTQzIDEyOS4wNDYgMTAwIDE0MCAxMDBIMTUwWiIgZmlsbD0iIzlDQTNBRiIvPgo8cGF0aCBkPSJNMjQwIDIyMEgyNDBWMjQwSDI0MEgyNDBWMjIwWk0yNDAgMjIwSDYwVjI0MEgyNDBWMjIwWiIgZmlsbD0iIzlDQTNBRiIvPgo8cGF0aCBkPSJNMjEwIDEzMEMxOTguOTU0IDEzMCAxOTAgMTM5LjA0NiAxOTAgMTUwQzE5MCAyMDkuMDU0IDE5OC45NTQgMjIwIDIxMCAyMjBIMjQwVjE1MEMyNDAgMTM5LjA0NiAyMzEuMDQ2IDEzMCAyMjAgMTMwSDIxMFoiIGZpbGw9IiM5Q0EzQUYiLz4KPHR4dCB4PSIxNTAiIHk9IjI3MCIgdGV4dC1hbmNob3I9Im1pZGRsZSIgZmlsbD0iIzlDQTNBRiIgZm9udC1mYW1pbHk9IkFyaWFsLCBzYW5zLXNlcmlmIiBmb250LXNpemU9IjE0Ij5JbWFnZSBub24gZGlzcG9uaWJsZTwvdGV4dD4KPHN2Zz4=';

  constructor() {
    console.log('🖼️ ImageUrlService initialisé avec baseUrl:', this.baseUrl);
    console.log('🖼️ Environment apiUrl:', environment.apiUrl);
  }

  /**
   * Obtient l'URL complète pour n'importe quelle image
   * @param imagePath - Chemin de l'image (peut être null/undefined)
   * @returns URL complète de l'image ou placeholder
   */
  getFullImageUrl(imagePath: string | null | undefined): string {
    console.log('🖼️ getFullImageUrl appelé avec:', imagePath);

    if (!imagePath || imagePath.trim() === '') {
      console.log('🖼️ Chemin vide, retour du placeholder');
      return this.getPlaceholderUrl();
    }

    // Si l'URL est déjà complète (commence par http), la retourner telle quelle
    if (imagePath.startsWith('http://') || imagePath.startsWith('https://')) {
      console.log('🖼️ URL déjà complète:', imagePath);
      return imagePath;
    }

    // Si le chemin commence par 'assets/', c'est un fichier local - le retourner tel quel
    if (imagePath.startsWith('assets/')) {
      console.log('🖼️ Chemin assets local:', imagePath);
      return imagePath;
    }

    // Si le chemin commence par '/', le supprimer pour éviter les doubles slashes
    const cleanPath = imagePath.startsWith('/') ? imagePath.substring(1) : imagePath;

    // Construire l'URL complète pour les fichiers du backend
    const fullUrl = `${this.baseUrl}/${cleanPath}`;

    console.log('🖼️ URL construite:', {
      baseUrl: this.baseUrl,
      imagePath,
      cleanPath,
      fullUrl
    });

    return fullUrl;
  }

  /**
   * Obtient l'URL complète pour une image de produit
   * @param imagePath - Chemin de l'image du produit
   * @returns URL complète de l'image du produit
   */
  getProduitImageUrl(imagePath: string | null | undefined): string {
    return this.getFullImageUrl(imagePath);
  }

  /**
   * Obtient l'URL complète pour un logo de fournisseur
   * @param logoPath - Chemin du logo du fournisseur
   * @returns URL complète du logo du fournisseur
   */
  getFournisseurLogoUrl(logoPath: string | null | undefined): string {
    return this.getFullImageUrl(logoPath);
  }

  /**
   * Obtient l'URL complète pour un logo de marque
   * @param logoPath - Chemin du logo de la marque
   * @returns URL complète du logo de la marque
   */
  getMarqueLogoUrl(logoPath: string | null | undefined): string {
    console.log('🏷️ getMarqueLogoUrl appelé avec:', logoPath);
    const url = this.getFullImageUrl(logoPath);
    console.log('🏷️ URL logo marque finale:', url);
    return url;
  }

  /**
   * Obtient l'URL du placeholder par défaut
   * @returns URL du placeholder
   */
  getPlaceholderUrl(): string {
    return this.placeholderImage;
  }

  /**
   * Vérifie si une URL d'image est valide
   * @param imageUrl - URL à vérifier
   * @returns Promise<boolean> - true si l'image est accessible
   */
  async isImageValid(imageUrl: string): Promise<boolean> {
    try {
      const response = await fetch(imageUrl, { method: 'HEAD' });
      return response.ok;
    } catch {
      return false;
    }
  }

  /**
   * Obtient l'URL d'image avec fallback vers placeholder
   * @param imagePath - Chemin de l'image
   * @returns URL de l'image ou placeholder si invalide
   */
  async getImageUrlWithFallback(imagePath: string | null | undefined): Promise<string> {
    const imageUrl = this.getFullImageUrl(imagePath);
    
    if (imageUrl === this.getPlaceholderUrl()) {
      return imageUrl;
    }

    const isValid = await this.isImageValid(imageUrl);
    return isValid ? imageUrl : this.getPlaceholderUrl();
  }
}

{"version": 3, "sources": ["src/app/services/image-url.service.ts"], "sourcesContent": ["import { Injectable } from '@angular/core';\nimport { environment } from '../../environments/environment';\n\n@Injectable({\n  providedIn: 'root'\n})\nexport class ImageUrlService {\n  // Pour les images, on utilise le serveur backend sans /api\n  private readonly baseUrl = environment.apiUrl?.replace('/api', '') || 'http://localhost:5014';\n  private readonly placeholderImage = 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMzAwIiBoZWlnaHQ9IjMwMCIgdmlld0JveD0iMCAwIDMwMCAzMDAiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxyZWN0IHdpZHRoPSIzMDAiIGhlaWdodD0iMzAwIiBmaWxsPSIjRjNGNEY2Ii8+CjxwYXRoIGQ9Ik0xNTAgMTAwQzE2MS4wNDYgMTAwIDE3MCA5MC45NTQzIDE3MCA4MEM1NyA2OS4wNDU3IDE0OC45NTQgNjAgMTQwIDYwQzEyOS4wNDYgNjAgMTIwIDY5LjA0NTcgMTIwIDgwQzEyMCA5MC45NTQzIDEyOS4wNDYgMTAwIDE0MCAxMDBIMTUwWiIgZmlsbD0iIzlDQTNBRiIvPgo8cGF0aCBkPSJNMjQwIDIyMEgyNDBWMjQwSDI0MEgyNDBWMjIwWk0yNDAgMjIwSDYwVjI0MEgyNDBWMjIwWiIgZmlsbD0iIzlDQTNBRiIvPgo8cGF0aCBkPSJNMjEwIDEzMEMxOTguOTU0IDEzMCAxOTAgMTM5LjA0NiAxOTAgMTUwQzE5MCAyMDkuMDU0IDE5OC45NTQgMjIwIDIxMCAyMjBIMjQwVjE1MEMyNDAgMTM5LjA0NiAyMzEuMDQ2IDEzMCAyMjAgMTMwSDIxMFoiIGZpbGw9IiM5Q0EzQUYiLz4KPHR4dCB4PSIxNTAiIHk9IjI3MCIgdGV4dC1hbmNob3I9Im1pZGRsZSIgZmlsbD0iIzlDQTNBRiIgZm9udC1mYW1pbHk9IkFyaWFsLCBzYW5zLXNlcmlmIiBmb250LXNpemU9IjE0Ij5JbWFnZSBub24gZGlzcG9uaWJsZTwvdGV4dD4KPHN2Zz4=';\n\n  constructor() {\n    console.log('🖼️ ImageUrlService initialisé avec baseUrl:', this.baseUrl);\n    console.log('🖼️ Environment apiUrl:', environment.apiUrl);\n  }\n\n  /**\n   * Obtient l'URL complète pour n'importe quelle image\n   * @param imagePath - Chemin de l'image (peut être null/undefined)\n   * @returns URL complète de l'image ou placeholder\n   */\n  getFullImageUrl(imagePath: string | null | undefined): string {\n    console.log('🖼️ getFullImageUrl appelé avec:', imagePath);\n\n    if (!imagePath || imagePath.trim() === '') {\n      console.log('🖼️ Chemin vide, retour du placeholder');\n      return this.getPlaceholderUrl();\n    }\n\n    // Si l'URL est déjà complète (commence par http), la retourner telle quelle\n    if (imagePath.startsWith('http://') || imagePath.startsWith('https://')) {\n      console.log('🖼️ URL déjà complète:', imagePath);\n      return imagePath;\n    }\n\n    // Si le chemin commence par 'assets/', c'est un fichier local\n    if (imagePath.startsWith('assets/')) {\n      console.log('🖼️ Chemin assets local:', imagePath);\n\n      // Correction automatique des chemins incorrects\n      let correctedPath = imagePath;\n\n      // Corriger assets/images/formes/ → assets/formes/\n      if (imagePath.includes('assets/images/formes/')) {\n        correctedPath = imagePath.replace('assets/images/formes/', 'assets/formes/');\n        console.log('🔧 Correction chemin formes:', imagePath, '→', correctedPath);\n      }\n\n      // Corriger assets/images/logos/ → assets/logos/\n      if (imagePath.includes('assets/images/logos/')) {\n        correctedPath = imagePath.replace('assets/images/logos/', 'assets/logos/');\n        console.log('🔧 Correction chemin logos:', imagePath, '→', correctedPath);\n      }\n\n      return correctedPath;\n    }\n\n    // Si le chemin commence par '/', le supprimer pour éviter les doubles slashes\n    const cleanPath = imagePath.startsWith('/') ? imagePath.substring(1) : imagePath;\n\n    // Construire l'URL complète pour les fichiers du backend\n    const fullUrl = `${this.baseUrl}/${cleanPath}`;\n\n    console.log('🖼️ URL construite:', {\n      baseUrl: this.baseUrl,\n      imagePath,\n      cleanPath,\n      fullUrl\n    });\n\n    return fullUrl;\n  }\n\n  /**\n   * Obtient l'URL complète pour une image de produit\n   * @param imagePath - Chemin de l'image du produit\n   * @returns URL complète de l'image du produit\n   */\n  getProduitImageUrl(imagePath: string | null | undefined): string {\n    return this.getFullImageUrl(imagePath);\n  }\n\n  /**\n   * Obtient l'URL complète pour un logo de fournisseur\n   * @param logoPath - Chemin du logo du fournisseur\n   * @returns URL complète du logo du fournisseur\n   */\n  getFournisseurLogoUrl(logoPath: string | null | undefined): string {\n    return this.getFullImageUrl(logoPath);\n  }\n\n  /**\n   * Obtient l'URL complète pour un logo de marque\n   * @param logoPath - Chemin du logo de la marque\n   * @returns URL complète du logo de la marque\n   */\n  getMarqueLogoUrl(logoPath: string | null | undefined): string {\n    console.log('🏷️ getMarqueLogoUrl appelé avec:', logoPath);\n    const url = this.getFullImageUrl(logoPath);\n    console.log('🏷️ URL logo marque finale:', url);\n    return url;\n  }\n\n  /**\n   * Obtient l'URL du placeholder par défaut\n   * @returns URL du placeholder\n   */\n  getPlaceholderUrl(): string {\n    return this.placeholderImage;\n  }\n\n  /**\n   * Vérifie si une URL d'image est valide\n   * @param imageUrl - URL à vérifier\n   * @returns Promise<boolean> - true si l'image est accessible\n   */\n  async isImageValid(imageUrl: string): Promise<boolean> {\n    try {\n      const response = await fetch(imageUrl, { method: 'HEAD' });\n      return response.ok;\n    } catch {\n      return false;\n    }\n  }\n\n  /**\n   * Obtient l'URL d'image avec fallback vers placeholder\n   * @param imagePath - Chemin de l'image\n   * @returns URL de l'image ou placeholder si invalide\n   */\n  async getImageUrlWithFallback(imagePath: string | null | undefined): Promise<string> {\n    const imageUrl = this.getFullImageUrl(imagePath);\n    \n    if (imageUrl === this.getPlaceholderUrl()) {\n      return imageUrl;\n    }\n\n    const isValid = await this.isImageValid(imageUrl);\n    return isValid ? imageUrl : this.getPlaceholderUrl();\n  }\n}\n"], "mappings": ";;;;;;;;;;;AAMM,IAAO,kBAAP,MAAO,iBAAe;;EAET,UAAU,YAAY,QAAQ,QAAQ,QAAQ,EAAE,KAAK;EACrD,mBAAmB;EAEpC,cAAA;AACE,YAAQ,IAAI,+DAAgD,KAAK,OAAO;AACxE,YAAQ,IAAI,uCAA2B,YAAY,MAAM;EAC3D;;;;;;EAOA,gBAAgB,WAAoC;AAClD,YAAQ,IAAI,mDAAoC,SAAS;AAEzD,QAAI,CAAC,aAAa,UAAU,KAAI,MAAO,IAAI;AACzC,cAAQ,IAAI,oDAAwC;AACpD,aAAO,KAAK,kBAAiB;IAC/B;AAGA,QAAI,UAAU,WAAW,SAAS,KAAK,UAAU,WAAW,UAAU,GAAG;AACvE,cAAQ,IAAI,+CAA0B,SAAS;AAC/C,aAAO;IACT;AAGA,QAAI,UAAU,WAAW,SAAS,GAAG;AACnC,cAAQ,IAAI,wCAA4B,SAAS;AAGjD,UAAI,gBAAgB;AAGpB,UAAI,UAAU,SAAS,uBAAuB,GAAG;AAC/C,wBAAgB,UAAU,QAAQ,yBAAyB,gBAAgB;AAC3E,gBAAQ,IAAI,uCAAgC,WAAW,UAAK,aAAa;MAC3E;AAGA,UAAI,UAAU,SAAS,sBAAsB,GAAG;AAC9C,wBAAgB,UAAU,QAAQ,wBAAwB,eAAe;AACzE,gBAAQ,IAAI,sCAA+B,WAAW,UAAK,aAAa;MAC1E;AAEA,aAAO;IACT;AAGA,UAAM,YAAY,UAAU,WAAW,GAAG,IAAI,UAAU,UAAU,CAAC,IAAI;AAGvE,UAAM,UAAU,GAAG,KAAK,OAAO,IAAI,SAAS;AAE5C,YAAQ,IAAI,mCAAuB;MACjC,SAAS,KAAK;MACd;MACA;MACA;KACD;AAED,WAAO;EACT;;;;;;EAOA,mBAAmB,WAAoC;AACrD,WAAO,KAAK,gBAAgB,SAAS;EACvC;;;;;;EAOA,sBAAsB,UAAmC;AACvD,WAAO,KAAK,gBAAgB,QAAQ;EACtC;;;;;;EAOA,iBAAiB,UAAmC;AAClD,YAAQ,IAAI,oDAAqC,QAAQ;AACzD,UAAM,MAAM,KAAK,gBAAgB,QAAQ;AACzC,YAAQ,IAAI,2CAA+B,GAAG;AAC9C,WAAO;EACT;;;;;EAMA,oBAAiB;AACf,WAAO,KAAK;EACd;;;;;;EAOM,aAAa,UAAgB;;AACjC,UAAI;AACF,cAAM,WAAW,MAAM,MAAM,UAAU,EAAE,QAAQ,OAAM,CAAE;AACzD,eAAO,SAAS;MAClB,QAAQ;AACN,eAAO;MACT;IACF;;;;;;;EAOM,wBAAwB,WAAoC;;AAChE,YAAM,WAAW,KAAK,gBAAgB,SAAS;AAE/C,UAAI,aAAa,KAAK,kBAAiB,GAAI;AACzC,eAAO;MACT;AAEA,YAAM,UAAU,MAAM,KAAK,aAAa,QAAQ;AAChD,aAAO,UAAU,WAAW,KAAK,kBAAiB;IACpD;;;qCArIW,kBAAe;EAAA;4EAAf,kBAAe,SAAf,iBAAe,WAAA,YAFd,OAAM,CAAA;;;sEAEP,iBAAe,CAAA;UAH3B;WAAW;MACV,YAAY;KACb;;;", "names": []}
.orders-container {
  padding: 20px;
  max-width: 1200px;
  margin: 0 auto;
}

/* Header */
.orders-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30px;
  padding-bottom: 20px;
  border-bottom: 2px solid #e9ecef;
}

.orders-header h1 {
  color: #2c3e50;
  margin: 0;
  font-size: 2rem;
}

.header-actions {
  display: flex;
  gap: 10px;
}

/* Filters */
.filters-section {
  background: #f8f9fa;
  padding: 20px;
  border-radius: 8px;
  margin-bottom: 20px;
}

.filters-row {
  display: flex;
  gap: 20px;
  align-items: end;
  flex-wrap: wrap;
}

.filter-group {
  display: flex;
  flex-direction: column;
  gap: 5px;
}

.filter-group label {
  font-weight: 600;
  color: #495057;
  font-size: 0.9rem;
}

.form-control {
  padding: 8px 12px;
  border: 1px solid #ced4da;
  border-radius: 4px;
  font-size: 0.9rem;
  min-width: 200px;
}

.form-control:focus {
  outline: none;
  border-color: #007bff;
  box-shadow: 0 0 0 2px rgba(0, 123, 255, 0.25);
}

/* Loading */
.loading-container {
  text-align: center;
  padding: 40px;
}

.spinner {
  border: 4px solid #f3f3f3;
  border-top: 4px solid #007bff;
  border-radius: 50%;
  width: 40px;
  height: 40px;
  animation: spin 1s linear infinite;
  margin: 0 auto 20px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Orders List */
.orders-list {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.no-orders {
  text-align: center;
  padding: 40px;
  color: #6c757d;
}

.no-orders p {
  font-size: 1.2rem;
  margin-bottom: 20px;
}

/* Order Card */
.order-card {
  background: white;
  border: 1px solid #dee2e6;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
  transition: box-shadow 0.2s ease;
}

.order-card:hover {
  box-shadow: 0 4px 8px rgba(0,0,0,0.15);
}

.order-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px;
  cursor: pointer;
  background: #f8f9fa;
  border-bottom: 1px solid #dee2e6;
}

.order-header:hover {
  background: #e9ecef;
}

.order-info {
  flex: 1;
}

.order-reference {
  font-size: 1.1rem;
  margin-bottom: 8px;
}

.order-meta {
  display: flex;
  gap: 20px;
  color: #6c757d;
  font-size: 0.9rem;
}

.order-status {
  display: flex;
  align-items: center;
  gap: 15px;
}

.status-badge {
  padding: 6px 12px;
  border-radius: 20px;
  font-size: 0.8rem;
  font-weight: 600;
  text-transform: uppercase;
}

.status-pending { background: #fff3cd; color: #856404; }
.status-confirmed { background: #d4edda; color: #155724; }
.status-preparing { background: #cce5ff; color: #004085; }
.status-shipped { background: #e2e3e5; color: #383d41; }
.status-delivered { background: #d1ecf1; color: #0c5460; }
.status-cancelled { background: #f8d7da; color: #721c24; }
.status-default { background: #e9ecef; color: #495057; }

.expand-icon {
  transition: transform 0.2s ease;
  font-size: 0.8rem;
  color: #6c757d;
}

.expand-icon.expanded {
  transform: rotate(180deg);
}

/* Order Details */
.order-details {
  padding: 20px;
  background: white;
}

.order-details h4 {
  color: #495057;
  margin-bottom: 15px;
  font-size: 1rem;
  border-bottom: 1px solid #dee2e6;
  padding-bottom: 8px;
}

/* Client Info */
.client-info, .shipping-info {
  margin-bottom: 25px;
}

.info-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 10px;
}

.info-item {
  font-size: 0.9rem;
}

.info-item strong {
  color: #495057;
}

.address {
  background: #f8f9fa;
  padding: 15px;
  border-radius: 4px;
  border-left: 4px solid #007bff;
}

.address p {
  margin: 0;
  line-height: 1.4;
}

/* Products */
.products-section {
  margin-bottom: 25px;
}

.products-list {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.product-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px;
  background: #f8f9fa;
  border-radius: 4px;
  border: 1px solid #dee2e6;
}

.product-info {
  flex: 1;
}

.product-name {
  font-size: 1rem;
  margin-bottom: 5px;
}

.product-details {
  display: flex;
  gap: 15px;
  font-size: 0.8rem;
  color: #6c757d;
}

.product-quantity {
  text-align: center;
  min-width: 80px;
}

.quantity {
  font-weight: 600;
  color: #495057;
}

.product-price {
  text-align: right;
  min-width: 120px;
}

.unit-price {
  font-size: 0.8rem;
  color: #6c757d;
}

.total-price {
  font-size: 1rem;
  color: #28a745;
}

.no-products {
  text-align: center;
  color: #6c757d;
  font-style: italic;
  padding: 20px;
}

/* Actions */
.order-actions {
  border-top: 1px solid #dee2e6;
  padding-top: 20px;
}

.actions-buttons {
  display: flex;
  gap: 10px;
  flex-wrap: wrap;
}

/* Buttons */
.btn {
  padding: 8px 16px;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 0.9rem;
  font-weight: 500;
  text-decoration: none;
  display: inline-flex;
  align-items: center;
  gap: 5px;
  transition: all 0.2s ease;
}

.btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.btn-primary { background: #007bff; color: white; }
.btn-primary:hover:not(:disabled) { background: #0056b3; }

.btn-success { background: #28a745; color: white; }
.btn-success:hover:not(:disabled) { background: #1e7e34; }

.btn-warning { background: #ffc107; color: #212529; }
.btn-warning:hover:not(:disabled) { background: #e0a800; }

.btn-info { background: #17a2b8; color: white; }
.btn-info:hover:not(:disabled) { background: #117a8b; }

.btn-danger { background: #dc3545; color: white; }
.btn-danger:hover:not(:disabled) { background: #c82333; }

.btn-secondary { background: #6c757d; color: white; }
.btn-secondary:hover:not(:disabled) { background: #545b62; }

.btn-outline-primary { 
  background: transparent; 
  color: #007bff; 
  border: 1px solid #007bff; 
}
.btn-outline-primary:hover:not(:disabled) { 
  background: #007bff; 
  color: white; 
}

.btn-outline-secondary { 
  background: transparent; 
  color: #6c757d; 
  border: 1px solid #6c757d; 
}
.btn-outline-secondary:hover:not(:disabled) { 
  background: #6c757d; 
  color: white; 
}

/* Pagination */
.pagination-container {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 30px;
  padding-top: 20px;
  border-top: 1px solid #dee2e6;
}

.pagination-info {
  color: #6c757d;
  font-size: 0.9rem;
}

.pagination {
  display: flex;
  gap: 5px;
}

/* Alert */
.alert {
  padding: 15px;
  border-radius: 4px;
  margin-bottom: 20px;
}

.alert-danger {
  background: #f8d7da;
  color: #721c24;
  border: 1px solid #f5c6cb;
}

/* Responsive */
@media (max-width: 768px) {
  .orders-container {
    padding: 10px;
  }
  
  .orders-header {
    flex-direction: column;
    gap: 15px;
    align-items: stretch;
  }
  
  .filters-row {
    flex-direction: column;
    gap: 15px;
  }
  
  .form-control {
    min-width: auto;
  }
  
  .order-header {
    flex-direction: column;
    gap: 15px;
    align-items: stretch;
  }
  
  .order-meta {
    flex-direction: column;
    gap: 5px;
  }
  
  .product-item {
    flex-direction: column;
    gap: 10px;
    align-items: stretch;
  }
  
  .actions-buttons {
    flex-direction: column;
  }
  
  .pagination-container {
    flex-direction: column;
    gap: 15px;
  }
}

import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { ProduitService } from '../../../../services/produit.service';
import { Produit } from '../../../../models';

@Component({
  selector: 'app-admin-products',
  standalone: true,
  imports: [CommonModule, FormsModule],
  template: `
    <div class="admin-products">
      <div class="header">
        <h1>📦 Gestion des Produits</h1>
        <p>Gérer et valider les produits de la plateforme</p>
      </div>

      <!-- Statistiques -->
      <div class="stats-grid">
        <div class="stat-card">
          <div class="stat-icon">📦</div>
          <div class="stat-info">
            <div class="stat-number">{{ totalProduits }}</div>
            <div class="stat-label">Total Produits</div>
          </div>
        </div>
        <div class="stat-card">
          <div class="stat-icon">✅</div>
          <div class="stat-info">
            <div class="stat-number">{{ produitsActifs }}</div>
            <div class="stat-label">Produits Actifs</div>
          </div>
        </div>
        <div class="stat-card">
          <div class="stat-icon">⚠️</div>
          <div class="stat-info">
            <div class="stat-number">{{ produitsEnRupture }}</div>
            <div class="stat-label">En Rupture</div>
          </div>
        </div>
      </div>

      <!-- Filtres et recherche -->
      <div class="filters-section">
        <div class="search-box">
          <input
            type="text"
            [(ngModel)]="searchTerm"
            (input)="onSearch()"
            placeholder="Rechercher un produit..."
            class="search-input">
        </div>
        <div class="filter-buttons">
          <button
            class="filter-btn"
            [class.active]="selectedFilter === 'all'"
            (click)="setFilter('all')">
            Tous
          </button>
          <button
            class="filter-btn"
            [class.active]="selectedFilter === 'active'"
            (click)="setFilter('active')">
            Actifs
          </button>
          <button
            class="filter-btn"
            [class.active]="selectedFilter === 'rupture'"
            (click)="setFilter('rupture')">
            En Rupture
          </button>
        </div>
      </div>

      <!-- Loading -->
      <div *ngIf="loading" class="loading">
        <div class="spinner"></div>
        <p>Chargement des produits...</p>
      </div>

      <!-- Error -->
      <div *ngIf="error" class="error-message">
        <div class="error-icon">❌</div>
        <p>{{ error }}</p>
        <button (click)="loadProduits()" class="retry-btn">Réessayer</button>
      </div>

      <!-- Liste des produits -->
      <div *ngIf="!loading && !error" class="products-section">
        <div class="section-header">
          <h2>Liste des Produits ({{ filteredProduits.length }})</h2>
        </div>

        <div *ngIf="filteredProduits.length === 0" class="empty-state">
          <div class="empty-icon">📦</div>
          <h3>Aucun produit trouvé</h3>
          <p>Aucun produit ne correspond à vos critères de recherche.</p>
        </div>

        <div *ngIf="filteredProduits.length > 0" class="products-table">
          <div class="table-header">
            <div class="col-image">Image</div>
            <div class="col-name">Nom</div>
            <div class="col-reference">Référence</div>
            <div class="col-fournisseur">Fournisseur</div>
            <div class="col-stock">Stock</div>
            <div class="col-prix">Prix</div>
            <div class="col-status">Statut</div>
            <div class="col-actions">Actions</div>
          </div>

          <div *ngFor="let produit of paginatedProduits" class="table-row">
            <div class="col-image">
              <img
                [src]="getProductImage(produit)"
                [alt]="produit.nom"
                class="product-image"
                (error)="onImageError($event)">
            </div>
            <div class="col-name">
              <div class="product-name">{{ produit.nom }}</div>
              <div class="product-description">{{ getShortDescription(produit.description) }}</div>
            </div>
            <div class="col-reference">{{ produit.referenceOriginal || 'N/A' }}</div>
            <div class="col-fournisseur">{{ getFournisseurName(produit) }}</div>
            <div class="col-stock">
              <span class="stock-badge" [class.low-stock]="produit.stock <= 5">
                {{ produit.stock }}
              </span>
            </div>
            <div class="col-prix">{{ produit.prixVenteHT | number:'1.2-2' }} TND</div>
            <div class="col-status">
              <span class="status-badge" [class.active]="produit.stock > 0" [class.inactive]="produit.stock === 0">
                {{ produit.stock > 0 ? 'En Stock' : 'Rupture' }}
              </span>
            </div>
            <div class="col-actions">
              <button class="action-btn view" (click)="viewProduct(produit)" title="Voir">👁️</button>
              <button class="action-btn edit" (click)="editProduct(produit)" title="Modifier">✏️</button>
              <button
                class="action-btn toggle"
                (click)="toggleProductStatus(produit)"
                [title]="produit.stock > 0 ? 'Marquer en rupture' : 'Remettre en stock'">
                {{ produit.stock > 0 ? '🔴' : '🟢' }}
              </button>
            </div>
          </div>
        </div>

        <!-- Pagination -->
        <div *ngIf="totalPages > 1" class="pagination">
          <button
            class="page-btn"
            [disabled]="currentPage === 1"
            (click)="goToPage(currentPage - 1)">
            ← Précédent
          </button>

          <span class="page-info">
            Page {{ currentPage }} sur {{ totalPages }}
          </span>

          <button
            class="page-btn"
            [disabled]="currentPage === totalPages"
            (click)="goToPage(currentPage + 1)">
            Suivant →
          </button>
        </div>
      </div>
    </div>
  `,
  styles: [`
    .admin-products {
      padding: 2rem;
      max-width: 1400px;
      margin: 0 auto;
      background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
      min-height: 100vh;
    }

    .header {
      margin-bottom: 2rem;
      background: white;
      border-radius: 16px;
      padding: 2rem;
      box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
      border: 1px solid rgba(255, 255, 255, 0.2);
    }

    .header h1 {
      font-size: 2.25rem;
      font-weight: 700;
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
      background-clip: text;
      margin-bottom: 0.5rem;
    }

    .header p {
      color: #64748b;
      font-size: 1.1rem;
    }

    /* Statistiques */
    .stats-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
      gap: 1.5rem;
      margin-bottom: 2rem;
    }

    .stat-card {
      background: white;
      padding: 2rem;
      border-radius: 16px;
      box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
      backdrop-filter: blur(10px);
      border: 1px solid rgba(255, 255, 255, 0.2);
      display: flex;
      align-items: center;
      gap: 1.5rem;
      transition: all 0.3s ease;
      position: relative;
      overflow: hidden;
    }

    .stat-card:hover {
      transform: translateY(-4px);
      box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15);
    }

    .stat-card::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      height: 4px;
      background: linear-gradient(90deg, #667eea 0%, #764ba2 100%);
    }

    .stat-icon {
      font-size: 2.5rem;
      width: 70px;
      height: 70px;
      display: flex;
      align-items: center;
      justify-content: center;
      background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
      border-radius: 16px;
      box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
    }

    .stat-number {
      font-size: 2rem;
      font-weight: 700;
      color: #1e293b;
    }

    .stat-label {
      color: #64748b;
      font-size: 0.9rem;
    }

    /* Filtres */
    .filters-section {
      background: white;
      padding: 2rem;
      border-radius: 16px;
      box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
      border: 1px solid rgba(255, 255, 255, 0.2);
      backdrop-filter: blur(10px);
      margin-bottom: 2rem;
      display: flex;
      gap: 1.5rem;
      align-items: center;
      flex-wrap: wrap;
    }

    .search-box {
      flex: 1;
      min-width: 300px;
    }

    .search-input {
      width: 100%;
      padding: 0.75rem 1rem;
      border: 2px solid #e2e8f0;
      border-radius: 8px;
      font-size: 1rem;
      transition: border-color 0.2s;
    }

    .search-input:focus {
      outline: none;
      border-color: #3b82f6;
    }

    .filter-buttons {
      display: flex;
      gap: 0.5rem;
    }

    .filter-btn {
      padding: 0.75rem 1.5rem;
      border: 2px solid rgba(226, 232, 240, 0.8);
      background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
      border-radius: 12px;
      cursor: pointer;
      transition: all 0.3s ease;
      font-weight: 600;
      text-transform: uppercase;
      letter-spacing: 0.5px;
      font-size: 0.875rem;
      position: relative;
      overflow: hidden;
    }

    .filter-btn::before {
      content: '';
      position: absolute;
      top: 0;
      left: -100%;
      width: 100%;
      height: 100%;
      background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
      transition: left 0.5s;
    }

    .filter-btn:hover::before {
      left: 100%;
    }

    .filter-btn:hover {
      transform: translateY(-2px);
      box-shadow: 0 4px 15px rgba(0, 0, 0, 0.15);
      background: linear-gradient(135deg, #e2e8f0 0%, #cbd5e0 100%);
    }

    .filter-btn.active {
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      border-color: transparent;
      color: white;
      box-shadow: 0 4px 15px rgba(102, 126, 234, 0.4);
    }

    .filter-btn.active:hover {
      transform: translateY(-2px);
      box-shadow: 0 8px 25px rgba(102, 126, 234, 0.6);
    }

    /* Loading et erreurs */
    .loading, .error-message {
      text-align: center;
      padding: 3rem;
      background: white;
      border-radius: 12px;
      box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    }

    .spinner {
      width: 40px;
      height: 40px;
      border: 4px solid #f3f4f6;
      border-top: 4px solid #3b82f6;
      border-radius: 50%;
      animation: spin 1s linear infinite;
      margin: 0 auto 1rem;
    }

    @keyframes spin {
      0% { transform: rotate(0deg); }
      100% { transform: rotate(360deg); }
    }

    .error-icon {
      font-size: 3rem;
      margin-bottom: 1rem;
    }

    .retry-btn {
      background: #3b82f6;
      color: white;
      border: none;
      padding: 0.75rem 1.5rem;
      border-radius: 6px;
      cursor: pointer;
      margin-top: 1rem;
    }

    /* Section produits */
    .products-section {
      background: white;
      border-radius: 16px;
      box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
      backdrop-filter: blur(10px);
      border: 1px solid rgba(255, 255, 255, 0.2);
      overflow: hidden;
    }

    .section-header {
      padding: 2rem;
      border-bottom: 1px solid rgba(226, 232, 240, 0.5);
      background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
    }

    .section-header h2 {
      color: #1e293b;
      margin: 0;
      font-size: 1.75rem;
      font-weight: 700;
    }

    .empty-state {
      text-align: center;
      padding: 3rem;
    }

    .empty-icon {
      font-size: 4rem;
      margin-bottom: 1rem;
    }

    .empty-state h3 {
      color: #1e293b;
      margin-bottom: 0.5rem;
    }

    .empty-state p {
      color: #64748b;
    }

    /* Tableau des produits */
    .products-table {
      overflow-x: auto;
    }

    .table-header, .table-row {
      display: grid;
      grid-template-columns: 80px 2fr 150px 150px 100px 120px 100px 120px;
      gap: 1rem;
      padding: 1rem 1.5rem;
      align-items: center;
    }

    .table-header {
      background: #f8fafc;
      border-bottom: 1px solid #e2e8f0;
      font-weight: 600;
      color: #374151;
      font-size: 0.9rem;
    }

    .table-row {
      border-bottom: 1px solid #f1f5f9;
      transition: background-color 0.2s;
    }

    .table-row:hover {
      background: #f8fafc;
    }

    .product-image {
      width: 70px;
      height: 70px;
      object-fit: cover;
      border-radius: 12px;
      border: 2px solid rgba(255, 255, 255, 0.8);
      box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
      transition: all 0.3s ease;
    }

    .product-image:hover {
      transform: scale(1.05);
      box-shadow: 0 8px 16px rgba(0, 0, 0, 0.15);
    }

    .product-name {
      font-weight: 600;
      color: #1e293b;
      margin-bottom: 0.25rem;
    }

    .product-description {
      color: #64748b;
      font-size: 0.85rem;
    }

    .stock-badge {
      display: inline-block;
      padding: 0.5rem 1rem;
      border-radius: 20px;
      background: linear-gradient(135deg, #d1fae5 0%, #a7f3d0 100%);
      color: #065f46;
      font-weight: 600;
      font-size: 0.8rem;
      text-transform: uppercase;
      letter-spacing: 0.5px;
      box-shadow: 0 2px 8px rgba(5, 150, 105, 0.3);
      border: 1px solid rgba(255, 255, 255, 0.2);
      backdrop-filter: blur(10px);
    }

    .stock-badge.low-stock {
      background: linear-gradient(135deg, #fef3c7 0%, #fde68a 100%);
      color: #92400e;
      box-shadow: 0 2px 8px rgba(251, 191, 36, 0.3);
    }

    .status-badge {
      display: inline-block;
      padding: 0.5rem 1rem;
      border-radius: 20px;
      font-weight: 600;
      font-size: 0.8rem;
      text-transform: uppercase;
      letter-spacing: 0.5px;
      border: 1px solid rgba(255, 255, 255, 0.2);
      backdrop-filter: blur(10px);
    }

    .status-badge.active {
      background: linear-gradient(135deg, #d1fae5 0%, #a7f3d0 100%);
      color: #065f46;
      box-shadow: 0 2px 8px rgba(5, 150, 105, 0.3);
    }

    .status-badge.inactive {
      background: linear-gradient(135deg, #fee2e2 0%, #fecaca 100%);
      color: #991b1b;
      box-shadow: 0 2px 8px rgba(220, 38, 38, 0.3);
    }

    .col-actions {
      display: flex;
      gap: 0.5rem;
    }

    .action-btn {
      width: 40px;
      height: 40px;
      border: none;
      border-radius: 12px;
      cursor: pointer;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 1.1rem;
      transition: all 0.3s ease;
      position: relative;
      overflow: hidden;
      backdrop-filter: blur(10px);
      border: 1px solid rgba(255, 255, 255, 0.2);
    }

    .action-btn::before {
      content: '';
      position: absolute;
      top: 0;
      left: -100%;
      width: 100%;
      height: 100%;
      background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
      transition: left 0.5s;
    }

    .action-btn:hover::before {
      left: 100%;
    }

    .action-btn.view {
      background: linear-gradient(135deg, #dbeafe 0%, #bfdbfe 100%);
      color: #1e40af;
      box-shadow: 0 2px 8px rgba(59, 130, 246, 0.3);
    }

    .action-btn.view:hover {
      transform: translateY(-2px);
      box-shadow: 0 4px 15px rgba(59, 130, 246, 0.4);
    }

    .action-btn.edit {
      background: linear-gradient(135deg, #fef3c7 0%, #fde68a 100%);
      color: #92400e;
      box-shadow: 0 2px 8px rgba(251, 191, 36, 0.3);
    }

    .action-btn.edit:hover {
      transform: translateY(-2px);
      box-shadow: 0 4px 15px rgba(251, 191, 36, 0.4);
    }

    .action-btn.toggle {
      background: linear-gradient(135deg, #f3f4f6 0%, #e5e7eb 100%);
      color: #374151;
      box-shadow: 0 2px 8px rgba(107, 114, 128, 0.3);
    }

    .action-btn.toggle:hover {
      transform: translateY(-2px);
      box-shadow: 0 4px 15px rgba(107, 114, 128, 0.4);
    }

    /* Pagination */
    .pagination {
      display: flex;
      justify-content: center;
      align-items: center;
      gap: 1rem;
      padding: 1.5rem;
      border-top: 1px solid #e2e8f0;
    }

    .page-btn {
      padding: 0.5rem 1rem;
      border: 1px solid #d1d5db;
      background: white;
      border-radius: 6px;
      cursor: pointer;
      transition: all 0.2s;
    }

    .page-btn:hover:not(:disabled) {
      background: #f9fafb;
      border-color: #9ca3af;
    }

    .page-btn:disabled {
      opacity: 0.5;
      cursor: not-allowed;
    }

    .page-info {
      color: #6b7280;
      font-weight: 500;
    }

    /* Responsive */
    @media (max-width: 1200px) {
      .table-header, .table-row {
        grid-template-columns: 60px 1fr 120px 100px 80px 100px 80px 100px;
        font-size: 0.85rem;
      }
    }

    @media (max-width: 768px) {
      .filters-section {
        flex-direction: column;
        align-items: stretch;
      }

      .search-box {
        min-width: auto;
      }

      .filter-buttons {
        justify-content: center;
      }

      .table-header, .table-row {
        grid-template-columns: 1fr;
        gap: 0.5rem;
      }

      .table-header {
        display: none;
      }

      .table-row {
        display: block;
        padding: 1rem;
        border: 1px solid #e2e8f0;
        border-radius: 8px;
        margin-bottom: 1rem;
      }
    }
  `]
})
export class AdminProductsComponent implements OnInit {
  // Données
  produits: Produit[] = [];
  filteredProduits: Produit[] = [];
  paginatedProduits: Produit[] = [];

  // États
  loading = false;
  error: string | null = null;

  // Filtres et recherche
  searchTerm = '';
  selectedFilter = 'all';

  // Pagination
  currentPage = 1;
  itemsPerPage = 10;
  totalPages = 1;

  // Statistiques
  totalProduits = 0;
  produitsActifs = 0;
  produitsEnRupture = 0;

  constructor(private produitService: ProduitService) {}

  ngOnInit() {
    this.loadProduits();
  }

  loadProduits() {
    this.loading = true;
    this.error = null;

    this.produitService.getAll().subscribe({
      next: (produits) => {
        this.produits = produits;
        this.calculateStats();
        this.applyFilters();
        this.loading = false;
        console.log('✅ Produits chargés:', produits);
      },
      error: (error) => {
        this.error = 'Erreur lors du chargement des produits';
        this.loading = false;
        console.error('❌ Erreur chargement produits:', error);
      }
    });
  }

  calculateStats() {
    this.totalProduits = this.produits.length;
    this.produitsActifs = this.produits.filter(p => p.stock > 0).length;
    this.produitsEnRupture = this.produits.filter(p => p.stock <= 5).length;
  }

  onSearch() {
    this.currentPage = 1;
    this.applyFilters();
  }

  setFilter(filter: string) {
    this.selectedFilter = filter;
    this.currentPage = 1;
    this.applyFilters();
  }

  applyFilters() {
    let filtered = [...this.produits];

    // Filtre par recherche
    if (this.searchTerm.trim()) {
      const term = this.searchTerm.toLowerCase();
      filtered = filtered.filter(p =>
        p.nom.toLowerCase().includes(term) ||
        p.referenceOriginal?.toLowerCase().includes(term) ||
        p.description?.toLowerCase().includes(term)
      );
    }

    // Filtre par statut
    switch (this.selectedFilter) {
      case 'active':
        filtered = filtered.filter(p => p.stock > 0);
        break;
      case 'rupture':
        filtered = filtered.filter(p => p.stock <= 5);
        break;
    }

    this.filteredProduits = filtered;
    this.updatePagination();
  }

  updatePagination() {
    this.totalPages = Math.ceil(this.filteredProduits.length / this.itemsPerPage);
    const startIndex = (this.currentPage - 1) * this.itemsPerPage;
    const endIndex = startIndex + this.itemsPerPage;
    this.paginatedProduits = this.filteredProduits.slice(startIndex, endIndex);
  }

  goToPage(page: number) {
    if (page >= 1 && page <= this.totalPages) {
      this.currentPage = page;
      this.updatePagination();
    }
  }

  getProductImage(produit: Produit): string {
    // Vérifier s'il y a une image principale
    if (produit.imagePrincipaleUrl) {
      return this.buildImageUrl(produit.imagePrincipaleUrl);
    }

    // Sinon, prendre la première image disponible
    if (produit.images && produit.images.length > 0) {
      const mainImage = produit.images.find(img => img.isMain) || produit.images[0];
      return this.buildImageUrl(mainImage.imageUrl);
    }

    // Image par défaut
    return '/assets/images/placeholder.jpg';
  }

  private buildImageUrl(imageUrl: string): string {
    if (!imageUrl) return '/assets/images/placeholder.jpg';

    // Si l'URL est déjà complète, la retourner telle quelle
    if (imageUrl.startsWith('http://') || imageUrl.startsWith('https://')) {
      return imageUrl;
    }

    // Si l'URL commence par /uploads, ajouter l'URL de base du backend
    if (imageUrl.startsWith('/uploads') || imageUrl.startsWith('uploads')) {
      const baseUrl = 'http://localhost:5014'; // URL du backend
      return `${baseUrl}/${imageUrl.startsWith('/') ? imageUrl.substring(1) : imageUrl}`;
    }

    // Sinon, retourner l'image par défaut
    return '/assets/images/placeholder.jpg';
  }

  onImageError(event: any) {
    console.warn('Erreur de chargement d\'image:', event.target.src);
    event.target.src = '/assets/images/placeholder.jpg';
  }

  getFournisseurName(produit: Produit): string {
    if (produit.fournisseur?.raisonSociale) {
      return produit.fournisseur.raisonSociale;
    }
    if (produit.fournisseur?.nom && produit.fournisseur?.prenom) {
      return `${produit.fournisseur.prenom} ${produit.fournisseur.nom}`;
    }
    if (produit.fournisseur?.nom) {
      return produit.fournisseur.nom;
    }
    return 'Fournisseur inconnu';
  }

  getShortDescription(description?: string): string {
    if (!description) return 'Aucune description';
    return description.length > 50 ? description.substring(0, 50) + '...' : description;
  }

  viewProduct(produit: Produit) {
    console.log('👁️ Voir produit:', produit);
    // TODO: Implémenter la vue détaillée
  }

  editProduct(produit: Produit) {
    console.log('✏️ Modifier produit:', produit);
    // TODO: Implémenter l'édition
  }

  toggleProductStatus(produit: Produit) {
    console.log('🔄 Changer statut produit:', produit);
    // TODO: Implémenter le changement de statut
  }
}
